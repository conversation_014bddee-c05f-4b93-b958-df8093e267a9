/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.ximalaya.ting.android.reactnative.modules.verticalpageview;

import java.util.Map;

import android.view.View;

import com.facebook.infer.annotation.Assertions;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.uimanager.PixelUtil;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.UIManagerModule;
import com.facebook.react.uimanager.ViewGroupManager;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.facebook.react.uimanager.events.EventDispatcher;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;

import javax.annotation.Nullable;

/**
 * Instance of {@link com.facebook.react.uimanager.ViewManager} that provides native {@link android.support.v4.view.ViewPager} view.
 */
public class ReactVerticalPreviewViewPagerManager extends ViewGroupManager<ReactVerticalPreviewViewPager> {

  private static final String REACT_CLASS = "XMVerticalPreviewViewPager";

  private static final int COMMAND_SET_PAGE = 1;
  private static final int COMMAND_SET_PAGE_WITHOUT_ANIMATION = 2;
  private static final int COMMAND_SET_SCROLL_ENABLED = 3;
  private static final int COMMAND_SET_SCROLL_VALUE = 4;

  @Override
  public String getName() {
    return REACT_CLASS;
  }

  @Override
  protected ReactVerticalPreviewViewPager createViewInstance(final ThemedReactContext reactContext) {
      final ReactVerticalPreviewViewPager reactVerticalPreviewViewPager = new ReactVerticalPreviewViewPager(reactContext);
      final EventDispatcher eventDispatcher = reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher();
      reactVerticalPreviewViewPager.setOverScrollMode(View.OVER_SCROLL_NEVER);
      reactVerticalPreviewViewPager.setOnFirstPagePullListener(new VerticalViewPager.OnFirstPagePullListener() {
          @Override
          public void onPull(float distance, String state) {
              eventDispatcher.dispatchEvent(
                      new PagePullToRefreshEvent(reactVerticalPreviewViewPager.getId(), state, BaseUtil.px2dip(ToolUtil.getCtx(), distance)));
          }
      });
      return reactVerticalPreviewViewPager;
  }

  @ReactProp(name = "scrollEnabled", defaultBoolean = true)
  public void setScrollEnabled(ReactVerticalPreviewViewPager viewPager, boolean value) {
    viewPager.setScrollEnabled(value);
  }

  @ReactProp(name = "pageBottomPadding", defaultInt = 0)
  public void pageBottomPadding(ReactVerticalPreviewViewPager viewPager, int value) {
      viewPager.setPadding(0, 0, 0, BaseUtil.dp2px(ToolUtil.getCtx(), value));
      viewPager.setClipToPadding(false);
      viewPager.setClipChildren(false);
      viewPager.setOverScrollMode(View.OVER_SCROLL_NEVER);
  }

  @ReactProp(name = "offscreenPageLimit", defaultInt = 1)
  public void offscreenPageLimit(ReactVerticalPreviewViewPager viewPager, int value) {
      viewPager.setOffscreenPageLimit(value);
  }

  @Override
  public boolean needsCustomLayoutForChildren() {
    return true;
  }

  @Override
  public Map getExportedCustomDirectEventTypeConstants() {
    return MapBuilder.of(
        PageScrollEvent.EVENT_NAME, MapBuilder.of("registrationName", "onPageScroll"),
        PageScrollStateChangedEvent.EVENT_NAME, MapBuilder.of("registrationName", "onPageScrollStateChanged"),
        PagePullToRefreshEvent.EVENT_NAME, MapBuilder.of("registrationName", "onPullToRefresh"),
        PageSelectedEvent.EVENT_NAME, MapBuilder.of("registrationName", "onPageSelected"));
  }

  @Override
  public Map<String,Integer> getCommandsMap() {
    return MapBuilder.of(
        "setPage",
        COMMAND_SET_PAGE,
        "setPageWithoutAnimation",
        COMMAND_SET_PAGE_WITHOUT_ANIMATION,
        "setScrollEnabled",
            COMMAND_SET_SCROLL_ENABLED,
            "smoothScrollTo",
            COMMAND_SET_SCROLL_VALUE);
  }

  @Override
  public void receiveCommand(
      ReactVerticalPreviewViewPager viewPager,
      int commandType,
      @Nullable ReadableArray args) {
    Assertions.assertNotNull(viewPager);
    Assertions.assertNotNull(args);
    switch (commandType) {
      case COMMAND_SET_PAGE: {
        viewPager.setCurrentItemFromJs(args.getInt(0), true);
        return;
      }
      case COMMAND_SET_PAGE_WITHOUT_ANIMATION: {
        viewPager.setCurrentItemFromJs(args.getInt(0), false);
        return;
      }
      case COMMAND_SET_SCROLL_ENABLED: {
        viewPager.setScrollEnabled(args.getBoolean(0));
        return;
      }
        case COMMAND_SET_SCROLL_VALUE:
            viewPager.smoothScrollTo(0, BaseUtil.dp2px(ToolUtil.getCtx(), args.getInt(0)),
                    BaseUtil.dp2px(ToolUtil.getCtx(), 32) * 100);
            return;
      default:
        throw new IllegalArgumentException(String.format(
            "Unsupported command %d received by %s.",
            commandType,
            getClass().getSimpleName()));
    }
  }

  @Override
  public void addView(ReactVerticalPreviewViewPager parent, View child, int index) {
    parent.addViewToAdapter(child, index);
  }

  @Override
  public int getChildCount(ReactVerticalPreviewViewPager parent) {
    return parent.getViewCountInAdapter();
  }

  @Override
  public View getChildAt(ReactVerticalPreviewViewPager parent, int index) {
    return parent.getViewFromAdapter(index);
  }

  @Override
  public void removeViewAt(ReactVerticalPreviewViewPager parent, int index) {
    parent.removeViewFromAdapter(index);
  }

  @ReactProp(name = "pageMargin", defaultFloat = 0)
  public void setPageMargin(ReactVerticalPreviewViewPager pager, float margin) {
    pager.setPageMargin((int) PixelUtil.toPixelFromDIP(margin));
  }

}
