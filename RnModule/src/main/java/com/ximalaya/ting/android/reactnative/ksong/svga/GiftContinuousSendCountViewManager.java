package com.ximalaya.ting.android.reactnative.ksong.svga;


import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;

/**
 * Created by hebin on 2018/11/26.
 */
public class GiftContinuousSendCountViewManager extends SimpleViewManager<GiftContinuousSendCountView> {

    private static final String PROP_FONT_SIZE = "fontSize";
    private static final String PROP_FONT_FAMILY = "fontFamily";
    private static final String PROP_COLOR = "color";
    private static final String PROP_DURATION = "duration";
    private static final String PROP_TEXT_SHADOW_COLOR = "shadowColor";
    private static final String PROP_TEXT_BEGIN_COUNT = "beginCount";
    private static final String PROP_TEXT_END_COUNT = "endCount";
    private static final String PROP_ANIMATING = "animating";
    private static final String PROP_PREFIX = "prefix";
    private static final String PROP_SUFFIX = "suffix";

    private static final String NAME = "GiftContinuousSendCountView";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    protected GiftContinuousSendCountView createViewInstance(ThemedReactContext reactContext) {
        return new GiftContinuousSendCountView(reactContext);
    }

    @ReactProp(name = PROP_FONT_SIZE)
    public void setFontSize(GiftContinuousSendCountView view, float size) {
        view.setFontSize(size);
    }

    @ReactProp(name = PROP_FONT_FAMILY)
    public void setFontFamily(GiftContinuousSendCountView view, String family) {
        view.setFontFamily(family);
    }

    @ReactProp(name = PROP_COLOR)
    public void setColor(GiftContinuousSendCountView view, String color) {
        view.setColor(color);
    }

    @ReactProp(name = PROP_TEXT_SHADOW_COLOR)
    public void setShadowColor(GiftContinuousSendCountView view, String shadowColor) {
        view.setShadowColor(shadowColor);
    }

    @ReactProp(name = PROP_DURATION)
    public void setDuration(GiftContinuousSendCountView view, int duration) {
        view.setDuration(duration);
    }

    @ReactProp(name = PROP_TEXT_BEGIN_COUNT)
    public void setBeginCount(GiftContinuousSendCountView view, int begin) {
        view.setBeginCount(begin);
    }

    @ReactProp(name = PROP_TEXT_END_COUNT)
    public void setEndCount(GiftContinuousSendCountView view, int end) {
        view.setEndCount(end);
    }

    @ReactProp(name = PROP_ANIMATING)
    public void setAnimating(GiftContinuousSendCountView view, boolean isAnimating) {
        view.setAnimating(isAnimating);
    }

    @ReactProp(name = PROP_PREFIX)
    public void setPrefix(GiftContinuousSendCountView view, String prefix) {
        view.setPrefix(prefix);
    }

    @ReactProp(name = PROP_SUFFIX)
    public void setSuffix(GiftContinuousSendCountView view, String suffix) {
        view.setSuffix(suffix);
    }
}
