package com.ximalaya.ting.android.reactnative.modules.pulltorefresh;

import android.content.Context;
import android.content.res.TypedArray;

import com.handmark.pulltorefresh.library.internal.LoadingLayout;

/**
 * 首页下拉刷新样式
 * Created by <PERSON> on 2021/5/19 2:32 下午.
 *
 * <AUTHOR>
 */

public abstract class HomeStyleReactPullToRefreshNestedScrollView extends ReactPullToRefreshNestedScrollView {

    public HomeStyleReactPullToRefreshNestedScrollView(Context context) {
        super(context);
    }

    @Override
    protected LoadingLayout createLoadingLayout(Context context, Mode mode, TypedArray attrs) {
        return new XmRecommendLoadingLayout(context, mode, getPullToRefreshScrollDirection(), attrs);
    }
}
