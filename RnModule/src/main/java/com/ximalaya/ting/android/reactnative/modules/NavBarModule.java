package com.ximalaya.ting.android.reactnative.modules;

import android.content.Context;
import android.content.res.Configuration;
import android.database.ContentObserver;
import android.graphics.Point;
import android.graphics.Rect;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.widget.FrameLayout;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.LifecycleEventListener;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.module.annotations.ReactModule;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.reactnative.utils.RNUtils;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Nullable;

/**
 * Created by hebin on 2018/8/20.
 */
@ReactModule(name = NavBarModule.NAME)
public class NavBarModule extends ReactContextBaseJavaModule implements LifecycleEventListener {
    private static final String TAG = NavBarModule.class.getSimpleName();
    public static final String NAME = "NavBar";

    private static final String EVENT_NAVBAR_CHANGED = "onNavBarChanged";

    public NavBarModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public void initialize() {
        super.initialize();
        getReactApplicationContext().addLifecycleEventListener(this);
    }

    @Nullable
    @Override
    public Map<String, Object> getConstants() {
        Map<String, Object> constants = new HashMap<>();
        constants.put("hasNavBar", BaseUtil.hasNavBar(getReactApplicationContext()));
        constants.put("navBarHeight", BaseUtil.getNavigationBarHeight(getReactApplicationContext()));
        return constants;
    }

    @ReactMethod
    public void getNavBarInfo(Promise promise) {
        WritableMap result = Arguments.createMap();
        result.putBoolean("hasNavBar", BaseUtil.hasNavBar(getReactApplicationContext()));
        if (getCurrentActivity() != null) {
            result.putBoolean("isNavBarShow", checkNavigationBarShow(getReactApplicationContext(), getCurrentActivity().getWindow()));
        }
        result.putInt("navBarHeight", BaseUtil.getNavigationBarHeight(getReactApplicationContext()));
        result.putInt("navBarHeightDp", BaseUtil.px2dip(ToolUtil.getCtx(),
                BaseUtil.getNavigationBarHeight(getReactApplicationContext())));

        promise.resolve(result);
    }

    private ContentObserver obsrver = new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange) {
            int navigationBarIsMin;

            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                navigationBarIsMin = Settings.System.getInt(getReactApplicationContext().getContentResolver(),
                        "navigationbar_is_min", 0);
            } else {
                navigationBarIsMin = Settings.Global.getInt(getReactApplicationContext().getContentResolver(),
                        "navigationbar_is_min", 0);
            }

            RNUtils.sendEvent(getReactApplicationContext(), EVENT_NAVBAR_CHANGED, navigationBarIsMin);
//            if (navigationBarIsMin == 1) {
//                //导航键隐藏了
//                RNUtils.sendEvent(getReactApplicationContext(),EVENT_NAVBAR_CHANGED, 1);
//            } else {
//                //导航键显示了
//                RNUtils.sendEvent(getReactApplicationContext(),EVENT_NAVBAR_CHANGED, 0);
//            }
        }
    };

    private ViewTreeObserver.OnGlobalLayoutListener globalLayoutListener = new ViewTreeObserver.OnGlobalLayoutListener() {
        @Override
        public void onGlobalLayout() {
            if (isSMARTISAN() && getCurrentActivity() != null && getCurrentActivity().getWindow() != null) {
                Display display = getCurrentActivity().getWindow().getWindowManager().getDefaultDisplay();
                Point point = new Point();
                display.getRealSize(point);

                View decorView = getCurrentActivity().getWindow().getDecorView();
                Configuration conf = getCurrentActivity().getResources().getConfiguration();
                if (Configuration.ORIENTATION_LANDSCAPE == conf.orientation) {
                    View contentView = decorView.findViewById(android.R.id.content);
                    if (point.x - contentView.getWidth() == BaseUtil.getNavigationBarHeight(getReactApplicationContext())) {
                        RNUtils.sendEvent(getReactApplicationContext(), EVENT_NAVBAR_CHANGED, 0);
                    } else if (point.x == contentView.getWidth()) {
                        RNUtils.sendEvent(getReactApplicationContext(), EVENT_NAVBAR_CHANGED, 1);
                    }
                } else {
                    Rect rect = new Rect();
                    decorView.getWindowVisibleDisplayFrame(rect);
                    if (point.y - rect.bottom == BaseUtil.getNavigationBarHeight(getReactApplicationContext())) {
                        RNUtils.sendEvent(getReactApplicationContext(), EVENT_NAVBAR_CHANGED, 0);
                    } else if ((point.y == rect.bottom)) {
                        RNUtils.sendEvent(getReactApplicationContext(), EVENT_NAVBAR_CHANGED, 1);
                    }
                }
//                RNUtils.sendEvent(getReactApplicationContext(), EVENT_NAVBAR_CHANGED,
//                        checkNavigationBarShow(getReactApplicationContext(), getCurrentActivity().getWindow()) ? 0 : 1);
            }
        }
    };

    private FrameLayout content;

    @ReactMethod
    public void setNavBarListener() {
        if (isSMARTISAN() && getCurrentActivity() != null) {
            content = getCurrentActivity().findViewById(android.R.id.content);
            if (content != null && content.getViewTreeObserver() != null) {
                content.getViewTreeObserver().addOnGlobalLayoutListener(globalLayoutListener);
            }
        } else {

            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                getReactApplicationContext().getContentResolver().registerContentObserver(Settings.System.getUriFor
                        ("navigationbar_is_min"), true, obsrver);
            } else {
                getReactApplicationContext().getContentResolver().registerContentObserver(Settings.Global.getUriFor
                        ("navigationbar_is_min"), true, obsrver);
            }
        }
    }

    @ReactMethod
    public void removeNavbarListener() {
        if (isSMARTISAN()) {
            if (content != null && content.getViewTreeObserver() != null) {
                content.getViewTreeObserver().removeOnGlobalLayoutListener(globalLayoutListener);
            }
        } else {
            getReactApplicationContext().getContentResolver().unregisterContentObserver(obsrver);
        }
    }

    private boolean isSMARTISAN() {
        return TextUtils.equals(Build.BRAND, "SMARTISAN");
    }

    public boolean checkNavigationBarShow(@NonNull Context context, @NonNull Window window) {
        boolean show;
        Display display = window.getWindowManager().getDefaultDisplay();
        Point point = new Point();
        display.getRealSize(point);

        View decorView = window.getDecorView();
        Configuration conf = context.getResources().getConfiguration();
        if (Configuration.ORIENTATION_LANDSCAPE == conf.orientation) {
            View contentView = decorView.findViewById(android.R.id.content);
            show = (point.x - contentView.getWidth() == BaseUtil.getNavigationBarHeight(context));
        } else {
            Rect rect = new Rect();
            decorView.getWindowVisibleDisplayFrame(rect);
            show = point.y - rect.bottom == BaseUtil.getNavigationBarHeight(context);
        }
        return show;
    }

    @Override
    public void onHostResume() {

    }

    @Override
    public void onHostPause() {

    }

    @Override
    public void onHostDestroy() {
        if (content != null && content.getViewTreeObserver() != null) {
            content.getViewTreeObserver().removeOnGlobalLayoutListener(globalLayoutListener);
            content = null;
        } else {
            getReactApplicationContext().getContentResolver().unregisterContentObserver(obsrver);
        }
    }
}
