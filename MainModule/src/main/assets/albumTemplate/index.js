(function() {
  for (var key in data) {
    if (data.hasOwnProperty(key) && typeof data[key] === "string") {
      data[key] = data[key].replace(/src=/g, "data-src=");
    }
  }
})();

function tempImg(img, link) {
  var $img = $(img);
  var first = true;
  $img.attr("src", link);
  $img.on("load", function() {
    $img.removeClass("placeHolder");
  });
  $img.on("error", function() {
    if (first) {
      var source = link.replace(/[&|\?]?magick=webp/, "");
      $img.attr("src", source);
    } else {
      $img.removeAttr("src");
    }
    first = false;
  });
}

function _getOffset(el) {
  if ($.os.ios) {
    return el.offsetTop;
  }
  var _t = el.offsetTop;
  while ((el = el.offsetParent)) {
    _t += el.offsetTop;
  }
  return _t;
}

function ctrlLength() {
  if (data.expandAll) return false;
  var cards = $(".j-card.load");
  var dpr = parseInt(document.querySelector("html").getAttribute("data-dpr"));
  cards.each(function(index, card) {
    if (card.offsetHeight / dpr > 620) {
      card.style.height = 620 * dpr + "px";
      $(".load-more").css("display", "block");
    }
  });
}

var $window = $(window);
function showInViewPort($imgs) {
  $imgs.each(function(index, e) {
    tempImg(e, e.getAttribute("data-preview"));
  });
}
var bt = baidu.template;
bt.ESCAPE = false;
var html = bt("t_album", data);
$("#container").html(html);
var $imgs = $(".j-card img");
$imgs.each(function(index, el) {
  var $this = $(this);
  var width = $this.width() || $this.attr("data-preview-width");
  $this.removeAttr("src");
  var ratio =
    $this.attr("data-preview-height") / $this.attr("data-preview-width");
  var height = width * ratio;
  // var width = $this.attr('data-preview-width')
  // var height = $this.attr('data-preview-height')
  // console.log(width, height)
  if ($.os.ios) {
    $this.css({
      width: width + "px",
      height: height + "px",
      display: "block"
    });
  }
  $this.addClass("placeHolder");
});
$(".load-more").css("display", "none");
if ($.os.ios) {
  ctrlLength();
}
window.onload = function() {
  setTimeout(loaded, 10);
  function loaded() {
    if ($.os.android) {
      ctrlLength();
    }
    var campService = $("#campservice");
    var commNum = $(".comment-box .comment-num");
    var joinGroup = $(".join-group");
    var $imgLoad = $(".img-loading");
    var joinCommunity = $(".join-community");
    // 主播介绍模块
    var anchorIntro = $(".anchor-intro .intro");
    var anchorFollowBtn = $(".anchor-intro .btn");
    var createItem = $(".create-item");
    var createBox = $(".create-box");

    var _offset = campService.offset();
    if (_offset) {
      _utils.requestHybrid({
        path: "scrollTo",
        param: {
          y: _offset.top,
          x: _offset.left
        }
      });
    }
    $imgLoad.each(function(index, img) {
      var $this = $(this);
      var img = $("<img/>");
      img.attr("src", $this.attr("data-head"));
      img.on("error", function() {
        $this.css({
          "background-image": "url(./user_head.png)"
        });
      });
      $this.css("background-image", "url(" + $this.attr("data-head") + ")");
    });
    showInViewPort($imgs);

    if (!!commNum.length) {
      commNum.on("click", function() {
        _utils.requestHybrid({
          path: "open_comment"
        });
      });
    }

    if (!!joinGroup.length) {
      joinGroup.on("click", function() {
        _utils.requestHybrid({
          path: "open_group"
        });
      });
    }

    if (!!joinCommunity.length) {
      joinCommunity.on("click", function() {
        var $this = $(this);
        console.log($this.data("url"));
        _utils.bridgePostMsg($this.data("url"));
      });
    }

    // 主播模块点击跳转
    if (!!anchorIntro.length) {
      anchorIntro.on("click", function() {
        var $this = $(this);
        var url = $this.data("url");
        _utils.requestHybrid({
          path: "open_anchor",
          param: {
            url,
            module: "anchor"
          }
        });
      });
    }
    // 主播模块点击关注
    if (!!anchorFollowBtn.length) {
      anchorFollowBtn.on("click", function() {
        _utils.requestHybrid({
          path: "click_follow"
        });
      });
    }

    // 创作团队点击跳转
    if (!!createItem.length) {
      createItem.on("click", function() {
        var $this = $(this);
        var url = $this.data("url");
        _utils.requestHybrid({
          path: "open_anchor",
          param: {
            url,
            module: "createItem"
          }
        });
      });
    }
    let startX = 0,
        startY = 0;
    if (!!createBox.length) {
      createBox.on("touchstart", function(e) {
        e.stopPropagation();
        startX = e.touches[0].pageX;
        startY = e.touches[0].pageY;
        _utils.requestHybrid({
          path: "lock_scroll"
        });
      });
      createBox.on("touchmove", function(e) {
        var moveX = e.touches[0].pageX;
        var moveY = e.touches[0].pageY;
        if (Math.abs(moveX - startX) < Math.abs(moveY - startY)) {
          e.preventDefault();
        }
      });
      createBox.on("touchcancel touchend", function(e) {
        _utils.requestHybrid({
          path: "unlock_scroll"
        });
      });
    }

    var loadMores = $(".load-more");
    loadMores.each(function(index, loadMore) {
      var loadTxt = loadMore.querySelector(".load-txt");
      loadTxt.addEventListener("click", function() {
        var expandName = loadTxt.getAttribute('data-expand');
        if (expandName) {
          _utils.requestHybrid({path: "expand_" + expandName});
        }
        loadMore.parentNode.style.height = "auto";
        loadMore.style.display = "none";
        setTimeout(function() {
          var _offset = campService.offset();
          _utils.requestHybrid({
            path: "post_height"
          });
          _utils.requestHybrid({
            path: "scrollTo",
            param: {
              y: _offset.top,
              x: _offset.left
            }
          });
        }, 10);
      });
    });
    $(".j-card img[data-preview]").on("click", function(index, e) {
      var $this = $(this);
      var _this = this;
      var curIndex = -1;
      var $card = $this.closest(".j-card");
      var large = [],
        origin = [];
      $card.find("img[data-preview]").each(function(index, e) {
        var $el = $(e);
        if (_this === e) {
          curIndex = index;
        }
        large.push($el.data("large") || $el.data("preview"));
        origin.push($el.data("origin") || "");
      });
      _utils.requestHybrid({
        path: "post_photo",
        param: {
          "data-large": large,
          "data-origin": origin,
          index: curIndex
        }
      });
    });
  }
};
/**
   * 设置body背景颜色、文字颜色
   * **/
  var $bodyFontColor = $("body").attr("data-font-color");
  var $bodyBgColor = $("body").attr("data-bg-color");
  var $bodyBgColorAlpha = $("body").attr("data-bg-color-alpha");
  var $showMore = $(".j-card .load-more .load-txt");
  if ($bodyFontColor) {
    var $fontList = [
      $("body"),
      $(".j-card"),
      $(".j-card .anchor-intro .m-info .name"),
      $(".j-card .title"),
      $(".j-card .intro"),
      $(".j-card .title .txt"),
      $(".j-card .anchor-intro"),
      $(".j-card .create-team"),
      $(".j-card .desc"),
      $(".j-card p"),
      $(".j-card span"),
      $(".create-item .nickname"),
      $(".join-group .group-title"),
      $(".j-card .group-title"),
    ];

    $(".j-card .rich-intro").each(function () {
            $(this).find("p,span,b,block").css("color", $bodyFontColor);
          });

    /*var richIntro = $("#rich-intro");
    var newStr = richIntro.html().replace(/style="[^"]*"/g, " ");
    richIntro.html(newStr);*/

    $fontList.forEach((ele) => {
      ele.css("color", $bodyFontColor);
          ele.children("*").css("color", $bodyFontColor);

    });
    $showMore.css({
      padding: "0.5rem 0 0.48rem",
    });
  }
  if ($bodyBgColor) {
    var $bgList = [
      $("body"),
      $(".j-card"),
      $(".j-card .title .txt"),
      $(".j-card .load-more .load-txt"),
    ];
    $bgList.forEach((ele) => {
      ele.css({
        background: $bodyBgColor,
      });
    });
    var $bgMdList = [
      $(".j-card  .create-item"),
      $(".j-card .join-group"),
      $(".j-card .join-community"),
    ];
    $bgMdList.forEach((ele) => {
      ele.css({
        background: "#00000033",
      });
    });
    var $showMoreMask = $(".j-card .load-more .mask");
    $showMoreMask.css({
      background:
        "linear-gradient(-180deg, " +
        $bodyBgColorAlpha +
        " 0%, " +
        $bodyBgColor +
        " 100% )",
    });
  }