<!DOCTYPE html>
<html>
<head>
  <title>ximalaya</title>
  <meta charset="utf-8" />
  <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
  />
  <meta name="format-detection" content="telephone=no" />
  <link
          rel="stylesheet"
          type="text/css"
          href="file:///android_asset/albumTemplate/style.css"
  />
  <script src="file:///android_asset/albumTemplate/flexible.js"></script>
</head>
<body>
<div id="container"></div>
<script id="t_album" type="text/template">
  <%var personalDescription;if(!!personalDescription){%>
  <div class="j-card">
    <div class="title">
      <span class="txt">主讲人</span>
    </div>
    <div class="intro">
      <%= personalDescription %>
    </div>
  </div>
  <%}%>
  <%var introRich;if(!!introRich){%>
  <div class="j-card load">
    <div class="title"><span class="txt">简介</span></div>
    <%=introRich%>
    <div class="load-more">
      <div class="mask"></div>
      <p class="load-txt">展开全部</p>
    </div>
  </div>
  <%}%>
  <%var outline;if(!!outline){%>
  <div class="j-card load">
    <div class="title"><span class="txt">专辑大纲</span></div>
    <div class="sub-title">估计更新<%=totalTrackCount%>集</div>
    <%=outline%>
    <div class="load-more">
      <div class="mask"></div>
      <p class="load-txt">展开全部</p>
    </div>
  </div>
  <%}%>
  <%var industryRecommend; if(!!industryRecommend){%>
  <div class="j-card comment-box">
    <div class="title"><span class="txt">大咖推荐</span></div>
    <%=industryRecommend%>
  </div>
  <%}%>
  <%if(!!comments){%>
  <div class="j-card comment-box">
    <div class="title"><span class="txt">听友说</span></div>
    <ul>
      <%for(var i = 0; i < comments.list.length; i++){%>
      <li class="comment-item">
        <p class="comment-content"><%=comments.list[i].content%></p>
        <div class="clear">
          <p class="star-bar fl">
            <span class="star-grade"><span class="star-graded" style="width:<%=(comments.list[i].album_score / 5) * 100 %>%"></span></span>
            <span class="c09 grade"><%=comments.list[i].album_score.toFixed(1)%></span>
          </p>
          <p class="fr user"><span class="nickname"><%=comments.list[i].nickname%></span><span class="userPic img-loading" data-head="<%=comments.list[i].smallHeader%>"></span></p>
        </div>
      </li>
      <%}%>
    </ul>
    <p class="comment-num" style="margin-bottom: -10px;">共<%=comments.totalCount%>条评论</p>
  </div>
  <%}%>
  <%if (!!groupInfo){%>
  <div class="j-card">
    <div class="title"><span class="txt">加入付费专享群</span></div>

    <div class="sub-title">购买专辑，与主讲人亲密互动</div>
    <div class="join-group">
      <div class="head-pic img-loading" data-head="<%=groupInfo.coverPath%>"></div>
      <div class="content">
        <div class="group-title ellipsis"><%=groupInfo.name%></div>
        <div class="tags">
          <span class="tag-pay tag"></span>
          <span class="tag-num tag"><%=groupInfo.memberCount%>人</span>
        </div>
        <div class="intro ellipsis"><%=groupInfo.intro%></div>
      </div>
    </div>
  </div>
  <%}%>
  <%var communityInfo;if (!!communityInfo){%>
  <div class="j-card">
    <div class="title">
      <span class="txt">加入主播圈子</span>
    </div>
    <div class="sub-title"> 购买专辑，参与主讲人的讨论互动</div>
    <div class="join-community" data-id="<%=communityInfo.id%>" data-sectionId="<%=communityInfo.sectionId%>" data-url="<%=communityInfo.url%>">
      <div class="head-pic img-loading square" data-head="<%=communityInfo.logoSmall%>"></div>
      <div class="content">
        <div class="group-title ellipsis">
          <%=communityInfo.name%>
        </div>
        <div class="group-info">
          <span>成员 <%=communityInfo.memberCount%></span>
          <span>帖子 <%=communityInfo.articleCount%></span>
        </div>
        <div class="intro ellipsis">
          <%=communityInfo.introduce%>
        </div>
      </div>
    </div>
  </div>
  <%}%>
  <%var supports;if(!!supports){%>
  <div class="j-card">
    <div class="title"><span class="txt">服务说明</span></div>
    <%for(var i = 0; i < supports.items.length; i++){%>
    <p class="txt-11 c01"><%= supports.items[i].itemName%></p>
    <p class="txt-11"><%= supports.items[i].itemDetail%></p>
    <%}%>
  </div>
  <%}%>
  <%var buyNotes;if(!!buyNotes){%>
  <div class="j-card">
    <div class="title"><span class="txt">购买须知</span></div>
    <%=buyNotes%>
  </div>
  <%}%>
  <%var staff;if(!!staff){%>
  <div class="j-card">
    <div class="title"><span class="txt">制作团队</span></div>
    <%=staff%>
  </div>
  <%}%>
  <%if(!!other_content){%>
  <div class="j-card">
    <div class="title"><span class="txt">其他说明</span></div>
    <%=other_content%>
  </div>
  <%}%>
</script>
<script>
      var data;
    </script>
<script src="file:///android_asset/albumTemplate/lib.js"></script>
<script
        type="text/javascript"
        src="file:///android_asset/albumTemplate/index.js"
></script>
</body>
</html>
