<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="107dp"
    android:layout_height="wrap_content">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_cover_iv"
        android:layout_width="107dp"
        android:layout_height="107dp"
        app:corner_radius="4dp"
        android:scaleType="centerCrop"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/main_color_ff6444" />

    <View
        android:id="@+id/main_track_play_icon"
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:layout_marginRight="6dp"
        android:layout_marginBottom="@dimen/host_y6"
        android:background="@drawable/main_bg_rect_000000_20_radius_26in"
        app:layout_constraintBottom_toBottomOf="@+id/main_cover_iv"
        app:layout_constraintRight_toRightOf="@+id/main_cover_iv" />

    <ImageView
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:src="@drawable/main_channel_play_icon_small"
        app:layout_constraintBottom_toBottomOf="@+id/main_track_play_icon"
        app:layout_constraintEnd_toEndOf="@+id/main_track_play_icon"
        app:layout_constraintStart_toStartOf="@+id/main_track_play_icon"
        app:layout_constraintTop_toTopOf="@+id/main_track_play_icon" />

    <TextView
        android:id="@+id/main_title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/host_y16"
        android:ellipsize="end"
        android:lines="2"
        android:textColor="@color/main_color_333333_dcdcdc"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_cover_iv"
        tools:text="vol. 01 暧昧期无疑是甜味的，但回暧昧期无疑是甜味的，但回但回暧昧期无疑是甜味的" />

    <TextView
        android:id="@+id/main_subtitle_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/host_y8"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="@color/main_color_999999_66666b"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_title_tv"
        tools:text="每日一笑系列笑话演播" />

</androidx.constraintlayout.widget.ConstraintLayout>