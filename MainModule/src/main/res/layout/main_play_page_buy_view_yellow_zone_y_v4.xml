<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_cl_buy_view_yellow_zone_y_v2"
    android:layout_width="match_parent"
    android:layout_height="52dp"
    android:visibility="visible"
    tools:background="@color/host_red"
    tools:visibility="visible">

    <FrameLayout
        android:id="@+id/main_fl_btn_container"
        android:layout_width="wrap_content"
        android:layout_height="52dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/main_buy_view_yellow_zone_btn_area"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="@dimen/host_x16"
            android:background="@null"
            app:cardCornerRadius="110dp"
            app:cardElevation="-100dp"
            tools:cardBackgroundColor="#FCE9E1"
            tools:visibility="visible">

            <TextView
                android:id="@+id/main_buy_view_yellow_zone_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/host_y28"
                android:minWidth="68dp"
                android:layout_gravity="center"
                android:gravity="center"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:paddingHorizontal="@dimen/host_x10"
                android:paddingVertical="@dimen/host_y4"
                android:textColor="#f09068"
                android:textFontWeight="500"
                android:textSize="12dp"
                android:visibility="visible"
                tools:text="首页"
                tools:textColor="#E87B4E"
                tools:visibility="visible" />
        </androidx.cardview.widget.CardView>
    </FrameLayout>


    <LinearLayout
        android:id="@+id/main_ll_logo_container"
        android:layout_width="wrap_content"
        android:layout_height="52dp"
        android:layout_marginStart="@dimen/host_x16"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/main_iv_logo"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:scaleType="fitXY"
            tools:src="@drawable/main_ic_yellow_bar_vip_logo" />

        <View
            android:id="@+id/main_vip_view_vertical_split"
            android:layout_width="0.75dp"
            android:layout_height="14dp"
            android:layout_marginHorizontal="@dimen/host_x8"
            tools:background="@color/host_black" />
    </LinearLayout>

    <TextView
        android:id="@+id/main_buy_view_yellow_zone_text"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginEnd="@dimen/host_x8"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:textColor="@color/host_white"
        android:textFontWeight="500"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/main_fl_btn_container"
        app:layout_constraintStart_toEndOf="@+id/main_ll_logo_container"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="@dimen/host_default_side_margin"
        app:layout_goneMarginLeft="12dp"
        app:layout_goneMarginStart="@dimen/host_default_side_margin"
        tools:text="会员音质特权体验会员音质特权体验会员音质特权体验会员音质特权体验" />


    <ImageView
        android:id="@+id/main_buy_view_yellow_zone_btn_close"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_gravity="top"
        android:paddingHorizontal="4dp"
        android:paddingVertical="4dp"
        android:scaleType="fitXY"
        android:src="@drawable/host_ic_x_close_n_line_regular_16"
        android:tint="@color/main_color_ffffff_alpha_50"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>