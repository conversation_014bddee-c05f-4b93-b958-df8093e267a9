<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:layout_centerInParent="true"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/main_bg_rect_white_corner_10">

    <TextView
        android:id="@+id/comment_theme_et_title_tv"
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="18dp"
        android:gravity="center_horizontal"
        tools:text="这是标题"
        android:textColor="@color/main_color_333333_cfcfcf"
        android:textSize="14sp" />


    <EditText
        android:id="@+id/comment_theme_et_floor_et"
        android:hint="整数"
        android:layout_below="@+id/comment_theme_et_title_tv"
        android:maxLines="1"
        android:inputType="number"
        android:gravity="center"
        android:textSize="13sp"
        android:layout_marginEnd="30dp"
        android:includeFontPadding="false"
        android:paddingBottom="10dp"
        android:paddingTop="10dp"
        android:paddingEnd="13dp"
        android:paddingStart="13dp"
        android:layout_marginStart="30dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/main_rect_e8e8e8_strock"
        android:textColorHint="@color/main_color_cccccc_414141"
        android:textColor="@color/main_color_666666_cfcfcf"
        android:layout_width="220dp"
        android:layout_height="wrap_content" />



    <View
        android:id="@+id/main_v_divider"
        android:layout_width="280dp"
        android:layout_height="1px"
        android:layout_marginTop="15dp"
        android:layout_below="@id/comment_theme_et_floor_et"
        android:background="@color/main_color_e8e8e8_1e1e1e" />

    <LinearLayout
        android:layout_width="280dp"
        android:layout_height="48dp"
        android:layout_below="@id/main_v_divider"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/main_tv_cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/main_color_666666_888888"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/main_tv_ok"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/main_bg_rect_f86442_radius_0_0_10_0"
            android:gravity="center"
            android:text="确认"
            android:textColor="@color/main_color_white"
            android:textSize="16sp" />
    </LinearLayout>
</RelativeLayout>