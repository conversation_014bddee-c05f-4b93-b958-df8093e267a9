<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/main_recommend_stream_common_bg"
    android:paddingTop="16dp"
    android:paddingBottom="8dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/main_tv_title"
        android:layout_marginStart="@dimen/main_category_recommend_item_not_card_margin"
        android:textSize="@dimen/main_module_category_title_text_size"
        android:textColor="@color/main_color_333333_cfcfcf"
        android:textStyle="bold"
        android:fontFamily="sans-serif-light"
        android:text="直播"/>

    <com.ximalaya.ting.android.main.view.RecyclerViewCanDisallowIntercept
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:id="@+id/main_rv_content"/>

</LinearLayout>