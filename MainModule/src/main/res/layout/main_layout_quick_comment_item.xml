<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="41dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingTop="8dp"
    android:paddingRight="8dp"
    android:paddingBottom="8dp">

    <TextView
        android:id="@+id/main_tv_content"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        tools:text="一键三连😊😊"
        android:paddingLeft="12dp"
        android:background="@drawable/main_bg_rect_ffffff_border_eeeeee_radius_25"
        android:gravity="center_vertical"
        android:paddingRight="12dp"
        android:textColor="@color/host_color_333333_dcdcdc"
        android:textSize="12sp" />

</FrameLayout>