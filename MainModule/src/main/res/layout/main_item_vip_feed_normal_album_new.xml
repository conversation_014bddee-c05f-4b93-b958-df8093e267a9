<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:minHeight="106dp"
    android:background="@color/host_color_ffffff_121212">

    <View
        android:id="@+id/main_top_divider"
        android:visibility="gone"
        style="@style/main_recommend_vip_page_top_divider_style"
        tools:visibility="visible" />

    <androidx.cardview.widget.CardView
        android:id="@+id/main_rl_cover"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="13dp"
        android:layout_marginBottom="13dp"
        android:layout_below="@id/main_top_divider"
        app:cardBackgroundColor="@color/host_transparent"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp">

        <ImageView
            android:id="@+id/main_iv_album_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:background="@drawable/main_bg_cover_border_corner_4"
            android:scaleType="fitXY"
            android:src="@drawable/host_default_album" />

        <ImageView
            android:id="@+id/main_iv_album_cover_tag"
            style="@style/main_album_cover_tag_size_small"
            android:layout_alignTop="@id/main_iv_album_cover" />

        <ImageView
            android:id="@+id/main_iv_activity_tag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom"
            android:contentDescription="@string/main_activity_tag"
            android:scaleType="fitStart"
            android:visibility="invisible"
            tools:visibility="visible" />

    </androidx.cardview.widget.CardView>

    <LinearLayout
        android:id="@+id/main_title_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:minHeight="55dp"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="50dp"
        android:layout_alignTop="@id/main_rl_cover"
        android:layout_toRightOf="@id/main_rl_cover">

        <TextView
            android:id="@+id/main_tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            android:textStyle="bold"
            android:textColor="@color/main_color_black"
            android:fontFamily="sans-serif-light"
            android:maxLines="2"
            android:ellipsize="end"
            android:lineSpacingExtra="3dp"
            tools:text="经典碰撞3D环绕，音乐在脑子里跳舞" />

        <TextView
            android:id="@+id/main_tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="@color/main_color_777777_a0a0a0"
            android:maxLines="1"
            android:singleLine="true"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:lineSpacingExtra="3dp"
            android:layout_marginTop="8dp"
            tools:text="怀旧经典碰撞3D，独特体验来非一般的体验。" />

    </LinearLayout>



    <TextView
        android:id="@+id/main_tv_play_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="11sp"
        android:textColor="@color/main_color_777777_888888"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        android:drawableLeft="@drawable/host_ic_recommend_stream_play_count"
        android:layout_marginTop="9dp"
        android:layout_below="@id/main_title_area"
        android:layout_alignLeft="@+id/main_title_area"
        tools:text="2456" />

    <ImageView
        android:id="@+id/main_iv_album_dislike"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/main_rl_cover"
        android:layout_alignParentRight="true"
        android:layout_marginTop="-4dp"
        android:layout_marginRight="6dp"
        android:contentDescription="@string/main_iv_cd_dislike"
        android:padding="10dp"
        android:scaleType="fitXY"
        android:src="@drawable/main_ic_recommend_close" />

    <View
        android:id="@+id/main_feed_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@id/main_tv_play_count"
        android:layout_toRightOf="@+id/main_rl_cover"
        android:layout_marginTop="12dp"
        android:layout_marginLeft="12dp"
        android:background="@color/main_color_dddddd_1e1e1e" />

    <View
        android:id="@+id/main_bottom_divider"
        android:visibility="gone"
        android:layout_marginTop="8dp"
        android:layout_below="@id/main_tv_play_count"
        style="@style/main_recommend_vip_page_bottom_divider_style"
        tools:visibility="visible" />

</RelativeLayout>