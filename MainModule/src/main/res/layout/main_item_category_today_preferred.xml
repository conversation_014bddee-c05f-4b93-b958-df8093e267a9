<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:id="@+id/main_v_top_bg"
        android:background="@color/main_transparent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="0dp"/>

    <View
        android:layout_width="0dp"
        android:layout_height="26dp"
        app:layout_constraintStart_toStartOf="@+id/main_tv_date"
        app:layout_constraintEnd_toEndOf="@+id/main_tv_module_title"
        app:layout_constraintTop_toTopOf="@+id/main_tv_date"
        android:background="@drawable/main_bg_ffffff_stroke_0_14_14_0"/>

    <View
        android:layout_width="0dp"
        android:layout_height="26dp"
        app:layout_constraintStart_toStartOf="@+id/main_tv_date"
        app:layout_constraintEnd_toEndOf="@+id/main_tv_date"
        app:layout_constraintTop_toTopOf="@+id/main_tv_date"
        android:background="@drawable/main_bg_ffffff_coner_14_0_0_14"/>

    <View
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:background="@drawable/main_ic_today_preferred_bg"
        app:layout_constraintStart_toEndOf="@+id/main_s_date"
        app:layout_constraintTop_toTopOf="@+id/main_tv_date"
        android:id="@+id/main_v_bg"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="26dp"
        android:id="@+id/main_tv_date"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/main_v_top_bg"
        android:layout_marginStart="22dp"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:textColor="@color/host_color_663a22"
        android:paddingLeft="9dp"
        android:textSize="15sp"
        android:background="@color/main_transparent"
        tools:text="9"/>

    <Space
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="@+id/main_tv_date"
        android:layout_marginEnd="6dp"
        android:id="@+id/main_s_date"/>

    <Space
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="@+id/main_v_bg"
        android:layout_marginEnd="6dp"
        android:id="@+id/main_s_bg"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="26dp"
        android:id="@+id/main_tv_module_title"
        android:gravity="center_vertical"
        android:textColor="@color/host_color_ffffff"
        android:textSize="15sp"
        app:layout_constraintStart_toEndOf="@+id/main_s_bg"
        app:layout_constraintTop_toTopOf="@+id/main_tv_date"
        android:maxLines="1"
        android:ellipsize="end"
        android:paddingRight="11dp"
        tools:text="李云迪的肖邦之曲刘敏涛李云迪的肖邦之曲刘敏涛李云迪的肖邦之曲刘敏涛"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="往期优选"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/main_tv_date"
        app:layout_constraintBottom_toBottomOf="@+id/main_tv_date"
        android:layout_marginEnd="20dp"
        android:id="@+id/main_tv_more"
        android:textColor="@color/host_color_ffffff"
        style="@style/main_category_recommend_common_more_btn"/>

    <include
        android:id="@+id/main_layout_album"
        app:layout_constraintTop_toBottomOf="@+id/main_v_top_bg"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        layout="@layout/main_item_category_preferred" />
</androidx.constraintlayout.widget.ConstraintLayout>