<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/main_podcast_cover_shadow"
        app:layout_constraintStart_toStartOf="@+id/main_album_single_album_cover"
        app:layout_constraintEnd_toEndOf="@+id/main_album_single_album_cover"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/main_album3_pod_cover_shadow_new"
        android:scaleType="centerCrop"
        android:layout_marginEnd="15dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
        android:id="@+id/main_album_single_album_cover"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:albumCoverSize="112dp"
        android:contentDescription="专辑封面"
        android:scaleType="centerCrop"
        app:corner_radius="6dp"
        android:src="@drawable/host_default_album"
        android:layout_marginLeft="@dimen/host_x16"
        android:layout_marginTop="16dp"
        android:importantForAccessibility="no"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/main_album_single_album_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/host_x12"
        android:layout_marginRight="@dimen/host_x16"
        android:fontFamily="sans-serif-light"
        android:includeFontPadding="false"
        android:lineSpacingExtra="2sp"
        android:maxLines="2"
        android:ellipsize="end"
        android:textColor="@color/main_color_131313_ffffff"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/main_album3_pod_live_anchor_layout"
        app:layout_constraintLeft_toRightOf="@id/main_album_single_album_cover"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/main_album_single_album_cover"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="你的孤独，虽败犹荣耀而最"
        tools:textColor="@color/main_black" />


    <LinearLayout
        android:id="@+id/main_album3_pod_live_anchor_layout"
        android:orientation="vertical"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/host_x12"
        android:layout_marginTop="@dimen/host_y10"
        android:gravity="center_vertical"
        android:layout_marginRight="@dimen/host_x16"
        app:layout_constraintBottom_toBottomOf="@+id/main_album_single_album_cover"
        app:layout_constraintLeft_toRightOf="@id/main_album_single_album_cover"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_album_single_album_title">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_album_anchor_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/main_album_single_anchor_portrait3"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:padding="1dp"
                android:scaleType="centerCrop"
                app:layout_goneMarginStart="0dp"
                android:layout_marginStart="12dp"
                app:border_color="@color/host_color_f7f9fc_181818"
                app:border_width="0.5dp"
                app:corner_radius="1000dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@+id/main_album_single_anchor_portrait2"
                app:layout_constraintTop_toTopOf="parent"
                android:importantForAccessibility="no"
                tools:src="@drawable/host_default_avatar_88" />

            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/main_album_single_anchor_portrait2"
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:padding="1dp"
                android:scaleType="centerCrop"
                android:layout_marginStart="12dp"
                app:layout_goneMarginStart="0dp"
                android:visibility="gone"
                app:border_color="@color/host_color_f7f9fc_181818"
                app:border_width="0.5dp"
                app:corner_radius="1000dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@+id/main_album_single_anchor_portrait1"
                app:layout_constraintTop_toTopOf="parent"
                android:importantForAccessibility="no"
                tools:src="@drawable/host_default_avatar_88" />

            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/main_album_single_anchor_portrait1"
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:padding="1dp"
                android:scaleType="centerCrop"
                app:border_color="@color/host_color_f7f9fc_181818"
                app:border_width="0.5dp"
                app:corner_radius="1000dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:importantForAccessibility="no"
                tools:src="@drawable/host_default_avatar_88" />

            <ImageView
                android:id="@+id/main_album_single_anchor_small_tag"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:scaleType="fitXY"
                android:layout_marginEnd="0.25dp"
                android:layout_marginBottom="0.25dp"
                android:visibility="gone"
                tools:visibility="visible"
                tools:src="@drawable/host_ic_zhanwai_v"
                android:importantForAccessibility="no"
                app:layout_constraintBottom_toBottomOf="@+id/main_album_single_anchor_portrait3"
                app:layout_constraintEnd_toEndOf="@+id/main_album_single_anchor_portrait3" />

            <TextView
                android:id="@+id/main_album_single_anchor_name"
                android:layout_marginStart="@dimen/host_x8"
                app:layout_constraintBottom_toTopOf="@+id/main_album_single_anchor_desc"
                app:layout_constraintStart_toEndOf="@+id/main_album_single_anchor_portrait3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/main_album_single_anchor_portrait3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:includeFontPadding="false"
                android:ellipsize="end"
                android:textStyle="bold"
                android:fontFamily="sans-serif-light"
                android:gravity="center_vertical"
                android:lines="1"
                android:singleLine="true"
                android:textColor="@color/main_color_393942_dcdcdc"
                android:textSize="12sp"
                tools:text="有声的紫襟有声的紫襟" />

            <TextView
                android:id="@+id/main_album_single_anchor_desc"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:visibility="visible"
                tools:visibility="gone"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:gravity="center_vertical"
                android:lines="1"
                android:singleLine="true"
                android:textColor="@color/main_color_66131313_66ffffff"
                android:textSize="12sp"
                android:layout_marginStart="@dimen/host_x8"
                app:layout_constraintBottom_toBottomOf="@+id/main_album_single_anchor_portrait3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main_album_single_anchor_portrait3"
                app:layout_constraintTop_toBottomOf="@+id/main_album_single_anchor_name"
                tools:text="实力唱将实力唱将实力唱将实力唱将实力唱将实力唱将" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>