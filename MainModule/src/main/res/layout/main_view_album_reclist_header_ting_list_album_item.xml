<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingStart="4dp"
    android:paddingEnd="4dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:id="@+id/main_cover_group"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_album_more_rec_list_tv"
        android:layout_width="101dp"
        android:layout_height="101dp"
        app:cardBackgroundColor="@color/host_transparent"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp">
        <!--专辑图-->
        <ImageView
            android:id="@+id/main_album_cover"
            android:scaleType="centerCrop"
            android:src="@drawable/host_default_album"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="0dp"
            android:layout_marginBottom="0dp"
            android:contentDescription="@string/main_content_description_album_default" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:layout_gravity="left|bottom"
            android:orientation="horizontal"
            android:background="@drawable/main_gradient_4d000000_00000000">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:layout_marginRight="3dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/main_rec_list_play_ic" />

            <TextView
                android:id="@+id/main_album_rec_list_playcount_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:textSize="10sp"
                android:textColor="@color/main_white"
                android:ellipsize="end"
                android:lines="1"
                tools:text="11.2亿12121231" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!--专辑标题-->
    <TextView
        android:id="@+id/main_title_tv"
        app:layout_constraintEnd_toEndOf="@+id/main_cover_group"
        android:layout_width="0dp"
        app:layout_constraintStart_toStartOf="@+id/main_cover_group"
        app:layout_constraintTop_toBottomOf="@+id/main_cover_group"
        android:layout_height="wrap_content"
        android:layout_marginLeft="0dp"
        android:layout_marginTop="@dimen/host_y5"
        android:layout_marginRight="0dp"
        android:paddingTop="3dp"
        android:ellipsize="end"
        android:gravity="top"
        android:lines="2"
        android:textColor="@color/main_color_333333_dcdcdc"
        android:textSize="13sp"
        tools:text="马红漫.财商课马红漫.财商课马红漫.财商课马红漫.财商课马红漫.财商课马红漫.财商课马红漫.财商课马红漫.财商课马红漫.财商课" />

</androidx.constraintlayout.widget.ConstraintLayout>