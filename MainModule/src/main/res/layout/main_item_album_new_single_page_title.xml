<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    xmlns:tools="http://schemas.android.com/tools">

    <com.ximalaya.ting.android.host.view.text.MarqueeTextView
        android:id="@+id/main_album_single_page_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="marquee"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:marqueeRepeatLimit="marquee_forever"
        android:scrollHorizontally="true"
        android:singleLine="true"
        android:textColor="@color/main_white"
        android:textSize="15sp"
        tools:text="专辑名称专辑名称专辑名称专辑名称专辑名称" />

    <LinearLayout
        android:id="@+id/main_album_title_rating_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_below="@id/main_album_single_page_title">

        <RatingBar
            android:id="@+id/main_album_single_page_rating"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:numStars="5"
            android:visibility="gone"
            android:clickable="false"
            style="@style/main_album_title_star"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/main_album_single_page_point"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:textColor="@color/main_color_ffd389"
            android:textSize="12sp"
            android:visibility="gone"
            tools:text="9.8"
            tools:visibility="visible" />
    </LinearLayout>

</RelativeLayout>