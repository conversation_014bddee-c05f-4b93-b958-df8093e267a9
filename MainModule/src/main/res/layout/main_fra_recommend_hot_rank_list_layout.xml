<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_fl_root_view"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/main_ll_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/main_bg_new_rank_list_shape"
        android:orientation="vertical"
        tools:layout_width="375dp">

        <LinearLayout
            android:id="@+id/main_ll_title_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="bottom"
            android:minHeight="40dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <ImageView
                        android:id="@+id/main_iv_rank_title_left"
                        android:layout_width="9dp"
                        android:layout_height="16dp"
                        android:src="@drawable/main_icon_rank_title_left"
                        android:tint="@color/main_color_module_title"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/main_fl_module_title"
                        app:layout_constraintTop_toTopOf="parent" />

                    <FrameLayout
                        android:id="@+id/main_fl_module_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="2dp"
                        android:gravity="center"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/main_iv_rank_title_left"
                        app:layout_constraintRight_toLeftOf="@+id/main_iv_rank_title_right"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/main_tv_module_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:fontFamily="sans-serif-light"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            android:singleLine="true"
                            android:textFontWeight="700"
                            android:textColor="@color/main_color_module_title"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:text="四大名著" />

                        <ImageView
                            android:id="@+id/main_iv_module_title"
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:visibility="gone"
                            android:scaleType="fitXY" />

                    </FrameLayout>

                    <ImageView
                        android:id="@+id/main_iv_rank_title_right"
                        android:layout_width="9dp"
                        android:layout_height="16dp"
                        android:src="@drawable/main_icon_rank_title_right"
                        android:tint="@color/main_color_module_title"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/main_fl_module_title"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/main_tv_more"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableEnd="@drawable/main_recommend_header_right_more"
                    android:drawableTint="@color/main_color_4d2c2c3c_8d8d91"
                    android:focusableInTouchMode="true"
                    android:fontFamily="sans-serif-light"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:paddingStart="16dp"
                    android:text="全部"
                    android:textColor="@color/main_color_4d2c2c3c_998d8d91"
                    android:textSize="13sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/main_rv_item_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="8dp" />

    </LinearLayout>

</FrameLayout>