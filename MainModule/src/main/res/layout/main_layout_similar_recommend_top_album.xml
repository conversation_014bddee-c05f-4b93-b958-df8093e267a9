<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/host_id_stickynavlayout_topview"
    android:layout_width="match_parent"
    android:layout_height="144dp"
    android:background="#494a5a">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_similar_recommend_top_album_cover"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="16dp"
        android:layout_marginBottom="32dp"
        android:contentDescription="@string/main_album_cover"
        android:scaleType="centerCrop"
        app:border_color="@color/main_color_ffffff_alpha_50"
        app:border_width="1dp"
        app:corner_radius="4dp"
        app:pressdown_shade="false"
        tools:src="@drawable/host_default_album" />

    <TextView
        android:id="@+id/main_similar_recommend_top_album_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/main_similar_recommend_top_album_cover"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="54dp"
        android:layout_toRightOf="@+id/main_similar_recommend_top_album_cover"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/main_color_ffffff"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="刘敏涛·古今女子图鉴·刘敏涛·古今女子图鉴·刘敏涛·古今女子图鉴·" />

    <TextView
        android:id="@+id/main_similar_recommend_top_album_play_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/main_similar_recommend_top_album_title"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="11dp"
        android:layout_toRightOf="@+id/main_similar_recommend_top_album_cover"
        android:drawableLeft="@drawable/main_ic_recommend_listen_light"
        android:drawablePadding="6dp"
        android:drawableTint="@color/main_color_BBBBBB"
        android:textColor="@color/main_color_BBBBBB"
        android:textSize="10sp"
        android:visibility="gone"
        tools:text="1028"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/main_similar_recommend_top_album_tracks_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/main_similar_recommend_top_album_title"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="11dp"
        android:layout_toRightOf="@+id/main_similar_recommend_top_album_play_count"
        android:drawableLeft="@drawable/main_ic_recommend_track_count_light"
        android:drawablePadding="6dp"
        android:drawableTint="@color/main_color_BBBBBB"
        android:textColor="@color/main_color_BBBBBB"
        android:textSize="10sp"
        android:visibility="gone"
        tools:text="1028"
        tools:visibility="visible" />

    <View
        android:layout_width="match_parent"
        android:layout_height="12dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/main_bg_radius_12_color_white_121212" />

</RelativeLayout>