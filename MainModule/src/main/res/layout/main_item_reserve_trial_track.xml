<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_card_item_root_ll"
    android:gravity="center"
    android:layout_width="280dp"
    android:background="@drawable/main_bg_f0f1f3_0affffff_radius_6"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/main_title_tv"
        tools:text="试听：地球基础科学出现异常的扰动，科学界人心惶惶。"
        android:maxLines="2"
        android:ellipsize="end"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:fontFamily="sans-serif-light"
        android:lineSpacingExtra="4dp"
        android:layout_marginEnd="20dp"
        android:layout_marginStart="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/main_play_iv_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="0dp"
        android:layout_height="match_parent" />
    
    <FrameLayout
        android:id="@+id/main_play_iv_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/host_round_142c2c3c_298d8d91"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="@dimen/host_x16"
        android:layout_width="34dp"
        android:layout_height="34dp">

        <ImageView
            android:id="@+id/main_play_iv"
            android:src="@drawable/host_ic_show_note_play"
            android:scaleType="fitXY"
            android:layout_gravity="center"
            android:layout_width="16dp"
            android:layout_height="16dp" />

    </FrameLayout>




</androidx.constraintlayout.widget.ConstraintLayout>