<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/main_iv_album_cover_bg"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:layout_centerVertical="true"
        app:layout_constraintStart_toStartOf="@+id/main_iv_cover"
        android:layout_marginStart="18dp"
        app:layout_constraintTop_toTopOf="@+id/main_iv_cover"
        app:layout_constraintBottom_toBottomOf="@+id/main_iv_cover"
        android:src="@drawable/main_ic_anchor_album_comment_cover_bg"
        />

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:layout_width="56dp"
        android:layout_height="56dp"
        app:corner_radius="4dp"
        android:contentDescription="@string/main_album_cover"
        android:id="@+id/main_iv_cover"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="16dp"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="16dp"/>

    <com.ximalaya.ting.android.framework.view.image.FlexibleRoundImageView
        android:id="@+id/main_iv_album_pay_cover_tag"
        style="@style/main_album_cover_tag_size_small"
        app:layout_constraintLeft_toLeftOf="@id/main_iv_cover"
        app:layout_constraintTop_toTopOf="@id/main_iv_cover"
        android:visibility="invisible"
        app:flexible_round_corner_radius="4dp"
        app:flexible_round_corner="left_top"/>

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@color/main_color_111111_cfcfcf"
        android:textStyle="bold"
        android:fontFamily="sans-serif-light"
        tools:text="我是专辑名我是专辑名我是专…我是专辑名我是专辑名我是专…"
        app:layout_constraintStart_toEndOf="@+id/main_iv_album_cover_bg"
        android:layout_marginStart="12dp"
        app:layout_constraintEnd_toStartOf="@+id/main_tv_comment"
        android:layout_marginEnd="8dp"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintTop_toTopOf="@+id/main_iv_cover"
        app:layout_constraintBottom_toTopOf="@+id/main_tv_progress"
        android:id="@+id/main_tv_title"/>

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="10sp"
        android:textColor="@color/main_color_bd8977"
        android:id="@+id/main_tv_progress"
        tools:text="已听99%"
        app:layout_constraintStart_toEndOf="@+id/main_iv_album_cover_bg"
        android:layout_marginStart="12dp"
        app:layout_constraintEnd_toStartOf="@+id/main_tv_comment"
        android:layout_marginEnd="8dp"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintTop_toBottomOf="@+id/main_tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/main_iv_cover"/>

    <TextView
        android:layout_width="64dp"
        android:layout_height="28dp"
        android:background="@drawable/main_bg_f86442_14corner"
        android:text="@string/main_go_to_comment"
        android:textColor="@color/host_color_ffffff"
        android:textSize="12sp"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="14dp"
        android:id="@+id/main_tv_comment"/>

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="@color/main_color_eeeeee_2a2a2a"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/main_tv_title"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>