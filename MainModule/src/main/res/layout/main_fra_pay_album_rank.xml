<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/main_title_bar"
        android:layout_width="0dp"
        android:layout_height="@dimen/main_title_bar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.astuetz.PagerSlidingTabStrip
        android:id="@+id/main_tabs"
        style="@style/host_my_pager_sliding_tab_strip_style"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:background="@color/main_transparent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_title_bar"
        app:pstsActivateTextColor="@color/main_color_333333"
        app:pstsDeactivateTextColor="@color/main_color_666666"
        app:pstsIndicatorHeight="5dp"
        app:pstsIndicatorWidth="14dp"
        app:pstsShouldExpand="false"
        app:pstsSmoothScroll="true"
        app:pstsTabPaddingLeftRight="10dp" />

    <View
        android:id="@+id/main_divider"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="@color/main_list_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_tabs" />


    <androidx.viewpager.widget.ViewPager
        android:id="@+id/main_view_pager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:focusable="false"
        android:focusableInTouchMode="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_divider" />
</androidx.constraintlayout.widget.ConstraintLayout>