<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginBottom="12dp"
        android:paddingTop="12dp"
        android:paddingBottom="10dp">

        <TextView
            android:id="@+id/main_album_video_header_title"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true"
            android:gravity="center_vertical"
            android:paddingLeft="@dimen/host_x15"
            android:text="共 5 个视频节目"
            android:textColor="@color/main_color_666666_8d8d91"
            android:textSize="14sp" />

        <ImageView
            android:id="@+id/main_album_video_header_sort"
            android:layout_width="45dp"
            android:layout_height="match_parent"
            android:paddingRight="@dimen/host_x15"
            android:scaleType="centerInside"
            android:layout_alignParentRight="true"
            android:src="@drawable/host_album_sort_asc"
            android:contentDescription="@string/main_sort" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_default_divider_height"
        android:background="@color/main_color_eeeeee_000000" />

</LinearLayout>