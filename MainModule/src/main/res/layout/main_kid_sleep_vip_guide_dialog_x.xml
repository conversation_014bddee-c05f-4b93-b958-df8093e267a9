<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/bgView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="44dp"
        android:background="@drawable/main_dialog_bg_corner_ffffff_282828"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@+id/main_bg_iv" />

    <ImageView
        android:id="@+id/main_bg_iv"
        android:layout_width="255dp"
        android:layout_height="120dp"
        app:layout_constraintDimensionRatio="375:124"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/kid_iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:src="@drawable/host_dialog_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bgView"/>

    <TextView
        android:id="@+id/main_dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:text="@string/main_kid_dialog_vip_guide_title"
        android:textColor="@color/host_color_333333_dcdcdc"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_bg_iv" />

    <TextView
        android:id="@+id/main_dialog_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:gravity="center"
        android:text="@string/main_kid_dialog_vip_guide_subtitle"
        android:textColor="@color/host_color_333333_dcdcdc"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_dialog_title" />

    <TextView
        android:id="@+id/mainLabelTv1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:drawableStart="@drawable/main_kid_sleep_dialog_no_ad"
        android:drawablePadding="8dp"
        android:text="免广告绿色无干扰"
        android:textColor="@color/host_color_666666"
        app:layout_constraintEnd_toStartOf="@+id/mainLabelTv2"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_dialog_subtitle" />

    <TextView
        android:id="@+id/mainLabelTv2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:drawableStart="@drawable/main_kid_sleep_dialog_put_to_bed"
        android:drawablePadding="8dp"
        android:text="IP陪伴宝贝轻松入睡"
        android:textColor="@color/host_color_666666"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/mainLabelTv1"
        app:layout_constraintTop_toBottomOf="@+id/main_dialog_subtitle"
        app:layout_constraintTop_toTopOf="@+id/mainLabelTv1" />

    <TextView
        android:id="@+id/main_btn_use"
        android:layout_width="260dp"
        android:layout_height="40dp"
        android:layout_marginTop="26dp"
        android:layout_marginBottom="26dp"
        android:background="@drawable/host_bg_ff4444_radius_50"
        android:gravity="center"
        android:text="@string/main_kid_dialog_btn_buy"
        android:textColor="@color/host_color_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mainLabelTv1" />


</androidx.constraintlayout.widget.ConstraintLayout>