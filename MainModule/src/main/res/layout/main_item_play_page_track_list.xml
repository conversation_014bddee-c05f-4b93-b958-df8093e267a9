<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_play_list_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/host_y16"
    android:paddingBottom="@dimen/host_y16"
    android:descendantFocusability="blocksDescendants"
    android:gravity="clip_vertical"
    android:orientation="horizontal"
    android:background="@drawable/main_bg_press_f6f7f8"
    android:paddingLeft="@dimen/host_default_side_margin">

    <ImageView
        android:id="@+id/main_playing_flag"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="4dp"
        android:contentDescription="@string/main_iv_cd_icon"
        android:scaleType="centerCrop"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/main_playlist_vip_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/main_rect_corner2_stroke_cb8f4c"
        android:gravity="center"
        android:paddingLeft="3dp"
        android:paddingRight="3dp"
        android:text="VIP"
        android:textColor="#CB8F4C"
        android:textSize="12sp"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/main_sound_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingLeft="5dp"
        android:textColor="@color/host_color_textColor"
        android:textSize="15sp"
        tools:text="标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题" />

    <TextView
        android:id="@+id/main_play_list_play_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="4dp"
        android:textColor="@color/host_color_yello107"
        android:textSize="10sp"
        android:visibility="gone"
        tools:text="已播10%"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/main_play_list_info"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="8dp"
        android:gravity="center_vertical"
        android:textColor="@color/main_interested_tag_text_color_normal"
        android:textSize="12sp"
        android:visibility="invisible"
        tools:text="100喜点"
        tools:visibility="visible" />

    <ImageButton
        android:id="@+id/main_download"
        android:layout_width="36dp"
        android:layout_height="match_parent"
        android:background="@android:color/transparent"
        android:contentDescription="@string/main_iv_cd_download"
        android:focusable="false"
        android:layout_marginEnd="8dp"
        android:scaleType="center"
        android:tint="@color/host_color_textColor"
        android:src="@drawable/host_ic_download_n_n_line_regular_20"
        android:visibility="gone"
        tools:visibility="visible"/>
</LinearLayout>