<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/main_play_quality_using_full_depth_guide_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="#b3000000" />

    <com.ximalaya.ting.android.host.view.XmLottieAnimationView
        android:id="@+id/main_play_quality_using_full_depth_guide"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <LinearLayout
        android:id="@+id/main_ll_sound_effect_and_quality_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="invisible"
        tools:visibility="visible"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:drawableLeft="@drawable/main_ic_standard_vip_logo"
            android:drawablePadding="4.5dp"
            android:text="会员尊享"
            android:textColor="#FFC7AF"
            android:textFontWeight="500"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/host_default_side_margin"
            android:layout_marginTop="12dp"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <com.ximalaya.ting.android.host.view.GradientTextView
                android:id="@+id/main_tv_sound_quality_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:textFontWeight="700"
                android:textSize="29dp"
                tools:text="无损音质"
                tools:textColor="#FFC7AF" />

            <com.ximalaya.ting.android.host.view.GradientTextView
                android:id="@+id/main_tv_sound_effect_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:textFontWeight="700"
                android:textSize="29dp"
                tools:text="清澈人声音效"
                tools:textColor="#FFC7AF" />
        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:text="搭配耳机 效果更佳"
            android:textColor="@color/host_white"
            android:textSize="12sp" />
    </LinearLayout>
</RelativeLayout>