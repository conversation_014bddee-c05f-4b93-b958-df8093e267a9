<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="228dp"
    android:layout_height="120dp"
    app:cardBackgroundColor="#F0F2F9"
    app:cardCornerRadius="4dp"
    app:cardElevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/main_card_bg_2"
            android:layout_width="76dp"
            android:layout_height="62dp"
            android:layout_marginStart="16dp"
            app:cardBackgroundColor="@color/host_color_05000000_05FFFFFF"
            app:cardCornerRadius="4dp"
            app:cardElevation="0dp"
            app:cardMaxElevation="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/main_iv_cover"
            app:layout_constraintStart_toStartOf="@id/main_iv_cover"
            app:layout_constraintTop_toTopOf="@+id/main_iv_cover" />

        <androidx.cardview.widget.CardView
            android:id="@+id/main_card_bg_1"
            android:layout_width="76dp"
            android:layout_height="70dp"
            android:layout_marginStart="8dp"
            app:cardBackgroundColor="@color/host_color_0f000000_0fffffff"
            app:cardCornerRadius="4dp"
            app:cardElevation="0dp"
            app:cardMaxElevation="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/main_iv_cover"
            app:layout_constraintStart_toStartOf="@id/main_iv_cover"
            app:layout_constraintTop_toTopOf="@+id/main_iv_cover" />

        <ImageView
            android:id="@+id/main_iv_cover"
            android:layout_width="76dp"
            android:layout_height="80dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/host_default_album" />

        <TextView
            android:id="@+id/main_tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:lineSpacingMultiplier="1.1"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:textColor="#263A4D"
            android:textSize="14dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/main_tv_count"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main_iv_cover"
            app:layout_constraintTop_toTopOf="@id/main_iv_cover"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="十月热门新书持续推荐" />

        <TextView
            android:id="@+id/main_tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:includeFontPadding="false"
            android:gravity="center"
            android:drawableEnd="@drawable/host_ic_jump_n_n_line_regular_12"
            android:drawableTint="#263A4D"
            android:textColor="#263A4D"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@+id/main_iv_cover"
            app:layout_constraintStart_toStartOf="@+id/main_tv_title"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_title"
            tools:text="12部节目" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
