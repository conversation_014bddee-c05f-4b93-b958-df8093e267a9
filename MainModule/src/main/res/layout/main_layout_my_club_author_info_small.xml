<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="30dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_v_root"
    android:orientation="vertical"
    android:clipChildren="false">

    <ImageView
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_marginTop="-3dp"
        android:layout_marginStart="-3dp"
        android:layout_marginEnd="-3dp"
        android:background="@drawable/main_ic_myclub_circle_new"
        android:id="@+id/main_iv_wave"
        android:visibility="gone"/>

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_iv_avatar"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:scaleType="centerCrop"
        app:corner_radius="130dp"/>

    <TextView
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:background="@drawable/main_bg_a7a5d2_42424c_radius_20"
        android:textSize="11sp"
        android:gravity="center"
        android:textColor="@color/main_color_ffffff"
        android:id="@+id/main_tv_avatar_name"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/main_iv_voice"
        android:layout_width="11dp"
        android:layout_height="11dp"
        android:layout_alignRight="@+id/main_iv_avatar"
        android:layout_alignBottom="@+id/main_iv_avatar"
        android:src="@drawable/main_ic_myclub_voice_new"
        android:layout_marginRight="-1dp"
        android:layout_marginBottom="-1dp"
        android:scaleType="center"
        android:background="@drawable/main_bg_rect_ffffff_3f3f3f_corner_11"/>

    <TextView
        android:id="@+id/main_tv_name"
        android:layout_width="36dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:textColor="@color/main_color_333333_8d8d91"
        android:textSize="10sp"
        android:layout_marginTop="7dp"
        android:layout_marginStart="-2dp"
        android:layout_marginEnd="-2dp"
        android:layout_below="@+id/main_iv_avatar"/>

</RelativeLayout>