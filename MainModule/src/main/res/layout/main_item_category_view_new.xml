<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_category_tv"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/main_corner30_bg_f3f4f5"
    android:gravity="center"
    android:paddingLeft="8dp"
    android:paddingTop="4dp"
    android:paddingRight="8dp"
    android:paddingBottom="4dp"
    android:textColor="@color/main_text_color_orange_ea6347_selector_new"
    android:textSize="13sp"
    tools:text="有声书  100" />
