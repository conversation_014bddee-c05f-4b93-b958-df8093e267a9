<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/main_border_12ffffff_6dp_corner"
    app:layout_constraintWidth_max="327dp"
    tools:background="@color/host_gray">

    <com.ximalaya.ting.android.host.view.XmLottieAnimationView
        android:id="@+id/main_cover_iv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        android:layout_marginTop="25dp"
        android:layout_marginBottom="25dp"
        android:layout_marginStart="10dp"
        android:layout_width="110dp"
        android:layout_height="110dp" />

    <LinearLayout
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/main_cover_iv"
        app:layout_constraintTop_toTopOf="@+id/main_cover_iv"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintBottom_toBottomOf="@+id/main_cover_iv"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/main_card_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="红楼梦红楼梦红楼梦红楼梦红楼梦红楼梦红楼梦红楼梦红楼梦红楼梦红楼梦红楼梦红楼梦红楼梦红楼梦：" />

        <TextView
            android:id="@+id/main_card_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:layout_marginTop="7dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:textSize="13sp"
            android:lines="2"
            android:textColor="@color/main_color_white_80"
            tools:text="这身临其境的体感，不经让他眼眶忍不住盈满了泪水。没想到在有生之年，居然真的能见到完全沉浸式的虚拟现实技术，这些都已经不能用屌爆形容了。" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/main_card_share"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="11dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:gravity="center_vertical"
            android:text="立即分享"
            android:textStyle="bold"
            android:paddingStart="13dp"
            android:paddingEnd="14dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:drawablePadding="3dp"
            android:includeFontPadding="false"
            android:background="@drawable/main_bg_rect_19ffffff_radius_23"
            app:drawableStartCompat="@drawable/main_emotional_value_wechat_icon_mot" />

    </LinearLayout>

    <ImageView
        android:id="@+id/main_card_close"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:gravity="center"
        android:scaleType="centerInside"
        app:tint="@color/main_color_white_60"
        android:src="@drawable/main_ic_x_close_n_line_regular_24"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>





