<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ximalaya.ting.android.host.view.StickyNavLayout
        android:id="@+id/main_stickynav"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/main_transparent">

        <RelativeLayout
            android:id="@+id/host_id_stickynavlayout_topview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <View
            android:id="@+id/host_id_stickynavlayout_indicator"
            android:layout_width="match_parent"
            android:layout_height="0dp" />

        <com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView xmlns:ptr="http://schemas.android.com/apk/res-auto"
            android:id="@+id/host_id_stickynavlayout_content"
            android:background="@drawable/host_bg_rect_ffffff_radius_10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:divider="@null"
            android:dividerHeight="0dp"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:listSelector="@color/main_transparent"
            android:overScrollMode="always"
            android:paddingBottom="@dimen/host_bottom_bar_height"
            android:paddingTop="@dimen/host_title_bar_height"
            android:scrollbars="none"
            ptr:ptrDrawable="@drawable/host_ic_loading_circle"
            ptr:ptrHeaderTextColor="@color/main_text_medium"
            ptr:ptrShowIndicator="false" />
    </com.ximalaya.ting.android.host.view.StickyNavLayout>

    <FrameLayout
        android:id="@+id/main_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@drawable/main_gray_underline_white_bg" />
</FrameLayout>