<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:id="@+id/main_ll_category_like_btn"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="28dp"
    android:orientation="horizontal"
    android:background="@drawable/main_bg_new_user_recommend_page_btn"
    android:paddingLeft="15dp"
    android:paddingRight="12dp"
    android:gravity="center"
    tools:showIn="@layout/main_view_new_user_recommend_page_header">

    <TextView
        android:id="@+id/main_tv_category_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="13sp"
        android:textColor="@color/main_color_333333"
        android:includeFontPadding="false"
        tools:text="#影视"/>

    <ImageView
        android:id="@+id/main_iv_like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/main_ic_new_user_recommend_like"
        android:layout_marginLeft="3dp"/>

</LinearLayout>