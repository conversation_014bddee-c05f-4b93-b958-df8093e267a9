<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:apps="http://schemas.android.com/apk/res-auto"
    xmlns:skin="http://schemas.android.com/android/skin"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/main_drop_down_ad"
        android:layout_width="match_parent"
        android:layout_height="220dp"
        android:scaleType="fitXY"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/main_home_ad_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="8dp"
        android:layout_marginRight="8dp"
        android:visibility="gone" />

    <com.ximalaya.ting.android.main.view.BottomOvalView
        android:id="@id/framework_tab_top_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/main_bottom_oval_view_height"
        android:visibility="gone"
        apps:bottomShape="rect" />

    <ImageView
        android:id="@+id/main_tab_top_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="fitCenter"
        android:src="@drawable/main_home_img_topbg"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/main_iv_atmosphere_bg"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:scaleType="fitXY"
        android:visibility="gone" />
    <ImageView
        android:id="@+id/main_iv_atmosphere_icon"
        android:layout_width="wrap_content"
        android:layout_height="200dp"
        android:scaleType="fitStart"
        android:layout_alignParentRight="true"
        android:visibility="gone"
        />

    <!--联合霸屏包框背景-->
    <com.ximalaya.ting.android.host.view.ad.ShowReversePairImageView
        android:id="@+id/main_unit_package_box"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        android:visibility="gone" />

    <!--巨幕包框高光蒙层-->
    <View
        android:id="@+id/main_unit_package_box_blur"
        android:layout_width="match_parent"
        android:layout_height="360dp"
        android:background="@drawable/host_bg_unit_box"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/main_iv_top_bg_for_scene"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        android:src="@drawable/host_icon_scene_top_bg"
        android:visibility="gone"
        android:layout_alignBottom="@+id/main_tabs"
        />

    <com.ximalaya.ting.android.host.view.ListenScrollStatePagerSlidingTabStrip
        android:id="@+id/main_tabs"
        style="@style/host_my_pager_sliding_tab_strip_style"
        android:layout_width="match_parent"
        android:layout_height="@dimen/main_vg_tab_bar_height"
        android:layout_below="@+id/main_vg_search_bar"
        android:layout_toLeftOf="@+id/main_iv_edit_tab"
        android:background="@color/main_transparent"
        android:textSize="16sp"
        apps:pstsActivateTextColor="@color/main_color_333333"
        apps:pstsDeactivateTextColor="@color/main_color_999999"
        apps:pstsIndicatorColor="@color/host_theme_pager_tab_indicator"
        apps:pstsRedDotStrokeColor="@color/host_color_xmRed"
        apps:pstsRedDotStrokeSize="0dp"
        apps:pstsIndicatorCornerRadius="1dp"
        apps:pstsIndicatorHeight="4dp"
        apps:pstsIndicatorWidth="20dp"
        apps:pstsRedDotAutoDismiss="true"
        apps:pstsRedDotEnable="true"
        apps:pstsRedDotLeftMargin="-2dp"
        apps:pstsRedDotTopMargin="4dp"
        apps:pstsRedDotSize="6dp"
        apps:pstsShouldExpand="false"
        apps:pstsSmoothScroll="true"
        apps:pstsTabPaddingLeftRight="12dp"
        apps:pstsUnderlineColor="@color/main_transparent"
        apps:pstsUnderlineHeight="0dp"
        skin:enable="true" />

    <View
        android:id="@+id/main_v_category_tab_expand_shadow"
        android:layout_width="4dp"
        android:layout_height="40dp"
        android:layout_alignTop="@id/main_tabs"
        android:layout_alignRight="@id/main_tabs"
        android:layout_alignBottom="@id/main_tabs"
        android:background="@drawable/main_home_page_category_tab_expand_shadow" />

    <ImageView
        android:id="@+id/main_iv_edit_tab"
        android:layout_width="46dp"
        android:layout_height="32dp"
        android:layout_alignTop="@id/main_tabs"
        android:layout_alignParentRight="true"
        android:layout_gravity="center"
        android:contentDescription="@string/main_all_category"
        android:paddingRight="20dp"
        android:paddingLeft="6dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:src="@drawable/host_ic_standard_tabs_edit" />

    <ViewStub
        android:id="@+id/main_view_stub_underage_mode_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:inflatedId="@+id/main_v_underage_mode_top"
        android:layout="@layout/main_view_underage_mode_top_in_homepage"/>

    <!--首页，tab顶部搜索框的高度涉及abtest，修改需要注意abtest的部分-->
    <RelativeLayout
        android:id="@+id/main_vg_search_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/main_vg_search_bar_height"
        android:visibility="visible"
        tools:background="@color/host_transparent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_slide_entry"
            android:layout_marginLeft="@dimen/main_home_page_search_bar_margin_left"
            android:contentDescription="侧边栏"
            android:visibility="gone"
            android:paddingEnd="3dp"
            tools:visibility="visible"
            android:layout_alignTop="@+id/main_tv_search"
            android:layout_alignBottom="@+id/main_tv_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/main_slide_entry_iv"
                apps:layout_constraintStart_toStartOf="parent"
                apps:layout_constraintTop_toTopOf="parent"
                apps:layout_constraintBottom_toBottomOf="parent"
                android:src="@drawable/host_slide_open_icon"
                android:layout_width="24dp"
                android:layout_height="24dp" />

            <View
                android:id="@+id/main_slide_entry_guide"
                apps:layout_constraintEnd_toEndOf="@+id/main_slide_entry_iv"
                apps:layout_constraintBottom_toBottomOf="@+id/main_slide_entry_iv"
                android:layout_marginBottom="13dp"
                android:layout_width="1px"
                android:layout_height="1px" />

            <TextView
                android:id="@+id/main_slide_entry_red_dot"
                apps:layout_constraintEnd_toEndOf="@+id/main_slide_entry_guide"
                apps:layout_constraintBottom_toBottomOf="@+id/main_slide_entry_guide"
                apps:layout_constraintStart_toStartOf="@+id/main_slide_entry_guide"
                android:visibility="invisible"
                tools:visibility="visible"
                android:text="99+"
                android:textColor="@color/host_color_ffffff"
                android:textSize="9sp"
                android:paddingBottom="1dp"
                android:paddingStart="3dp"
                android:background="@drawable/host_ic_red_dot_ff4444_no_size"
                android:paddingEnd="3dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_tv_search"
            android:layout_width="208dp"
            android:layout_height="34dp"
            android:layout_toLeftOf="@+id/main_vg_search_bar_recommend_tab_action"
            android:layout_toEndOf="@+id/main_slide_entry"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="@dimen/main_home_page_search_bar_margin_left"
            android:layout_marginRight="@dimen/main_home_page_search_bar_margin_right"
            android:contentDescription="@string/main_search"
            android:background="@drawable/main_bg_home_page_search_new"
            android:minWidth="280dp">

            <ImageView
                android:id="@+id/main_iv_home_search_bar_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginLeft="8dp"
                android:scaleType="centerCrop"
                android:src="@drawable/host_ic_standard_et_search"
                android:visibility="visible"
                android:tint="@color/host_color_ff4444"
                apps:layout_constraintBottom_toBottomOf="parent"
                apps:layout_constraintLeft_toLeftOf="parent"
                apps:layout_constraintTop_toTopOf="parent" />

            <com.ximalaya.ting.android.main.view.search.SearchSwitcher
                android:id="@+id/main_home_search_text_switcher"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="2dp"
                apps:layout_constraintLeft_toRightOf="@+id/main_iv_home_search_bar_icon"
                apps:layout_constraintRight_toLeftOf="@+id/main_barrier_search_right" />

            <ImageView
                android:id="@+id/main_iv_home_search_bar_recognize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingRight="12dp"
                android:paddingVertical="7dp"
                android:scaleType="fitXY"
                android:contentDescription="@string/main_content_description_voice_search"
                android:src="@drawable/host_ic_standard_micro_input"
                android:tint="@color/host_color_666666_8d8d91"
                apps:layout_constraintBottom_toBottomOf="parent"
                apps:layout_constraintRight_toRightOf="parent"
                apps:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/host_iv_voice_search_red_dot"
                android:layout_width="7dp"
                android:layout_height="7dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="14dp"
                android:layout_marginTop="5dp"
                android:src="@drawable/host_ic_red_dot_with_stroke"
                apps:layout_constraintTop_toTopOf="@id/main_iv_home_search_bar_recognize"
                apps:layout_constraintStart_toStartOf="@id/main_iv_home_search_bar_recognize"
                android:visibility="invisible"
                tools:visibility="visible"
                />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/main_barrier_search_right"
                android:layout_width="0dp"
                android:layout_height="0dp"
                apps:barrierDirection="start"
                apps:constraint_referenced_ids="main_tv_chatxmly_bubble,main_iv_home_search_bar_recognize"
                />

            <TextView
                android:id="@+id/main_tv_chatxmly_bubble"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="2dp"
                android:background="@drawable/main_bg_rect_e1f0fa_131313_corner_100"
                android:ellipsize="end"
                android:paddingStart="10dp"
                android:paddingEnd="34dp"
                android:layout_marginTop="3dp"
                android:layout_marginBottom="3dp"
                android:gravity="center_vertical"
                android:scaleX="0"
                android:scaleY="1"
                android:singleLine="true"
                android:textColor="@color/main_color_3A4F72_a0d8ff"
                android:visibility="gone"
                apps:layout_constrainedWidth="true"
                apps:layout_constraintBottom_toBottomOf="parent"
                apps:layout_constraintEnd_toEndOf="@+id/main_iv_home_search_bar_recognize"
                apps:layout_constraintStart_toEndOf="@id/main_iv_home_search_bar_icon"
                apps:layout_constraintTop_toTopOf="parent"
                apps:layout_constraintHorizontal_bias="1"
                android:layout_marginEnd="3dp"
                tools:scaleX="1"
                tools:text="大护法大大护法大大护法大大护法大大护法大大护法大"
                tools:visibility="visible" />

            <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                android:id="@+id/main_lottie_chatxmly_bubble_robot"
                android:layout_width="20dp"
                android:layout_height="20dp"
                apps:layout_constraintTop_toTopOf="parent"
                apps:layout_constraintBottom_toBottomOf="parent"
                apps:layout_constraintEnd_toEndOf="@id/main_tv_chatxmly_bubble"
                android:layout_marginEnd="9dp"
                apps:lottie_repeatCount="0"
                android:visibility="gone"
                tools:visibility="visible"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- 推荐页的搜索栏右侧按钮 -->
        <LinearLayout
            android:id="@+id/main_vg_search_bar_recommend_tab_action"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:layout_alignParentRight="true"
            android:layout_marginRight="16dp"
            android:layout_alignParentBottom="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="2dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:visibility="invisible"
            tools:visibility="visible">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main_csl_search_bar_free_listen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="9dp"
                android:visibility="gone"
                tools:visibility="visible"
                >
                <ImageView
                    android:layout_marginRight="9dp"
                    android:src="@drawable/main_ic_free_listen_24"
                    android:id="@+id/main_iv_search_bar_free_listen"
                    android:tint="@color/host_color_1cc5a9"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:scaleType="fitXY"
                    android:contentDescription="看视频免费听"
                    apps:layout_constraintTop_toTopOf="parent"
                    apps:layout_constraintLeft_toLeftOf="parent"
                    apps:layout_constraintBottom_toBottomOf="parent"/>
                <com.ximalaya.ting.android.host.view.bar.FreeRoundProgressBar
                    android:id="@+id/main_round_progressbar"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    apps:layout_constraintLeft_toLeftOf="@+id/main_iv_search_bar_free_listen"
                    apps:layout_constraintRight_toRightOf="@+id/main_iv_search_bar_free_listen"
                    apps:layout_constraintTop_toTopOf="@+id/main_iv_search_bar_free_listen"
                    apps:layout_constraintBottom_toBottomOf="@+id/main_iv_search_bar_free_listen"
                    apps:style="STROKE"
                    apps:style_special="LISTEN_TASK"
                    apps:textIsDisplayable="false"
                    apps:roundWidth="1.2dp"
                    apps:roundColor="@color/host_color_4d333333_4dcfcfcf"
                    apps:roundProgressColor="@color/host_color_1cc5a9"
                    apps:cap="ROUND"
                    android:visibility="visible"/>

                <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                    android:id="@+id/main_coin_animation"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible"
                    apps:lottie_fileName="lottie/host_home_red_packet_lottie.json"
                    apps:layout_constraintLeft_toLeftOf="@+id/main_iv_search_bar_free_listen"
                    apps:layout_constraintRight_toRightOf="@+id/main_iv_search_bar_free_listen"
                    apps:layout_constraintTop_toTopOf="@+id/main_iv_search_bar_free_listen"
                    apps:layout_constraintBottom_toBottomOf="@+id/main_iv_search_bar_free_listen" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <RelativeLayout
                android:id="@+id/main_rl_search_bar_sign"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible">

                <ImageView
                    android:id="@+id/main_iv_search_bar_sign"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginRight="9dp"
                    android:contentDescription="签到"
                    android:scaleType="fitXY"
                    android:src="@drawable/main_ic_sign_no_n_line_regular_24" />

                <ImageView
                    android:id="@+id/main_iv_sign_red_dot"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_alignStart="@id/main_iv_search_bar_sign"
                    android:layout_marginStart="18dp"
                    android:layout_marginBottom="18dp"
                    android:scaleType="fitXY"
                    android:src="@drawable/host_ic_red_dot_with_stroke"
                    android:visibility="invisible" />

                <ImageView
                    android:visibility="gone"
                    android:layout_marginRight="9dp"
                    android:src="@drawable/main_ic_home_page_search_bar_effect"
                    android:id="@+id/main_iv_search_bar_sign_effect"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:contentDescription="签到动效" />

            </RelativeLayout>

            <!-- 加一个FrameLayout是为了放红点进去 -->
            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/main_v_more_action"
                android:clipChildren="false"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/main_iv_search_bar_more_action"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginTop="3dp"
                    android:layout_marginBottom="3dp"
                    android:contentDescription="@string/main_more"
                    android:padding="2dp"
                    android:src="@drawable/host_ic_add_n_n_line_regular_24"
                    android:tint="@color/host_color_2c2c3c_ffffff" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="14dp"
                    android:textSize="8sp"
                    android:textColor="@color/host_color_ffffff"
                    android:background="@drawable/main_bg_rect_ff4646_corner_8"
                    android:paddingLeft="3dp"
                    android:paddingRight="3dp"
                    android:includeFontPadding="false"
                    android:gravity="center"
                    android:layout_alignParentLeft="true"
                    android:layout_alignTop="@+id/main_iv_search_bar_more_action"
                    android:layout_marginLeft="14dp"
                    android:layout_marginTop="-1dp"
                    android:id="@+id/main_tv_more_hot"
                    android:visibility="gone"/>
                <View
                    android:id="@+id/main_v_more_hot_dot"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:background="@drawable/main_bg_rect_ff4646_corner_8"
                    android:layout_alignTop="@+id/main_iv_search_bar_more_action"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="18dp"
                    android:layout_marginTop="1dp"
                    android:visibility="gone"
                    />
            </RelativeLayout>

        </LinearLayout>

        <!-- vip页的搜索栏右侧按钮 -->
        <LinearLayout
            android:id="@+id/main_vg_search_bar_vip_tab_action"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:paddingLeft="6dp"
            android:paddingRight="16dp"
            android:visibility="invisible"
            android:contentDescription="Vip交流圈"
            tools:visibility="gone">

            <ImageView
                android:id="@+id/main_vg_search_bar_vip_enter_quanzi"
                android:layout_width="105dp"
                android:layout_height="34dp"
                android:scaleType="fitXY"
                android:contentDescription="Vip交流圈"
                android:src="@drawable/main_ic_enter_quanzi_default" />
        </LinearLayout>

        <!-- 直播页的搜索栏右侧按钮 -->
        <LinearLayout
            android:id="@+id/main_vg_search_bar_live_tab_action"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="9dp"
            android:paddingRight="6dp"
            android:visibility="invisible"
            tools:visibility="gone">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/main_iv_search_bar_live_start"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/main_start_live"
                    android:src="@drawable/main_ic_home_page_search_bar_live_start" />

                <ImageView
                    android:id="@+id/main_iv_live_start_red_dot"
                    android:layout_width="6dp"
                    android:layout_height="6dp"
                    android:layout_alignRight="@id/main_iv_search_bar_live_start"
                    android:layout_marginTop="4dp"
                    android:layout_marginRight="4dp"
                    android:visibility="gone"
                    android:src="@drawable/host_ic_red_dot_normal" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="5dp"
                android:layout_marginBottom="5dp">

                <ImageView
                    android:id="@+id/main_iv_search_bar_live_mine"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/main_personal_center"
                    android:src="@drawable/main_ic_home_page_search_bar_live_mine" />

                <ImageView
                    android:id="@+id/main_iv_search_bar_mine_reminder"
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:layout_marginLeft="-8dp"
                    android:layout_toRightOf="@id/main_iv_search_bar_live_mine"
                    android:background="@drawable/main_bg_personal_center_reminder_sign"
                    android:contentDescription="@string/main_personal_center_reminder"
                    android:gravity="center"
                    android:src="@drawable/main_ic_live_mine_reddot_reminder"
                    android:visibility="gone" />

            </RelativeLayout>

        </LinearLayout>

        <!-- 精品页的搜索栏右侧按钮 -->
        <LinearLayout
            android:id="@+id/main_vg_search_bar_quality_tab_action"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="6dp"
            android:paddingRight="16dp"
            android:visibility="invisible"
            tools:visibility="visible">

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                tools:visibility="gone">

                <ImageView
                    android:id="@+id/main_iv_search_bar_quality_cart_action"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="5dp"
                    android:contentDescription="购物车"
                    android:src="@drawable/main_ic_shopping_cart_home_page" />

            </FrameLayout>

        </LinearLayout>

        <!-- 通用的搜索栏右侧按钮 -->
        <LinearLayout
            android:id="@+id/main_vg_search_bar_action"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/main_bg_home_page_search"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:visibility="invisible"
            tools:visibility="gone">

            <TextView
                android:id="@+id/main_tv_search_bar_action"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:textColor="@color/main_color_black"
                tools:text="全部" />

        </LinearLayout>

    </RelativeLayout>

    <com.ximalaya.ting.android.host.view.other.MyViewPagerCanDisableFillNeighbourTab
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/main_tabs"
        android:focusable="false"
        android:focusableInTouchMode="false" />

    <ViewStub
        android:id="@+id/main_vs_emergency_announcement"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/main_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout="@layout/main_view_emergency_announcement" />

</RelativeLayout>