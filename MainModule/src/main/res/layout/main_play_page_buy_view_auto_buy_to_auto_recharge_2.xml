<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:background="@color/main_color_000000_alpha_20">

    <ImageView
        android:id="@+id/main_auto_recharge_guide_close"
        android:layout_width="36dp"
        android:layout_height="44dp"
        android:src="@drawable/host_icon_close_black"
        android:paddingVertical="16dp"
        android:paddingLeft="10dp"
        android:paddingRight="14dp"
        android:layout_gravity="center_vertical"
        android:tint="@color/main_color_ffffff"
        android:layout_centerVertical="true"
        android:layout_alignParentRight="true"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="5dp"
        android:text="余额不足，自动购买未成功"
        android:textColor="@color/host_color_ffffff_70"
        android:textSize="13sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/main_tv_auto_recharge_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="开通自动充值购买，连续收听不中断"
        android:textColor="@color/host_color_ffffff_60"
        android:textSize="11sp"
        android:textStyle="bold"
        android:layout_marginLeft="12dp"
        android:layout_marginBottom="5dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true" />

    <TextView
        android:id="@+id/main_auto_recharge_btn"
        android:layout_width="74dp"
        android:layout_height="26dp"
        android:text="立即开通"
        android:textSize="12sp"
        android:textColor="@color/host_color_ffffff"
        android:textStyle="bold"
        android:gravity="center"
        android:background="@drawable/main_rect_1affffff_corner_14"
        android:layout_marginRight="7dp"
        android:layout_centerVertical="true"
        android:layout_toLeftOf="@id/main_auto_recharge_guide_close"/>

</RelativeLayout>