<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingLeft="16dp"
    android:paddingRight="16dp">
    <View
        style="@style/main_recommend_page_stream_item_small_divider_style"
        android:layout_height="1dp"
        android:layout_marginTop="14dp"/>
    <TextView
        android:id="@+id/main_tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:ellipsize="end"
        android:lineSpacingExtra="3dp"
        android:maxLines="2"
        android:textColor="@color/main_color_111111_cfcfcf"
        android:textSize="15sp"
        android:textStyle="bold"
        android:fontFamily="sans-serif-light"
        tools:text="预算范围内你的买车首选考虑因素是什么？"/>

    <TextView
        android:id="@+id/main_tv_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:ellipsize="end"
        android:textColor="@color/main_color_999999_cfcfcf"
        android:textSize="12sp"
        android:maxLines="2"
        tools:text="50万以内的选择面太大了，上到豪车BBA，下到秋名山神车，要啥有啥。既然考虑耐用性，那一个是要有好品质。" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="92dp"
        android:layout_marginTop="12dp">

        <RelativeLayout
            android:id="@+id/main_ll_track_pk_left"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/main_ic_tag_track_pk_left_normal">

            <TextView
                android:id="@+id/main_tv_track_pk_left_title"
                android:layout_width="126dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="12dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="#ffffffff"
                android:textSize="12sp"
                tools:text="新能源车是趋势，我要买新能源。" />

            <TextView
                android:id="@+id/tv_left_count"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="16dp"
                android:layout_marginBottom="12dp"
                android:background="@drawable/main_item_track_pk_vote"
                android:gravity="center"
                android:minWidth="56dp"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:text="投票"
                android:textColor="#ffffffff"
                android:textSize="12sp" />
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/main_ll_track_pk_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="@drawable/main_ic_tag_track_pk_right_normal"
            android:layout_alignParentRight="true">

            <TextView
                android:id="@+id/main_tv_track_pk_right_title"
                android:layout_width="126dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginLeft="66dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="#ffffffff"
                android:textSize="12sp"
                tools:text="然后汽车升级换代，技术成熟，首选燃油车。" />
            <TextView
                android:id="@+id/tv_right_count"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:text="投票"
                android:textColor="#ffffffff"
                android:textSize="12sp"
                android:minWidth="56dp"
                android:layout_marginTop="8dp"
                android:layout_marginLeft="66dp"
                android:gravity="center"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="12dp"
                android:background="@drawable/main_item_track_pk_vote"/>
        </RelativeLayout>

        <ImageView
            android:id="@+id/main_img_track_pk"
            android:layout_width="112dp"
            android:layout_height="92dp"
            android:layout_centerInParent="true"
            android:src="@drawable/main_ic_tag_track_pk" />
    </RelativeLayout>
    
    <View
        style="@style/main_recommend_page_stream_item_small_divider_style"
        android:layout_toRightOf="@id/main_rl_cover"
        android:layout_below="@id/main_ll_content"
        android:layout_marginTop="8dp"/>
</LinearLayout>