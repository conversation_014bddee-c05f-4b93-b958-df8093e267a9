<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:makeramen="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/host_transparent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/main_track_produce_pop_ll"
        android:layout_width="match_parent"
        android:layout_height="63dp"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp"
        android:gravity="center_vertical"
        android:background="@drawable/main_play_produce_dialog_bg">

        <ImageView
            android:id="@+id/main_play_track_produce_img_iv"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginLeft="6dp"
            android:layout_centerVertical="true"
            android:contentDescription="声音提及商品图片" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="18dp"
            android:layout_toLeftOf="@id/main_play_pop_store_more_tv"
            android:layout_toRightOf="@id/main_play_track_produce_img_iv">

            <TextView
                android:id="@+id/main_play_track_produce_desc_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lines="1"
                android:maxLines="1"
                android:paddingRight="12dp"
                android:textColor="@color/main_color_333333_cfcfcf"
                android:textSize="12sp"
                tools:text="换种活法，也许从一株小多肉就可以开始，探寻内心的幸福力量。" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="6dp">

                <RelativeLayout
                    android:id="@+id/main_play_track_produce_avatars_rl"
                    android:layout_width="26dp"
                    android:layout_height="13dp">

                    <com.ximalaya.ting.android.framework.view.image.RoundImageView
                        android:id="@+id/main_produce_look_riv1"
                        android:layout_width="13dp"
                        android:layout_height="13dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerHorizontal="true"
                        android:scaleType="centerCrop"
                        android:src="@drawable/host_default_avatar_88"
                        makeramen:corner_radius="6.5dp"
                        makeramen:border_width="0.5px"
                        makeramen:border_color="@color/main_color_ffffff_1e1e1e" />


                    <com.ximalaya.ting.android.framework.view.image.RoundImageView
                        android:id="@+id/main_produce_look_riv2"
                        android:layout_width="13dp"
                        android:layout_height="13dp"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="6.5dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/host_default_avatar_88"
                        makeramen:corner_radius="6.5dp" />


                    <com.ximalaya.ting.android.framework.view.image.RoundImageView
                        android:id="@+id/main_produce_look_riv3"
                        android:layout_width="13dp"
                        android:layout_height="13dp"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="13dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/host_default_avatar_88"
                        makeramen:corner_radius="6.5dp" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/main_produce_look_count_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="6dp"
                    android:textColor="@color/main_color_999999_888888"
                    android:textSize="9sp"
                    tools:text="11人看过" />

            </LinearLayout>

        </LinearLayout>


        <TextView
            android:id="@+id/main_play_pop_store_more_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:drawableRight="@drawable/main_play_produce_more_icon"
            android:drawablePadding="3dp"
            android:gravity="center"
            android:text="去看看"
            android:textColor="#FFF86442"
            android:textSize="11sp" />


    </RelativeLayout>

    <View
        android:id="@+id/main_product_arrow"
        android:layout_width="13dp"
        android:layout_height="6.5dp"
        android:layout_marginTop="-2dp"
        android:backgroundTint="@color/main_color_null_000000"
        android:background="@drawable/main_play_produce_arrow_icon" />
</LinearLayout>