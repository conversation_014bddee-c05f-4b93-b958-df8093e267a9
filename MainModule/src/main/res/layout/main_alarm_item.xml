<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="109dp"
    android:minHeight="109dp"
    android:descendantFocusability="blocksDescendants">

    <TextView
        android:id="@+id/item_tv_clock_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="99:99"
        android:textSize="42sp"
        android:textColor="@color/main_color_333333_cfcfcf"
        android:fontFamily="Regular"
        android:layout_marginTop="16dp"/>

    <LinearLayout
        android:layout_below="@id/item_tv_clock_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginLeft="7dp">

        <TextView
            android:id="@+id/item_tv_repeat_times"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/main_color_333333_cfcfcf"
            android:fontFamily="Regular"
            android:textSize="12sp"/>

        <TextView
            android:id="@+id/item_tv_divider"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="7dp"
            android:text="|"/>

        <TextView
            android:id="@+id/item_tv_clock_ring_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/main_color_333333_cfcfcf"
            android:fontFamily="Regular"
            android:layout_marginLeft="7dp"
            android:textSize="12sp"/>

    </LinearLayout>

    <CheckBox
        android:id="@+id/item_cb_switch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@null"
        android:focusable="false"
        android:background="@drawable/host_switch_selector"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="10dp">
    </CheckBox>

    <View
        android:id="@+id/main_alarm_divider"
        android:layout_alignParentBottom="true"
        android:background="@color/main_border_gray"
        android:layout_width="match_parent"
        android:layout_height="1px"/>

</RelativeLayout>