package com.ximalaya.ting.android.main.playpage.view;

import androidx.recyclerview.widget.RecyclerView;

/**
 * Create by {jian.kang} on 2024/5/14
 *
 * <AUTHOR>
 */
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Shader;
import android.util.AttributeSet;

import androidx.annotation.NonNull;

public class FadeEdgeRecyclerView extends RecyclerView {

    private Paint mTopPaint;
    private Paint mBottomPaint;
    private int fadeHeight; // 透视效果的高度,可根据具体需求调整

    public FadeEdgeRecyclerView(@NonNull Context context) {
        super(context);
        init();
    }

    public FadeEdgeRecyclerView(@NonNull Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public FadeEdgeRecyclerView(@NonNull Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    private void init() {
        // 设置顶部混合模式
        mTopPaint = new Paint();
        LinearGradient topGradient = new LinearGradient(0, 0, 0, fadeHeight,
                0x00000000, 0xFF000000, Shader.TileMode.CLAMP);
        mTopPaint.setShader(topGradient);
        mTopPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_IN));


        // 设置底部混合模式
        mBottomPaint = new Paint();
        LinearGradient bottomGradient = new LinearGradient(0, 0, 0, fadeHeight,
                0xFF000000, 0x00000000, Shader.TileMode.CLAMP);
        mBottomPaint.setShader(bottomGradient);
        mBottomPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_IN));

        setLayerType(LAYER_TYPE_SOFTWARE, null);
    }

    public void setFadeHeight(int fadeHeight) {
        this.fadeHeight = fadeHeight;
    }

    @Override
    public void onDrawForeground(Canvas canvas) {
        super.onDrawForeground(canvas);
        int sc = canvas.saveLayer(0, 0, getWidth(), getHeight(), null, Canvas.ALL_SAVE_FLAG);

        // 顶部渐变
        canvas.save();
        canvas.drawRect(0, 0, getWidth(), fadeHeight, mTopPaint);
        canvas.restore();

        // 底部渐变
        canvas.save();
        canvas.translate(0, getHeight() - fadeHeight);
        canvas.drawRect(0, 0, getWidth(), fadeHeight, mBottomPaint);
        canvas.restore();

        canvas.restoreToCount(sc);
    }
}
