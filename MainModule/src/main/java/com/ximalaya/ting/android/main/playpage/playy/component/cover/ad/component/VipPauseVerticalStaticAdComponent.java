package com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.component;

import android.content.Context;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.ad.model.thirdad.IAbstractAd;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.IXAdComponentProvider;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.view.VipPauseStaticAdView;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.view.VipPauseVerticalStaticAdView;

/**
 * Created by zhao.peng.
 * describe
 * Date: 2023/5/26
 */
public class VipPauseVerticalStaticAdComponent extends XBaseStaticAdComponent {
    public VipPauseVerticalStaticAdComponent(@NonNull IXAdComponentProvider adComponentProvider, int viewKey, String complexTag) {
        super(adComponentProvider, viewKey, complexTag);
    }

    @Override
    public VipPauseVerticalStaticAdView getView(Context context, IAbstractAd abstractAd) {
        VipPauseVerticalStaticAdView coverAdView = new VipPauseVerticalStaticAdView(context, mAdComponentProvider);

        RelativeLayout.LayoutParams params =
                new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT);
        params.addRule(RelativeLayout.CENTER_HORIZONTAL);
        coverAdView.setLayoutParams(params);

        return coverAdView;
    }

}
