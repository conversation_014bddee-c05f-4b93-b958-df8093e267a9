package com.ximalaya.ting.android.main.manager.wholeAlbum.recommend;

import com.ximalaya.ting.android.main.albumModule.album.wholeAlbum.WholeAlbumRecommendFragment;
import com.ximalaya.ting.android.main.manager.IRequesterCallBack;
import com.ximalaya.ting.android.main.manager.wholeAlbum.IWholeAlbumFragmentManager;
import com.ximalaya.ting.android.main.manager.wholeAlbum.IWholeAlbumFragmentPresenter;
import com.ximalaya.ting.android.main.manager.wholeAlbum.WholeAlbumNetRequestManager;
import com.ximalaya.ting.android.main.model.album.WholeAlbumRecommendList;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * Created by 5Greatest on 2021.01.18
 *
 * <AUTHOR>
 * On 2021/1/18
 */
public class WholeAlbumRecommendDataRequester implements IWholeAlbumFragmentManager<WholeAlbumRecommendFragment> {

    private WholeAlbumRecommendPresenter mPresenter;

    public WholeAlbumRecommendDataRequester(WholeAlbumRecommendPresenter presenter) {
        this.mPresenter = presenter;
    }

    public void requestRecommendAlbumList(IRequesterCallBack callBack) {
        WholeAlbumNetRequestManager.requestRecommendAlbumList(mPresenter.getAlbumId(), mPresenter.getCategoryId(), new IDataCallBack<WholeAlbumRecommendList>() {
            @Override
            public void onSuccess(@androidx.annotation.Nullable WholeAlbumRecommendList object) {
                if (null != object) {
                    if (null != mPresenter) {
                        mPresenter.setWholeData(object);
                        mPresenter.setSimilarUserAlbums(null == object.similarUserAlbums ? null : object.similarUserAlbums.albums);
                        mPresenter.setSimilarCategoryAlbums(null == object.similarCategoryAlbums ? null : object.similarCategoryAlbums.albums);
                    }
                }
                if (null != callBack) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onError(int code, String message) {
                if (null != callBack) {
                    callBack.onFail(code, message);
                }
            }
        });
    }

    @NotNull
    @Override
    public IWholeAlbumFragmentPresenter<WholeAlbumRecommendFragment> getPresenter() {
        return mPresenter;
    }

    @Override
    public void doOnDestroy() {

    }

    @Nullable
    @Override
    public WholeAlbumRecommendFragment getFragment() {
        return null;
    }
}
