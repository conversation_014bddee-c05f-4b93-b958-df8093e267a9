//package com.ximalaya.ting.android.main.model.comment;
//
///**
// * 推荐流声音评论信息
// *
// * <AUTHOR>
// */
//
//public class CommentResult {
//    private String content;
//    private long createAt;
//    private long id;
//    private int likes;
//    private boolean liked;
//    private long trackId;
//    private String trackTitle;
//    private long uid;
//    private long updateAt;
//    private String nickname;
//
//    public String getContent() {
//        return content;
//    }
//
//    public void setContent(String content) {
//        this.content = content;
//    }
//
//    public long getCreateAt() {
//        return createAt;
//    }
//
//    public void setCreateAt(long createAt) {
//        this.createAt = createAt;
//    }
//
//    public long getId() {
//        return id;
//    }
//
//    public void setId(long id) {
//        this.id = id;
//    }
//
//    public int getLikes() {
//        return likes;
//    }
//
//    public void setLikes(int likes) {
//        this.likes = likes;
//    }
//
//    public boolean isLiked() {
//        return liked;
//    }
//
//    public void setLiked(boolean liked) {
//        this.liked = liked;
//    }
//
//    public long getTrackId() {
//        return trackId;
//    }
//
//    public void setTrackId(long trackId) {
//        this.trackId = trackId;
//    }
//
//    public long getUid() {
//        return uid;
//    }
//
//    public void setUid(long uid) {
//        this.uid = uid;
//    }
//
//    public long getUpdateAt() {
//        return updateAt;
//    }
//
//    public void setUpdateAt(long updateAt) {
//        this.updateAt = updateAt;
//    }
//
//    public String getTrackTitle() {
//        return trackTitle;
//    }
//
//    public void setTrackTitle(String trackTitle) {
//        this.trackTitle = trackTitle;
//    }
//
//    public String getNickname() {
//        return nickname;
//    }
//
//    public void setNickname(String nickname) {
//        this.nickname = nickname;
//    }
//}
