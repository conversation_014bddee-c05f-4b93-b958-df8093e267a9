package com.ximalaya.ting.android.main.adapter.find.rank;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.ViewGroup;

import com.ximalaya.ting.android.host.adapter.MyFragmentStatePagerAdapter;
import com.ximalaya.ting.android.host.model.rank.RankCategoryKeys;
import com.ximalaya.ting.android.main.fragment.find.other.rank.RankDetailFragment;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * Created by xmly on 10/08/2018.
 *
 * <AUTHOR>
 */
public class RankTabAdapter extends MyFragmentStatePagerAdapter {

    private int rankingListId;
    private List<RankCategoryKeys> mData;
    private SparseArray<WeakReference<Fragment>> mFragmentRefs;
    private int mType;
    private int mPlaySource;
    private String target;
    private boolean showSubscribe = false;
    private String rankRule;

    public RankTabAdapter(FragmentManager fm, List<RankCategoryKeys> data, int type, int playSource, int rankingListId, String target, boolean showSubscribe, String rankRule) {
        super(fm);
        mData = data;
        mFragmentRefs = new SparseArray<>();
        mType = type;
        mPlaySource = playSource;
        this.rankingListId = rankingListId;
        this.target = target;
        this.showSubscribe = showSubscribe;
        this.rankRule = rankRule;
    }

    public List<RankCategoryKeys> getData() {
        return mData;
    }

    @Override
    public Fragment getItem(int position) {
        Fragment fragment;
        fragment = RankDetailFragment.newInstance(rankingListId, mType, mData.get(position).getName(), mData.get(position).getId(), target, mPlaySource, showSubscribe, rankRule);
        mFragmentRefs.put(position, new WeakReference<>(fragment));
        return fragment;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        super.destroyItem(container, position, object);
        mFragmentRefs.remove(position);
    }

    public Fragment getFragmentAt(int position) {
        WeakReference<Fragment> fragmentRef = mFragmentRefs.get(position);
        if (fragmentRef != null) {
            return fragmentRef.get();
        }
        return null;
    }

    @Override
    public int getCount() {
        return mData == null ? 0 : mData.size();
    }

    @Override
    public CharSequence getPageTitle(int position) {
        return mData.get(position).getName();
    }

    public int getTagPosition(String tagName) {
        if (mData != null) {
            int count = mData.size();
            for (int i = 0; i != count; i++) {
                if (TextUtils.equals(mData.get(i).getName(), tagName)) {
                    return i;
                }
            }
        }
        return -1;
    }
}
