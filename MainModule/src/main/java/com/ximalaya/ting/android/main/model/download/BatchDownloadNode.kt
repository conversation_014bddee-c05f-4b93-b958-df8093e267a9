package com.ximalaya.ting.android.main.model.download

import com.ximalaya.ting.android.host.adapter.base.entity.BaseExpandNode
import com.ximalaya.ting.android.host.adapter.base.entity.BaseNode
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.main.downloadModule.other.BatchDownloadHelper

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2024/4/3
 */
class BatchDownloadNode(
    override val childNode: MutableList<BaseNode>? = null
) : BaseExpandNode() {
    var type: Int = TYPE_ITEM
    var albumM: AlbumM? = null
    var trackM: TrackM? = null
    var chapterIndexStr: String = ""

    constructor(
        albumM: AlbumM?,
        chapterIndexStr: String,
        childNode: MutableList<BaseNode>? = null
    ) : this(childNode) {
        this.type = TYPE_NODE
        this.albumM = albumM
        this.chapterIndexStr = chapterIndexStr
    }

    constructor(
        albumM: AlbumM?,
        trackM: TrackM?
    ) : this() {
        this.type = TYPE_ITEM
        this.albumM = albumM
        this.trackM = trackM
    }

    init {
        isExpanded = false
    }

    fun canDownload(): Boolean {
        return BatchDownloadHelper.canDownLoad(trackM, albumM)
    }

    companion object {
        const val TYPE_ITEM = 111
        const val TYPE_NODE = 222
    }
}