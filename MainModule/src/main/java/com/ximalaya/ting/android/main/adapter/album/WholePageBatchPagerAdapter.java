package com.ximalaya.ting.android.main.adapter.album;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.util.view.AccessibilityClassNameUtil;
import com.ximalaya.ting.android.host.view.text.AdaptiveTextView;
import com.ximalaya.ting.android.main.R;

import java.util.List;

/**
 * Author: <PERSON>
 * <PERSON>ail: <EMAIL>
 * Date: 2021/6/7
 * Description：
 */
public class WholePageBatchPagerAdapter extends HolderAdapter<BatchPagerAdapter.PageIndex> {

    public WholePageBatchPagerAdapter(Context context, List<BatchPagerAdapter.PageIndex> listData) {
        super(context, listData);
    }

    @Override
    public void onClick(View view, BatchPagerAdapter.PageIndex t, int position,
                        BaseViewHolder holder) {

    }

    @Override
    public int getConvertViewId() {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        AdaptiveTextView tv;
        if (convertView == null) {
            tv = new AdaptiveTextView(context);
            tv.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
            tv.setHeight(BaseUtil.dp2px(context, 28));
            if (!ElderlyModeManager.getInstance().isElderlyMode()) {
                tv.setTextSize(14);
            } else {
                ElderlyModeManager.getInstance().setElderlyModeTextSize(tv, 18);
            }
//            tv.setTextColor(Color.parseColor("#f5f8fa"));
            tv.setGravity(Gravity.CENTER);
            tv.setSingleLine();
            tv.setBackgroundColor(context.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_131313));
//            tv.setBackgroundResource(R.drawable.main_album_pager_item_bg_rect_gray);
            convertView = tv;
        }
        tv = (AdaptiveTextView) convertView;
        BatchPagerAdapter.PageIndex pi = listData.get(position);
        tv.setText(pi.pageString);
        if (!ElderlyModeManager.getInstance().isElderlyMode()) {
            tv.setBackgroundResource(pi.isWholePageSelected() ? R.drawable.main_album_pager_item_bg_rect_orange_whole
                    : R.drawable.main_album_pager_item_bg_rect_gray_whole);
        } else {
            ElderlyModeManager.getInstance().setTextBackground(tv, pi.isWholePageSelected() ? R.drawable.main_album_pager_item_bg_rect_orange_whole_elderly
                    : R.drawable.main_album_pager_item_bg_rect_gray_whole);
        }
        tv.setTextColor(context.getResources().getColor(pi.isWholePageSelected() ? com.ximalaya.ting.android.host.R.color.host_color_ff4444_282828 : R.color.main_color_333333_dcdcdc));
        AccessibilityClassNameUtil.setAccessibilityIsChecked(tv, pi.isWholePageSelected() ? true : false);
        return convertView;
    }


    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new BaseViewHolder();
    }


    @Override
    public void bindViewDatas(BaseViewHolder holder, BatchPagerAdapter.PageIndex t, int position) {

    }

}
