package com.ximalaya.ting.android.main.fragment.dialog.h5;

import com.ximalaya.ting.android.host.util.common.StringUtil;

/**
 * Created by 5Greatest on 2020.08.11
 *
 * <AUTHOR>
 * On 2020-08-11
 */
public class H5DialogManager {
    private static final String CLOSE = "close";

    public static H5DialogManager getInstance() {
        return Holder.instance;
    }

    private H5DialogManager() {
    }

    public void handleOperation(String operation, String target) {
        if (StringUtil.isEmpty(operation)) {
            return;
        }
        switch (operation) {
            case CLOSE:
                if ("vipbuydialog".equals(target)) {
                    VipFloatPurchaseDialog.close();
                }
                break;
            default:
                break;
        }
    }

    private static class Holder {
        private static H5DialogManager instance = new H5DialogManager();
    }
}
