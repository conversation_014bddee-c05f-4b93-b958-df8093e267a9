package com.ximalaya.ting.android.main.adapter.find.util

import android.os.Bundle
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.play.util.PlayDataUtils
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentPageErrorManager
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.playpage.dialog.voteshare.ReactNativeAdapter
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.util.SceneDragonBallUtil
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.simplePlayer.mixplay.MixTrack
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil

object ScenePlayDataUtil {
    var isRequesting: Boolean = false
    private var lastRequestParams: Map<String, String>? = null

    fun refreshRequestParams(requestParams: Map<String, String>) {
        lastRequestParams = requestParams
    }

    fun checkIsNeedRefresh(callBack: IDataCallBack<RecommendCommonItem>): Boolean {
        if (isRequesting) {
            return false
        }
        var params = PlayDataUtils.getAndRefreshSceneRequestParams(false)
        if (lastRequestParams != null && isMapsSame(params, lastRequestParams!!)) {
            return false
        }
        isRequesting = true
        lastRequestParams = params
        MainCommonRequest.refreshSceneListenList(object : IDataCallBack<RecommendCommonItem> {
            override fun onSuccess(data: RecommendCommonItem?) {
                isRequesting = false
                if (data?.subElements?.size != null && data.subElements!!.size < MmkvCommonUtil.getInstance(ToolUtil.getCtx())
                        .getInt(CConstants.Group_toc.ITEM_HOME_SCENE_CARD_MIN_COUNT, 3)) {
                    RecommendFragmentPageErrorManager.uploadDataError("习惯听刷新数量不足", "刷新接口")
                }
                callBack.onSuccess(data)
                SceneDragonBallUtil.reset()
            }

            override fun onError(code: Int, message: String?) {
                isRequesting = false
                callBack.onError(code, message)
            }
        }, params)
        return true
    }

    fun saveDataForRn(recommendItemNew: RecommendItemNew) {
        var bundle = Bundle()
        bundle.putString("data", recommendItemNew.jsonObject?.toString())
        ReactNativeAdapter.saveSearchData(recommendItemNew.itemType, bundle)
    }

    fun isMapsSame(map1: Map<String, String>, map2: Map<String, String>): Boolean {
        // 首先检查两个 map 的大小是否相同
        if (map1.size != map2.size) {
            return false
        }

        // 遍历第一个 map
        for ((key, value) in map1) {
            // 检查第二个 map 中是否存在相同的键,并且值也相同
            if ((!map2.containsKey(key) || map2[key] != value) && !"playStatus".equals(key)) {
                return false
            }
        }
        // 如果所有键值对都相同,则返回 true
        return true
    }
}