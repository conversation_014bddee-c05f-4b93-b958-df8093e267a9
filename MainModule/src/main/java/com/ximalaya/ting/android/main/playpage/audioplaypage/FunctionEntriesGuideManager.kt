package com.ximalaya.ting.android.main.playpage.audioplaypage

/**
 * Created by <PERSON><PERSON><PERSON> on 2021/4/15.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
object FunctionEntriesGuideManager {

    // 优先级从上往下
    enum class Guide {
        DRIVE_MODE_ENTRY,
        SKIP_HEAD_TAIL,
        MORE_AD,
        MORE_LISTEN_TASK,
        SOUND_EFFECT_GUIDE
    }

    private val mNoNeedShowList = mutableListOf<Guide>()
    private val mReadyShowMap = mutableMapOf<Guide, Runnable>()
    private var mShowingTips: Guide? = null

    fun readyToShow(guide: Guide, runnable: Runnable) {
        mReadyShowMap[guide] = runnable
        checkToShow()
    }

    fun noNeedShow(guide: Guide) {
        mNoNeedShowList.add(guide)
        checkToShow()
    }

    fun notifyHide(guide: Guide) {
        if (guide == mShowingTips) {
            mShowingTips = null
        }
    }

    fun reset() {
        mShowingTips = null
        mNoNeedShowList.clear()
        mReadyShowMap.clear()
    }

    private fun checkToShow() {
        if (mShowingTips != null) {
            return
        }
        Guide.values().forEach { guide ->
            val runnable = mReadyShowMap[guide]
            if (runnable != null) {
                mShowingTips = guide
                runnable.run()
                // 有显示了，其他就先清除掉，本次不会再被显示了
                mReadyShowMap.clear()
                return
            } else if (!mNoNeedShowList.contains(guide)) {
                // 高优先级的还没确定不出，不用往下遍历了
                return
            }
        }
    }

    fun isTipsShow(guide: Guide): Boolean {
        return guide == mShowingTips
    }

    fun hasTipsShowing(): Boolean {
        return mShowingTips != null
    }
}