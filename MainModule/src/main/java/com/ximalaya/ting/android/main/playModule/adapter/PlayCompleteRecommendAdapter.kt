package com.ximalaya.ting.android.main.playModule.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.AnimationDrawable
import android.graphics.drawable.GradientDrawable
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.LeadingMarginSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listener.ICollectWithFollowStatusCallback
import com.ximalaya.ting.android.host.manager.PlayCompleteManager
import com.ximalaya.ting.android.host.manager.account.AnchorCollectManage
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.soundpatch.SoundPatchHostManager
import com.ximalaya.ting.android.host.manager.tolisten.AutoPlaySwitchManager
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumCollectParam
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.model.play.AutoPlayAlbumModel
import com.ximalaya.ting.android.host.model.play.PlayCompleteSubscribeGuideInfo
import com.ximalaya.ting.android.host.model.play.PlayEndDetailItemModel
import com.ximalaya.ting.android.host.model.play.PlayEndGuideModel
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.common.SpanUtils
import com.ximalaya.ting.android.host.util.common.TimeHelper
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.ui.AlbumTagUtilNew
import com.ximalaya.ting.android.host.util.ui.AnimationUtil
import com.ximalaya.ting.android.host.util.view.LocalImageUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.util.view.setTextIfChanged
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.vote.dialog.VoteDialogFragment
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import java.util.ArrayList
import java.util.Locale
import kotlin.math.min

class PlayCompleteRecommendAdapter(val context: Context?, val fragment: Fragment) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val isAutoPlayOpen: Boolean = AutoPlaySwitchManager.isAutoPlay() // 本次展示不再更新状态
    var mXmRequestId : String? = null

    companion object {
        const val TYPE_HEAD_DEFAULT_STR = "default_head"

        private const val TYPE_NONE = 0
        private const val TYPE_WHITE_LIST_ALBUM = 1 // 白名单专辑
        private const val TYPE_EVERYDAY_UPDATE_PROGRAM = 2 // 正在追更的节目
        private const val TYPE_RECOMMEND_ALBUM_LIST = 3 // 听了本节目的人也爱听
        private const val TYPE_RANK = 4 // 同类节目排行榜
        private const val TYPE_MONTHLY_TICKET_GUIDE = 5 // 月票引导
        private const val TYPE_AUTO_PLAY_RECOMMEND = 6
        private const val TYPE_HEAD_DEFAULT = 7
        private const val TYPE_SUBSCRIBE_GUIDE = 8
    }

    private val mMonthlyVoteGuideTip: String by lazy(LazyThreadSafetyMode.NONE) {
        ConfigureCenter.getInstance().getString(
            CConstants.Group_toc.GROUP_NAME,
            "monthlyticket_copywriter", null
        )?.split("&")?.let {
            if (it.isEmpty()) null else it.random()
        } ?: "你的月票，是我更新的动力"
    }
    private var mData = ArrayList<Any>()
    private var mIsPlayStatusChange = false

    fun setData(list: ArrayList<Any>) {
        this.mData = list
    }

    fun addData(data: Any) {
        mData.add(data)
    }

    fun setPlayStatusChange(isPlayStatusChange: Boolean) {
        mIsPlayStatusChange = isPlayStatusChange
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view: View
        val holder: RecyclerView.ViewHolder
        when (viewType) {
            TYPE_WHITE_LIST_ALBUM -> {
                view = LayoutInflater.from(parent.context).inflate(
                    R.layout.main_item_play_complete_white_list_album, parent, false
                )
                holder = WhiteListAlbumViewHolder(view)
            }

            TYPE_EVERYDAY_UPDATE_PROGRAM -> {
                view = LayoutInflater.from(parent.context).inflate(
                    R.layout.main_item_play_complete_everyday_update_program, parent, false
                )
                holder = EverydayUpdateProgramViewHolder(view)
            }

            TYPE_RECOMMEND_ALBUM_LIST -> {
                view = LayoutInflater.from(parent.context).inflate(
                    R.layout.main_item_play_complete_recommend_album_list, parent, false
                )
                holder = RecommendAlbumListViewHolder(view)
            }

            TYPE_RANK -> {
                view = LayoutInflater.from(parent.context).inflate(
                    R.layout.main_item_play_complete_recommend_rank, parent, false
                )
                holder = RecommendRankViewHolder(view)
            }

            TYPE_MONTHLY_TICKET_GUIDE -> {
                view = LayoutInflater.from(parent.context).inflate(
                    R.layout.main_item_play_complete_monthly_ticket_guide, parent, false
                )
                holder = MonthlyTicketGuideHolder(view)
            }

            TYPE_SUBSCRIBE_GUIDE -> {
                view = LayoutInflater.from(parent.context).inflate(
                    R.layout.main_item_play_complete_subscribe_guide, parent, false
                )
                holder = SubscribeGuideHolder(view)
            }

            TYPE_AUTO_PLAY_RECOMMEND -> {
                view = LayoutInflater.from(parent.context).inflate(
                    R.layout.main_item_play_complete_auto_play, parent, false
                )
                holder = AutoPlayRecommendViewHolder(view)
            }

            TYPE_HEAD_DEFAULT -> {
                view = LayoutInflater.from(parent.context).inflate(
                    R.layout.main_item_play_complete_head_default, parent, false
                )
                holder = DefaultViewHolder(view)
            }

            else -> {
                view = LayoutInflater.from(parent.context).inflate(0, parent, false)
                holder = DefaultViewHolder(view)
            }
        }
        view.tag = holder
        return holder
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = getItem(position) ?: return
        when (holder) {
            is AutoPlayRecommendViewHolder -> {
                if (item is AutoPlayAlbumModel) {
                    initAutoPlayAlbumView(holder, item)
                }
            }

            is WhiteListAlbumViewHolder -> {
                if (item is PlayEndGuideModel) {
                    initWhiteListAlbumView(holder, item)
                }
            }

            is EverydayUpdateProgramViewHolder -> {
                if (item is PlayEndDetailItemModel) {
                    initEverydayUpdateProgramView(holder, item)
                }
            }

            is RecommendAlbumListViewHolder -> {
                if (item is PlayEndDetailItemModel) {
                    initRecommendAlbumListView(holder, item)
                }
            }

            is RecommendRankViewHolder -> {
                if (item is PlayEndDetailItemModel) {
                    initRecommendRankView(holder, item)
                }
            }

            is MonthlyTicketGuideHolder -> {
                if (item is PlayingSoundInfo) {
                    initMonthlyTicketView(holder, item)
                }
            }

            is SubscribeGuideHolder -> {
                if (item is PlayCompleteSubscribeGuideInfo) {
                    initSubscribeGuideView(holder, item)
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initAutoPlayAlbumView(
        holder: AutoPlayRecommendViewHolder,
        model: AutoPlayAlbumModel
    ) {
        if (isAutoPlayOpen) {
            if (PlayTools.isCurrentAlbum(context, model.albumId)) {
                holder.ivPlayingIconBeforeTitle.visibility = View.VISIBLE
                holder.ivPlayingIconBeforeTitle.setImageDrawable(
                    ContextCompat.getDrawable(
                        holder.itemView.context,
                        R.drawable.host_anim_play_flag
                    )
                )
                SpanUtils.with(holder.tvAlbumTitle)
                    .append("    ")
                    .append(model.title)
                    .create()
                if (PlayTools.isAlbumPlaying(
                        context,
                        model.albumId
                    ) && !SoundPatchHostManager.getInstance().isPlayingSoundPatch
                ) {
                    if (holder.ivPlayingIconBeforeTitle.drawable is AnimationDrawable) {
                        (holder.ivPlayingIconBeforeTitle.drawable as AnimationDrawable).start()
                    }
                    holder.ivPlay.setImageResource(
                        R.drawable.host_btn_pause_btn_n_fill_n_12
                    )
                } else {
                    holder.ivPlay.setImageResource(
                        R.drawable.host_btn_play_btn_inside_fill_n_12
                    )
                    AnimationUtil.stopAnimation(holder.ivPlayingIconBeforeTitle)
                }
                holder.ivAlbumCover.setOnClickListener {
                    if (!OneClickHelper.getInstance().onClick(it)) {
                        return@setOnClickListener
                    }
                    if (SoundPatchHostManager.getInstance().isPlayingSoundPatch) {
                        return@setOnClickListener
                    }
                    if (PlayTools.isAlbumPlaying(context, model.albumId)) {
                        PlayTools.pause(context, PauseReason.Business.PlayCompleteRecommend)
                    } else {
                        PlayTools.playByAlbumByIdIfHasHistoryUseHistory(
                            context,
                            model.albumId,
                            null
                        )
                    }
                }
                holder.ivPlay.visibility = View.VISIBLE
                holder.ivPlayPanel.visibility = View.VISIBLE
            } else {
                AnimationUtil.stopAnimation(holder.ivPlayingIconBeforeTitle)
                holder.ivPlayingIconBeforeTitle.visibility = View.INVISIBLE
                holder.tvAlbumTitle.text = model.title
                holder.ivPlay.visibility = View.INVISIBLE
                holder.ivPlayPanel.visibility = View.INVISIBLE
            }

            ImageManager.from(context).displayImage(
                holder.ivAlbumCover,
                model.coverLargePath,
                R.drawable.host_default_album
            ) { _: String?, bitmap: Bitmap? ->
                ColorUtil.getDefaultAlbumCoverDomainColor(
                    bitmap, Color.BLACK
                ) { color: Int ->
                    holder.ivPlayPanel.imageTintList = ColorStateList.valueOf(color)
                }
            }
            holder.itemView.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@setOnClickListener
                }
                // 新声音播放页-自动续播专辑卡片  点击事件
                XMTraceApi.Trace()
                    .click(54682) // 用户点击时上报
                    .put("currPage", "newPlay")
                    .put("currTrackId", getCurTrackId())
                    .put("currAlbumId", getCurAlbumId())
                    .put("recAlbumId", model.albumId.toString()) // 若存在专辑推荐的专辑则传推荐的专辑 id
                    .put("rec_src", model.recSrc)
                    .put("rec_track", model.recTrack)
                    .put("ubtTraceId", model.traceId)
                    .put(XmRequestIdManager.XM_REQUEST_ID, mXmRequestId)
                    .createTrace()
                AlbumEventManage.startMatchAlbumFragment(
                    model.albumId, AlbumEventManage.FROM_RECOMMEND_ALBUM,
                    -1, null, null, -1, BaseApplication.getMainActivity()
                )
            }
            holder.tvSubtitle.text = model.recReason
//            if (model.albumScore > 0) {
//                holder.tvScore.text = model.albumScore.toString()
//                ViewStatusUtil.setVisible(View.VISIBLE, holder.vPlayDot, holder.tvScore)
//            } else {
//                ViewStatusUtil.setVisible(View.GONE, holder.vPlayDot, holder.tvScore)
//            }
            holder.tvPlayCount.text = "${StringUtil.getFriendlyNumStr(model.playCounts)}次播放"
            holder.cbSettingOpen.isChecked = AutoPlaySwitchManager.isAutoPlay()
            holder.cslSettingOpen.setOnClickListener {
                traceAutoPlayClick(model)
                val status = holder.cbSettingOpen.isChecked.not()
                AutoPlaySwitchManager.setAutoPlay(status,
                    object : IDataCallBack<Boolean> {
                        override fun onSuccess(data: Boolean?) {
                            holder.cbSettingOpen.isChecked = status
                            if (PlayTools.isAlbumPlaying(context, model.albumId)) {
                                PlayTools.pause(context, PauseReason.Business.PlayCompleteRecommend)
                            }
                        }
                        override fun onError(code: Int, message: String?) {}
                    })
            }
            holder.cslOpen.visibility = View.VISIBLE
            holder.cslSettingClose.visibility = View.GONE
        } else {
            holder.cbSettingClose.isChecked = AutoPlaySwitchManager.isAutoPlay()
            holder.cslSettingClose.setOnClickListener {
                traceAutoPlayClick(model)
                val status = holder.cbSettingClose.isChecked.not()
                AutoPlaySwitchManager.setAutoPlay(status,
                    object : IDataCallBack<Boolean> {
                        override fun onSuccess(data: Boolean?) {
                            holder.cbSettingClose.isChecked = status
                        }

                        override fun onError(code: Int, message: String?) {}
                    })
            }
            holder.cslOpen.visibility = View.GONE
            holder.cslSettingClose.visibility = View.VISIBLE
            holder.ivPlay.visibility = View.INVISIBLE
            holder.ivPlayPanel.visibility = View.INVISIBLE
        }
    }

    private fun traceAutoPlayClick(
        model: AutoPlayAlbumModel
    ) {
        // 新声音播放页-自动续播设置条  点击事件
        XMTraceApi.Trace()
            .click(54676) // 用户点击时上报
            .put("currPage", "newPlay")
            .put("currTrackId", getCurTrackId())
            .put("currAlbumId", getCurAlbumId())
            .put("recAlbumId", model.albumId.toString()) // 若存在专辑推荐的专辑则传推荐的专辑 id
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                mXmRequestId
            )
            .put("status", if (AutoPlaySwitchManager.isAutoPlay()) "open" else "close") // 记录当前状态
            .put("action", if (AutoPlaySwitchManager.isAutoPlay()) "close" else "open") // 记录当前状态
            .createTrace()
    }

    private fun initWhiteListAlbumView(holder: WhiteListAlbumViewHolder, model: PlayEndGuideModel) {
        ImageManager.from(context).displayImageNotIncludeDownloadCacheSizeInDp(
            holder.ivCover,
            model.coverLargePath,
            com.ximalaya.ting.android.host.R.drawable.host_default_album,
            150,
            150
        )
        AlbumTagUtilNew.getInstance().loadImage(holder.ivTag, model.getAlbumSubscriptValue())
        if (model.playCounts > 0) {
            holder.tvPlayCount.setTextIfChanged(StringUtil.getFriendlyNumStr(model.playCounts))
            holder.tvPlayCount.visibility = View.VISIBLE
        } else {
            holder.tvPlayCount.visibility = View.GONE
        }
        if (PlayTools.isAlbumPlaying(
                context,
                model.albumId
            ) && !SoundPatchHostManager.getInstance().isPlayingSoundPatch
        ) {
            holder.tvHint.text = "正在为您播放"
            holder.tvHint.visibility = View.VISIBLE
            holder.tvAlbumTitle.setTextColor(
                ContextCompat.getColor(
                    holder.itemView.context,
                    R.color.main_color_888888_dcdcdc
                )
            )
            holder.ivPlayingIconBeforeTitle.visibility = View.VISIBLE
            if (holder.ivPlayingIconBeforeTitle.drawable is AnimationDrawable) {
                (holder.ivPlayingIconBeforeTitle.drawable as AnimationDrawable).start()
            }
            // 标题首行缩进一些，留位置给前面的图标
            val spannableString = SpannableString(model.title)
            val leadingMarginSpan = LeadingMarginSpan.Standard(16.dp, 0)
            spannableString.setSpan(
                leadingMarginSpan, 0, spannableString.length,
                SpannableString.SPAN_INCLUSIVE_INCLUSIVE
            )
            holder.tvAlbumTitle.text = spannableString
        } else {
            holder.tvHint.text = "即将为您播放"
            holder.tvHint.visibility = if (mIsPlayStatusChange) View.INVISIBLE else View.VISIBLE
            AnimationUtil.stopAnimation(holder.ivPlayingIconBeforeTitle)
            holder.ivPlayingIconBeforeTitle.visibility = View.INVISIBLE
            holder.tvAlbumTitle.setTextColor(
                ContextCompat.getColor(
                    holder.itemView.context,
                    R.color.main_color_333333_dcdcdc
                )
            )
            holder.tvAlbumTitle.text = model.title
        }
        holder.itemView.setOnClickListener { v ->
            if (!OneClickHelper.getInstance().onClick(v)) {
                return@setOnClickListener
            }
            AlbumEventManage.startMatchAlbumFragment(
                model.albumId, AlbumEventManage.FROM_RECOMMEND_ALBUM,
                -1, null, null, -1, BaseApplication.getMainActivity()
            )
            // 新声音播放页-白名单续播  点击事件
            XMTraceApi.Trace()
                .click(46140)
                .put("currPage", "newPlay")
                .put("albumId", model.albumId.toString())
                .put("trackId", model.trackId.toString())
                .put("ubtTraceId", "")
                .put("rec_track", "")
                .put("rec_src", "")
                .put("moduleName", "白名单续播")
                .put("isAutoPlay", "false")
                .put("currTrackId", getCurTrackId())
                .put(
                    XmRequestIdManager.XM_REQUEST_ID,
                    mXmRequestId
                )
                .createTrace()
        }
    }

    private fun initEverydayUpdateProgramView(
        holder: EverydayUpdateProgramViewHolder,
        model: PlayEndDetailItemModel
    ) {
        val tracks = model.tracks
        if (tracks.isNullOrEmpty()) {
            holder.itemView.visibility = View.GONE
            return
        }
        val moduleName = model.title ?: "正在追更的节目"
        holder.tvUpdateTitle.setTextIfChanged(moduleName)
        if (model.count > 0) {
            ViewStatusUtil.setVisible(View.GONE, holder.tvUpdateNum, holder.tvUpdateMore)
            holder.tvUpdateNum.text = StringUtil.getFriendlyNumStr(model.count)
            holder.tvUpdateMore.setOnClickListener { v ->
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return@setOnClickListener
                }
                // 新声音播放页-追更中（追更）  点击事件
                XMTraceApi.Trace()
                    .click(46162)
                    .put("moduleName", moduleName)
                    .put("currPage", "newPlay")
                    .createTrace()
            }
        } else {
            ViewStatusUtil.setVisible(View.GONE, holder.tvUpdateNum, holder.tvUpdateMore)
        }
        val count = min(tracks.size, 3)
        holder.itemHolders.forEachIndexed { i, itemHolder ->
            if (i < count) {
                val track = tracks[i]
                itemHolder.itemView.visibility = View.VISIBLE
                initTrackView(itemHolder, track, tracks, i, moduleName)
            } else {
                itemHolder.itemView.visibility = View.GONE
                itemHolder.itemView.setOnClickListener(null)
            }
        }
    }

    private fun initTrackView(
        itemHolder: EverydayUpdateItemHolder, track: Track,
        tracks: ArrayList<Track>, index: Int, moduleName: String
    ) {
        val res = if (PlayTools.isCurrentTrackPlaying(context, track))
            R.drawable.main_ic_recommend_stream_pause_btn_center_new1 else
            R.drawable.main_ic_recommend_stream_play_btn_center_new1
        itemHolder.ivPlayBtn.setImageResource(res)
        setPlayBtnColor(
            ContextCompat.getColor(
                itemHolder.itemView.context,
                R.color.main_color_33ffffff
            ), itemHolder
        )
        val coverPath = track.validCover
        ImageManager.from(context).displayImageNotIncludeDownloadCacheSizeInDp(
            itemHolder.ivCover,
            coverPath, com.ximalaya.ting.android.host.R.drawable.host_default_album, 60, 60
        ) { _: String?, bitmap: Bitmap? ->
            if (bitmap != null) {
                val color = ColorUtil.getColorByCoverUrl(coverPath)
                if (color != ColorUtil.INVALID_COLOR) {
                    setPlayBtnColor(color, itemHolder)
                } else {
                    LocalImageUtil.getDomainColorForRecommend(bitmap, Color.BLACK) { color1: Int ->
                        setPlayBtnColor(color1, itemHolder)
                        ColorUtil.saveColor(coverPath, color1)
                    }
                }
            }
        }
        if (PlayTools.isCurrentTrackPlaying(context, track)) {
            itemHolder.tvTitle.setTextColor(
                ContextCompat.getColor(
                    itemHolder.itemView.context,
                    R.color.main_color_888888_dcdcdc
                )
            )
            itemHolder.ivPlayingIconBeforeTitle.visibility = View.VISIBLE
            if (itemHolder.ivPlayingIconBeforeTitle.drawable is AnimationDrawable) {
                (itemHolder.ivPlayingIconBeforeTitle.drawable as AnimationDrawable).start()
            }
            // 标题首行缩进一些，留位置给前面的图标
            val spannableString = SpannableString(track.trackTitle)
            val leadingMarginSpan = LeadingMarginSpan.Standard(16.dp, 0)
            spannableString.setSpan(
                leadingMarginSpan, 0, spannableString.length,
                SpannableString.SPAN_INCLUSIVE_INCLUSIVE
            )
            itemHolder.tvTitle.text = spannableString
        } else {
            AnimationUtil.stopAnimation(itemHolder.ivPlayingIconBeforeTitle)
            itemHolder.ivPlayingIconBeforeTitle.visibility = View.INVISIBLE
            itemHolder.tvTitle.setTextColor(
                ContextCompat.getColor(
                    itemHolder.itemView.context,
                    R.color.main_color_333333_dcdcdc
                )
            )
            itemHolder.tvTitle.text = track.trackTitle
        }
        itemHolder.tvSubtitle.setTextIfChanged(
            String.format(
                Locale.getDefault(),
                "专辑：%s",
                track.album?.albumTitle
            )
        )
        itemHolder.tvDuration.setTextIfChanged(TimeHelper.toTime(track.duration.toDouble()))
        val lastPos = XmPlayerManager.getInstance(context).getHistoryPos(track.dataId)
        val ps = getPlaySchedule(lastPos.toLong(), track.duration.toLong())
        if (ps.isNullOrEmpty()) {
            itemHolder.tvProgress.visibility = View.GONE
        } else {
            itemHolder.tvProgress.visibility = View.VISIBLE
            if ("已播完" == ps) {
                itemHolder.tvProgress.text = ps
            } else {
                val spannableString = SpannableString(ps)
                spannableString.setSpan(
                    ForegroundColorSpan(Color.parseColor("#ffb32c")),
                    3, ps.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
                itemHolder.tvProgress.text = spannableString
            }
        }
        itemHolder.itemView.setOnClickListener { v ->
            if (!OneClickHelper.getInstance().onClick(v)) {
                return@setOnClickListener
            }
            val albumId = track.album?.albumId ?: 0
            // 新声音播放页-追更  点击事件
            XMTraceApi.Trace()
                .click(46142)
                .put("currPage", "newPlay")
                .put("albumId", albumId.toString())
                .put("trackId", track.dataId.toString())
                .put("ubtTraceId", "")
                .put("rec_track", "")
                .put("rec_src", "")
                .put("moduleName", moduleName)
                .put("isAutoPlay", "false")
                .put("currTrackId", getCurTrackId())
                .createTrace()
            if (track.isPaid && !track.isFree && !track.isAudition && !track.isAuthorized && !UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(context)
                return@setOnClickListener
            }
            when {
                PlayTools.isCurrentTrackPlaying(context, track) -> {
                    PlayTools.pause(context, PauseReason.Business.PlayCompleteRecommend)
                }

                PlayTools.isCurrentTrack(context, track) -> {
                    PlayTools.play(context)
                }

                else -> {
                    PlayTools.playList(context, tracks, index, v)
                }
            }
        }
    }

    private fun setPlayBtnColor(color: Int, itemHolder: EverydayUpdateItemHolder) {
        val gradientDrawable = GradientDrawable()
        gradientDrawable.shape = GradientDrawable.RECTANGLE
        gradientDrawable.cornerRadius = 11.dp.toFloat()
        gradientDrawable.setColor(color)
        itemHolder.vPlayBtnBg.background = gradientDrawable
    }

    fun getPlaySchedule(curPos: Long, duration: Long): String? {
        return when (curPos) {
            PlayerConstants.PLAY_COMPLETE.toLong() -> "已播完"
            PlayerConstants.PLAY_NO_HISTORY.toLong() -> null
            else -> {
                if (duration <= 0 || curPos < 0) return null
                val time = curPos / (duration * 1000.0) * 100
                val percent = time.toInt()
                when {
                    percent >= 97 -> "已播完"
                    percent >= 1 -> "已播：$percent%"
                    else -> "已播：1%"
                }
            }
        }
    }

    private fun initRecommendAlbumListView(
        holder: RecommendAlbumListViewHolder,
        model: PlayEndDetailItemModel
    ) {
        val albums = model.albums
        if (albums.isNullOrEmpty()) {
            holder.itemView.visibility = View.GONE
            return
        }
        val moduleName = model.title ?: "听了本节目的人也爱听"
        holder.tvRecommendAlbumTitle.setTextIfChanged(moduleName)
        if (albums.size > 3) {
            holder.vRefresh.visibility = View.VISIBLE
            holder.vRefresh.setOnClickListener { v ->
                if (OneClickHelper.getInstance().onClick(v)) {
                    // 新声音播放页-换一批（完播相似推荐）  点击事件
                    XMTraceApi.Trace()
                        .click(45467)
                        .put("currPage", "newPlay")
                        .put("moduleName", moduleName)
                        .put("currTrackId", getCurTrackId())
                        .createTrace()
                    showRecommendAlbum(holder, albums, true, moduleName)
                    AnimationUtil.rotateView(context, holder.ivRefresh)
                    holder.ivRefresh.postDelayed(
                        { AnimationUtil.stopAnimation(holder.ivRefresh) },
                        200
                    )
                    traceRecommendAlbumListView(holder, model)
                }
            }
        } else {
            holder.vRefresh.visibility = View.GONE
        }
        showRecommendAlbum(holder, albums, false, moduleName)
    }

    private fun showRecommendAlbum(
        holder: RecommendAlbumListViewHolder, albums: ArrayList<AlbumM>,
        isRefresh: Boolean, moduleName: String
    ) {
        holder.itemHolders.forEachIndexed { i, itemHolder ->
            if (i < albums.size) {
                if (isRefresh) {
                    itemHolder.curAlbumIndex = (itemHolder.curAlbumIndex + 3) % albums.size
                } else {
                    itemHolder.curAlbumIndex = itemHolder.curAlbumIndex % albums.size
                }
                val album = albums[itemHolder.curAlbumIndex]
                itemHolder.itemView.visibility = View.VISIBLE
                initRecommendAlbumView(itemHolder, album, i, moduleName)
            } else {
                itemHolder.itemView.visibility = View.INVISIBLE
                itemHolder.itemView.setOnClickListener(null)
            }
        }
    }

    private fun initRecommendAlbumView(
        itemHolder: RecommendAlbumItemHolder, album: AlbumM,
        position: Int, moduleName: String
    ) {
        ImageManager.from(context).displayImageNotIncludeDownloadCache(
            itemHolder.ivCover,
            album.validCover, com.ximalaya.ting.android.host.R.drawable.host_default_album
        )
        AlbumTagUtilNew.getInstance().loadImage(itemHolder.ivTag, album.albumSubscriptValue)
        if (album.playCount > 0) {
            itemHolder.tvPlayCount.setTextIfChanged(StringUtil.getFriendlyNumStr(album.playCount))
            itemHolder.tvPlayCount.visibility = View.VISIBLE
        } else {
            itemHolder.tvPlayCount.visibility = View.GONE
        }
        itemHolder.tvTitle.setTextIfChanged(album.albumTitle)
        itemHolder.itemView.setOnClickListener { v ->
            if (!OneClickHelper.getInstance().onClick(v)) {
                return@setOnClickListener
            }
            AlbumEventManage.startMatchAlbumFragment(
                album.id,
                0,
                0,
                album.recommentSrc,
                album.recTrack,
                0,
                BaseApplication.getMainActivity()
            )
            // 新声音播放页-完播相似推荐  点击事件
            XMTraceApi.Trace()
                .click(45465)
                .put("currPage", "newPlay")
                .put("rec_track", album.recTrack)
                .put("ubtTraceId", album.traceId)
                .put("currTrackId", getCurTrackId())
                .put("albumId", album.id.toString())
                .put("trackId", "0")
                .put("moduleName", moduleName)
                .put("positionNew", (position + 1).toString())
                .put("rec_src", album.recommentSrc)
                .put(
                    XmRequestIdManager.XM_REQUEST_ID,
                    mXmRequestId
                )
                .createTrace()
        }
    }

    private fun initRecommendRankView(
        holder: RecommendRankViewHolder,
        model: PlayEndDetailItemModel
    ) {
        val albums = model.albums
        if (albums.isNullOrEmpty()) {
            holder.itemView.visibility = View.GONE
            return
        }
        val moduleName = model.title ?: "同类节目排行榜"
        holder.tvRecommendRankTitle.setTextIfChanged(moduleName)
        holder.vRecommendRank.removeAllViews()
        for (i in albums.indices) {
            val album = albums[i]
            val lp = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            val item = buildRecommendRankAlbumView(holder.vRecommendRank, i, album, moduleName)
            holder.vRecommendRank.addView(item, lp)
        }
    }

    private fun initMonthlyTicketView(holder: MonthlyTicketGuideHolder, model: PlayingSoundInfo) {
        // 投月票入口  控件曝光
        val currTrack = PlayTools.getCurTrack(context)
        XMTraceApi.Trace()
            .setMetaId(48517)
            .setServiceId("slipPage")
            .put(
                "currPage",
                "newPlay"
            ) // giveMonthTicket表示投月票页|newPlay表示新声音播放页|album表示专辑详情页|newCommentPage表示评论 tab 页
            .put("currTrackId", (currTrack?.dataId ?: -1).toString()) // 记录当前页面的声音 id，若无则传-1
            .put("currAlbumId", (currTrack?.album?.albumId ?: -1).toString()) // 记录当前页面的专辑 id，若无则传-1
            .put("albumId", (model.albumInfo?.albumId ?: -1).toString())
            .put("trackId", (model.trackInfo?.trackId ?: -1).toString()) // 当没有时传-1
            .put("pageType", "完播浮层") // 新声音播放页｜完播浮层
            .put(
                XmRequestIdManager.CONT_ID,
                currTrack?.dataId.toString() + "_" + model.trackInfo?.trackId
            )
            .put(XmRequestIdManager.CONT_TYPE, "newPlayCompleteRecommend")
            .put(XmRequestIdManager.XM_REQUEST_ID, mXmRequestId)
            .createTrace()

        ImageManager.from(context).displayImage(
            holder.ivAvatar,
            model.userInfo?.smallLogo, R.drawable.mine_icon_space_default_avatar_210
        )
        holder.tvName.text = model.userInfo?.nickname
        holder.tvGuideTip.text = mMonthlyVoteGuideTip
        holder.btnVote.setOnClickListener {
            // 投月票入口  点击事件
            XMTraceApi.Trace()
                .click(48516) // 用户点击时上报
                .put(
                    "currPage",
                    "newPlay"
                ) // giveMonthTicket表示投月票页|newPlay表示新声音播放页|album表示专辑详情页|newCommentPage表示评论 tab 页
                .put("currTrackId", (currTrack?.dataId ?: -1).toString()) // 记录当前页面的声音 id，若无则传-1
                .put(
                    "currAlbumId",
                    (currTrack?.album?.albumId ?: -1).toString()
                ) // 记录当前页面的专辑 id，若无则传-1
                .put("albumId", (model.albumInfo?.albumId ?: -1).toString())
                .put("trackId", (model.trackInfo?.trackId ?: -1).toString()) // 当没有时传-1
                .put("pageType", "完播浮层") // 新声音播放页｜完播浮层
                .createTrace()

            VoteDialogFragment.show(
                fragment.childFragmentManager,
                "newPlay",
                model.trackInfo?.trackId ?: 0L,
                model.albumInfo?.albumId ?: 0L,
                model.userInfo?.uid ?: 0L
            )
        }
    }

    private fun initSubscribeGuideView(
        holder: SubscribeGuideHolder,
        model: PlayCompleteSubscribeGuideInfo
    ) {
        ImageManager.from(context).displayImage(holder.ivCover, model.albumCover, 0)
        holder.tvTitle.text = model.albumTitle
        holder.tvGuideTip.text = model.guideContent
        holder.btnSubscribe.visibility = if (model.isFavorite) View.INVISIBLE else View.VISIBLE
        holder.btnSubscribe.setOnClickListener {
            val param = AlbumCollectParam(
                "newPlay",
                AnchorCollectManage.SUBSCRIBE_BIZ_TYPE_PLAY_COMPLETE_RECOMMEND_RANK,
                0, 0, model.toAlbumM(), true
            )
            param.showFollowDialog = false
            AlbumEventManage.doCollectActionV3WithLoginCheckDeferredAction(
                param, fragment as? BaseFragment2, object : ICollectWithFollowStatusCallback {
                    override fun onCollectSuccess(code: Int, isCollected: Boolean) {
                        if (isCollected) {
                            model.isFavorite = true
                            holder.btnSubscribe.visibility = View.INVISIBLE
                        }
                    }

                    override fun onError() {
                    }

                    override fun followDialogAction(status: Int) {
                    }
                }, "newPlay"
            )
            traceClickSubscribeGuideView(model.albumId, "订阅")
        }
        holder.itemView.setOnClickListener {
            traceClickSubscribeGuideView(model.albumId, "专辑信息条")
            AlbumEventManage.startMatchAlbumFragment(
                model.albumId, 0, 0, null, null, 0, BaseApplication.getMainActivity()
            )
        }
    }

    private fun traceClickSubscribeGuideView(id: Long, item: String) {
        // 专辑完播页-订阅  点击事件
        XMTraceApi.Trace()
            .click(55739) // 用户点击时上报
            .put("currPage", "AlbumCompletionPage")
            .put("albumId", id.toString())
            .put("Item", item) // 订阅｜专辑信息条
            .createTrace()
    }

    private fun buildRecommendRankAlbumView(
        parent: ViewGroup,
        position: Int,
        album: AlbumM,
        moduleName: String
    ): View {
        val view = LayoutInflater.from(parent.context).inflate(
            R.layout.main_item_play_complete_recommend_rank_album, parent, false
        )
        val tvNum = view.findViewById<TextView>(R.id.main_tv_number)
        val ivCover = view.findViewById<ImageView>(R.id.main_iv_cover)
        val ivTag = view.findViewById<ImageView>(R.id.main_iv_tag)
        val tvTitle = view.findViewById<TextView>(R.id.main_tv_title)
        val tvIntro = view.findViewById<TextView>(R.id.main_tv_intro)
        val ivAnchor = view.findViewById<ImageView>(R.id.main_iv_anchor)
        val tvAnchor = view.findViewById<TextView>(R.id.main_tv_anchor)
        val tvAlsoListen = view.findViewById<TextView>(R.id.main_tv_also_listen)
        val tvSubscribe = view.findViewById<TextView>(R.id.main_tv_subscribe)
        tvNum.typeface = Typeface.createFromAsset(
            parent.context.resources.assets,
            "fonts/XmlyNumberV1.0-Regular.otf"
        )
        tvNum.text = (position + 1).toString()
        if (position < 3) {
            tvNum.setTextColor(ContextCompat.getColor(parent.context, R.color.main_color_ff4444))
        } else {
            tvNum.setTextColor(ContextCompat.getColor(parent.context, R.color.main_color_a3a3b6))
        }
        ImageManager.from(context).displayImageNotIncludeDownloadCacheSizeInDp(
            ivCover,
            album.validCover, com.ximalaya.ting.android.host.R.drawable.host_default_album, 60, 60
        )
        AlbumTagUtilNew.getInstance().loadImage(ivTag, album.albumSubscriptValue)
        var titleWithTag: Spanned? = null
        val textSize = tvTitle.textSize
        if (album.isFinished == 2) {
            titleWithTag = ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(
                context,
                " " + album.albumTitle,
                com.ximalaya.ting.android.host.R.drawable.host_tag_complete,
                textSize.toInt()
            )
        }
        if (titleWithTag != null) {
            tvTitle.text = titleWithTag
        } else {
            tvTitle.text = album.albumTitle
        }
        tvIntro.text = album.intro
        val announcer = album.announcer
        ImageManager.from(context).displayImageNotIncludeDownloadCacheSizeInDp(
            ivAnchor,
            announcer?.avatarUrl,
            com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88,
            11,
            11
        )
        tvAnchor.text = announcer?.nickname
        if (album.playCount > 0) {
            tvAlsoListen.text = String.format(
                Locale.getDefault(), "%s人正在收听",
                StringUtil.getFriendlyNumStr(album.playCount)
            )
            tvAlsoListen.visibility = View.VISIBLE
        } else {
            tvAlsoListen.visibility = View.GONE
        }
        setSubscribeStatus(tvSubscribe, album.isFavorite)
        tvSubscribe.setOnClickListener { v ->
            if (!OneClickHelper.getInstance().onClick(v)) {
                return@setOnClickListener
            }
            val item = if (album.isFavorite) "取消订阅" else "订阅"
            // 新声音播放页-订阅（完播同类节目排行榜）  点击事件
            XMTraceApi.Trace()
                .click(45464)
                .put("currPage", "newPlay")
                .put("Item", item)
                .put("albumId", album.id.toString())
                .put("ubtTraceId", album.traceId)
                .put("rec_track", album.recTrack)
                .put("rec_src", album.recommentSrc)
                .put("moduleName", moduleName)
                .createTrace()
            val param = AlbumCollectParam(
                "newPlay",
                AnchorCollectManage.SUBSCRIBE_BIZ_TYPE_PLAY_COMPLETE_RECOMMEND_RANK,
                0, 0, album
            )
            param.showFollowDialog = false
            AlbumEventManage.doCollectActionV3WithLoginCheckDeferredAction(
                param, fragment as? BaseFragment2, object : ICollectWithFollowStatusCallback {
                    override fun onCollectSuccess(code: Int, isCollected: Boolean) {
                        if (code == 0) {
                            album.isFavorite = isCollected
                            //  CustomToast.showSuccessToast(if (isCollected) com.ximalaya.ting.android.host.R.string.host_collect_success else com.ximalaya.ting.android.host.R.string.host_cancel_collect_success)
                            setSubscribeStatus(tvSubscribe, isCollected)
                        }
                    }

                    override fun onError() {
                    }

                    override fun followDialogAction(status: Int) {
                    }
                }, param.currPage
            )
        }
        view.setOnClickListener { v ->
            if (!OneClickHelper.getInstance().onClick(v)) {
                return@setOnClickListener
            }
            AlbumEventManage.startMatchAlbumFragment(
                album.id, AlbumEventManage.FROM_RECOMMEND_ALBUM,
                -1, album.recommentSrc, album.recTrack, -1, BaseApplication.getMainActivity()
            )
            // 新声音播放页-完播同类节目排行榜  点击事件
            XMTraceApi.Trace()
                .click(27559)
                .put("albumId", album.id.toString())
                .put("currTrackId", getCurTrackId())
                .put("currPage", "newPlay")
                .put("rec_track", album.recTrack)
                .put("rec_src", album.recommentSrc)
                .put("ubtTraceId", album.traceId)
                .put("positionNew", (position + 1).toString())
                .put("moduleName", moduleName)
                .put("trackId", "0")
                .put(XmRequestIdManager.XM_REQUEST_ID, mXmRequestId)
                .createTrace()
        }
        return view
    }

    private fun setSubscribeStatus(tvSubscribe: TextView, isFavorite: Boolean) {
        if (isFavorite) {
            tvSubscribe.isSelected = true
            tvSubscribe.text = "已订阅"
            tvSubscribe.setCompoundDrawables(null, null, null, null)
        } else {
            tvSubscribe.isSelected = false
            tvSubscribe.text = "订阅"
            tvSubscribe.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.main_album_icon_add, 0, 0, 0
            )
        }
    }

    private fun getCurTrackId(): String {
        val curSound =
            XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound
        return (curSound?.dataId ?: 0).toString()
    }

    private fun getCurAlbumId(): String {
        return (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound as? Track)?.album?.albumId?.toString()
            ?: ""
    }

    fun traceOnItemShow(position: Int, view: View?, markTime: Long) {
        view ?: return
        val item = getItem(position) ?: return
        val holder = view.tag
        if (holder is WhiteListAlbumViewHolder && item is PlayEndGuideModel) {
            traceWhiteListAlbumView(item)
        } else if (holder is EverydayUpdateProgramViewHolder && item is PlayEndDetailItemModel) {
            traceEverydayUpdateProgramView(holder, item)
        } else if (holder is RecommendAlbumListViewHolder && item is PlayEndDetailItemModel) {
            traceRecommendAlbumListView(holder, item)
        } else if (holder is RecommendRankViewHolder && item is PlayEndDetailItemModel) {
            traceRecommendRankView(holder, item, markTime)
        } else if (holder is AutoPlayRecommendViewHolder && item is AutoPlayAlbumModel) {
            traceAutoPlayAlbumView(item)
        } else if (holder is SubscribeGuideHolder && item is PlayCompleteSubscribeGuideInfo) {
            traceSubscribeGuideView(item)
        }
    }

    private fun traceAutoPlayAlbumView(model: AutoPlayAlbumModel) {
        // 新声音播放页-自动续播专辑卡片  控件曝光
        XMTraceApi.Trace()
            .setMetaId(54683)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newPlay")
            .put("currTrackId", getCurTrackId())
            .put("currAlbumId", getCurAlbumId())
            .put("recAlbumId", model.albumId.toString()) // 若存在专辑推荐的专辑则传推荐的专辑 id
            .put("rec_src", model.recSrc)
            .put("rec_track", model.recTrack)
            .put("ubtTraceId", model.traceId)
            .put(XmRequestIdManager.CONT_ID, getCurAlbumId())
            .put(XmRequestIdManager.CONT_TYPE, "newPlayCompleteRecommend")
            .put(
                XmRequestIdManager.XM_REQUEST_ID, model.xmRequestId)
            .createTrace()

        // 新声音播放页-自动续播设置条  控件曝光
        XMTraceApi.Trace()
            .setMetaId(54677)
            .setServiceId("slipPage")
            .put("currPage", "newPlay")
            .put("currTrackId", getCurTrackId())
            .put("currAlbumId", getCurAlbumId())
            .put("recAlbumId", model.albumId.toString())
            .put("status", if (AutoPlaySwitchManager.isAutoPlay()) "open" else "close") // 记录当前状态
            .put(XmRequestIdManager.CONT_ID, getCurAlbumId())
            .put(XmRequestIdManager.CONT_TYPE, "newPlayCompleteRecommend")
            .put(
                XmRequestIdManager.XM_REQUEST_ID, model.xmRequestId)
            .createTrace()
    }

    private fun traceSubscribeGuideView(info: PlayCompleteSubscribeGuideInfo) {
        // 专辑完播页-订阅  控件曝光
        XMTraceApi.Trace()
            .setMetaId(55740)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "AlbumCompletionPage")
            .put("albumId", info.albumId.toString())
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                mXmRequestId
            )
            .put(XmRequestIdManager.CONT_ID, info.albumId.toString())
            .put(XmRequestIdManager.CONT_TYPE, "订阅")
            .createTrace()
    }

    private fun traceWhiteListAlbumView(model: PlayEndGuideModel) {
        // 新声音播放页-白名单续播  控件曝光
        XMTraceApi.Trace()
            .setMetaId(46141)
            .setServiceId("slipPage")
            .put("currPage", "newPlay")
            .put("albumId", model.albumId.toString())
            .put("trackId", model.trackId.toString())
            .put("ubtTraceId", "")
            .put("rec_track", "")
            .put("rec_src", "")
            .put("moduleName", "白名单续播")
            .put("isAutoPlay", "false")
            .put("currTrackId", getCurTrackId())
            .put(XmRequestIdManager.CONT_ID, getCurTrackId())
            .put(XmRequestIdManager.CONT_TYPE, "newPlayCompleteRecommend")
            .put(
                XmRequestIdManager.XM_REQUEST_ID, model.xmRequestId
            ).createTrace()
    }

    private fun traceEverydayUpdateProgramView(
        holder: EverydayUpdateProgramViewHolder,
        model: PlayEndDetailItemModel
    ) {
        val tracks = model.tracks
        if (tracks.isNullOrEmpty()) {
            return
        }
        val moduleName = model.title ?: "正在追更的节目"
        holder.itemHolders.forEachIndexed { i, itemHolder ->
            val track = tracks.getOrNull(i)
            if (track != null && ViewStatusUtil.viewIsRealShowing(itemHolder.itemView)) {
                val albumId = track.album?.albumId ?: 0
                // 新声音播放页-追更  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(46143)
                    .setServiceId("slipPage")
                    .put("currPage", "newPlay")
                    .put("albumId", albumId.toString())
                    .put("trackId", track.dataId.toString())
                    .put("ubtTraceId", "")
                    .put("rec_track", "")
                    .put("rec_src", "")
                    .put("moduleName", moduleName)
                    .put("isAutoPlay", "false")
                    .put("currTrackId", getCurTrackId())
                    .put(XmRequestIdManager.CONT_ID, getCurTrackId())
                    .put(XmRequestIdManager.CONT_TYPE, "newPlayCompleteRecommend")
                    .put(
                        XmRequestIdManager.XM_REQUEST_ID, model.xmRequestId
                    ).createTrace()
            }
        }
    }

    private fun traceRecommendAlbumListView(
        holder: RecommendAlbumListViewHolder,
        model: PlayEndDetailItemModel
    ) {
        val albums = model.albums
        if (albums.isNullOrEmpty()) {
            return
        }
        val moduleName = model.title ?: "听了本节目的人也爱听"
        holder.itemHolders.forEachIndexed { i, itemHolder ->
            val album = albums.getOrNull(itemHolder.curAlbumIndex)
            if (album != null && ViewStatusUtil.viewIsRealShowing(itemHolder.itemView)) {
                // 新声音播放页-完播相似推荐  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(45466)
                    .setServiceId("slipPage")
                    .put("currPage", "newPlay")
                    .put("rec_track", album.recTrack)
                    .put("ubtTraceId", album.traceId)
                    .put("currTrackId", getCurTrackId())
                    .put("albumId", album.id.toString())
                    .put("trackId", "0")
                    .put("moduleName", moduleName)
                    .put("positionNew", (i + 1).toString())
                    .put("rec_src", album.recommentSrc)
                    .put(XmRequestIdManager.CONT_ID, album.id.toString())
                    .put(XmRequestIdManager.CONT_TYPE, "newPlayCompleteRecommend")
                    .put(
                        XmRequestIdManager.XM_REQUEST_ID, model.xmRequestId)
                    .createTrace()
            }
        }
    }

    private fun traceRecommendRankView(
        holder: RecommendRankViewHolder,
        model: PlayEndDetailItemModel,
        markTime: Long
    ) {
        val albums = model.albums
        if (albums.isNullOrEmpty()) {
            return
        }
        val container = holder.vRecommendRank
        val moduleName = model.title ?: "同类节目排行榜"
        for (i in 0 until container.childCount) {
            val child = container.getChildAt(i)
            val album = albums.getOrNull(i) ?: continue
            if (ViewStatusUtil.viewIsRealShowing(child)) {
                // 新声音播放页-完播同类节目排行榜  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(27560)
                    .setServiceId("slipPage")
                    .put("albumId", album.id.toString())
                    .put("currTrackId", getCurTrackId())
                    .put("currPage", "newPlay")
                    .put("rec_track", album.recTrack)
                    .put("rec_src", album.recommentSrc)
                    .put("ubtTraceId", album.traceId)
                    .put("positionNew", (i + 1).toString())
                    .put("moduleName", moduleName)
                    .put("trackId", "0")
                    .put("time", "$markTime")
                    .put(XmRequestIdManager.CONT_ID, album.id.toString())
                    .put(XmRequestIdManager.CONT_TYPE, "newPlayCompleteRecommend")
                    .put(
                        XmRequestIdManager.XM_REQUEST_ID, model.xmRequestId)
                    .createTrace()
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        val item = getItem(position) ?: return TYPE_NONE
        if (item is PlayEndGuideModel) {
            return TYPE_WHITE_LIST_ALBUM
        } else if (item is PlayEndDetailItemModel) {
            return when (item.type) {
                PlayEndDetailItemModel.TYPE_SUBSCRIBE_TRACK -> {
                    TYPE_EVERYDAY_UPDATE_PROGRAM
                }

                PlayEndDetailItemModel.TYPE_LISTEN_ALBUM -> {
                    TYPE_RECOMMEND_ALBUM_LIST
                }

                else -> {
                    TYPE_RANK
                }
            }
        } else if (item is PlayingSoundInfo) {
            return TYPE_MONTHLY_TICKET_GUIDE
        } else if (item is PlayCompleteSubscribeGuideInfo) {
            return TYPE_SUBSCRIBE_GUIDE
        } else if (item is AutoPlayAlbumModel) {
            return TYPE_AUTO_PLAY_RECOMMEND
        } else if (item is String && item == TYPE_HEAD_DEFAULT_STR) {
            return TYPE_HEAD_DEFAULT
        }
        return TYPE_NONE
    }

    override fun getItemCount(): Int {
        return mData.size
    }

    fun getItem(position: Int): Any? {
        return mData.getOrNull(position)
    }

    class AutoPlayRecommendViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val cslOpen: ConstraintLayout = itemView.findViewById(R.id.main_csl_auto)
        val tvStatusTitle: TextView = itemView.findViewById(R.id.main_tv_status)
        val ivAlbumCover: ImageView = itemView.findViewById(R.id.main_album_cover)
        val tvAlbumTitle: TextView = itemView.findViewById(R.id.main_tv_album_title)
        val tvSubtitle: TextView = itemView.findViewById(R.id.main_tv_subtitle)
        val tvScore: TextView = itemView.findViewById(R.id.main_tv_score)
        val tvPlayCount: TextView = itemView.findViewById(R.id.main_tv_play_count)
        val cslSettingOpen: ConstraintLayout = itemView.findViewById(R.id.main_csl_setting_open)
        val cbSettingOpen: CheckBox = itemView.findViewById(R.id.main_cb_setting_open)
        val ivPlayingIconBeforeTitle: ImageView =
            itemView.findViewById(R.id.main_iv_playing_icon_before_title)
        val ivPlay: ImageView = itemView.findViewById(R.id.main_iv_playing)
        val ivPlayPanel: ImageView = itemView.findViewById(R.id.main_iv_play_panel)
        val vPlayDot: View = itemView.findViewById(R.id.main_v_play_dot)

        // 引导开启
        val cslSettingClose: ConstraintLayout = itemView.findViewById(R.id.main_csl_setting_close)
        val cbSettingClose: CheckBox = itemView.findViewById(R.id.main_cb_setting_close)
    }

    class WhiteListAlbumViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvHint: TextView = itemView.findViewById(R.id.main_tv_will_or_playing_hint)
        val ivCover: ImageView = itemView.findViewById(R.id.main_iv_recommend_album_cover)
        val ivTag: ImageView = itemView.findViewById(R.id.main_iv_recommend_album_tag)
        val tvPlayCount: TextView = itemView.findViewById(R.id.main_tv_recommend_album_play_count)
        val ivPlayingIconBeforeTitle: ImageView =
            itemView.findViewById(R.id.main_iv_playing_icon_before_title)
        val tvAlbumTitle: TextView = itemView.findViewById(R.id.main_tv_recommend_album_title)
    }

    class EverydayUpdateProgramViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvUpdateTitle: TextView = itemView.findViewById(R.id.main_tv_everyday_update_title)
        val tvUpdateNum: TextView = itemView.findViewById(R.id.main_tv_everyday_update_num)
        val tvUpdateMore: TextView = itemView.findViewById(R.id.main_tv_everyday_update_more)
        val itemHolders: MutableList<EverydayUpdateItemHolder> = ArrayList()

        init {
            itemHolders.add(EverydayUpdateItemHolder(itemView.findViewById(R.id.main_v_track1)))
            itemHolders.add(EverydayUpdateItemHolder(itemView.findViewById(R.id.main_v_track2)))
            itemHolders.add(EverydayUpdateItemHolder(itemView.findViewById(R.id.main_v_track3)))
        }
    }

    class EverydayUpdateItemHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivCover: ImageView = itemView.findViewById(R.id.main_iv_cover)
        val ivPlayBtn: ImageView = itemView.findViewById(R.id.main_iv_play_btn)
        val vPlayBtnBg: View = itemView.findViewById(R.id.main_v_bg_play)
        val ivPlayingIconBeforeTitle: ImageView =
            itemView.findViewById(R.id.main_iv_playing_icon_before_title)
        val tvTitle: TextView = itemView.findViewById(R.id.main_tv_title)
        val tvSubtitle: TextView = itemView.findViewById(R.id.main_tv_subtitle)
        val tvDuration: TextView = itemView.findViewById(R.id.main_tv_duration)
        val tvProgress: TextView = itemView.findViewById(R.id.main_tv_progress)
    }

    class RecommendAlbumListViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvRecommendAlbumTitle: TextView =
            itemView.findViewById(R.id.main_tv_recommend_album_title)
        val vRefresh: View = itemView.findViewById(R.id.main_v_refresh)
        val ivRefresh: ImageView = itemView.findViewById(R.id.main_iv_refresh)
        val itemHolders: MutableList<RecommendAlbumItemHolder> = ArrayList()

        init {
            itemHolders.add(RecommendAlbumItemHolder(itemView.findViewById(R.id.main_v_album1), 0))
            itemHolders.add(RecommendAlbumItemHolder(itemView.findViewById(R.id.main_v_album2), 1))
            itemHolders.add(RecommendAlbumItemHolder(itemView.findViewById(R.id.main_v_album3), 2))
        }
    }

    class RecommendAlbumItemHolder(itemView: View, curPos: Int) :
        RecyclerView.ViewHolder(itemView) {
        val ivCover: ImageView = itemView.findViewById(R.id.main_iv_cover)
        val ivTag: ImageView = itemView.findViewById(R.id.main_iv_tag)
        val tvPlayCount: TextView = itemView.findViewById(R.id.main_tv_play_count)
        val tvTitle: TextView = itemView.findViewById(R.id.main_tv_title)
        var curAlbumIndex = curPos
    }

    class RecommendRankViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvRecommendRankTitle: TextView =
            itemView.findViewById(R.id.main_tv_recommend_rank_title)
        val vRecommendRank: LinearLayout = itemView.findViewById(R.id.main_ll_rank)
    }

    class MonthlyTicketGuideHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivAvatar: ImageView = itemView.findViewById(R.id.main_iv_anchor_avatar)
        val tvName: TextView = itemView.findViewById(R.id.main_tv_anchor_name)
        val tvGuideTip: TextView = itemView.findViewById(R.id.main_tv_guide_tip)
        val btnVote: Button = itemView.findViewById(R.id.main_btn_vote)
    }

    class SubscribeGuideHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivCover: ImageView = itemView.findViewById(R.id.main_iv_album_cover)
        val tvTitle: TextView = itemView.findViewById(R.id.main_tv_album_title)
        val tvGuideTip: TextView = itemView.findViewById(R.id.main_tv_guide_tip)
        val btnSubscribe: ViewGroup = itemView.findViewById(R.id.main_ll_subscribe)
    }

    class DefaultViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
}