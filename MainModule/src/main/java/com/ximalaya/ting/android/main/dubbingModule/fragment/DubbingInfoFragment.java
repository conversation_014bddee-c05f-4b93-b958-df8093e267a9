package com.ximalaya.ting.android.main.dubbingModule.fragment;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.Editable;
import android.text.Html;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.GestureDetector;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.WindowManager;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AccelerateInterpolator;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.airbnb.lottie.LottieAnimationView;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constants.CommentConstants;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.manager.OnlyUseMainProcessSharePreUtil;
import com.ximalaya.ting.android.host.manager.ShareResultManager;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.video.IVideoFunctionAction.IVideoCacheReuseManager;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.video.IVideoFunctionAction.VideoCacheReuseCallback;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.player.video.listener.IXmVideoView;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router.SimpleBundleInstallCallback;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.VideoActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.ZoneActionRouter;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.share.ShareConstants;
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel;
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage;
import com.ximalaya.ting.android.host.model.play.ChallengeInfoModel;
import com.ximalaya.ting.android.host.model.play.DubCoopActorData;
import com.ximalaya.ting.android.host.model.play.DubCoopData;
import com.ximalaya.ting.android.host.model.play.DubDownloadInfo;
import com.ximalaya.ting.android.host.model.play.DubShowModel;
import com.ximalaya.ting.android.host.model.play.LyricModel;
import com.ximalaya.ting.android.host.model.topic.TopicSourceInfo;
import com.ximalaya.ting.android.host.model.track.DubDialectLabel;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.ZoneBundleInterceptKt;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.other.PermissionManage;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.view.CenterAlignImageSpan;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.host.view.other.EmotionSelector;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain;
import com.ximalaya.ting.android.main.dubbingModule.DubbingPlayFragmentNew;
import com.ximalaya.ting.android.main.dubbingModule.adapter.ICallPagerCanScroll;
import com.ximalaya.ting.android.main.dubbingModule.adapter.IPageTrackIdChanged;
import com.ximalaya.ting.android.main.dubbingModule.controller.PPTPlayController;
import com.ximalaya.ting.android.main.dubbingModule.controller.VideoPlayController;
import com.ximalaya.ting.android.main.dubbingModule.dubdownload.DubVideoDownloadTaskController;
import com.ximalaya.ting.android.main.dubbingModule.interfaces.IClickTypeCallBack;
import com.ximalaya.ting.android.main.dubbingModule.interfaces.IOnParentFramgmentUserHint;
import com.ximalaya.ting.android.main.dubbingModule.interfaces.IOnUserInfoCallBack;
import com.ximalaya.ting.android.main.dubbingModule.interfaces.ISlideMoveCallback;
import com.ximalaya.ting.android.main.dubbingModule.model.DubLikeModel;
import com.ximalaya.ting.android.main.dubbingModule.model.DubPlayParams;
import com.ximalaya.ting.android.main.dubbingModule.model.DubbingSimpleItemInfo;
import com.ximalaya.ting.android.main.dubbingModule.util.DubbingInfoHelp;
import com.ximalaya.ting.android.main.dubbingModule.view.DubCooperationView;
import com.ximalaya.ting.android.main.dubbingModule.view.DubbAdPageView;
import com.ximalaya.ting.android.main.dubbingModule.view.DubbingCommentView;
import com.ximalaya.ting.android.main.dubbingModule.view.DubbingOurPeopleDialog;
import com.ximalaya.ting.android.main.dubbingModule.view.DubbingRankInstructionDialog;
import com.ximalaya.ting.android.main.dubbingModule.view.ICommentCallBack;
import com.ximalaya.ting.android.main.dubbingModule.view.IDubbingCommentView;
import com.ximalaya.ting.android.main.dubbingModule.view.IViewVisChanger;
import com.ximalaya.ting.android.main.dubbingModule.view.ResizeXmLottieAnimationView;
import com.ximalaya.ting.android.main.playModule.fragment.BasePlayFragment;
import com.ximalaya.ting.android.main.playModule.presenter.CommentPresenter;
import com.ximalaya.ting.android.main.playModule.presenter.PlayCommentManager;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.main.util.other.ShareUtilsInMain;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.main.view.VerticalImageSpan;
import com.ximalaya.ting.android.main.view.other.CommentQuoraInputLayout;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.history.XmPlayRecord;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.statistic.IPlayStatisticsUploaderFactory;
import com.ximalaya.ting.android.opensdk.player.statistic.IXmPlaySource;
import com.ximalaya.ting.android.opensdk.player.statistic.IXmPlayStatisticUploader;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayStatisticsUploaderManager;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.ref.WeakReference;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.Nullable;
import androidx.collection.LongSparseArray;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

/**
 * Created by le.xin on 2018/7/26. 配音秀详情页面(不能单独使用)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17097256298
 */
public class DubbingInfoFragment extends BasePlayFragment implements
    OnClickListener,
    IPlayerControllerContext,
    ICommentCallBack,
    ShareResultManager.ShareListener,
    IOnParentFramgmentUserHint {

    private static final String KEY_DUB_ID = "dubId";//可视化埋点指定key
    private static final boolean ALL_IS_VIDEO = true;
    public static LongSparseArray<Boolean> mUserIsAttention = new LongSparseArray<>();
    public static LongSparseArray<DubLikeModel> mDubbIsLiked = new LongSparseArray<>();
    protected IDubbingCommentView mCommentView = new DubbingCommentView(this, this);
    private RelativeLayout mRootLayout;
    private ImageView mIvPpt0;
    private ImageView mIvPpt1;
    private TextView mLyrView;
    private RoundImageView mLyrViewAvatar;
    private TextView mLyrViewHorizontal;
    private RoundImageView mLyrViewHorizontalAvatar;
    private FrameLayout mVideoPlayerContainer;
    private ImageView mBack;
    private RoundImageView mAnchorIcon;
    private TextView mAnchorName;
    private TextView mAnchorState;
    private LinearLayout mBottomLay;
    private TextView mLike;
    private TextView mComment;
    private TextView mShare;
    private TextView mTitle;
    private TextView mTvCommunityRelated;
    private TextView mDialectTextView;
    private LinearLayout mTopicLayout;
    private TextView mTopicNamePkView;
    private TextView mChallenge;
    private ImageView mGotoDubbing;
    private ImageView mPlayStatueBtn;
    private RelativeLayout mStateLay;
    private View mMarkView;
    private LinearLayout mInfoLay;
    private View commentBackView;
    private ImageView mZoom;
    private RelativeLayout mTitleBarLay;
    private LottieAnimationView mPlayLoadingView;
    private ViewStub mNoSuccessVideoStub;
    private View mNoSuccessVideoView;
    private View mMateLay;
    private ImageView mMateIcon1;
    private ImageView mMateIcon2;
    private ImageView mMateIcon3;
    private View mMateIconLay;
    private TextView mMateTitle;
    private ImageView[] mMateIcons;
    private DubbAdPageView mDubbAdPageView;
    private View mTopMask;
    private View mBottomMask;
    private FrameLayout mAdLay;
    private RelativeLayout mHorizontalLrcLay;
    private DubCooperationView mDubCooperationView;
    private long mTrackId;  // 页面当前的TrackId
    /**
     * 页面来源类型
     */
    private TopicSourceInfo mTopicSourceInfo;
    /**
     * 从挑战话题页面传进来的topic相关信息
     */
    private ChallengeInfoModel mChallengeInfoModel;
    private long mCurPlayTrackId;
    @Nullable
    private DubShowModel mData;
    private DubShowModel mCurPageData;
    private String[] mVideoPlayInfo;
    @Nullable
    private List<LyricModel> mLyrics;
    private String mErrorInfo;
    private boolean isTemplate;
    private boolean hasInnerSubtitle;
    private boolean isLoadingData = false;
    private boolean isPaused = false;
    private PPTPlayController mPPTPlayController;
    private VideoPlayController mVideoPlayController;
    ISlideMoveCallback mSlideMoveCallback = new ISlideMoveCallback() {
        private int moveLastHeight;

        @Override
        public void onSlideMove(int viewHeight, int moveLength) {
            if (moveLastHeight == moveLength && moveLength != 0) {
                return;
            }

            moveLastHeight = moveLength;

            if (mVideoPlayController != null && getView() != null) {
                final int screenWidth = BaseUtil.getScreenWidth(mContext);
                int toHeigh = getView().getHeight() - viewHeight;
                final int currentHeigh = toHeigh + moveLength;

                if (currentHeigh >= (getView().getHeight() - BaseUtil.dp2px(mContext, 20))) {
                    showTopView();
                } else if (currentHeigh <= getView().getHeight() / 2) {
                    hideTopView();
                }

                ViewGroup.LayoutParams layoutParams = mVideoPlayerContainer.getLayoutParams();
                layoutParams.width = screenWidth;
                layoutParams.height = currentHeigh;
                mVideoPlayerContainer.setLayoutParams(layoutParams);

                View videoView = mVideoPlayerContainer.getChildAt(0);
                if (videoView instanceof IXmVideoView) {
                    FrameLayout.LayoutParams lp = (FrameLayout.LayoutParams) videoView.getLayoutParams();
                    if (lp != null) {
                        if (isVertical()) {
                            if (currentHeigh == getView().getHeight()) {
                                lp.width = FrameLayout.LayoutParams.WRAP_CONTENT;
                                lp.height = FrameLayout.LayoutParams.WRAP_CONTENT;
                            } else {
                                int[] videoSize = mVideoPlayController.videoSize();

                                float scale = 1;
                                if (videoSize != null && videoSize.length > 1 && videoSize[1] > 0) {
                                    scale = videoSize[0] * 1.0f / videoSize[1];
                                }

                                float v = screenWidth / scale;
                                if (v > currentHeigh) {
                                    lp.width = (int) (currentHeigh * scale);
                                    lp.height = currentHeigh;
                                } else {
                                    lp.width = screenWidth;
                                    lp.height = (int) v;
                                }
                            }

                            lp.gravity = Gravity.CENTER;
                            videoView.setLayoutParams(lp);
                        } else {
                            lp.width = currentHeigh;
                            lp.height = screenWidth;
                            lp.gravity = Gravity.CENTER;
                            videoView.setLayoutParams(lp);
                        }
                    }
                }

                HandlerManager.postOnUIThread(() -> mVideoPlayController.notifyViewSize());
            }
        }
    };
    private DubbingInfoHelp mDubbingInfoHelp;
    private ICallPagerCanScroll mCanScrollCallback;
    private IHandleOk mCloseCallBack;
    private IPageTrackIdChanged mPageIdChanged;
    private IOnUserInfoCallBack mUserInfoCallBack;
    private int cannotScrollCount = 0;
    IViewVisChanger viewVisChanger = new IViewVisChanger() {
        @Override
        public void onVisibilityChangeListener(int visibility) {
            if (visibility != View.VISIBLE) {
                cannotScrollCount--;
            } else {
                cannotScrollCount++;
            }

            if (cannotScrollCount < 0) {
                cannotScrollCount = 0;
            }
            if (mCanScrollCallback != null) {
                mCanScrollCallback.canScroll(cannotScrollCount == 0);
            }

        }
    };
    EmotionSelector.IKeyboardListener keyboardListener = show -> {
        if (mCommentInputBar == null) {
            return;
        }

        boolean isInputing = !(mCommentInputBar.getEmotionSelector().getEmotionPanelStatus() == View.GONE &&
            !show);

        if (isInputing && mCommentView.isViseable()) {
            float y = mCommentInputBar.getY();
            float commentViewY = BaseUtil.getScreenHeight(mContext) - DubbingInfoHelp.getDialogHeight(mContext);
            if (commentViewY < y) {
                EditText etInput = mCommentInputBar.getEtInput();
                if (etInput != null) {
                    float addLineCount = (y - commentViewY) / ((etInput.getHeight() - (etInput
                        .getPaddingBottom() + etInput.getPaddingTop())) * 1.0f / etInput.getLineCount());
                    double realAddLineCount = Math.ceil(addLineCount);
                    double v = realAddLineCount + etInput.getLineCount();
                    if (v < 4) {
                        v = 4;
                    }
                    etInput.setLines((int) v);
                }
            }
        }

        if (!isInputing) {
            if (viewVisChanger != null) {
                viewVisChanger.onVisibilityChangeListener(View.GONE);
            }
            if (mCommentInputBar.getVisibility() != View.GONE && cannotScrollCount == 0) {
                if (mSlideMoveCallback != null) {
                    mSlideMoveCallback.onSlideMove(0, 0);
                }
            }
            mCommentInputBar.setVisibility(View.GONE);
            findViewById(R.id.main_whole_mask).setVisibility(View.GONE);
        }
    };
    private List<TrackM> mPeopleDialogTracks;
    private List<TrackM> mTopTracks;
    private int mPeopleDialogPosition;
    private int mPeopleDialogTop;
    private int mPeopleDialogPageId;
    private int mPeopleDataPageId;
    private long mDemoTrackId;
    private int mPageIndex;
    private long mTemplateVideoId;
    private boolean mOpenComment;
    private boolean mIsFromUserInfo;
    private Editable mCommentEdit;
    private Runnable mOpenCommentRunnable = new Runnable() {
        @Override
        public void run() {
            if (mComment != null && canUpdateUi()) {
                mComment.performClick();
            }
        }
    };
    private String mAnchorNickName;
    private String mAnchorUserIcon;
    private boolean likeingIsLoading = false;
    /**
     * 代表页面显示状态：0 无话题；1 直播话题；2  旧话题； 3 新话题正在进行 4 新话题已过期
     */
    private int mDubInfoViewState;
    /**
     * 是否已显示过合作配音view
     */
    private boolean mHasShownCooperationView;
    private int mAdPosition;
    private long requestId;
    private boolean hasCallShowed = false;
    private ScreenChangeBroadcastReceiver mChangeBroadcastReceiver = new ScreenChangeBroadcastReceiver();
    private long lastScreenPauseTime;
    private long lastPauseTime = 0;
    private boolean isLyrViewPositioned = false;    // 歌词控件是否已经定位好 ,定位完成才能显示
    private int lastIndexOfZimu = -1;
    private Runnable mRunnable = new Runnable() {
        @Override
        public void run() {
            if (mPlayLoadingView != null) {
                mPlayLoadingView.playAnimation();
                mPlayLoadingView.setVisibility(View.VISIBLE);
            }
        }
    };
    private long lastBuffTime;
    private IXmPlayStatisticUploader mPlayRecord;
    private IXmPlayStatisticUploader mDynamicPlayRecord;    // 动态播放上报
    private long mLastCreateLikeAnimationTime;
    private XmLottieAnimationView mLastBottomAnimtion;
    private boolean mCanPreLoad = false;
    private String videoPlayUrl;
    private boolean isPreloaded = false;
    private AnimatorSet mHideAnimatorSet;
    private AnimatorSet mShowAnimatorSet;
    private Runnable mHideInfoViewRunnable = new Runnable() {
        @Override
        public void run() {
            if (canUpdateUi() && mInfoLay != null) {
                if (mHideAnimatorSet != null) {
                    mHideAnimatorSet.cancel();
                    mHideAnimatorSet = null;
                }

                if (mShowAnimatorSet != null) {
                    mShowAnimatorSet.cancel();
                    mShowAnimatorSet = null;
                }

                if (mTopMask.getAlpha() == 0 || mTopMask.getVisibility() != View.VISIBLE) {
                    return;
                }
                ObjectAnimator infoLayTranY = ObjectAnimator.ofFloat(mInfoLay, "translationY", 0, mInfoLay.getHeight());
                ObjectAnimator gotoLayTranY = ObjectAnimator.ofFloat(mGotoDubbing, "translationY", 0, mGotoDubbing.getHeight());
                ObjectAnimator topViewTranY = ObjectAnimator.ofFloat(mTitleBarLay, "translationY", 0, -mTitleBarLay.getHeight());
                ObjectAnimator adLayTranX = ObjectAnimator.ofFloat(mAdLay, "translationX", 0, -mAdLay.getWidth());
                ObjectAnimator topMaskAlpha = ObjectAnimator.ofFloat(mTopMask, "alpha", 1.0f, 0f);
                ObjectAnimator bottomMaskAlpha = ObjectAnimator.ofFloat(mBottomMask, "alpha", 1.0f, 0f);
                ObjectAnimator coopViewTranY = null;
                if (mDubCooperationView.getVisibility() == View.VISIBLE) {
                    RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mDubCooperationView.getLayoutParams();
                    coopViewTranY = ObjectAnimator.ofFloat(mDubCooperationView, "translationY", 0,
                        mDubCooperationView.getHeight() + layoutParams.bottomMargin);
                }
                infoLayTranY.addUpdateListener(animation -> {
                    if (mLyrViewHorizontal != null && !TextUtils.isEmpty(mLyrViewHorizontal.getText())) {
                        changeHorizontalLyrViewParams(mLyrViewHorizontal.getText().toString());
                    }
                });
                AnimatorSet animatorSet = new AnimatorSet();
                if (null == coopViewTranY) {
                    animatorSet.playTogether(infoLayTranY, gotoLayTranY, topViewTranY, adLayTranX, topMaskAlpha, bottomMaskAlpha);
                } else {
                    animatorSet.playTogether(infoLayTranY, gotoLayTranY, topViewTranY, adLayTranX, topMaskAlpha, bottomMaskAlpha, coopViewTranY);
                }
                animatorSet.setDuration(500);
                animatorSet.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        hideInfoViewNoAnimation(true);
                        // mInfoLay的x方向复位
                        if (mInfoLay.getX() < 0) {
                            mInfoLay.setTranslationX(0);
                        }
                        mHideAnimatorSet = null;
                    }
                });
                animatorSet.start();
                mHideAnimatorSet = animatorSet;
            }
        }
    };
    private Runnable mShowInfoViewRunnable = new Runnable() {
        @Override
        public void run() {
            if (canUpdateUi() && mInfoLay != null) {
                if (mHideAnimatorSet != null) {
                    mHideAnimatorSet.cancel();
                    mHideAnimatorSet = null;
                }

                if (mShowAnimatorSet != null) {
                    mShowAnimatorSet.cancel();
                    mShowAnimatorSet = null;
                }

                if (mInfoLay.getTranslationY() == 0 && mInfoLay.getVisibility() == View.VISIBLE) {
                    return;
                }

                ObjectAnimator infoLayTranY = ObjectAnimator.ofFloat(mInfoLay, "translationY", mInfoLay.getHeight(), 0);
                infoLayTranY.addUpdateListener(animation -> {
                    if (mLyrViewHorizontal != null && !TextUtils.isEmpty(mLyrViewHorizontal.getText())) {
                        changeHorizontalLyrViewParams(mLyrViewHorizontal.getText().toString());
                    }
                });
                ObjectAnimator gotoLayTranY = ObjectAnimator.ofFloat(mGotoDubbing, "translationY", mGotoDubbing.getHeight(), 0);
                ObjectAnimator topViewTranY = ObjectAnimator.ofFloat(mTitleBarLay, "translationY", -mTitleBarLay.getHeight(), 0);
                ObjectAnimator adLayTranX = ObjectAnimator.ofFloat(mAdLay, "translationX", -mAdLay.getWidth(), 0);
                ObjectAnimator topMaskAlpha = ObjectAnimator.ofFloat(mTopMask, "alpha", 0f, 1.0f);
                ObjectAnimator bottomMaskAlpha = ObjectAnimator.ofFloat(mBottomMask, "alpha", 0f, 1.0f);
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playTogether(infoLayTranY, gotoLayTranY, topViewTranY, adLayTranX, topMaskAlpha, bottomMaskAlpha);
                animatorSet.setDuration(500);
                animatorSet.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                        super.onAnimationStart(animation);
                        visiableNoAnimator();
                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        mShowAnimatorSet = null;

                        setHiddendInfoView(false);
                    }
                });
                animatorSet.start();
                mShowAnimatorSet = animatorSet;
            }
        }
    };
    private Runnable mHorizontalShowInfoViewRunnable = new Runnable() {
        @Override
        public void run() {
            if (canUpdateUi() && mInfoLay != null) {
                if (mHideAnimatorSet != null) {
                    mHideAnimatorSet.cancel();
                    mHideAnimatorSet = null;
                }
                if (mShowAnimatorSet != null) {
                    mShowAnimatorSet.cancel();
                    mShowAnimatorSet = null;
                }
                if (mInfoLay.getTranslationX() == 0 && mInfoLay.getVisibility() == View.VISIBLE) {
                    return;
                }
                ObjectAnimator infoLayTranX = ObjectAnimator.ofFloat(mInfoLay, "translationX", -mInfoLay.getWidth(), 0);
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playSequentially(infoLayTranX);
                animatorSet.setDuration(300);
                animatorSet.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                        super.onAnimationStart(animation);
                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        mShowAnimatorSet = null;
                        setHiddendInfoView(false);
                    }
                });
                animatorSet.start();
                mShowAnimatorSet = animatorSet;
            }
        }
    };
    private Runnable mHorizontalHideInfoViewRunnable = new Runnable() {
        @Override
        public void run() {
            if (canUpdateUi() && mInfoLay != null) {
                if (mHideAnimatorSet != null) {
                    mHideAnimatorSet.cancel();
                    mHideAnimatorSet = null;
                }
                if (mShowAnimatorSet != null) {
                    mShowAnimatorSet.cancel();
                    mShowAnimatorSet = null;
                }
                RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mDubCooperationView.getLayoutParams();
                int translationX = -mDubCooperationView.getWidth() - layoutParams.leftMargin;
                mDubCooperationView.setTranslationX(translationX);
                mDubCooperationView.visible();
                ObjectAnimator dubCoopTranX = ObjectAnimator.ofFloat(mDubCooperationView, "translationX", translationX, 0);
                ObjectAnimator infoLayTranX = ObjectAnimator.ofFloat(mInfoLay, "translationX", 0, -mInfoLay.getWidth());
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playSequentially(infoLayTranX, dubCoopTranX);
                animatorSet.setDuration(300);
                animatorSet.addListener(new AnimatorListenerAdapter() {

                    @Override
                    public void onAnimationStart(Animator animation) {
                        super.onAnimationStart(animation);
                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        mHideAnimatorSet = null;
                    }
                });
                animatorSet.start();
                mHideAnimatorSet = animatorSet;
            }
        }
    };
    private AutoTraceHelper.IDataProvider mDataProvider = new AutoTraceHelper.IDataProvider() {
        @Override
        public Object getData() {
            if (mCurPageData == null) {
                return mData;
            }
            return mData;
        }

        @Override
        public Object getModule() {
            return null;
        }

        @Override
        public String getModuleType() {
            return AutoTraceHelper.MODULE_DEFAULT;
        }
    };

    public static DubbingInfoFragment newInstance(long trackId, boolean isFromUserInfo, int position) {
        DubbingInfoFragment fragment = new DubbingInfoFragment();
        Bundle bundle = new Bundle();
        bundle.putLong(KEY_DUB_ID, trackId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_PAGE_INDEX, position);
        bundle.putBoolean(BundleKeyConstantsInMain.KEY_IS_FROM_USERINFO, isFromUserInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected String getPageLogicName() {
        return getClass().getSimpleName();
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        super.initUi(savedInstanceState);
        if (getArguments() != null) {
            mTrackId = getArguments().getLong(KEY_DUB_ID);
            mIsFromUserInfo = getArguments().getBoolean(BundleKeyConstantsInMain.KEY_IS_FROM_USERINFO);
            mPageIndex = getArguments().getInt(BundleKeyConstantsInMain.KEY_PAGE_INDEX);
            mTemplateVideoId = getCooperateTemplateIdFromParent(mTrackId);
        }
        mTitleBarLay = findViewById(R.id.main_title_lay);
        mIvPpt0 = findViewById(R.id.main_iv_ppt_0);
        mIvPpt1 = findViewById(R.id.main_iv_ppt_1);
        mLyrView = findViewById(R.id.main_lyr);
        mLyrViewAvatar = findViewById(R.id.main_lyr_avatar);
        mLyrViewHorizontal = findViewById(R.id.main_lyr_horizontal);
        mLyrViewHorizontalAvatar = findViewById(R.id.main_lyr_horizontal_avatar);
        mVideoPlayerContainer = findViewById(R.id.main_video_player_container);
        mBack = findViewById(R.id.main_back);
        mAnchorIcon = findViewById(R.id.main_anchor_icon);
        mAnchorName = findViewById(R.id.main_anchor_name);
        mAnchorState = findViewById(R.id.main_anchor_state);
        mBottomLay = findViewById(R.id.main_bottom_lay);
        mLike = findViewById(R.id.main_like);
        mComment = findViewById(R.id.main_comment);
        mShare = findViewById(R.id.main_share);
        mTitle = findViewById(R.id.main_title);
        mTvCommunityRelated = findViewById(R.id.main_community_name_related);
        mTvCommunityRelated.setOnClickListener(this);
        mDialectTextView = findViewById(R.id.main_dialect_text_view);
        mTopicLayout = findViewById(R.id.main_topic_layout);
        mTopicNamePkView = findViewById(R.id.main_topic_name_pk_view);
        mChallenge = findViewById(R.id.main_challenge);
        mGotoDubbing = findViewById(R.id.main_goto_dubbing);
        mPlayStatueBtn = findViewById(R.id.main_play_or_pause_btn);
        mStateLay = findViewById(R.id.main_attention_state_lay);
        mInfoLay = findViewById(R.id.main_anchor_info_lay);
        mMarkView = findViewById(R.id.main_mark_view);
        mZoom = findViewById(R.id.main_zoom);
        mZoom.setImageResource(isVertical() ? R.drawable.main_icon_zoom : R.drawable.main_icon_zoomin);
        mPlayLoadingView = findViewById(R.id.main_lottie_play_loading);
        mNoSuccessVideoStub = findViewById(R.id.main_dubbing_video_coding_no_success_stub);
        mMateLay = findViewById(R.id.main_mate_lay);
        mMateIconLay = findViewById(R.id.main_mate_icon_lay);
        mMateIcon1 = findViewById(R.id.main_mate_icon_1);
        mMateIcon2 = findViewById(R.id.main_mate_icon_2);
        mMateIcon3 = findViewById(R.id.main_mate_icon_3);
        mMateIcons = new ImageView[] {mMateIcon1, mMateIcon2, mMateIcon3};
        mMateTitle = findViewById(R.id.main_dubbing_mate_title);
        mRootLayout = findViewById(R.id.main_container);
        mTopMask = findViewById(R.id.main_dubb_top_mask);
        mBottomMask = findViewById(R.id.main_dubb_bottom_mask);
        mAdLay = findViewById(R.id.main_dubb_ad_lay);
        mHorizontalLrcLay = findViewById(R.id.main_lyr_horizontal_ll);
        mDubCooperationView = findViewById(R.id.main_dub_cooperation_view);
        final GestureDetector gestureDetector = createGestureDetector(mContext, new IClickTypeCallBack() {
            @Override
            public void onSingleClick(MotionEvent event) {
                if (mRootLayout != null) {
                    if (mVideoPlayController != null) {
                        if (!mVideoPlayController.playOrPause() && !isVertical()) {
                            beginShowOtherView();
                        }
                    } else if (mPPTPlayController != null) {
                        mPPTPlayController.playOrPause();
                    }
                }
            }

            @Override
            public void onMultClick(MotionEvent event) {
                if (mData != null) {
                    createLikeAnimationView(mRootLayout, event);
                }

                if (mData != null && mData.trackInfo != null && !mData.trackInfo.isLike() &&
                    !likeingIsLoading) {
                    mLike.performClick();
                }

                OnlyUseMainProcessSharePreUtil.getInstance(mContext).saveBoolean(PreferenceConstantsInMain.KEY_DUBB_IS_DOUBLE_ED, true);
            }
        });
        mRootLayout.setOnTouchListener((v, event) -> gestureDetector.onTouchEvent(event));

        mTopicLayout.setOnClickListener(this);
        mBack.setOnClickListener(this);
        mAnchorIcon.setOnClickListener(this);
        mAnchorName.setOnClickListener(this);
        mAnchorState.setOnClickListener(this);
        mComment.setOnClickListener(this);
        mShare.setOnClickListener(this);
        mChallenge.setOnClickListener(this);
        mGotoDubbing.setOnClickListener(this);
        mZoom.setOnClickListener(this);
        mLike.setOnClickListener(this);
        mMateLay.setOnClickListener(this);
        mTitle.setOnClickListener(this);

        AutoTraceHelper.bindDataCallback(mBack, mDataProvider);
        AutoTraceHelper.bindDataCallback(mAnchorIcon, mDataProvider);
        AutoTraceHelper.bindDataCallback(mAnchorName, mDataProvider);
        AutoTraceHelper.bindDataCallback(mAnchorState, mDataProvider);
        AutoTraceHelper.bindDataCallback(mComment, mDataProvider);
        AutoTraceHelper.bindDataCallback(mShare, mDataProvider);
        AutoTraceHelper.bindDataCallback(mChallenge, mDataProvider);
        AutoTraceHelper.bindDataCallback(mGotoDubbing, mDataProvider);
        AutoTraceHelper.bindDataCallback(mZoom, mDataProvider);
        AutoTraceHelper.bindDataCallback(mLike, mDataProvider);
        AutoTraceHelper.bindDataCallback(mMateLay, mDataProvider);

        mDubbingInfoHelp = new DubbingInfoHelp(this);

        loadDataOnInit(mTrackId);

        if (mCommentManager != null) {
            mCommentManager.setQuoraLayoutChangeListener(visibility -> {
                if (visibility == View.VISIBLE) {
                    cannotScrollCount++;
                } else {
                    if (mCommentInputBar != null && mCommentInputBar.getEmotionSelector() != null
                        && mCommentInputBar.getEmotionSelector().getEmotionPanelStatus() == View.VISIBLE) {
                        cannotScrollCount--;
                    }
                }

                if (cannotScrollCount < 0) {
                    cannotScrollCount = 0;
                }

                if (mCanScrollCallback != null) {
                    mCanScrollCallback.canScroll(cannotScrollCount == 0);
                }
                if (commentBackView != null) {
                    commentBackView.setEnabled(visibility == View.VISIBLE);
                }

                if (visibility != View.VISIBLE && mCommentInputBar != null) {
                    mCommentEdit = mCommentInputBar.getCommentContent();
                    mRootLayout.removeView(mCommentInputBar);
                    mCommentInputBar = null;
                }

                if (cannotScrollCount == 1) {
                    if (visibility == View.VISIBLE) {
                        HandlerManager.postOnUIThread(new Runnable() {
                            @Override
                            public void run() {
                                if (mSlideMoveCallback != null && mCommentInputBar != null
                                    && mCommentInputBar.getContent() != null) {
                                    if (mCommentInputBar.getContent().getHeight() == 0) {
                                        mCommentInputBar.getContent().measure(0, 0);
                                        mSlideMoveCallback.onSlideMove(mCommentInputBar.getContent().getMeasuredHeight(), 0);
                                    } else {
                                        mSlideMoveCallback.onSlideMove(mCommentInputBar.getContent().getHeight(), 0);
                                    }
                                }
                            }
                        });
                    }
                }

                if (cannotScrollCount == 0 && visibility != View.VISIBLE) {
                    if (mSlideMoveCallback != null) {
                        mSlideMoveCallback.onSlideMove(0, 0);
                    }
                }
            });
        }

        if (mCommentView != null) {
            mCommentView.setViewVisChangerListener(viewVisChanger);
            mCommentView.setSlideMoveHight(mSlideMoveCallback);
            mCommentView.setCommentCallBack(this);
            mCommentPresenter = new CommentPresenter(mCommentView);
            mCommentView.setPresenter(mCommentPresenter);
            mCommentManager.setCommentView(mCommentView);
        }

        IntentFilter filter = new IntentFilter();
        filter.addAction(ScreenChangeBroadcastReceiver.SCREEN_CHANGE_ACTION);
        filter.addAction(ScreenChangeBroadcastReceiver.HIDDEN_CHANGE_ACTION);
        filter.addAction(ScreenChangeBroadcastReceiver.VISABLE_CHANGE_ACTION);
        filter.addAction(ScreenChangeBroadcastReceiver.CLOSE_AD_CHANGE_ACTION);
        LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).
            registerReceiver(mChangeBroadcastReceiver, filter);

        if (!isVertical() && !isRealVisable()) {
            if (isHiddendInfoView()) {
                hideInfoViewNoAnimation(false);
            } else {
                showInfoAnimatorNoAnimtor();
            }
        }
    }

    private void loadDataOnInit(final long trackId) {
        loadDataOnInit(trackId, false);
    }

    private void loadDataOnInit(final long trackId, final boolean isFromDialog) {
        if (isLoadingData && requestId == trackId) {
            return;
        }
        mCommentEdit = null;
        requestId = trackId;

        // 设置父类不再在setUserHint的时候再次调用loadData
        super.hasLoadData = true;
        isLoadingData = true;

        Map<String, String> params = new HashMap<>();
        DubShowModel data = getRequestCache().get(trackId);
        if (data != null) {
            if (data.trackInfo != null) {
                onPageLoadingCompleted(LoadCompleteType.OK);
                setDataToView(data, isFromDialog);
            }
            // 如果缓存有配音信息，就不请求网络直接设置
            if (data.getDubCoopData() != null) {
                setDataToCooperationView(data.getDubCoopData().getActors());
            } else if (data.materialInfo != null && data.materialInfo.subMaterialId > 0) {
                loadDubShowCoopData(trackId, data.materialInfo.subMaterialId);
            }
            return;
        }
        // 从挑战页面进来带上topicId
        if (mTopicSourceInfo != null && DubbingInfoHelp.isNewTopicSourceType(mTopicSourceInfo.getSourceType())) {
            params.put("topicId", String.valueOf(mTopicSourceInfo.getTopicId()));
        }
        if (canUpdateUi() && getRealVisableHint()) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }

        MainCommonRequest.getDubShowInfo(trackId, params, new IDataCallBack<DubShowModel>() {
            @Override
            public void onSuccess(@Nullable final DubShowModel dubModel) {
                if (requestId != trackId) {
                    return;
                }
                onPageLoadingCompleted(LoadCompleteType.OK);

                isLoadingData = false;
                if (dubModel == null || dubModel.trackInfo == null) {
                    if (!(isFromDialog && mData != null)) {
                        onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                    } else {
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_net_error);
                    }
                    return;
                }

                if (dubModel.materialInfo != null) {
                    isTemplate = dubModel.trackInfo.getDataId() == dubModel.materialInfo.demoTrackId;
                }

                hasInnerSubtitle = dubModel.trackInfo.isHasSubtitle();

                // 转码中
                if (dubModel.trackInfo.getProcessState() != 2) {
                    onCodingOnSuccess(isFromDialog);
                    return;
                } else {
                    if (mNoSuccessVideoView != null) {
                        mNoSuccessVideoView.setVisibility(View.INVISIBLE);
                    }
                }
                // 缓存数据
                getRequestCache().put(trackId, dubModel);
                setDataToView(dubModel, isFromDialog);
                if (dubModel.materialInfo != null && dubModel.materialInfo.subMaterialId > 0) {
                    loadDubShowCoopData(trackId, dubModel.materialInfo.subMaterialId);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (requestId != trackId) {
                    return;
                }
                if (!(isFromDialog && mData != null)) {
                    onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                } else {
                    CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_net_error);
                    onPageLoadingCompleted(LoadCompleteType.OK);
                }

                isLoadingData = false;
                if (getRealVisableHint()) {
                    CustomToast.showFailToast(message);
                } else {
                    mErrorInfo = message;
                }
            }
        });
    }

    private void loadDubShowCoopData(final long trackId, long materialId) {
        Map<String, String> params = new HashMap<>();
        params.put("templateId", String.valueOf(materialId));
        params.put("trackId", String.valueOf(trackId));
        MainCommonRequest.getDubShowCoopInfo(params, new IDataCallBack<DubCoopData>() {
            @Override
            public void onSuccess(@Nullable DubCoopData object) {
                // 如果返回为空或者showFloat为false，则不存储配音信息到缓存
                if (null == object || !object.isShowFloat()) {
                    return;
                }
                DubShowModel dubModel = getRequestCache().get(trackId);
                if (dubModel != null) {
                    dubModel.setDubCoopData(object);
                }
                setDataToCooperationView(object.getActors());
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    @Override
    public void onPageLoadingCompleted(LoadCompleteType loadCompleteType) {
        super.onPageLoadingCompleted(loadCompleteType);
        if (mNoSuccessVideoView != null) {
            mNoSuccessVideoView.setVisibility(View.INVISIBLE);
        }
        boolean hasSuccessfullyLoaded = (loadCompleteType == LoadCompleteType.OK);
        int visibility = hasSuccessfullyLoaded ? View.VISIBLE : View.INVISIBLE;
        if (mBottomLay != null) {
            mBottomLay.setVisibility(visibility);
        }
        if (mGotoDubbing != null) {
            if (visibility == View.VISIBLE) {
                if (mData != null && mData.materialInfo != null && mData.materialInfo.canDub) {
                    mGotoDubbing.setVisibility(visibility);
                }
            } else {
                mGotoDubbing.setVisibility(visibility);
            }
        }
        if (mZoom != null) {
            mZoom.setVisibility(visibility);
        }
    }

    @Override
    protected void loadingState() {
        super.loadingState();

        View viewById = findViewById(R.id.main_lottie_progress);
        if (viewById instanceof LottieAnimationView) {
            ((LottieAnimationView) viewById).playAnimation();
        }
    }

    // 当转码成功的时候
    private void onCodingOnSuccess(boolean isFromDialog) {
        if (mNoSuccessVideoStub != null && mNoSuccessVideoView == null) {
            mNoSuccessVideoView = mNoSuccessVideoStub.inflate();
        }
        if (mNoSuccessVideoView != null) {
            mNoSuccessVideoView.setVisibility(View.VISIBLE);
            if (isFromDialog) {
                mNoSuccessVideoView.setOnClickListener(null);
            } else {
                mNoSuccessVideoView.setOnClickListener(v -> loadDataOnInit(requestId));
            }
            AutoTraceHelper.bindDataCallback(mNoSuccessVideoView, mDataProvider);
        }
    }

    @Override
    public void loadData() {
        loadDataOnInit(mTrackId);
    }

    @Override
    public void setHintBuy(boolean fromAdUnLock) {

    }

    private void setDataToView(final DubShowModel data, final boolean isFromDialog) {
        mData = data;
        if (data == null) {
            return;
        }

        if (data.communityInfo != null) {
            mTvCommunityRelated.setText("来自社团：" + data.communityInfo.name);
            mTvCommunityRelated.setVisibility(View.VISIBLE);
        } else {
            mTvCommunityRelated.setVisibility(View.GONE);
        }

        if (data.trackInfo != null) {
            hasInnerSubtitle = data.trackInfo.isHasSubtitle();

            if (hasInnerSubtitle) {
                mLyrView.setVisibility(View.INVISIBLE);
                mLyrViewAvatar.setVisibility(View.INVISIBLE);
                mLyrViewHorizontal.setVisibility(View.INVISIBLE);
                mLyrViewHorizontalAvatar.setVisibility(View.INVISIBLE);
            }
        }

        mDemoTrackId = 0;
        if (data.materialInfo != null) {
            mDemoTrackId = data.materialInfo.demoTrackId;
        }

        if (data.trackInfo != null && data.trackInfo.getDataId() == mTrackId) {
            mCurPageData = data;
        }

        mDubInfoViewState = DubbingInfoHelp.getDubInfoViewState(data.topicInfo, data.themeInfo, mChallengeInfoModel);
        if (mUserInfoCallBack != null) {
            mUserInfoCallBack.onUserInfoCallBack(data.userInfo);
        }

        if (data.richInfo != null) {
            mLyrics = data.richInfo.lyricTimeline;
        }

        if (data.userInfo != null) {
            mUserIsAttention.put(data.userInfo.getUid(), data.userInfo.isFollowed());
        }

        if (data.trackInfo != null) {
            // 传递给父类
            mCurTrack = data.trackInfo;

            boolean video = data.trackInfo.isVideo() || ALL_IS_VIDEO;
            if (!video && data.richInfo != null && !ToolUtil.isEmptyCollects(data.richInfo.insetTimeline)) {
                if (mPPTPlayController == null) {
                    mPPTPlayController = new PPTPlayController(mContext, mIvPpt0, mIvPpt1, this);
                }
                mPPTPlayController.setData(data);

                if (getRealVisableHint() && !isPaused) {
                    // 播放
                    setTrackPlayData(data.trackInfo);
                }
            } else if (video) {
                if (mVideoPlayController == null) {
                    mVideoPlayController = new VideoPlayController(mContext, mVideoPlayerContainer, this);
                }
                mVideoPlayController.setDefualtImg(data.trackInfo.getCoverUrlLarge());

                final DubShowModel dubShowModel = getRequestCache().get(data.trackInfo.getDataId());
                if (dubShowModel != null && dubShowModel.videoPlayInfo != null
                    && dubShowModel.videoPlayInfo.length > 1) {
                    mVideoPlayInfo = dubShowModel.videoPlayInfo;
                    if (canUpdateUi()) {
                        printLog("VideoPlayController  setVideoPlayData 2 === " + getRealVisableHint() + "   "
                            + mVideoPlayInfo[0]);
                        setVideoPlayData(data.trackInfo, dubShowModel.videoPlayInfo,
                            getRealVisableHint() && !isPaused, dubShowModel);
                    }
                } else {
                    CommonRequestM.getVideoBaseInfo(data.trackInfo.getDataId(), new IDataCallBack<String[]>() {
                        @Override
                        public void onSuccess(@Nullable String[] object) {
                            if (dubShowModel != null) {
                                dubShowModel.videoPlayInfo = object;
                            }
                            if (mData != null) {
                                mData.videoPlayInfo = object;
                            }

                            doAfterAnimation(() -> {
                                if (object == null || object.length < 2 || !canUpdateUi()) {
                                    if (isFromDialog && mVideoPlayController != null
                                            && !TextUtils.isEmpty(mVideoPlayController.getPlayUrl())) {
                                        CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_net_error);
                                        onPageLoadingCompleted(LoadCompleteType.OK);
                                    } else {
                                        onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                                    }
                                    return;
                                }
                                onPageLoadingCompleted(LoadCompleteType.OK);
                                mVideoPlayInfo = object;
                                printLog("VideoPlayController  setVideoPlayData === " + getRealVisableHint() + "   "
                                        + object[0]);
                                setVideoPlayData(data.trackInfo, object,
                                        getRealVisableHint() && !isPaused, dubShowModel);
                            });
                        }

                        @Override
                        public void onError(int code, String message) {
                            if (!(isFromDialog && mVideoPlayController != null
                                    && !TextUtils.isEmpty(mVideoPlayController.getPlayUrl()))) {
                                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            } else {
                                CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_net_error);
                                onPageLoadingCompleted(LoadCompleteType.OK);
                            }

                            if (getRealVisableHint()) {
                                if (!TextUtils.isEmpty(message)) {
                                    CustomToast.showFailToast(message);
                                }
                            } else {
                                mErrorInfo = message;
                            }
                        }
                    });
                }
            }
        }

        setPlayInfoData(data);

        checkOpenComment();

    }

    private void setDataToCooperationView(List<DubCoopActorData> actors) {
        mDubCooperationView.setDataForView(actors, new DubCooperationView.ICoopRoleJumpListener() {
            @Override
            public void onRoleJump(DubCoopActorData dubCoopActorData) {
                if (mDubbingInfoHelp != null) {
                    mDubbingInfoHelp.prepareGoToDubbing(dubCoopActorData);
                }
            }

            @Override
            public void onCoopViewDismiss() {
                mDubCooperationView.invisible();
                beginShowInfoView();
            }
        });
    }

    private void checkOpenComment() {
        doAfterAnimation(() -> {
            boolean openComment = canUpdateUi() && mData != null && mOpenComment && mComment != null;
            if (openComment) {
                mComment.postDelayed(mOpenCommentRunnable, 500);
                mOpenComment = false;
            }
        });
    }

    private void setAnchorState(DubShowModel data) {
        if (data != null && data.userInfo != null) {
            if (UserInfoMannage.getUid() == data.userInfo.getUid()) {
                mAnchorState.setVisibility(View.GONE);
            } else {
                if (mUserIsAttention.indexOfKey(data.userInfo.getUid()) >= 0) {
                    Boolean aBoolean = mUserIsAttention.get(data.userInfo.getUid());
                    if (aBoolean == null) {
                        aBoolean = false;
                    }
                    mAnchorState.setVisibility(aBoolean ? View.GONE : View.VISIBLE);
                } else {
                    mAnchorState.setVisibility(data.userInfo.isFollowed() ? View.GONE : View.VISIBLE);
                }
            }
        }
    }

    private boolean isFollowed(DubShowModel data) {
        if (data != null && data.userInfo != null) {
            if (mUserIsAttention.indexOfKey(data.userInfo.getUid()) >= 0) {
                Boolean aBoolean = mUserIsAttention.get(data.userInfo.getUid());
                if (aBoolean == null) {
                    aBoolean = false;
                }
                return aBoolean;
            } else {
                return data.userInfo.isFollowed();
            }
        }
        return false;
    }

    private void setPlayInfoData(DubShowModel data) {
        if (data == null) {
            return;
        }
        // 以服务端返回的挑战话题数据优先为准
        ChallengeInfoModel actualTopicInfo = (data.topicInfo != null) ? data.topicInfo : mChallengeInfoModel;
        // 主播信息
        if (data.userInfo != null) {
            mAnchorNickName = data.userInfo.getNickName();
            mAnchorUserIcon = data.userInfo.getLogo();
            ImageManager.from(mContext).displayImage(mAnchorIcon, mAnchorUserIcon, R.drawable.main_dubbing_pic_avatar);
            mAnchorName.setText(mAnchorNickName);
            mStateLay.setVisibility(View.VISIBLE);

            if (data.userInfo.getUid() == UserInfoMannage.getUid()) {
                mAnchorState.setVisibility(View.GONE);
            } else {
                setAnchorState(data);
            }

            if (mUserInfoCallBack != null) {
                mUserInfoCallBack.onUserInfoCallBack(data.userInfo);
            }
        } else {
            mStateLay.setVisibility(View.INVISIBLE);
        }
        // 方言显示
        if (data.trackInfo != null) {
            List<DubDialectLabel> dialectLabels = data.trackInfo.getLabels();
            if (!ToolUtil.isEmptyCollects(dialectLabels)) {
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < dialectLabels.size(); i++) {
                    if (i == dialectLabels.size() - 1) {
                        stringBuilder.append(dialectLabels.get(i).getName());
                    } else {
                        stringBuilder.append(dialectLabels.get(i).getName()).append(" · ");
                    }
                }
                mDialectTextView.setText(stringBuilder.toString());
                mDialectTextView.setVisibility(View.VISIBLE);
            }
        }
        // 旧话题 或 无话题
        if (mDubInfoViewState == DubbingInfoHelp.DUB_OLD_TOPIC_STATE
            || mDubInfoViewState == DubbingInfoHelp.DUB_NO_TOPIC_STATE) {
            if (actualTopicInfo != null && !TextUtils.isEmpty(actualTopicInfo.getName())) {
                mChallenge.setVisibility(View.VISIBLE);
                mChallenge.setOnClickListener(this);
                AutoTraceHelper.bindDataCallback(mChallenge, mDataProvider);
                mChallenge.setText(actualTopicInfo.getName());
            } else {
                mChallenge.setVisibility(View.GONE);
            }
        }
        // title
        if (mDubInfoViewState == DubbingInfoHelp.DUB_OLD_TOPIC_STATE
            || mDubInfoViewState == DubbingInfoHelp.DUB_NO_TOPIC_STATE) {
            mTitle.setVisibility(View.VISIBLE);
            mTopicLayout.setVisibility(View.GONE);
            boolean isDeal = data.trackInfo != null && (!TextUtils.isEmpty(data.trackInfo.getTrackTitle())
                || !TextUtils.isEmpty(data.trackInfo.getTrackIntro()));
            String titleText = "";
            if (isDeal) {
                if (!TextUtils.isEmpty(data.trackInfo.getTrackIntro())) {
                    titleText = data.trackInfo.getTrackIntro();
                } else if (!TextUtils.isEmpty(data.trackInfo.getTrackTitle())) {
                    titleText = data.trackInfo.getTrackTitle();
                }
            }
            SpannableString ss = new SpannableString(Html.fromHtml(titleText) + "   ");
            addReadHallImageSpan2End(ss);
            mTitle.setText(ss);
        } else if (DubbingInfoHelp.isNewTopicState(mDubInfoViewState)) {
            ViewStatusUtil.setVisible(View.GONE, mTitle, mChallenge);
            mTopicLayout.setVisibility(View.VISIBLE);
            boolean isTopicWorkNameExist = data.trackInfo != null && !TextUtils.isEmpty(data.trackInfo.getTrackIntro());
            String workNameText = isTopicWorkNameExist ? data.trackInfo.getTrackIntro() : "";
            String text = "   " + workNameText;
            boolean isTopicNameExist = actualTopicInfo != null && !TextUtils.isEmpty(actualTopicInfo.getName());
            if (isTopicNameExist) {
                text = "   " + actualTopicInfo.getName() + " · " + workNameText;
            }
            SpannableString ss = getSpannableString(text, com.ximalaya.ting.android.host.R.drawable.host_ic_topic_title_pk_tag);
            addReadHallImageSpan2End(ss);
            mTopicNamePkView.setText(ss);
        } else if (mDubInfoViewState == DubbingInfoHelp.DUB_THEME_TOPIC_STATE) {
            ViewStatusUtil.setVisible(View.VISIBLE, mTopicLayout);
            ViewStatusUtil.setVisible(View.GONE, mTitle, mChallenge);
            boolean isTopicNameExist = data.themeInfo != null && !TextUtils.isEmpty(data.themeInfo.getName());
            boolean isTopicWorkNameExist = data.trackInfo != null && !TextUtils.isEmpty(data.trackInfo.getTrackIntro());
            String workNameText = isTopicWorkNameExist ? data.trackInfo.getTrackIntro() : "";
            String text = "   " + workNameText;
            if (isTopicNameExist) {
                text = "   " + data.themeInfo.getName() + " · " + workNameText;
            }
            SpannableString ss = getSpannableString(text, com.ximalaya.ting.android.host.R.drawable.host_ic_topic_title_tag);
            addReadHallImageSpan2End(ss);
            mTopicNamePkView.setText(ss);
        }
        // 底部信息
        if (data.trackInfo != null) {
            String favoriteCount = hindOver999(data.trackInfo.getFavoriteCount());
            mLike.setContentDescription("喜欢" + favoriteCount);
            mLike.setText(favoriteCount);
            mLike.setSelected(data.trackInfo.isLike());
            String commentCount = hindOver999(data.trackInfo.getCommentCount());
            mComment.setText(commentCount);
            mComment.setContentDescription("评论" + commentCount);
            String shareCount = hindOver999(data.trackInfo.getSharesCounts());
            mShare.setText(shareCount);
            mShare.setContentDescription("分享" + shareCount);
        } else {
            mLike.setText("");
            mLike.setSelected(false);
            mComment.setText("");
            mShare.setText("");
        }
        if (DubbingInfoHelp.isNewTopicState(mDubInfoViewState) &&
            (mTopicSourceInfo != null) &&
            (DubbingInfoHelp.isNewTopicSourceType(mTopicSourceInfo.getSourceType()))) {
            // 只有从PK相关页面跳转进来的PK话题才显示PK
            mGotoDubbing.setImageResource(R.drawable.main_ic_topic_pkplay);
        } else if (mTemplateVideoId > 0L) {
            mGotoDubbing.setImageResource(R.drawable.main_dubbing_recording_cooperate);
        } else {
            mGotoDubbing.setImageResource(R.drawable.main_dubbing_recording);
        }

        if (data.materialInfo != null && data.materialInfo.trackTotalCount > 0) {
            String text;
            if (DubbingInfoHelp.isNewTopicState(mDubInfoViewState) &&
                (mTopicSourceInfo != null) &&
                (DubbingInfoHelp.isNewTopicSourceType(mTopicSourceInfo.getSourceType()))) {
                text = "已有" + StringUtil.getFriendlyNumStr(data.materialInfo.trackTotalCount) + "人参与PK";
            } else {
                text = StringUtil.getFriendlyNumStr(data.materialInfo.trackTotalCount) + "人都在配";
            }
            mMateTitle.setText(text);
            if (!ToolUtil.isEmptyCollects(data.materialInfo.materialWorks)) {
                for (ImageView icon : mMateIcons) {
                    icon.setVisibility(View.GONE);
                }
                for (int i = 0; i < data.materialInfo.materialWorks.size(); i++) {
                    if (i >= mMateIcons.length) {
                        break;
                    }
                    ImageView mateIcon = mMateIcons[i];
                    ImageManager.from(mContext).displayImage(mateIcon, data.materialInfo.materialWorks.get(i), R.drawable.main_dubbing_pic_avatar);
                    mateIcon.setVisibility(View.VISIBLE);
                }
                mMateIconLay.setVisibility(View.VISIBLE);
            } else {
                mMateIconLay.setVisibility(View.GONE);
            }
        } else {
            for (ImageView mateIcon : mMateIcons) {
                mateIcon.setVisibility(View.GONE);
            }
            mMateIcons[0].setImageResource(R.drawable.main_dubbing_pic_avatar);
            mMateIcons[0].setVisibility(View.VISIBLE);
            if (DubbingInfoHelp.isNewTopicState(mDubInfoViewState) && (mTopicSourceInfo != null)
                && (DubbingInfoHelp.isNewTopicSourceType(mTopicSourceInfo.getSourceType()))) {
                mMateTitle.setText("等你来PK哟");
            } else {
                mMateTitle.setText("等你来配哟");
            }
        }

        if (mTemplateVideoId > 0) {
            mMateLay.setVisibility(View.GONE);
        } else {
            mMateLay.setVisibility(View.VISIBLE);
        }

        if (data.materialInfo != null && data.materialInfo.canDub && data.materialInfo.materialId > 0) {
            mMateLay.setVisibility(View.VISIBLE);
            if (!(!isVertical() && isHiddendInfoView())) {
                mGotoDubbing.setVisibility(View.VISIBLE);
            }
        } else {
            mMateLay.setVisibility(View.GONE);
            mGotoDubbing.setVisibility(View.GONE);
        }

        if (data.adResult != null && !ToolUtil.isEmptyCollects(data.adResult.adInfos) && !isClosedAd()) {
            initDubAdView();
            mDubbAdPageView.setData(data.adResult.adInfos);

            recordAdShow();
        } else {
            removeDubbView();
        }
    }

    private void addReadHallImageSpan2End(SpannableString ss) {
        if (mData != null && mData.trackInfo != null
            && mData.trackInfo.getWorkType() == AppConstants.WORK_TYPE_READ_HALL) {
            int len = ss.length();
            Drawable d = ContextCompat.getDrawable(mContext, com.ximalaya.ting.android.host.R.drawable.host_ic_read_hall);
            if (d != null) {
                d.setBounds(0, 0, d.getIntrinsicWidth(), d.getIntrinsicHeight());
                CenterAlignImageSpan span = new CenterAlignImageSpan(d);
                ss.setSpan(span, len - 1, len, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
    }

    private void removeDubbView() {
        if (mDubbAdPageView != null && mDubbAdPageView.getParent() != null) {
            ((ViewGroup) mDubbAdPageView.getParent()).removeView(mDubbAdPageView);
            mDubbAdPageView = null;
        }
    }

    private void recordAdShow() {
        if (mData != null && mData.adResult != null && !ToolUtil.isEmptyCollects(mData.adResult.adInfos)
            && getRealVisableHint()) {
            mAdPosition = mAdPosition % mData.adResult.adInfos.size();
            requestUserTracking(mData.adResult.adInfos.get(mAdPosition).getAdId());
        }
    }

    private void initDubAdView() {
        if (mAdLay == null) {
            return;
        }
        if (mDubbAdPageView == null) {
            mDubbAdPageView = new DubbAdPageView(mContext);
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
            mDubbAdPageView.setLayoutParams(params);
            mAdLay.addView(mDubbAdPageView);
            mDubbAdPageView.setCloseHandle(() -> setClosedAded(true));
            mDubbAdPageView.setPageChangeListener(position -> {
                if (mData != null && mData.adResult != null && !ToolUtil.isEmptyCollects(mData.adResult.adInfos)) {
                    position = position % mData.adResult.adInfos.size();
                    if (position != mAdPosition && getRealVisableHint()) {
                        // 可见的那个播放页面才上报
                        requestUserTracking(mData.adResult.adInfos.get(position).getAdId());
                    }
                    mAdPosition = position;
                }
            });
            mDubbAdPageView.setItemClick(dubAdInfo -> {
                if (dubAdInfo != null && !TextUtils.isEmpty(dubAdInfo.getAdLink())) {
                    ToolUtil.clickUrlAction(DubbingInfoFragment.this, dubAdInfo.getAdLink(), null);

                    new UserTracking()
                        .setSrcPage("dub")
                        .setSrcModule("推广位")
                        .setItem("page")
                        .setItemId(dubAdInfo.getAdLink())
                        .setDubId(mCurPlayTrackId)
                        .setId("5173")
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
                }
            });
        }
    }

    private void requestUserTracking(int adId) {
        new UserTracking()
            .setId("5172")
            .setModuleType("推广位")
            .setSrcPage("dub")
            .setDubId(mCurPlayTrackId)
            .setAdid(adId)
            .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
    }

    private SpannableString getSpannableString(String text, int drawableId) {
        SpannableString spannableString = new SpannableString(text);
        Drawable drawable = ContextCompat.getDrawable(mContext, drawableId);
        if (drawable != null) {
            drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
            spannableString.setSpan(new VerticalImageSpan(drawable), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        return spannableString;
    }

    private String hindOver999(int num) {
        if (num > 999) {
            return "999+";
        }

        return num + "";
    }

    @Override
    protected View getNoContentView() {
        return View.inflate(getActivity(), R.layout.main_dubbing_network_error, null);
    }

    @Override
    protected View getLoadingView() {
        return View.inflate(getActivity(), R.layout.main_dubbing_loading, null);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_dubbing_info;
    }

    @Override
    protected boolean isPageBgDark() {
        return true;
    }

    @Override
    public void onParentFragmentUserHint(boolean isVisibleToUser) {
        if (isResumed()) {
            printLog("VideoPlayController .onParentFragmentUserHint  " + isVisibleToUser + "    "
                + System.currentTimeMillis());

            if (isVisibleToUser) {
                isPaused = false;

                onPageWillShow(true);

                if (!TextUtils.isEmpty(mErrorInfo)) {
                    CustomToast.showFailToast(mErrorInfo);
                    mErrorInfo = null;
                }

                recordAdShow();
            } else {
                hasCallShowed = false;

                statePlayStatistics();

                if (mVideoPlayController != null) {
                    mVideoPlayController.onUserHint();
                }
                if (mPPTPlayController != null) {
                    mPPTPlayController.onUserHint();
                }

                ShareResultManager.getInstance().clearShareFinishListener();
            }
        }
    }

    public void onPageWillShow(boolean checkIsVisable) {
        if (!hasCallShowed) {
            printLog("VideoPlayController .onPageWillShow  " + checkIsVisable + "    " + System.currentTimeMillis());
            if (mData != null && mData.trackInfo != null) {
                if (mData.trackInfo.getDataId() != getCurTrackId()) {
                    loadDataOnInit(getCurTrackId());
                } else {
                    setAnchorState(mData);
                    updateLikeState();

                    if (!mData.trackInfo.isVideo() && !ALL_IS_VIDEO) {
                        setTrackPlayData(mData.trackInfo);
                        hasCallShowed = true;
                    } else {
                        if (mVideoPlayInfo != null) {
                            boolean willStart = !checkIsVisable || getRealVisableHint();
                            if (willStart) {
                                hasCallShowed = true;
                            }
                            printLog(
                                "VideoPlayController  setVideoPlayData 3 === " + willStart + "   " + mVideoPlayInfo[0]);
                            setVideoPlayData(mData.trackInfo, mVideoPlayInfo, willStart, mData);
                        }
                    }
                }
            }

            // 如果有没有成功的videoView
            if (mNoSuccessVideoView != null && mNoSuccessVideoView.getVisibility() == View.VISIBLE) {
                loadData();
            }
        }

    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isResumed()) {
            if (!isVisibleToUser) {
                if (mVideoPlayController != null) {
                    mVideoPlayController.onFragmentPause();
                }
                if (mPPTPlayController != null) {
                    mPPTPlayController.onFragmentPause();
                }
            } else {
                isPaused = false;

                if (mData != null) {
                    setAnchorState(mData);
                }
                updateLikeState();

                if (mVideoPlayController != null) {
                    mVideoPlayController.onFragmentResume(true);
                }
                if (mPPTPlayController != null) {
                    mPPTPlayController.onFragmentResume();
                }

                recordAdShow();
            }
        }
    }

    public void setUserInfoCallBack(IOnUserInfoCallBack userInfoCallBack) {
        mUserInfoCallBack = userInfoCallBack;
    }

    public void setTopicSourceInfo(TopicSourceInfo topicSourceInfo) {
        mTopicSourceInfo = topicSourceInfo;
    }

    public ChallengeInfoModel getChallengeInfo() {
        return mChallengeInfoModel;
    }

    public void setChallengeInfo(ChallengeInfoModel challengeInfoModel) {
        mChallengeInfoModel = challengeInfoModel;
    }

    public long getTemplateVideoId() {
        return getCooperateTemplateIdFromParent(mTrackId);
    }

    public void resetCooperationViewShowState() {
        mHasShownCooperationView = false;
        if (mDubCooperationView != null) {
            mDubCooperationView.invisible();
        }
        // 说明mInfoLay未复位
        if (mInfoLay != null && mInfoLay.getTranslationX() != 0) {
            mInfoLay.setVisibility(View.VISIBLE);
            mInfoLay.setTranslationX(0);
        }
        // 说明mDubCooperationView未复位
        if (mDubCooperationView != null && mDubCooperationView.getTranslationY() != 0) {
            mDubCooperationView.setTranslationY(0);
        }
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        isPaused = false;

        setAnchorState(mData);

        if (mData != null && mLike != null) {
            DubPlayParams dubPlayParams = getDubPlayParams(mTrackId);
            if (dubPlayParams != null) {
                mLike.setText(hindOver999((int) dubPlayParams.likeCount));
                mLike.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable
                    .main_dubbing_like_state_new), null, null, null);
                mLike.setSelected(dubPlayParams.isLiked);
                if (mData.trackInfo != null && mData.trackInfo.isLike() != dubPlayParams.isLiked) {
                    mData.trackInfo.setLike(dubPlayParams.isLiked);
                    mData.trackInfo.setFavoriteCount((int) dubPlayParams.likeCount);
                }
            } else {
                updateLikeState();
            }
        }

        if (mVideoPlayController != null) {
            mVideoPlayController.onFragmentResume(false);
        }
//        if (mPPTPlayController != null) {
//            mPPTPlayController.onFragmentResume();
//        }

        checkOpenComment();

        if (mDynamicPlayRecord != null && lastScreenPauseTime != 0) {
            mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_ADD_SCREEN_OFF_DURATION,
                System.currentTimeMillis() - lastScreenPauseTime);
            lastScreenPauseTime = 0;
        }
    }

    private void updateLikeState() {
        DubLikeModel dubLikeModel = mDubbIsLiked.get(mCurPlayTrackId);
        if (dubLikeModel != null) {
            if (mData != null && mData.trackInfo != null && mData.trackInfo.getDataId() == mCurPlayTrackId) {
                mData.trackInfo.setLike(dubLikeModel.isLiked);
                mData.trackInfo.setFavoriteCount(dubLikeModel.likeCount);
            }

            mLike.setText(hindOver999(dubLikeModel.likeCount));
            mLike.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable
                .main_dubbing_like_state_new), null, null, null);
            mLike.setSelected(dubLikeModel.isLiked);
        }
    }

    @Override
    public void onPause() {
        super.onPause();

        isPaused = true;
        if (mVideoPlayController != null) {
            mVideoPlayController.onFragmentPause();
        }
        if (mPPTPlayController != null) {
            mPPTPlayController.onFragmentPause();
        }

        lastScreenPauseTime = System.currentTimeMillis();

        Logger.d("lhg", "onPause current state:" + XmPlayerManager.getInstance(getContext()).isPlaying());
    }

    @Override
    public void onDestroy() {
        if (mComment != null) {
            mComment.removeCallbacks(mOpenCommentRunnable);
        }
        mHasShownCooperationView = false;
        super.onDestroy();
    }

    private void setVideoPlayData(TrackM trackInfo, String[] videoPlayInfo, boolean willStart, DubShowModel dubShowModel) {
        if (trackInfo == null || videoPlayInfo == null || videoPlayInfo.length < 2) {
            return;
        }
        if (mVideoPlayController != null) {
            if (trackInfo != null) {
                setCurPlayTrackId(trackInfo.getDataId());
            }

            if (willStart) {
                new UserTracking()
                    .setDubId(trackInfo.getDataId())
                    .setId(3099).setIsTeamDub(mTemplateVideoId > 0)
                    .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_VIEW);
            }

            videoPlayUrl = videoPlayInfo[0];
            mVideoPlayController.setVideoSource(videoPlayInfo[0], videoPlayInfo[1], willStart, dubShowModel);
        }
    }

    @Override
    public void setTrackPlayData() {
        if (mData != null && mData.trackInfo != null) {
            if (mPPTPlayController != null) {
                setTrackPlayData(mData.trackInfo);
            }
        }
    }

    @Override
    public DubPlayParams getDubPlayParams(long dataId) {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            parentFragment = parentFragment.getParentFragment();
            if (parentFragment instanceof DubbingPlayFragmentNew) {
                return ((DubbingPlayFragmentNew) parentFragment).getDubPlayParamsByTrackId(dataId);
            }
        }
        return null;
    }

    @Override
    public void onRenderingStart(long renderingSpentMilliSec) {
        if (mPlayRecord != null) {
            mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_LOADING_TIME, renderingSpentMilliSec);
        }
    }

    private void setTrackPlayData(TrackM trackM) {
        if (trackM != null) {
            setCurPlayTrackId(trackM.getDataId());

            PlayTools.playList(mContext, Collections.singletonList(trackM), 0, null);
            XmPlayerManager.getInstance(mContext).setVolume(1.0f, 1.0f);
            if (mPPTPlayController != null) {
                mPPTPlayController.addPlayStatueListener();
            }

            new UserTracking()
                .setDubId(trackM.getDataId())
                .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_VIEW);
        }
    }

    private void setCurPlayTrackId(long trackId) {
        mCurPlayTrackId = trackId;
        if (mPageIdChanged != null && getRealVisableHint()) {
            mPageIdChanged.onPageTrackIdChange(trackId);
        }
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        // 以服务端返回的挑战话题数据优先为准
        ChallengeInfoModel actualTopicInfo =
            (mData != null && mData.topicInfo != null) ? mData.topicInfo : mChallengeInfoModel;
        int i = v.getId();
        if (i == R.id.main_back) {
            if (mCloseCallBack != null) {
                mCloseCallBack.onReady();
            }

            new UserTracking()
                .setDubId(mCurPlayTrackId)
                .setItem("button")
                .setItemId("返回")
                .setSrcModule("roofTool")
                .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
        }

        // 跳转主播信息
        else if (i == R.id.main_anchor_icon || i == R.id.main_anchor_name) {
            // 如果是从主播页过来的不进行跳转
            if (mIsFromUserInfo) {
                return;
            }

            if (mData != null && mData.userInfo != null) {
                new UserTracking()
                    .setDubId(mCurPlayTrackId)
                    .setItem("dubUser")
                    .setItemId(mData.userInfo.getUid())
                    .setSrcModule("上传者头像")
                    .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
                if (getParentFragment() instanceof DubbingGroupFragment) {
                    ((DubbingGroupFragment) getParentFragment()).selectDubbingUserInfoFragment();
                } else {
                    startFragment(DubbingUserInfoFragment.newInstance(mData.userInfo.getUid()), v);
                }
            }
        }

        // 关注
        else if (i == R.id.main_anchor_state) {
            if (mData != null && mData.userInfo != null) {
                new UserTracking(7190, "dub", "button")
                    .setDubId(mCurPlayTrackId)
                    .setSrcModule("roofTool")
                    .setItemId(mData.userInfo.isFollowed() ? "unfollow" : "follow")
                    .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
                AnchorFollowManage.followV3(mActivity, mData.userInfo.getUid(),
                    isFollowed(mData),
                    AnchorFollowManage.FOLLOW_BIZ_TYPE_DUBBIN_PLAY, AnchorFollowManage.FOLLOW_BIZ_TYPE_DUBBIN_PLAY_SUB,
                    new IDataCallBack<Boolean>() {
                        @Override
                        public void onSuccess(Boolean object) {
                            if (!canUpdateUi()) {
                                return;
                            }
                            if (object != null) {
                                mData.userInfo.setFollowed(object);
                                mUserIsAttention.put(mData.userInfo.getUid(), object);
                                mAnchorState.setVisibility(object ? View.GONE : View.VISIBLE);
                                if (object) {
                                    CustomToast.showSuccessToast("关注成功");
                                }
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                        }
                    }, true);
            }
        }

        // 喜欢
        else if (i == R.id.main_like) {
            if (mData != null && mData.trackInfo != null) {
                if (UserInfoMannage.hasLogined()) {
                    new UserTracking()
                        .setDubId(mCurPlayTrackId)
                        .setItem("button")
                        .setItemId(mData.trackInfo.isLike() ? "取消赞" : "点赞")
                        .setSrcModule("互动区域")
                        .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);

                    likeingIsLoading = true;

                    LikeTrackManage.toLikeOrUnLike(mData.trackInfo, null, getActivity(), new IDataCallBack<Boolean>() {
                        @Override
                        public void onSuccess(@Nullable Boolean object) {
                            likeingIsLoading = false;
                            if (object == null) {
                                return;
                            }

                            mData.trackInfo.setLike(!mData.trackInfo.isLike());
                            int favoriteCount = mData.trackInfo.getFavoriteCount();
                            if (mData.trackInfo.isLike()) {
                                favoriteCount++;
                            } else {
                                favoriteCount--;
                            }
                            if (favoriteCount < 0) {
                                favoriteCount = 0;
                            }
                            mData.trackInfo.setFavoriteCount(favoriteCount);
                            DubPlayParams playParams = getDubPlayParams(mTrackId);
                            if (playParams != null) {
                                playParams.isLiked = mData.trackInfo.isLike();
                                playParams.likeCount = favoriteCount;
                            }
                            mDubbIsLiked.put(mData.trackInfo.getDataId(), new DubLikeModel(mData.trackInfo.isLike(), mData.trackInfo.getFavoriteCount()));
                            if (canUpdateUi()) {
                                if (mData.trackInfo.isLike()) {
                                    addLikeAnimation();
                                } else {
                                    resetLastBottomAnimation();
                                }
                                mLike.setSelected(mData.trackInfo.isLike());
                                sendLikeActionBroadCast(mData.trackInfo.isLike(), mData.trackInfo.getFavoriteCount());
                                mLike.setText(hindOver999(favoriteCount));
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            likeingIsLoading = false;
                            CustomToast.showFailToast(message);
                        }
                    });
                } else {
                    UserInfoMannage.gotoLogin(getActivity(), LoginByConstants.LOGIN_BY_LIKE);
                }
            }
        }

        // 评论
        else if (i == R.id.main_comment) {
            if (mData != null && mData.trackInfo != null) {
                if (mData.trackInfo.getCommentCount() > 0) {
                    mCommentView.init(this);
                } else {
                    onClickQuora();
                }

                new UserTracking()
                    .setDubId(mCurPlayTrackId)
                    .setItem("button")
                    .setItemId("评论")
                    .setSrcModule("互动区域")
                    .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
            }
        }

        // 大家都在配
        else if (i == R.id.main_mate_lay) {
            if (mData != null && mData.materialInfo != null && mData.materialInfo.trackTotalCount > 0) {
                boolean isVideo = false;
                if (mData.trackInfo != null) {
                    isVideo = mData.trackInfo.isVideo();
                }
                DubbingOurPeopleDialog dubbingOurPeopleDialog = null;
                boolean isPKSource = DubbingInfoHelp.isNewTopicState(mDubInfoViewState) &&
                    (mTopicSourceInfo != null) &&
                    (DubbingInfoHelp.isNewTopicSourceType(mTopicSourceInfo.getSourceType()));
                if (isPKSource && (mData != null) && (actualTopicInfo != null)) {
                    dubbingOurPeopleDialog =
                        DubbingOurPeopleDialog.newInstance(mData.materialInfo.materialId, isVideo, getCurPlayingTrackId(),
                            mPeopleDialogTracks, mTopTracks, mPeopleDialogPosition, mPeopleDialogTop, mPeopleDialogPageId,
                            mDemoTrackId, mDubInfoViewState, actualTopicInfo.getTopicId(), mTopicSourceInfo, mPeopleDataPageId);
                } else if (mData != null && mData.materialInfo != null) {
                    dubbingOurPeopleDialog =
                        DubbingOurPeopleDialog.newInstance(mData.materialInfo.materialId, isVideo, getCurPlayingTrackId(),
                            mPeopleDialogTracks, mTopTracks, mPeopleDialogPosition, mPeopleDialogTop,
                            mPeopleDialogPageId, mDemoTrackId, mCurPageData, mPeopleDataPageId);
                }

                if (dubbingOurPeopleDialog == null) {
                    return;
                }

                dubbingOurPeopleDialog.setDubbingPeople(trackId -> {
                    if (trackId > 0) {
                        if (mPPTPlayController != null) {
                            mPPTPlayController.setPlayPause();
                        }

                        if (mVideoPlayController != null) {
                            mVideoPlayController.setPlayPause();
                        }

                        statePlayStatistics();

                        loadDataOnInit(trackId, true);
                    }
                });
                dubbingOurPeopleDialog.show(getFragmentManager(), "DubbingOurPeopleDialog");
                dubbingOurPeopleDialog.setDestroy(new DubbingOurPeopleDialog.IDialogDestroy() {
                    @Override
                    public void getParameter(List<TrackM> trackMList, List<TrackM> topList, int position, int top,
                        int mPageId, int mDataPageId) {
                        mPeopleDialogTracks = trackMList;
                        mTopTracks = topList;
                        mPeopleDialogPosition = position;
                        mPeopleDialogTop = top;
                        mPeopleDialogPageId = mPageId;
                        mPeopleDataPageId = mDataPageId;

                        updateLikeState();
                    }

                    @Override
                    public void onRankInstructionDialogPopup() {
                        openRankInstructionDialog();
                    }
                });
                dubbingOurPeopleDialog.setSlideMoveCallback(mSlideMoveCallback);

                new UserTracking()
                    .setDubId(mCurPlayTrackId)
                    .setItem("button")
                    .setItemId("大家都在配")
                    .setSrcModule("互动区域")
                    .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
            } else if (mData != null) {
                if (mDubbingInfoHelp != null) {
                    mDubbingInfoHelp.prepareGoToDubbing(null);
                }
            }
        }

        // 分享
        else if (i == R.id.main_share) {
            if (mData != null && mData.trackInfo != null) {
                ShareWrapContentModel model = new ShareWrapContentModel(ICustomShareContentType.SHARE_TYPE_TRACK_DUB_NEW);
                model.soundInfo = getCurTrack();
                model.isPictureVideo =
                    mVideoPlayInfo == null || mVideoPlayInfo.length <= 0 || TextUtils.isEmpty(mVideoPlayInfo[0]);
                if (mData.materialInfo != null) {
                    if (null != mData.materialInfo.dubRoles) {
                        model.roleNames = mData.materialInfo.dubRoles;//配音角色名
                    }
                    if (null != mData.materialInfo.ipTag && !TextUtils.isEmpty(mData.materialInfo.ipTag)) {
                        model.ipTagName = mData.materialInfo.ipTag;
                    }
                }
                model.picUrl = mData.trackInfo.getCoverUrlLarge();//封面图
                model.anchorUsername = mAnchorNickName;
                if (mData.userInfo != null) {
                    model.anchorUserAvatar = mData.userInfo.getLogo();
                }
                model.isDubbingInfoPage = true;
                model.trackId = getCurTrackId();
                model.fromPage = "dubInfo";

                ShareUtilsInMain.shareTrack(mActivity, getCurTrack(), model, shareType -> {
                    String shareTypeName = shareType.getEnName();
                    if ("download".equals(shareTypeName)) {
                        startDownloadVideo();
                        return;
                    }
                    if ("tSina".equals(shareType.getEnName())) {
                        shareTypeName = "weibo";
                    }
                    if (ShareConstants.SHARE_TYPE_CREATE_QR_CODE.equals("")) {
                        shareTypeName = "poster";
                    }
                    UserTracking ut = new UserTracking();
                    ut.setDubId(getCurPlayingTrackId());
                    ut.setSrcModule("selectSharePlatform");
                    ut.setItem("button");
                    ut.setItemId(shareTypeName);
                    ut.statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
                });
                ShareResultManager.getInstance().setShareFinishListener(this);

                new UserTracking()
                    .setDubId(mCurPlayTrackId)
                    .setItem("button")
                    .setItemId("分享")
                    .setSrcModule("互动区域")
                    .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
            }
        }

        //新话题
        else if (i == R.id.main_topic_layout) {
            CustomToast.showFailToast(R.string.main_sorry_topic_invalid);
        }

        // 挑战
        else if (i == R.id.main_challenge) {
            CustomToast.showFailToast(R.string.main_sorry_topic_invalid);
            new UserTracking()
                .setDubId(mCurPlayTrackId)
                .setItem("button")
                .setItemId(actualTopicInfo.getName())
                .setSrcModule("挑战入口")
                .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
        }

        // 去录音
        else if (i == R.id.main_goto_dubbing) {
            if (mDubCooperationView != null) {
                mDubCooperationView.invisible();
                mDubCooperationView.setTranslationY(0);
            }
            // 说明mInfoLay未复位
            if (mInfoLay != null && mInfoLay.getTranslationX() != 0) {
                mInfoLay.setVisibility(View.VISIBLE);
                mInfoLay.setTranslationX(0);
            }
            if (mDubInfoViewState == DubbingInfoHelp.DUB_NEW_TOPIC_EXPIRE_STATE &&
                (mTopicSourceInfo != null) &&
                (DubbingInfoHelp.isNewTopicSourceType(mTopicSourceInfo.getSourceType()))) {
                // 已过期的挑战话题就不去录制
                CustomToast.showFailToast("该PK话题活动已结束！");
            } else {
                if (mDubbingInfoHelp != null) {
                    mDubbingInfoHelp.prepareGoToDubbing(null);
                }
            }

            new UserTracking()
                .setDubId(getCurPlayingTrackId())
                .setSrcModule("bottomTool")
                .setItem("button")
                .setItemId("配音入口")
                .setId(3107)
                .setIsTeamDub(mTemplateVideoId > 0)
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
        }

        // 全屏
        else if (i == R.id.main_zoom) {
            setVertical(!isVertical());
            changeScreenDirByUser();

            new UserTracking()
                .setSrcPage("dub")
                .setSrcModule("roofTool")
                .setItem("button")
                .setItemId(isVertical() ? "halfScreen" : "fullScreen")
                .setDubId(mCurPlayTrackId)
                .setId("5249")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
        }

        // 来自社团：xxxxx
        else if (i == R.id.main_community_name_related) {
            if (mData == null || mData.communityInfo == null) {
                return;
            }

            ZoneBundleInterceptKt.afterZoneModuleLoaded(() -> {
                try {
                    BaseFragment fragment = Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE).getFragmentAction().newCommunityHomepageFragment(mData.communityInfo.id);
                    if (fragment != null && getActivity() instanceof MainActivity) {
                        ((MainActivity) getActivity()).startFragment(fragment, "tag_community_home_page", 0, 0);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
    }

    private void openRankInstructionDialog() {
        // 加了延时是因为需要等待页面title动画结束
        HandlerManager.obtainMainHandler().postDelayed(() -> {
            if (canUpdateUi()) {
                DubbingRankInstructionDialog dubbingRankInstructionDialog = new DubbingRankInstructionDialog();
                dubbingRankInstructionDialog.show(getFragmentManager(), "DubbingRankInstructionDialog");
                dubbingRankInstructionDialog.setSlideMoveCallback(mSlideMoveCallback);
            }
        }, 300);
    }

    private void changeScreenDirByUser() {
        onScreenChange();
        LocalBroadcastManager.getInstance(mContext)
            .sendBroadcast(new Intent(ScreenChangeBroadcastReceiver.SCREEN_CHANGE_ACTION));
    }

    private void onScreenChange() {
        if (!isVertical() && isRealVisable()) {
            if (mVideoPlayController != null && mVideoPlayController.isPlaying()) {
                beginHideOtherView();
            }
            setHasNavBar(false);
        } else if (isVertical() && isRealVisable()) {
            beginShowOtherView();
            setHasNavBar(isOriginHasNavBar());
        }

        if (mPPTPlayController != null) {
            mPPTPlayController.setIsVertical(isVertical());
        }

        if (mVideoPlayController != null) {
            mVideoPlayController.setIsVertical(isVertical());
        }
        if (mZoom != null) {
            mZoom.setImageResource(isVertical() ? R.drawable.main_icon_zoom : R.drawable.main_icon_zoomin);
        }

        if (mPlayStatueBtn != null) {
            if (mPlayStatueBtn.getVisibility() != View.VISIBLE) {
                mPlayStatueBtn.setRotation(isVertical() ? 0 : 90);
            } else {
                ObjectAnimator rotationAnimator;
                if (isVertical()) {
                    rotationAnimator = ObjectAnimator.ofFloat(mPlayStatueBtn, "rotation", 90, 0);
                } else {
                    rotationAnimator = ObjectAnimator.ofFloat(mPlayStatueBtn, "rotation", 0, 90);
                }
                rotationAnimator.start();
            }
        }

        changeLyrOnScreenOrientationChange();
    }

    private void changeLyrOnScreenOrientationChange() {
        if (hasInnerSubtitle) {
            return;
        }
        if (isVertical()) {
            CharSequence text = mLyrViewHorizontal.getText();
            if (TextUtils.isEmpty(text) || isTemplate) {
                mLyrViewAvatar.setVisibility(View.GONE);
            } else {
                if (mLyrViewHorizontalAvatar.getDrawable() != null) {
                    mLyrViewAvatar.setVisibility(View.VISIBLE);
                    mLyrViewAvatar.setImageDrawable(mLyrViewHorizontalAvatar.getDrawable());
                }
            }
            mLyrView.setText(text);
            mLyrView.setVisibility(View.VISIBLE);
            mLyrViewHorizontal.setVisibility(View.INVISIBLE);
            mLyrViewHorizontalAvatar.setVisibility(View.GONE);
        } else {
            String text = mLyrView.getText().toString();
            if (TextUtils.isEmpty(text) || isTemplate) {
                mLyrViewHorizontalAvatar.setVisibility(View.GONE);
            } else {
                if (mLyrViewAvatar.getDrawable() != null) {
                    mLyrViewHorizontalAvatar.setVisibility(View.VISIBLE);
                    mLyrViewHorizontalAvatar.setImageDrawable(mLyrViewAvatar.getDrawable());
                }
            }
            mLyrViewHorizontal.setText(text);
            mLyrViewHorizontal.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(text)) {
                changeHorizontalLyrViewParams(text);
            }
            mLyrView.setVisibility(View.INVISIBLE);
            mLyrViewAvatar.setVisibility(View.GONE);

            if (mPlayRecord != null) {
                mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_ON_FULL_SCREEN_PLAY, null);
            }

            if (mDynamicPlayRecord != null) {
                mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_ON_FULL_SCREEN_PLAY, null);
            }
        }
    }

    private void changeHorizontalLyrViewParams(String text) {
        if (hasInnerSubtitle) {
            return;
        }
        int avatarWidth = BaseUtil.dp2px(mContext, 25);
        int margin = BaseUtil.dp2px(mContext, 5);
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mLyrViewHorizontal.getLayoutParams();
        layoutParams.width = (int) (mInfoLay.getY()
            - ((int) (mTitleBarLay.getTranslationY() + mTitleBarLay.getHeight()))
            - BaseUtil.getStatusBarHeight(mContext)
            - BaseUtil.dp2px(mContext, 20 * 2));
        mLyrViewHorizontal.setLayoutParams(layoutParams);

        int height = layoutParams.width;
        int textHeight = BaseUtil.dp2px(mContext, 20);
        int defHeight = BaseUtil.dp2px(mContext, 240);
        mLyrViewHorizontal.setPivotX(0);
        mLyrViewHorizontal.setPivotY(defHeight);
        Rect rect = new Rect();
        mLyrViewHorizontal.getPaint().getTextBounds(text, 0, text.length(), rect);
        if (rect.width() > height) {
            mLyrViewHorizontal.setTranslationY(avatarWidth + margin - defHeight);
            mLyrViewHorizontalAvatar.setTranslationY(0);
            int column = rect.width() / height + ((rect.width() % height) > 0 ? 1 : 0);
            if (textHeight * column > avatarWidth) {
                mLyrViewHorizontalAvatar.setTranslationX((textHeight * column - avatarWidth) / 2);
            }
        } else {
            mLyrViewHorizontal.setTranslationY((height - rect.width()) / 2 - defHeight);
            mLyrViewHorizontal.setTranslationX((avatarWidth - textHeight) / 2);
            mLyrViewHorizontalAvatar.setTranslationY((height - rect.width()) / 2 - (avatarWidth + margin));
            mLyrViewHorizontalAvatar.setTranslationX(0);
        }

        RelativeLayout.LayoutParams horizontalLrcLayLayoutParams = (RelativeLayout.LayoutParams) mHorizontalLrcLay.getLayoutParams();
        horizontalLrcLayLayoutParams.topMargin = (int) (BaseUtil.dp2px(mContext, 20) + mTitleBarLay.getTranslationY());
        mHorizontalLrcLay.setLayoutParams(horizontalLrcLayLayoutParams);
    }

    @Override
    public long getCurTrackId() {
        return mTrackId;
    }

    @Override
    public long getCurPlayingTrackId() {
        if (mCurPlayTrackId > 0) {
            return mCurPlayTrackId;
        }
        return mTrackId;
    }

    @Override
    public void onPlayStart() {
        if (mPlayStatueBtn != null && mPlayStatueBtn.getVisibility() == View.VISIBLE) {
            ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(mPlayStatueBtn, "alpha", 0.6f, 0f);
            ObjectAnimator xAnimator = ObjectAnimator.ofFloat(mPlayStatueBtn, "scaleX", 1.0f, 1.5f);
            ObjectAnimator yAnimator = ObjectAnimator.ofFloat(mPlayStatueBtn, "scaleY", 1.0f, 1.5f);
            AnimatorSet animatorSet = new AnimatorSet();
            animatorSet.playTogether(alphaAnimator, xAnimator, yAnimator);
            animatorSet.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    mPlayStatueBtn.setVisibility(View.INVISIBLE);
                }
            });
            animatorSet.setInterpolator(new AccelerateDecelerateInterpolator());
            animatorSet.setDuration(100);
            animatorSet.start();
        }

        if (mPlayLoadingView != null) {
            mPlayLoadingView.cancelAnimation();
            mPlayLoadingView.setVisibility(View.INVISIBLE);
        }

        if (isPaused && getCurPlayingTrackId() == PlayTools.getCurTrackId(mContext)
            && !(getManageFragment().getCurrentFragment() instanceof DubbingPlayFragmentNew)) {
            if (mData != null && mData.trackInfo != null) {
                if (!mData.trackInfo.isVideo() && !ALL_IS_VIDEO) {
                    if (mPPTPlayController != null) {
                        mPPTPlayController.setPlayPause();
                    }
                } else {
                    if (mVideoPlayController != null) {
                        mVideoPlayController.setPlayPause();
                    }
                }
            }
        } else if (!isPaused) {
            XmPlayerManager.getInstance(mContext).setVolume(1.0f, 1.0f);
        }

        int playSource = getPlaySource();
        if (mPlayRecord == null) {
            if (mData != null) {
                DubbingSimpleItemInfo dubbingSimpleFromParent = getDubbingSimpleFromParent(mCurPlayTrackId);
                if (dubbingSimpleFromParent != null) {
                    mData.setRecSrc(dubbingSimpleFromParent.getRecSrc());
                    mData.setRecTrack(dubbingSimpleFromParent.getRecTrack());
                }
            }

            mPlayRecord = PlayStatisticsUploaderManager.
                getInstance().
                newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_DUBBING_VIDEO_PLAY_STATISTICS, mData);
            if (mPlayRecord != null) {
                mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_START_TIME, System.currentTimeMillis());
                mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_URL, getPlayUrl());
                mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_TYPE, 0);
                mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_SHOW_TYPE, 2);
                mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_SOURCE, playSource);
            }
        }
        DubPlayParams dubPlayParams = getDubPlayParams(mTrackId);
        if (mDynamicPlayRecord == null && dubPlayParams != null
            && playSource != IXmPlaySource.EVENT_DUB_PLAY_SOURCE_MATERIAL_LANDING) {

            XmPlayRecord mXmPlayRecord = new XmPlayRecord();
            mXmPlayRecord.setVideoId(dubPlayParams.trackId);
            mXmPlayRecord.setFeedId(dubPlayParams.feedId);
            if (mVideoPlayController != null) {
                mXmPlayRecord.setVideoDuration((int) mVideoPlayController.getDuration() / 1000);
            }
            mXmPlayRecord.setRecSrc(dubPlayParams.recSrc);
            mXmPlayRecord.setRecTrack(dubPlayParams.recTrack);
            mXmPlayRecord.setPlayMode(3);//播放方式。1：列表播放(非沉浸式) 2：详情播放(非沉浸式) 3：全屏播放(沉浸式)

            mDynamicPlayRecord = PlayStatisticsUploaderManager.
                getInstance().
                newUploader(IPlayStatisticsUploaderFactory.UPLOADER_TYPE_CHAOS_VIDEO_PLAY_STATISTICS, mXmPlayRecord);
            if (mDynamicPlayRecord != null) {
                mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_START_TIME, System.currentTimeMillis());
                if (mVideoPlayController != null) {
                    mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_URL, mVideoPlayController.getPlayUrl());
                }
                mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_TYPE, 0);
                mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_START_SECOND, 0);
                mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_PLAY_SOURCE, playSource);//6为动态视频详情
            }
        }

        if (mDynamicPlayRecord != null && lastPauseTime != 0) {
            mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_ADD_PAUSED_TIME,
                System.currentTimeMillis() - lastPauseTime);
            lastPauseTime = 0;
        }

        if (mVideoPlayController != null) {
            WeakReference<DubbingInfoFragment> nextDubbInfoFragment = getNextDubbInfoFragment();
            if (nextDubbInfoFragment != null) {
                if (nextDubbInfoFragment.get() != null) {
                    nextDubbInfoFragment.get().preLoadVideoUrl();
                }
            } else {
                HandlerManager.postOnUIThreadDelay(() -> {
                    if (canUpdateUi()) {
                        WeakReference<DubbingInfoFragment> nextDubbInfoFragment1 = getNextDubbInfoFragment();
                        if (nextDubbInfoFragment1 != null) {
                            if (nextDubbInfoFragment1.get() != null) {
                                nextDubbInfoFragment1.get().preLoadVideoUrl();
                            }
                        }
                    }
                }, 500);
            }
        }

        if (!isVertical()) {
            beginHideOtherView();
        }
    }

    @Nullable
    public DubbingSimpleItemInfo getDubbingSimpleFromParent(long trackId) {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            if ((parentFragment = parentFragment.getParentFragment()) instanceof DubbingPlayFragmentNew) {
                return ((DubbingPlayFragmentNew) parentFragment).getDubbingSimpleInfoByTrackId(trackId);
            }
        }
        return null;
    }

    public long getCooperateTemplateIdFromParent(long trackId) {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            if ((parentFragment = parentFragment.getParentFragment()) instanceof DubbingPlayFragmentNew) {
                return ((DubbingPlayFragmentNew) parentFragment).getCooperateTemplateIdByTrackId(trackId);
            }
        }
        return 0L;
    }

    private String getPlayUrl() {
        if (mPPTPlayController != null) {
            return mPPTPlayController.getPlayUrl();
        } else if (mVideoPlayController != null) {
            return mVideoPlayController.getPlayUrl();
        }
        return null;
    }

    @Override
    public void onPlayPause() {
        HandlerManager.postOnMainAuto(() -> {
            if (mPlayStatueBtn != null) {
                ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(mPlayStatueBtn, "alpha", 0f, 0.6f);
                ObjectAnimator xAnimator = ObjectAnimator.ofFloat(mPlayStatueBtn, "scaleX", 1.5f, 1.0f);
                ObjectAnimator yAnimator = ObjectAnimator.ofFloat(mPlayStatueBtn, "scaleY", 1.5f, 1.0f);
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.setInterpolator(new AccelerateInterpolator());
                animatorSet.playTogether(alphaAnimator, xAnimator, yAnimator);
                animatorSet.start();
                animatorSet.setDuration(100);
                mPlayStatueBtn.setVisibility(View.VISIBLE);
            }
        });

        lastPauseTime = System.currentTimeMillis();

    }

    @Override
    public void onPlayProgress(int cur, int duration) {
        changeLyrOnPlayProgress(cur);

        if (mPlayRecord != null) {
            mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_END_TIME, System.currentTimeMillis());
        }

        if (mDynamicPlayRecord != null) {
            mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_END_TIME, System.currentTimeMillis());
        }
    }

    private void changeLyrOnPlayProgress(int cur) {
        if (hasInnerSubtitle) {
            return;
        }
        if (isVertical()) {
            if (mLyrView != null) {
                int indexOfZimu = getZimuIndexByPos(cur);
                if (lastIndexOfZimu != indexOfZimu) {
                    lastIndexOfZimu = indexOfZimu;
                    if (indexOfZimu >= 0 && mLyrics != null && indexOfZimu < mLyrics.size()) {
                        LyricModel lyricModel = mLyrics.get(indexOfZimu);
                        String zimu = StringUtil.chomp(lyricModel.text);
                        if (!TextUtils.isEmpty(zimu)) {
                            zimu = zimu.trim();
                        }
                        mLyrView.setText(zimu);
                        if (TextUtils.isEmpty(zimu) || isTemplate || TextUtils.isEmpty(lyricModel.logoUrl)) {
                            mLyrViewAvatar.setVisibility(View.GONE);
                        } else {
                            mLyrViewAvatar.setVisibility(View.VISIBLE);
                            ImageManager.from(mContext).displayImage(mLyrViewAvatar, lyricModel.logoUrl, com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88);
                        }
                    } else {
                        mLyrView.setText("");
                        mLyrViewAvatar.setVisibility(View.GONE);
                    }
                }

                if (isLyrViewPositioned) {
                    mLyrView.setVisibility(View.VISIBLE);
                } else {
                    mLyrView.setVisibility(View.INVISIBLE);
                }
            }
        } else {
            if (mLyrViewHorizontal != null) {
                int indexOfZimu = getZimuIndexByPos(cur);
                if (lastIndexOfZimu != indexOfZimu) {
                    lastIndexOfZimu = indexOfZimu;

                    if (indexOfZimu >= 0 && mLyrics != null && indexOfZimu < mLyrics.size()) {
                        LyricModel lyricModel = mLyrics.get(indexOfZimu);
                        String zimu = StringUtil.chomp(lyricModel.text);
                        if (!TextUtils.isEmpty(zimu)) {
                            zimu = zimu.trim();
                        }
                        mLyrViewHorizontal.setText(zimu);
                        if (TextUtils.isEmpty(zimu) || isTemplate || TextUtils.isEmpty(lyricModel.logoUrl)) {
                            mLyrViewHorizontalAvatar.setVisibility(View.GONE);
                        } else {
                            mLyrViewHorizontalAvatar.setVisibility(View.VISIBLE);
                            ImageManager.from(mContext).displayImage(mLyrViewHorizontalAvatar, lyricModel.logoUrl, com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88);

                        }
                    } else {
                        mLyrViewHorizontal.setText("");
                        mLyrViewHorizontalAvatar.setVisibility(View.GONE);
                    }
                }
                mLyrViewHorizontal.setVisibility(View.VISIBLE);
                changeHorizontalLyrViewParams(mLyrViewHorizontal.getText().toString());
            }
        }
    }

    @Override
    public void onViewSizeChange(int y, int width, int height) {
        if (isVertical()) {
            isLyrViewPositioned = true;
            int maxY = (int) mInfoLay.getY();
            int realY = y + height;

            if (y + height > maxY) {
                realY = maxY;
            }

            ViewGroup.LayoutParams layoutParams = mMarkView.getLayoutParams();
            if (layoutParams instanceof RelativeLayout.LayoutParams) {
                ((RelativeLayout.LayoutParams) layoutParams).topMargin = realY;
                mMarkView.setLayoutParams(layoutParams);
            }
        }

    }

    @Override
    public void onBufferStart() {
        lastBuffTime = System.currentTimeMillis();
        HandlerManager.removeCallbacks(mRunnable);
        HandlerManager.postOnUIThreadDelay(mRunnable, 500);
    }

    @Override
    public void onBufferStop() {
        HandlerManager.removeCallbacks(mRunnable);
        if (mPlayLoadingView != null) {
            mPlayLoadingView.cancelAnimation();
            mPlayLoadingView.setVisibility(View.INVISIBLE);
        }
        if (mPlayRecord != null) {
            mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_ADD_BLOCK_DURATION,
                System.currentTimeMillis() - lastBuffTime);
        }
    }

    @Override
    public void onComplete() {
        statePlayStatistics();
        // 如果是非竖屏就不走双人合作配音view显现流程
        if (!isVertical()) {
            return;
        }
        // 第一次播放完毕显示双人合作配音view
        boolean canShowCoopView = !mHasShownCooperationView &&
            mDubCooperationView != null &&
            (mDubCooperationView.getVisibility() != View.VISIBLE) &&
            !ToolUtil.isEmptyCollects(mDubCooperationView.getDubCooperationData());
        if (canShowCoopView) {
            beginHideInfoView();
            mHasShownCooperationView = true;
            new UserTracking()
                .setSrcPage("dub")
                .setModuleType("双人信息浮层")
                .setDubId(mCurPlayTrackId)
                .setId("6423")
                .statIting(XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
        }
    }

    private int getZimuIndexByPos(int currPo) {
        if (mLyrics != null) {
            for (int i = 0; i < mLyrics.size(); i++) {
                if (currPo >= mLyrics.get(i).start && currPo <= mLyrics.get(i).end) {
                    return i;
                }
            }
        }
        return -1;
    }

    @Override
    public void onDestroyView() {
        statePlayStatistics();

        if (mVideoPlayController != null) {
            mVideoPlayController.onDestoryView();
        }
        if (mPPTPlayController != null) {
            mPPTPlayController.onFragmentDestory();
        }
        mCanScrollCallback = null;
        mCloseCallBack = null;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mChangeBroadcastReceiver);

        if (mCommentView != null) {
            mCommentView.release();
        }
        if (mCommentManager != null) {
            mCommentManager.release();
        }
        super.onDestroyView();
    }

    public DubShowModel getData() {
        return mData;
    }

    @Override
    protected boolean showTrackInfoView() {
        return false;
    }

    @Override
    protected boolean agentLoadBottomBar() {
        return false;
    }

    @Override
    protected boolean isShowLikeBtn() {
        return false;
    }

    @Override
    protected boolean needShowSponsorView() {
        return false;
    }

    @Override
    protected boolean shouldInitCommentView() {
        return false;
    }

    // 评论
    @Override
    protected void onClickQuora() {
        if (UserInfoMannage.hasLogined()) {
            if (mCommentInputBar == null) {
                initCommentInput();
            }

            if (mCommentInputBar != null && mCommentManager != null && mData != null && mData.trackInfo != null) {
                mCommentInputBar.switchQuora(false);
                mCommentManager.toggleInputBar(CommentConstants.COMMENT_TYPE_SEND_COMMENT);

            }
        } else {
            UserInfoMannage.gotoLogin(getActivity());
        }
    }

    @Override
    public void initCommentInput() {
        if (mCommentInputBar != null) {
            return;
        }
        mCommentInputBar = new CommentQuoraInputLayout(getActivity(), CommentQuoraInputLayout.THEME_TYPE_BLACK);
        mCommentInputBar.setCommentContent(mCommentEdit);
        mCommentInputBar.switchQuora(false);
        mCommentInputBar.setHidePicView(true);
        RelativeLayout relativeLayout = mCommentInputBar.findViewById(R.id.main_comment_header);
        View picLayout = mCommentInputBar.findViewById(R.id.main_v_scrollview_pics);

        if (picLayout.getLayoutParams() instanceof RelativeLayout.LayoutParams) {
            ((RelativeLayout.LayoutParams) picLayout.getLayoutParams()).addRule(RelativeLayout.BELOW, R.id.main_comment_header);
        }

        if (relativeLayout != null && getActivity() != null) {
            getActivity().getLayoutInflater().inflate(R.layout.main_comment_title_top, relativeLayout,
                true);
            commentBackView = relativeLayout.findViewById(R.id.main_comment_back);
            TextView dialogTitle = relativeLayout.findViewById(R.id.main_dialog_title);
            dialogTitle.setText("评论");
            commentBackView.setOnClickListener(v -> {
                if (mCommentManager != null) {
                    mCommentManager.toggleInputBar(CommentConstants.COMMENT_TYPE_SEND_COMMENT);
                }
            });
            AutoTraceHelper.bindDataCallback(commentBackView, mDataProvider);
        }

        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        mRootLayout.addView(mCommentInputBar, params);
        if (mCommentManager != null) {
            mCommentManager.setCommentQuoraInputLayout(mCommentInputBar);
        }
        mCommentInputBar.setVisibility(View.GONE);

        mCommentInputBar.setKeyboardListener(keyboardListener);
    }

    public void setCanScrollCallback(ICallPagerCanScroll canScrollCallback) {
        mCanScrollCallback = canScrollCallback;
    }

    public void setCloseCallBack(IHandleOk closeCallBack) {
        mCloseCallBack = closeCallBack;
    }

    @Override
    public void onSendSuccess() {
        if (mData != null && mData.trackInfo != null) {
            mData.trackInfo.setCommentCount(mData.trackInfo.getCommentCount() + 1);
            if (mComment != null && canUpdateUi()) {
                mComment.setText(hindOver999(mData.trackInfo.getCommentCount()));

                sendCommentCountActionBroadCast(mData.trackInfo.getCommentCount());
            }
        }
    }

    @Override
    public void deleSuccess() {
        if (mData != null && mData.trackInfo != null) {
            mData.trackInfo.setCommentCount(mData.trackInfo.getCommentCount() - 1);
            if (mComment != null && canUpdateUi()) {
                mComment.setText(hindOver999(mData.trackInfo.getCommentCount()));

                sendCommentCountActionBroadCast(mData.trackInfo.getCommentCount());
            }
        }
    }

    @Override
    public boolean onBackPressed() {
        boolean b = super.onBackPressed();
        if (!b && mCommentView != null) {
            boolean onBackPressed = mCommentView.onBackPressed();
            if (onBackPressed) {
                return true;
            }
            if (mCommentView.isViseable()) {
                mCommentView.gone();
                return true;
            }
        }

        if (!b) {
            statePlayStatistics();
        }

        return b;
    }

    @Override
    public void onShareSuccess(String thirdName) {
        if (mData != null && mData.trackInfo != null && !ShareConstants.SHARE_TYPE_CREATE_QR_CODE.equals(thirdName)) {
            mData.trackInfo.setSharesCounts(mData.trackInfo.getSharesCounts() + 1);
            if (mShare != null && canUpdateUi()) {
                mShare.setText(hindOver999(mData.trackInfo.getSharesCounts()));

                sendShareCountActionBroadCast(mData.trackInfo.getSharesCounts());
            }
        }

        new UserTracking()
            .setItemId(mCurPlayTrackId)
            .setItem("dub")
            .setSrcPage("趣配音作品详情页")
            .setShareType(thirdName)
            .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_SHARE);
    }

    @Override
    public void onShareFail(String thirdName) {

    }

    @Override
    public PlayCommentManager getCommentManager() {
        return mCommentManager;
    }

    private void showTopView() {
        if (mTitleBarLay != null && mTitleBarLay.getTranslationY() == -mTitleBarLay.getHeight()) {
            ObjectAnimator.ofFloat(mTitleBarLay, "translationY", -mTitleBarLay.getHeight(), 0).setDuration(200).start();
        }

        if (mDubbAdPageView != null && !isClosedAd()) {
            mDubbAdPageView.setVisibility(View.VISIBLE);
        }
    }

    private void hideTopView() {
        if (mTitleBarLay != null && mTitleBarLay.getTranslationY() == 0) {
            ObjectAnimator.ofFloat(mTitleBarLay, "translationY", 0, -mTitleBarLay.getHeight()).setDuration(200).start();
        }
        if (mDubbAdPageView != null) {
            mDubbAdPageView.setVisibility(View.INVISIBLE);
        }
    }

    public void setPageTrackChanged(IPageTrackIdChanged pageTrackIdChanged) {
        mPageIdChanged = pageTrackIdChanged;
    }

    /**
     * 该方法需要在onCompletePlay和onDestroy中调用
     */
    private void statePlayStatistics() {
        if (mPlayRecord != null) {
            mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_ON_END_PLAY,
                getCurrentPosition() / 1000); //真正的duration会在上报时计算
            mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_BREAK_SECOND, (int) (getCurrentPosition() / 1000));

            if (!isVertical()) {
                mPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_ON_FULL_SCREEN_PLAY, null);
            }

            mPlayRecord.upload();
            mPlayRecord = null;
        }

        if (mDynamicPlayRecord != null) {
            if (!isVertical()) {
                mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_ON_FULL_SCREEN_PLAY, null);
            }

            mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_ON_END_PLAY,
                getCurrentPosition() / 1000); //真正的duration会在上报时计算
            mDynamicPlayRecord.onEvent(IXmPlayStatisticUploader.EVENT_SET_BREAK_SECOND, (int) (getCurrentPosition()
                / 1000));

            mDynamicPlayRecord.upload();
            mDynamicPlayRecord = null;
        }
    }

    private long getCurrentPosition() {
        if (mPPTPlayController != null) {
            return mPPTPlayController.getCurrentPosition();
        } else if (mVideoPlayController != null) {
            return mVideoPlayController.getCurrentPosition();
        }
        return 0;
    }

    @Override
    public boolean isOriginHasNavBar() {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            parentFragment = parentFragment.getParentFragment();
            if (parentFragment instanceof DubbingPlayFragmentNew) {
                return ((DubbingPlayFragmentNew) parentFragment).isOriginHasNavBar();
            }
        }

        return false;
    }

    @Override
    public boolean isVertical() {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            parentFragment = parentFragment.getParentFragment();
            if (parentFragment instanceof DubbingPlayFragmentNew) {
                return ((DubbingPlayFragmentNew) parentFragment).isVertical();
            }
        }

        return true;
    }

    private void setVertical(boolean isVertical) {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            parentFragment = parentFragment.getParentFragment();
            if (parentFragment instanceof DubbingPlayFragmentNew) {
                ((DubbingPlayFragmentNew) parentFragment).setVertical(isVertical);
            }
        }
    }

    private void setHasNavBar(boolean hasNavBar) {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            parentFragment = parentFragment.getParentFragment();
            if (parentFragment instanceof DubbingPlayFragmentNew) {
                ((DubbingPlayFragmentNew) parentFragment).setHasNavBar(hasNavBar);
            }
        }
    }

    private boolean isHiddendInfoView() {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            parentFragment = parentFragment.getParentFragment();
            if (parentFragment instanceof DubbingPlayFragmentNew) {
                return ((DubbingPlayFragmentNew) parentFragment).isHiddenInfoView();
            }
        }
        return false;
    }

    private void setHiddendInfoView(boolean isHiddened) {
        if (isRealVisable() && canUpdateUi()) {
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(isHiddened ?
                ScreenChangeBroadcastReceiver.HIDDEN_CHANGE_ACTION
                : ScreenChangeBroadcastReceiver.VISABLE_CHANGE_ACTION));

            Fragment parentFragment = getParentFragment();
            if (parentFragment instanceof DubbingGroupFragment) {
                parentFragment = parentFragment.getParentFragment();
                if (parentFragment instanceof DubbingPlayFragmentNew) {
                    ((DubbingPlayFragmentNew) parentFragment).setHiddenInfoView(isHiddened);
                }
            }
        }
    }

    public void openComment(boolean openComment) {
        mOpenComment = openComment;
    }

    private void startDownloadVideo() {
        trackOnDownloadClicked(mTrackId);

        if (mVideoPlayInfo == null || mVideoPlayInfo.length <= 0 || TextUtils.isEmpty(mVideoPlayInfo[0])) {
            CustomToast.showFailToast("只有视频才可以保存哦");
            return;
        }

        if (getActivity() == null) {
            return;
        }

        PermissionManage.checkPermission(getActivity(), (MainActivity) getActivity(), new LinkedHashMap<String, Integer>() {
            {
                put(Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    com.ximalaya.ting.android.host.R.string.host_deny_perm_sdcard);
            }
        }, new IMainFunctionAction.IPermissionListener() {
            @Override
            public void havedPermissionOrUseAgree() {
                if (mData == null) {
                    return;
                }

                DubDownloadInfo info = new DubDownloadInfo();
                if (mData.userInfo != null) {
                    info.setUserId(mData.userInfo.getUid());
                    info.setUserName(mData.userInfo.getNickName());
                    info.setUserAvatar(mData.userInfo.getLogo());
                }
                info.setTrackId(getTrackId());
                info.setDubVideoUrl(mVideoPlayInfo[0]);

                if (mData.materialInfo == null) {
                    info.setRoleName("");
                    info.setMaterialName("");
                } else {
                    if (mData.trackInfo != null && !TextUtils.isEmpty(mData.trackInfo.getTrackTitle())) {
                        info.setMaterialName(mData.trackInfo.getTrackTitle());
                    } else if (!TextUtils.isEmpty(mData.materialInfo.ipTag)) {
                        info.setMaterialName(mData.materialInfo.ipTag);
                    }
                    if (ToolUtil.isEmptyCollects(mData.materialInfo.dubRoles)) {
                        info.setRoleName("");
                    } else {
                        if (mData.materialInfo.dubRoles.size() == 1) {
                            info.setRoleName("演绎角色: " + mData.materialInfo.dubRoles.get(0));
                        } else {
                            StringBuilder sb = new StringBuilder("角色: ");
                            for (String dubRole : mData.materialInfo.dubRoles) {
                                sb.append(dubRole).append("  ");
                            }
                            info.setRoleName(sb.toString());
                        }
                    }
                }

                try {
                    IVideoCacheReuseManager cacheReuseManager =
                        Router.<VideoActionRouter>getActionRouter(Configure.BUNDLE_VIDEO).getFunctionAction().getVideoCacheReuseManager();
                    cacheReuseManager.reuseCacheVideo(videoPlayUrl, new VideoCacheReuseCallback() {
                        @Override
                        public void onCacheReused(String localCachePath) {
                            info.setRawVideoPath(localCachePath);
                            realStartDownloadVideo(info);
                        }

                        @Override
                        public void onCacheNoneExist() {
                            realStartDownloadVideo(info);
                        }
                    });
                } catch (Exception e) {
                    realStartDownloadVideo(info);
                }
            }

            @Override
            public void userReject(Map<String, Integer> noRejectPermiss) {}
        });
    }

    private void realStartDownloadVideo(DubDownloadInfo info) {
        HandlerManager.postOnUIThread(() -> DubVideoDownloadTaskController.getInstance().startDownload(info));
    }

    private void trackOnDownloadClicked(long id) {
        UserTracking ut = new UserTracking();
        ut.setSrcModule("selectSharePlatform");
        ut.setSrcPage("dub");
        ut.setItem("button");
        ut.setItemId("save");
        ut.setDubId(id);
        ut.statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DUB_PAGE_CLICK);
    }

    // 获取真正的visableHint
    private boolean getRealVisableHint() {
        if (getParentFragment() instanceof DubbingGroupFragment) {
            printLog(
                "VideoPlayController  .getRealVisableHint  parent =" + getParentFragment().getUserVisibleHint() + "   "
                    + getUserVisibleHint() + "   " + ((DubbingGroupFragment) getParentFragment()).getPageIndex());
            return getParentFragment().getUserVisibleHint() && getUserVisibleHint();
        }
        return getUserVisibleHint();
    }

    private GestureDetector createGestureDetector(Context context, final IClickTypeCallBack clickTypeCallBack) {

        return new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            private long lastDoubleClickTime = 0;

            @Override
            public boolean onDown(MotionEvent e) {
                if (System.currentTimeMillis() - lastDoubleClickTime > 1000) {
                    return super.onDown(e);
                } else {
                    if (clickTypeCallBack != null) {
                        clickTypeCallBack.onMultClick(e);
                    }
                    return super.onDown(e);
                }
            }

            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                if (System.currentTimeMillis() - lastDoubleClickTime > 1000) {
                    if (clickTypeCallBack != null) {
                        clickTypeCallBack.onSingleClick(e);
                    }
                    return super.onSingleTapConfirmed(e);
                } else {
                    return super.onSingleTapConfirmed(e);
                }

            }

            @Override
            public boolean onDoubleTap(MotionEvent e) {
                lastDoubleClickTime = System.currentTimeMillis();
                return super.onDoubleTap(e);
            }
        });
    }

    private void createLikeAnimationView(final RelativeLayout viewGroup, MotionEvent event) {
        if (viewGroup == null || event == null || System.currentTimeMillis() - mLastCreateLikeAnimationTime < 200) {
            return;
        }

        mLastCreateLikeAnimationTime = System.currentTimeMillis();

        final ResizeXmLottieAnimationView lottieAnimationView = new ResizeXmLottieAnimationView(viewGroup.getContext());

        int h = BaseUtil.dp2px(mContext, 329);
        int w = BaseUtil.dp2px(mContext, 265);
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(w, h);

        params.topMargin = (int) event.getY() - h + BaseUtil.dp2px(mContext, 70);
        params.leftMargin = (int) event.getX() - w / 2;

        lottieAnimationView.setImageAssetsFolder("lottie/dubb_double_click/images/");
        lottieAnimationView.setAnimation("lottie/dubb_double_click/data.json");
        lottieAnimationView.addAnimatorListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                postOnUiThread(() -> {
                    if (canUpdateUi() && ViewCompat.isAttachedToWindow(mRootLayout)
                        && ViewCompat.isAttachedToWindow(lottieAnimationView)) {
                        mRootLayout.removeView(lottieAnimationView);
                    }
                });
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });

        viewGroup.addView(lottieAnimationView, params);

        lottieAnimationView.playAnimation();
    }

    private LinkedHashMap<Long, DubShowModel> getRequestCache() {
        Fragment fragment;
        if ((fragment = getParentFragment()) instanceof DubbingGroupFragment) {
            if ((fragment = fragment.getParentFragment()) instanceof DubbingPlayFragmentNew) {
                return ((DubbingPlayFragmentNew) fragment).getRequestCache();
            }
        }
        return new LinkedHashMap<>();
    }

    private void resetLastBottomAnimation() {
        if (canUpdateUi()) {
            if (mLastBottomAnimtion != null && mLastBottomAnimtion.isAnimating()) {
                mLastBottomAnimtion.cancelAnimation();

                if (canUpdateUi() && mLastBottomAnimtion != null && ViewCompat.isAttachedToWindow(mRootLayout)
                    && ViewCompat.isAttachedToWindow(mLastBottomAnimtion)) {
                    mRootLayout.removeView(mLastBottomAnimtion);
                    mLastBottomAnimtion = null;
                }
            }

            mLike.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable
                .main_dubbing_like_state_new), null, null, null);
        }
    }

    private void addLikeAnimation() {
        if (canUpdateUi() && mRootLayout != null) {
            final XmLottieAnimationView bottomAnimation = new XmLottieAnimationView(mContext);
            mLastBottomAnimtion = bottomAnimation;
            RelativeLayout.LayoutParams params1 = new RelativeLayout.LayoutParams(BaseUtil.dp2px(mContext, 50),
                BaseUtil.dp2px(mContext, 50));
            params1.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
            params1.bottomMargin = BaseUtil.dp2px(mContext, 11);
            params1.leftMargin = BaseUtil.dp2px(mContext, 4);

            bottomAnimation.setImageAssetsFolder("lottie/dubb_bottom_click/images/");
            bottomAnimation.setAnimation("lottie/dubb_bottom_click/data.json");
            bottomAnimation.addAnimatorListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                    mLike.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable
                        .main_icon_transparent), null, null, null);
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    mLike.setCompoundDrawables(LocalImageUtil.getDrawable(mContext,
                        R.drawable.main_dubbing_like_state_new), null, null, null);

                    postOnUiThread(() -> {
                        if (canUpdateUi() && bottomAnimation != null && ViewCompat.isAttachedToWindow(mRootLayout)
                            && ViewCompat.isAttachedToWindow(bottomAnimation)) {
                            mRootLayout.removeView(bottomAnimation);
                            if (mLastBottomAnimtion == bottomAnimation) {
                                mLastBottomAnimtion = null;
                            }
                        }
                    });

                }

                @Override
                public void onAnimationCancel(Animator animation) {

                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });

            mRootLayout.addView(bottomAnimation, params1);
            bottomAnimation.playAnimation();

            final XmLottieAnimationView lottieAnimationView = new XmLottieAnimationView(mContext);
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(BaseUtil.dp2px(mContext, 58),
                BaseUtil.dp2px(mContext, 230));
            params.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
            params.bottomMargin = BaseUtil.dp2px(mContext, 35);
            params.leftMargin = BaseUtil.dp2px(mContext, 6);

            lottieAnimationView.setImageAssetsFolder("lottie/dubb_like_up/images/");
            lottieAnimationView.setAnimation("lottie/dubb_like_up/data.json");
            lottieAnimationView.addAnimatorListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {

                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    postOnUiThread(() -> {
                        if (canUpdateUi() && ViewCompat.isAttachedToWindow(mRootLayout)
                            && ViewCompat.isAttachedToWindow(lottieAnimationView)) {
                            mRootLayout.removeView(lottieAnimationView);
                        }
                    });
                }

                @Override
                public void onAnimationCancel(Animator animation) {

                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });

            mRootLayout.addView(lottieAnimationView, params);
            lottieAnimationView.playAnimation();
        }
    }

    private void sendLikeActionBroadCast(boolean isLike, int likeCount) {
        Intent intent = new Intent(AppConstants.TYPE_DUBBING_ACTION_LIKE);
        intent.putExtra(AppConstants.DATA_DUBBING_IS_LIKE, isLike);
        intent.putExtra(AppConstants.DATA_DUBBING_LIKE_COUNT, likeCount);
        // 当页面来源是话题-最近页面时也发广播
        if ((mTopicSourceInfo != null) && (mTopicSourceInfo.getSourceType() == AppConstants.TYPE_TOPIC_RECENT_WORK)) {
            intent.putExtra(AppConstants.DATA_DUBBING_TRACK_ID, getTrackId());
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);
        }
        // 当页面来源是发现-趣配音页面时也需要发广播
        else if (getSourceType() == AppConstants.TYPE_FINDING) {
            intent.putExtra(AppConstants.DATA_DUBBING_TRACK_ID, getTrackId());
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);
        } else {
            sendBroadCast(intent);
        }
    }

    private void sendShareCountActionBroadCast(int shareCount) {
        Intent intent = new Intent(AppConstants.TYPE_DUBBING_ACTION_SHARE);
        intent.putExtra(AppConstants.DATA_DUBBING_SHARE_COUNT, shareCount);
        sendBroadCast(intent);
    }

    private void sendCommentCountActionBroadCast(int commentCount) {
        Intent intent = new Intent(AppConstants.TYPE_DUBBING_ACTION_COMMENT);
        intent.putExtra(AppConstants.DATA_DUBBING_COMMENT_COUNT, commentCount);
        sendBroadCast(intent);
    }

    private void sendBroadCast(Intent intent) {

        DubPlayParams dubPlayParams = getDubPlayParams(getTrackId());
        if (dubPlayParams != null) {
            intent.putExtra(AppConstants.DATA_DUBBING_FEED_ID, dubPlayParams.feedId);
            intent.putExtra(AppConstants.DATA_DUBBING_TRACK_ID, dubPlayParams.trackId);
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);
        }

    }

    private long getTrackId() {
        long trackId = mTrackId;
        if (mCurPlayTrackId > 0) {
            trackId = mCurPlayTrackId;
        }
        return trackId;
    }

    public int getPlaySource() {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            parentFragment = parentFragment.getParentFragment();
            if (parentFragment instanceof DubbingPlayFragmentNew) {
                return ((DubbingPlayFragmentNew) parentFragment).getPlaySource();
            }
        }
        return 0;
    }

    /**
     * 获取原始传进来的sourceType
     */
    public int getSourceType() {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            parentFragment = parentFragment.getParentFragment();
            if (parentFragment instanceof DubbingPlayFragmentNew) {
                return ((DubbingPlayFragmentNew) parentFragment).getSourceType();
            }
        }
        return 0;
    }

    public WeakReference<DubbingInfoFragment> getNextDubbInfoFragment() {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            parentFragment = parentFragment.getParentFragment();
            if (parentFragment instanceof DubbingPlayFragmentNew) {
                return ((DubbingPlayFragmentNew) parentFragment).getDubbInfoByPosition(mPageIndex + 1);
            }
        }
        return null;
    }

    public long getActivityId() {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            parentFragment = parentFragment.getParentFragment();
            if (parentFragment instanceof DubbingPlayFragmentNew) {
                return ((DubbingPlayFragmentNew) parentFragment).getActivityId();
            }
        }
        return 0L;
    }

    public void preLoadVideoUrl() {
        mCanPreLoad = true;
        if (videoPlayUrl != null && canPreload() && (mVideoPlayController == null
            || !mVideoPlayController.isSetDataToVideo())) {
            Router.getActionByCallback(Configure.BUNDLE_VIDEO, (SimpleBundleInstallCallback) bundleModel -> {
                if (canPreload()) {
                    try {
                        Router.<VideoActionRouter>getActionRouter(Configure.BUNDLE_VIDEO).getFunctionAction()
                            .getVideoPreLoadManager(mContext).preLoadVideo(videoPlayUrl);
                        preLoaded();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
        }
    }

    @Override
    public void preLoaded() {
        isPreloaded = true;
    }

    @Override
    public boolean canPreload() {
        return !isRemoving() && !isDetached() && !isPreloaded && mCanPreLoad;
    }

    @Override
    public int getCurPage() {
        return mPageIndex;
    }

    public void printLog(String content) {
        Logger.log(content + "  curPage=" + mPageIndex);
    }

    private void hideInfoViewNoAnimation(boolean byUser) {
        if (canUpdateUi() && mInfoLay != null) {
            mInfoLay.setVisibility(View.INVISIBLE);
            mGotoDubbing.setVisibility(View.INVISIBLE);
            mGotoDubbing.setTranslationY(BaseUtil.dp2px(mContext, 105));
            mAdLay.setVisibility(View.INVISIBLE);
            mTitleBarLay.setVisibility(View.INVISIBLE);
            mTopMask.setVisibility(View.INVISIBLE);
            mBottomMask.setVisibility(View.INVISIBLE);
            mDubCooperationView.invisible();
            if (byUser) {
                getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
                setHiddendInfoView(true);
            }
        }
    }

    private void showInfoAnimatorNoAnimtor() {
        if (canUpdateUi() && mInfoLay != null) {
            mInfoLay.setTranslationY(0);
            mGotoDubbing.setTranslationY(0);
            mTitleBarLay.setTranslationY(0);
            mAdLay.setTranslationX(0);
            mTopMask.setAlpha(1.0f);
            mBottomMask.setAlpha(1.0f);
            visiableNoAnimator();
        }
    }

    private void visiableNoAnimator() {
        if (canUpdateUi() && mInfoLay != null) {
            mInfoLay.setVisibility(View.VISIBLE);
            if (mData != null && mData.materialInfo != null && mData.materialInfo.canDub) {
                mGotoDubbing.setVisibility(View.VISIBLE);
            }
            mAdLay.setVisibility(View.VISIBLE);
            mTitleBarLay.setVisibility(View.VISIBLE);
            mTopMask.setVisibility(View.VISIBLE);
            mBottomMask.setVisibility(View.VISIBLE);

            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
    }

    private void beginHideOtherView() {
        cancelShowOtherView();
        HandlerManager.postOnUIThreadDelay(mHideInfoViewRunnable, 1500);
    }

    private void beginShowOtherView() {
        cancelHideOtherView();
        HandlerManager.postOnUIThread(mShowInfoViewRunnable);
    }

    private void cancelShowOtherView() {
        HandlerManager.removeCallbacks(mShowInfoViewRunnable);
    }

    private void cancelHideOtherView() {
        HandlerManager.removeCallbacks(mHideInfoViewRunnable);
    }

    private void beginShowInfoView() {
        HandlerManager.removeCallbacks(mHorizontalHideInfoViewRunnable);
        HandlerManager.postOnUIThread(mHorizontalShowInfoViewRunnable);
    }

    private void beginHideInfoView() {
        HandlerManager.removeCallbacks(mHorizontalShowInfoViewRunnable);
        HandlerManager.postOnUIThread(mHorizontalHideInfoViewRunnable);
    }

    @Nullable
    private DubbingPlayFragmentNew getParentParentFragment() {
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof DubbingGroupFragment) {
            parentFragment = parentFragment.getParentFragment();
            if (parentFragment instanceof DubbingPlayFragmentNew) {
                return (DubbingPlayFragmentNew) parentFragment;
            }
        }
        return null;
    }

    private void setClosedAded(boolean isClosed) {
        LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(ScreenChangeBroadcastReceiver
            .CLOSE_AD_CHANGE_ACTION));
        DubbingPlayFragmentNew parentParentFragment = getParentParentFragment();
        if (parentParentFragment != null) {
            parentParentFragment.setCloseAd(isClosed);
        }
    }

    private boolean isClosedAd() {
        DubbingPlayFragmentNew parentParentFragment = getParentParentFragment();
        if (parentParentFragment != null) {
            return parentParentFragment.isCloseAd();
        }
        return false;
    }

    @Override
    protected boolean hasInfiniteCommentView() {
        return false;
    }

    public class ScreenChangeBroadcastReceiver extends BroadcastReceiver {

        public static final String SCREEN_CHANGE_ACTION = "SCREEN_CHANGE_ACTION";
        public static final String HIDDEN_CHANGE_ACTION = "HIDDEN_CHANGE_ACTION";
        public static final String VISABLE_CHANGE_ACTION = "VISABLE_CHANGE_ACTION";
        public static final String CLOSE_AD_CHANGE_ACTION = "CLOSE_AD_CHANGE_ACTION";

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null) {
                return;
            }
            if (SCREEN_CHANGE_ACTION.equals(intent.getAction()) && !getRealVisableHint()) {
                onScreenChange();
            } else if (HIDDEN_CHANGE_ACTION.equals(intent.getAction()) && !getRealVisableHint()) {
                hideInfoViewNoAnimation(false);
            } else if (VISABLE_CHANGE_ACTION.equals(intent.getAction()) && !getRealVisableHint()) {
                showInfoAnimatorNoAnimtor();
            } else if (CLOSE_AD_CHANGE_ACTION.equals(intent.getAction()) && !getRealVisableHint()) {
                removeDubbView();
            }
        }
    }
}
