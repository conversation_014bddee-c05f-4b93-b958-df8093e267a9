package com.ximalaya.ting.android.main.dialog;

import android.app.Activity;

import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constants.TingListConstants;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFunctionAction;
import com.ximalaya.ting.android.host.manager.tinglist.ITingListManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.dialog.BaseDialogModel;
import com.ximalaya.ting.android.host.util.MyListenRouterUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.albumModule.other.SimilarRecommendFragment;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

import java.util.ArrayList;
import java.util.List;

/**
 * H5唤起Native底部推荐弹框， 多次调用会唤起多个弹框
 * Created by xiaolei on 2021/9/27.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13701664636
 */
public class RecommendBottomDialogCaller {

    public static void handleIting(Activity activity, String type, long albumId, long trackId) {
        if ("sound".equals(type)) {
            callSoundBottomDialog(activity, albumId, trackId);
        } else if ("album".equals(type)) {
            callAlbumBottomDialog(activity, albumId);
        }
    }

    /**
     * 弹出专辑对应的底部推荐弹框
     */
    private static void callAlbumBottomDialog(Activity activity, long albumId) {
        if (activity == null || activity.isFinishing()) return;
        int iconColor = (activity.getResources() != null)
                ? activity.getResources().getColor(R.color.main_color_333333_cfcfcf) : 0xff333333;
        List<BaseDialogModel> models = new ArrayList<>();
        models.add(new BaseDialogModel(R.drawable.main_ic_recommend_similar, iconColor, "找相似", 0));
        models.add(new BaseDialogModel(R.drawable.main_ic_recommend_add_to_tinglist, iconColor, "添加到听单", 1));
        new RecommendPageBottomDialog(activity, models,
                (dialog, model) -> {
                    dialog.dismiss();
                    if (model != null) {
                        switch (model.position) {
                            case 0:
                                if (activity instanceof MainActivity) {
                                    ((MainActivity) activity).startFragment(SimilarRecommendFragment.newInstanceByAlbumId(
                                            albumId, "相似推荐"));
                                }
                                break;
                            case 1:
                                if (activity instanceof MainActivity) {
                                    Fragment fragment = ((MainActivity) activity).getCurrentFragmentInManage();
                                    if (fragment instanceof BaseFragment2) {
                                        addAlbumTingList(albumId, (BaseFragment2) fragment);
                                    }
                                }
                                break;
                            default:
                                break;
                        }
                    }
                }).show();
    }

    private static void addAlbumTingList(final long albumId, BaseFragment2 fragment) {
        MyListenRouterUtil.getMyListenBundle(bundleModel -> {
            IMyListenFunctionAction funAction = MyListenRouterUtil.getFunAction();
            if (funAction != null && fragment != null && fragment.canUpdateUi()) {
                ITingListManager tingListManager = funAction.newTingListManager(fragment);
                tingListManager.showTingList(TingListConstants.TYPE_ALBUM, albumId);
            }
        });
    }

    /**
     * 弹出声音对应的底部推荐弹框
     */
    private static void callSoundBottomDialog(Activity activity, long albumId, long trackId) {
        if (activity == null || activity.isFinishing()) return;
        int iconColor = (activity.getResources() != null)
                ? activity.getResources().getColor(R.color.main_color_333333_cfcfcf) : 0xff333333;
        List<BaseDialogModel> models = new ArrayList<>();
        models.add(new BaseDialogModel(R.drawable.main_ic_view_album, iconColor, "查看专辑", 0));
        models.add(new BaseDialogModel(R.drawable.main_ic_recommend_add_to_tinglist, iconColor, "添加到听单", 1));
        new RecommendPageBottomDialog(activity, models, (dialog, model) -> {
            dialog.dismiss();
            int pos = model.position;
            switch (pos) {
                case 0:  // 查看专辑
                    AlbumEventManage.startMatchAlbumFragment(albumId,
                            AlbumEventManage.FROM_OTHER
                            , ConstantsOpenSdk.PLAY_FROM_OTHER, "", ""
                            , -1, activity);
                    break;
                case 1: // 添加到听单
                    if (activity instanceof MainActivity) {
                        Fragment fragment = ((MainActivity) activity).getCurrentFragmentInManage();
                        if (fragment instanceof BaseFragment2) {
                            addTrackToTingList(trackId, (BaseFragment2) fragment);
                        }
                    }
                    break;
                default:
                    break;
            }
        }).show();
    }

    private static void addTrackToTingList(final long trackId, final BaseFragment2 fragment) {
        MyListenRouterUtil.getMyListenBundle(bundleModel -> {
            IMyListenFunctionAction funAction = MyListenRouterUtil.getFunAction();
            if (funAction != null && fragment != null && fragment.canUpdateUi()) {
                ITingListManager tingListManager = funAction.newTingListManager(fragment);
                tingListManager.showTingList(TingListConstants.TYPE_TRACK, trackId);
            }
        });
    }
}
