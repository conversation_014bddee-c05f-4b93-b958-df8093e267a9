//package com.ximalaya.ting.android.main.fragment.find.child.manager
//
//import android.graphics.Color
//import android.net.Uri
//import android.os.Bundle
//import android.view.View
//import com.google.gson.Gson
//import com.google.gson.reflect.TypeToken
//import com.tmall.wireless.vaf.virtualview.Helper.LocalImageProvider
//import com.tmall.wireless.vaf.virtualview.core.ElParseUtil
//import com.tmall.wireless.vaf.virtualview.core.IView
//import com.tmall.wireless.vaf.virtualview.event.EventData
//import com.tmall.wireless.vaf.virtualview.event.EventManager
//import com.tmall.wireless.vaf.virtualview.view.image.NativeImage
//import com.tmall.wireless.vaf.virtualview.view.image.NativeLottieImage
//import com.tmall.wireless.vaf.virtualview.view.nlayout.NativeLayoutImpl
//import com.ximalaya.ting.android.adsdk.external.INativeAd
//import com.ximalaya.ting.android.adsdk.external.feedad.IAdModel
//import com.ximalaya.ting.android.adsdk.external.feedad.IAnchorAdInfo
//import com.ximalaya.ting.android.adsdk.external.feedad.IFeedAd
//import com.ximalaya.ting.android.configurecenter.ConfigureCenter
//import com.ximalaya.ting.android.framework.BaseApplication
//import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
//import com.ximalaya.ting.android.framework.util.CustomToast
//import com.ximalaya.ting.android.host.activity.MainActivity
//import com.ximalaya.ting.android.host.download.bean.TaskInfo
//import com.ximalaya.ting.android.host.download.listener.DownloadListener
//import com.ximalaya.ting.android.host.download.manager.TaskMgr
//import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog
//import com.ximalaya.ting.android.host.feedback.XmFeedBackManager
//import com.ximalaya.ting.android.host.feedback.XmFeedBackPopDialog
//import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
//import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build
//import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
//import com.ximalaya.ting.android.host.fragment.BaseFragment2
//import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
//import com.ximalaya.ting.android.host.manager.NewShowNotesManager
//import com.ximalaya.ting.android.host.manager.PlayCompleteRecommendManager
//import com.ximalaya.ting.android.host.manager.ToListenManager
//import com.ximalaya.ting.android.host.manager.ToListenManager.addToListenTrack
//import com.ximalaya.ting.android.host.manager.ToListenManager.playToListenTracks
//import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
//import com.ximalaya.ting.android.host.manager.ad.AdSDKManager
//import com.ximalaya.ting.android.host.manager.bundleframework.Configure
//import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
//import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter
//import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
//import com.ximalaya.ting.android.host.manager.play.PlayerManager
//import com.ximalaya.ting.android.host.manager.playlist.saveRecommendTrackId
//import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType
//import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
//import com.ximalaya.ting.android.host.model.XmFeedInnerModel
//import com.ximalaya.ting.android.host.model.dialog.ItemType
//import com.ximalaya.ting.android.host.util.AccessibilityUtil
//import com.ximalaya.ting.android.host.util.MyListenRouterUtil
//import com.ximalaya.ting.android.host.util.common.ToolUtil
//import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
//import com.ximalaya.ting.android.host.util.server.PlayTools
//import com.ximalaya.ting.android.host.util.template.XmTemplateDetail.XmLayout
//import com.ximalaya.ting.android.host.view.VVRoundImageView
//import com.ximalaya.ting.android.host.view.VVXmLottieAnimationView
//import com.ximalaya.ting.android.lottie.download.LottieDownloadProvider
//import com.ximalaya.ting.android.main.R
//import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel
//import com.ximalaya.ting.android.main.anchorModule.anchorSpace.util.newAnchorSpaceFragment
//import com.ximalaya.ting.android.main.constant.MainUrlConstants
//import com.ximalaya.ting.android.main.dialog.AdDislikeBottomDialogX
//import com.ximalaya.ting.android.main.dialog.RecommendPageBottomDialog
//import com.ximalaya.ting.android.main.fragment.find.child.recommendad.IFeedAnchorAdManagerStaggered
//import com.ximalaya.ting.android.main.fragment.find.child.staggered.*
//import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager.VIEW_TYPE_AD_MIX_ANCHOR
//import com.ximalaya.ting.android.main.listener.IRecommendFeedItemActionListener
//import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager
//import com.ximalaya.ting.android.main.model.rec.*
//import com.ximalaya.ting.android.main.model.recommend.DislikeReasonModel
//import com.ximalaya.ting.android.main.model.recommend.DislikeReasonModelV1
//import com.ximalaya.ting.android.main.model.recommend.DislikeReasonNew
//import com.ximalaya.ting.android.main.util.other.AlbumActionUtil
//import com.ximalaya.ting.android.main.util.other.ShareUtilsInMain
//import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
//import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
//import com.ximalaya.ting.android.opensdk.model.track.SimpleTrackForToListen
//import com.ximalaya.ting.android.opensdk.model.track.Track
//import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
//import com.ximalaya.ting.android.shareservice.base.IShareDstType
//import com.ximalaya.ting.android.template.TemplateManager
//import org.json.JSONObject
//import java.lang.ref.WeakReference
//import java.util.concurrent.CopyOnWriteArrayList
//
///**
// * Created by WolfXu on 2022/5/11.
// * <AUTHOR>
// * @email <EMAIL>
// * @phoneNumber ***********
// */
//object RecommendPageVirtualViewManager {
//    const val BUSINESS_NAME = "firstPage"
//    private const val ACTION_JUMP = "jump"
//    private const val ACTION_PLAY = "play"
//    private const val ACTION_SHOW_NOTE_JUMP = "showNoteJump"
//    private const val ACTION_SHOW_NOTE_PLAY = "showNotePlay"
//    private const val ACTION_REMOVE = "remove"
//    private const val ACTION_SHARE_TO_MOMENT = "share1"
//    private const val ACTION_SHARE_TO_WECHAT = "share2"
//    private const val ACTION_DISLIKE = "dislike"
//    private const val ACTION_DISLIKE_AD = "dislikeAd"
//    private const val ACTION_DISLIKE_AD_LONG = "dislikeAdLong"
//    private const val ACTION_USER = "user"
//    const val ACTION_ADD_TO_BE_PLAY = "addToBePlay"
//    val DATA_TAG = R.id.main_item_view_data
//
//    private var mViewTypeMap = mutableMapOf<String, Int>()
//    private var mViewTypeToTypeMap = mutableMapOf<Int, String>()
//    private var mDebugTemplateMap = mutableMapOf<String, ByteArray>()
//    private val mIsDebug = ConstantsOpenSdk.isDebug
//    private var mFragmentReference: WeakReference<BaseFragment2?>? = null
//    private val mFragment get() = mFragmentReference?.get()
//    private var mAdapterAction: IAdatperAction? = null
//
//    init {
//        registerLocalTemplate()
//
//        TemplateManager.getInstance().registerEventProcessor(EventManager.TYPE_Click) { eventData ->
//            if (eventData.mView?.visibility == View.VISIBLE) {
//                return@registerEventProcessor handleEvent(EventManager.TYPE_Click, eventData)
//            }
//            return@registerEventProcessor false
//        }
//        TemplateManager.getInstance().registerEventProcessor(EventManager.TYPE_LongCLick) { eventData ->
//            if (eventData.mView?.visibility == View.VISIBLE) {
//                return@registerEventProcessor handleEvent(EventManager.TYPE_LongCLick, eventData)
//            }
//            return@registerEventProcessor false
//        }
//
//        TemplateManager.getInstance().registerNativeView(NativeImage::class.java) { ctx ->
//            VVRoundImageView(ctx)
//        }
//        TemplateManager.getInstance().registerNativeView(NativeLottieImage::class.java) { ctx ->
//            VVXmLottieAnimationView(ctx)
//        }
//
//        LottieDownloadProvider.getInstance(BaseApplication.getMyApplicationContext()).setDownloadHelper { url, downloadCallback ->
//            val taskInfo = TaskInfo.TaskInfoBuilder()
//                .setUrl(url)
//                .setDirPath(LottieDownloadProvider.getInstance(BaseApplication.getMyApplicationContext()).downloadPath)
//                .setFileName(LottieDownloadProvider.KeyFactory.DEFAULT.buildKey(Uri.parse(url)))
//                .build()
//            TaskMgr.get().add(taskInfo, object : DownloadListener {
//                override fun onTaskStart(task: TaskInfo?) {
//                }
//
//                override fun onTaskSuccess(task: TaskInfo?) {
//                    downloadCallback.onSuccess()
//                }
//
//                override fun onTaskFailed(task: TaskInfo?) {
//                }
//
//                override fun onTaskProgress(task: TaskInfo?, progress: Int) {
//                }
//            })
//        }
//
//        LocalImageProvider.addLocalImage("coverHolder", com.ximalaya.ting.android.host.R.drawable.host_default_album)
//        LocalImageProvider.addLocalImage("playCountHolder", R.drawable.main_ic_play_count_new)
//        LocalImageProvider.addLocalImage("durationHolder", R.drawable.main_ic_play_duration_new)
//        LocalImageProvider.addLocalImage("share1Holder", com.ximalaya.ting.android.host.R.drawable.host_btn_share_moment_new1)
//        LocalImageProvider.addLocalImage("share2Holder", com.ximalaya.ting.android.host.R.drawable.host_btn_share_wx)
//        LocalImageProvider.addLocalImage("feedbackHolder", R.drawable.main_ic_feedback)
//        LocalImageProvider.addLocalImage("logoHolder", com.ximalaya.ting.android.host.R.drawable.host_default_avatar_210)
//        LocalImageProvider.addLocalImage("showNotesPlay", com.ximalaya.ting.android.host.R.drawable.host_btn_play_btn_inside_fill_n_24)
//        LocalImageProvider.addLocalImage("showNotesPause", com.ximalaya.ting.android.host.R.drawable.host_btn_pause_btn_n_fill_n_24_1)
//    }
//
//    fun init(fragment: BaseFragment2, action: IAdatperAction) {
//        mFragmentReference = WeakReference(fragment)
//        mAdapterAction = action
//    }
//
//    fun release() {
//        mViewTypeMap.clear()
//        mViewTypeToTypeMap.clear()
//        mFragmentReference = null
//        mAdapterAction = null
//    }
//
//    fun getItemViewType(item: Any): Int {
//        if (item is RecommendItemNew) {
//            return getItemViewType(item)
//        }
//        if (item is ItemModel<*> && item.viewType == VIEW_TYPE_AD_MIX_ANCHOR) {
//            return VIEW_TYPE_AD_MIX_ANCHOR
//        }
//        // todo: 其他情况处理，比如焦点图、广告等
//        return RecommendFragmentTypeManager.VIEW_TYPE_DEFAULT
//    }
//
//    fun getViewTypeForVV(recommendItemNew: RecommendItemNew): String {
//        if (recommendItemNew.itemType.isNotEmpty()) {
//            if (recommendItemNew.itemType.equals(RecommendItemNew.RECOMMEND_ITEM_MODULE)) {
//                return recommendItemNew.jsonObject?.getJSONObject("item")?.getString("moduleType") ?: ""
//            }
//            return recommendItemNew.itemType
//        } else {
//            return ""
//        }
//    }
//
//    private fun getItemViewType(item: RecommendItemNew): Int {
//        var viewType: Int? = null
//        var type: String = if (RecommendItemNew.RECOMMEND_ITEM_MODULE == item.itemType) {
//            val moduleItem = item.item as? RecommendModuleItem
//            "${RecommendItemNew.RECOMMEND_ITEM_MODULE}_" + (moduleItem?.moduleType ?: "")
//        } else {
//            item.itemType ?: ""
//        }
//        if (RecommendFragmentAbManager.is2024NewRecommendFragment()) {
//            type = "newRec_$type"
//        } else if (NewShowNotesManager.userNewShowNotes() && RecommendItemNew.RECOMMEND_ITEM_TRACK == item.itemType) {
//            type = "showNote_$type"
//        }
//        // 如果已经加过对应viewType，直接取
//        if (type.isNotEmpty()) {
//            viewType = mViewTypeMap[type]
//        }
//
//
//        // 没有加过的话，看有没有对应模板，有的话加一个新的viewType
//        if (viewType == null) {
//            var vvTypeString = getViewTypeForVV(item)
//            if (RecommendFragmentAbManager.is2024NewRecommendFragment()) {
//                vvTypeString = "newRec_$vvTypeString"
//            } else if (NewShowNotesManager.userNewShowNotes() && RecommendItemNew.RECOMMEND_ITEM_TRACK == item.itemType) {
//                vvTypeString = "showNote_$vvTypeString"
//            }
//            if (hasTemplate(vvTypeString)) {
//                loadTemplate(vvTypeString)
//                viewType = RecommendFragmentTypeManager.VIEW_TYPE_BASE++
//                mViewTypeMap[type] = viewType
//                mViewTypeToTypeMap[viewType] = type
//                loadTemplate(vvTypeString)
//            } else {
//                // 没有模板的也需要存一下，避免中途变化，导致异常
//                mViewTypeMap[type] = RecommendFragmentTypeManager.VIEW_TYPE_DEFAULT
//                viewType = RecommendFragmentTypeManager.VIEW_TYPE_DEFAULT
//            }
//        }
//        // 没有模板，把缓存的jsonObject移除
////        if (viewType == RecommendFragmentTypeManager.VIEW_TYPE_DEFAULT) {
////            item.jsonObject = null
////        }
//        return viewType
//    }
//
//    fun isTypeOfVirtualView(viewType: Int): Boolean {
//        if (viewType == RecommendFragmentTypeManager.VIEW_TYPE_DEFAULT || viewType == VIEW_TYPE_AD_MIX_ANCHOR) {
//            return false
//        }
//        return mViewTypeMap.values.contains(viewType)
//    }
//
//    fun isFullSpan(viewType: Int): Boolean? {
//        return if (isTypeOfVirtualView(viewType)) {
//            val type = mViewTypeToTypeMap[viewType]
//            if (type != null) {
//                val templateDetail: XmLayout? = TemplateManager.getInstance().getTemplateDetail(
//                    BUSINESS_NAME, type)
//                val columnsNum = templateDetail?.columnsNum
//                if (columnsNum != null) {
//                    columnsNum == 1L
//                } else {
//                    true
//                }
//            } else {
//                null
//            }
//        } else {
//            null
//        }
//    }
//
//    fun isBigCard(viewType: Int): Boolean {
//        return if (isTypeOfVirtualView(viewType)) {
//            val type = mViewTypeToTypeMap[viewType]
//            if (type != null) {
//                val templateDetail: XmLayout? = TemplateManager.getInstance().getTemplateDetail(
//                    BUSINESS_NAME, type)
//                val columnsNum = templateDetail?.isBigCard
//                if (columnsNum != null) {
//                    columnsNum == true
//                } else {
//                    false
//                }
//            } else {
//                false
//            }
//        } else {
//            false
//        }
//    }
//
//    fun createContainer(viewType: Int): View? {
//        if (viewType < 0) {
//            return null
//        }
//        val type = mViewTypeToTypeMap[viewType]
//        return type?.let {
//            var realType = it
//            if (realType.startsWith("MODULE_")) {
//                realType = realType.replace("MODULE_", "")
//            }
//            if (RecommendFragmentAbManager.is2024NewRecommendFragment()) {
//                var newRecType = "newRec_$realType"
//                if (TemplateManager.getInstance().hasTemplate(BUSINESS_NAME, newRecType)) {
//                    return TemplateManager.getInstance().getView(BUSINESS_NAME, newRecType)
//                }
//            } else if (NewShowNotesManager.userNewShowNotes() && RecommendItemNew.RECOMMEND_ITEM_TRACK == realType) {
//                var newRecType = "showNote_$realType"
//                if (TemplateManager.getInstance().hasTemplate(BUSINESS_NAME, newRecType)) {
//                    return TemplateManager.getInstance().getView(BUSINESS_NAME, newRecType)
//                }
//            }
//            return TemplateManager.getInstance().getView(BUSINESS_NAME, realType)
//        }
//    }
//
//    fun createColorString(color: Int): String {
//        val stringBuilder = StringBuilder("#")
//        val alpha = Integer.toHexString((Color.alpha(color)).toInt())
//        if (1 == alpha.length) {
//            stringBuilder.append("0").append(alpha)
//        } else {
//            stringBuilder.append(alpha)
//        }
//        val red = Integer.toHexString(Color.red(color))
//        if (1 == red.length) {
//            stringBuilder.append("0").append(red)
//        } else {
//            stringBuilder.append(red)
//        }
//        val green = Integer.toHexString(Color.green(color))
//        if (1 == green.length) {
//            stringBuilder.append("0").append(green)
//        } else {
//            stringBuilder.append(green)
//        }
//        val blue = Integer.toHexString(Color.blue(color))
//        if (1 == blue.length) {
//            stringBuilder.append("0").append(blue)
//        } else {
//            stringBuilder.append(blue)
//        }
//        return stringBuilder.toString()
//    }
//
//    private fun registerLocalTemplate() {
//        if (mIsDebug) {
////            mDebugTemplateMap[RecommendItemNew.RECOMMEND_ITEM_TRACK] =
////                RecommendPageTrackViewTemplate.BIN
//        }
//    }
//
//    public fun hasVVTemplateForItem(recommendItem: RecommendItemNew?): Boolean {
//        val type: String = if (RecommendItemNew.RECOMMEND_ITEM_MODULE == recommendItem?.itemType) {
//            val moduleItem = recommendItem?.item as? RecommendModuleItem
//            "${RecommendItemNew.RECOMMEND_ITEM_MODULE}_" + (moduleItem?.moduleType ?: "")
//        } else {
//            recommendItem?.itemType ?: ""
//        }
//        return hasTemplate(type)
//    }
//
//    private fun hasTemplate(type: String): Boolean {
//        // 只有MODE_MIX才走vv模板
//        if (RecommendFragmentAbManager.MODE_MIX != RecommendFragmentAbManager.mode) {
//            return false
//        }
//        // 无障碍模式禁止使用virtualView
//        if (AccessibilityUtil.isScreenReaderEnable()) {
//            return false
//        }
//        var hasTemplate = false
//        if (mIsDebug) {
//            hasTemplate = mDebugTemplateMap[type] != null
//        }
//        if (!hasTemplate) {
//            hasTemplate = TemplateManager.getInstance().hasTemplate(BUSINESS_NAME, type)
//        }
//        return hasTemplate
//    }
//
//    private fun loadTemplate(type: String) {
//        if (mIsDebug) {
//            val template = mDebugTemplateMap[type]
//            template?.let {
////                TemplateManager.getInstance().loadBinBufferSync(it)
//            }
//        }
//    }
//
//    private fun findItemRootView(view: View?): View? {
//        if (view !is IView || view.parent !is IView) {
//            return view
//        }
//        val parent = view.parent
//        if (parent !is View || parent !is IView) {
//            return view
//        }
//        return findItemRootView(parent)
//    }
//
//    private fun handleEvent(type: Int, eventData: EventData): Boolean {
//        val action = when (type) {
//            EventManager.TYPE_Click -> eventData.mVB?.action
//            EventManager.TYPE_LongCLick -> eventData.mVB?.longAction
//            else -> null
//        }
//        if (action.isNullOrEmpty()) {
//            return false
//        }
//        var handled = true
//
//        var tagData: Any? = null
//        var itemData: RecommendItemNew? = null
//        var view = findItemRootView(eventData.mView)
//        while (view != null && tagData == null) {
//            tagData = view.getTag(DATA_TAG)
//            if (view.parent !is View) {
//                break
//            }
//            view = view.parent as View?
//        }
//
//        // 广告点击处理
//        if (tagData is ItemModel<*> && tagData.viewType == VIEW_TYPE_AD_MIX_ANCHOR) {
//            handleAdEvent(tagData, action, eventData.mView)
//            return true
//        }
//        itemData = tagData as RecommendItemNew?
//        if (itemData == null) {
//            when {
//                action.startsWith("iting://") || action.startsWith("http://")
//                    || action.startsWith("https://") -> {
//                    mFragment?.let {
//                        ToolUtil.clickUrlAction(it, action, eventData.mView)
//                        return true
//                    }
//                }
//            }
//            return false
//        }
//        val pos = itemData.vvPosition
//
//        val headSize = (mFragment as? RecommendFragmentStaggered?)?.mAdapterAction?.headSize() ?: 0
//        val positionNew = itemData.vvPosition + 1 - headSize
//
//        if (mAdapterAction != null && eventData.mView != null) {
//            if (RecommendItemNew.RECOMMEND_ITEM_TRACK == itemData.itemType && itemData.item is RecommendTrackItem) {
//                handleTrackItem(eventData.mView, action, itemData, itemData.item as RecommendTrackItem, pos, positionNew)
//                return true
//            }
//            if (RecommendItemNew.RECOMMEND_ITEM_CHASING_TRACK == itemData.itemType) {
//                var recommendTrackItem = RecommendTrackItem()
//                kotlin.runCatching {
//                    var itemJsonObject = itemData.jsonObject.optJSONObject("item").optJSONArray("list")[0] as JSONObject
//                    val trackId = itemJsonObject.optLong("refId")
//                    var trackCover = itemJsonObject.optString("cover")
//                    var trackTitle = itemJsonObject.optString("title")
//                    recommendTrackItem.dataId = trackId
//                    recommendTrackItem.coverUrlLarge = trackCover
//                    recommendTrackItem.trackTitle = trackTitle
//                }
//                handleTrackItem(eventData.mView, action, itemData, recommendTrackItem, pos, positionNew)
//                return true
//            }
//        }
//        handled = handleAction(action, itemData, eventData.mView)
//        return handled
//    }
//
//    private fun handleAction(action: String, itemData: RecommendItemNew, view: View?): Boolean {
//        var handled = true
//        val pos = itemData.vvPosition
//        val headSize = (mFragment as? RecommendFragmentStaggered?)?.mAdapterAction?.headSize() ?: 0
//        val positionNew = itemData.vvPosition + 1 - headSize
//        when {
//            action.startsWith("iting://") || action.startsWith("http://")
//                    || action.startsWith("https://") -> {
//                mFragment?.let {
//                    val moduleItem = itemData.item
//                    if (moduleItem is RecommendModuleItem) {
//                        val list = moduleItem.list
//                        if (!list.isNullOrEmpty()) {
//                            val data = list[0] as? RecommendUserResearch
//                            if (data != null) {
//                                val builder = NativeHybridFragment.Builder()
//                                    .url(data.url)
//                                    .showTitle(true)
//                                    .webViewResultCallback { result ->
//                                        if (!result.isNullOrEmpty() && result[0] is String && result[0] == "userResearch") {
//                                            mAdapterAction?.remove(pos, false)
//                                        }
//                                    }
//                                it.startFragment(builder.builder())
//                                return handled
//                            }
//                        }
//                    }
//                    ToolUtil.clickUrlAction(it, action, view)
//                }
//            }
//            action == ACTION_JUMP -> {
//                if (RecommendItemNew.RECOMMEND_ITEM_ALBUM == itemData.itemType) {
//                    jumpToAlbumPage(itemData)
//                } else if (itemData.item is RecommendSpecialItem) {
//                    if (view != null) {
//                        gotoTingDetail(itemData.item as RecommendSpecialItem, itemData, view)
//                    }
//                }
//            }
//            action == ACTION_PLAY -> {
//            }
//            action == ACTION_DISLIKE -> {
//                if (RecommendItemNew.RECOMMEND_ITEM_ALBUM == itemData.itemType || RecommendItemNew.RECOMMEND_ITEM_TRACK == itemData.itemType) {
//                    if (RecommendFragmentAbManager.is2024NewRecommendFragment()) {
//                        showFeedBackDialogNew(pos, itemData, positionNew, view)
//                    } else {
//                         showAlbumMoreActionDialog(pos, itemData, positionNew)
//                    }
//                } else {
//                    // 负反馈通用逻辑
//                    showCommonFeedBackDialog(itemData, pos, positionNew)
//                }
//            }
//            action == ACTION_USER -> {
//                if (RecommendItemNew.RECOMMEND_ITEM_ALBUM == itemData.itemType) {
//                    val recommendAlbum = itemData.item as? RecommendAlbumItem
//                    recommendAlbum?.let {
//                        mFragment?.startFragment(newAnchorSpaceFragment(it.uid))
//                    }
//                }
//                if (RecommendItemNew.RECOMMEND_ITEM_CHASING_ALBUM == itemData.itemType) {
//                    var uid = 0L
//                    kotlin.runCatching {
//                        var itemJsonObject = itemData.jsonObject.optJSONObject("item").optJSONArray("list")[0] as JSONObject
//                        uid = itemJsonObject.optJSONObject("anchor").optLong("uid")
//                    }
//                    if (uid > 0) {
//                        mFragment?.startFragment(newAnchorSpaceFragment(uid))
//                    }
//                }
//            }
//            action == ACTION_REMOVE -> {
//                mAdapterAction?.remove(pos, true)
//            }
//            else -> handled = false
//        }
//        return handled
//    }
//
//    private fun showCommonFeedBackDialog(itemData: RecommendItemNew, position: Int, positionNew: Int) {
//        val viewType = itemData.viewType
//        val type = mViewTypeToTypeMap[viewType] ?: return
//        val templateDetail: XmLayout =
//            TemplateManager.getInstance().getTemplateDetail(BUSINESS_NAME, type) ?: return
//        val feedbackJson = ElParseUtil.parse(templateDetail.feedBackProps, itemData.jsonObject) ?: return
//
//        val dislikeDataType = feedbackJson.optString("dataType", "") // RecommendPageBottomDialog.DISLIKE_TYPE_ALBUM
//        val dislikeDataId = feedbackJson.optLong("dataId", 0L)
//        val dislikeTrackId = feedbackJson.optLong("trackId", 0L)
//        val traceVal = feedbackJson.optJSONObject("trace")
//
//        val itemJson = itemData.jsonObject?.optJSONObject("item")
//        val dislikeReasonModel: DislikeReasonModel? =
//            runCatching {
//                Gson().fromJson(
//                    itemJson?.optString("dislikeReasonNew"),
//                    DislikeReasonModel::class.java
//                )
//            }.getOrNull()
//        var doNotNeedAddData = false
//        if (itemData.jsonObject?.has("doNotNeedAddData") == true) {
//            doNotNeedAddData = itemData.jsonObject.getBoolean("doNotNeedAddData")
//        }
//        val dialog = RecommendPageBottomDialog(
//            mFragment?.activity, null, null, dislikeReasonModel,
//            dislikeDataType, dislikeDataId, dislikeTrackId, "discoveryFeed",
//            object : IDataCallBack<DislikeReasonNew> {
//                override fun onSuccess(`object`: DislikeReasonNew?) {
//                    showDislikeReasonToast(`object`)
//                    mAdapterAction?.remove(position, !doNotNeedAddData)
//                }
//
//                override fun onError(code: Int, message: String?) {
//                    CustomToast.showFailToast("操作失败")
//                    mAdapterAction?.remove(position, !doNotNeedAddData)
//                }
//            }, AlbumActionUtil.SOURCE_HOME_PAGE_STAGGERED, false
//        )
//            .setData(itemData)
//            .setBodyPosition(positionNew)
//            .setPos(position)
//            .setTrace(traceVal)
//
//        runCatching {
//            val dislikeReasonModelV1: List<DislikeReasonModelV1>? =
//                Gson().fromJson(
//                    itemJson?.optString("dislikeReasonNewV1"),
//                    object : TypeToken<List<DislikeReasonModelV1>>() {}.type
//                )
//            if (dislikeReasonModelV1 != null) {
//                dialog.setDislikeModeList(dislikeReasonModelV1)
//            }
//        }
//        setBottomDialogFeedbackClickCallback(dialog)
//        dialog.show()
//    }
//
//    private fun gotoTingDetail(
//        recommendSpecialItem: RecommendSpecialItem,
//        recommendItemNew: RecommendItemNew,
//        view: View
//    ) {
//        if (!TextUtils.isEmpty(recommendSpecialItem.aggregationIting)) {
//            try {
//                Router.getActionRouter<MainActionRouter>(Configure.BUNDLE_MAIN)!!
//                    .functionAction.handleITing(
//                        BaseApplication.getMainActivity(),
//                        Uri.parse(recommendSpecialItem.aggregationIting)
//                    )
//                return
//            } catch (e: Exception) {
//                e.printStackTrace()
//            }
//        }
//
//        if (RecommendItemNew.RECOMMEND_SPECIAL == recommendItemNew.itemType) {
//            if (recommendSpecialItem.specialId > 0 && recommendSpecialItem.contentType > 0) {
//                mFragment?.startFragment(
//                    NativeHybridFragment.newInstance(
//                        MainUrlConstants.getInstanse()
//                            .getSubjectDetailPageUrl(recommendSpecialItem.specialId.toString() + ""),
//                        true
//                    ), view
//                )
//            }
//        } else if (RecommendItemNew.RECOMMEND_UGC_SPECIAL == recommendItemNew.itemType) {
//            MyListenRouterUtil.getMyListenBundle { bundleModel: BundleModel? ->
//                val fragAction = MyListenRouterUtil.getFragAction()
//                if (fragAction != null && mFragment?.canUpdateUi() == true) {
//                    val frag = fragAction.newTingListDetailFragment(
//                        recommendSpecialItem.specialId,
//                        recommendSpecialItem.contentType
//                    )
//                    mFragment?.startFragment(frag)
//                }
//            }
//        }
//    }
//
//    private fun jumpToAlbumPage(itemData: RecommendItemNew) {
//        val activity = BaseApplication.getOptActivity()
//        val recommendAlbum = itemData.item as? RecommendAlbumItem ?: return
//        val option = AlbumEventManage.AlbumFragmentOption()
//        if (XmPlayerManager.getInstance(activity).isPlaying) {
//            option.isAutoPlay = false
//        } else {
//            option.isAutoPlay = ConfigureCenter.getInstance().getBool("toc", "feedalbumplay", false)
//        }
//        option.sourceTitle = recommendAlbum.albumTitle
//        option.sourceCover = recommendAlbum.validCover
//        option.sourceAnchorAvatar = recommendAlbum.logo ?: recommendAlbum.announcer?.avatarUrl
//        option.sourceAnchorName = recommendAlbum.announcer?.nickname
//        recommendAlbum.paidPage?.let {
//            option.paidPageCode = it.paidPageCode
//            option.pgcAlbumPageRouteResult = it.pgcAlbumPageRouteResult
//        }
//        AlbumEventManage.startMatchAlbumFragment(activity, recommendAlbum, AlbumEventManage.FROM_HOME_PAGE_RECOMMEND_FEED,
//            ConstantsOpenSdk.PLAY_FROM_OTHER, recommendAlbum.recommentSrc, recommendAlbum.recTrack, -1, option)
//        if (RecommendPersonalRecManager.needRequestRealTime()) {
//            mAdapterAction?.onItemAction(IRecommendFeedItemActionListener.FeedItemType.ALBUM, recommendAlbum.id,
//                IRecommendFeedItemActionListener.ActionType.CLICK, 0, itemData)
//        }
//    }
//
//    private fun handleTrackItem(itemView: View, action: String, item: RecommendItemNew, trackItem: RecommendTrackItem, pos: Int, positionNew: Int): Boolean {
//        if (action.startsWith("iting://") || action.startsWith("http://") || action.startsWith("https://")) {
//            if (action.startsWith("iting://")) {
//                mFragment?.let {
//                    ToolUtil.clickUrlAction(it, NewShowNotesManager.appendPodListModeParam(action), itemView)
//                }
//            } else {
//                mFragment?.let {
//                    ToolUtil.clickUrlAction(it, action, itemView)
//                }
//            }
//            return true
//        }
//        when (action) {
//            ACTION_SHARE_TO_MOMENT -> shareToMoment(item, trackItem, pos)
//            ACTION_SHARE_TO_WECHAT -> shareToWechat(item, trackItem, pos)
//            ACTION_PLAY, ACTION_JUMP -> playTrack(itemView, item, trackItem, pos)
//            ACTION_DISLIKE -> {
//                if (RecommendFragmentAbManager.is2024NewRecommendFragment()) {
//                    showFeedBackDialogNew(pos, item, positionNew, itemView)
//                } else {
//                    showMoreActionDialog(trackItem, pos, item, positionNew)
//                }
//            }
//            ACTION_SHOW_NOTE_PLAY -> {
//                playTrack(itemView, item, trackItem, pos)
//            }
//            ACTION_SHOW_NOTE_JUMP -> {
//                // 打开shownotes二级页
//                NewShowNotesManager.startShowNotesDetailFragment(NewShowNotesManager.SOURCE_FROM_HOME, NewShowNotesManager.appendPodListModeParam("iting://open?msg_type=11&track_id=" + trackItem.dataId), 0,
//                    trackItem.dataId, null)
//            }
//            ACTION_USER -> {
//                mFragment?.startFragment(newAnchorSpaceFragment(trackItem.uid))
////                RecommendStaggeredTraceManager.traceAnchorClick(trackItem.uid, RecommendStaggeredTraceManager.CONTENT_TYPE_TRACK, trackItem.dataId)
//            }
//            ACTION_ADD_TO_BE_PLAY -> addToBePlay(trackItem, pos, item, positionNew)
//            else -> return true
//        }
//        return true
//    }
//
//    private fun handleAdEvent(data: ItemModel<*>, action: String, view: View) {
//        if (data.tag !is IFeedAd) {
//            return
//        }
//        val feedAd: IFeedAd = data.tag as IFeedAd
//        val adModel: IAdModel = feedAd.adModel ?: return
//        var nativeAd: INativeAd = feedAd.nativeAd ?: return
//        val anchorAdInfo: IAnchorAdInfo? = adModel.anchorInfo
//        when (action) {
//            ACTION_DISLIKE_AD_LONG, ACTION_DISLIKE_AD -> {
//                feedAd.adClose()
//                val activity = BaseApplication.getOptActivity()
//                if (activity == null) {
//                    adClose(false, data.vvPosition, data.adIndex)
//                    return
//                }
//                var responseId: Long = 0
//                val responseIdInfo = AdSDKManager.getOtherInfo(nativeAd,
//                        INativeAd.OtherInfoKey.RESPONSE_ID)
//                if (responseIdInfo is Long) {
//                    responseId = responseIdInfo
//                }
//
//                var positionId = 0
//                val positionIdInfo = AdSDKManager.getOtherInfo(nativeAd,
//                        INativeAd.OtherInfoKey.POSITION_ID)
//                if (positionIdInfo is Int) {
//                    positionId = positionIdInfo
//                }
//
//                var adUserType = ""
//                val adUserTypeInfo = AdSDKManager.getOtherInfo(nativeAd,
//                        INativeAd.OtherInfoKey.AD_USER_TYPE)
//                if (adUserTypeInfo is String) {
//                    adUserType = adUserTypeInfo
//                }
//
//                adModel?.adid?.let {
//                    if (RecommendFragmentAbManager.is2024NewRecommendFragment()) {
//                        XmMoreFuncManager.checkShowMorePage(MoreFuncBuild().apply {
//                            this.isShowLevel2Dislike = true
//                            this.fragment2 = mFragment
//                            this.disLikeLeve2Build = DisLikeLeve2Build().apply {
//                                this.isFromAd = true
//                                this.onFeedBackListener =
//                                    object : NewXmFeedBackPopDialog.IOnFeedBackListener() {
//                                        override fun onDialogShow(showSuccess: Boolean) {
//
//                                        }
//
//                                        override fun onFeedBack(list: List<XmFeedInnerModel>) {
//                                            try {
//                                                val model = list.getOrNull(0)
//                                                feedAd.reportFeedback(
//                                                    model?.name,
//                                                    model?.codeType?.toInt() ?: -1
//                                                )
//                                            } catch (e: java.lang.Exception) {
//                                                e.printStackTrace()
//                                            }
//                                            feedAd.adRealCloseByUser()
//                                            adClose(true, data.vvPosition, data.adIndex)
//                                        }
//                                    }
//                            }
//                        })
//                    } else {
//                        var coverUrl = if (nativeAd.isXmNativeAd) anchorAdInfo?.iconUrl else nativeAd.cover
//                        AdDislikeBottomDialogX(activity, null, positionId.toString(), responseId, it, adUserType, coverUrl, { isAdCloseByFeedBack ->
//                            feedAd.adRealCloseByUser()
//                            adClose(isAdCloseByFeedBack, data.vvPosition, data.adIndex)
//                        }, null).showDialog()
//                    }
//                }
//            }
//            ACTION_USER ->
//                {
//                    mFragment?.startFragment(anchorAdInfo?.broadcasterId?.let { newAnchorSpaceFragment(it.toLong()) })
//                }
//        }
//    }
//
//    private fun adClose(isAdCloseByFeedBack: Boolean, vvPosition: Int, adIndex: Int) {
//        var feedAnchorAdManage: IFeedAnchorAdManagerStaggered? = null
//        if (mFragment is RecommendFragmentStaggered) {
//            feedAnchorAdManage = (mFragment as RecommendFragmentStaggered).mFeedAnchorAdManager
//        }
//        if (isAdCloseByFeedBack) {
//            if (feedAnchorAdManage != null && feedAnchorAdManage.dislikeAd(adIndex)) {
//                mAdapterAction?.remove(vvPosition)
//            }
//        } else {
//            mAdapterAction?.remove(vvPosition)
//        }
//    }
//
//    private fun playTrack(itemView: View, data: RecommendItemNew, track: RecommendTrackItem, position: Int) {
//        if (ToListenManager.shouldShowToListenNew() && ToListenManager.isToListenTrack(track)) {
//            ToListenManager.playToListenTracks(true, track, null)
//        } else {
//            NewShowNotesManager.setTempPodCastPlayListMode(true)
//            play(data, track, itemView, position, true)
//        }
//        notifyItemAction(track, IRecommendFeedItemActionListener.ActionType.CLICK, data)
//        // 新首页-声音（双列）  点击事件
//        val recInfo = track.recInfo
////        XMTraceApi.Trace()
////            .click(38747)
////            .put("rec_track", recInfo?.recTrack.realString())
////            .put("rec_src", recInfo?.recSrc.realString())
////            .put("reason_track", recInfo?.reasonTrack ?: "")
////            .put("reason_src", recInfo?.reasonSrc ?: "")
////            .put("recommendedLanguage", recInfo?.recReason ?: "")
////            .put("ubtTraceId", data.ubtTraceId ?: "")
////            .put("trackId", track.dataId.toString())
////            .put("anchorId", track.uid.toString())
////            .put("positionNew", data.newPos.toString())
////            .put("playCount", track.playCount.toString())
////            .put("style", RecommendStaggeredTraceManager.getStyle())
////            .put("currPage", "newHomePage")
////            .createTrace()
//    }
//
//    private fun notifyItemAction(track: RecommendTrackItem, click: IRecommendFeedItemActionListener.ActionType, data: RecommendItemNew) {
//        if (RecommendPersonalRecManager.needRequestRealTime()) {
//            mAdapterAction?.onItemAction(IRecommendFeedItemActionListener.FeedItemType.TRACK, track.dataId,
//                IRecommendFeedItemActionListener.ActionType.CLICK, 0, data)
//        }
//    }
//
//    private fun play(recommendItem: RecommendItemNew?, track: RecommendTrackItem?, view: View, position: Int, openPlay: Boolean) {
//        if (track == null) {
//            return
//        }
//
//        // 新首页信息流play_source
//        track.playSource = ConstantsOpenSdk.PLAY_FROM_NEW_HOME_TRACK
//        val playPageBundle = Bundle().apply {
//            putLong("source_content_id", track.dataId)
//            putLong("source_meta_id", 38747L)
//            putString("source_ubtTraceId", recommendItem?.ubtTraceId?: "")
//        }
//
//        if (PlayTools.isCurrentTrack(mFragment?.context, track)) {
//            if (!PlayTools.isCurrentTrackPlaying(mFragment?.context, track)) {
//                if (openPlay) {
//                    XmPlayerManager.getInstance(mFragment?.context).setPlayFragmentIsShowing(true)
//                }
//                XmPlayerManager.getInstance(mFragment?.context).play()
//            }
//            if (openPlay) {
//                val mainActivity = mFragment?.activity as? MainActivity
//                mainActivity?.showPlayFragment(view, 0, playPageBundle, PlayerManager.PLAY_TAG)
//            }
//        } else {
//            if (shouldLogin(track)) {
//                UserInfoMannage.gotoLogin(mFragment?.context)
//            } else {
//                val allowRecommendPlay = ConfigureCenter.getInstance().getBool("toc", "homepagefeedplay", false)
//                if (allowRecommendPlay && RecommendPersonalRecManager.needRequestRealTime()) {
//                    getRealTimeTrack(
//                        IRecommendFeedItemActionListener.FeedItemType.TRACK,
//                        track,
//                        IRecommendFeedItemActionListener.ActionType.CLICK,
//                        recommendItem,
//                        view,
//                        openPlay,
//                        playPageBundle
//                    )
//                } else {
//                    playHistory(track, view, openPlay, playPageBundle)
//                }
//            }
//        }
//    }
//
//    private fun getRealTimeTrack(itemType: IRecommendFeedItemActionListener.FeedItemType, track: RecommendTrackItem, actionType: IRecommendFeedItemActionListener.ActionType,
//                                 recommendItem: RecommendItemNew?, view: View, openPlay: Boolean, playPageBundle: Bundle? = null) {
//        val allowRecommendPlay =
//            ConfigureCenter.getInstance().getBool("toc", "homepagefeedplay", false)
//        RecommendFragmentRealTimeFeedManager.instance.getRealTimeTrack(track.dataId, itemType, recommendItem, allowRecommendPlay, track, openPlay, view, IRecommendFeedItemActionListener.ActionType.CLICK, playPageBundle)
//    }
//
//    private fun playHistory(track: Track, view: View, openPlay: Boolean, playPageBundle: Bundle? = null) {
//        PlayCompleteRecommendManager.getInstance().finishRecommend()
//        PlayTools.playTrackWithAlbum(mFragment?.activity, track, openPlay, playPageBundle)
//    }
//
//    private fun shareToMoment(data: RecommendItemNew?, track: RecommendTrackItem?, position: Int) {
//        ShareUtilsInMain.shareTrackWithoutXdcs(
//            BaseApplication.getMainActivity(), track,
//            IShareDstType.SHARE_TYPE_WX_CIRCLE,
//            ICustomShareContentType.SHARE_TYPE_TRACK
//        )
//        traceOnClickForShare(track, data, position)
//    }
//
//    private fun shareToWechat(data: RecommendItemNew?, track: RecommendTrackItem?, position: Int) {
//        ShareUtilsInMain.shareTrackWithoutXdcs(
//            BaseApplication.getMainActivity(), track,
//            IShareDstType.SHARE_TYPE_WX_FRIEND,
//            ICustomShareContentType.SHARE_TYPE_TRACK
//        )
//        traceOnClickForShare(track, data, position)
//    }
//
//    private fun traceOnClickForShare(
//        track: RecommendTrackItem?,
//        data: RecommendItemNew?,
//        position: Int
//    ) {
////        track ?: return
////        val recInfo = track?.recInfo
////        // 新首页-声音流分享  点击事件
////        XMTraceApi.Trace()
////            .click(27993, "trackFlow")
////            .put("trackId", "${track.dataId}")
////            .put("position", "$position")
////            .put("isAd", if (track.adInfo == null) "false" else "true")
////            .put("rec_src", "${recInfo?.recSrc}")
////            .put("rec_track", "${recInfo?.recTrack}")
////            .put("adId", "${track.adInfo?.adid}")
////            .put("reason_track", recInfo?.reasonTrack)
////            .put("reason_src", recInfo?.reasonSrc)
////            .put("albumId", "${track.album?.albumId}")
////            .put("categoryId", "${track.categoryId}")
////            .put("recommendedLanguage", recInfo?.recReason)
////            .put("currPage", "newHomePage")
////            .put("ubtTraceId", data?.ubtTraceId)
////            .put("style", RecommendStaggeredTraceManager.getStyle())
////            .put("positionNew", "${data?.newPos}")
////            .createTrace()
//    }
//
//    private fun setBottomDialogFeedbackClickCallback(dialog: RecommendPageBottomDialog) {
//        dialog.setFeedbackClickCallback(object : IDataCallBack<Boolean> {
//            override fun onSuccess(data: Boolean?) {
//                dialog.show()
//            }
//
//            override fun onError(code: Int, message: String?) {
//            }
//        })
//    }
//
//    private fun showDislikeReasonToast(`object`: DislikeReasonNew?) {
//        when (`object`?.type) {
//            ItemType.DISLIKE -> {
//                CustomToast.showSuccessToast("已为你屏蔽该内容")
//            }
//            ItemType.DISLIKE_ANCHOR -> {
//                CustomToast.showSuccessToast("将为你减少该主播创作的内容推荐")
//            }
//            else -> {
//                CustomToast.showSuccessToast("反馈成功，将为你优化推荐结果")
//            }
//        }
//    }
//
//    private fun addToBePlay(track: RecommendTrackItem, position: Int, data: RecommendItemNew?, positionNew: Int) {
//        if (track.dataId <= 0) {
//            return
//        }
//        if (ToListenManager.istoListenTrack(track.dataId)) {
//            return
//        }
//        val callback = object : IDataCallBack<CopyOnWriteArrayList<SimpleTrackForToListen>> {
//            override fun onSuccess(data: CopyOnWriteArrayList<SimpleTrackForToListen>?) {
//                mAdapterAction?.onItemChanged(position)
//            }
//
//            override fun onError(code: Int, message: String?) {
//            }
//        }
//        if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).playListSize == 0) {
//            playToListenTracks(false, track, callback)
//        } else {
//            addToListenTrack(true, true, track, null, null, callback, false)
//        }
//    }
//
//    private fun showMoreActionDialog(track: RecommendTrackItem, position: Int, data: RecommendItemNew?, positionNew: Int) {
//        val dialog = RecommendPageBottomDialog(
//            mFragment?.activity, null, null, track.dislikeReasonNew,
//            RecommendPageBottomDialog.DISLIKE_TYPE_TRACK, track.album?.albumId
//                ?: 0, track, "discoveryFeed",
//            object : IDataCallBack<DislikeReasonNew> {
//                override fun onSuccess(`object`: DislikeReasonNew?) {
//                    showDislikeReasonToast(`object`)
//                    mAdapterAction?.remove(position)
//                }
//
//                override fun onError(code: Int, message: String?) {
//                    CustomToast.showFailToast("操作失败")
//                    mAdapterAction?.remove(position)
//                }
//            }, AlbumActionUtil.SOURCE_HOME_PAGE_STAGGERED, false).setBodyPosition(positionNew).setDislikeModeList(track.dislikeReasonNewV1).setData(data)
//        setBottomDialogFeedbackClickCallback(dialog)
//        dialog.show()
//    }
//
//    private fun showFeedBackDialogNew(position: Int, itemData: RecommendItemNew?, positionNew: Int, view: View?) {
//        if (itemData == null) {
//            return
//        }
//        var coverImageView: View? = null
//        if (view is NativeLayoutImpl) {
//            coverImageView = view.virtualView.findViewBaseById(10086)?.nativeView
//        }
//        val requestMap = mutableMapOf<String, String>()
//        val traceMap = mutableMapOf<String, String>()
//        traceMap["currPage"] = "newHomePage"
//        traceMap["positionNew"] = (positionNew + 1).toString()
//        traceMap["xmRequestId"] = itemData.xmRequestId ?: ""
//        traceMap["contentType"] = itemData.itemType
//        var albumId = 0L
//        var trackId = 0L
//        var anchorId = 0L
//        var anchorNickName: String? = null
//        if (itemData.item is RecommendAlbumItem) {
//            val albumItem = itemData.item as? RecommendAlbumItem
//            traceMap["rec_src"] = albumItem?.recInfo?.recSrc ?: ""
//            traceMap["rec_track"] = albumItem?.recInfo?.recTrack ?: ""
//            traceMap["ubtTraceId"] = itemData.ubtTraceId ?: ""
//            albumId = albumItem?.id ?: 0L
//            anchorId = albumItem?.uid ?: 0L
//            anchorNickName = albumItem?.nickName
//            traceMap["contentId"] = albumId.toString()
//            requestMap[HttpParamsConstants.PARAM_ALBUM_ID] = albumId.toString()
//        } else if (itemData.item is RecommendTrackItem) {
//            val trackItem = itemData.item as? RecommendTrackItem
//            traceMap["rec_src"] = trackItem?.recSrc ?: ""
//            traceMap["rec_track"] = trackItem?.recTrack ?: ""
//            traceMap["ubtTraceId"] = itemData.ubtTraceId ?: ""
//            anchorId = trackItem?.uid ?: 0L
//            trackId = trackItem?.dataId ?: 0L
//            anchorNickName = trackItem?.nickName
//            traceMap["contentId"] = trackId.toString()
//            requestMap[HttpParamsConstants.PARAM_TRACK_ID] = trackId.toString()
//        }
//        if (coverImageView == null) {
//            coverImageView = view
//        }
//        requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
//        requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] = anchorId.toString()
//        XmFeedBackManager.showFeedBackDialogCommon(view, coverImageView, mFragment!!, anchorNickName, requestMap, traceMap,
//            object : XmFeedBackPopDialog.IOnFeedBackListener {
//            override fun onDialogShow(showSuccess: Boolean) {
//            }
//
//            override fun onFeedBack(feedPosition: Int, model: XmFeedInnerModel) {
//                mAdapterAction?.remove(position, false)
//            }
//        })
//    }
//
//    private fun showAlbumMoreActionDialog(position: Int, data: RecommendItemNew?, positionNew: Int) {
//        val recommendAlbum = data?.item as? RecommendAlbumItem ?: return
//        val dialog = RecommendPageBottomDialog(
//            mFragment?.activity, null, null, recommendAlbum.dislikeReasonNew,
//            RecommendPageBottomDialog.DISLIKE_TYPE_ALBUM, recommendAlbum, "discoveryFeed",
//            object : IDataCallBack<DislikeReasonNew> {
//                override fun onSuccess(`object`: DislikeReasonNew?) {
//                    if (mAdapterAction?.isPersonalRecOpen() == true) {
//                        showDislikeReasonToast(`object`)
//                        try {
//                            mAdapterAction?.remove(position, false)
//                        } catch (e: Exception) {
//                        }
//                    } else {
//                        CustomToast.showSuccessToast(R.string.main_dislike_disable_toast)
//                    }
//                }
//
//                override fun onError(code: Int, message: String?) {
//                    CustomToast.showFailToast("操作失败")
//                    mAdapterAction?.remove(position, false)
//                }
//            }, AlbumActionUtil.SOURCE_HOME_PAGE_STAGGERED, false)
//            .setBodyPosition(positionNew)
//            .setDislikeModeList(recommendAlbum.dislikeReasonNewV1)
//            .setData(data)
//        setBottomDialogFeedbackClickCallback(dialog)
//        dialog.show()
//    }
//
//    private fun shouldLogin(track: Track): Boolean {
//        return track.isPaid && !track.isAudition && !track.isFree && !UserInfoMannage.hasLogined()
//    }
//}