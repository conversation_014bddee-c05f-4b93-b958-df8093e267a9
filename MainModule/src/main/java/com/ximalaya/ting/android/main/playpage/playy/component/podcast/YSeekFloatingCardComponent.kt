package com.ximalaya.ting.android.main.playpage.playy.component.podcast

import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.play.PlayPageShowNotesModel
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.internalservice.ISeekBarDragListener
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.playy.IPlayContainer
import com.ximalaya.ting.android.main.playpage.playy.biz.BizArbiters
import com.ximalaya.ting.android.main.playpage.playy.biz.BizResource
import com.ximalaya.ting.android.main.playpage.playy.biz.SeekBarFloating
import com.ximalaya.ting.android.main.playpage.playy.component.base.BaseComponentWithPlayStatusListener
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import kotlin.math.max

class YSeekFloatingCardComponent(
    private val bizArb: BizArbiters
) : BaseComponentWithPlayStatusListener(), BizResource {
    private var mRecyclerView: RecyclerView? = null
    private var mContainerView: View? = null
    private var mAdapter: SeekCardAdapter? = null
    private var mItemClickListener: OnItemClickListener? = null
    private var mLinearLayoutManager: LinearLayoutManager? = null
    private var mShowNotes: List<PlayPageShowNotesModel>? = null
    private var mTouchProgress: Int = 0
    private var mOnScrollListener: RecyclerView.OnScrollListener? = null

    interface OnItemClickListener {
        fun onClick(position: Int, cardModel: PlayPageShowNotesModel?)
    }

    // seekbar 资源位逻辑

    override fun provideView(): View? {
        if (mShowNotes.isNullOrEmpty()) return null
        if (mContainerView == null) {
            initUi()
        }
        return mContainerView
    }

    override fun onShow() {
        PlayPageInternalServiceManager.getInstance().registerService(ISeekBarDragListener::class.java, mSeekBarListener)
        HandlerManager.removeCallbacks(mTraceRunnable)
        HandlerManager.postOnUIThreadDelay(mTraceRunnable, 100)
    }

    override fun onHide() {
        PlayPageInternalServiceManager.getInstance().unRegisterService(ISeekBarDragListener::class.java)
    }

    //seekbar 资源位 end

    fun initUi() {
        if (mRecyclerView == null) {
            mContainerView = mLayoutInflater.inflate(R.layout.main_layout_audio_xplay_page_seek_guide_card, null)
            mRecyclerView = mContainerView!!.findViewById(R.id.main_rv_seek_card)
            val width = 180 * BaseUtil.getScreenWidth(mContext) / 375
            val mTypeface = Typeface.createFromAsset(mContext.resources.assets, "fonts/XmlyNumberV1.0-Regular.otf")
            mAdapter = SeekCardAdapter(-1, if (mShowNotes == null) mutableListOf() else mShowNotes!!, width, mTypeface)
            mRecyclerView!!.adapter = mAdapter
            mLinearLayoutManager = LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
            mRecyclerView!!.itemAnimator = null
            mRecyclerView!!.layoutManager = mLinearLayoutManager
            mItemClickListener = object : OnItemClickListener {
                override fun onClick(position: Int, cardModel: PlayPageShowNotesModel?) {
                    scrollToPositionInner(position)
                    cardModel?.let {
                        XmPlayerManager.getInstance(mContext).seekTo(it.startAt.toInt())
                        XmPlayerManager.getInstance(mContext).play()
                        val isFullScreen = (mFragment as? IPlayContainer)?.isFullScreen() == true
                        // 新声音播放页-分段信息卡片  点击事件
                        XMTraceApi.Trace()
                            .click(56008) // 用户点击时上报
                            .put("currPage", "newPlay")
                            .put("currTrackId", curTrackId.toString())
                            .put("currAlbumId", curAlbumId.toString())
                            .put("fullScreenMode", if (isFullScreen) "full" else "half") // full 表示全屏，half 表示半屏
                            .put("trackForm", "track") //  track表示音频，video 表示视频
                            .put("anchorId", curAnchorId.toString())
                            .put("id", it.id.toString()) // 记录对应的分段 id
                            .put("time", it.getTimeStampStr()) // 记录对应的分段时间，以时:分:秒的形式，例如 00:00:10
                            .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                            .createTrace()
                    }
                }
            }
            mAdapter!!.mItemClickListener = mItemClickListener
            mOnScrollListener = object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                        HandlerManager.removeCallbacks(mTraceRunnable)
                        HandlerManager.postOnUIThreadDelay(mTraceRunnable, 100)
                    }
                }
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    HandlerManager.removeCallbacks(mTraceRunnable)
                    HandlerManager.postOnUIThreadDelay(mTraceRunnable, 100)
                }
            }
            mRecyclerView!!.addOnScrollListener(mOnScrollListener!!)
        }
    }

    override fun provideFragment(): Fragment? = null

    /**
     * @params：progressTime
     * 进度条移动的progress，不是播放器
     * */
    fun onShow(progressTime: Long) {
        if (mShowNotes.isNullOrEmpty()) {
            return
        }
        // 懒加载
        initUi()
        if (mAdapter == null) {
            return
        }
        mAdapter!!.cardList = mShowNotes!!
        var originalIndex = -1
        var maxPointTime = 0L
        mShowNotes!!.forEachIndexed { index, playPageShowNotesModel ->
            if (progressTime < playPageShowNotesModel.startAt && originalIndex == -1) {
                originalIndex = index - 1
            }
            maxPointTime = max(maxPointTime, playPageShowNotesModel.startAt)
        }
        if (progressTime > maxPointTime) {
            // 已经超过最大值，选中最后一个
            originalIndex = mShowNotes!!.size - 1
        }
        mAdapter!!.mSelectedPosition = originalIndex
        mAdapter!!.notifyDataSetChanged()
        scrollToPositionInner(originalIndex)
    }

    private fun updatePosition() {
        if (mAdapter?.cardList.isNullOrEmpty() || mShowNotes == null) {
            return
        }
        HandlerManager.removeCallbacks(mUpdateRunnable)
        HandlerManager.postOnUIThread(mUpdateRunnable)
    }

    private val mUpdateRunnable = Runnable {
        if (mAdapter?.cardList.isNullOrEmpty() || mShowNotes == null) {
            return@Runnable
        }
        val originalPosition = mAdapter!!.mSelectedPosition
        var newIndex = -1
        var maxPointTime = 0L
        mAdapter!!.cardList.forEachIndexed { index, playPageShowNotesModel ->
            if (mTouchProgress < playPageShowNotesModel.startAt && newIndex == -1) {
                newIndex = index - 1
            }
            maxPointTime = max(maxPointTime, playPageShowNotesModel.startAt)
        }
        if (mTouchProgress > maxPointTime) {
            // 已经超过最大值，选中最后一个
            newIndex = mShowNotes!!.size - 1
        }
        if (newIndex == originalPosition) {
            return@Runnable
        }
        mAdapter!!.mSelectedPosition = newIndex
        if (originalPosition >= 0) {
            try {
                mAdapter!!.notifyItemChanged(originalPosition)
            } catch (e: Exception) {
                mAdapter!!.notifyDataSetChanged()
            }
        }
        if (newIndex >= 0) {
            try {
                mAdapter!!.notifyItemChanged(newIndex)
            } catch (e: Exception) {
                mAdapter!!.notifyDataSetChanged()
            }
            scrollToPositionInner(newIndex)
        }
    }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        super.onSoundInfoLoaded(soundInfo)
        mShowNotes = if (soundInfo == null || !soundInfo.hasShowNotes()) {
            bizArb.unRegister(SeekBarFloating.PODCAST)
            null
        } else {
            bizArb.register(SeekBarFloating.PODCAST, this)
            soundInfo.showNotes!!
        }
        mAdapter?.mSelectedPosition = -1
    }

    fun scrollToPositionInner(position: Int) {
        if (position < 0) {
            mRecyclerView?.smoothScrollToPositionWithOffset(0, 48.dp)
            return
        }
        mRecyclerView?.smoothScrollToPositionWithOffset(position, 48.dp)
    }

    var mSeekBarListener: ISeekBarDragListener = object : ISeekBarDragListener {
        override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
            mTouchProgress = progress
            if (fromUser) {
                updatePosition()
            }
        }
        override fun onStartTrackingTouch(seekBar: SeekBar) {
            onShow(seekBar.progress.toLong())
        }
        override fun onStopTrackingTouch(seekBar: SeekBar) {
        }
    }

    override fun onCreate(fragment: BaseFragment2?) {
        super.onCreate(fragment)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mOnScrollListener != null) {
            mRecyclerView?.removeOnScrollListener(mOnScrollListener!!)
        }
        PlayPageInternalServiceManager.getInstance().unRegisterService(ISeekBarDragListener::class.java)
    }

    /**
     * RecyclerView的各种滑动方法都太tm恶心
     * */
    private fun RecyclerView.smoothScrollToPositionWithOffset(position: Int, offset: Int) {
        val linearSmoothScroller = object : LinearSmoothScroller(context) {
            override fun onTargetFound(targetView: View, state: RecyclerView.State, action: Action) {
                super.onTargetFound(targetView, state, action)
                val dx = calculateDxToMakeVisible(targetView, SNAP_TO_START)
                val dy = calculateDyToMakeVisible(targetView, verticalSnapPreference)

                val distance = kotlin.math.sqrt((dx * dx + dy * dy).toDouble()).toInt()
                val time = calculateTimeForDeceleration(distance)
                if (time > 0) {
                    action.update(-dx - offset, -dy, time, mDecelerateInterpolator)
                }
            }
        }
        linearSmoothScroller.targetPosition = position
        layoutManager?.startSmoothScroll(linearSmoothScroller)
    }

    private val mTraceRunnable = Runnable {
        if (mLinearLayoutManager == null || mRecyclerView == null || !canUpdateUi() || mAdapter == null) {
            return@Runnable
        }
        val firstPosition: Int = mLinearLayoutManager!!.findFirstVisibleItemPosition()
        val lastPosition: Int = mLinearLayoutManager!!.findLastVisibleItemPosition()
        if (firstPosition == -1 || lastPosition == -1) {
            return@Runnable
        }
        val isFullScreen = (mFragment as? IPlayContainer)?.isFullScreen() == true
        for (position in firstPosition..lastPosition) {
            if (mAdapter!!.cardList.size > position) {
                // 新声音播放页-分段信息卡片  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(56009)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "newPlay")
                    .put("currTrackId", curTrackId.toString())
                    .put("currAlbumId", curAlbumId.toString())
                    .put("fullScreenMode", if (isFullScreen) "full" else "half") // full 表示全屏，half 表示半屏
                    .put("trackForm", "track") // track 表示音频，video 表示视频
                    .put("anchorId", curAnchorId.toString())
                    .put("id", mAdapter!!.cardList[position].id.toString()) // 记录对应的分段 id
                    .put("time", mAdapter!!.cardList[position].getTimeStampStr()) // 记录对应的分段时间，以时:分:秒的形式，例如 00:00:10
                    .put(XmRequestIdManager.CONT_ID, mAdapter!!.cardList[position].id.toString())
                    .put(XmRequestIdManager.CONT_TYPE, "newPlaySeekbar")
                    .put(
                        XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                            XmRequestPage.PAGE_PLAY_PAGE))
                    .createTrace()
            }
        }
    }

    class SeekCardAdapter(var mSelectedPosition: Int, var cardList: List<PlayPageShowNotesModel>, var mItemWidth: Int, var typeface: Typeface?) :
        RecyclerView.Adapter<SeekCardAdapter.CardViewHolder>() {
        var mItemClickListener: OnItemClickListener? = null
        override fun onCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): CardViewHolder {
            return CardViewHolder(
                LayoutInflater.from(
                    BaseApplication.getMyApplicationContext()
                ).inflate(
                    R.layout.main_item_audio_xplay_page_seek_guide_card, parent,
                    false
                ),
                mItemWidth
            )
        }

        override fun onBindViewHolder(holder: CardViewHolder, position: Int) {
            val cardItem = cardList.getOrNull(position) ?: return

            val selected = mSelectedPosition == position
            holder.viewContainer?.isSelected = selected
            holder.tvTitle?.isSelected = selected
            holder.tvTime?.isSelected = selected
            holder.tvTime?.typeface = typeface

            holder.tvTitle?.text = cardItem.summary
            holder.tvTime?.text = cardItem.getTimeStampStr()
            holder.itemView.setOnClickListener {
                val lastSelectPosition = mSelectedPosition
                mSelectedPosition = position
                try {
                    notifyItemChanged(lastSelectPosition)
                    notifyItemChanged(position)
                } catch (e: Exception) {
                    notifyDataSetChanged()
                }
                mItemClickListener?.onClick(position, cardItem)
            }
        }

        override fun getItemCount(): Int {
            return cardList.size
        }

        class CardViewHolder(view: View, mItemWidth: Int) : RecyclerView.ViewHolder(view) {
            var viewContainer: View?
            var tvTitle: TextView?
            var tvTime: TextView?
            init {
                viewContainer = view.findViewById(R.id.main_container_layout)
                tvTitle = view.findViewById(R.id.main_tv_title)
                tvTime = view.findViewById(R.id.main_tv_time_stamp)
                if (viewContainer!!.layoutParams is ViewGroup.LayoutParams) {
                    viewContainer!!.layoutParams.width = mItemWidth
                    viewContainer!!.layoutParams.height = 66.dp
                }
            }
        }
    }
}