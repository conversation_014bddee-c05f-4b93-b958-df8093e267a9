package com.ximalaya.ting.android.main.adapter.find.other;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ClickableSpan;
import android.text.style.ImageSpan;
import android.text.style.StyleSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.ViewFlipper;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constants.CommentConstants;
import com.ximalaya.ting.android.host.data.model.message.RequestError;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.listener.ICollectWithFollowStatusCallback;
import com.ximalaya.ting.android.host.manager.account.AnchorCollectManage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.RecommendAlbumCard;
import com.ximalaya.ting.android.host.model.album.AlbumCollectParam;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.AlbumRankInfo;
import com.ximalaya.ting.android.host.model.album.TagResult;
import com.ximalaya.ting.android.host.model.playcard.HotCommentBean;
import com.ximalaya.ting.android.host.model.playcard.TrackResult;
import com.ximalaya.ting.android.host.util.common.TimeHelper;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.AlbumTagUtilNew;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.util.view.CenterAlignImageSpan;
import com.ximalaya.ting.android.host.util.view.PlayCardImageSpan;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.commentModule.fragment.FloatingTrackCommentFragment;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

public class RecommendAlbumCardAdapterC extends AbRecyclerViewAdapter<RecommendAlbumCardAdapterC.ViewHolder> implements View.OnClickListener {

    public static final int DEFAULT_BACKGROUND_COLOR = 0xffa0a4a8;
    private BaseFragment2 mFragment;
    private Context mContext;
    private List<RecommendAlbumCard> mListData;
    private ViewHolder curHolder;
    private AlbumM curAlbum;
    private boolean canUpdateSeekBar = true;
    private MyRunnable myRunnable = new MyRunnable();
    private final String TIME_FORMAT = "%s / %s";

    public RecommendAlbumCardAdapterC(BaseFragment2 fragment, List<RecommendAlbumCard> list) {
        this.mFragment = fragment;
        this.mContext = fragment.getContext();
        this.mListData = list;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public RecommendAlbumCard getItem(int position) {
        if (mListData != null && position >= 0 && position < mListData.size()) {
            return mListData.get(position);
        }
        return null;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(
                R.layout.main_item_recommend_album_card_c, parent, false);
        return new ViewHolder(view, mContext);
    }

    @Override
    public void onBindViewHolder(@NonNull RecommendAlbumCardAdapterC.ViewHolder holder, int position) {
        RecommendAlbumCard recommendAlbumCard = getItem(position);
        if (recommendAlbumCard == null) {
            return;
        }
        AlbumM albumM = recommendAlbumCard.getAlbumM();
        if (albumM == null) {
            return;
        }
        ImageManager.from(mContext).displayImage(holder.cover, albumM.getValidCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album);
        AlbumTagUtilNew.getInstance().loadImage(holder.tag, albumM.getAlbumSubscriptValue());

        if (albumM.getPlayCount() > 0) {
            String playTimesStr = StringUtil.getFriendlyNumStr(albumM.getPlayCount());
            boolean hasSuffix = albumM.getPlayCount() >= 10000;
            int len = playTimesStr.length();
            SpannableString ss = new SpannableString("" + playTimesStr);
            ss.setSpan(new AbsoluteSizeSpan(BaseUtil.sp2px(mContext, 17)),
                    0, len, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            ss.setSpan(new StyleSpan(Typeface.BOLD), 0, len , Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            if (hasSuffix) {
                ss.setSpan(new AbsoluteSizeSpan(BaseUtil.sp2px(mContext, 11)),
                        len - 1, len, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
//                ss.setSpan(new StyleSpan(Typeface.NORMAL),
//                        len - 1, len, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
            holder.playCount.setText(ss);
        } else {
            holder.playCount.setText("0");
        }

        if (albumM.getScore() <= 0) {
            holder.albumScoreDsp.setVisibility(View.GONE);
            holder.albumScore.setText("暂无");
            holder.commentCount.setText("评分");
            //holder.commentCount.setCompoundDrawablesWithIntrinsicBounds(0,0,0,0);
        } else {
            holder.albumScoreDsp.setVisibility(View.VISIBLE);
            holder.albumScore.setText(""+albumM.getScore());
            String commentsCount = StringUtil.getFriendlyNumStr(albumM.getCommentsCounts());
            holder.commentCount.setText("" + commentsCount + "评论");
            //holder.commentCount.setCompoundDrawablesWithIntrinsicBounds(0,0,albumM.getCommentsCounts() > 0 ? R.drawable.host_ic_jump_n_n_line_regular_12 : 0, 0);

        }

        String albumTitle = albumM.getAlbumTitle();
        if (!TextUtils.isEmpty(albumTitle)) {
            setAlbumTitleWithTag(holder, albumM, albumTitle);
        } else {
            holder.albumTitle.setText(albumTitle);
        }

        if (albumM.getAlbumRankInfo() != null) {
            AlbumRankInfo albumRankInfo = albumM.getAlbumRankInfo();
            holder.llRank.setVisibility(View.VISIBLE);
            holder.divRank.setVisibility(View.VISIBLE);
            String[] split = albumRankInfo.getShowLabel().split(" ");
            holder.tvRank.setText("NO." + albumRankInfo.getPositionChange());
            if (split.length > 1) {
                holder.tvRankDsp.setText("" + split[split.length - 1]);
            } else {
                holder.tvRankDsp.setText("" + albumM.getCategory());
            }
            holder.llRank.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!OneClickHelper.getInstance().onClick(v)) {
                        return;
                    }
                    // 播放卡片-排行榜  点击事件
                    new XMTraceApi.Trace()
                            .click(54773) // 用户点击时上报
                            .put("currPage", "playCard")
                            .put("Item", "" + albumRankInfo.getShowLabel())
                            .put("albumId", "" + albumM.getId())
                            .put(XmRequestIdManager.XM_REQUEST_ID, albumM.getRequestId())
                            .createTrace();
                    goToIting(albumRankInfo.getIting());
                }
            });
        } else {
            holder.llRank.setVisibility(View.GONE);
            holder.divRank.setVisibility(View.GONE);
        }

        if (albumM.getIsFinished() == 2 || albumM.getSerialState() == 2 || albumM.isCompleted()) {
            holder.tvFinish.setVisibility(View.VISIBLE);
        } else {
            holder.tvFinish.setVisibility(View.GONE);
        }

        //标签简介
//        for (int i = 2; i < holder.llTags.getChildCount(); i++) {
//            holder.llTags.removeViewAt(i);
//        }
        holder.llTags.removeAllViews();
        if (!ToolUtil.isEmptyCollects(albumM.getTagResults())) {
            for (TagResult tagResult : albumM.getTagResults()) {
                if (tagResult != null && !TextUtils.isEmpty(tagResult.getTagName())) {
                    holder.llTags.addView(buildAlbumTagView(tagResult, albumM), getLayoutParams());
                }
            }

        }

        if (!TextUtils.isEmpty(albumM.getAlbumIntro())) {
            //设置简介-即推荐理由  click
            setAlbumIntroWithTag(holder,albumM,albumM.getAlbumIntro());
        } else {
            holder.albumIntro.setText("");
        }

        // 集数
        holder.tvEpisodes.setText("共"+albumM.getIncludeTrackCount()+"集");
        holder.tvMoreEpisodes.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                // 播放卡片-查看更多节目  点击事件
                new XMTraceApi.Trace()
                        .click(54768) // 用户点击时上报
                        .put("currPage", "playCard")
                        .put("albumId", ""+albumM.getId())
                        .put(XmRequestIdManager.XM_REQUEST_ID, albumM.getRequestId())
                        .createTrace();
                gotoAlbumFragment(albumM);
            }
        });

        //热评相关
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) holder.rlPlay.getLayoutParams();
        Track lastTrack = XmPlayerManager.getInstance(mContext).getLastPlayTrackInAlbum(albumM.getId());
        boolean canShowHotComment = lastTrack == null || lastTrack.getDataId() == albumM.getFirstTrackId();
        if (canShowHotComment && recommendAlbumCard.getHotCommentList() != null && recommendAlbumCard.getHotCommentList().size() > 0) {
            holder.llComment.setVisibility(View.VISIBLE);
            holder.trackTitle.setVisibility(View.GONE);
            layoutParams.bottomMargin = BaseUtil.dp2px(mContext, 18);
            int count = Math.min(recommendAlbumCard.getHotCommentList().size(), 5);
            for (int i = 0; i < count; i++) {
                HotCommentBean hotCommentBean = recommendAlbumCard.getHotCommentList().get(i);
                holder.viewFlipper.addView(buildCommentTextView("" + hotCommentBean.getTitle(), hotCommentBean.getCommentId(), albumM));
            }
            holder.viewFlipper.getInAnimation().setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {
                }
                @Override
                public void onAnimationEnd(Animation animation) {
                    int index = holder.viewFlipper.getDisplayedChild() % count;
                    if (index < count) {
                        HotCommentBean hotCommentBean = recommendAlbumCard.getHotCommentList().get(index);
                        // 播放卡片-热评  控件曝光
                        new XMTraceApi.Trace()
                                .setMetaId(55152)
                                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                                .put("currPage", "playCard")
                                .put("commentId", ""+hotCommentBean.getCommentId())
                                .put("trackId", ""+albumM.getFirstTrackId())
                                .put(XmRequestIdManager.CONT_ID, String.valueOf(albumM.getId()))
                                .put(XmRequestIdManager.CONT_TYPE, "playCard")
                                .put(XmRequestIdManager.XM_REQUEST_ID, albumM.getRequestId())
                                .createTrace();
                    }
                }
                @Override
                public void onAnimationRepeat(Animation animation) {
                }
            });
            holder.viewFlipper.setFlipInterval(6000);
        } else {
            holder.viewFlipper.stopFlipping();
            holder.viewFlipper.getInAnimation().setAnimationListener(null);
            holder.llComment.setVisibility(View.GONE);
            holder.trackTitle.setVisibility(View.VISIBLE);
            layoutParams.bottomMargin = BaseUtil.dp2px(mContext, 14);
        }

        //AnimationUtil.stopAnimation(holder.loadingStatus);
        //showLoadingView(holder, PlayTools.isAlbumBuffering(mContext, albumM.getId()));
        updatePlayStatus(holder, albumM, recommendAlbumCard.getTrackResult(), recommendAlbumCard.getHotCommentList());

        ViewStatusUtil.setOnClickListener(R.id.main_tag_default_id, albumM, this,
                holder.playContainer, holder.cover);
        AutoTraceHelper.bindData(holder.playContainer, AutoTraceHelper.MODULE_DEFAULT, albumM);
    }

    //专辑标题+订阅按钮
    private void setAlbumTitleWithTag(@NonNull ViewHolder holder, AlbumM albumM, final String mAlbumTitle) {
        holder.albumTitle.post(new Runnable() {
            @Override
            public void run() {
                String albumTitle = mAlbumTitle;
                String tail = " 订阅";
                String realTitle = albumTitle+tail;
                float originTextWidth = holder.albumTitle.getPaint().measureText(albumTitle+tail);
                float textViewWidth = holder.albumTitle.getWidth();
                if (originTextWidth + 147 > textViewWidth * 2 && textViewWidth > 0) {
                    while (originTextWidth + 147 > textViewWidth * 2) {
                        albumTitle = albumTitle.substring(0, albumTitle.length() - 1);
                        realTitle = albumTitle + "..." + tail;
                        originTextWidth = holder.albumTitle.getPaint().measureText(realTitle);
                    }
                }

                SpannableString title = new SpannableString(realTitle);
                Drawable d = ContextCompat.getDrawable(mContext, (albumM.isFavorite()? R.drawable.main_ic_unsubscript_playcard : R.drawable.main_ic_subscribe_playcard));
                if (d != null) {
                    int width = d.getIntrinsicWidth();
                    int height = d.getIntrinsicHeight();
                    d.setBounds(0, 0, width, height);
                    CenterAlignImageSpan imageSpan = new CenterAlignImageSpan(d);
                    //title.setSpan(imageSpan, title.length() - tail.length() + 1, title.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    title.setSpan(imageSpan, title.length() - tail.length() + 1, title.length(), ImageSpan.ALIGN_CENTER);
                    title.setSpan(new ClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            if (!OneClickHelper.getInstance().onClick(widget)) {
                                return;
                            }
                            // 播放卡片-订阅按钮  点击事件
                            new XMTraceApi.Trace()
                                    .click(54771) // 用户点击时上报
                                    .put("currPage", "playCard")
                                    .put("Item", albumM.isFavorite()? "已订阅" : "订阅")
                                    .put("albumId", ""+albumM.getId())
                                    .put(XmRequestIdManager.XM_REQUEST_ID, albumM.getRequestId())
                                    .createTrace();
                            doSubscribe(holder, albumM);
                        }
                    }, title.length() - tail.length() + 1, title.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);

                    title.setSpan(new ClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            // 播放卡片-专辑标题  点击事件
                            new XMTraceApi.Trace()
                                    .click(54766) // 用户点击时上报
                                    .put("currPage", "playCard")
                                    .put("rec_track", albumM.getRecTrack())
                                    .put("rec_src", albumM.getRecommentSrc())
                                    .put("ubtTraceId", ""+albumM.getUbtTraceId())
                                    .put("albumId", ""+albumM.getId())
                                    .put(XmRequestIdManager.XM_REQUEST_ID, albumM.getRequestId())
                                    .createTrace();
                            gotoAlbumFragment(albumM);
                        }

                        @Override
                        public void updateDrawState(@NonNull TextPaint ds) {
                            super.updateDrawState(ds);
                            ds.setColor(Color.parseColor("#131313"));
                            ds.setUnderlineText(false);
                        }
                    }, 0, title.length() - tail.length() + 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    holder.albumTitle.setMovementMethod(LinkMovementMethod.getInstance());
                }
                holder.albumTitle.setText(title);
            }
        });
    }

    //专辑标题+订阅按钮
    private void setAlbumIntroWithTag(@NonNull ViewHolder holder, AlbumM albumM, String mAlbumIntro) {
        if (TextUtils.isEmpty(mAlbumIntro)) {
            holder.albumIntro.setVisibility(View.GONE);
        }
        holder.albumIntro.post(new Runnable() {
            @Override
            public void run() {
                String albumintro = mAlbumIntro;
                String tail = "更";//更多
                String realIntro = albumintro;
                float originTextWidth = holder.albumIntro.getPaint().measureText(albumintro + tail);
                float textViewWidth = holder.albumIntro.getWidth();
                if (originTextWidth > textViewWidth * 2 && textViewWidth > 0) {
                    while (originTextWidth + 102 > textViewWidth * 2) {
                        albumintro = albumintro.substring(0, albumintro.length() - 1);
                        realIntro = albumintro + "..." + tail;
                        originTextWidth = holder.albumIntro.getPaint().measureText(realIntro);
                    }

                    SpannableString intro = new SpannableString(realIntro);
                    //根据id从资源文件中获取图片对象
                    Drawable d = ContextCompat.getDrawable(mContext, R.drawable.main_ic_more_playcard);
                    if (d != null) {
                        int width = d.getIntrinsicWidth();
                        int height = d.getIntrinsicHeight();
                        d.setBounds(0, 0, width, height);
                        CenterAlignImageSpan imageSpan = new CenterAlignImageSpan(d);
                        intro.setSpan(imageSpan, intro.length() - tail.length(), intro.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    }
                    holder.albumIntro.setText(intro);
                } else {
                    holder.albumIntro.setText(mAlbumIntro);
                }

                holder.albumIntro.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (!OneClickHelper.getInstance().onClick(v)) {
                            return;
                        }
                        new XMTraceApi.Trace()
                                .click(30945)
                                .put("albumId", String.valueOf(albumM.getId()))
                                .put("rec_track", albumM.getRecTrack())
                                .put("rec_src", albumM.getRecommentSrc())
                                .put("currPage", "playCard")
                                .put(XmRequestIdManager.XM_REQUEST_ID, albumM.getRequestId())
                                .createTrace();
                        gotoAlbumFragment(albumM);
                    }
                });
            }
        });
    }

    private void doSubscribe(ViewHolder holder,AlbumM albumM) {
        AlbumCollectParam albumCollectParam = new AlbumCollectParam(
                "播放卡片",
                AnchorCollectManage.SUBSCRIBE_BIZ_TYPE_PLAY_CARD,
                0,
                0,
                albumM, true);
        albumCollectParam.setShowFollowDialog(false);
        AlbumEventManage.doCollectActionV3WithLoginCheckDeferredAction(albumCollectParam, mFragment, new ICollectWithFollowStatusCallback() {
            @Override
            public void followDialogAction(int status) {
            }
            @Override
            public void onCollectSuccess(int code, boolean isCollected) {
                if (mFragment.canUpdateUi()){
                    albumM.setFavorite(isCollected);
                    setAlbumTitleWithTag(holder, albumM, albumM.getAlbumTitle());
                    if (code == RequestError.CODE_ALBUM_ALREADY_SUBSCRIBE) {
                        CustomToast.showToast("亲，您已经订阅过该专辑了哦");
                    }
                }
            }
            @Override
            public void onError() {
            }
        }, "searchResult");
    }

    private View buildAlbumRankView(String rankInfo)  {
            TextView tv = new TextView(mContext);
            tv.setText(rankInfo);
            tv.setTextSize(12);
            tv.setTextColor(Color.parseColor("#ffd389"));
            tv.setTypeface(Typeface.create("sans-serif-light", Typeface.BOLD));
            tv.setBackgroundResource(R.drawable.main_recommend_album_card_tag_bg);
            return tv;
        }

    private View buildAlbumTagView(TagResult tagResult, AlbumM albumM) {
        TextView tv = new TextView(mContext);
        tv.setText("" + tagResult.getTagName());
        tv.setTextSize(12);
        tv.setTextColor(Color.parseColor("#b3000000"));
        tv.setCompoundDrawablesWithIntrinsicBounds(0,0, R.drawable.main_icon_right_gray_playcard_c, 0);
        //tv.setTypeface(Typeface.create("sans-serif-light", Typeface.BOLD));
        tv.setBackgroundResource(R.drawable.main_recommend_album_card_tag_bg_new);
        tv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                // 播放卡片-简介标签  点击事件
                new XMTraceApi.Trace()
                        .click(54769) // 用户点击时上报
                        .put("currPage", "playCard")
                        .put("tagId", ""+tagResult.getTagId())
                        .put("tagName", ""+tagResult.getTagName())
                        .put("albumId", ""+albumM.getId())
                        .put(XmRequestIdManager.XM_REQUEST_ID, albumM.getRequestId())
                        .createTrace();
                goToIting(tagResult.getIting());
            }
        });
        return tv;
    }

    private View buildCommentTextView(String title, Long commentId, AlbumM albumM) {
        TextView tv = new TextView(mContext);
        ViewFlipper.LayoutParams params = new ViewFlipper.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, Gravity.LEFT);
        tv.setIncludeFontPadding(false);
        tv.setLayoutParams(params);
        tv.setTextSize(11);
        tv.setTextColor(Color.parseColor("#66000000"));
        tv.setLineSpacing(0.0f, 1.2f);
        //tv.setMinLines(2);
        tv.setLines(2);
        tv.setSingleLine(false);
        //tv.setMinHeight(BaseUtil.dp2px(mContext,30));
        tv.setEllipsize(TextUtils.TruncateAt.END);
        tv.post(new Runnable() {
            @Override
            public void run() {
                String comment = " "+title;
                String tail = " 围观";//去围观
                String realTitle = comment + tail;
                float originTextWidth = tv.getPaint().measureText(comment + tail);
                float textViewWidth = tv.getMeasuredWidth();
                if (originTextWidth + 84 + 126 > textViewWidth * 2 && textViewWidth > 0) {
                    while (originTextWidth + 84 + 126 > textViewWidth * 2) {
                        comment = comment.substring(0, comment.length() - 1);
                        realTitle = comment + "..." + tail;
                        originTextWidth = tv.getPaint().measureText(realTitle);
                    }
                }

                SpannableString title = new SpannableString(realTitle);
                Drawable d = ContextCompat.getDrawable(mContext,R.drawable.main_ic_hot_comment_playcard);
                Drawable dMore = ContextCompat.getDrawable(mContext,R.drawable.main_ic_more_comment);
                if (d != null && dMore != null) {
                    int width = d.getIntrinsicWidth();
                    int height = d.getIntrinsicHeight();
                    d.setBounds(0, 0, width, height);
                    ImageSpan imageSpan = new PlayCardImageSpan(d);
                    title.setSpan(imageSpan, 0, 1, ImageSpan.ALIGN_CENTER);

                    int widthMore = dMore.getIntrinsicWidth();
                    int heightMore = dMore.getIntrinsicHeight();
                    dMore.setBounds(0, 0, widthMore, heightMore);
                    title.setSpan(new CenterAlignImageSpan(dMore), title.length() - tail.length() + 1, title.length(), ImageSpan.ALIGN_BOTTOM);

                    title.setSpan(new ClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            if (!OneClickHelper.getInstance().onClick(widget)) {
                                return;
                            }
                            try{
                                Track curTrack = PlayTools.getCurTrack(mContext);
                                if (curTrack != null) {
                                    long trackId = curTrack.getDataId();
//                                    Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction()
//                                            .handleITing(mFragment.getActivity(),  Uri.parse("iting://open?msg_type=11&track_id="+ trackId +"&autoplay=true&card_type=TALK&&card_id=1&from_page=24&comment_id="+commentId));
                                    FloatingTrackCommentFragment fragment = FloatingTrackCommentFragment.newInstanceForTrack(CommentConstants.FROM_PAGE_PLAY_CARD,
                                            curTrack, false, commentId);
                                    mFragment.startFragment(fragment, null, com.ximalaya.ting.android.host.R.anim.host_slide_in_bottom, com.ximalaya.ting.android.host.R.anim.host_slide_out_bottom);
                                    // 播放卡片-热评  点击事件
                                    new XMTraceApi.Trace()
                                            .click(55151) // 用户点击时上报
                                            .put("currPage", "playCard")
                                            .put("commentId", ""+commentId)
                                            .put("trackId", ""+trackId)
                                            .put(XmRequestIdManager.XM_REQUEST_ID, albumM.getRequestId())
                                            .createTrace();
                                }
                            }catch (Exception e){
                                e.printStackTrace();
                            }
                        }
                    }, title.length() - tail.length() + 1, title.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    tv.setMovementMethod(LinkMovementMethod.getInstance());
                }

                tv.setText(title);
            }
        });
        return tv;
    }

    private void goToIting(String iTing) {
        Activity mainActivity = BaseApplication.getMainActivity();
        if (mainActivity instanceof MainActivity) {
            NativeHybridFragment.start((MainActivity) mainActivity, iTing, false);
        }
    }

    private LinearLayout.LayoutParams getLayoutParams() {
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        lp.rightMargin = BaseUtil.dp2px(mContext, 8);
        return lp;
    }

    @Override
    public void onViewDetachedFromWindow(@NonNull ViewHolder holder) {
        super.onViewDetachedFromWindow(holder);
    }

    private void updatePlayStatus(ViewHolder holder, AlbumM albumM, TrackResult trackResult, List<HotCommentBean> hotCommentList) {
        boolean isPlaying = PlayTools.isAlbumPlaying(mContext, albumM.getId())
                || PlayTools.isAlbumAdPlaying(mContext, albumM.getId());
        boolean lastSelected = holder.playContainer.isSelected();
        // selected状态表示播放中
        holder.playContainer.setSelected(isPlaying);
        holder.playContainer.setContentDescription(isPlaying ? "暂停" : "播放");
        if (lastSelected != holder.playContainer.isSelected()) {
            //startPlayCenterIconLottie(holder);
        }
        holder.trackTitle.setText(""+albumM.getFirstTrackTitle());
        holder.trackTitle2.setText(""+albumM.getFirstTrackTitle());
        long trackId = 0;
        int duration = 0;
        //设置播放按钮loading
        if (curAlbum != null && curAlbum.getId() == albumM.getId() && (!isPlaying || XmPlayerManager.getInstance(mContext).getPlayerStatus() == PlayerConstants.STATE_PREPARING)) {
            holder.ivLoad.setVisibility(View.VISIBLE);
            AnimationUtil.rotateView(mContext, holder.ivLoad, 600, null, new LinearInterpolator());
            curAlbum = null;
        } else {
            holder.ivLoad.setVisibility(View.GONE);
            AnimationUtil.stopAnimation(holder.ivLoad);
        }
        holder.ivLoad.setOnClickListener(this);
        if (PlayTools.isCurrentAlbum(mContext, albumM.getId())) {
            if (isPlaying) {
                holder.playContainer.setImageResource(R.drawable.main_btn_pause_red_n_fill_n_44);
                if (curHolder != null) {
                    curHolder.seekBar.setOnSeekBarChangeListener(null);
                }
                curHolder = holder;
            } else {
                holder.playContainer.setImageResource(R.drawable.main_btn_play_red_n_fill_n_44);
            }
            if (holder.llComment.getVisibility() == View.VISIBLE && hotCommentList != null && hotCommentList.size() > 1 ) {
                //当前播放的item开始轮播热评
                holder.viewFlipper.startFlipping();
            }
        } else {
            holder.playContainer.setImageResource(R.drawable.main_btn_play_red_n_fill_n_44);
        }

        // 设置播放进度条
        Track curTrack = XmPlayerManager.getInstance(mContext).getLastPlayTrackInAlbum(albumM.getId());
        //Track curTrack = PlayTools.getCurTrack(mContext);
        if (curTrack != null) {
            holder.llPlayStatus.setVisibility(View.VISIBLE);
            holder.trackTitle.setText(""+curTrack.getTrackTitle());
            holder.trackTitle2.setText(""+curTrack.getTrackTitle());
            trackId = curTrack.getDataId();
            duration = curTrack.getDuration();
        } else if (trackResult != null) {
            holder.llPlayStatus.setVisibility(View.VISIBLE);
            trackId = trackResult.getId();
            duration = trackResult.getDuration();
        } else {
            holder.llPlayStatus.setVisibility(View.INVISIBLE);
        }

        long finalTrackId = trackId;
        int playPos = XmPlayerManager.getInstance(mContext).getHistoryPos(finalTrackId);
        if (playPos <= 0 && trackResult != null) {
            playPos = trackResult.getPlayPoint() * 1000;
        }
        duration = duration * 1000;
        if (playPos < duration && duration > 0) {
            holder.seekBar.setProgress(playPos * 100 / duration);
            holder.tvTime.setText(getFormatTime(playPos, duration));
            int finalDuration = duration;
            holder.seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    holder.tvTime.setText(getFormatTime(finalDuration * progress / 100, finalDuration));
                }
                @Override
                public void onStartTrackingTouch(SeekBar seekBar) {
                    HandlerManager.removeCallbacks(myRunnable);
                    canUpdateSeekBar = false;
                }
                @Override
                public void onStopTrackingTouch(SeekBar seekBar) {
                    XmPlayerManager.getInstance(mContext).seekTo(finalDuration * seekBar.getProgress() / 100);
                    HandlerManager.postOnUIThreadDelay(myRunnable,1000);
                }
            });
        } else {
            holder.seekBar.setOnSeekBarChangeListener(null);
            holder.seekBar.setProgress(0);
            holder.tvTime.setText(getFormatTime(0, duration));
        }

    }

    private String getFormatTime(int progress, int duration) {
        String progressStr = TimeHelper.toTime(progress / 1000f);
        String durationStr = TimeHelper.toTime(duration / 1000f);
        return String.format(Locale.getDefault(), TIME_FORMAT, progressStr, durationStr);
    }

    class MyRunnable implements Runnable {
        @Override
        public void run() {
            canUpdateSeekBar = true;
        }
    }

    private void showLoadingView(ViewHolder holder, boolean show) {
//        if (show) {
//            AnimationUtil.rotateView(mContext, holder.loadingStatus);
//            holder.loadingStatus.setVisibility(View.VISIBLE);
//        } else {
//            holder.loadingStatus.setVisibility(View.INVISIBLE);
//            AnimationUtil.stopAnimation(holder.loadingStatus);
//        }
    }

    private void setPlayBtnCenterIconColor(ViewHolder holder, int color) {
//        if (holder.playToPauseLottieDrawable != null) {
//            holder.playToPauseLottieDrawable.addValueCallback(new KeyPath("**"), LottieProperty.COLOR_FILTER,
//                    new LottieValueCallback<>(new PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN)));
//        }
//        if (holder.pauseToPlayLottieDrawable != null) {
//            holder.pauseToPlayLottieDrawable.addValueCallback(new KeyPath("**"), LottieProperty.COLOR_FILTER,
//                    new LottieValueCallback<>(new PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN)));
//        }
    }

    @Override
    public int getItemCount() {
        if (ToolUtil.isEmptyCollects(mListData)) {
            return 0;
        }
        return mListData.size();
    }

    public void setListData(List<RecommendAlbumCard> list) {
        if (ToolUtil.isEmptyCollects(list)) {
            return;
        }

        if (mListData == null) {
            mListData = new ArrayList<>(list);
        } else {
            mListData.clear();
            mListData.addAll(list);
        }
        notifyDataSetChanged();
    }

    public void addListData(List<RecommendAlbumCard> list) {
        if (ToolUtil.isEmptyCollects(list)) {
            return;
        }
        if (mListData == null) {
            mListData = new ArrayList<>(list);
            notifyDataSetChanged();
            return;
        }
        mListData.addAll(list);
        notifyItemRangeInserted(mListData.size(), list.size());
    }

    public void clear() {
        if (ToolUtil.isEmptyCollects(mListData)) {
            return;
        }
        mListData.clear();
        notifyDataSetChanged();
    }

    public List<RecommendAlbumCard> getListData() {
        return mListData;
    }

    @Override
    public void onClick(View view) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        AlbumM albumM = ViewStatusUtil.getTag(view, R.id.main_tag_default_id, AlbumM.class);
        if (albumM == null) {
            return;
        }
        int id = view.getId();
        if (id == R.id.main_vg_play_btn) {
            String item;
            if (PlayTools.isAlbumPlaying(mContext, albumM.getId())) {
                PlayTools.pause(mContext, PauseReason.Business.RecommendAlbumCardC);
                item = "手动暂停";
            } else if (PlayTools.isCurrentAlbum(mContext, albumM.getId())) {
                PlayTools.play(mContext);
                item = "手动播放";
            } else {
                if (albumM.getFirstTrackId() > 0) {
                    PlayTools.playTrackByCommonList(mContext, albumM.getFirstTrackId()
                            , ConstantsOpenSdk.PLAY_FROM_PLAY_CARD, view, false);
                } else {
                    PlayTools.playByAlbumByIdIfHasHistoryUseHistory(mContext, albumM.getId(),null);
                }
                item = "手动播放";
            }
            tracePlayBtnClick(albumM, item);
        } else if (id == R.id.main_album_cover) {
            gotoAlbumFragment(albumM);
            XMTraceApi.Trace trace = new XMTraceApi.Trace()
                    .click(30944)
                    .put("albumId", String.valueOf(albumM.getId()))
                    .put("rec_track", albumM.getRecTrack())
                    .put("rec_src", albumM.getRecommentSrc())
                    .put("ubtTraceId", ""+albumM.getUbtTraceId())
                    .put("currPage", "playCard")
                    .put(XmRequestIdManager.XM_REQUEST_ID, albumM.getRequestId());
            trace.createTrace();
        }
    }

    private void gotoAlbumFragment(AlbumM albumM) {
        AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_HOME_PAGE_RECOMMEND_FEED,
                ConstantsOpenSdk.PLAY_FROM_OTHER, albumM.getRecommentSrc(), albumM.getRecTrack(),
                -1, mFragment.getActivity());
    }

    public void tracePlayBtnClick(AlbumM albumM, String item) {
        if (albumM == null) {
            return;
        }
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .click(18849)
                .put("currPage", "playCard")
                .put("albumId", String.valueOf(albumM.getId()))
                .put("Item", item)
                .put("type", item)
                .put("ubtTraceId", ""+albumM.getUbtTraceId())
                .put("rec_track", albumM.getRecTrack())
                .put("rec_src", albumM.getRecommentSrc())
                .put(XmRequestIdManager.XM_REQUEST_ID, albumM.getRequestId());
        trace.createTrace();
    }

    public void setProgress(int currPos, int duration, int progress){
        if (curHolder != null && canUpdateSeekBar) {
            curHolder.seekBar.setProgress(progress);
            curHolder.tvTime.setText(getFormatTime(currPos,duration));
        }
    }
    public void hideLoading(){
        if (curHolder != null) {
            AnimationUtil.stopAnimation(curHolder.ivLoad);
            curHolder.ivLoad.setVisibility(View.GONE);
        }
    }

    public void setSelectAlbum(AlbumM albumM){
        curAlbum = albumM;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        ImageView cover;
        ImageView tag;
        TextView albumTitle;
        TextView albumIntro;
        LinearLayout llRank;
        View divRank;
        TextView playCount;
        View divScore;
        LinearLayout llScore;
        TextView albumScore;
        TextView albumScoreDsp;
        TextView commentCount;
        LinearLayout llTags;
        ImageView playContainer;
        //ImageView playBtn;
        TextView tvRank;
        TextView tvRankDsp;
        TextView tvFinish;
        TextView tvEpisodes;
        TextView tvMoreEpisodes;
        TextView trackTitle;
        LinearLayout llPlayStatus;
        public TextView tvTime;
        public SeekBar seekBar;

        LinearLayout llComment;
        RelativeLayout rlPlay;
        TextView trackTitle2;
        public ViewFlipper viewFlipper;
        public ImageView ivLoad;

//        XmLottieDrawable playToPauseLottieDrawable;
//        XmLottieDrawable pauseToPlayLottieDrawable;
//        XmLottieDrawable playFlagLottieDrawable;

        ViewHolder(View itemView, Context context) {
            super(itemView);
            cover = itemView.findViewById(R.id.main_album_cover);
            tag = itemView.findViewById(R.id.main_album_tag);
            albumTitle = itemView.findViewById(R.id.main_album_title);
            albumIntro = itemView.findViewById(R.id.main_tv_intro);
            llRank = itemView.findViewById(R.id.main_ll_rank);
            divRank = itemView.findViewById(R.id.main_div_rank);
            playCount = itemView.findViewById(R.id.main_album_play_count);
            divScore = itemView.findViewById(R.id.main_div_score);
            llScore = itemView.findViewById(R.id.main_ll_score);
            albumScore = itemView.findViewById(R.id.main_album_score);
            albumScoreDsp = itemView.findViewById(R.id.main_album_score_dsp);
            commentCount = itemView.findViewById(R.id.main_album_comment);
            llTags = itemView.findViewById(R.id.main_ll_tags);
            playContainer = itemView.findViewById(R.id.main_vg_play_btn);
            playContainer.setSelected(false);
            //playBtn = itemView.findViewById(R.id.main_play_or_pause);
            tvRank = itemView.findViewById(R.id.main_tv_rank);
            tvRankDsp = itemView.findViewById(R.id.main_tv_rank_dsp);
            tvFinish = itemView.findViewById(R.id.main_tv_finish);
            tvEpisodes = itemView.findViewById(R.id.main_tv_episodes);
            tvMoreEpisodes = itemView.findViewById(R.id.main_tv_more_episodes);
            trackTitle = itemView.findViewById(R.id.main_tv_title_track);
            llPlayStatus = itemView.findViewById(R.id.main_ll_play_status);
            seekBar = itemView.findViewById(R.id.main_seek_bar);
            tvTime = itemView.findViewById(R.id.main_tv_album_time);
            //热评区域
            rlPlay = itemView.findViewById(R.id.main_rl_play);
            llComment = itemView.findViewById(R.id.main_ll_comment);
            trackTitle2 = itemView.findViewById(R.id.main_tv_title_track2);
            viewFlipper = itemView.findViewById(R.id.main_view_flipper);
            ivLoad = itemView.findViewById(R.id.main_vg_play_load);

            //loadLottieResourcesForPlayBtnCenterIcon(this, context);
        }

//        private void loadLottieResourcesForPlayBtnCenterIcon(ViewHolder holder, Context context) {
//            holder.playToPauseLottieDrawable = new XmLottieDrawable();
//            holder.playToPauseLottieDrawable.setScale(0.5f);
//            String playToPauseLottiePath = "lottie/play_page_play_btn_play_to_pause.json";
//            LottieCompositionFactory.fromAsset(context, playToPauseLottiePath).addListener((composition) -> {
//                holder.playToPauseLottieDrawable.setComposition(composition);
//                // 如果另一个也加载完成了，就设到控件上
//                if (holder.pauseToPlayLottieDrawable != null && holder.pauseToPlayLottieDrawable.getComposition() != null) {
//                    setPlayBtnCenterIconToLottie(holder);
//                }
//            });
//            holder.pauseToPlayLottieDrawable = new XmLottieDrawable();
//            holder.pauseToPlayLottieDrawable.setScale(0.5f);
//            String pauseToPlayLottiePath = "lottie/play_page_play_btn_pause_to_play.json";
//            LottieCompositionFactory.fromAsset(context, pauseToPlayLottiePath).addListener((composition) -> {
//                holder.pauseToPlayLottieDrawable.setComposition(composition);
//                // 如果另一个也加载完成了，就设到控件上
//                if (holder.playToPauseLottieDrawable != null && holder.playToPauseLottieDrawable.getComposition() != null) {
//                    setPlayBtnCenterIconToLottie(holder);
//                }
//            });
//            holder.playFlagLottieDrawable = new XmLottieDrawable();
//            String lottiePath = "lottie/main_album_track_playing.json";
//            LottieCompositionFactory.fromAsset(context, lottiePath).addListener(composition -> {
//                holder.playFlagLottieDrawable.setComposition(composition);
//                holder.playFlagLottieDrawable.setScale(1.0f);
//                holder.playFlagLottieDrawable.setRepeatCount(LottieDrawable.INFINITE);
//            });
//        }

//        private void setPlayBtnCenterIconToLottie(ViewHolder holder) {
//            StateListDrawable stateListDrawable = new StateListDrawable();
//            stateListDrawable.addState(new int[]{android.R.attr.state_selected}, holder.playToPauseLottieDrawable);
//            stateListDrawable.addState(new int[]{}, holder.pauseToPlayLottieDrawable);
//            if (holder.playContainer.isSelected()) {
//                holder.playToPauseLottieDrawable.setProgress(1);
//            } else {
//                holder.pauseToPlayLottieDrawable.setProgress(1);
//            }
//            holder.playBtn.setImageDrawable(stateListDrawable);
//        }
    }

}
