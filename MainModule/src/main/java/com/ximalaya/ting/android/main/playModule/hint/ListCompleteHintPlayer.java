//package com.ximalaya.ting.android.main.playModule.hint;
//
//import android.content.Context;
//import androidx.annotation.NonNull;
//
//import com.ximalaya.ting.android.host.manager.bundleframework.route.action.base.IAction;
//import com.ximalaya.ting.android.opensdk.model.PlayableModel;
//import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
//import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
//import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
//
///**
// * <AUTHOR> on 2018/3/9.
// */
//
//public class ListCompleteHintPlayer extends BaseHintPlayer implements IXmPlayerStatusListener{
//
//    private IAction.ICallback<Void> mHintCompleteCallback;
//
//    public ListCompleteHintPlayer(@NonNull Context context, IAction.ICallback<Void> hintCompleteCallback) {
//        super(context,"play_complete_hint.mp3");
//        mHintCompleteCallback = hintCompleteCallback;
//
//        XmPlayerManager.getInstance(context).addPlayerStatusListener(this);
//    }
//
//    @Override
//    public void onPlayStart() {
//        stop();
//    }
//
//    @Override
//    public void onPlayPause() {
//
//    }
//
//    @Override
//    public void onPlayStop() {
//
//    }
//
//    @Override
//    public void onSoundPlayComplete() {
//
//    }
//
//    @Override
//    public void onSoundPrepared() {
//
//    }
//
//    @Override
//    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
//
//    }
//
//    @Override
//    public void onBufferingStart() {
//
//    }
//
//    @Override
//    public void onBufferingStop() {
//
//    }
//
//    @Override
//    public void onBufferProgress(int percent) {
//
//    }
//
//    @Override
//    public void onPlayProgress(int currPos, int duration) {
//
//    }
//
//    @Override
//    public boolean onError(XmPlayerException exception) {
//        return false;
//    }
//
//
//
//    @Override
//    protected void onHintComplete() {
//        XmPlayerManager.getInstance(mContext).removePlayerStatusListener(this);
//        if (mHintCompleteCallback != null) {
//            mHintCompleteCallback.dataCallback(null);
//        }
//    }
//}
