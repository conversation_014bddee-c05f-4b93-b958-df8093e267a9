package com.ximalaya.ting.android.main.adapter.podcast;

import android.graphics.Rect;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.main.model.podcast.HomePodcastRecItemVO;
import com.ximalaya.ting.android.main.model.podcast.HomePodcastRecVO;
import com.ximalaya.ting.android.main.view.RecyclerViewCanDisallowIntercept;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Created by wenbin.liu on 2021/1/23
 *
 * <AUTHOR>
 */
public class FeedRecAdapter extends AbstractPodCastModuleAdapter<FeedRecAdapter.FeedRecViewHolder>{

    private List<HomePodcastRecItemVO> mListData;

    public FeedRecAdapter(BaseFragment2 baseFragment, IPodcastFraDataProvider provider) {
        super(baseFragment, provider);
        mListData = new ArrayList<>();
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_podcast_item_feed_album_recom, parent, false);
    }

    @Override
    public FeedRecViewHolder createViewHolder(View convertView) {
        FeedRecViewHolder holder = new FeedRecViewHolder(convertView);
        return holder;
    }

    @Override
    public void bindViewData(int position, ItemModel model, FeedRecViewHolder viewHolder) {
        if (model == null || !(model.getObject() instanceof HomePodcastRecVO)
                || viewHolder == null) return;
        HomePodcastRecVO feedRec = (HomePodcastRecVO)model.getObject();
        if (ToolUtil.isEmptyCollects(feedRec.getList())) return;
        String title = TextUtils.isEmpty(feedRec.getModuleName()) ?
                feedRec.getTitle() : feedRec.getModuleName();
        if (!TextUtils.isEmpty(title)) {
            viewHolder.titleTv.setText(title);
        }
        if (mBaseFragment.getView() != null) {
            viewHolder.rcv.setDisallowInterceptTouchEventView((ViewGroup) mBaseFragment.getView());
        }

        if (viewHolder.rcv.getItemDecorationCount() == 0) {
            viewHolder.rcv.addItemDecoration(new RecyclerView.ItemDecoration() {
                @Override
                public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                    super.getItemOffsets(outRect, view, parent, state);
                    int position = parent.getChildAdapterPosition(view);
                    if (position == 0) {
                        outRect.left = DP16;
                    } else {
                        outRect.left = DP10;
                    }
                    if (position == (parent.getAdapter().getItemCount() - 1)) {
                        outRect.right = DP16;
                    }
                }
            });
        }
        List<HomePodcastRecItemVO> recItemVOList = feedRec.getList();
        mListData.clear();
        if (!ToolUtil.isEmptyCollects(recItemVOList)) {
            mListData.addAll(recItemVOList);
        }
        if (viewHolder.rcv.getAdapter() == null) {
            viewHolder.rcv.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));
            RecDataAdapter adapter = new RecDataAdapter(mListData, mBaseFragment, mWeakDataProvider);
            viewHolder.rcv.setAdapter(adapter);
        } else {
            viewHolder.rcv.getAdapter().notifyDataSetChanged();
        }
        viewHolder.rcv.scrollToPosition(0);
        if (isFeedDataChanged()) {
            setFeedDataChanged(false);
        }
    }

    static class RecDataAdapter extends RecyclerView.Adapter<RecDataAdapter.VH> {

        private List<HomePodcastRecItemVO> listData;
        private BaseFragment2 fragment;
        private WeakReference<IPodcastFraDataProvider> mWeakDataProvider;

        public RecDataAdapter(List<HomePodcastRecItemVO> listData, BaseFragment2 fragment,
                              WeakReference<IPodcastFraDataProvider> weakReference) {
            this.listData = listData;
            this.fragment = fragment;
            this.mWeakDataProvider = weakReference;
        }

        @NonNull
        @Override
        public VH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.main_podcast_item_album_recomm, parent, false);
            return new VH(itemView);
        }

        @Override
        public void onBindViewHolder(@NonNull VH holder, int position) {
            if (holder == null || ToolUtil.isEmptyCollects(listData)) return;
            HomePodcastRecItemVO recItemData = listData.get(position);
            if (recItemData == null) return;
            if (!TextUtils.isEmpty(recItemData.getCoverPath())) {
                Object coverLoaded = holder.coverIv.getTag();
                if (coverLoaded instanceof String
                        && !TextUtils.isEmpty((String)coverLoaded)
                        && recItemData.getCoverPath().equals(coverLoaded)) {
                    // igonre
                } else {
                    holder.coverIv.setTag(recItemData.getCoverPath());
                    ImageManager.from(fragment.getContext()).displayImage(holder.coverIv, recItemData.getCoverPath(),
                            com.ximalaya.ting.android.host.R.drawable.host_default_album, (lastUrl, bitmap)->{
                    if (bitmap != null) { holder.coverIv.setImageBitmap(bitmap); } });
                }
            }
            holder.itemView.setOnClickListener((view)->{
                // 播客频道-分类导航-专辑流  点击事件
                new XMTraceApi.Trace()
                        .click(27886)
                        .put("albumId", recItemData.getAlbumId()+"")
                        .put("categoryId", getCurCategoryId() + "")
                        .put("position", (position+1)+"")
                        .put("tabName", getCurCategoryName())
                        .put("currPage", "podcast")
                        .createTrace();
                AlbumEventManage.startMatchAlbumFragment(recItemData.getAlbumId(), -1, -1, "", "", 0, fragment.getActivity());
            });
            if (!TextUtils.isEmpty(recItemData.getAlbumTitle())) {
                holder.titleTv.setText(recItemData.getAlbumTitle());
            }
            holder.subscribeCountTv.setText(String.format(Locale.CHINA, "%s人订阅",
                    StringUtil.getFriendlyNumStr(recItemData.getSubcribeCount())));
            // 播客频道-分类导航-专辑流  控件曝光
            new XMTraceApi.Trace()
                    .setMetaId(27887)
                    .setServiceId("slipPage")
                    .put("albumId", recItemData.getAlbumId()+"")
                    .put("categoryId", getCurCategoryId()+"")
                    .put("position", (position+1)+"")
                    .put("tabName", getCurCategoryName()+"")
                    .put("currPage", "podcast")
                    .createTrace();
        }

        private int getCurCategoryId() {
            return mWeakDataProvider != null && mWeakDataProvider.get() != null ?
                    mWeakDataProvider.get().getCurCategoryId() : 0;
        }

        private String getCurCategoryName() {
            return mWeakDataProvider != null && mWeakDataProvider.get() != null ?
                    mWeakDataProvider.get().getCurCategoryName() : "";
        }

        @Override
        public int getItemCount() {
            if (!ToolUtil.isEmptyCollects(listData)) {
                return listData.size();
            }
            return 0;
        }

        static class VH extends RecyclerView.ViewHolder {

            ImageView coverIv;
            TextView titleTv;
            TextView subscribeCountTv;

            public VH(@NonNull View itemView) {
                super(itemView);
                coverIv = itemView.findViewById(R.id.main_album_cover_iv);
                titleTv = itemView.findViewById(R.id.main_album_name_tv);
                subscribeCountTv = itemView.findViewById(R.id.main_subscribe_count_tv);
            }
        }
    }

    static class FeedRecViewHolder extends HolderAdapter.BaseViewHolder {
        TextView titleTv;
        RecyclerViewCanDisallowIntercept rcv;
        View itemView;

        public FeedRecViewHolder(View convertView) {
            itemView = convertView;
            titleTv = convertView.findViewById(R.id.main_recommend_title_tv);
            rcv = convertView.findViewById(R.id.main_album_recommend_rcv);
        }
    }
}
