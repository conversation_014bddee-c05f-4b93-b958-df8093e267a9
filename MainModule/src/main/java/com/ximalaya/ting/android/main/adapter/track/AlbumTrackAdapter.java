package com.ximalaya.ting.android.main.adapter.track;

import android.app.Activity;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.Pair;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.TextViewCompat;
import androidx.fragment.app.Fragment;

import com.airbnb.lottie.LottieComposition;
import com.airbnb.lottie.LottieCompositionFactory;
import com.airbnb.lottie.LottieDrawable;
import com.airbnb.lottie.LottieListener;
import com.ximalaya.ting.android.downloadservice.base.IDownloadStatus;
import com.ximalaya.ting.android.framework.manager.AlbumOrderManager;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.framework.view.snackbar.Snackbar;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.adapter.track.base.AbstractAlbumTrackAdapter;
import com.ximalaya.ting.android.host.download.ActionCallBack;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.AccessibilityModeManager;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.manager.TempDataManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFragmentAction;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenHostManager;
import com.ximalaya.ting.android.host.manager.pay.ISingleAlbumPayResultListener;
import com.ximalaya.ting.android.host.manager.pay.PayManager;
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager;
import com.ximalaya.ting.android.host.manager.play.PlayerManager;
import com.ximalaya.ting.android.host.model.album.AlbumListGuideVipResourceModel;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.payment.UniversalPayment;
import com.ximalaya.ting.android.host.model.payment.UniversalPaymentCommonFragmentFinishCallBack;
import com.ximalaya.ting.android.host.model.payment.behavior.BehaviorAction;
import com.ximalaya.ting.android.host.model.payment.behavior.CommonBehavior;
import com.ximalaya.ting.android.host.model.play.CopyrightExtendInfo;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.util.CountDownTimeUtil;
import com.ximalaya.ting.android.host.util.MyListenRouterUtil;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.commercial.AlbumTypeUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.other.VideoPlayParamsBuildUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.util.ui.TrackItemTagUtil;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.host.view.XmLottieDrawable;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.dialog.VipPriorListenDialog;
import com.ximalaya.ting.android.main.dialog.universal.RequestMaterial;
import com.ximalaya.ting.android.main.dialog.universal.UniversalPaymentActionsDialog;
import com.ximalaya.ting.android.main.downloadModule.other.DownloadTrackDlgFragment;
import com.ximalaya.ting.android.main.downloadModule.quality.ChooseTrackQualityDialog;
import com.ximalaya.ting.android.main.manager.albumFragment.AlbumFragmentMarkPointManager;
import com.ximalaya.ting.android.main.manager.freelisten.FreeListenManager;
import com.ximalaya.ting.android.main.payModule.BundleBuyDialogFragment;
import com.ximalaya.ting.android.main.payModule.BundleBuyDialogFragment1;
import com.ximalaya.ting.android.main.payModule.single.SingleAlbumPayManager;
import com.ximalaya.ting.android.main.util.other.CopyrightUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.model.track.UnlockModeInfo;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * 付费声音条。支持付费业务。
 * 主播个人的专辑页面的声音条不可以用这个Adapter，而是用MyTrackAdapter。
 *
 * <AUTHOR> on 16/8/1.
 */
public class AlbumTrackAdapter extends AbstractAlbumTrackAdapter implements AdMakeVipLocalManager.IAlbumPageCallback {
    private static final String TEXT_UNLOCK_HINT_AREA = "以下声音可通过看视频，获得免费畅听权益";
    private static final String TEXT_UNLOCK_HINT_AREA_TICKING = "以下声音的畅听时长剩余：";

    private static final int TYPE_SNACKBAR_SINGLE = 1;  //点击单个item下载出的snackbar
    private static final int TYPE_SNACKBAR_BATCH = 2;   //批量下载出的snackbar
    public static String snackbarAb = "";   //批量下载ab实验

    private BundleBuyDialogFragment.IAlbumStatusChangedListener albumStatusChangedListener;
    private LottieDrawable mLottieDrawable;
    private SingleAlbumPayManager singleAlbumPayManager;
    private BehaviorAction.IActionOuterImp actionOuterImp;
    private ISingleAlbumPayResultListener payResultListener;
    @Nullable private final BaseFragment2 mFragment;
    @Nullable private CopyrightExtendInfo mCopyrightExtendInfo;
    private int fromFragmentType = AlbumTypeUtil.TYPE_FROM_ALBUM_FRAGMENT;

    private UnlockHintCountDown unlockHintCountDown;
    //snackbar相关ui
    private Snackbar mDownloadSnackBar;
    private TextView mSnackBarTv;
    private TextView mSnackBarTvAfter;
    private ImageView mSnackBarIv;

    public AlbumTrackAdapter(Context activity, List<Track> listData, @Nullable BaseFragment2 fragment) {
        super(activity, listData);
        this.mFragment = fragment;
    }

    public void showPositionBright(int index) {
        brightPosition = index;
    }

    public void setPayResultListener(ISingleAlbumPayResultListener payResultListener) {
        this.payResultListener = payResultListener;
    }

    public void setAlbumStatusChangedListener(BundleBuyDialogFragment.IAlbumStatusChangedListener albumStatusChangedListener) {
        this.albumStatusChangedListener = albumStatusChangedListener;
    }

    public void setFromFragmentType(int fromFragmentType) {
        this.fromFragmentType = fromFragmentType;
    }

    public void onFragmentPause(ListView listView) {
        if (listView == null) {
            return;
        }
    }

    public void onFragmentResume(ListView listView) {
        if (listView == null) {
            return;
        }
    }

    @Override
    public void bindViewDatas(BaseViewHolder h, Track track, int position) {
        if (track == null || !(h instanceof ViewHolder)) {
            return;
        }
        ViewHolder holder = (ViewHolder) h;

        AlbumM album = null == mAlbumMDataProvider ? null : mAlbumMDataProvider.getAlbumM();
        // 声音条头部的解锁提示信息
        // bindUnlockHintArea(album, track, holder);

        // 分割线
        holder.border.setVisibility(position + 1 == getCount() ? View.GONE : View.VISIBLE);

        // 更新时间
        holder.updateAt.setText(StringUtil.getFriendlyTimeStr(track.getCreatedAt()));
        // 上次收听
        if (showLastListenTag && lastPlayTrackId == track.getDataId()) {
            holder.updateAt.setVisibility(View.GONE);
            holder.lastListenTag.setVisibility(View.VISIBLE);
        } else {
            holder.updateAt.setVisibility(View.VISIBLE);
            holder.lastListenTag.setVisibility(View.GONE);
        }

        if (hideUpdateTime) {
            //强制隐藏更新时间
            holder.updateAt.setVisibility(View.GONE);
        }

        // 声音时长
        holder.duration.setVisibility(View.VISIBLE);
        holder.duration.setText(track.getDuration() > 0 ? StringUtil.toTime(track.getDuration()) : "--:--");

        // 播放数
        if (track.getPlayCount() > 0) {
            holder.playCount.setVisibility(View.VISIBLE);
            holder.playCount.setText(StringUtil.getFriendlyNumStr(track.getPlayCount()));
        } else {
            holder.playCount.setVisibility(View.GONE);
        }

        // 评论数
        if (track.getCommentCount() > 0) {
            holder.commentCount.setVisibility(View.VISIBLE);
            holder.commentCount.setText(StringUtil.getFriendlyNumStr(track.getCommentCount()));
        } else {
            holder.commentCount.setVisibility(View.GONE);
        }

        // 声音标签 播放进度设置
        boolean tagShow = setUpTrackTags(holder, track);
        boolean isTrackAccess = !track.isPaid() || track.isFree() || track.isAuthorized();
        boolean playScheduleShow = false;
        if (isTrackAccess) {
            int lastPos = XmPlayerManager.getInstance(context).getHistoryPos(track.getDataId());
            String ps = ToolUtil.getPlaySchedule(lastPos, track.getDuration());
            if (!TextUtils.isEmpty(ps)) {
                playScheduleShow = true;
                holder.playSchedule.setText(ps);
            }
        }
        holder.playSchedule.setVisibility(playScheduleShow ? View.VISIBLE : View.GONE);
        holder.vAlbumTagsGroup.setVisibility(playScheduleShow || tagShow ? View.VISIBLE : View.GONE);

        if (isAllVideo) {
            //全视频，修改右下角图标和点击事件
            holder.videoIv.setVisibility(View.GONE);
            ViewStatusUtil.setVisible(View.VISIBLE, holder.audioIvForAllVideo);
            setClickListener(holder.audioIvForAllVideo, track, position, holder);
        } else {
            ViewStatusUtil.setVisible(View.GONE, holder.audioIvForAllVideo);
            // video ppt
            if (track.isVideo()) {
                holder.videoIv.setVisibility(View.VISIBLE);
                Drawable drawable = context.getResources().getDrawable(R.drawable.host_ic_video_n_n_line_regular_20).mutate();
                drawable.setColorFilter(context.getResources().getColor(R.color.host_color_666666_8d8d91), PorterDuff.Mode.SRC_IN);
                holder.videoIv.setImageDrawable(drawable);
                setClickListener(holder.videoIv, track, position, holder);
            } else {
                holder.videoIv.setVisibility(View.GONE);
            }
        }
        if (track.isRichAudio()) {
            holder.pptIv.setVisibility(View.VISIBLE);
            setClickListener(holder.pptIv, track, position, holder);
        } else {
            holder.pptIv.setVisibility(View.GONE);
        }

        ViewStatusUtil.setVisible(View.GONE, holder.singleTrackPrice, holder.adHintUnLockDoneTv, holder.adHintUnLockDoneIv, holder.freeUnlockIv);
        if (track.isPaid()) {
            bindPaidView(holder, track, position);
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, holder.downloadMask);
            setDownloadEnable(holder.downloadMask, true);
            holder.priceIv.setVisibility(View.GONE);
            setTrackDownloadStatus(context, holder.download, holder.downloadMask,
                    RouteServiceUtil.getDownloadService().getDownloadStatus(track));
        }
        //tts专辑隐藏下载按钮
        if (mHideDownLoadBtn) {
            setDownloadEnable(holder.downloadMask, false);
            ViewStatusUtil.setVisible(View.GONE, holder.downloadMask);
        }
        setClickListener(holder.downloadMask, track, position, holder);

        // 声音标题和播放状态
        setTrackTitleAndPlayingStatus(holder, track, position);

        holder.title.setTag(R.id.main_sound_name, track);

        AlbumFragmentMarkPointManager.Companion.markPointOnShowAlbumTrackList(mAlbumId, mAlbumTitle, track.getUid(),
                track.getCategoryId(), track.getDataId(), holder.root, track.getXmRequestId());
        if (AlbumTypeUtil.TYPE_FROM_UNIVERSAL_CUSTOM_ALBUM == fromFragmentType) {
            AlbumFragmentMarkPointManager.Companion.markPointOnShowCustomedAlbumTrackList(mAlbumId, track.getXmRequestId(), track.getDataId(), album != null
                    && (album.isAuthorized() || AlbumTypeUtil.SingleAlbumRelative.isSingleAlbum(album) || AlbumTypeUtil.SingleAlbumRelative.isFreeAlbum(album)));
        }
    }

    private void bindUnlockHintArea(AlbumM album, @NonNull Track track, @NonNull ViewHolder holder) {
        boolean isAsc = AlbumOrderManager.isAlbumSortAsc(mAlbumId);
//        boolean isAsc = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
//                .getBooleanCompat(PreferenceConstantsInHost.KEY_IS_ASC + mAlbumId, true);
        long showHintTrackId = null == album ? 0 : (isAsc ? album.getFirstPaidTrackId() : album.getLastPaidTrackId());
        if (FreeListenHostManager.getInstance().canUnlockWithTime()
                && FreeListenManager.getInstance().isTargetAlbum(album)
                && showHintTrackId == track.getDataId()) {
            setUnlockHintArea(holder.adHintArea, album);
            ViewStatusUtil.setVisible(View.VISIBLE, holder.adHintArea);
            ViewStatusUtil.setOnClickListener(holder.adHintArea, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // do Nothing
                }
            });
        } else {
            ViewStatusUtil.setVisible(View.GONE, holder.adHintArea);
        }
    }

    private void setUnlockHintArea(View area, AlbumM album) {
        if (null == area) {
            return;
        }
        ImageView iconIv = area.findViewById(R.id.main_track_view_ad_info_tip_icon);
        TextView textTv = area.findViewById(R.id.main_track_view_ad_info_tip_text);
        Pair<Long, Long> countDownTimeInfo;
        boolean tickTacking = false;
        long millisUntilFinished = 0;
        if (null != album && null != (countDownTimeInfo = UnlockModeInfo.getCountDownTimes(album.getUnlockModeInfos()))) {
            millisUntilFinished = CountDownTimeUtil.getRealCountDownDuration(countDownTimeInfo.first, countDownTimeInfo.second);
            if (0 < millisUntilFinished) {
                tickTacking = true;
            }
        }

        if (tickTacking) {
            ViewStatusUtil.setImageRes(iconIv, R.drawable.main_icon_free_unlock_track_done);
            if (null != unlockHintCountDown) {
                unlockHintCountDown.cancel();
                unlockHintCountDown = null;
            }
            UnlockHintCountDown.setCountDownText(textTv, millisUntilFinished);
            unlockHintCountDown = new UnlockHintCountDown(millisUntilFinished, area);
            unlockHintCountDown.start();
        } else {
            ViewStatusUtil.setImageRes(iconIv, R.drawable.main_icon_free_unlock_track);
            ViewStatusUtil.setText(textTv, TEXT_UNLOCK_HINT_AREA);
        }
    }

    private void setTrackTitleAndPlayingStatus(ViewHolder holder, Track track, int position) {
        if (track instanceof TrackM && ((TrackM) track).isNewTrack()) {
            SpannableString titleSpan = ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(
                    context, "  " + track.getTrackTitle(),
                    com.ximalaya.ting.android.host.R.drawable.host_album_ic_info_new, (int) holder.title.getTextSize(),
                    context.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_xmRed));
            holder.title.setText(titleSpan);
        } else {
            holder.title.setText(track.getTrackTitle());
        }

        int lastPos = XmPlayerManager.getInstance(context).getHistoryPos(track.getDataId());
        String ps = ToolUtil.getPlaySchedule(lastPos, track.getDuration());
        if (!TextUtils.isEmpty(ps)) {
            holder.title.setTextColor(context.getResources().getColor(R.color.main_color_999999_66666b));
        } else {
            holder.title.setTextColor(context.getResources().getColor(R.color.main_color_111111_ffffff));
        }

        if (track instanceof TrackM) {
            String num = String.valueOf(((TrackM) track).getOrderNo());
            adjustTvTextSize(holder.orderNo, BaseUtil.dp2px(context, 25), num);
            holder.orderNo.setText(num);

            // 处理播放中的动画
            boolean isPerformPlayingAnim = (PlayTools.isPlayCurrTrackById(context, track.getDataId()) && brightPosition < 0)
                    || brightPosition == position;
            if (isPerformPlayingAnim) {
                holder.playingFlag.setVisibility(View.VISIBLE);
                if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
                    // 为了读屏软件能读出序号
                    holder.orderNo.setAlpha(0.01f);
                } else {
                    holder.orderNo.setVisibility(View.GONE);
                }
                createPlayingLottieDrawableIfNotExist();
                XmPlayerManager xManager = XmPlayerManager.getInstance(context);
                holder.title.setTextColor(context.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_xmRed));
                // 广告过来的标识 不需要根据状态进行改变
                if (brightPosition >= 0) {
                    stopPlayingFlagLoading(holder.playingFlag);
                    holder.playingFlag.setImageDrawable(mLottieDrawable);
                    mLottieDrawable.cancelAnimation();
                } else if (!xManager.isPlaying()) {
                    if (xManager.isBuffering()) {
                        startPlayingFlagLoading(holder.playingFlag);
                    } else {
                        stopPlayingFlagLoading(holder.playingFlag);
                        holder.playingFlag.setImageDrawable(mLottieDrawable);
                        mLottieDrawable.cancelAnimation();
                    }
                } else {
                    stopPlayingFlagLoading(holder.playingFlag);
                    holder.playingFlag.setImageDrawable(mLottieDrawable);
                    mLottieDrawable.playAnimation();
                }
            } else {
                stopPlayingFlagLoading(holder.playingFlag);
                if (holder.playingFlag.getDrawable() instanceof LottieDrawable) {
                    ((LottieDrawable) holder.playingFlag.getDrawable()).stop();
                    holder.playingFlag.setImageDrawable(null);
                }
                holder.playingFlag.setVisibility(View.GONE);
                holder.orderNo.setVisibility(View.VISIBLE);
                if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
                    holder.orderNo.setAlpha(1);
                }
            }
            if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
                holder.orderNo.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES);
            }
        }

        // 上次收听的声音 需要更换 字体颜色
        holder.orderNo.setTextColor(context.getResources().getColor(R.color.main_color_999999_66666b));
        if (lastPlayTrackId == track.getDataId()) {
            if (showLastListenTag || showAutoSelectionColor) {
                holder.title.setTextColor(context.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_xmRed));
                holder.orderNo.setTextColor(context.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_xmRed));
            }
        }
        int playingColor = context.getResources().getColor(R.color.main_color_aaaaaa_66666b);
        if (holder.playCount.getCompoundDrawables()[0] != null) {
            holder.playCount.getCompoundDrawables()[0].mutate().setColorFilter(new PorterDuffColorFilter(playingColor, PorterDuff.Mode.SRC_IN));
            holder.playCount.setTextColor(playingColor);
        }
        if (holder.duration.getCompoundDrawables()[0] != null) {
            holder.duration.getCompoundDrawables()[0].mutate().setColorFilter(new PorterDuffColorFilter(playingColor, PorterDuff.Mode.SRC_IN));
            holder.duration.setTextColor(playingColor);
        }
        if (holder.commentCount.getCompoundDrawables()[0] != null) {
            holder.commentCount.getCompoundDrawables()[0].mutate().setColorFilter(new PorterDuffColorFilter(playingColor, PorterDuff.Mode.SRC_IN));
            holder.commentCount.setTextColor(playingColor);
        }
    }

    private void createPlayingLottieDrawableIfNotExist() {
        if (mLottieDrawable == null) {
            // 获取动画LottieDrawable
            mLottieDrawable = new XmLottieDrawable();
            String lottiePath = "lottie" + File.separator + "album_ic_playing.json";
            LottieListener<LottieComposition> listener = new LottieListener<LottieComposition>() {
                @Override
                public void onResult(LottieComposition composition) {
                    mLottieDrawable.setComposition(composition);
                    mLottieDrawable.setRepeatCount(LottieDrawable.INFINITE);
                }
            };
            LottieCompositionFactory.fromAsset(context, lottiePath).addListener(listener);
        }
    }

    private void adjustTvTextSize(TextView tv, int maxWidth, String text) {
        int avaiWidth = maxWidth - tv.getPaddingLeft() - tv.getPaddingRight();

        if (avaiWidth <= 0) {
            return;
        }

        TextPaint textPaintClone = new TextPaint(tv.getPaint());

        DisplayMetrics displayMetrics = tv.getContext().getResources().getDisplayMetrics();
        float trySize = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, 16.0f, displayMetrics);
        // 先用最大字体写字
        textPaintClone.setTextSize(trySize);
        while (textPaintClone.measureText(text) > avaiWidth) {
            trySize--;
            textPaintClone.setTextSize(trySize);
        }

        tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, trySize);
    }

    private boolean setUpTrackTags(ViewHolder holder, Track track) {
        if (track instanceof TrackM) {
            boolean result = TrackItemTagUtil.INSTANCE.setTagsToView(holder.tagTv, ((TrackM) track).getLabelList());
            holder.tagTv.setVisibility(result ? View.VISIBLE : View.GONE);
            return result;
        }
        return false;
    }
//    private boolean setUpTrackTags(ViewHolder holder, Track track) {
//        boolean isTop = track.isTop(); // 置顶标签
//        boolean isTrailer = track.isTrailer(); // 发刊词
//        boolean isTransmit = track.getOpType() == 2; // 转采
//        // 单集付费
//        boolean singlePaid = track.isPaid() && track.getPaidType() == 1;
//        // ximi标签
//        boolean isXimi = track.ximiFirstStatus == AppConstants.XIMI_VIP_PRIORITY
//                || (singlePaid && track.ximiVipFreeType == AppConstants.XIMI_VIP_FREE);
//        boolean isVip = track.vipPriorListenStatus == 1; // vip抢先听标签
//        List<Integer> typeList = new ArrayList<>();
//        if (track instanceof TrackM) {
//            List<Integer> contentTypeList = ((TrackM) track).getContentTypesList();
//            if (contentTypeList != null && contentTypeList.size() > 0) {
//                typeList.addAll(contentTypeList);
//            }
//            if (typeList.isEmpty()) {
//                int contentTypes = ((TrackM) track).getContentTypes();
//                if (contentTypes != -1) {
//                    typeList.add(contentTypes);
//                }
//            }
//        }
//        List<String> tagList = new ArrayList<>();
//        List<Integer> tagColorList = new ArrayList<>();
//        List<Drawable> tagBgDrawableList = new ArrayList<>();
//        if (isTop) {
//            tagList.add("置顶");
//            tagColorList.add(context.getResources().getColor(R.color.main_color_ac1f1f_da6666));
//            Drawable drawable = context.getResources().getDrawable(com.ximalaya.ting.android.host.R.drawable.host_tag_bg).mutate();
//            drawable.setColorFilter(context.getResources().getColor(R.color.main_color_ffe7e7_2a2a2a), PorterDuff.Mode.SRC_IN);
//            tagBgDrawableList.add(drawable);
//        }
//
//        if (!ToolUtil.isEmptyCollects(typeList)) {
//            for (Integer type : typeList) {
//                //服务端不做版本控制，客户端判断是否在枚举值中，没有则不显示
//                TrackContentTypeEnum typeEnum = TrackContentTypeUtil.Companion.getEnumWithValue(type);
//                if (typeEnum != null && !TextUtils.isEmpty(typeEnum.contentName)) {
//                    tagList.add(typeEnum.contentName);
//                    tagColorList.add(context.getResources().getColor(R.color.main_color_825740_888888));
//                    Drawable drawable = context.getResources().getDrawable(com.ximalaya.ting.android.host.R.drawable.host_tag_bg).mutate();
//                    drawable.setColorFilter(context.getResources().getColor(R.color.main_color_fff3e7_252525), PorterDuff.Mode.SRC_IN);
//                    tagBgDrawableList.add(drawable);
//                }
//            }
//        } else if (isTrailer) {
//            tagList.add("发刊词");
//            tagColorList.add(context.getResources().getColor(R.color.main_color_825740_888888));
//            Drawable drawable = context.getResources().getDrawable(com.ximalaya.ting.android.host.R.drawable.host_tag_bg).mutate();
//            drawable.setColorFilter(context.getResources().getColor(R.color.main_color_fff3e7_252525), PorterDuff.Mode.SRC_IN);
//            tagBgDrawableList.add(drawable);
//        }
//        if (isTransmit) {
//            tagList.add("转采");
//            tagColorList.add(context.getResources().getColor(R.color.main_color_825740_888888));
//            Drawable drawable = context.getResources().getDrawable(com.ximalaya.ting.android.host.R.drawable.host_tag_bg).mutate();
//            drawable.setColorFilter(context.getResources().getColor(R.color.main_color_fff3e7_252525), PorterDuff.Mode.SRC_IN);
//            tagBgDrawableList.add(drawable);
//        }
//        if (isXimi) {
//            tagList.add("主播会员");
//            tagColorList.add(context.getResources().getColor(R.color.main_color_825740_888888));
//            Drawable drawable = context.getResources().getDrawable(com.ximalaya.ting.android.host.R.drawable.host_tag_bg).mutate();
//            drawable.setColorFilter(context.getResources().getColor(R.color.main_color_fff3e7_252525), PorterDuff.Mode.SRC_IN);
//            tagBgDrawableList.add(drawable);
//        }
//        if (isVip) {
//            tagList.add("VIP");
//            tagColorList.add(context.getResources().getColor(R.color.main_color_825740_888888));
//            Drawable drawable = context.getResources().getDrawable(com.ximalaya.ting.android.host.R.drawable.host_tag_bg).mutate();
//            drawable.setColorFilter(context.getResources().getColor(R.color.main_color_fff3e7_252525), PorterDuff.Mode.SRC_IN);
//            tagBgDrawableList.add(drawable);
//        }
//        if (ToolUtil.isEmptyCollects(tagList)) {
//            holder.tagTv.setVisibility(View.GONE);
//            return false;
//        }
//        holder.tagTv.setVisibility(View.VISIBLE);
//        holder.tagTv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
//        TextPaint textPaint = holder.tagTv.getPaint();
//        SpannableStringBuilder ssb = new SpannableStringBuilder();
//        for (int i = 0; i < tagList.size(); i++) {
//            String tag = tagList.get(i);
//            ssb.append(tag);
//            int padding = BaseUtil.dp2px(context, 4);
//            TrackTagSpan span = new TrackTagSpan(
//                    context, tag, padding, tagBgDrawableList.get(i),
//                    (int) (textPaint.measureText(tag) + padding * 2),
//                    10, tagColorList.get(i)
//            );
//            ssb.setSpan(span, ssb.length() - tag.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
//            ssb.append("  ");
//        }
//        holder.tagTv.setText(ssb);
//        return true;
//    }

    private void startPlayingFlagLoading(final ImageView ivPlayFlag) {
        Drawable leftDrawable = ViewStatusUtil.filterDrawable(context, R.drawable.main_album_ic_list_loading,
                context.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_xmRed));
        ivPlayFlag.setImageDrawable(leftDrawable);
        AnimationUtil.rotateView(context, ivPlayFlag);
    }

    private void stopPlayingFlagLoading(ImageView ivPlayFlag) {
        AnimationUtil.stopAnimation(ivPlayFlag);
    }

    private void bindPaidView(ViewHolder holder, Track track, int position) {
        AlbumM albumM = mAlbumMDataProvider != null ? mAlbumMDataProvider.getAlbumM() : null;
        //处理是否显示下载按钮
        boolean isOnlyVipFree = (mAlbumMDataProvider != null
                && albumM != null
                && albumM.isVipFree());

//        ViewStatusUtil.setVisible((track.isAuthorized() || track.isFree() || isOnlyVipFree) ? View.VISIBLE : View.GONE, holder.downloadMask);
//        holder.download.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_ic_download_n_n_line_regular_20);
        ViewStatusUtil.setVisible(View.GONE, holder.downloadMask);
        holder.download.clearAnimation();

        boolean isOldADUnlock = albumM != null && albumM.isShowVideoAdverts() && AdMakeVipLocalManager.getInstance().isTargetUser();
        boolean isNewUnlock = albumM != null && albumM.getUnlockModeInfos() != null && !albumM.getUnlockModeInfos().isEmpty();
        boolean isUnlockAble = isOldADUnlock || isNewUnlock;
        //支持免费解锁
        if (!track.isFree() && isUnlockAble) {
            if (FreeListenHostManager.getInstance().canUnlockWithTime()) {
                bindPaidViewUnlockWithTime(albumM, holder, track, position);
                return;
            } else if (FreeListenHostManager.getInstance().canUnlockWithTrack()) {
                bindPaidViewUnlockWithTrack(holder, track, position);
                return;
            }
        }

      /*  //处理显示价格
        if (!track.isAuthorized() && track.isPaid() && track.getPaidType() == 1) { // 单集付费声音
            ViewStatusUtil.setVisible(View.GONE, holder.downloadMask);
            holder.priceIv.setVisibility(View.GONE);

            holder.singleTrackPrice.setVisibility(View.VISIBLE);
            holder.singleTrackPrice.setText(String.format("%s喜点", String.valueOf(track.getPrice())));
            setClickListener(holder.singleTrackPrice, track, position, holder);
            AutoTraceHelper.bindData(holder.singleTrackPrice, track);

        } else */ //9242版本专辑页双端一致修改，不再显示单卖价格标签
        if (!track.isAuthorized()) {
            if (isOnlyVipFree) {
                holder.priceIv.setVisibility(View.GONE);
//                ViewStatusUtil.setVisible(View.VISIBLE, holder.downloadMask);
//                setDownloadEnable(holder.downloadMask, true);
//                holder.download.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_ic_download_n_n_line_regular_20);
            } else if (track.getPriceTypeEnum() == PayManager.PAY_ALBUM_WHOLE
                    || track.getPriceTypeEnum() == PayManager.PAY_ALBUM_MEMBER
                    || track.getPriceTypeEnum() == PayManager.PAY_ALBUM_MEMBER_WHOLE) {
                holder.priceIv.setVisibility(View.GONE);

                //判断是否有折扣
                ViewStatusUtil.setVisible(View.GONE, holder.downloadMask);
                holder.priceIv.setVisibility(View.VISIBLE);
                Drawable leftDrawable = ViewStatusUtil.filterDrawable(context, com.ximalaya.ting.android.host.R.drawable.host_album_ic_buy,
                        context.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_xmRed));
                holder.priceIv.setImageDrawable(leftDrawable);
                setClickListener(holder.priceIv, track, position, holder);

            } else if (track.isFree()) {
                holder.priceIv.setVisibility(View.GONE);
//                setDownloadEnable(holder.downloadMask, true);
            } else {
                //判断是否有折扣
                holder.priceIv.setVisibility(View.VISIBLE);
                // 2018/1/16  单集购买不显示价格（不显示价格（划线价、无划线价两种样式统一）），显示购买图标
                Drawable leftDrawable = ViewStatusUtil.filterDrawable(context, com.ximalaya.ting.android.host.R.drawable.host_album_ic_buy,
                        context.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_xmRed));
                holder.priceIv.setImageDrawable(leftDrawable);
                setClickListener(holder.priceIv, track, position, holder);
                AutoTraceHelper.bindData(holder.priceIv, track);
            }
        } else {
            holder.priceIv.setVisibility(View.GONE);
//            setDownloadEnable(holder.downloadMask, true);
        }

        if (track.isAuthorized() || track.isFree()|| isOnlyVipFree) {
            ViewStatusUtil.setVisible(View.VISIBLE, holder.downloadMask);
            setTrackDownloadStatus(context, holder.download, holder.downloadMask,
                    RouteServiceUtil.getDownloadService().getDownloadStatus(track));
        }

        Logger.d("z_album", "AlbumTrackAdapter isFreeListenAlbum:" + isFreeListenAlbum);
        if (isFreeListenAlbum) {
            ////畅听专辑隐藏付费图标
            holder.priceIv.setVisibility(View.GONE);
        }
    }

    private void bindPaidViewUnlockWithTrack(ViewHolder holder, Track track, int position) {
        ViewStatusUtil.setVisible(View.GONE, holder.singleTrackPrice, holder.priceIv);
        if (!track.isAuthorized()) {
            showToUnlockStatus(holder, track, position);
            return;
        } else if (FreeListenManager.getInstance().isUnlockTrack(track)) {
            //隐藏下载
            ViewStatusUtil.setVisible(View.GONE, holder.downloadMask);
            if (FreeListenHostManager.getInstance().canUnlockWithTime()) {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.adHintUnLockDoneIv);
            } else if (FreeListenHostManager.getInstance().canUnlockWithTrack()) {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.adHintUnLockDoneTv);
            } else {
                ViewStatusUtil.setVisible(View.GONE, holder.adHintUnLockDoneTv, holder.adHintUnLockDoneIv);
            }
        }
    }

    private void bindPaidViewUnlockWithTime(@NonNull AlbumM albumM, ViewHolder holder, Track track, int position) {
        ViewStatusUtil.setVisible(View.GONE, holder.singleTrackPrice, holder.priceIv);
        if (!track.isAuthorized()) {
            showToUnlockStatus(holder, track, position);
        } else {
            if (FreeListenManager.getInstance().isUnlockTrack(track)) {
                Pair<Long, Long> countDownInfo = UnlockModeInfo.getCountDownTimes(albumM.getUnlockModeInfos());
                boolean isExpired = true;
                if (null != countDownInfo && null != countDownInfo.first && null != countDownInfo.second) {
                    long timeLeft = CountDownTimeUtil.getRealCountDownDuration(countDownInfo.first, countDownInfo.second);
                    if (0 < timeLeft) {
                        isExpired = false;
                    }
                }
                if (isExpired) {
                    showToUnlockStatus(holder, track, position);
                } else {
                    //隐藏下载
                    ViewStatusUtil.setVisible(View.GONE, holder.downloadMask);
                    if (FreeListenHostManager.getInstance().canUnlockWithTime()) {
                        ViewStatusUtil.setVisible(View.VISIBLE, holder.adHintUnLockDoneIv);
                    } else if (FreeListenHostManager.getInstance().canUnlockWithTrack()) {
                        ViewStatusUtil.setVisible(View.VISIBLE, holder.adHintUnLockDoneTv);
                    } else {
                        ViewStatusUtil.setVisible(View.GONE, holder.adHintUnLockDoneTv, holder.adHintUnLockDoneIv);
                    }
                }
            } else {
                // 通过其他方式获得了权限，所以不做额外处理
            }
        }
    }

    private void showToUnlockStatus(ViewHolder holder, Track track, int position) {
        if (null == holder) {
            return;
        }
        ViewStatusUtil.setVisible(View.VISIBLE, holder.freeUnlockIv);
        //隐藏下载
        ViewStatusUtil.setVisible(View.GONE, holder.downloadMask);
        setClickListener(holder.freeUnlockIv, track, position, holder);
    }

    private void setTrackDownloadStatus(Context context, ImageView view, View maskView, int status) {
        if (view == null) {
            return;
        }
        switch (status) {
            case IDownloadStatus.DOWNLOAD_WAITING:
            case IDownloadStatus.DOWNLOADING:
                maskView.setContentDescription("下载中");
                setDownloadEnable(maskView, false);
                if (context == null) return;
                view.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_btn_downloading);
                Animation operatingAnim = AnimationUtils.loadAnimation(context, com.ximalaya.ting.android.host.R.anim.host_loading_rotate);
                LinearInterpolator lin = new LinearInterpolator();
                operatingAnim.setInterpolator(lin);
                view.clearAnimation();
                view.startAnimation(operatingAnim);
                break;
            case IDownloadStatus.DOWNLOAD_FINISH:
                maskView.setContentDescription("已下载");
                setDownloadEnable(maskView, false);
                view.clearAnimation();
                view.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_album_btn_downloaded_new);
                break;
            default:
                maskView.setContentDescription("下载");
                setDownloadEnable(maskView, true);
                view.clearAnimation();
                Drawable drawable = context.getResources().getDrawable(R.drawable.host_ic_download_n_n_line_regular_20).mutate();
                drawable.setColorFilter(context.getResources().getColor(R.color.host_color_666666_8d8d91), PorterDuff.Mode.SRC_IN);
                view.setImageDrawable(drawable);
                break;
        }
    }

    @Override
    public void onClick(View view, Track track, int position, BaseViewHolder h) {
        int i = view.getId();
        if (i == R.id.main_btn_download_mask) {
            downloadTrack(track, position);
        } else if (i == R.id.main_iv_price) {
            if (!OneClickHelper.getInstance().onClick(view)) {
                return;
            }
            AlbumFragmentMarkPointManager.Companion.markPointOnClickPriceIconInTrackList(track, position);
            showUniversalPaymentActionsDialog(track, position, true);
        } else if (i == R.id.main_iv_video) {
            if (OneClickHelper.getInstance().onMiddleTimeGapClick(view)) {
                if (ConstantsOpenSdk.isDebug) {
                    Log.d("z_video", "onClick main_iv_video s1, isAllVideo:" + isAllVideo);
                }
                if (isAllVideo) {
                    //全视频时，点击 icon 播放声音
                    if (track instanceof TrackM) {
                        TempDataManager.getInstance().saveBoolean(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_TO_AUDIO, true);
                        mFragmentActionListener.onMyItemClick(view, (TrackM) track, position + 1, position);
                    }
                } else {
                    onVideoIconClick(track);
                }
            }
        } else if (i == R.id.main_iv_audio_for_all_video) {
            if (OneClickHelper.getInstance().onMiddleTimeGapClick(view)) {
                if (ConstantsOpenSdk.isDebug) {
                    Log.d("z_video", "onClick main_iv_video_all_video s1, isAllVideo:" + isAllVideo);
                }
                if (isAllVideo || ElderlyModeManager.getInstance().isElderlyMode()) {
                    //全视频时，点击 icon 播放声音
                    if (track instanceof TrackM) {
                        TempDataManager.getInstance().saveBoolean(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_TO_AUDIO, true);
                        mFragmentActionListener.onMyItemClick(view, (TrackM) track, position + 1, position);
                    }
                } else {
                    onVideoIconClick(track);
                }
            }
        } else if (i == R.id.main_iv_ppt) {
            onPptIconClick(track);
        } else if (i == R.id.main_tv_single_track_price) {
            if (!OneClickHelper.getInstance().onClick(view)) {
                return;
            }
            showUniversalPaymentActionsDialog(track, position, true);
        } else if (R.id.main_single_track_free_unlock == i) {
            if (!OneClickHelper.getInstance().onClick(view)) {
                return;
            }
            AlbumM album = mAlbumMDataProvider.getAlbumM();
            boolean isAuthorized = null != album && album.isAuthorized();
            String currPage = isAuthorized ? "afterSaleDetailPage" : "preSaleDetailPage";
            AlbumFragmentMarkPointManager.Companion.markPointOnTrackListItemClick(mAlbumMDataProvider.getAlbumM(), track,
                    currPage, position, getTrackPlayStatus(track), "2", "音频");
            showUniversalPaymentActionsDialog(track, position, true);
        }
    }

    public void showUniversalPaymentActionsDialog(Track track, int position, boolean isFromIcon) {
        CommonTrackList<TrackM> trackList = null == mAlbumMDataProvider ? null : (null == mAlbumMDataProvider.getAlbumM() ? null : mAlbumMDataProvider.getAlbumM().getCommonTrackList());
        String source = AlbumTypeUtil.TYPE_FROM_UNIVERSAL_CUSTOM_ALBUM == fromFragmentType ? UniversalPayment.SOURCE_PRODUCT_TRACK_LIST : UniversalPayment.SOURCE_ALBUM_TRACK_LIST;
        RequestMaterial requestMaterial = new RequestMaterial(mAlbumId, source)
                .setTrack(track).setActionOuterImp(getActionOuterImp()).setTrackList(trackList);
        if (isFromIcon) {
            requestMaterial.shutAutoPerform();
        }
        UniversalPaymentActionsDialog.requestAndShowDialog(mFragment, requestMaterial);
    }

    public void onVideoIconClick(Track track) {
        if (!(track instanceof TrackM)) {
            return;
        }
        TrackM trackM = (TrackM) track;
        if (trackM.getProcessState() == 2) {  //转码完成
            if (!track.isPaid() || track.isFree() || track.isAuthorized() || track.isAudition()) {
                if (context instanceof MainActivity) {
                    int switchOutSec = XmPlayerManager.getInstance(context).getPlayCurrPositon() / 1000;
                    XmPlayerManager.getInstance(context).onSwitchOutAudio(switchOutSec);
                    List<Track> listData = getListData();
                    boolean isAsc = true;
                    if (track.getAlbum() != null) {
                        isAsc = AlbumOrderManager.isAlbumSortAsc(track.getAlbum().getAlbumId());
//                        isAsc = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getBooleanCompat(
//                                PreferenceConstantsInHost.KEY_IS_ASC + track.getAlbum().getAlbumId(), true);
                    }
                    Bundle bundle;
                    long albumId = track.getAlbum() != null ? track.getAlbum().getAlbumId() : 0;
                    if (listData instanceof ArrayList) {
                        bundle = new VideoPlayParamsBuildUtil.Builder().
                                setVideoList((ArrayList<Track>) listData).
                                setAlbumId(albumId).
                                setTrack(track).
                                setTrackId(track.getDataId())
                                .setAsc(isAsc)
                                .setIsAllVideo(isAllVideo)
                                .build();
                    } else {
                        bundle = new VideoPlayParamsBuildUtil.Builder().
                                setAlbumId(albumId).
                                setTrack(track).
                                setTrackId(track.getDataId())
                                .setIsAllVideo(isAllVideo)
                                .setAsc(isAsc).build();
                    }
                    VideoPlayParamsBuildUtil.startChosenFragment(((MainActivity) context), bundle, null);
                }
            } else {
                if (track.isPaid() && !track.isAuthorized()) {
                    CustomToast.showFailToast("购买专辑后可观看视频");
                }
            }
        } else { //转码未完成
            CustomToast.showFailToast("视频转码中，请稍后再试");
        }
        new UserTracking("album", "page").setSrcPageId(track.getAlbum() != null ? track.getAlbum().getAlbumId() : 0)
                .setSrcModule("声音条").setItemId("视频页").setTrackId(track.getDataId()).setSrcSubModule("视频按钮")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_ALBUM_PAGE_CLICK);
    }

    private void onPptIconClick(Track track) {
        if (track instanceof TrackM && context instanceof MainActivity) {
            TrackM trackM = (TrackM) track;
            PlayTools.playTrack(context, trackM, false, null);
            boolean gotoPptPage = trackM.isRichAudio() && (!track.isPaid() || track.isFree() || track.isAuthorized());
            if (gotoPptPage) {  //只有免费，整首试听和已购的需要直接跳转ppt页
                ((MainActivity) context).showPlayFragment(null, PlayerManager.PLAY_TAG);
            }
        }
        new UserTracking("album", "page").setSrcPageId(track.getAlbum() != null ? track.getAlbum().getAlbumId() : 0)
                .setSrcModule("声音条").setItemId("PPT页").setTrackId(track.getDataId()).setSrcSubModule("PPT按钮")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_ALBUM_PAGE_CLICK);
    }

    private void downloadTrack(Track track, int position) {
        if (CopyrightUtil.showCopyRightTipsDialogIfNeeded(mFragment, mCopyrightExtendInfo)) {
            return;
        }
        if (track.isHasCopyRight()) {
            logTrackDownload(position, track);
            // 会员专享
            if (!track.isAuthorized()
                    && !track.isFree()
                    && mAlbumMDataProvider != null
                    && mAlbumMDataProvider.getAlbumM() != null
                    && mAlbumMDataProvider.getAlbumM().isVipFree()) {
                showOnlyVipFreeGuideDialog();
                return;
            }
            // 会员抢先听
            if (track.vipPriorListenStatus == 1
                    && !UserInfoMannage.isVipUser()) {
                showVipPriorListenDownloadDialog();
                return;
            }
            // ximi会员抢先听
            boolean singlePaid = track.isPaid() && track.getPaidType() == 1; // 单集付费
            // 声音未授权且声音属于ximi声音用户未加入ximi圈
            if (!track.isAuthorized()
                    && mAlbumMDataProvider != null
                    && mAlbumMDataProvider.getAlbumM() != null
                    && !mAlbumMDataProvider.getAlbumM().hasJoinedXimi
                    && !TextUtils.isEmpty(mAlbumMDataProvider.getAlbumM().ximiUrl)
                    && (track.ximiFirstStatus == AppConstants.XIMI_VIP_PRIORITY
                    || (singlePaid && track.ximiVipFreeType == AppConstants.XIMI_VIP_FREE))) {
                showXimiGuideDialog(context, mAlbumMDataProvider.getAlbumM().ximiUrl, mFragmentActionListener);
                return;
            }
            if (RouteServiceUtil.getDownloadService().isTrackQualitySettingActive()) {
                super.downloadInner(track, canShowSnackBar(track)? mOnStartDownloadCallBack : null);
            } else {
                ActionCallBack callBack = new ActionCallBack() {
                    @Override
                    public void onConfirm() {
                        downloadInner(track, canShowSnackBar(track)? mOnStartDownloadCallBack : null);
                    }

                    @Override
                    public void onCancel() {

                    }
                };

                try {
                    ChooseTrackQualityDialog.newInstance(context, track, callBack).show();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            CustomToast.showFailToast("应版权方要求，你所在的地区暂不可下载该资源");
        }
    }

    private void showOnlyVipFreeGuideDialog() {
        Activity activity;
        if (context instanceof Activity) {
            activity = (Activity) context;
        } else {
            activity = MainApplication.getTopActivity();
        }
        if (activity == null) return;
        if (mAlbumMDataProvider == null
                || mAlbumMDataProvider.getAlbumM() == null
                || mAlbumMDataProvider.getAlbumM().getListGuideVipResourceModel() == null)
            return;
        final AlbumListGuideVipResourceModel model = mAlbumMDataProvider.getAlbumM().getListGuideVipResourceModel();
        final XmBaseDialog dialog = new XmBaseDialog(activity);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        Window window = dialog.getWindow();
        if (window != null)
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        View dialogView = LayoutInflater.from(context).inflate(R.layout.main_dialog_vip_free_single_buy_track_guide, null);
        ImageView closeIcon = dialogView.findViewById(R.id.main_iv_close);
        TextView titleText = dialogView.findViewById(R.id.main_title);
        final TextView contentText = dialogView.findViewById(R.id.main_desc);
        TextView button = dialogView.findViewById(R.id.main_button);

        titleText.setText(model.title);
        contentText.setText(model.intro);
        button.setText(model.buttonContent);
        closeIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (context instanceof MainActivity) {
                    Bundle bundle = new Bundle();
                    bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, model.url);
                    ((MainActivity) context).startFragment(NativeHybridFragment.class, bundle, v);
                }
                dialog.dismiss();
            }
        });
        dialog.setContentView(dialogView);
        dialog.setDialogId("vip_free_single_buy_track_guide");
        dialog.show();
    }

    private void logTrackDownload(int position, Track track) {
        if (track != null) {
            long albumId = 0;
            if (track.getAlbum() != null) {
                albumId = track.getAlbum().getAlbumId();
            }
            boolean isVipFirst = track != null && track.vipPriorListenStatus == 1;
            new UserTracking("5949", "album", "button").setSrcPageId(albumId).setSrcModule("trackDownload")
                    .setItemId("下载").setSrcPosition(position + 1).setTrackId(track.getDataId()).setIsVIP(UserInfoMannage.isVipUser())
                    .setIsVipFirst(isVipFirst)
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_ALBUM_PAGE_CLICK);
        }
    }

    private void showVipPriorListenDownloadDialog() {
        if (mAlbumMDataProvider == null) {
            return;
        }
        final AlbumM albumM = mAlbumMDataProvider.getAlbumM();
        Activity activity = null;
        if (context instanceof Activity) {
            activity = (Activity) context;
        } else {
            activity = MainApplication.getTopActivity();
        }
        if (activity == null || albumM == null || TextUtils.isEmpty(albumM.vipPriorListenDownloadPopupRes))
            return;

        try {
            JSONObject jsonObject = new JSONObject(albumM.vipPriorListenDownloadPopupRes);
            VipPriorListenDialog.Data data = new VipPriorListenDialog.Data(
                    jsonObject.optString("title"), jsonObject.optString("intro"),
                    jsonObject.optString("buttonContent"), jsonObject.optString("url")
            );
            VipPriorListenDialog dialog = new VipPriorListenDialog(activity, data);
            dialog.setLogListener(new VipPriorListenDialog.TrackingLogListener() {
                @Override
                public void onGetVipButtonClick() {
                    new UserTracking("5951", "album", "button").setSrcPageId(albumM.getId()).setSrcModule("单集下载提示弹窗").setItemId("免费领会员")
                            .setIsVipFirst(true)
                            .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_ALBUM_PAGE_CLICK);
                }
            });
            dialog.show();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void showXimiGuideDialog(Context context, String url, FragmentActionListener listener) {
        Activity activity;
        if (context instanceof Activity) {
            activity = (Activity) context;
        } else {
            activity = MainApplication.getTopActivity();
        }
        if (activity == null) {
            return;
        }
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(activity);
            return;
        }
        final XmBaseDialog dialog = new XmBaseDialog(activity);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }
        View dialogView = LayoutInflater.from(context).inflate(R.layout.main_dialog_ximi_circle_download_guide, null);
        dialogView.findViewById(R.id.main_iv_close).setOnClickListener(v -> {
            dialog.dismiss();
        });
        dialogView.findViewById(R.id.main_goto_ximi_circle_tv).setOnClickListener(v -> {
            if (context instanceof MainActivity && !TextUtils.isEmpty(url)) {
                NativeHybridFragment.start((MainActivity) context, url, true);
                if (listener != null) {
                    listener.registerXiMiPayBroadCast();
                }
            }
            dialog.dismiss();
        });
        dialog.setContentView(dialogView);
        dialog.setDialogId("vip_free_single_buy_track_guide");
        dialog.show();
    }

    public void actionBuyTrack(Track track, boolean isFromAdChooseDialog) {
        if (track == null || !track.isPaid() || track.isAuthorized() || track.isFree())
            return;
        if (!track.isHasCopyRight()) {
            CustomToast.showFailToast("版权方要求，该资源在该地区无法购买");
            return;
        }
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(context);
            // dUnLockPaidManager.registerLoginStatus(track.getDataId());
            return;
        }
        if (getSingleAlbumPayManager() != null) {
            getSingleAlbumPayManager().buySinglePayAlbumTrack(track, false, isFromAdChooseDialog, BundleBuyDialogFragment1.FLAG_ALBUM_FRAGMENT_NEW);
        }
    }

    private SingleAlbumPayManager getSingleAlbumPayManager() {
        if (singleAlbumPayManager == null && payResultListener != null) {
            singleAlbumPayManager = new SingleAlbumPayManager(payResultListener);
            singleAlbumPayManager.setAlbumStatusChangedListener(albumStatusChangedListener);
            if (mAlbumMDataProvider != null && !TextUtils.isEmpty(mAlbumMDataProvider.getAlbumActivityParams())) {
                singleAlbumPayManager.setActivityTraceParams(mAlbumMDataProvider.getAlbumActivityParams());
            }
        }
        return singleAlbumPayManager;
    }

    private BehaviorAction.IActionOuterImp getActionOuterImp() {
        if (null == actionOuterImp) {
            actionOuterImp = new BehaviorAction.IActionOuterImp() {
                @Override
                public boolean doOnTrackPurchase(Track track, BehaviorAction action) {
                    actionBuyTrack(track, false);
                    return true;
                }

                @Override
                public boolean doOnPurchaseForPurchase(CommonBehavior behavior, Track track, BehaviorAction action) {
                    UniversalPaymentCommonFragmentFinishCallBack callBack = UniversalPaymentCommonFragmentFinishCallBack.buildForBuyAlbum(mAlbumId);
                    BaseFragment2 fragment = null;
                    Fragment temp = null == mFragment ? null : mFragment.getParentFragment();
                    if (temp instanceof BaseFragment2) {
                        fragment = (BaseFragment2) temp;
                    }
                    return UniversalPayment.Util.enterRnWithCallBack(fragment, action.url, callBack);
                }
            };
        }
        return actionOuterImp;
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_track_view_warpped;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public Object getItem(int position) {
        Object obj = super.getItem(position);
        if (obj instanceof Track && mPlaysource > 0) {
            ((Track) obj).setPlaySource(mPlaysource);
        }
        return obj;
    }

    @Override
    public void onAdUnlockVipRewardSuccess(long albumId, long trackId) {
        if (albumId != mAlbumId || ToolUtil.isEmptyCollects(listData)) {
            return;
        }
        try {
            for (Track track : listData) {
                if (null != track && trackId == track.getDataId()) {
                    AdMakeVipLocalManager.getInstance().setAsAuthorizedTrack(track);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onAdUnlockVipRewardSuccess(Track track) {

    }

    public void setDownloadEnable(View downloadMask, boolean enable) {
        if (downloadMask != null) {
            downloadMask.setEnabled(enable);
        }
    }

    public void setCopyrightExtendInfo(@Nullable CopyrightExtendInfo copyrightExtendInfo) {
        mCopyrightExtendInfo = copyrightExtendInfo;
    }

    public static class ViewHolder extends BaseViewHolder {
        public View adHintArea;
        public View root;
        public TextView title;
        TextView updateAt;
        TextView lastListenTag;
        TextView playCount;
        TextView commentCount;
        TextView duration;
        View border;
        TextView playSchedule;
        ImageView download; //控制控制download的图标
        View downloadMask; //控制控制download的点击
        ImageView pptIv;
        ImageView videoIv;
        ImageView audioIvForAllVideo;
        public ImageView priceIv;
        public TextView singleTrackPrice;
        ViewGroup orderNoContainer;
        TextView orderNo;
        ImageView playingFlag;
        LinearLayout vAlbumTagsGroup;
        TextView tagTv;
        TextView adHintUnLockDoneTv;
        ImageView adHintUnLockDoneIv;
        ImageView freeUnlockIv;

        public ViewHolder(View convertView) {
            root = convertView;
            adHintArea = convertView.findViewById(R.id.main_track_view_ad_info_tip);
            updateAt = convertView.findViewById(R.id.main_update_at);
            lastListenTag = convertView.findViewById(R.id.main_last_listen_tag);
            title = convertView.findViewById(R.id.main_sound_name);
            priceIv = convertView.findViewById(R.id.main_iv_price);
            singleTrackPrice = convertView.findViewById(R.id.main_tv_single_track_price);
            download = convertView.findViewById(R.id.main_btn_download);
            downloadMask = convertView.findViewById(R.id.main_btn_download_mask);
            playCount = convertView.findViewById(R.id.main_playtimes_num);
            duration = convertView.findViewById(R.id.main_tv_total_time);
            commentCount = convertView.findViewById(R.id.main_comments_num);
            playSchedule = convertView.findViewById(R.id.main_play_schedule);
            border = convertView.findViewById(R.id.main_track_divider);
            orderNoContainer = convertView.findViewById(R.id.main_rl_order_no_container);
            orderNo = convertView.findViewById(R.id.main_tv_order_no);
            playingFlag = convertView.findViewById(R.id.main_iv_playing_flag);
            vAlbumTagsGroup = convertView.findViewById(R.id.main_album_tags_group);
            tagTv = convertView.findViewById(R.id.main_track_tags_tv);
            adHintUnLockDoneTv = convertView.findViewById(R.id.main_single_track_ad_hint_unlock_done_tv);
            adHintUnLockDoneIv = convertView.findViewById(R.id.main_single_track_ad_hint_unlock_done_iv);
            pptIv = convertView.findViewById(R.id.main_iv_ppt);
            videoIv = convertView.findViewById(R.id.main_iv_video);
            audioIvForAllVideo = convertView.findViewById(R.id.main_iv_audio_for_all_video);
            freeUnlockIv = convertView.findViewById(R.id.main_single_track_free_unlock);
        }
    }

    private static class UnlockHintCountDown extends CountDownTimer {
        public static void setCountDownText(TextView tv, long millisUntilFinished) {
            if (null == tv) {
                return;
            }
            String text = TEXT_UNLOCK_HINT_AREA_TICKING + CountDownTimeUtil.buildCountDownStyle1(millisUntilFinished);
            ViewStatusUtil.setText(tv, text);
        }

        private final WeakReference<View> hintAreaReference;

        public UnlockHintCountDown(long millisInFuture, View area) {
            super(millisInFuture, 1000L);
            this.hintAreaReference = new WeakReference<>(area);
        }

        @Override
        public void onTick(long millisUntilFinished) {
            View area = hintAreaReference.get();
            if (null == area) {
                cancel();
                return;
            }
            TextView textTv = area.findViewById(R.id.main_track_view_ad_info_tip_text);
            setCountDownText(textTv, millisUntilFinished);
        }

        @Override
        public void onFinish() {
            View area = hintAreaReference.get();
            if (null != area) {
                TextView textTv = area.findViewById(R.id.main_track_view_ad_info_tip_text);
                ViewStatusUtil.setText(textTv, TEXT_UNLOCK_HINT_AREA);
            }
        }
    }

    private OnStartDownloadCallBack mOnStartDownloadCallBack = new OnStartDownloadCallBack() {
        @Override
        public void onSuccess(Track track) {
            if (TextUtils.isEmpty(snackbarAb)) {
                snackbarAb = ABTest.getBoolean("snackbar", false)+"";
            }
            if (!"true".equals(snackbarAb)) {
                return;
            }
            showDomainDownloadSnackBar(TYPE_SNACKBAR_SINGLE, track);
        }
    };

    private void showDomainDownloadSnackBar(int type, Track track){
        if(track ==null){
            return;
        }
        ImageManager.from(context).downloadBitmap(mAlbumCoverUrl, new ImageManager.DisplayCallback() {
            @Override
            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                ColorUtil.getDefaultAlbumCoverDomainColorForAlbum(bitmap, 0xFF2D2D2D, dstColor -> {
                    int color = ColorUtil.covertColorToLightVibrant(dstColor);
                    showDownloadSnackBar(type, track, color);
                });
            }
        });
    }

    private void showDownloadSnackBar(int type, Track track, int color) {
        if (mFragment == null || mFragment.getActivity() == null || !mFragment.isRealVisable() || track == null) {
            return;
        }

        int index = listData.indexOf(track);
        if (index < 0) {
            return;
        }
        boolean mIsAsc = true;
        if (index < listData.size() -1){
            int curNo = ((TrackM) track).getOrderNo();
            int nextNo = ((TrackM) listData.get(index+1)).getOrderNo();
            mIsAsc = nextNo > curNo;
        }

        if (mDownloadSnackBar == null) {
            View contentView = LayoutInflater.from(context).inflate(R.layout.main_snackbar_album_download, null);
            mSnackBarTv = contentView.findViewById(R.id.main_tv_content);
            mSnackBarTvAfter = contentView.findViewById(R.id.main_tv_content_after);
            mSnackBarIv = contentView.findViewById(R.id.main_iv_icon);

            int marginHorizontal = BaseUtil.dp2px(context, 16);
            int marginBot = BaseUtil.dp2px(context, 92);
            mDownloadSnackBar = Snackbar.with(context)
                    .customView(contentView)
                    .swapVertical()
                    .duration(Snackbar.SnackbarDuration.LENGTH_MUCH_LONG)
                    .margin(marginHorizontal, 0, marginHorizontal, marginBot)
                    .position(Snackbar.SnackbarPosition.BOTTOM_CENTER);
        }
        mSnackBarTvAfter.setTextColor(color);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            TextViewCompat.setCompoundDrawableTintList(mSnackBarTvAfter, ColorStateList.valueOf(color));
        }
        String leftText = type == TYPE_SNACKBAR_SINGLE? "已添加到下载队列" : "提交下载成功";
        String rightText = type == TYPE_SNACKBAR_SINGLE? "下载后续声音" : "查看我的下载";
        mSnackBarTv.setText(leftText);
        mSnackBarTvAfter.setText(rightText);
        mSnackBarIv.setImageResource(type == TYPE_SNACKBAR_SINGLE? R.drawable.main_icon_download_single_snack :  R.drawable.main_icon_download_batch_snack);
        boolean finalMIsAsc = mIsAsc;
        mSnackBarTvAfter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (type == TYPE_SNACKBAR_SINGLE) {
                    AlbumM album = null == mAlbumMDataProvider ? null : mAlbumMDataProvider.getAlbumM();
                    if (album != null) {
                        //获取本地保存专辑的排序方式
                        DownloadTrackDlgFragment fra = DownloadTrackDlgFragment.newInstance(finalMIsAsc,album.getIncludeTrackCount(), album.getUid(), mAlbumId, ((TrackM) track).getOrderNo() - 1, track.getDataId(), album.getRequestId());
                        fra.setDlgCallBack(new DownloadTrackDlgFragment.OnDlgCallBack() {
                                    @Override
                                    public void onConfirm(boolean needShowSnackBar) {
                                        if (needShowSnackBar) {
                                            showDomainDownloadSnackBar(TYPE_SNACKBAR_BATCH, track);
                                        }
                                    }
                                    @Override
                                    public void onCustomize() {
                                        if (mOnCustomizeClickListener != null) {
                                            mOnCustomizeClickListener.onCustomizeClick();
                                        }
                                    }
                                });
                        mFragment.startFragment(fra, -1, -1);
                    }
                    mDownloadSnackBar.dismiss();
                } else {
                    mDownloadSnackBar.dismiss();
                    // 跳转到下载中界面
                    MyListenRouterUtil.getMyListenBundle(bundleModel -> {
                        IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                        if (fragAction != null && mFragment.canUpdateUi()) {
                            Bundle bundle = new Bundle();
                            bundle.putString(BundleKeyConstants.KEY_MYLISTEN_IS_FROM, BundleKeyConstants.KEY_MYLISTEN_IS_FROM_OTHER);
                            bundle.putBoolean(BundleKeyConstants.KEY_MYLISTEN_NEED_HIDE_BADGE, true);
                            BaseFragment2 frag = fragAction.newDownloadFragment();
                            if (frag != null) {
                                frag.setArguments(bundle);
                                mFragment.startFragment(frag);
                            }
                        }
                    });
                }
                AlbumM album = (mAlbumMDataProvider != null) ? mAlbumMDataProvider.getAlbumM() : null;
                // 新声音播放页-下载统一snakbar  点击事件
                new XMTraceApi.Trace()
                        .click(53759) // 用户点击时上报
                        .put("currPage", "newPlay")
                        .put("Text", leftText) // 横条左边的文案
                        .put("Item", rightText) // 横条右边的按钮文案
                        .put("currAlbumId", ""+mAlbumId)
                        .put("currTrackId", ""+track.getDataId())
                        .put(XmRequestIdManager.XM_REQUEST_ID, album != null ? album.getRequestId() : "")
                        .createTrace();
            }
        });

        if (!mDownloadSnackBar.isShowing()) {
            mDownloadSnackBar.show(mFragment.getActivity());
        }
        if (index == listData.size() -1) {
            mSnackBarTvAfter.setVisibility(View.GONE);
        } else {
            mSnackBarTvAfter.setVisibility(View.VISIBLE);
        }
        AlbumM album = (mAlbumMDataProvider != null) ? mAlbumMDataProvider.getAlbumM() : null;

        // 新声音播放页-下载统一snakbar  控件曝光
        new XMTraceApi.Trace()
                .setMetaId(53760)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newPlay")
                .put("Text",leftText) // 横条左边的文案
                .put("Item", mSnackBarTvAfter.getVisibility() == View.VISIBLE? rightText : "") // 横条右边的按钮文案
                .put("currAlbumId", ""+mAlbumId)
                .put("currTrackId", ""+track.getDataId())
                .put(XmRequestIdManager.CONT_ID, String.valueOf(track.getDataId()))
                .put(XmRequestIdManager.CONT_TYPE, "newPlay")
                .put(XmRequestIdManager.XM_REQUEST_ID, (album != null) ? album.getRequestId() : "")
                .createTrace();
    }

    private boolean canShowSnackBar(Track track){
        if (track instanceof TrackM) {
            AlbumM album = null == mAlbumMDataProvider ? null : mAlbumMDataProvider.getAlbumM();
            if (album != null){
                return true;
            }
        }
        return false;
    }

    public interface OnCustomizeClickListener {
        void onCustomizeClick();
    }
    private OnCustomizeClickListener mOnCustomizeClickListener = null;
    public void setOnCustomClickListener(OnCustomizeClickListener onCustomizeClickListener){
        mOnCustomizeClickListener = onCustomizeClickListener;
    }
}
