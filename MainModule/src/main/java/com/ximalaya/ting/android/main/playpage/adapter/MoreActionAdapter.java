package com.ximalaya.ting.android.main.playpage.adapter;

import android.Manifest;
import android.app.Activity;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.TingListConstants;
import com.ximalaya.ting.android.host.drivemode.DriveModeActivityV3;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.ForwardVideoManager;
import com.ximalaya.ting.android.host.manager.ad.gamead.AdGameUtil;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.tinglist.ITingListManager;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.MyListenRouterUtil;
import com.ximalaya.ting.android.host.util.XmRequestPage;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adModule.fragment.RemoveAdMorePageDialogFragment;
import com.ximalaya.ting.android.main.dialog.CallingRingtoneDownloadDialog;
import com.ximalaya.ting.android.main.fragment.find.other.recommend.ReportFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.AddOrEditAlarmFragment;
import com.ximalaya.ting.android.main.playModule.view.DlnaActionDialog;
import com.ximalaya.ting.android.main.playpage.dialog.PlayMoreActionDialogFragment;
import com.ximalaya.ting.android.main.playpage.internalservice.IAudioAdComponentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.util.other.CopyrightUtil;
import com.ximalaya.ting.android.main.util.ui.RingtoneUtil;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.DrawableRes;
import androidx.fragment.app.FragmentActivity;

import static com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants.IRemoveAdHintBenefit.REMOVE_AD_HINT_BENEFIT_MOREPAGE;

/**
 * Created by ZhuPeipei on 2020-05-28 13:45.
 *
 * @Description: 更多操作adapter
 */
public class MoreActionAdapter extends HolderAdapter<MoreActionAdapter.MoreActionTag> {
    private final BaseDialogFragment mDialogFragment;

    public enum MoreActionTag {
        Alarm(R.drawable.main_play_action_plan_terminate, "定时播放"),
        //TingList(R.drawable.main_play_action_add_tinglist, "加到听单"),
        Reward(R.drawable.main_play_action_complain_reward, "打赏"),
        OpenReward(R.drawable.main_play_action_complain_reward, "开通打赏"),
        Dlna(R.drawable.main_play_action_dlna, "连接外设"),
        LoteGame(R.drawable.main_play_action_game, "边听边玩"),
        DriveMode(R.drawable.main_play_action_drive_more, "驾驶模式"),
        FreeAd(R.drawable.main_play_action_ad_free, "免广告"),
        Ring(R.drawable.main_play_action_ring, "设为铃声"),
        CopyRight(R.drawable.main_play_action_copyright, "版权申诉"),
        Complain(R.drawable.main_play_action_complain, "举报");

        MoreActionTag(int img, String title) {
            this.img = img;
            this.title = title;
        }

        MoreActionTag(String tag, String title) {
            this.tag = tag;
            this.title = title;
        }

        public void setImg(int img) {
            this.img = img;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public void setExtraData(Object extraData) {
            this.extraData = extraData;
        }

        @DrawableRes
        private int img;
        private String title;
        private String tag;
        private Object extraData;
    }

    public MoreActionAdapter(BaseDialogFragment fragment, List<MoreActionTag> listData) {
        super(fragment.getContext(), listData);
        mDialogFragment = fragment;
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, MoreActionTag moreActionTag, int position) {
        if (!(holder instanceof MoreActionViewHolder) || moreActionTag == null) {
            return;
        }
        MoreActionViewHolder h = (MoreActionViewHolder) holder;

        if (moreActionTag.img != 0) {
            h.coverIv.setImageResource(moreActionTag.img);
            h.coverIv.setVisibility(View.VISIBLE);
            h.tagTv.setVisibility(View.GONE);
        } else {
            h.tagTv.setText(moreActionTag.tag);
            h.coverIv.setVisibility(View.GONE);
            h.tagTv.setVisibility(View.VISIBLE);
        }
        h.titleTv.setText(moreActionTag.title);
        setClickListener(h.itemView, moreActionTag, position, holder);
        if (moreActionTag == MoreActionTag.FreeAd) {
            Logger.i("-----------msg ---- ", " -------- moreActionTag = FreeAd 上报 ");
            adRecord();
        }
    }

    private void adRecord() {
        List<Advertis> advertisList = XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).getForwardAdvertis();
        if (advertisList == null) {
            AdvertisList curAdvertis =  XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).getCurSoundAdList();
            if (advertisList != null) {
                advertisList = curAdvertis.getAdvertisList();
            }
        }

        // 如果有广告数据，则曝光入口
        if (!ToolUtil.isEmptyCollects(advertisList) && advertisList.get(0) != null) {
            Advertis advertis = advertisList.get(0);
            AdManager.adRecord(MainApplication.getMyApplicationContext(),
                    advertis, AdReportModel.newBuilder(
                            AppConstants.AD_LOG_TYPE_SHOW_OB,
                            AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                            .benefitTip(REMOVE_AD_HINT_BENEFIT_MOREPAGE)
                            .promptObType("1")
                            .adPlayVersion(AdManager.getAdPlayVersion())
                            .ignoreTarget(true).build());
        }
    }

    @Override
    public void onClick(View view, MoreActionTag moreActionTag, int position, BaseViewHolder holder) {
        if (!mDialogFragment.canUpdateUi()) {
            return;
        }
        mDialogFragment.dismiss();

        switch (moreActionTag) {
            case Dlna:
                openDlnaDialog();
                break;
            case DriveMode:
                openDriveModePage();
                break;
            case Alarm:
                toAlarmPage();
                break;
//            case TingList:
//                addToTingList();
//                break;
            case Complain:
                complain();
                break;
            case CopyRight:
                toCopyRightPage();
                break;
            case Ring:
                doSetCallingRingtone();
                break;
            case LoteGame:
                jumpToLetoGameCenter();
                break;
            case FreeAd:
                showFreeAdBottomDialog();
                break;
            case Reward:
            case OpenReward:
                reward();
                break;
            default:
                break;
        }

        PlayingSoundInfo info = PlayPageDataManager.getInstance().getSoundInfo();
        if (info == null || info.trackInfo2TrackM() == null || info.toAlbumM() == null) {
            return;
        }
        TrackM track = info.trackInfo2TrackM();
        AlbumM album = info.toAlbumM();

        new XMTraceApi.Trace()
                .click(17637)
                .put("currPage", "newPlay")
                .put("currTrackId", String.valueOf(track.getDataId()))
                .put("currAlbumId", String.valueOf(album.getId()))
                .put("anchorId", String.valueOf(track.getUid()))
                .put("categoryId", String.valueOf(track.getCategoryId()))
                .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                .put("Item", moreActionTag.title)
                .createTrace();

        new XMTraceApi.Trace()
                .setMetaId(38339)
                .setServiceId("dialogClick")
                .put("item", moreActionTag.title)
                .put("currPage", "新声音播放页")
                .createTrace();
    }

    private void addToTingList() {
        PlayingSoundInfo soundInfo = PlayPageDataManager.getInstance().getSoundInfo();
        if (!mDialogFragment.canUpdateUi() || soundInfo == null || soundInfo.trackInfo2TrackM() == null) {
            return;
        }
        TrackM track = soundInfo.trackInfo2TrackM();
        if (!(mDialogFragment.getParentFragment() instanceof BaseFragment2)) {
            return;
        }
        final BaseFragment2 frag = (BaseFragment2) mDialogFragment.getParentFragment();
        MyListenRouterUtil.getMyListenBundle(bundleModel -> {
            IMyListenFunctionAction funAction = MyListenRouterUtil.getFunAction();
            if (funAction != null && frag != null && frag.canUpdateUi()) {
                ITingListManager tingListManager = funAction.newTingListManager(frag);
                boolean isMusic = false;
                if (soundInfo.otherInfo != null) {
                    isMusic = soundInfo.otherInfo.musicRelated == PlayingSoundInfo.OtherInfo.IS_MUSIC;
                }
                tingListManager.showTingList(TingListConstants.TYPE_TRACK, track.getDataId(),
                        track, isMusic, TingListConstants.TING_LIST_UBT_PREV_RESOURCE_TYPE_MORE_DIALOG);
                tingListManager.setITingListResultCallback(tingListInfoModel -> {
                    if (mDialogFragment instanceof PlayMoreActionDialogFragment) {
                        PlayMoreActionDialogFragment.ClickCallback callback = ((PlayMoreActionDialogFragment) mDialogFragment).getClickCallback();
                        if (callback != null) {
                            callback.onAddTingListSuccess(tingListInfoModel);
                        }
                    }
                });
            }
        });
        new UserTracking()
                .setSrcPage("track")
                .setTrackId(track.getDataId())
                .setSrcModule("more")
                .setItem("button")
                .setItemId("addToSubject")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);
    }

    private void showFreeAdBottomDialog() {

        List<Advertis> advertisList = XmPlayerManager.getInstance(context).getForwardAdvertis();
        if (advertisList == null) {
            AdvertisList curAdvertis =  XmPlayerManager.getInstance(context).getCurSoundAdList();
            if (advertisList != null) {
                advertisList = curAdvertis.getAdvertisList();
            }
        }

        if (advertisList == null) {
            return;
        }
        Advertis advertis = advertisList.get(0);
        if (advertis != null) {
            //1、广告、VIP均有返回，拉起弹层，用户可选择看视频或购买VIP免除广告，按原逻辑不做改动
            //2、VIP有返回，广告无返回，点击入口直接跳转至VIP落地页
            //3、广告有返回，VIP无返回，点击入口直接播放激励视频
            if (hasVipLink(advertis)) {
                RemoveAdMorePageDialogFragment removeAdBottomDialogFragment = new  RemoveAdMorePageDialogFragment();
                removeAdBottomDialogFragment.show(((FragmentActivity) context).getSupportFragmentManager(), "free_ad_dialog");
            } else {
                Activity optActivity = MainApplication.getOptActivity();
                if (ToolUtil.activityIsValid(optActivity)) {
                    ForwardVideoManager.getInstance().lookVideo(optActivity, 3, REMOVE_AD_HINT_BENEFIT_MOREPAGE, getRewardVideoCallBack(advertis), advertis);
                    AdManager.adRecord(context, advertis
                            , AdReportModel.newBuilder(
                                    AppConstants.AD_LOG_TYPE_CLICK_OB,
                                    AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                    .benefitTip(REMOVE_AD_HINT_BENEFIT_MOREPAGE)
                                    .promptObType("3")
                                    .ignoreTarget(true).build());
                }
            }
            AdManager.adRecord(context, advertis
                    , AdReportModel.newBuilder(
                            AppConstants.AD_LOG_TYPE_CLICK_OB,
                            AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                            .benefitTip(REMOVE_AD_HINT_BENEFIT_MOREPAGE)
                            .promptObType("1")
                            .ignoreTarget(true).build());
        }
    }

    private boolean hasVipLink(Advertis advertis) {
        if(advertis == null) {
            return false;
        }

        return !TextUtils.isEmpty(advertis.getCopywriting())
                && !TextUtils.isEmpty(advertis.getVipPaymentLink());
    }

    private ForwardVideoManager.IRewardVideoCallBack getRewardVideoCallBack(Advertis forwardAd) {
        IAudioAdComponentService audioAdComponentService =
                PlayPageInternalServiceManager.getInstance().getService(IAudioAdComponentService.class);
        ForwardVideoManager.IRewardVideoCallBack rewardVideoCallBack = null;
        if (audioAdComponentService != null) {
            rewardVideoCallBack = audioAdComponentService.getRewardVideoCallBack(forwardAd);
        }
        return rewardVideoCallBack;
    }

    private void jumpToLetoGameCenter() {
        if (mDialogFragment == null || mDialogFragment.getActivity() == null) {
            return;
        }
        TrackM track = getCurTrack();
        if (track == null || track.getAlbum() == null) {
            return;
        }

        // 专辑id 和 声音id
        AdGameUtil.jumpToGameBundle(track.getAlbum().getAlbumId(), track.getDataId(), track.getCategoryId());
    }

    private TrackM getCurTrack() {
        PlayingSoundInfo soundInfo = PlayPageDataManager.getInstance().getSoundInfo();
        if (soundInfo == null) {
            return null;
        }
        return soundInfo.trackInfo2TrackM();
    }

    /**
     * 设置铃声
     */
    private void doSetCallingRingtone() {
        Track track = getCurTrack();
        if (track == null || mDialogFragment == null || mDialogFragment.getActivity() == null) {
            return;
        }

        if (mDialogFragment.getParentFragment() instanceof BaseFragment2) {
            if (CopyrightUtil.showCopyRightTipsDialogIfNeeded((BaseFragment2) mDialogFragment.getParentFragment(),
                    PlayPageDataManager.getInstance().getSoundInfo())) {
                return;
            } else if (!track.isHasCopyRight()) {
                CustomToast.showFailToast(PlayPageDataManager.getInstance().getNoCopyrightMsg());
                return;
            }
        }

        Activity activity = mDialogFragment.getActivity();
        SharedPreferencesUtil settings = SharedPreferencesUtil
                .getInstance(activity);
        final long ringtoneTrackId = settings
                .getLong(PreferenceConstantsInHost.TINGMAIN_KEY_CALLING_RINGTONE_TRACKID);

        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().checkPermission(activity,
                    (IMainFunctionAction.ISetRequestPermissionCallBack) activity, new HashMap<String, Integer>() {
                        {
                            put(Manifest.permission.WRITE_EXTERNAL_STORAGE, com.ximalaya.ting.android.host.R.string.host_deny_perm_sdcard);
                        }
                    }, new IMainFunctionAction.IPermissionListener() {
                        @Override
                        public void havedPermissionOrUseAgree() {
                            File file;
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                                File vDirPath = new File(activity.getExternalFilesDir(Environment.DIRECTORY_RINGTONES), "media/ringtones");
                                file = new File(vDirPath, String.valueOf(ringtoneTrackId) + ".mp3");
                            } else {
                                file = new File(Environment.getExternalStorageDirectory() + "/media/ringtones",
                                        ringtoneTrackId + ".mp3");
                            }

                            if (ringtoneTrackId == track.getDataId() && file.exists()) {
                                String name = "";
                                if (track.getAnnouncer() != null) {
                                    name = track.getAnnouncer().getNickname();
                                }
                                String path = Uri.fromFile(file).toString();
                                RingtoneUtil.setMyRingtone(activity, path, RingtoneUtil
                                        .buildMediaInfo(track.getTrackTitle(),
                                                activity.getString(R.string.main_xm_ring), name));
                            } else {
                                if (track.isPaid() && !track.isFree()) {
                                    CustomToast.showFailToast("付费声音不支持设置为铃声");
                                    return;
                                }
                                CallingRingtoneDownloadDialog processDlg = new CallingRingtoneDownloadDialog(
                                        activity);
                                processDlg.setDownloadInfo(track);
                                processDlg.show();
                            }
                        }

                        @Override
                        public void userReject(Map<String, Integer> noRejectPermiss) {
                            CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_failed_to_request_storage_permission);
                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void openDlnaDialog() {
        if (!(mDialogFragment.getParentFragment() instanceof BaseFragment2)) {
            return;
        }
        TrackM track = getCurTrack();
        if (track == null) {
            return;
        }
        if (!track.isHasCopyRight()) {
            CustomToast.showFailToast("版权方要求，该资源在该地区无法播放");
            return;
        }
        DlnaActionDialog dlnaDialog = new DlnaActionDialog(mDialogFragment.getActivity(),
                (BaseFragment2) mDialogFragment.getParentFragment(), getCurTrack());
        dlnaDialog.toggle();

        new UserTracking()
                .setSrcPage("track")
                .setSrcPageId(track.getDataId())
                .setSrcModule("外放设备")
                .setItem("button")
                .setItemId("外放设备")
                .setId("5273")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);

        new UserTracking()
                .setSrcPage("track")
                .setSrcPageId(track.getDataId())
                .setSrcModule("功能入口")
                .setItem("button")
                .setItemId("投射")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);
    }

    private void openDriveModePage() {
        //DriveModeActivityV2.startDriveModeActivityV2();
        DriveModeActivityV3.startDriveModeActivityV3(DriveModeActivityV3.FROM_PLAY_PAGE);
        if (getCurTrack() != null) {
            new UserTracking(6666, "track", "button")
                    .setSrcPageId(getCurTrack().getDataId())
                    .setSrcModule("more")
                    .setItemId("驾驶模式")
                    .statIting(XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);
        }
    }

    private void toAlarmPage() {
        TrackM track = getCurTrack();
        if (track == null || !track.isHasCopyRight()) {
            String noCopyRightMsg = "版权方要求，该资源在该地区无法播放";
            CustomToast.showFailToast(noCopyRightMsg);
            return;
        }
        if (mDialogFragment.getParentFragment() instanceof BaseFragment2) {
            ((BaseFragment2) mDialogFragment.getParentFragment()).startFragment(AddOrEditAlarmFragment.newInstance(IMainFragmentAction.FROM_PLAYING_SOUND));
        }
        new UserTracking()
                .setSrcPage("track")
                .setSrcPageId(track.getDataId())
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_SET_CLOCK);
    }

    private void complain() {
        Track track = getCurTrack();
        if (UserInfoMannage.hasLogined() && track != null && (mDialogFragment.getParentFragment() instanceof BaseFragment2)) {
            ReportFragment reportFragment = ReportFragment.newInstanceByTrack(track.getDataId(), track.getAgeLevel(), track.getUid());
            ((BaseFragment2) mDialogFragment.getParentFragment()).startFragment(reportFragment);
        } else if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mDialogFragment.getContext());
        }
    }

    private void toCopyRightPage() {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mDialogFragment.getContext());
            return;
        }
        if (!(mDialogFragment.getParentFragment() instanceof BaseFragment2)) {
            return;
        }
        TrackM track = getCurTrack();
        if (track == null || track.getDataId() <= 0) {
            return;
        }
        try {
            String url = ConfigureCenter.getInstance().getString("tob", "tort_url") + "?trackId=" + track.getDataId();
            ((BaseFragment2) mDialogFragment.getParentFragment()).startFragment(NativeHybridFragment.newInstance(url, true));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 显示喜点打赏弹框
     */
    private void reward() {
        if (mDialogFragment instanceof PlayMoreActionDialogFragment) {
            PlayMoreActionDialogFragment.ClickCallback callback = ((PlayMoreActionDialogFragment) mDialogFragment).getClickCallback();
            if (callback != null) {
                callback.onRewardClicked();
            }
        }
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_play_more_action;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new MoreActionViewHolder(convertView);
    }

    private static class MoreActionViewHolder extends BaseViewHolder {
        View itemView;
        ImageView coverIv;
        TextView titleTv;
        TextView tagTv;

        public MoreActionViewHolder(View convertView) {
            itemView = convertView;
            coverIv = convertView.findViewById(R.id.main_play_more_action_item_iv);
            titleTv = convertView.findViewById(R.id.main_play_more_action_item_tv);
            tagTv = convertView.findViewById(R.id.main_play_more_action_item_tag_tv);
        }
    }
}
