package com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.manager;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.util.Pair;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.tracemonitor.TraceNodeModel;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constants.TraceNodeTraceNameConstants;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.pay.PayManager;
import com.ximalaya.ting.android.host.manager.pay.PaySignatureUtil;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.base.BaseModel;
import com.ximalaya.ting.android.host.model.play.OverAuditionRes;
import com.ximalaya.ting.android.host.model.play.PlayTimeLimitFreeInfo;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.util.XmRequestPage;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.view.text.XmTextSwitcher;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.albumModule.album.wholeAlbum.WholeAlbumPriceInfoPresenter;
import com.ximalaya.ting.android.main.albumModule.album.wholeAlbum.WholeAlbumRnBuyParamsUtil;
import com.ximalaya.ting.android.main.constant.MainUrlConstants;
import com.ximalaya.ting.android.main.fragment.dialog.h5.VipFloatPurchaseDialog;
import com.ximalaya.ting.android.main.fragment.trainingcamp.TrainingCampFragment;
import com.ximalaya.ting.android.main.manager.overAudition.OverAuditionMarkPointManager;
import com.ximalaya.ting.android.main.manager.playPage.PlayPageMarkPointManager;
import com.ximalaya.ting.android.main.model.pay.Coupon;
import com.ximalaya.ting.android.main.model.pay.WholeAlbumPriceInfo;
import com.ximalaya.ting.android.main.model.pay.WholeAlbumPurchaseChannelBuyAlbum;
import com.ximalaya.ting.android.main.model.pay.WholeAlbumPurchaseChannelUrl;
import com.ximalaya.ting.android.main.model.pay.WholeAlbumPurchasePrice;
import com.ximalaya.ting.android.main.payModule.BuyAlbumFragment;
import com.ximalaya.ting.android.main.playModule.view.buyView.BuyView;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.overAudition.TrackOverAuditionComponent;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.manager.data.BuyAlbumData;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.manager.data.GetVipData;
import com.ximalaya.ting.android.main.model.share.ShareAssistData;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.manager.data.VipDiscountData;
import com.ximalaya.ting.android.main.playpage.util.overAudition.OverAuditionViewUtil;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.main.util.AlbumTypeUtil;
import com.ximalaya.ting.android.main.util.other.CouponUtil;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import java.io.Serializable;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class TrackOverAuditionWholeAlbumViewManager extends AbstractOverAuditionChildViewManager implements View.OnClickListener {
    private static final String ERROR_REQUESTED = "ERROR_REQUESTED";
    private static final String ERROR_BUSINESS_EXCEPTION = "ERROR_BUSINESS_EXCEPTION";
    public static final String PRICE_UNIT = "喜点";

    private Object[] objects;
    private OverAuditionRes mOverAuditionRes;
    private WholeAlbumPriceInfo mPriceInfo;
    private boolean interuptByBusinessException = false;
    private String mSource = WholeAlbumPriceInfoPresenter.SOURCE_TYPE_PLAY;
    private LinearLayout mContainer;

    public TrackOverAuditionWholeAlbumViewManager(Context context, int viewType, ITrackOverAuditionDataProvider dataProvider, ITrackOverAuditionUiProvider uiProvider) {
        super(context, viewType, dataProvider, uiProvider);
    }

    public void resetSource(String source) {
        this.mSource = source;
    }

    private boolean isWholeAlbum() {
        Track currTrack = mDataProvider.getCurrentTrack();
        if (currTrack != null) {
            int priceType = currTrack.getPriceTypeEnum();
            return priceType == PayManager.PAY_ALBUM_WHOLE || priceType == PayManager.PAY_ALBUM_MEMBER_WHOLE;
        }
        return false;
    }

    private boolean isAlbumAuthorized() {
        PlayingSoundInfo soundInfo = mDataProvider.getPlayingSoundInfo();
        return soundInfo != null && soundInfo.albumInfo != null && soundInfo.albumInfo.isAuthorized;
    }

    @Override
    public boolean enableShow() {
        return isWholeAlbum() && !isAlbumAuthorized()
                // 训练营专辑也不显示
                && !AlbumTypeUtil.WholePayAlbum.isTrainingCampAlbum(mDataProvider.getPlayingSoundInfo());
    }

    @Override
    CharSequence getCustomTitleHint() {
        if (null != mPriceInfo && !StringUtil.isEmpty(mPriceInfo.afterSampleMessage)) {
            return mPriceInfo.afterSampleMessage;
        }
        if (mOverAuditionRes != null) {
            return mOverAuditionRes.message;
        }
        return null;
    }

    @Override
    void addCustomContentView(LinearLayout container) {
        mContainer = container;

        if (isShowNothing()) {
            // 价格接口业务异常 和 无channel 不显示任何内容
            return;
        }

        if (Util.isPurchaseDisabled(mPriceInfo)) {
            // 退款中 和 不能购买
            createDisableViewContent(container);
            return;
        }

        switch (getViewType()) {
            case ITrackOverAuditionChildView.LOCATION_AUDIO_PLAY_PAGE_V2:
                addCustomContentViewByAudioPlayFraV2(container);
                break;
            default:
                addCustomContentViewByDefault(container);
                break;
        }
    }

    @Override
    public void addCustomContentViewByAudioPlayFraV2(LinearLayout container) {
        Pair<WholeAlbumPurchasePrice, Serializable> priceDataPair = getVipDiscountPrice();
        WholeAlbumPurchasePrice priceData = null == priceDataPair ? null : priceDataPair.first;
        Serializable priceChannel = null == priceDataPair ? null : priceDataPair.second;
        WholeAlbumPurchaseChannelUrl.UrlBehavior urlBehavior = getVipDiscountBehavior();
        if ((null != urlBehavior || priceData != null
                || isVipDiscountOld() || Util.isXiMiVipFree(mPriceInfo)) && !isChildVipAlbum()) {
            createVipDiscountViewContent(urlBehavior, priceData, priceChannel, container);
            GetVipData getVipData = GetVipData.create(mPriceInfo, mOverAuditionRes);
            UserTracking.trackAudioPlayPageShowUserInfo("试听结束按钮", mDataProvider.getCurrentAlbumId() + "", getVipData != null ? getVipData.url : null, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE));
            return;
        }

        if (Util.isVipFree(mPriceInfo, mDataProvider.getCurrentTrack()) || Util.isXiMiVipFree(mPriceInfo)) {
            createVipFreeViewContent(container);
            GetVipData getVipData = GetVipData.create(mPriceInfo, mOverAuditionRes);
            UserTracking.trackAudioPlayPageShowUserInfo("试听结束按钮", mDataProvider.getCurrentAlbumId() + "", getVipData != null ? getVipData.url : null, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE));
            return;
        }

        if (Util.isVipOnly(mPriceInfo, mDataProvider.getCurrentTrack())
                || Util.isXiMiVipFreeOnly(mPriceInfo)
                || Util.isVerticalVipOnly(mPriceInfo)) {
            createVipOnlyViewContent(container);
            GetVipData getVipData = GetVipData.create(mPriceInfo, mOverAuditionRes);
            UserTracking.trackAudioPlayPageShowUserInfo("试听结束按钮", mDataProvider.getCurrentAlbumId() + "", getVipData != null ? getVipData.url : null, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE));
            return;
        }
        createPayableViewContent(container);
    }

    @Override
    public void addCustomContentViewByDefault(LinearLayout container) {
        Pair<WholeAlbumPurchasePrice, Serializable> priceDataPair = getVipDiscountPrice();
        WholeAlbumPurchasePrice priceData = null == priceDataPair ? null : priceDataPair.first;
        Serializable priceChannel = null == priceDataPair ? null : priceDataPair.second;
        WholeAlbumPurchaseChannelUrl.UrlBehavior urlBehavior = getVipDiscountBehavior();
        if ((null != urlBehavior || priceData != null
                || isVipDiscountOld() || Util.isXiMiVipFree(mPriceInfo)) && !isChildVipAlbum()) {
            createVipDiscountViewContent(urlBehavior, priceData, priceChannel, container);
            GetVipData getVipData = GetVipData.create(mPriceInfo, mOverAuditionRes);
            UserTracking.trackAudioPlayPageShowUserInfo("试听结束按钮", mDataProvider.getCurrentAlbumId() + "", getVipData != null ? getVipData.url : null, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE));
            return;
        }

        if (Util.isVipFree(mPriceInfo, mDataProvider.getCurrentTrack()) ||
                Util.isVipOnly(mPriceInfo, mDataProvider.getCurrentTrack())) {
            OverAuditionViewUtil.checkShareAssistAdd(mDataProvider, container, this);
        }

        if (Util.isVipFree(mPriceInfo, mDataProvider.getCurrentTrack()) || Util.isXiMiVipFree(mPriceInfo)) {
            createVipFreeViewContent(container);
            GetVipData getVipData = GetVipData.create(mPriceInfo, mOverAuditionRes);
            UserTracking.trackAudioPlayPageShowUserInfo("试听结束按钮", mDataProvider.getCurrentAlbumId() + "", getVipData != null ? getVipData.url : null, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE));
            return;
        }

        if (Util.isVipOnly(mPriceInfo, mDataProvider.getCurrentTrack())
                || Util.isXiMiVipFreeOnly(mPriceInfo)
                || Util.isVerticalVipOnly(mPriceInfo)) {
            createVipOnlyViewContent(container);
            GetVipData getVipData = GetVipData.create(mPriceInfo, mOverAuditionRes);
            UserTracking.trackAudioPlayPageShowUserInfo("试听结束按钮", mDataProvider.getCurrentAlbumId() + "", getVipData != null ? getVipData.url : null, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE));
            return;
        }
        createPayableViewContent(container);
    }

    private boolean isChildVipAlbum() {
        return  (mDataProvider.getPlayingSoundInfo() != null
                && mDataProvider.getPlayingSoundInfo().isChildVipAlbum());
    }

    @Override
    boolean isNeedRequestNetwork() {
        return true;
    }

    void checkRequest(IDataCallBack<Object> callBack) {
        if (objects != null && objects.length == 2
                && objects[0] != null && objects[1] != null) {
            if (objects[0] instanceof WholeAlbumPriceInfo) {
                mPriceInfo = (WholeAlbumPriceInfo) objects[0];
            }
            if (objects[1] instanceof OverAuditionRes) {
                mOverAuditionRes = (OverAuditionRes) objects[1];
            }
            if (objects[0] instanceof String && ERROR_BUSINESS_EXCEPTION.equals((String) objects[0])) {
                interuptByBusinessException = true;
            }
            if (mPriceInfo != null || mOverAuditionRes != null || interuptByBusinessException) {
                callBack.onSuccess(null);
            } else {
                callBack.onError(-1, "");
            }
        }
    }

    @Override
    void requestCustomData(IDataCallBack<Object> callBack) {
        final long albumId = mDataProvider.getCurrentAlbumId();
        final long trackId = mDataProvider.getCurrentTrackId();
        objects = new Object[2];
        mOverAuditionRes = null;
        mPriceInfo = null;
        interuptByBusinessException = false;

        if (albumId <= 0) {
            objects[0] = ERROR_REQUESTED;
            checkRequest(callBack);
        } else {
            new WholeAlbumPriceInfoPresenter().loadData(mUiProvider.getFragment(), albumId, mSource,
                    new WholeAlbumPriceInfoPresenter.DataCallback() {
                        @Override
                        public void onSuccess(WholeAlbumPriceInfo data) {
                            if (albumId == mDataProvider.getCurrentAlbumId()
                                    && trackId == mDataProvider.getCurrentTrackId()
                                    && data != null) {
                                objects[0] = data;
                            } else {
                                objects[0] = ERROR_REQUESTED;
                            }
                            checkRequest(callBack);
                        }

                        @Override
                        public void onError(boolean isBusinessException) {
                            if (isBusinessException) {
                                objects[0] = ERROR_BUSINESS_EXCEPTION;
                            } else {
                                objects[0] = ERROR_REQUESTED;
                            }
                            checkRequest(callBack);
                        }
                    });
        }


        MainCommonRequest.getPlayPageOverAuditionVipConvertRes(albumId, trackId, new IDataCallBack<OverAuditionRes>() {
            @Override
            public void onSuccess(@Nullable OverAuditionRes object) {
                if (albumId == mDataProvider.getCurrentAlbumId()
                        && trackId == mDataProvider.getCurrentTrackId()
                        && object != null) {
                    Track track = mDataProvider.getCurrentTrack();
                    PlayingSoundInfo soundInfo = mDataProvider.getPlayingSoundInfo();
                    if (track != null && PlayTimeLimitFreeInfo.TRACK_TAG_TIME_LIMIT_FREE_FINISH.equals(track.getTrackTags())
                            && soundInfo != null && soundInfo.playTimeLimitFreeInfo != null) {
                        object.message = soundInfo.playTimeLimitFreeInfo.playFinishedTip;
                    }
                    objects[1] = object;
                } else {
                    objects[1] = ERROR_REQUESTED;
                }
                checkRequest(callBack);
            }

            @Override
            public void onError(int code, String message) {
                objects[1] = ERROR_REQUESTED;
                checkRequest(callBack);
            }
        });
    }

    private Pair<WholeAlbumPurchasePrice, Serializable> getVipDiscountPrice() {
        if (mPriceInfo != null
                && mPriceInfo.purchaseChannelBuyAlbum != null
                && mPriceInfo.purchaseChannelBuyAlbum.price != null) {
            if (null != mPriceInfo.purchaseChannelUrl
                    && null != mPriceInfo.purchaseChannelUrl.price) {
                return new Pair<>(mPriceInfo.purchaseChannelUrl.price, mPriceInfo.purchaseChannelUrl);
            } else if (mPriceInfo.purchaseChannelVipAndAlbumPackedBuy != null
                    && mPriceInfo.purchaseChannelVipAndAlbumPackedBuy.price != null) {
                return new Pair<>(mPriceInfo.purchaseChannelVipAndAlbumPackedBuy.price, mPriceInfo.purchaseChannelVipAndAlbumPackedBuy);
            } else if (mPriceInfo.purchaseChannelSubscriptionVip != null
                    && mPriceInfo.purchaseChannelSubscriptionVip.price != null) {
                return new Pair<>(mPriceInfo.purchaseChannelSubscriptionVip.price, mPriceInfo.purchaseChannelSubscriptionVip);
            } else if (WholeAlbumPurchasePrice.TYPE_VIP.equals(mPriceInfo.purchaseChannelBuyAlbum.price.type)) {
                return new Pair<>(mPriceInfo.purchaseChannelBuyAlbum.price, mPriceInfo.purchaseChannelBuyAlbum);
            }
        }
        return null;
    }

    private WholeAlbumPurchaseChannelUrl.UrlBehavior getVipDiscountBehavior() {
        if (null != mPriceInfo
                && null != mPriceInfo.purchaseChannelUrl
                && null != mPriceInfo.purchaseChannelUrl.behavior) {
            return mPriceInfo.purchaseChannelUrl.behavior;
        }
        return null;
    }

    private boolean isVipDiscountOld() {
        PlayingSoundInfo soundInfo = mDataProvider.getPlayingSoundInfo();
        if (mOverAuditionRes != null && soundInfo != null) {
            return soundInfo.albumInfo != null && soundInfo.albumInfo.vipPrice > 0;
        }
        return false;
    }

    private boolean isShowNothing() {
        if (interuptByBusinessException) {
            return true;
        }
        if (null != mPriceInfo && !mPriceInfo.containsAnyPurchaseChannel()) {
            // 服务端不返回数据的时候，不显示购买按钮
            return true;
        }
        return false;
    }

    /**
     * 退款中 或 无法购买
     */
    private void createDisableViewContent(LinearLayout container) {
        String textContent = "无法购买";
        String toastString = null;
        if (null != mPriceInfo
                && null != mPriceInfo.purchaseChannelDisable
                && null != mPriceInfo.purchaseChannelDisable.behavior) {
            if (!StringUtil.isEmpty(mPriceInfo.purchaseChannelDisable.behavior.buttonText)) {
                textContent = mPriceInfo.purchaseChannelDisable.behavior.buttonText;
            }
            toastString = mPriceInfo.purchaseChannelDisable.behavior.toast;
        }
        OverAuditionViewUtil.WholeAlbum.addDisableButton(textContent, toastString, container, this);
    }

    /**
     * VIP折扣
     */
    private void createVipDiscountViewContent(WholeAlbumPurchaseChannelUrl.UrlBehavior behavior, WholeAlbumPurchasePrice vipPriceData, Serializable priceChannel, LinearLayout container) {
        BuyAlbumData buyAlbumData
                = BuyAlbumData.create(mPriceInfo, mOverAuditionRes, mDataProvider.getPlayingSoundInfo());
        if (mPriceInfo != null && mPriceInfo.purchaseChannelXiMiVipFree != null) {
            OverAuditionViewUtil.WholeAlbum.addGetXiMiVipButton(mPriceInfo.purchaseChannelXiMiVipFree, container, this);
        } else {
            // 用兑换券直接兑换的情况，不显示会员折扣购买
            WholeAlbumPurchaseChannelBuyAlbum buyAlbumChannel = (priceChannel instanceof WholeAlbumPurchaseChannelBuyAlbum) ? (WholeAlbumPurchaseChannelBuyAlbum) priceChannel : null;
            VipDiscountData vipDiscountData
                    = VipDiscountData.create(behavior, vipPriceData, buyAlbumChannel, mOverAuditionRes, mDataProvider.getPlayingSoundInfo());
            if (vipDiscountData != null) {
                OverAuditionViewUtil.WholeAlbum.addVipDiscountButton(vipDiscountData, container, this);
            }
        }
        if (!UserInfoMannage.isVipUser() || Util.isXiMiVipFree(mPriceInfo)) {
            if (buyAlbumData != null) {
                OverAuditionViewUtil.WholeAlbum.addBuyAlbumButton(buyAlbumData, container, this);
            }
        }
    }

    /**
     * 会员免费
     */
    private void createVipFreeViewContent(LinearLayout container) {
        if (mPriceInfo != null && mPriceInfo.purchaseChannelXiMiVipFree != null) {
            OverAuditionViewUtil.WholeAlbum.addGetXiMiVipButton(mPriceInfo.purchaseChannelXiMiVipFree, container, this);
        } else {
            addGetVipButton(container);
        }
        BuyAlbumData buyAlbumData
                = BuyAlbumData.create(mPriceInfo, mOverAuditionRes, mDataProvider.getPlayingSoundInfo());
        if (buyAlbumData != null) {
            OverAuditionViewUtil.WholeAlbum.addBuyAlbumButton(buyAlbumData, container, this);
        }
    }

    /**
     * 会员专享
     */
    private void createVipOnlyViewContent(LinearLayout container) {
        PlayingSoundInfo soundInfo = mDataProvider.getPlayingSoundInfo();
        if (mPriceInfo != null && mPriceInfo.purchaseChannelXiMiVipFree != null) {
            OverAuditionViewUtil.WholeAlbum.addGetXiMiVipButton(mPriceInfo.purchaseChannelXiMiVipFree, container, this);
        } else {
            addGetVipButton(container);
        }
    }

    private void createPayableViewContent(LinearLayout container) {
        BuyAlbumData buyAlbumData
                = BuyAlbumData.create(mPriceInfo, mOverAuditionRes, mDataProvider.getPlayingSoundInfo());
        if (buyAlbumData != null) {
            OverAuditionViewUtil.WholeAlbum.addBuyAlbumButton(buyAlbumData, container, this);
        }
    }

    @Override
    public void addViewToButtonLine(View view, LinearLayout container) {
        if (null == view || null == container) {
            return;
        }
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin = 0;
        }
        container.addView(view);
    }

    @Override
    public View.OnClickListener getOnClickListener() {
        return this;
    }

    private void addGetVipButton(LinearLayout container) {
        GetVipData getVipData = GetVipData.create(mPriceInfo, mOverAuditionRes);
        if (getVipData != null) {
            OverAuditionViewUtil.WholeAlbum.addGetVipButton(getVipData, container, this, mDataProvider);
        }
    }

    /**
     * 判断是否是限时免费听结束后的弹窗，是的话，完成埋点
     *
     * @param isToBuyVip 是否是购买vip
     */
    private void tryToSetMarkPointWhenLimitFreeEnd(boolean isToBuyVip) {
        Track currentTrack = mDataProvider.getCurrentTrack();
        if (null == currentTrack || !PlayTimeLimitFreeInfo.TRACK_TAG_TIME_LIMIT_FREE_FINISH.equals(currentTrack.getTrackTags())) {
            return;
        }
        new UserTracking().setSrcModule("423限时免费听到期弹窗").setItem("button").setItemId(isToBuyVip ? "购买VIP" : "购买专辑")
                .setId(8359).setAlbumId(mDataProvider.getCurrentAlbumId())
                .setViewStyle(isAdUnlock() ? 1 : 0)
                .setPaidAlbumType(BuyView.getPaidAlbumType(currentTrack))
                .statIting(XDCSCollectUtil.SERVICE_PAGE_CLICK);
    }

    public void onResume() {
        if (mContainer != null) {
            XmTextSwitcher shareSwitcher = mContainer.findViewById(R.id.main_xts_share_assist);
            if (com.ximalaya.ting.android.host.util.view.ViewStatusUtil.isViewVisible(shareSwitcher)) {
                try {
                    shareSwitcher.startSwitch();
                } catch (Exception e) {
                    // ignore
                }
            }
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        mUiProvider.dismissOverAuditionDialog();
        Object textTag = ViewStatusUtil.getTag(v, R.id.main_play_page_over_audition_tip_text);
        Object vipDataAnalysis = ViewStatusUtil.getTag(v, R.id.main_id_tag_vip_data_analysis);
        if (id == R.id.main_play_page_over_audition_get_vip) {
            OverAuditionMarkPointManager.Companion.markPointOnClickGetVip(mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrack(), textTag, vipDataAnalysis);
            OverAuditionMarkPointManager.Companion.markPointOnClickGetVip2(mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrack(), textTag, vipDataAnalysis);
            Object url_type = v.getTag(R.id.main_play_page_over_audition_url_type);
            String vipProductsUrl = (String) v.getTag(R.id.main_play_page_over_audition_vip_products_url);
            tryToGetRebateCoupon();
            tryToSetMarkPointWhenLimitFreeEnd(true);
            UserTracking.trackAudioPlayPageClickUserInfo("试听结束按钮", mDataProvider.getCurrentAlbumId() + "", vipProductsUrl);
            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(mContext);
                return;
            }

            if (url_type instanceof Integer && ((int) url_type) == 1) {
                Activity activity = BaseApplication.getMainActivity();
                if (activity instanceof MainActivity) {
                    NativeHybridFragment.start((MainActivity) activity, vipProductsUrl, true);
                }
            } else {
                // ToolUtil.clickUrlAction(mUiProvider.getFragment(), vipProductsUrl, v);
                VipFloatPurchaseDialog.VipDialogMaterial material = new VipFloatPurchaseDialog.VipDialogMaterial(vipProductsUrl
                        , ITrackOverAuditionChildView.LOCATION_AUDIO_PLAY_PAGE == mViewType
                        ? VipFloatPurchaseDialog.SOURCE_PLAY_PAGE : VipFloatPurchaseDialog.SOURCE_POPUP);
                material.setIds(mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrackId());
                VipFloatPurchaseDialog.show(mUiProvider.getFragment(), material);
            }
        } else if (id == R.id.main_play_page_over_audition_buy_button) {
            tryToGetRebateCoupon();
            tryToSetMarkPointWhenLimitFreeEnd(false);
            TrackOverAuditionComponent.logBuy(mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrackId(), "vipPrice",
                    TrackOverAuditionComponent.getLogAlbumPayType(mDataProvider.getPlayingSoundInfo()),
                    null, null, isAdUnlock());
            OverAuditionMarkPointManager.Companion.markPointOnClickBuyAlbum(mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrack(), textTag);

            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(mContext);
                return;
            }
            Object couponUrlTag = v.getTag(R.id.main_play_page_over_audition_get_coupon_url);
            Object couponIdTag = v.getTag(R.id.main_play_page_over_audition_get_coupon_id);

            if (couponUrlTag instanceof String) {
                String couponUrl = (String) couponUrlTag;
                //有优惠券 未领取，去领优惠券
                requestTakeCoupon(couponUrl);
            } else if (couponIdTag instanceof Long) {
                long couponId = (long) couponIdTag;
                //有优惠券 未领取，去领优惠券
                requestTakeCoupon(couponId);
            } else {
                if (AlbumTypeUtil.WholePayAlbum.isTrainingCampAlbum(mDataProvider.getPlayingSoundInfo())) {
                    TrainingCampFragment fragment = TrainingCampFragment.newInstance(mDataProvider.getCurrentAlbumId(), ConstantsOpenSdk.PLAY_FROM_NONE, null);
                    mUiProvider.startFragment(fragment);
                } else {
                    gotoBuy();
                }
            }
        } else if (id == R.id.main_play_page_over_audition_vip_buy_button) {
            tryToGetRebateCoupon();
            tryToSetMarkPointWhenLimitFreeEnd(false);
            TrackOverAuditionComponent.logBuy(mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrackId(), "vipPrice",
                    TrackOverAuditionComponent.getLogAlbumPayType(mDataProvider.getPlayingSoundInfo()),
                    null, null, isAdUnlock());
            OverAuditionMarkPointManager.Companion.markPointOnClickBuyAlbum(mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrack(), textTag);
            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(mContext);
                return;
            }

            Coupon coupon = getBestPriceCoupon();
            if (coupon != null && !coupon.isHasGet()) {
                CouponUtil.requestCouponOld(mContext, coupon,
                        new IDataCallBack<BaseModel>() {
                            @Override
                            public void onSuccess(@Nullable BaseModel object) {
                                HandlerManager.postOnUIThreadDelay(new Runnable() {
                                    @Override
                                    public void run() {
                                        if (mUiProvider.canUpdateUi()) {
                                            gotoBuy();
                                        }
                                    }
                                }, CouponUtil.AUTOMATIC_GET_COUPON_WAIT_TIME);
                            }

                            @Override
                            public void onError(int code, String message) {
                                if (mUiProvider.canUpdateUi()) {
                                    gotoBuy();
                                }
                            }
                        });
                return;
            }

            if (!UserInfoMannage.isVipUser()) {
                Object urlTag = v.getTag(R.id.main_play_page_over_audition_vip_products_url);
                if (urlTag instanceof String) {
                    String url = (String) urlTag;
                    VipFloatPurchaseDialog.VipDialogMaterial material = new VipFloatPurchaseDialog.VipDialogMaterial(url
                            , ITrackOverAuditionChildView.LOCATION_AUDIO_PLAY_PAGE == mViewType
                            ? VipFloatPurchaseDialog.SOURCE_PLAY_PAGE : VipFloatPurchaseDialog.SOURCE_POPUP);
                    material.setIds(mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrackId());
                    // ToolUtil.clickUrlAction(mUiProvider.getFragment(), url, v);
                    VipFloatPurchaseDialog.show(mUiProvider.getFragment(), material);
                    return;
                }
            }
            gotoBuy();
        } else if (id == R.id.main_play_page_over_audition_get_ximi_vip) {
            Object tagObject = v.getTag(R.id.main_play_page_over_audition_get_ximi_vip);
            if (tagObject instanceof String) {
                String url = (String) tagObject;
                ToolUtil.clickUrlAction(mUiProvider.getFragment(), url, v);
                mUiProvider.registerBuyXiMiVipBroadcast();
            }
            OverAuditionMarkPointManager.Companion.markPointOnClickJoinXimi(mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrack(), textTag);
        } else if (R.id.main_view_id_album_disable == id) {
            Object tag = v.getTag(R.id.main_view_id_album_disable);
            if (tag instanceof String) {
                String toast = (String) tag;
                if (!StringUtil.isEmpty(toast)) {
                    CustomToast.showToast(toast);
                }
            }
        } else if (R.id.main_tv_share_assist == id) {
            Object tag = v.getTag();
            if (tag instanceof ShareAssistData) {
                OverAuditionMarkPointManager.Companion.markPointOnClickShareAssist(
                        mDataProvider.getCurrentAlbumId(), mDataProvider.getCurrentTrack(),
                        ((ShareAssistData) tag).getTitle(), ((ShareAssistData) tag).getLink()
                );
                ToolUtil.clickUrlAction(mUiProvider.getFragment(), ((ShareAssistData) tag).getLink(), v);
            }
        }
    }

    private void requestTakeCoupon(String couponUrl) {
        if (!TextUtils.isEmpty(couponUrl) && couponUrl.contains("?")) {
            try {
                URL url = new URL(couponUrl);
                String hostPath = url.getProtocol() + "://" + url.getHost() + url.getPath();
                String query = url.getQuery();
                Map<String, String> params = ToolUtil.getQueryMap(query);
                if (!TextUtils.isEmpty(hostPath) && params != null) {
                    params.put("signature", PaySignatureUtil.getSignature(mContext, params));
                    MainCommonRequest.getAlbumCoupon(hostPath, params, new IDataCallBack<BaseModel>() {
                        @Override
                        public void onSuccess(@Nullable final BaseModel object) {
                            gotoBuy();
                        }

                        @Override
                        public void onError(int code, String message) {
                            CustomToast.showFailToast(message);
                            gotoBuy();
                        }
                    });
                } else {
                    CustomToast.showFailToast("领取失败");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            CustomToast.showFailToast("领取失败");
        }
    }

    private void requestTakeCoupon(long couponId) {
        Map<String, String> params = new HashMap<>();
        params.put("domain", "" + 1);
        params.put("couponId", "" + couponId);
        params.put("comment", "get_coupon");
        params.put("ts", System.currentTimeMillis() + "");
        params.put("signature", PaySignatureUtil.getSignature(BaseApplication.getMyApplicationContext(), params));
        MainCommonRequest.getAlbumCoupon(MainUrlConstants.getInstanse().getRequestCouponUrl(), params, new IDataCallBack<BaseModel>() {
            @Override
            public void onSuccess(@Nullable final BaseModel object) {
                gotoBuy();
            }

            @Override
            public void onError(int code, String message) {
                CustomToast.showFailToast(message);
                gotoBuy();
            }
        });
    }

    private void gotoBuy() {
        TraceNodeTraceNameConstants.Util.reportTraceNode(
                TraceNodeTraceNameConstants.Util.buildTraceNodeModel(
                        TraceNodeTraceNameConstants.NAME_WHOLE_ALBUM_BUY, TraceNodeModel.NODE_PAGE_START));
        AlbumM albumM = new AlbumM();
        albumM.setId(mDataProvider.getCurrentAlbumId());
        albumM.setPriceTypeEnum(getPriceTypeEnum());
        BuyAlbumFragment.buyAlbum(mUiProvider.getFragment(), albumM, mPriceInfo, WholeAlbumRnBuyParamsUtil.FROM_PAGE_PLAYPAGE,
                mUiProvider.getFragmentFinishCallback(mDataProvider.getCurrentAlbumId()), isAdUnlock());
    }

    private int getPriceTypeEnum() {
        Track track = mDataProvider.getCurrentTrack();
        if (track != null) {
            return track.getPriceTypeEnum();
        }
        return 0;
    }

    public Coupon getBestPriceCoupon() {
        if (mPriceInfo != null && !ToolUtil.isEmptyCollects(mPriceInfo.coupons)) {
            return CouponUtil.getBestPriceCoupon(mPriceInfo.coupons);
        }
        return null;
    }

    private void tryToGetRebateCoupon() {
        if (UserInfoMannage.hasLogined() &&
                null != mPriceInfo
                && !ToolUtil.isEmptyCollects(mPriceInfo.rebateCoupons)) {
            CouponUtil.requestRebateCoupon(mPriceInfo.rebateCoupons, null);
        }
    }

    private static class Util {
        public static boolean isPurchaseDisabled(WholeAlbumPriceInfo priceInfo) {
            return null != priceInfo && null != priceInfo.purchaseChannelDisable;
        }

        public static boolean isVipFree(WholeAlbumPriceInfo priceInfo, Track track) {
            boolean newLogic = priceInfo != null
                    && priceInfo.purchaseChannelBuyAlbum != null
                    && priceInfo.purchaseChannelBuyAlbum.price != null
                    && priceInfo.purchaseChannelVipFree != null
                    && !UserInfoMannage.isVipUser();
            boolean oldLogic = null != track && track.getVipFreeType() == 1;
            return newLogic || oldLogic;
        }

        public static boolean isVipOnly(WholeAlbumPriceInfo priceInfo, Track track) {
            boolean newLogic = priceInfo != null
                    && priceInfo.purchaseChannelVipFree != null
                    && priceInfo.purchaseChannelBuyAlbum == null;
            boolean oldLogic = null != track && track.isVipFree();
            return newLogic || oldLogic;
        }

        public static boolean isXiMiVipFree(WholeAlbumPriceInfo priceInfo) {
            return priceInfo != null
                    && priceInfo.purchaseChannelXiMiVipFree != null
                    && priceInfo.purchaseChannelBuyAlbum != null
                    && priceInfo.purchaseChannelBuyAlbum.price != null
                    && WholeAlbumPurchasePrice.TYPE_NOT_VIP.equals(priceInfo.purchaseChannelBuyAlbum.price.type);
        }

        public static boolean isXiMiVipFreeOnly(WholeAlbumPriceInfo priceInfo) {
            return priceInfo != null
                    && priceInfo.purchaseChannelXiMiVipFree != null
                    && priceInfo.purchaseChannelBuyAlbum == null;
        }

        public static boolean isVerticalVipOnly(WholeAlbumPriceInfo priceInfo) {
            return null != priceInfo
                    && null != priceInfo.purchaseChannelVerticalVip
                    && null == priceInfo.purchaseChannelBuyAlbum
                    && null == priceInfo.purchaseChannelBuyAlbumVipDiscount;
        }

        public static void setMergeViewParams(View view) {
            LinearLayout.LayoutParams llp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
            llp.weight = 1;
            view.setLayoutParams(llp);
        }
    }
}
