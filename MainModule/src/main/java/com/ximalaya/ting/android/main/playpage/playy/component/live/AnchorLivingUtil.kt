package com.ximalaya.ting.android.main.playpage.playy.component.live

import android.app.Activity
import android.net.Uri
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.other.FrequencyUtil
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi


/**
 * 播放页专辑主播直播中提示条辅助工具类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13037236220
 * @wiki https://alidocs.dingtalk.com/i/nodes/KGZLxjv9VG3RZlMvUr9XkqQDV6EDybno?utm_scene=team_space
 * @server 周乐乐
 * @since 2024/11/7
 */
object AnchorLivingUtil {

    private const val KEY_ANCHOR_LIVING_TIP_EXPOSED = "KEY_PLAY_PAGE_ANCHOR_LIVING_TIP_EXPOSED"

    /**
     * 判断是否符合频控规则以展示提示条：同一场直播 [liveId] + [bizType]，在同一个专辑 [albumId] 内，同一天只出一次
     */
    fun canBeExposed(bizType: Int, liveId: Long, albumId: Long): Boolean {
        if (liveId <= 0 || albumId <= 0) {
            return false
        }
        val sameDay = FrequencyUtil.EachDay.checkAndSet(
            FrequencyUtil.EachDay.KEY_PLAY_PAGE_ANCHOR_LIVING_TIP
        )
        if (sameDay.not()) {
            MMKVUtil.getInstance().saveArrayList(KEY_ANCHOR_LIVING_TIP_EXPOSED, ArrayList())
            return true
        }

        val value = "${bizType}-${liveId}-${albumId}"
        val cache = MMKVUtil.getInstance().getArrayList(KEY_ANCHOR_LIVING_TIP_EXPOSED)
        return cache.contains(value).not()
    }

    /**
     * 标记曝光
     */
    fun markExposed(bizType: Int, liveId: Long, albumId: Long) {
        val cache = MMKVUtil.getInstance().getArrayList(KEY_ANCHOR_LIVING_TIP_EXPOSED)
        cache.add("${bizType}-${liveId}-${albumId}")
        MMKVUtil.getInstance().saveArrayList(KEY_ANCHOR_LIVING_TIP_EXPOSED, cache)
    }

    /**
     * 获取 Tip 的按钮文案
     */
    fun getTipBtnContent(): String {
        return "去观看"
    }

    /**
     * 跳转 iting
     */
    fun jumpIting(activity: Activity, iting: String) {
        try {
            val uri = Uri.parse(iting)
            Router.getActionRouter<MainActionRouter>(
                Configure.BUNDLE_MAIN
            )?.functionAction?.handleITing(activity, uri)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 点击上报
     */
    fun traceTipClicked(currTrackId: Long, currAlbumId: Long, anchorId: Long, liveRoomType: Int) {
        // 新声音播放页-直播开播声音条  点击事件
        XMTraceApi.Trace()
            .click(66151) // 用户点击时上报
            .put("currPage", "newPlay")
            .put("xmRequestId", XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
            .put("currTrackId", currTrackId.toString())
            .put("currAlbumId", currAlbumId.toString())
            .put("anchorId", anchorId.toString())
            .put("liveRoomType", liveRoomType.toString()) // 直播类型   （1：个人音频直播，4：个人视频直播，5 PGC聊天室，6 UGC语音房，10000：课程直播）
            .createTrace()
    }

    /**
     * 曝光上报
     */
    fun traceTipExposed(currTrackId: Long, currAlbumId: Long, anchorId: Long, liveRoomType: Int) {
        // 新声音播放页-直播开播声音条  控件曝光
        XMTraceApi.Trace()
            .setMetaId(66152)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newPlay")
            .put("xmRequestId", XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
            .put("currTrackId", currTrackId.toString())
            .put("currAlbumId", currAlbumId.toString())
            .put("anchorId", anchorId.toString())
            .put("liveRoomType", liveRoomType.toString()) // 直播类型   （1：个人音频直播，4：个人视频直播，5 PGC聊天室，6 UGC语音房，10000：课程直播）
            .put("contentType", "newPlay_anchor_living_tip") // 去重使用
            .put("contentId", "${anchorId}-${currAlbumId}") // 去重使用
            .createTrace()
    }
}