package com.ximalaya.ting.android.main.payModule;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.util.Random;

import static com.ximalaya.ting.android.framework.util.CustomToast.showSuccessToast;

/**
 * <AUTHOR> on 2016/12/12.
 *         主播付费专辑评价页-回复评价弹窗
 */

public class PayCommentReplyDialog extends BaseDialogFragment implements View.OnClickListener {
    private TextView mContentView;
    private int mPreRandom;//上一次的随机数
    private final Integer[] contentResIds = new Integer[]{
            R.string.main_pay_comment_reply1
            , R.string.main_pay_comment_reply2
            , R.string.main_pay_comment_reply3
            , R.string.main_pay_comment_reply4
            , R.string.main_pay_comment_reply5};

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        Window window = getDialog().getWindow();
        View containView = inflater.inflate(R.layout.main_dialog_pay_comment_reply, ((ViewGroup) window.findViewById(android.R.id.content)), false);//此处必须是android.R.id.content
        containView.findViewById(R.id.main_positive_button).setOnClickListener(this);
        containView.findViewById(R.id.main_negative_button).setOnClickListener(this);
        containView.findViewById(R.id.main_iv_close).setOnClickListener(this);
        AutoTraceHelper.bindData(containView.findViewById(R.id.main_positive_button),"");
        AutoTraceHelper.bindData(containView.findViewById(R.id.main_negative_button),"");
        AutoTraceHelper.bindData(containView.findViewById(R.id.main_iv_close),"");
        mContentView = (TextView) containView.findViewById(R.id.main_content);
        mPreRandom = new Random().nextInt(5);
        mContentView.setText(getResourcesSafe().getString(contentResIds[mPreRandom]));
        return containView;
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.main_positive_button) {
            dismiss();
            SystemServiceManager.setClipBoardData(getActivity(), mContentView.getText().toString());
            new UserTracking().setSrcPage("小秘书秘籍弹窗").setSrcModule("复制")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_COPYWRITE);
            showSuccessToast("已复制");

        } else if (i == R.id.main_negative_button) {
            int newRandom;//新随机数
            do {
                newRandom = new Random().nextInt(5);
            } while (mPreRandom == newRandom);
            mPreRandom = newRandom;
            mContentView.setText(getResourcesSafe().getString(contentResIds[newRandom]));
            new UserTracking().setSrcPage("小秘书秘籍弹窗").setSrcModule("下一条")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_NEXTWRITE);

        } else if (i == R.id.main_iv_close) {
            dismiss();

        }
    }
}
