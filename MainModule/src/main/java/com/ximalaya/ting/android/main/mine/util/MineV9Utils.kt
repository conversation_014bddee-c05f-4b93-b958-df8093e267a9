package com.ximalaya.ting.android.main.mine.util

import com.ximalaya.ting.android.host.util.common.getFriendlyNumStringPair

/**
 * Created by dekai.liu on 2021/9/16.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18392566603
 */
fun getListenedDurationPairNew(listenedMinutes: Long?): Pair<Pair<String, String>, String> {
    if (MineTopUtil.isVipNewStyle()) {
        return if (listenedMinutes == null) {
            ("-" to "") to "小时"
        } else {
            val hour = listenedMinutes / 60
            if (hour <= 0) {
                (listenedMinutes.toString() to "") to "分钟"
            } else {
                getFriendlyNumStringPair(hour) to "小时"
            }
        }
    } else {
        return if (listenedMinutes == null) {
            ("-" to "") to "收听(小时)"
        } else {
            val hour = listenedMinutes / 60
            if (hour <= 0) {
                (listenedMinutes.toString() to "") to "收听(分钟)"
            } else {
                getFriendlyNumStringPair(hour) to "收听(小时)"
            }
        }
    }
}

fun getFriendlyNumStringPairNew(number: Int?): Pair<String, String> {
    if (number == null) {
        return "-" to ""
    }
    return getFriendlyNumStringPair(number)
}