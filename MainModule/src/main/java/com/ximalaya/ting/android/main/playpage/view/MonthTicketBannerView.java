package com.ximalaya.ting.android.main.playpage.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.view.pageindicator.CirclePageIndicator;
import com.ximalaya.ting.android.host.view.looppager.AutoScrollViewPager;
import com.ximalaya.ting.android.host.view.looppager.BaseLoopPagerAdapter;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.playpage.vote.model.BannerForTicketRank;

import java.util.ArrayList;
import java.util.List;


/**
 * Created by zhifu.zhang on 2019-08-26.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18362080562
 * @desc 个人页banner
 */
public class MonthTicketBannerView extends ConstraintLayout {

    private final PorterDuffXfermode mXfermode = new PorterDuffXfermode(PorterDuff.Mode.CLEAR);
    private final Paint mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private int mCornerRadius = 0;
    private AutoScrollViewPager mViewPager;
    private CirclePageIndicator mIndicator;
    private BaseLoopPagerAdapter<ViewPagerItem<BannerForTicketRank>> mAdapter;
    private ArrayList<ViewPagerItem<BannerForTicketRank>> mListData = new ArrayList<>();

    public MonthTicketBannerView(Context context) {
        this(context, null);
    }

    public MonthTicketBannerView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MonthTicketBannerView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    private void initView(Context context) {
        mPaint.setColor(ContextCompat.getColor(context, R.color.main_color_ffffff_131313));
        View view = LayoutInflater.from(context).inflate(R.layout.main_layout_month_ticket_banner,
                this, true);
        mViewPager = view.findViewById(R.id.main_view_page);
        mIndicator = view.findViewById(R.id.main_indicator_dot_new);
        mIndicator.setViewPager(mViewPager);
        mViewPager.setEnableAutoScroll(true);
        mViewPager.setSwapDuration(6000);
        mViewPager.setFiexSpeedTime(500);
        mAdapter = new BaseLoopPagerAdapter<ViewPagerItem<BannerForTicketRank>>(context, mListData) {
            @Override
            public View createView(int position, ViewGroup container) {
                return LayoutInflater.from(context).inflate(R.layout.main_item_month_ticket_banner,
                        container, false);
            }

            @Override
            public void bindData(View itemView, int position) {
                if (!(itemView instanceof ImageView)) {
                    return;
                }
                ViewPagerItem<BannerForTicketRank> item = getItem(position);
                if (item != null && item.getData() != null) {
                    BannerForTicketRank model = item.getData();
                    ImageManager.from(context).displayImage((ImageView) itemView, model.getPicUrl(),
                            position % 2 == 0 ? R.color.main_color_ebebeb_2a2a2a : R.color.main_color_ff4444);
                    itemView.setOnClickListener(v -> {
                        if (mOnItemClick != null) {
                            mOnItemClick.onItemClick(model);
                        }
                    });
                }
            }

        };
        mViewPager.setILoopPagerAdapter(mAdapter);
    }

    public void startAutoSwapFocusImage() {
        if (mViewPager != null) {
            mViewPager.startSwapViewPager();
        }
    }

    public void stopAutoSwapFocusImage() {
        if (mViewPager != null) {
            mViewPager.stopSwapViewPager();
        }
    }

    public int getCurrIndex() {
        if (mViewPager != null) {
            return mViewPager.getCurrentItem() % mViewPager.getDataSize();
        }
        return 0;
    }

    public AutoScrollViewPager getViewPager() {
        return mViewPager;
    }

    public CirclePageIndicator getIndicator() {
        return mIndicator;
    }

    public void setCornerRadius(int mCornerRadius) {
        this.mCornerRadius = mCornerRadius;
    }

    public void setData(@NonNull List<BannerForTicketRank> banners) {
        mIndicator.setPagerRealCount(banners.size());
        if (banners.size() == 1) {
            mIndicator.setVisibility(View.GONE);
        } else {
            mIndicator.setVisibility(View.VISIBLE);
        }

        mListData.clear();
        for (int i = 0; i < banners.size(); i++) {
            mListData.add(new ViewPagerItem<>(banners.get(i), 0));
        }
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void onStartTemporaryDetach() {
        super.onStartTemporaryDetach();
        stopAutoSwapFocusImage();
    }

    @Override
    public void onFinishTemporaryDetach() {
        super.onFinishTemporaryDetach();
        startAutoSwapFocusImage();
    }

    @Nullable
    public BannerForTicketRank getCurrentActivityModel() {
        int currentIndex = mViewPager.getCurrentItem();
        if (currentIndex >= 0 && currentIndex < mAdapter.getCount() && mAdapter.getItem(currentIndex) != null) {
            return mAdapter.getItem(currentIndex).getData();
        }
        return null;
    }


    public static class ViewPagerItem<D> implements AutoScrollViewPager.IViewPagerItem<D> {
        D mD;
        int mType;

        public ViewPagerItem(D data, int type) {
            mD = data;
            mType = type;
        }

        @Override
        public D getData() {
            return mD;
        }

        @Override
        public int getViewType() {
            return mType;
        }
    }

    private OnItemClick mOnItemClick;

    public void setOnItemClick(OnItemClick mOnItemClick) {
        this.mOnItemClick = mOnItemClick;
    }

    public interface OnItemClick {
        void onItemClick(BannerForTicketRank bannerInfo);
    }

    @Override
    protected void dispatchDraw(Canvas canvas) {
        super.dispatchDraw(canvas);
        if (mCornerRadius > 0) {
            int layerId = canvas.saveLayer(0, 0, getMeasuredWidth(), getMeasuredHeight(), null, Canvas.ALL_SAVE_FLAG);
            canvas.drawRect(0, 0, getMeasuredWidth(), getMeasuredHeight(), mPaint);
            mPaint.setXfermode(mXfermode);
            canvas.drawRoundRect(0, 0, getMeasuredWidth(), getMeasuredHeight(), mCornerRadius, mCornerRadius, mPaint);
            mPaint.setXfermode(null);
            canvas.restoreToCount(layerId);
        }
    }
}
