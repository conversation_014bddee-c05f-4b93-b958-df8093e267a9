package com.ximalaya.ting.android.main.playpage.listener;

import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.view.tips.NewCustomTipsView;
import com.ximalaya.ting.android.main.playpage.manager.commentmanager.PlayCommentManagerNew;

/**
 * <AUTHOR> on 2017/11/24.
 */

public interface IPlayCommentFunctionNew {
    boolean isAllowComment();

    void toggleInputBar(int typeComment);

    void toggleInputBar(int type, String hint, long parentCommentId, String inputHint);

    int getAllowCommentType();

    void sendBullet(final String content, int bulletColorType, boolean bottom);

    PlayingSoundInfo getSoundInfo();

    boolean tryShowRaiseDialog(int type);

    void setCommentCount(int hotCount, int totalCount);

    void setNewCommentCount(int newCommentCount);

    void showCommentSinglePage(boolean needToAnchor);

    PlayCommentManagerNew getCommentManager();

    void setCommentEmptyState(boolean isEmpty, boolean isNetError, boolean hasTalkList);

}
