package com.ximalaya.ting.android.main.fragment.find.child;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.os.Bundle;
import android.util.Log;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Observer;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewpager.widget.ViewPager;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.PadAdaptUtil;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.host.view.VerticalViewPager;
import com.ximalaya.ting.android.host.view.image.TouchRelativeLayout;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.fragment.find.child.manager.PodCastImmersiveAudioDataManager;
import com.ximalaya.ting.android.main.fragment.find.child.manager.PodcastImmersiveBitmapPool;
import com.ximalaya.ting.android.main.fragment.find.child.manager.PodcastImmersiveConfigureManager;
import com.ximalaya.ting.android.main.model.podcast.v2.PodGuessTrackInfo;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.util.ArrayList;
import java.util.List;

public class PodCastImmersiveAudioListFragment extends BaseFragment2 implements Observer<List<PodGuessTrackInfo>>, AlbumEventManage.CollectListener,
        LikeTrackManage.TrackLikeStatusListener {
    private static final String BUNDLE_KEY_POD_GUESS_TRACK_INFO_LIST = "key_pod_guess_track_info_list";
    private static final String BUNDLE_KEY_CATEGORY_ID = "key_category_id";
    private static final String BUNDLE_KEY_FIRST_TRACK_ID = "key_first_track_id";
    private static final String BUNDLE_KEY_POOL_ID = "key_pool_id";

    private View mRlBottomContainer;
    private SwipeRefreshLayout swipeRefreshLayout;
    private VerticalViewPager viewPager;
    private PodCastImmersiveAudioListPageAdapter pageAdapter;
    private ViewGroup mRlUserGuide;
    private TouchRelativeLayout mTrlContainer;

    private boolean firstEnter = true;
    private boolean mIsRefresh = false;

    private int lastRecordValue = -1;    //记录上一次滑动的positionOffsetPixels值
    private int lastRecordPosition = 0; //记录上一次的position;
    private boolean isViewPagerDragging = false;//viewPager 是否在拖动，用来判断是否在最后一页拖动。
    private long startStayTime; //当前页面开始停留的时间

    private boolean isSlideDown = true;     //是否向下滑动
    private volatile NetworkType.NetWorkType lastNetType;
    private boolean isLoading;
    private long mCategoryId;
    private long mFirstTrackId;
    private long mPoolId;
    private boolean mPoolNoData = false;
    private ArrayList<PodGuessTrackInfo> mExistList;
    private long mCurResumeTrackId;
    private boolean mIsDataChanged;
    private float mPlaySpeedValue = 1f;

    public static PodCastImmersiveAudioListFragment newInstance(long categoryId, long firstTrackId, long poolId, ArrayList<PodGuessTrackInfo> list) {
        Bundle bundle = new Bundle();
        bundle.putLong(BUNDLE_KEY_CATEGORY_ID, categoryId);
        bundle.putLong(BUNDLE_KEY_FIRST_TRACK_ID, firstTrackId);
        bundle.putLong(BUNDLE_KEY_POOL_ID, poolId);
        bundle.putParcelableArrayList(BUNDLE_KEY_POD_GUESS_TRACK_INFO_LIST, list);
        PodCastImmersiveAudioListFragment fragment = new PodCastImmersiveAudioListFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected String getPageLogicName() {
        return PodCastImmersiveAudioListFragment.class.getSimpleName();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            mCategoryId = bundle.getLong(BUNDLE_KEY_CATEGORY_ID, 0);
            mFirstTrackId = bundle.getLong(BUNDLE_KEY_FIRST_TRACK_ID, 0);
            mPoolId = bundle.getLong(BUNDLE_KEY_POOL_ID, 0);
            mExistList = bundle.getParcelableArrayList(BUNDLE_KEY_POD_GUESS_TRACK_INFO_LIST);
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mPlaySpeedValue = ConfigureCenter.getInstance().getFloat(CConstants.Group_android.GROUP_NAME, "item_podcast_immersive_audio_speed", 1);
        swipeRefreshLayout = findViewById(R.id.main_swipe_refresh);
        mRlBottomContainer = findViewById(R.id.main_rl_bottom_pretend_container);
        viewPager = findViewById(R.id.main_vertical_viewpager);
        viewPager.setInterceptBottom(true);
        viewPager.setCanChildScroll(false);
        viewPager.setOffscreenPageLimit(1);
        mRlUserGuide = findViewById(R.id.main_rl_user_guide);
        mTrlContainer = findViewById(R.id.main_trl_container);
        addListener();
        PodCastImmersiveAudioDataManager.getInstance().getDataLiveData().observe(this, this);
    }

    private void addListener() {
        AlbumEventManage.addListener(this);
        LikeTrackManage.addListener(this);
        swipeRefreshLayout.setOnRefreshListener(() -> {
            mIsRefresh = true;
            mPoolNoData = false;
            if (pageAdapter != null) {
                pageAdapter.clearData();
            }
            onPageLoadingCompleted(LoadCompleteType.LOADING);
            doRealLoadData(false);
        });

        viewPager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            private float mPreviousOffset = -1;

            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                if (positionOffset != 0) {
                    if (lastRecordValue >= positionOffsetPixels) {
                        //下滑
                        isSlideDown = true;
//                        Logger.i("onPageListenerListener", " ### 正在向下滑 ###");
                    } else {
                        //上滑
                        isSlideDown = false;
//                        Logger.i("onPageListenerListener", " ### 正在向上滑 ###");
                    }
                } else {
                    int lastPosition = pageAdapter == null ? 0 : pageAdapter.getCount() - 1;
                    if (isViewPagerDragging && positionOffsetPixels == 0.0f) {
                        Logger.i("onPageListene recommdnd",
                                " position = " + position + ",lastRecordPosition:" + lastRecordPosition
                                        + "\n  positionOffset = " + positionOffset
                                        + "  positionOffsetPixels = " + positionOffsetPixels);
                        if (position == lastPosition) {
                            CustomToast.showToast(mPoolId == 0 ? "正在加载，请稍等" : "没有更多内容啦～");
                        }
                    }
                }
                lastRecordValue = positionOffsetPixels;
                mPreviousOffset = positionOffset;
            }

            @Override
            public void onPageSelected(int position) {
                //页面切换的间隔时间，用于控制请求播放的速度，滑动比较快时延迟开始播放
                long pageScrollInterval = System.currentTimeMillis() - startStayTime;

                startStayTime = System.currentTimeMillis();
                IPodCastImmersiveVerticalChildListener lastChild = pageAdapter.getChildByPosition(lastRecordPosition);
                if (lastChild != null) {
                    lastChild.onScrollOut();
                }

                swipeRefreshLayout.setEnabled(position == 0);

                lastRecordPosition = position;
                IPodCastImmersiveVerticalChildListener currentChild = pageAdapter.getChildByPosition(position);
                if (currentChild != null) {
                    currentChild.onScrollIn(isSlideDown, pageScrollInterval);
                }

                pageAdapter.recordCurrentPosition(position);
                if (!mPoolNoData) {
                    PodCastImmersiveAudioDataManager.getInstance().checkLoadMore(position, mCategoryId, 0, mPoolId);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                isViewPagerDragging = state == ViewPager.SCROLL_STATE_DRAGGING;
            }
        });
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        if (getActivity() != null) {
            getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
        StatusBarManager.setStatusBarColorDelay(getWindow(), false, this);
        ViewUtil.keepScreenOn(getActivity(), true);
        firstEnter = false;
    }

    @Override
    public void onPause() {
        ViewUtil.keepScreenOn(getActivity(), false);
        super.onPause();
        if (getActivity() != null && PadAdaptUtil.isPad(getActivity())) {
            getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_USER);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        PodCastImmersiveAudioDataManager.release();
        PodcastImmersiveBitmapPool.getInstance().clearCache();
        AlbumEventManage.removeListener(this);
        LikeTrackManage.removeListener(this);
    }

    @Override
    protected void loadData() {
        // 已有数据传进来，就直接加载
        if (!ToolUtil.isEmptyCollects(mExistList)) {
            PodCastImmersiveAudioDataManager.getInstance().setExistData(mExistList);
            onPageLoadingCompleted(LoadCompleteType.OK);
            return;
        }
        mIsRefresh = true;
        doRealLoadData(true);
    }

    private int findIndexByTrackId(long trackId) {
        if (trackId <= 0) {
            return 0;
        }
        if (pageAdapter != null && pageAdapter.getDataList() != null) {
            for (int i = 0; i < pageAdapter.getDataList().size(); i++) {
                if (pageAdapter.getDataList().get(i).getTrackId() == trackId) {
                    return i;
                }
            }
        }
        return 0;
    }

    @Override
    public void onPageLoadingCompleted(LoadCompleteType loadCompleteType) {
        super.onPageLoadingCompleted(loadCompleteType);
        if (loadCompleteType == LoadCompleteType.OK) {
            ViewStatusUtil.setVisible(View.VISIBLE, viewPager);
            ViewStatusUtil.setVisible(View.GONE, mRlBottomContainer);
        } else if (loadCompleteType == LoadCompleteType.LOADING ||
                loadCompleteType == LoadCompleteType.NETWOEKERROR ||
                loadCompleteType == LoadCompleteType.NOCONTENT) {
            if (mIsRefresh) {
                ViewStatusUtil.setVisible(View.VISIBLE, mRlBottomContainer);
                ViewStatusUtil.setVisible(View.GONE, viewPager);
            }
        }
    }

    private void doRealLoadData(boolean needTrackId) {
        if (isLoading) {
            return;
        }
        if (firstEnter) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }
        isLoading = true;
        long trackId = needTrackId ? mFirstTrackId : 0;
        PodCastImmersiveAudioDataManager.getInstance().requestAllData(mIsRefresh, mCategoryId, trackId, mPoolId,
                new IDataCallBack<List<PodGuessTrackInfo>>() {
                    @Override
                    public void onSuccess(@Nullable List<PodGuessTrackInfo> data) {
                        isLoading = false;
                        if (!canUpdateUi()) {
                            return;
                        }
                        if (data == null || data.isEmpty()) {
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                            swipeRefreshLayout.setRefreshing(false);
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        swipeRefreshLayout.setRefreshing(false);
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (!canUpdateUi()) return;
                        if (code == -10110) {
                            if (pageAdapter == null || pageAdapter.getCount() == 0) {
                                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                            } else {
                                mPoolNoData = true;
                            }
                        } else {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                        }
                        swipeRefreshLayout.setRefreshing(false);
                        isLoading = false;
                        mIsRefresh = false;
                    }
                });
    }

    public void refreshLoadData() {
        mIsRefresh = true;
        pageAdapter.clearData();
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        doRealLoadData(false);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_frag_pod_cast_immersive_home;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    public VerticalViewPager getViewPager() {
        return viewPager;
    }

    public void setViewPagerCanScroll(boolean isScrollEnable) {
        viewPager.setCanScroll(isScrollEnable);
    }

    @Override
    public void onChanged(List<PodGuessTrackInfo> data) {
        final boolean isRefresh = mIsRefresh;
        mIsRefresh = false;
        if (data == null) {
            return;
        }
        if (!canUpdateUi()) {
            return;
        }
        mIsDataChanged = true;
        if (pageAdapter == null) {
            //首次或者刷新
            FragmentManager fragmentManager = getChildFragmentManager();
            pageAdapter = new PodCastImmersiveAudioListPageAdapter(fragmentManager, PodCastImmersiveAudioListFragment.this, data, mPlaySpeedValue);
            viewPager.setAdapter(pageAdapter);
            // 首次进来定位到mFirstTrackId指定的位置
            lastRecordPosition = findIndexByTrackId(mFirstTrackId);
            pageAdapter.recordCurrentPosition(lastRecordPosition);
            viewPager.setCurrentItem(lastRecordPosition);
            checkToShowGuideView();
        } else {
            // 刷新的情况下要定位到第一条并播放
            if (isRefresh) {
                FragmentManager fragmentManager = getChildFragmentManager();
                pageAdapter = new PodCastImmersiveAudioListPageAdapter(fragmentManager, PodCastImmersiveAudioListFragment.this, data, mPlaySpeedValue);

                viewPager.setAdapter(pageAdapter);
                lastRecordPosition = 0;
                pageAdapter.recordCurrentPosition(lastRecordPosition);
                return;
            }
            pageAdapter.setData(data);
        }
    }

    public void registerNetWorkReceiver() {
        try {
            IntentFilter intentFilterNetwork = new IntentFilter();
            intentFilterNetwork.addAction("android.net.conn.CONNECTIVITY_CHANGE");
            mContext.registerReceiver(mNetWorkReceiver, intentFilterNetwork);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void unregisterNetWorkReceiver() {
        try {
            mContext.unregisterReceiver(mNetWorkReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected View getNoContentView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.main_layout_pod_cast_home_no_data, null, false);
        view.findViewById(R.id.main_cover_no_network_retry).setOnClickListener(v -> {
            if (OneClickHelper.getInstance().onClick(v)) {
                mIsRefresh = true;
                onPageLoadingCompleted(LoadCompleteType.LOADING);
                doRealLoadData(true);
            }
        });
        return view;
    }

    @Override
    protected View getLoadingView() {
        return LayoutInflater.from(getContext()).inflate(R.layout.main_layout_pod_cast_home_loading, null, false);
    }

    @Override
    protected View getNetworkErrorView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.main_layout_pod_cast_home_net_error, null, false);
        view.findViewById(R.id.main_cover_no_network_retry).setOnClickListener(v -> {
            if (OneClickHelper.getInstance().onClick(v)) {
                mIsRefresh = true;
                onPageLoadingCompleted(LoadCompleteType.LOADING);
                doRealLoadData(true);
            }
        });
        return view;
    }

    private final BroadcastReceiver mNetWorkReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            /// 网络状态已经改变
            NetworkType.NetWorkType type = NetworkType.getNetWorkType(context);
            if (lastNetType == type) {//过滤重复
                return;
            }
            lastNetType = type;
            boolean isUseMobile = NetworkType.isConnectMOBILE(context);
            boolean isUseFreeFlow = false;
            if (FreeFlowServiceUtil.getFreeFlowService() != null) {
                isUseFreeFlow = FreeFlowServiceUtil.getFreeFlowService().isUsingFreeFlow();
            }
            if (isUseMobile && !isUseFreeFlow
                    && !NetworkUtils.isAllowUse3G) {
                ToastManager.showToast("正在使用流量播放～");
            }
        }
    };

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    public void setIsDataChanged(boolean isDataChanged) {
        mIsDataChanged = isDataChanged;
    }

    public void requestPlayTrack(int index, boolean isForceSetList) {
        if (pageAdapter == null || ToolUtil.isEmptyCollects(pageAdapter.getDataList())) {
            return;
        }
        List<PodGuessTrackInfo> list = pageAdapter.getDataList();
        // todo 刷新后会出现列表声音和播放器声音不一致情况，不知道为啥，先注释
//        if (!mIsDataChanged && !isForceSetList) {
//            XmPlayerManager.getInstance(ToolUtil.getCtx()).play(index);
//            return;
//        }
        mIsDataChanged = false;
        List<Track> playList = new ArrayList<>();
        for (PodGuessTrackInfo podGuessTrackInfo : list) {
            Track track = new Track();
            track.setDataId(podGuessTrackInfo.getTrackId());
            track.setKind(PlayableModel.KIND_TRACK);
            track.setTrackTitle(podGuessTrackInfo.getTrackTitle());
            track.setCoverUrlLarge(podGuessTrackInfo.getCoverPath());
            playList.add(track);
        }
        XmPlayerManager.getInstance(ToolUtil.getCtx()).setChannelJumpOver(true);
        PlayTools.playList(ToolUtil.getCtx(), playList, index, false, null);
    }

    public void playNext() {
        if (ConstantsOpenSdk.isDebug) {
            Log.e("z_audio", "playNext >>>>");
        }
        if (viewPager != null) {
            try {
                viewPager.setCurrentItem(viewPager.getCurrentItem() + 1, true);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);
        setTitle("专属电台");
        ((TextView) titleBar.getTitle()).setTextColor(getResourcesSafe().getColor(R.color.main_color_ffffff));
        ((TextView) titleBar.getTitle()).setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        ((ImageView) titleBar.getBack()).setImageResource(R.drawable.host_ic_x_close_n_line_regular_72);
        titleBar.getBack().setOnClickListener(v -> {
            finish();
            if (pageAdapter != null && !ToolUtil.isEmptyCollects(pageAdapter.getDataList())) {
                requestTraceImmersiveCloseClick();
            }
        });
        if (mPoolId <= 0) {
            TitleBar.ActionType interestAction = new TitleBar.ActionType("interest", TitleBar.RIGHT,
                    0, R.drawable.main_ic_category_n_n_line_regular_20, R.color.main_color_white, ImageView.class, 0, 16);
            titleBar.addAction(interestAction, v -> {
                if (pageAdapter != null && !ToolUtil.isEmptyCollects(pageAdapter.getDataList())) {
                    requestShowInterestsFragment();
                    requestTraceImmersiveInterestClick();
                }
            });
        }
        titleBar.update();
    }

    @Override
    public void finish() {
        setFinishCallBackData(mCurResumeTrackId);
        super.finish();
    }

    public void setCurResumeTrackId(long trackId) {
        mCurResumeTrackId = trackId;
    }

    public long getPoolId() {
        return mPoolId;
    }

    @SuppressLint("ClickableViewAccessibility")
    private void checkToShowGuideView() {
        if (!PodcastImmersiveConfigureManager.INSTANCE.isImmersiveGuideViewShow()) {
            mRlUserGuide.setVisibility(View.VISIBLE);
            PodcastImmersiveConfigureManager.INSTANCE.saveImmersiveGuideViewShow();
            mTrlContainer.setIsIntercept(true);
            mTrlContainer.setICallback(new TouchRelativeLayout.ICallback() {
                @Override
                public void onTouch() {
                    mTrlContainer.setIsIntercept(false);
                    mRlUserGuide.setVisibility(View.GONE);
                    if (!hasPoolId()) {
                        requestShowInterestsFragment();
                    }
                }

                @Override
                public void onScroll() {
                    mTrlContainer.setIsIntercept(false);
                    mRlUserGuide.setVisibility(View.GONE);
                    // 播放下一首
                    if (viewPager != null) {
                        viewPager.setCurrentItemInternal(viewPager.getCurrentItem() + 1, true, false, 5000);
                    }
                    HandlerManager.postOnUIThreadDelay(() -> {
                                if (!hasPoolId()) {
                                    requestShowInterestsFragment();
                                }
                            }
                            , 300);
                }
            });
        }
    }

    private void requestShowInterestsFragment() {
        IPodCastImmersiveVerticalChildListener currentChild = pageAdapter.getChildByPosition(lastRecordPosition);
        if (currentChild != null) {
            currentChild.requestShowInterestsFragment();
        }
    }

    private void requestTraceImmersiveCloseClick() {
        IPodCastImmersiveVerticalChildListener currentChild = pageAdapter.getChildByPosition(lastRecordPosition);
        if (currentChild != null) {
            currentChild.requestTraceImmersiveCloseClick();
        }
    }

    private void requestTraceImmersiveInterestClick() {
        IPodCastImmersiveVerticalChildListener currentChild = pageAdapter.getChildByPosition(lastRecordPosition);
        if (currentChild != null) {
            currentChild.requestTraceImmersiveInterestClick();
        }
    }

    private boolean hasPoolId() {
        return mPoolId > 0;
    }

    @Override
    public void onCollectChanged(boolean collect, long id) {
        if (!canUpdateUi()) {
            return;
        }
        // 同步订阅状态
        XmPlayerManager.getInstance(ToolUtil.getCtx()).updateSubscribeStatus(id, collect);
        if (pageAdapter != null && !ToolUtil.isEmptyCollects(pageAdapter.getDataList())) {
            boolean needUpdate = false;
            for (int i = 0; i < pageAdapter.getDataList().size(); i++) {
                PodGuessTrackInfo podGuessTrackInfo = pageAdapter.getDataList().get(i);
                if (podGuessTrackInfo.getAlbumId() == id) {
                    podGuessTrackInfo.setSubscribed(collect);
                    needUpdate = true;
                }
            }
            if (needUpdate) {
                pageAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    public void onTrackLikeChanged(boolean isLikeNow, long trackId) {
        if (!canUpdateUi()) {
            return;
        }
        // 如果是resume情况就不往下走
        if (isRealVisable()) {
            return;
        }
        if (pageAdapter != null && !ToolUtil.isEmptyCollects(pageAdapter.getDataList())) {
            boolean needUpdate = false;
            for (int i = 0; i < pageAdapter.getDataList().size(); i++) {
                PodGuessTrackInfo podGuessTrackInfo = pageAdapter.getDataList().get(i);
                if (podGuessTrackInfo.getTrackId() == trackId) {
                    podGuessTrackInfo.setLiked(isLikeNow);
                    int likeCount = podGuessTrackInfo.getLikeCount();
                    if (isLikeNow) {
                        likeCount++;
                    } else {
                        likeCount--;
                    }
                    likeCount = Math.max(likeCount, 0);
                    podGuessTrackInfo.setLikeCount(likeCount);
                    needUpdate = true;
                }
            }
            if (needUpdate) {
                pageAdapter.notifyDataSetChanged();
            }
        }
    }
}