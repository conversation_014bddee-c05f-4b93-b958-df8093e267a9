package com.ximalaya.ting.android.main.playpage.playy.ai.view

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.widget.NestedScrollView
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.main.playpage.view.CustomLayout


class FinalContentLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    init {
        setBackgroundColor(
            if (BaseFragmentActivity.sIsDarkMode) {
                0xff131313.toInt()
            } else {
                0xffF8f8f8.toInt()
            }
        )
    }

    fun setAiLayout(aiLayout: CustomLayout) {
        val nestScrollView = NestedScrollView(context)
        nestScrollView.isFillViewport = true
        addView(nestScrollView, LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT,
        ))
        if (aiLayout.parent != null) {
            (aiLayout.parent as ViewGroup).removeView(aiLayout)
        }

        nestScrollView.addView(aiLayout, FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT,
        ))
    }
}