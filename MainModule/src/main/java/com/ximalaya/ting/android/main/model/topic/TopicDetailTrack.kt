package com.ximalaya.ting.android.main.model.topic

import com.google.gson.annotations.SerializedName
import com.ximalaya.ting.android.host.constants.CommentConstants
import com.ximalaya.ting.android.host.model.play.CommentModel
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.track.Track

/**
 * Created by WolfXu on 2023/3/17.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
data class TopicDetailTrack(
    val uid: Long,
    val trackId: Long,
    val title: String? = null,
    val recContent: String? = null,
    @SerializedName("playsCounts")
    val playCount: Long, // 播放量
    @SerializedName("ptitle", alternate = ["pTitle"])
    val pTitle: String? = null, // 人设描述
    val nickname: String? = null,
    val logoPic: String? = null, // 主播头像
    var likesCount: Long, // 点赞数
    val fansCount: Long, // 粉丝数
    val coverPath: String? = null, // 声音封面
    val commentsCount: Long, // 声音评论数
    val greatComment: Comment? = null,
    val duration: Int,
    @SerializedName("follow", alternate = ["isFollow"])
    val isFollow: Boolean, // 是否关注该主播
    @SerializedName("like", alternate = ["isLike"])
    var isLike: Boolean, // 是否点赞该主播
    val albumId: Long
) {
    @Transient private var covertTrack: Track? = null // 转化成的Track模型
    @Transient private var isFollowNow: Boolean? = null
    @Transient var cachedThemeColor: Int? = null

    fun getConvertTrack(): Track {
        covertTrack?.let {
            return it
        }
        val track = Track()
        track.dataId = trackId
        track.kind = PlayableModel.KIND_TRACK
        track.coverUrlLarge = coverPath
        track.trackTitle = title
        track.duration = duration
        covertTrack = track
        return track
    }

    fun isFollowNow(): Boolean {
        if (isFollowNow == null) {
            isFollowNow = isFollow
        }
        return isFollowNow ?: false
    }

    fun setIsFollowNow(isFollow: Boolean) {
        isFollowNow = isFollow
    }
}

data class TopicDetailTrackList(
    val items: List<TopicDetailTrack?>? = null,
    val totalPage: Int
)

data class Comment(
    val commentId: Long,
    val comment: String? = null,
    var likesCount: Int,
    val uid: Long,
    val userCover: String? = null,
    val username: String? = null,
    @SerializedName("like", alternate = ["isLike"])
    var isLike: Boolean
) {
    @Transient private var covertCommentModel: CommentModel? = null

    fun convertToCommentModel(trackId: Long): CommentModel {
        covertCommentModel?.let {
            return it
        }

        val commentModel = CommentModel()
        commentModel.business = CommentConstants.BUSINESS_OLD
        commentModel.id = commentId
        commentModel.uid = uid
        commentModel.nickname = username
        commentModel.liked = isLike
        commentModel.likes = likesCount
        commentModel.trackId = trackId
        covertCommentModel = commentModel
        return commentModel
    }

    fun isValid(): Boolean {
        return !comment.isNullOrEmpty() && !userCover.isNullOrEmpty()
    }
}