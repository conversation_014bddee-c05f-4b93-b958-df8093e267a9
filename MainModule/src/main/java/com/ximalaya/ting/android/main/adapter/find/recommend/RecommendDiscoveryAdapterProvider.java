package com.ximalaya.ting.android.main.adapter.find.recommend;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.PadAdaptUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.ITomatoesClickCallback;
import com.ximalaya.ting.android.host.util.CategoryCardUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConfigConstants;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.host.view.ListenScrollStateHorizontalScrollView;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adapter.CalabashLineAdapter;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.main.categoryModule.fragment.CategoryRecommendFragment;
import com.ximalaya.ting.android.main.categoryModule.util.LocalListenTraceUtil;
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentNew;
import com.ximalaya.ting.android.main.manager.RecommendDailyNewsHintManager;
import com.ximalaya.ting.android.main.model.recommend.RecommendDiscoveryM;
import com.ximalaya.ting.android.main.view.CalabashBubbleView;
import com.ximalaya.ting.android.main.view.DailyNewsHintPopupWindow;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.ManualExposureHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2016/11/29.
 * <AUTHOR>
 * <p>
 * 新版糖葫芦使用CalabashAdapterProvider 替代
 */


class RecommendDiscoveryAdapterProvider implements IMulitViewTypeViewAndData<RecommendDiscoveryAdapterProvider.DiscoveryHolder, List<RecommendDiscoveryM>> {

    private ITomatoesClickCallback mCallback;
    private BaseFragment2 baseFragment;
    private Context context;
    private List<RecommendDiscoveryM> data;
    private @Nullable
    CalabashLineAdapter adapter;
    private boolean mNeedChangeSkin;
    private boolean mIsNewUi;
    private int lastScreenWidth;
    private int mItemWidth;
    private int mFirstAndLastItemMargin;
    private String mCityCode;
    private DailyNewsHintPopupWindow mDailyNewsHintPopupWindow;

    RecommendDiscoveryAdapterProvider(BaseFragment2 baseFragment, boolean needChangeSkin, boolean isNewUi, ITomatoesClickCallback callback) {
        this.baseFragment = baseFragment;
        context = baseFragment.getActivity();
        mNeedChangeSkin = needChangeSkin;
        mIsNewUi = isNewUi;
        mCallback = callback;
        mItemWidth = mIsNewUi ? BaseUtil.dp2px(context, 58) : BaseUtil.dp2px(context, 70);
        mFirstAndLastItemMargin = mIsNewUi ? BaseUtil.dp2px(context, 20) : 0;
    }

    @Override
    public void bindViewDatas(DiscoveryHolder holder, ItemModel<List<RecommendDiscoveryM>> t, View convertView, int
            position) {
        if (holder == null || t == null || t.getObject() == null || ToolUtil.isEmptyCollects(t.getObject())) {
            return;
        }

        if (ToolUtil.isEqualList(data, t.getObject()) && lastScreenWidth == BaseUtil.getScreenWidth(context)) {
            data = t.getObject();
            if (!ToolUtil.isEmptyCollects(data)) {
                for (int i = 0; i < data.size(); i++) {
                    View view = null;
                    if (adapter != null) {
                        view = adapter.getView(i, null, null);
                    }
                }
            }
            showDailyNewsHintPopupWindow(holder);
            return;
        }
        if (mIsNewUi) {
            holder.itemView.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            holder.panelCalabash.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            holder.panelCalabash.setLayoutParams(holder.panelCalabash.getLayoutParams());
        }
        lastScreenWidth = BaseUtil.getScreenWidth(context);
        data = t.getObject();

        setData(holder, data, t);
        if (position == 1) {
            convertView.setPadding(0, (int) BaseUtil.dp2px(context, 5), 0, 0);
        }
    }

    public void removeUpdateRedDotCallback() {
        if (adapter != null) {
            adapter.removeUpdateRedDotCallback();
        }
    }

    private int getDefaultDivider() {
        return BaseUtil.dp2px(context, 10);
    }

    public void setData(DiscoveryHolder holder, final List<RecommendDiscoveryM> list, ItemModel<List<RecommendDiscoveryM>> itemModel) {
        holder.panelCalabash.removeAllViews();
        Activity activity = BaseApplication.getMainActivity();
        adapter = new CalabashLineAdapter(activity == null ? context : activity, baseFragment, list, CalabashLineAdapter.FROM_RECOMMEND, mNeedChangeSkin, getNewCallback(holder, list));
        adapter.setFrom(mFrom);
        adapter.setIsNewUi(mIsNewUi);
        adapter.setMetadatasJson(mCategoryId, mTagTitle);
        holder.panelCalabash.setClipToPadding(false);
        holder.panelCalabash.setClipChildren(false);
        int screenWidth = BaseUtil.getScreenWidth(context);
        if (mFrom == CalabashLineAdapter.FROM_FIND_2)screenWidth = BaseUtil.getScreenWidth(context) - BaseUtil.dp2px(context,30);
        deleteGame(list);
        if (PadAdaptUtil.isPad(activity)) {
            deleteEbook(list);
        }
        int itemNum = list.size();
        View bubbleTargetView = null;
        RecommendDiscoveryM bubbleData = null;

        int dailyNewsItemId = BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId ? 1014 : 13;
        if (itemNum <= 5) {
            int divider;
            if (mFirstAndLastItemMargin > 0) {
                divider = (screenWidth - itemNum * mItemWidth - 2 * mFirstAndLastItemMargin) / (itemNum - 1);
            } else {
                divider = (screenWidth - itemNum * mItemWidth) / (itemNum + 1);
            }

            if (screenWidth < 10) {
                divider = getDefaultDivider();
            }

            for (int i = 0; i < list.size(); i++) {
                View child = adapter.getView(i, null, null);
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(mItemWidth, mIsNewUi ? ViewGroup.LayoutParams.WRAP_CONTENT : ViewGroup.LayoutParams.MATCH_PARENT);
                if (i == 0 && mFirstAndLastItemMargin > 0) {
                    params.leftMargin = mFirstAndLastItemMargin;
                } else {
                    params.leftMargin = divider;
                }
                if (i == list.size() - 1) {
                    params.rightMargin = (mFirstAndLastItemMargin > 0) ? mFirstAndLastItemMargin : divider;
                }
                holder.panelCalabash.addView(child, params);
                RecommendDiscoveryM discoveryM = list.get(i);
                if (discoveryM != null && discoveryM.showBubble()) {
                    bubbleTargetView = child;
                    bubbleData = discoveryM;
                }
                if (discoveryM != null && discoveryM.getId() == dailyNewsItemId) {
                    holder.dailyNewsView = child;
                }
                final int position = i;
//                setTag(child, t.get(postion).getTitle());
                child.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
//                        checkXLog(mFrom, t.get(postion).getTitle());
                        if (mFrom == CalabashLineAdapter.FROM_FIND_2 && mCallback != null) {
                            mCallback.onClicked();
                        }
                        traceOnClick(list.get(position), position);
                        adapter.onClick(v, list.get(position), position);
                    }
                });
                AutoTraceHelper.bindData(child, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(i, list.get(position)));
            }

        } else {
            int divider = (int) (screenWidth / 5.5 - mItemWidth);

            if (screenWidth < 10) {
                divider = getDefaultDivider();
            }
            holder.scrollView.setOnScrollListener(new ListenScrollStateHorizontalScrollView.OnScrollListener() {
                @Override
                public void onScrollStateChanged(ListenScrollStateHorizontalScrollView view, int scrollState) {
                    if (scrollState == SCROLL_STATE_IDLE) {
                        traceOnItemShow(itemModel, 0, holder, false, false);
                        if (mFrom == CalabashLineAdapter.FROM_RECOMMEND_NEW) {
                            traceOnItemShow(itemModel, 0, holder, true, false);
                            ManualExposureHelper.exposureViewsByScroll(baseFragment, holder.panelCalabash);
                        }
                    }
                }

                @Override
                public void onScroll(ListenScrollStateHorizontalScrollView view, boolean isTouchScroll, int l, int t, int oldl, int oldt) {

                }
            });
            holder.panelCalabash.setPadding(divider / 2, 0, 0, 0);
            for (int i = 0; i < list.size(); i++) {
                View child = adapter.getView(i, null, null);
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(mItemWidth, -1);
                params.rightMargin = divider;
                holder.panelCalabash.addView(child, params);
                RecommendDiscoveryM discoveryM = list.get(i);
                if (discoveryM != null && discoveryM.showBubble()) {
                    bubbleTargetView = child;
                    bubbleData = discoveryM;
                }
                if (discoveryM != null && discoveryM.getId() == dailyNewsItemId) {
                    holder.dailyNewsView = child;
                }
                final int position = i;
//                setTag(child, t.get(postion).getTitle());
                child.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
//                        checkXLog(mFrom, t.get(postion).getTitle());
                        if (mFrom == CalabashLineAdapter.FROM_FIND_2 && mCallback != null) {
                            mCallback.onClicked();
                        }
                        traceOnClick(list.get(position), position);
                        adapter.onClick(v, list.get(position), position);
                    }
                });
                AutoTraceHelper.bindData(child, list.get(position));
            }
        }
        final View bubbleTargetViewFinal = bubbleTargetView;
        final RecommendDiscoveryM bubbleDataFinal = bubbleData;
        if (bubbleTargetViewFinal != null && bubbleDataFinal != null) {
            holder.bubbleView.post(() -> {
                int[] location = new int[2];
                bubbleTargetViewFinal.getLocationInWindow(location);
                holder.bubbleView.setData(bubbleDataFinal.getBubbleText(), location[0] + bubbleTargetViewFinal.getWidth() / 2, bubbleDataFinal);
            });
        } else {
            holder.bubbleView.setVisibility(View.GONE);
        }
        showDailyNewsHintPopupWindow(holder);
    }

    private CalabashLineAdapter.ICalabashLineCallback getNewCallback(final DiscoveryHolder holder, final List<RecommendDiscoveryM> list) {
        return new CalabashLineAdapter.ICalabashLineCallback() {
            @Override
            public void updateRedDot() {
                if (holder == null || holder.panelCalabash == null) {
                    return;
                }
                int count = holder.panelCalabash.getChildCount();
                if (count <= 0) {
                    return;
                }
                for (int i = 0; i < count; i++) {
                    updateView(holder.panelCalabash.getChildAt(i), i);
                }
            }

            private void updateView(View itemView, int pos) {
                if (itemView != null && itemView.getTag() instanceof CalabashLineAdapter.ViewHolder) {
                    CalabashLineAdapter.ViewHolder holder = (CalabashLineAdapter.ViewHolder) itemView.getTag();
                    if (list != null && pos >= 0 && pos < list.size()) {
                        if (adapter != null) {
                            adapter.updateRedDot(holder, list.get(pos), pos);
                        }
                    }
                }
            }
        };
    }

    private void traceOnClick(RecommendDiscoveryM discoveryM, int position) {
        if (discoveryM != null) {
            if (mFrom == CalabashLineAdapter.FROM_RECOMMEND_NEW) {
                int dailyNewsItemId = BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId ? 1014 : 13;
                int intervalTime = 0;
                String dialogName = "";
                String tipsText = "";
                if (discoveryM.getId() == dailyNewsItemId && (RecommendDailyNewsHintManager.INSTANCE.needShowDailyNewsHint()
                                || (mDailyNewsHintPopupWindow != null && mDailyNewsHintPopupWindow.isShowing()))) {
                    intervalTime = RecommendDailyNewsHintManager.INSTANCE.getAnimationTime();
                    tipsText = RecommendDailyNewsHintManager.INSTANCE.getContent();
                    dialogName = "今日热点引导动效气泡";
                }
                int hitPersonal = -1;
                if (discoveryM.getProperties() != null) {
                    hitPersonal = discoveryM.getProperties().isPersional() ? 1 : -1;
                }
                // 新首页-糖葫芦  点击事件
                new XMTraceApi.Trace()
                        .click(777)
                        .put("Item", discoveryM.getTitle())
                        .put("displayClass", "0")
                        .put("positionNew", String.valueOf(position + 1))
                        .put("contentId", String.valueOf(discoveryM.getId()))
                        .put("strategy", discoveryM.getStrategyName())
                        .put("itingUrl", discoveryM.getItingForTrace())
                        .put("hasDynamicGuide", discoveryM.getEnhanceShowTag())
                        .put("isHot", String.valueOf(discoveryM.isShowLabelValue()))
                        .put("content", discoveryM.isShowLabel() ? discoveryM.getBubbleText() : "")
                        .put("rec_track", discoveryM.getRecTrack())
                        .put("rec_src", discoveryM.getRecSrc())
                        .put("ubtTraceId", discoveryM.getUbtTraceId())
                        .put("currPage", "newHomePage")
                        .put("IntervalTime", intervalTime + "")
                        .put("tipsText", tipsText)
                        .put("dialogName", dialogName)
                        .put("isHitPersonnal", String.valueOf(hitPersonal))
                        .createTrace();
            } else if (mFrom == CalabashLineAdapter.FROM_CATEGORY_RECOMMEND) {
                // 分类_推荐-糖葫芦  点击事件
                new XMTraceApi.Trace()
                        .click(938)
                        .put("Item", discoveryM.getTitle())
                        .put("categoryId", mCategoryId)
                        .put("displayClass", "0")
                        .put("currPage", "categoryRecommend")
                        .createTrace();
            } else if (CategoryCardUtil.isLocalListenNewPage(mFrom == CalabashLineAdapter.FROM_LOCAL)) {
                // 推荐_本地听-糖葫芦  点击事件
                new XMTraceApi.Trace()
                        .click(2935)
                        .put("Item", discoveryM.getTitle())
                        .put("displayClass", "0")
                        .put("cityId", mCityCode)
                        .put("itingUrl", discoveryM.getItingForTrace())
                        .createTrace();
            }
        }
    }

    private void bindTrace(List<RecommendDiscoveryM> datas, ViewGroup viewGroup) {
        if (ToolUtil.isEmptyCollects(datas) || viewGroup == null) {
            return;
        }
        int childCount = viewGroup.getChildCount();
        int dataSize = datas.size();
        for (int i = 0; i < childCount; i++) {
            View view = viewGroup.getChildAt(i);
            if (view != null && i < dataSize) {
                RecommendDiscoveryM discoveryM = datas.get(i);
                if (discoveryM != null && mFrom == CalabashLineAdapter.FROM_RECOMMEND_NEW) {
                    // 新首页-糖葫芦  控件曝光
                    XMTraceApi.Trace trace = new XMTraceApi.Trace()
                            .setMetaId(36538)
                            .setServiceId("slipPage")
                            .put("Item", discoveryM.getTitle())
                            .put("displayClass", "0")
                            .put("itingUrl", discoveryM.getItingForTrace())
                            .put("currPage", "newHomePage")
                            .excludeForKey(Arrays.asList("exploreType"));
                    trace.bindTrace(view);
                } else if (discoveryM != null && mFrom == CalabashLineAdapter.FROM_CATEGORY_RECOMMEND) {
                    // 分类_推荐-糖葫芦（新版曝光）  控件曝光
                    new XMTraceApi.Trace()
                            .setMetaId(38992)
                            .setServiceId("slipPage")
                            .put("categoryId", mCategoryId)
                            .put("Item", discoveryM.getTitle())
                            .put("displayClass", "0")
                            .put("currPage", "categoryRecommend")
                            .bindTrace(view);
                }
            }
        }
    }

    private void showDailyNewsHintPopupWindow(DiscoveryHolder holder) {
        if (holder == null || holder.dailyNewsView == null
                || !RecommendDailyNewsHintManager.INSTANCE.needShowDailyNewsHint()) {
            return;
        }
        final View finalAnchor = holder.dailyNewsView;
        holder.dailyNewsView.post(() -> {
            if (baseFragment == null || !baseFragment.canUpdateUi()) {
                return;
            }
            String content = RecommendDailyNewsHintManager.INSTANCE.getContent();
            if (content != null && content.length() > 0) {
                if (mDailyNewsHintPopupWindow == null) {
                    mDailyNewsHintPopupWindow = new DailyNewsHintPopupWindow(baseFragment, content);
                }
                mDailyNewsHintPopupWindow.showAtLocation(finalAnchor,
                        RecommendDailyNewsHintManager.INSTANCE.getAnimationTime() * 1000);
            }
        });
    }

    public void dismissDailyNewsHintPopupWindow() {
        if (mDailyNewsHintPopupWindow != null && mDailyNewsHintPopupWindow.isShowing()) {
            mDailyNewsHintPopupWindow.dismiss();
        }
    }

    public void traceOnItemShow(ItemModel<List<RecommendDiscoveryM>> itemModel, int position, HolderAdapter.BaseViewHolder holder, boolean isNew, boolean needBubble) {
        if (itemModel == null) {
            return;
        }
        List<RecommendDiscoveryM> datas = itemModel.object;
        if (ToolUtil.isEmptyCollects(datas)) {
            return;
        }
        int dataSize = datas.size();
        if (holder instanceof DiscoveryHolder) {
            DiscoveryHolder discoveryHolder = (DiscoveryHolder) holder;
            if (discoveryHolder.panelCalabash == null) {
                return;
            }
            int childCount = discoveryHolder.panelCalabash.getChildCount();
            //先把频道页的exploreType存下来，防止出现post操作后取到的exploreType不准确的问题
            String categoryExploreType = baseFragment instanceof CategoryRecommendFragment ?
                    ((CategoryRecommendFragment) baseFragment).getExploreType() : "0";
            String recommendExploreType = mFrom == CalabashLineAdapter.FROM_RECOMMEND_NEW
                    ? String.valueOf(RecommendFragmentNew.mExploreType) : "";
            if (isNew) {
                bindTrace(datas, discoveryHolder.panelCalabash);
                return;
            }else if (mFrom == CalabashLineAdapter.FROM_CATEGORY_RECOMMEND) {
                bindTrace(datas, discoveryHolder.panelCalabash);
            }
            boolean showDailyNewsHint = RecommendDailyNewsHintManager.INSTANCE.needShowDailyNewsHint()
                    || (mDailyNewsHintPopupWindow != null && mDailyNewsHintPopupWindow.isShowing());
            discoveryHolder.panelCalabash.post(new Runnable() {
                @Override
                public void run() {
                    for (int i = 0; i < childCount; i++) {
                        View view = discoveryHolder.panelCalabash.getChildAt(i);
                        if (ViewStatusUtil.viewIsShowing(view)) {
                            if (i < dataSize) {
                                RecommendDiscoveryM discoveryM = datas.get(i);
                                if (discoveryM != null) {
                                    if (mFrom == CalabashLineAdapter.FROM_RECOMMEND_NEW) {
                                        int dailyNewsItemId = BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId ? 1014 : 13;
                                        int intervalTime = 0;
                                        String dialogName = "";
                                        String tipsText = "";
                                        if (discoveryM.getId() == dailyNewsItemId && showDailyNewsHint) {
                                            intervalTime = RecommendDailyNewsHintManager.INSTANCE.getAnimationTime();
                                            tipsText = RecommendDailyNewsHintManager.INSTANCE.getContent();
                                            dialogName = "今日热点引导动效气泡";
                                        }
                                        int hitPersonal = -1;
                                        if (discoveryM.getProperties() != null) {
                                            hitPersonal = discoveryM.getProperties().isPersional() ? 1 : -1;
                                        }
                                        // 新首页-糖葫芦  控件曝光
                                        new XMTraceApi.Trace()
                                                .setMetaId(2956)
                                                .setServiceId("slipPage")
                                                .put("Item", discoveryM.getTitle())
                                                .put("positionNew", String.valueOf(i + 1))
                                                .put("displayClass", "0")
                                                .put("itingUrl", discoveryM.getItingForTrace())
                                                .put("contentId", String.valueOf(discoveryM.getId()))
                                                .put("hasDynamicGuide", discoveryM.getEnhanceShowTag())
                                                .put("strategy", discoveryM.getStrategyName())
                                                .put("isHot", String.valueOf(discoveryM.isShowLabelValue()))
                                                .put("content", discoveryM.isShowLabel() ? discoveryM.getBubbleText() : "")
                                                .put("rec_track", discoveryM.getRecTrack())
                                                .put("rec_src", discoveryM.getRecSrc())
                                                .put("ubtTraceId", discoveryM.getUbtTraceId())
                                                .put("currPage", "newHomePage")
                                                .put("exploreType", recommendExploreType)
                                                .put("IntervalTime", intervalTime + "")
                                                .put("tipsText", tipsText)
                                                .put("dialogName", dialogName)
                                                .put("isHitPersonnal", String.valueOf(hitPersonal))
                                                .createTrace();
                                    } else if (mFrom == CalabashLineAdapter.FROM_CATEGORY_RECOMMEND) {
                                        // 分类_推荐-糖葫芦  控件曝光
                                        if (baseFragment instanceof CategoryRecommendFragment) {
                                            new XMTraceApi.Trace()
                                                    .setMetaId(11520)
                                                    .setServiceId("slipPage")
                                                    .put("Item", discoveryM.getTitle())
                                                    .put("categoryId", mCategoryId)
                                                    .put("displayClass", "0")
                                                    .put("currPage", "categoryRecommend")
                                                    .put("exploreType", categoryExploreType)
                                                    .createTrace();
                                        }
                                    } else if (mFrom == CalabashLineAdapter.FROM_LOCAL) {
                                        // 分类_推荐-糖葫芦  控件曝光
                                        if (baseFragment instanceof CategoryRecommendFragment) {
                                            LocalListenTraceUtil.markPointOnLocalListenTanghuluItemDisplay(discoveryM.getTitle(), "0",
                                                    mCityCode, discoveryM.getItingForTrace(), categoryExploreType,
                                                    discoveryM.getXmRequestId(), discoveryM.getId());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            });
            if (needBubble) {
                discoveryHolder.bubbleView.post(() -> discoveryHolder.bubbleView.traceForShow(recommendExploreType));
            }
        }
    }

    private void setTag(View child, String title) {
        Map<String, String> logTag = null;
        if (AutoTraceHelper.getLogTag(child) != null) {
            logTag = AutoTraceHelper.getLogTag(child);
        } else {
            logTag = new HashMap<>();
        }
        logTag.put("page", "home");
        logTag.put("from", mFrom + "");
        logTag.put("clickName", "tangHuLu");
        logTag.put("tag", "new");
        logTag.put("title", title);

        AutoTraceHelper.setLogTag(child, logTag);
    }

    private String mCategoryId;
    private String mTagTitle;

    void setMetaData(String categoryId, String tagTitle) {
        if (TextUtils.isEmpty(tagTitle)) {
            tagTitle = "全部筛选";
        }

        mCategoryId = categoryId;
        mTagTitle = tagTitle;

        if (adapter != null) {
            adapter.setMetadatasJson(categoryId, tagTitle);
        }
    }

    private int mFrom;

    public void setFrom(int from) {
        if (adapter != null) {
            adapter.setFrom(from);
        }
        mFrom = from;
    }

    public void setCityCode(String cityCode) {
        mCityCode = cityCode;
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_view_recommend_discovery, parent, false);
    }

    @Override
    public DiscoveryHolder buildHolder(View convertView) {
        return new DiscoveryHolder(convertView);
    }

    private void deleteGame(List<RecommendDiscoveryM> data) {
        if (data == null) {
            return;
        }

        String channel;
        channel = DeviceUtil.getChannelInApk(context);
        if (!TextUtils.isEmpty(channel) && AppConfigConstants.channelHasNoGameCenterList.contains(channel)) {
            Iterator<RecommendDiscoveryM> iterator = data.iterator();
            while (iterator.hasNext()) {
                RecommendDiscoveryM temp = iterator.next();
                if (temp != null && !TextUtils.isEmpty(temp.getTitle())) {
                    boolean haveGame = temp.getTitle().contains("游戏中心") || temp.getId() == 10;
                    if (haveGame) {
                        iterator.remove();
                    }
                }
            }
        }
    }

    private void deleteEbook(List<RecommendDiscoveryM> data) {
        if (data == null) {
            return;
        }

        String channel;
        channel = DeviceUtil.getChannelInApk(context);
        if (!TextUtils.isEmpty(channel)) {
            Iterator<RecommendDiscoveryM> iterator = data.iterator();
            while (iterator.hasNext()) {
                RecommendDiscoveryM temp = iterator.next();
                if (temp != null && !TextUtils.isEmpty(temp.getTitle())) {
                    boolean haveEbook = temp.getTitle().contains("看小说") || temp.getId() == 192038;
                    if (haveEbook) {
                        iterator.remove();
                    }
                }
            }
        }
    }

    void manulClick(View view, RecommendDiscoveryM model, int position) {
        new UserTracking().setSrcPage("首页_精品")
                .setSrcModule("活动")
                .setSrcTitle("文本类型")
                .statIting("event", XDCSCollectUtil.SERVICE_CLICK);
        if (adapter != null) {
            adapter.onClick(view, model, position);
        }
    }

    static class DiscoveryHolder extends HolderAdapter.BaseViewHolder {
        LinearLayout panelCalabash;
        View itemView;
        ListenScrollStateHorizontalScrollView scrollView;
        CalabashBubbleView bubbleView;
        View dailyNewsView;

        DiscoveryHolder(View convertView) {
            itemView = convertView;
            scrollView = convertView.findViewById(R.id.main_hsl_calabash);
            panelCalabash = (LinearLayout) convertView.findViewById(R.id.main_layout_calabash);
            bubbleView = (CalabashBubbleView) convertView.findViewById(R.id.main_bubble);
        }
    }

}
