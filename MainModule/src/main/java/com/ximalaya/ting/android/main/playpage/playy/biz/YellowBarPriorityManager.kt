package com.ximalaya.ting.android.main.playpage.playy.biz

import android.view.ViewGroup
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.main.playpage.playy.biz.impl.IYellowBarPriorityImpl
import com.ximalaya.ting.android.robust.utils.Log
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger

class YellowBarPriorityManager(
    private val barContainer: ViewGroup,
    vipBarId: Int,
    adBarId: Int,
) : IYellowBarPriorityImpl {

    private val priorityListeners = mutableMapOf<XYellowBar, YellowBarPriorityListener>()
    private val sizeChangeListeners = mutableSetOf<YellowBarSizeChangeListener>()


    private val validBiz = listOf("yellowZone", "ads")

    private val vipBarView: ViewGroup
    private val adBarView: ViewGroup

    private var currentShowingTrace: XYellowBar? = null
    private var serverResp: String? = null
    private var soundInfo: PlayingSoundInfo? = null

    private var containerHeight: Int = -1
        private set
    init {
        vipBarView = barContainer.findViewById(vipBarId)
        adBarView = barContainer.findViewById(adBarId)

        barContainer.addOnLayoutChangeListener { v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
            computeTrace()

            val height = bottom - top
            if (height != containerHeight) {
                containerHeight = height
                Log.d("YellowBar", "containerHeight = $containerHeight")
                kotlin.runCatching {
                    sizeChangeListeners.forEach { it.onChange(containerHeight) }
                }
            }
        }
    }

    override fun getYellowBarHeight(): Int {
        return containerHeight
    }

    override fun getVipBarView(): ViewGroup {
        return vipBarView
    }

    override fun getAdBarView(): ViewGroup {
        return adBarView
    }

    private fun computeTrace() {
        val preShowing = currentShowingTrace
        currentShowingTrace = null

        if (vipBarView.isShown && vipBarView.height > 0) {
            currentShowingTrace =  XYellowBar.COMMERCIAL
        }
        if (adBarView.isShown && adBarView.height > 0) {
            currentShowingTrace =  XYellowBar.AD
        }

        if (preShowing != currentShowingTrace && currentShowingTrace != null) {
            trace()
        }
    }

    private fun trace() {
        val showing = currentShowingTrace?: return

        // 新声音播放页-统一下挂小黄条（优先级）  控件曝光
        XMTraceApi.Trace()
            .setMetaId(54532)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newPlay")
            .put("currTrackId", soundInfo?.trackInfo?.trackId?.toString())
            .put("currAlbumId", soundInfo?.trackInfo?.albumId?.toString())
            .put("type", if (showing == XYellowBar.AD) "ads" else "yellowZone")
            .put("responseType", "$serverResp") // 记录服务端返回的类型
            .put(XmRequestIdManager.CONT_ID, soundInfo?.trackInfo?.trackId?.toString())
            .put(XmRequestIdManager.CONT_TYPE, "newPlayYellowBar")
            .put(
                XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                    XmRequestPage.PAGE_PLAY_PAGE))
            .createTrace()
    }

    override fun onResume() {
        trace()
    }

    override fun registerPriorityListener(biz: XYellowBar, listener: YellowBarPriorityListener) {
        priorityListeners[biz] = listener
    }

    override fun removePriorityListener(biz: XYellowBar) {
        priorityListeners.remove(biz)
    }

    //    "yellowZone",
    //    "ads"
    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        currentShowingTrace = null
        this.soundInfo = soundInfo
        barContainer.post { computeTrace() }

        serverResp = soundInfo?.hangingBarPriority?.firstOrNull()
        val priority = soundInfo?.hangingBarPriority?.firstOrNull { it in validBiz }

        if (priority.isNullOrBlank()) {
            Logger.e("msg_uve_pri", " ---- --- - --- -- -- - 没返回优先级")

            barContainer.removeAllViews()
            notify(null)
            return
        }

        if (priority == "yellowZone") {
            Logger.e("msg_uve_pri", " ---- --- - --- -- -- - 会员优先级高")
            barContainer.removeView(adBarView)
            if (barContainer.indexOfChild(vipBarView) < 0) {
                barContainer.addView(vipBarView)
            }
            notify(XYellowBar.COMMERCIAL)
        }

        if (priority == "ads") {
            Logger.e("msg_uve_pri", " ---- --- - --- -- -- - 广告优先级高")

            barContainer.removeView(vipBarView)
            if (barContainer.indexOfChild(adBarView) < 0) {
                barContainer.addView(adBarView)
            }
            notify(XYellowBar.AD)
            return
        }
    }

    private fun notify(biz: XYellowBar?) {
        priorityListeners.entries.forEach {
            if (it.key == biz) {
                it.value.onGainedPriority()
            } else {
                it.value.onLosePriority()
            }
        }
    }

    override fun registerSizeChangeListener(listener: YellowBarSizeChangeListener) {
        if (containerHeight >= 0) listener.onChange(containerHeight)
        sizeChangeListeners.add(listener)
    }

    override fun unRegisterSizeChangeListener(listener: YellowBarSizeChangeListener?) {
        if (sizeChangeListeners != null && listener != null && sizeChangeListeners.contains(listener)) {
            sizeChangeListeners.remove(listener)
        }
    }
}