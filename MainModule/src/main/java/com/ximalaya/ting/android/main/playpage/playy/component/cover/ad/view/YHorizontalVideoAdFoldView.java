package com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.view;

import android.content.Context;
import android.util.AttributeSet;

import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.IXAdComponentProvider;

public class YHorizontalVideoAdFoldView extends YHorizontalVideoAdView{

    public YHorizontalVideoAdFoldView(Context context, IXAdComponentProvider adComponentProvider) {
        super(context, adComponentProvider);
    }

    public YHorizontalVideoAdFoldView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public YHorizontalVideoAdFoldView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected int getResId() {
        return R.layout.main_play_video_view_y_v2_fold;
    }
}
