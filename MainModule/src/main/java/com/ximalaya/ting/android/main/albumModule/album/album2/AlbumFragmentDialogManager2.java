package com.ximalaya.ting.android.main.albumModule.album.album2;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.AlbumCollectManager;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.fragment.BaseFragmentManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.albumModule.other.AlbumRatingDialogFragment;
import com.ximalaya.ting.android.main.dialog.AlbumFreeToPaidDialog;
import com.ximalaya.ting.android.main.dialog.SubcribeAlbumDialog;
import com.ximalaya.ting.android.main.manager.albumFragment.AlbumFragmentMarkPointManager;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;

/**
 * Created by 5Greatest on 2021.07.01
 *
 * <AUTHOR>
 * On 2021/7/1
 */
public class AlbumFragmentDialogManager2 extends BaseFragmentManager<AlbumFragmentNew2> {
    private static final String SPKEY_IS_SUBSCRIBE_DIALOG_SHOW = "SPKEY_IS_SUBSCRIBE_DIALOG_SHOW_";
    private static final String SPKEY_ENTER_TIMES = "SPKEY_ALBUM_ENTER_TIMES_";

    private static final String GROUP_NAME = "toc";
    private static final String ITEM_NAME_POP = "Album-popup";
    private static final String ITEM_NAME_POP_COUNT = "Album-popup-times";
    private static final String ITEM_NAME_POP_SUBSCRIBE_COUNT = "Album-popup-subscriptionCount";

    // 没有很搞清楚这个字段是做什么用的
    public boolean mIsFinish;

    private final AlbumFragmentPresenter2 mPresenter;
    private SharedPreferencesUtil mSp;

    public AlbumFragmentDialogManager2(@NotNull AlbumFragmentPresenter2 presenter, @NotNull AlbumFragmentNew2 fragmentNew) {
        super(presenter, fragmentNew);
        this.mPresenter = presenter;
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////     setter and getter    ///////////////////////////////////////
    ///////////////////////////////////           start          ///////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////////

    private SharedPreferencesUtil getSharedPreferencesUtil() {
        if (null == mSp) {
            mSp = SharedPreferencesUtil.getInstance(mPresenter.getContext());
        }
        return mSp;
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////     setter and getter    ///////////////////////////////////////
    ///////////////////////////////////            end           ///////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////////

    @Override
    public void doOnDestroyFragment() {

    }

    /**
     * 显示 免转付 的提示dialog
     */
    public void showAlbumFreeToPaidDialog() {
        AlbumFreeToPaidDialog.Data data = null;
        if (mPresenter.getAlbum() != null && mPresenter.getAlbum().albumFreeToPaidInfo != null
                && mPresenter.getAlbum().albumFreeToPaidInfo.getActivateReminder() != null
                && mPresenter.getAlbum().albumFreeToPaidInfo.getActivateReminder().isShow) {
            data = AlbumFreeToPaidDialog.Data.parse(mPresenter.getAlbum().albumFreeToPaidInfo.getActivateReminder());
        }
        if (data == null) {
            return;
        }
        if (mPresenter.getAlbum().canShow818Pop()) {
            return;
        }
        String fragmentTag = "AlbumFreeToPaidDialog";
        AlbumFragmentNew2 fragment = getFragment();
        if (null == fragment) {
            return;
        }
        FragmentManager fragmentManager = fragment.getFragmentManager();
        if (fragmentManager != null && fragmentManager.findFragmentByTag(fragmentTag) == null) {
            AlbumFreeToPaidDialog.UserTrackingData trackingData = new AlbumFreeToPaidDialog.UserTrackingData();
            trackingData.srcPage = "album";
            trackingData.srcPageId = String.valueOf(mPresenter.getAlbum().getId());
            AlbumFreeToPaidDialog dialog = new AlbumFreeToPaidDialog();
            Bundle args = new Bundle();
            args.putSerializable(AlbumFreeToPaidDialog.ARGS_DATA, data);
            args.putBoolean(AlbumFreeToPaidDialog.ARGS_HAS_PERMISSION, mPresenter.getAlbum().albumFreeToPaidInfo.isHasPermission());
            args.putBoolean(AlbumFreeToPaidDialog.ARGS_HAS_ACTIVATED, mPresenter.getAlbum().albumFreeToPaidInfo.isHasActivated());
            args.putLong(AlbumFreeToPaidDialog.ARGS_ALBUM_ID, mPresenter.getAlbum().getId());
            args.putSerializable(AlbumFreeToPaidDialog.ARGS_USER_TRACKING_DATA, trackingData);
            dialog.setArguments(args);
            dialog.show(fragmentManager, fragmentTag);

            AlbumFragmentMarkPointManager.Companion.markPointOnShowFreeToPaidDialog(mPresenter.getAlbum().getId());
        }
    }

    /**
     * 显示评分引导弹窗
     */
    public boolean showAlbumRatingDialogFrag(boolean afterComment) {
        Logger.d("AlbumFragmentNew", "showAlbumRatingDialogFrag");
        if (!UserInfoMannage.hasLogined()) {
            return false;
        }
        if (mPresenter.getAlbum() == null || mPresenter.getAlbum().isInBlacklist()) {
            return false;
        }
        if (mPresenter.getAlbum().getUid() == UserInfoMannage.getUid()) {
            return false;
        }
        if (mPresenter.getAlbum().isCommented()) {
            return false;
        }
        if (afterComment) {
            if (Util.canAlbumRate(mPresenter.getAlbum())) {
                return false;
            }
        } else {
            int minPercentage = ConfigureCenter.getInstance().getInt("toc", "pop_ablum_value", 20);
            if (mPresenter.getAlbum().getListenedPercentage() <= minPercentage || mPresenter.getAlbum().getListenedPercentage() == 100) {
                return false;
            }
        }
        AlbumFragmentNew2 fragment = getFragment();
        if (null == fragment) {
            return false;
        }
        long lastShowTime = getSharedPreferencesUtil().getLong("main_album_rating_dialog_frag_last_show_date", 0);
        if (DateTimeUtil.calDiffDay(lastShowTime) >= 30) {
            AlbumRatingDialogFragment.newInstance(mPresenter.getAlbum(), afterComment, 0)
                    .show(fragment.getChildFragmentManager(), "AlbumRatingDialogFragment");
            getSharedPreferencesUtil().saveLong("main_album_rating_dialog_frag_last_show_date", System.currentTimeMillis());
            return true;
        }
        return false;
    }

    /**
     * 显示打开售后页时的订阅引导弹窗
     */
    public void showOnStartSubscribeDialog(AlbumM album) {
        // 先统计进入次数
        Util.recordAlbumCount(album, getSharedPreferencesUtil());

        if (null == album || album.isFavorite()
                || mPresenter.isHasShowSubscribeDialog()) {
            return;
        }
        if (album.canShow818Pop()) {
            return;
        }

        boolean serverShow = ConfigureCenter.getInstance().getBool(GROUP_NAME, ITEM_NAME_POP, true);
        int enterTimes = ConfigureCenter.getInstance().getInt(GROUP_NAME, ITEM_NAME_POP_COUNT, 3);
        // 登录用户订阅弹窗订阅量阈值，小于阈值才出弹窗
        int subscribeCount = ConfigureCenter.getInstance().getInt(GROUP_NAME, ITEM_NAME_POP_SUBSCRIBE_COUNT, 1);

        long albumId = album.getId();
        String isShowedKey = SPKEY_IS_SUBSCRIBE_DIALOG_SHOW + albumId;
        boolean isShowed = getSharedPreferencesUtil().getBoolean(isShowedKey, false);
        boolean doShow = false;
        if (serverShow && !isShowed) {
            String key = SPKEY_ENTER_TIMES + albumId;
            int times = getSharedPreferencesUtil().getInt(key, 0);
            if (times >= enterTimes) {
                if (!UserInfoMannage.hasLogined()) {
                    List list = AlbumCollectManager.getInstance(BaseApplication.getMyApplicationContext()).getAlbumList();
                    if (ToolUtil.isEmptyCollects(list)) {
                        doShow = true;
                    }
                } else {
                    Map<String, Object> extras = album.getExtras();
                    if (extras.containsKey("subscriptionCount")) {
                        int count = (int) extras.get("subscriptionCount");
                        if (count < subscribeCount) {
                            doShow = true;
                        }
                    }
                }
            }
            if (doShow) {
                getSharedPreferencesUtil().removeByKey(key);
                String subscribe = "订阅";
                String subscribeInResource = mPresenter.getContext().getResources().getString(R.string.main_subscribe);
                if (!TextUtils.isEmpty(subscribeInResource)) {
                    subscribe = subscribeInResource;
                }
                showSubscribeAlbumDialog("喜欢就" + subscribe + "吧", "可以在【我听】中找到" + subscribe + "的专辑");
                getSharedPreferencesUtil().saveBoolean(isShowedKey, true);

                AlbumFragmentMarkPointManager.Companion.markPointOnShowSubscribeDialog();
            }
        }
    }

    /**
     * 显示售后页关闭时的订阅引导弹窗
     */
    public void showOnEndSubscribeDialog() {
        if (mPresenter.getAlbum() != null && !mIsFinish && !mPresenter.isHasShowSubscribeDialog()
                && !Util.isAlbumSubscribe(mPresenter.getContext(), mPresenter.getAlbumId(), mPresenter.getAlbum())
                && !mPresenter.getAlbum().isOfflineHidden()) {
            String str = getSharedPreferencesUtil().getString(PreferenceConstantsInHost.TINGMAIN_KEY_RECOMMEND_ALBUMS);
            if (TextUtils.isEmpty(str)) {
                return;
            }
            try {
                JSONArray jsonArray = new JSONArray(str);
                if (jsonArray.length() <= 0) {
                    return;
                }
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jsonObject = jsonArray.optJSONObject(i);
                    if (jsonObject == null || !jsonObject.has("albumId")) {
                        continue;
                    }
                    long albumId = jsonObject.optLong("albumId");
                    if (albumId != mPresenter.getAlbumId()) {
                        continue;
                    }
                    boolean hasShown = getSharedPreferencesUtil().getBoolean(SPKEY_IS_SUBSCRIBE_DIALOG_SHOW + albumId, false);
                    if (!hasShown) {
                        mIsFinish = true;
                        showSubscribeAlbumDialog("", "");
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    private void showSubscribeAlbumDialog(String title, String msg) {
        AlbumFragmentNew2 fragment = getFragment();
        if (null == fragment) {
            return;
        }
        mPresenter.setHasShowSubscribeDialog(true);
        getSharedPreferencesUtil().saveBoolean(SPKEY_IS_SUBSCRIBE_DIALOG_SHOW + mPresenter.getAlbumId(), true);
        SubcribeAlbumDialog subcribeAlbumDialog = SubcribeAlbumDialog.newInstance(mPresenter.getAlbum(), title, msg);
        subcribeAlbumDialog.show(fragment.getChildFragmentManager(), SubcribeAlbumDialog.TAG);
        subcribeAlbumDialog.setResultListener(new SubcribeAlbumDialog.IOnSubscribeResult() {

            @Override
            public void onClickSubScripteButton() {
                AlbumFragmentNew2 tempFragment = getFragment();
                if (null != tempFragment) {
                    tempFragment.doSubscribe(null, false);
                }
            }

            @Override
            public void onDismiss() {
                mIsFinish = false;
            }
        });
    }

    private static class Util {
        /**
         * 统计该专辑的进入次数
         */
        public static void recordAlbumCount(AlbumM album, SharedPreferencesUtil util) {
            if (null == album || album.isFavorite() || null == util) {
                return;
            }
            long albumId = album.getId();
            String isShowedKey = SPKEY_IS_SUBSCRIBE_DIALOG_SHOW + albumId;
            boolean isShowed = util.getBoolean(isShowedKey, false);
            if (!isShowed) {
                String key = AlbumFragmentDialogManager2.SPKEY_ENTER_TIMES + albumId;
                int times = util.getInt(key, 0);
                times++;
                util.saveInt(key, times);
            }
        }

        /**
         * 判断改专辑否否已经订阅
         */
        public static boolean isAlbumSubscribe(Context context, long albumId, AlbumM album) {
            if (!UserInfoMannage.hasLogined()) {
                Album record = AlbumCollectManager.getInstance(context).getAlbum(albumId);
                return record != null;
            } else if (album == null) {
                return false;
            } else {
                return album.isFavorite();
            }
        }

        /**
         * 是否可以评分当前专辑
         */
        public static boolean canAlbumRate(AlbumM album) {
            if (album == null) {
                return false;
            }
            return true;
        }
    }
}
