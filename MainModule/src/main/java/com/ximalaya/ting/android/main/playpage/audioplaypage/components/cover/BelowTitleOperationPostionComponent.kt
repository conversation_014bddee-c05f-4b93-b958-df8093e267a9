package com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover

import android.animation.ValueAnimator
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.AbsListView
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listener.ICollectWithFollowStatusCallback
import com.ximalaya.ting.android.host.listener.IFollowDialogCallback
import com.ximalaya.ting.android.host.manager.LastAudioPlayListCache
import com.ximalaya.ting.android.host.manager.account.AnchorCollectManage
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumCollectParam
import com.ximalaya.ting.android.host.model.play.PlayPageMinorData
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.ActionConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.anchorModule.anchorSpace.util.newAnchorSpaceFragment
import com.ximalaya.ting.android.main.mine.extension.visible
import com.ximalaya.ting.android.main.model.MyClubSubscribeModel
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayPageAnimationManager
import com.ximalaya.ting.android.main.playpage.audioplaypage.CoverComponentsManager
import com.ximalaya.ting.android.main.playpage.audioplaypage.PlayPageAuthorPopAdManager
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.BaseCoverComponent
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.remote.IScrollListenerCallBack
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.others.BarBelowTitleComponent
import com.ximalaya.ting.android.main.playpage.internalservice.IAudioPlayPageTrackInfoComponentService
import com.ximalaya.ting.android.main.playpage.internalservice.OnFollowGuideVisibleChangedListener
import com.ximalaya.ting.android.main.playpage.internalservice.IBelowTitleOperationPositionComponentService
import com.ximalaya.ting.android.main.playpage.internalservice.ICommentBoxHideListener
import com.ximalaya.ting.android.main.playpage.internalservice.IAudioPlayFragmentService
import com.ximalaya.ting.android.main.playpage.internalservice.ICommentBoxService
import com.ximalaya.ting.android.main.playpage.internalservice.IXPlayFragmentService
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.manager.PlayPageMinorDataManager
import com.ximalaya.ting.android.main.playpage.util.PlayCommentUtil
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.util.TimeUtil
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.main.view.BookOpenAnimationLinearLayout
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import java.util.Calendar
import kotlin.collections.HashMap

/**
 * Created by WolfXu on 2022/8/11.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
class BelowTitleOperationPostionComponent : BaseCoverComponent() {

    private var mHasSetTopTrackInfoShowContentChangeListener = false
    private var mPlayPageMinorData: PlayPageMinorData? = null
    private var mShowingView: View? = null
    private var mHeightChangeListener: ((height: Int) -> Unit)? = null

    private val mIvArrow by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<ImageView>(R.id.main_iv_arrow)
    }

    // 有声书引导语 + 订阅
    private var mAlbumGuideFirstShowAfterDataLoaded = true
    private var mVsAlbumGuideSubscribe: ViewStub? = null
    private var mAlbumGuideSubscribe: BookOpenAnimationLinearLayout? = null
    private var mGuideSubscribeContent: TextView? = null
    private var mGuideSubscribeBtn: TextView? = null
    private var mGuideSubscribeClose: ImageView? = null
    private var mIsAlbumSubscribeHasClicked = false

    // 直播相关控件
    private var mLiveViewFirstShowAfterDataLoaded = true
    private var mLiveRootView: View? = null
    private var mIvLiveAvatar: ImageView? = null
    private var mTvLiveContent: TextView? = null
    private var mTvLivingTag: TextView? = null
    private var mTvLiveGoBtn: TextView? = null
    private var mVgLiveAvatarContainer: ViewGroup? = null
    private var mLiveWaveView: XmLottieAnimationView? = null
    private var mLiveVideoWaveView: XmLottieAnimationView? = null
    private var mHasShowLiveWaveAnimation = false

    // MyClub相关控件
    private var mMcViewFirstShowAfterDataLoaded = true
    private var mMyClubView: View? = null
    private var mIvMcAvatar: ImageView? = null
    private var mTvMcTopic: TextView? = null
    private var mTvMcInfo: TextView? = null
    private var mHasMcStatViewed = false
    private var mTvMcJoinBtn: TextView? = null
    private var mReceiver: BroadcastReceiver? = null
    private var mMyclubWaveView: XmLottieAnimationView? = null
    private var mGroupMcChatting: Group? = null
    private var mGroupMcSchedule: Group? = null
    private var mHasShowMcWaveAnimation = false

    // 关注引导相关控件
    private var mFollowGuideFirstShowAfterDataLoaded = true
    private var mVFollowGuideRootView: View? = null
    private var mIvGuideAnchorAvatar: ImageView? = null
    private var mTvGuideContent: TextView? = null
    private var mTvGuideFollowBtn: TextView? = null
    private var mTvGuideCloseBtn: View? = null
    private var mIsAboveCover = false

    // 主播气泡广告相关控件
    private var mPopAdView: View? = null
    private var mAuthorPopAdManager: PlayPageAuthorPopAdManager? = null
    private var mPopAdNeedWait = true

    private var mHasPaused = false

    private var followGuideVisibleChangeListeners = mutableSetOf<OnFollowGuideVisibleChangedListener>()

    override fun onCreate(fragment: BaseFragment2) {
        super.onCreate(fragment)
        mAuthorPopAdManager = PlayPageAuthorPopAdManager(coverComponentsManager, fragment, true) { updateComponentsOrUpdateUI() }
        coverComponentsManager?.addListenerFunction?.addAdsStatusListener(mAuthorPopAdManager)
        coverComponentsManager?.addListenerFunction?.addLifecycleListener(mAuthorPopAdManager)
        coverComponentsManager?.addListenerFunction?.addPlayStatusListener(mAuthorPopAdManager)
        registerService()
    }

    private fun registerService() {
        PlayPageInternalServiceManager.getInstance().registerService(
            IBelowTitleOperationPositionComponentService::class.java,
            object : IBelowTitleOperationPositionComponentService {
            override fun canShowLiveOrMc(): Boolean {
                val topTrackInfoComponentService = PlayPageInternalServiceManager.getInstance()
                    .getService(IAudioPlayPageTrackInfoComponentService::class.java)
                return (topTrackInfoComponentService?.isAnchorViewShow() == true) && (canShowLive() || canShowMc())
            }

            override fun isShowPc(): Boolean {
                return isVisible &&(
                        mMyClubView?.visibility == View.VISIBLE
                        || mLiveRootView?.visibility == View.VISIBLE
                        || mPopAdView?.visibility == View.VISIBLE && mAuthorPopAdManager?.isShowing == true)
            }

            override fun isShowFollowGuide(): Boolean {
                return isVisible && mVFollowGuideRootView?.visibility == View.VISIBLE
            }

            override fun addFollowGuideVisibleChangedListener(listener: OnFollowGuideVisibleChangedListener) {
                followGuideVisibleChangeListeners.add(listener)
            }

            override fun removeFollowGuideVisibleChangedListener(listener: OnFollowGuideVisibleChangedListener) {
                followGuideVisibleChangeListeners.remove(listener)
            }

            override fun canShowInteractiveCard(): Boolean {
                val topTrackInfoComponentService = PlayPageInternalServiceManager.getInstance()
                    .getService(IAudioPlayPageTrackInfoComponentService::class.java)
                if (topTrackInfoComponentService == null || topTrackInfoComponentService?.isContentViewShow() == false) {
                    return true
                }

                return !(canShowLive() || isMcSpeaking() || canShowPopAd() || canShowAlbumGuideSubscribe())
            }
        })
    }

    private fun notifyFollowGuideVisibleChanged(show: Boolean) {
        followGuideVisibleChangeListeners.forEach { it.onChange(show) }
    }

    private val onICommentBoxHideListener = object : ICommentBoxHideListener {
        override fun onHide() {
            updateUI()
        }
    }

    private fun unregisterService() {
        PlayPageInternalServiceManager.getInstance().unRegisterService(IBelowTitleOperationPositionComponentService::class.java)
    }

    override fun initUi() {
        checkChildProtectMode()
        AnchorFollowManage.getSingleton().addFollowListener(mFollowAnchorListener)
        PlayPageInternalServiceManager.getInstance().getService(ICommentBoxService::class.java)?.addCommentBoxHideListener(onICommentBoxHideListener)
    }

    override fun onDestroy() {
        super.onDestroy()
        AnchorFollowManage.getSingleton().removeFollowListener(mFollowAnchorListener)
        unregisterService()
        PlayPageInternalServiceManager.getInstance().getService(ICommentBoxService::class.java)?.removeCommentBoxHideListener(onICommentBoxHideListener)
    }

    override fun getViewStubId(): Int {
        return R.id.main_vs_below_title_operation_position
    }

    override fun show() {
        super.show()
        updateUI()
    }

    override fun needShowThisComponent(soundInfo: PlayingSoundInfo?): Boolean {
        curSoundInfo = soundInfo
        val topTrackInfoComponentService = PlayPageInternalServiceManager.getInstance()
            .getService(IAudioPlayPageTrackInfoComponentService::class.java)
        topTrackInfoComponentService ?: return false
        if (!mHasSetTopTrackInfoShowContentChangeListener) {
            mHasSetTopTrackInfoShowContentChangeListener
            topTrackInfoComponentService.addShowingContentChangeListener(this::handleTopTrackInfoComponentShowContentChange)
            if (topTrackInfoComponentService.isAnchorViewShow()) {
                mAuthorPopAdManager?.authorViewShow()
            } else {
                mAuthorPopAdManager?.authorViewHide()
                AudioPlayPageAnimationManager.noNeedShow(AudioPlayPageAnimationManager.EAnimation.ANCHOR_AD)
            }
        }
        loadMinorDataIfNeeded(soundInfo)
        if (canShowAlbumGuideSubscribe()) {
            return true
        }
        if (!topTrackInfoComponentService.isAnchorViewShow()) {
            return false
        }
        return hasNeedShowContent()
    }

    private fun hasNeedShowContent(): Boolean {
        return canShowMc() || canShowLive() || canShowPopAd() || canShowGuideText() || canShowAlbumGuideSubscribe()
    }

    private fun canShowMc(): Boolean {
        if (isChildProtectMode) {
            return false
        }
        val mcInfo = mPlayPageMinorData?.mcInfo ?: return false
        return (!mcInfo.landingUrl.isNullOrEmpty() && !mcInfo.topic.isNullOrEmpty()) ||
                (!mcInfo.landingScheduleUrl.isNullOrEmpty() && !mcInfo.scheduleTitle.isNullOrEmpty())
    }

    private fun isMcSpeaking(): Boolean {
        if (isChildProtectMode) {
            return false
        }

        return (mPlayPageMinorData?.mcInfo?.roomId ?: 0) > 0
    }

    private fun canShowLive(): Boolean {
        if (isChildProtectMode) {
            return false
        }
        val liveInfo = mPlayPageMinorData?.playLiveInfo ?: return false
        return liveInfo.currentAnchorIsLiving && !liveInfo.iting.isNullOrEmpty() && !liveInfo.logoPic.isNullOrEmpty()
    }

    private fun canShowGuideText(): Boolean {
        return !mPlayPageMinorData?.guideText.isNullOrEmpty() && curSoundInfo?.otherInfo?.isFollowed != true
                && PlayPageInternalServiceManager.getInstance().getService(ICommentBoxService::class.java)?.hasFinish() == true
                && MMKVUtil.getInstance(BarBelowTitleComponent.FOLLOW_GUIDE_MMKV_FILE_NAME).getBoolean(curAlbumId.toString(), true)
    }

    private fun canShowPopAd(): Boolean {
        return mAuthorPopAdManager?.isPopAdCanShow == true
    }

    private fun handleTopTrackInfoComponentShowContentChange(isAnchorViewShow: Boolean) {
        if (isAnchorViewShow xor isVisible) {
            coverComponentsManager?.updateAttachedComponentsOnly()
            if (isAnchorViewShow) {
                showAuthorPopAd()
            } else {
                mAuthorPopAdManager?.authorViewHide()
                AudioPlayPageAnimationManager.noNeedShow(AudioPlayPageAnimationManager.EAnimation.ANCHOR_AD)
            }
        }
    }

    private fun showAuthorPopAd() {
        if (mPopAdNeedWait) {
            AudioPlayPageAnimationManager.readyToStart(
                AudioPlayPageAnimationManager.EAnimation.ANCHOR_AD, Runnable {
                    mPopAdNeedWait = false
                    if (canFragmentUpdateUi()) {
                        mAuthorPopAdManager?.authorViewShow()
                    }
                })
        } else {
            if (canFragmentUpdateUi()) {
                mAuthorPopAdManager?.authorViewShow()
            }
        }
    }

    private fun loadMinorDataIfNeeded(soundInfo: PlayingSoundInfo?) {
        val soundInfoTrackId = soundInfo?.trackInfo?.trackId
        if (mPlayPageMinorData == null || soundInfoTrackId != mPlayPageMinorData?.trackId) {
            mPlayPageMinorData = null
            val trackId = soundInfoTrackId ?: 0
            if (trackId > 0) {
                PlayPageMinorDataManager.getInstance().getData(trackId, mMinorDataCallback, false)
            }
        }
    }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        super.onSoundInfoLoaded(soundInfo)
        mAlbumGuideFirstShowAfterDataLoaded = true
        mMcViewFirstShowAfterDataLoaded = true
        mFollowGuideFirstShowAfterDataLoaded = true
        mLiveViewFirstShowAfterDataLoaded = true
        mHasShowLiveWaveAnimation = false
        mHasShowMcWaveAnimation = false
        mPopAdNeedWait = true
        if (mPlayPageMinorData != null) {
            updateUI()
        }
    }

    override fun onResume() {
        super.onResume()
        updateUI()

        HandlerManager.postOnUIThreadDelay({
            traceMcItemView(2)
            traceFollowGuideViewViewed()
            if (mHasPaused) {
                mHasPaused = false
                traceOnSubscribeShown()
            }
        }, 250)
        val playFragmentService = PlayPageInternalServiceManager.getInstance()
            .getService(IAudioPlayFragmentService::class.java)
        playFragmentService?.registerScrollChangeListener(mOnAudioPlayFragmentScrollStateChangeListener)
    }

    override fun onPause() {
        super.onPause()
        mHasPaused = true
        unregisterReceiverForMc()
    }

    fun setHeightChangeListener(listener: ((height: Int) -> Unit)?) {
        mHeightChangeListener = listener
    }

    private fun updateUI() {
        // 显示优先级是订阅引导>mc>直播>主播气泡广告>关注引导
        val showView: View?
        when {
            // 有声书引导
            canShowAlbumGuideSubscribe() -> {
                if (mAlbumGuideSubscribe == null) {
                    initAlbumGuideViews()
                } else {
                    showAlbumGuideAnimation()
                }
                showView = mAlbumGuideSubscribe
            }
            // my club + 加入
            canShowMc() -> {
                if (mMyClubView == null) {
                    initMcView()
                }
                showView = mMyClubView
            }
            // 直播 + 去围观
            canShowLive() -> {
                if (mLiveRootView == null) {
                    initLiveView()
                }
                showView = mLiveRootView
            }

            // 气泡广告
            canShowPopAd() -> {
                if (mPopAdView == null) {
                    initPopAdView()
                }
                showView = mPopAdView
            }

            // 关注引导
            canShowGuideText() -> {
                if (mVFollowGuideRootView == null) {
                    initFollowGuideView()
                }
                showView = mVFollowGuideRootView
                notifyFollowGuideVisibleChanged(true)
            }
            else -> {
                showView = null
            }
        }

        if (showView != null && showView != mAlbumGuideSubscribe) {
            showView?.visible(View.VISIBLE)
        }

        // 确保其他未显示状态
        showView?.let {
            if (it != mAlbumGuideSubscribe && mAlbumGuideSubscribe?.visibility == View.VISIBLE) {
                hideAlbumGuideView()
            }
            if (it != mLiveRootView && mLiveRootView?.visibility == View.VISIBLE) {
                hideLive()
            }
            if (it != mMyClubView && mMyClubView?.visibility == View.VISIBLE) {
                hideMc()
            }
            if (it != mVFollowGuideRootView) {
                if (mVFollowGuideRootView?.visibility != View.GONE) {
                    notifyFollowGuideVisibleChanged(false)
                    mVFollowGuideRootView.visible(View.GONE)
                }
            }
            if (it != mPopAdView && mPopAdView?.visibility == View.VISIBLE) {
                hidePopAdView()
            }
        }

        // 及时更新数据
        when (showView) {
            mAlbumGuideSubscribe -> updateAlbumGuideViews()
            mLiveRootView -> showLive()
            mMyClubView -> showMc()
            mVFollowGuideRootView -> bindFollowGuideData()
            mPopAdView -> showPopAdView()
        }

        if (showView != mShowingView) {
            mShowingView = showView
            mIvArrow.visible(if (mShowingView == mPopAdView) View.GONE else View.VISIBLE)
            if (mShowingView != mLiveRootView) {
                AudioPlayPageAnimationManager.noNeedShow(AudioPlayPageAnimationManager.EAnimation.LIVE_AVATAR_ANIMATION)
            }
            if (mShowingView != mMyClubView) {
                AudioPlayPageAnimationManager.noNeedShow(AudioPlayPageAnimationManager.EAnimation.MC_AVATAR_ANIMATION)
            }
        }
        mContentView?.post {
            mHeightChangeListener?.invoke(mContentView?.height ?: 0)
        }
    }

    private fun updateComponentsOrUpdateUI() {
        val isTopTrackInfoShowAnchorView = PlayPageInternalServiceManager.getInstance()
            .getService(IAudioPlayPageTrackInfoComponentService::class.java)?.isAnchorViewShow() ?: false
        val shouldBeVisible = isTopTrackInfoShowAnchorView && hasNeedShowContent()
        if ((shouldBeVisible xor isVisible) || canShowAlbumGuideSubscribe()) {
            coverComponentsManager?.updateAttachedComponentsOnly()
        } else {
            updateUI()
        }
    }

    private fun initLiveView() {
        val viewStub: ViewStub? = mContentView?.findViewById(R.id.main_vs_live)
        if (viewStub == null || viewStub.parent == null) return
        this.mLiveRootView = runCatching {
            viewStub.inflate()
        }.getOrNull()
        this.mLiveRootView?.let {
            mIvLiveAvatar = it.findViewById(R.id.main_iv_live_avatar)
            mTvLiveContent = it.findViewById(R.id.main_tv_content)
            mTvLivingTag = it.findViewById(R.id.main_tv_living_tag)
            mTvLiveGoBtn = it.findViewById(R.id.main_tv_live_go_btn)
            mVgLiveAvatarContainer = it.findViewById(R.id.main_vg_live_avatar)
            mLiveWaveView = it.findViewById(R.id.main_avatar_wave_view_live_xav)
            mLiveVideoWaveView = it.findViewById(R.id.main_avatar_wave_view_live_video_xav)
        }
//            mLottieLiveBeam = it.findViewById(R.id.main_lottie_live_beam)
    }

    private fun hideLive() {
        this.mLiveRootView?.visibility = View.GONE
        stopLiveAnim()
    }

    private fun stopLiveAnim() {
        mLiveWaveView?.cancelAnimation()
        mLiveVideoWaveView?.cancelAnimation()
    }

    private fun showLive() {
        if (this.mLiveRootView == null) {
            return
        }
        val liveInfo = mPlayPageMinorData?.playLiveInfo ?: return
        val waveView = if (liveInfo.isVideoLive) {
            mLiveWaveView?.cancelAnimation()
            mLiveWaveView?.visibility = View.INVISIBLE
            mLiveVideoWaveView?.visibility = View.VISIBLE
            mLiveVideoWaveView
        } else {
            mLiveVideoWaveView?.cancelAnimation()
            mLiveVideoWaveView?.visibility = View.INVISIBLE
            mLiveWaveView?.visibility = View.VISIBLE
            mLiveWaveView
        }
        if (mHasShowLiveWaveAnimation) {
            waveView?.playAnimation()
        } else {
            AudioPlayPageAnimationManager.readyToStart(
                AudioPlayPageAnimationManager.EAnimation.LIVE_AVATAR_ANIMATION,
                Runnable {
                    if (canUpdateUi()) {
                        waveView?.playAnimation()
                        mHasShowLiveWaveAnimation = true
                        // 因为水波纹动画是一直动的，所以5秒后就允许其他动效去显示
                        HandlerManager.postOnUIThreadDelay4Kt(5000) {
                            AudioPlayPageAnimationManager.end(AudioPlayPageAnimationManager.EAnimation.LIVE_AVATAR_ANIMATION)
                        }
                    }
                })
        }
        val liveTagName = if (liveInfo.liveName.isNullOrEmpty()) "直播" else liveInfo.liveName
        mTvLivingTag?.text = liveTagName
        mIvLiveAvatar?.let {
            ImageManager.from(context)
                .displayImage(it, liveInfo.logoPic, com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default)
        }

        mTvLiveContent?.text = if (liveTagName == "直播") {
            "${liveInfo.nickname}正在直播中"
        } else {
            liveInfo.recordName
        }
        this.mLiveRootView?.let {
            it.visibility = View.VISIBLE
            it.setOnClickListener(mOnClickListener)
            AutoTraceHelper.bindData(it, AutoTraceHelper.MODULE_DEFAULT, mPlayPageMinorData ?: "")
        }
        val soundInfo = curSoundInfo
        if (mLiveViewFirstShowAfterDataLoaded) {
            mLiveViewFirstShowAfterDataLoaded = false
            XMTraceApi.Trace()
                .setMetaId(24719)
                .setServiceId("slipPage")
                .put("currPage", "newPlay")
                .put("roomId", liveInfo.roomId.toString())
                .put("liveRoomType", liveInfo.bizType.toString())
                .put("liveCategoryId", liveInfo.subBizType)
                .put("anchorId", soundInfo?.userInfo?.uid.toString())
                .put("currTrackId", soundInfo?.trackInfo?.trackId.toString())
                .put("currAlbumId", soundInfo?.albumInfo?.albumId.toString())
                .put("albumTitle", soundInfo?.albumInfo?.title)
                .put("LiveBroadcastState", if (liveInfo.currentAnchorIsLiving) "正在直播" else "推荐直播")
                .put(
                    XmRequestIdManager.XM_REQUEST_ID,
                    XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
                )
                .put(XmRequestIdManager.CONT_TYPE, "play_living_show")
                .put(XmRequestIdManager.CONT_ID, liveInfo.liveRecordId.toString())
                .createTrace()
        }
    }

    private fun registerReceiverForMc() {
        if (mReceiver != null) {
            return
        }
        mReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val bundle: Bundle? =
                    intent.getBundleExtra(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE)
                if (bundle != null && mPlayPageMinorData?.mcInfo != null) {
                    val scheduleId =
                        bundle.getLong(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_SCHEDULE_ID)
                    val roomId =
                        bundle.getLong(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_ROOM_ID)
                    val subscribed =
                        bundle.getBoolean(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_SUBSCRIBED)
                    if (scheduleId == mPlayPageMinorData?.mcInfo?.scheduleId && scheduleId > 0) {
                        if (roomId > 0) {
                            // 已开播
                            val landingUrl =
                                bundle.getString(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_LANDING_URL)
                            val participantsInfo =
                                bundle.getString(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_PARTICIPANTSINFO)
                            mPlayPageMinorData?.mcInfo?.landingUrl = landingUrl
                            mPlayPageMinorData?.mcInfo?.participantsInfo = participantsInfo
                            mPlayPageMinorData?.mcInfo?.roomId = roomId
                            updateMyClubParticipantStatus(participantsInfo)
                        } else {
                            val scheduleStartTime =
                                mPlayPageMinorData?.mcInfo?.scheduleStartTime ?: 0
                            updateScheduleMyClubStatus(subscribed, scheduleStartTime)
                        }
                        mPlayPageMinorData?.mcInfo?.isScheduleStatus = subscribed
                    }
                }
            }
        }
        LocalBroadcastManager.getInstance(context).registerReceiver(
            mReceiver as BroadcastReceiver,
            IntentFilter(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_NOTIFY)
        )
    }

    private fun unregisterReceiverForMc() {
        if (mReceiver != null) {
            LocalBroadcastManager.getInstance(context).unregisterReceiver(mReceiver!!)
            mReceiver = null
        }
    }

    private fun hideMc() {
        this.mMyClubView?.visibility = View.GONE
        mMyclubWaveView?.cancelAnimation()
    }

    private fun showMc() {
        if (this.mMyClubView == null) {
            return
        }

        val mcInfo = mPlayPageMinorData?.mcInfo ?: return
        if (isMcHasRoomId()) {
            mTvMcTopic?.text = mcInfo.topic
            updateMyClubParticipantStatus(mcInfo.participantsInfo)
        } else {
            mTvMcTopic?.text = mcInfo.scheduleTitle
            updateScheduleMyClubStatus(mcInfo.isScheduleStatus, mcInfo.scheduleStartTime)
        }
        ImageManager.from(context)
            .displayImage(mIvMcAvatar, mcInfo.anchorLogo, com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default)
        this.mMyClubView?.let {
            it.visibility = View.VISIBLE
            it.setOnClickListener(mOnClickListener)
        }
        mTvMcJoinBtn?.setOnClickListener(mOnClickListener)
        if (!mHasMcStatViewed) {
            mHasMcStatViewed = true
            if (mMcViewFirstShowAfterDataLoaded) {
                mMcViewFirstShowAfterDataLoaded = false
                traceMcItemView(1)
            }
        }
        mMyclubWaveView?.playAnimation()
        registerReceiverForMc()
    }

    private fun updateScheduleMyClubStatus(orderStatus: Boolean, scheduleStartTime: Long) {
        mTvMcJoinBtn?.text = if (orderStatus) "已预约" else "预约"
        mTvMcJoinBtn?.setTextColor(Color.parseColor(if (orderStatus) "#80ffffff" else "#ffffff"))
        mTvMcJoinBtn?.setBackgroundResource(if (orderStatus) com.ximalaya.ting.android.host.R.drawable.host_bg_26ffffff_radius_100 else R.drawable.main_bg_rect_ff4444_radius_100)
        mTvMcInfo?.let {
            "预告：${TimeUtil.getMyClubScheduleStartTimeText(scheduleStartTime)}".let { text ->
                it.text = text
            }
            it.visibility =
                if (it.text.isNullOrEmpty()) View.GONE else View.VISIBLE
        }
        mMyclubWaveView?.cancelAnimation()
        mMyclubWaveView?.visibility = View.INVISIBLE
        mGroupMcSchedule.visible(View.VISIBLE)
        mGroupMcChatting.visible(View.GONE)
        AudioPlayPageAnimationManager.noNeedShow(AudioPlayPageAnimationManager.EAnimation.MC_AVATAR_ANIMATION)
    }

    private fun updateMyClubParticipantStatus(participantsInfo: String?) {
        mTvMcJoinBtn?.text = "加入"
        mTvMcJoinBtn?.setTextColor(Color.parseColor("#ffffff"))
        mTvMcJoinBtn?.setBackgroundResource(R.drawable.main_bg_rect_ff4444_radius_100)
        mTvMcInfo?.let {
            it.text = participantsInfo
            it.visibility =
                if (participantsInfo.isNullOrEmpty()) View.GONE else View.VISIBLE
        }
        mMyclubWaveView?.visibility = View.VISIBLE
        if (mHasShowMcWaveAnimation) {
            mMyclubWaveView?.playAnimation()
        } else {
            AudioPlayPageAnimationManager.readyToStart(
                AudioPlayPageAnimationManager.EAnimation.MC_AVATAR_ANIMATION,
                Runnable {
                    if (canUpdateUi()) {
                        mHasShowMcWaveAnimation = true
                        mMyclubWaveView?.playAnimation()
                        HandlerManager.postOnUIThreadDelay4Kt(5000) {
                            AudioPlayPageAnimationManager.end(AudioPlayPageAnimationManager.EAnimation.MC_AVATAR_ANIMATION)
                        }
                    }
                })
        }
        mGroupMcSchedule.visible(View.GONE)
        mGroupMcChatting.visible(View.VISIBLE)
    }

    private fun isMcHasRoomId(): Boolean {
        val mcInfo = mPlayPageMinorData?.mcInfo ?: return false
        return mcInfo.roomId > 0
    }

    private fun traceMcItemView(exploreType: Int) {
        if (!isVisible || this.mMyClubView == null || this.mMyClubView?.getGlobalVisibleRect(Rect()) == false) {
            return
        }
        if (curSoundInfo == null) {
            return
        }
        val mcInfo = mPlayPageMinorData?.mcInfo ?: return
        val trace = XMTraceApi.Trace()
            .setMetaId(38509)
            .setServiceId("slipPage")
            .put("anchorId", mcInfo.anchorId.toString())
            .put(
                "status",
                if (mcInfo.roomId > 0) "1" else "0"
            )
            .put("currTrackId", curSoundInfo?.trackInfo?.trackId.toString())
            .put("currPage", "newPlay")
            .put("exploreType", exploreType.toString())
        if (mcInfo.roomId > 0) {
            trace.put("roomId", mcInfo.roomId.toString())
        }
        if (mcInfo.scheduleId > 0) {
            trace.put("scheduleId", mcInfo.scheduleId.toString())
        }
        trace.createTrace()
    }

    private fun initMcView() {
        val viewStub: ViewStub? = findViewById(R.id.main_vs_my_club)
        if (viewStub == null || viewStub.parent == null) return
        this.mMyClubView = runCatching {
            viewStub.inflate()
        }.getOrNull()
        this.mMyClubView?.let {
            mIvMcAvatar = it.findViewById(R.id.main_iv_anchor_avatar)
            mTvMcTopic = it.findViewById(R.id.main_tv_topic)
            mTvMcInfo = it.findViewById(R.id.main_tv_info)
            mTvMcJoinBtn = it.findViewById(R.id.main_tv_join_btn)
            mMyclubWaveView = it.findViewById(R.id.main_avatar_wave_view_myclub_xav)
            mGroupMcChatting = it.findViewById(R.id.main_group_chatting)
            mGroupMcSchedule = it.findViewById(R.id.main_group_schedule)
        }
    }

    private fun initPopAdView() {
        val viewStub: ViewStub? = mContentView?.findViewById(R.id.main_vs_pop_ad)
        if (viewStub == null || viewStub.parent == null) return
        this.mPopAdView = runCatching {
            viewStub.inflate()
        }.getOrNull()
        mAuthorPopAdManager?.initView(context, mPopAdView, mContentView)
    }

    private fun showPopAdView() {
        val padding = 14.dp
        mContentView?.let {
            it.setPadding(padding, it.paddingTop, padding, it.paddingBottom)
        }
    }

    private fun hidePopAdView() {
        val padding = 20.dp
        mContentView?.let {
            it.setPadding(padding, it.paddingTop, padding, it.paddingBottom)
        }
        mPopAdView?.visibility = View.GONE
    }

    private fun initAlbumGuideViews() {
        mVsAlbumGuideSubscribe = mContentView?.findViewById(R.id.main_vs_guide_subscribe)
        if (mVsAlbumGuideSubscribe == null || mVsAlbumGuideSubscribe?.parent == null) {
            return
        }
        mIsAlbumSubscribeHasClicked = false
        mAlbumGuideSubscribe = mVsAlbumGuideSubscribe?.inflate() as BookOpenAnimationLinearLayout?
        mAlbumGuideSubscribe?.apply {
            mGuideSubscribeContent = this.findViewById(R.id.main_tv_guide_subscribe_content)
            mGuideSubscribeBtn = this.findViewById(R.id.main_tv_subscribe_btn)
            mGuideSubscribeClose = this.findViewById(R.id.main_tv_follow_close)
            mGuideSubscribeBtn?.setOnClickListener(mOnClickListener)
            mGuideSubscribeClose?.setOnClickListener(mOnClickListener)
            mGuideSubscribeContent?.setOnClickListener(mOnClickListener)
            this.setOnClickListener(mOnClickListener)
        }
        showAlbumGuideAnimation()
    }

    private var mAnimator: ValueAnimator? = null
    private var hasAnimator = false
    private fun showAlbumGuideAnimation() {
        ViewStatusUtil.setVisible(View.VISIBLE, mAlbumGuideSubscribe)
        if (!hasAnimator) {
            mAnimator = ValueAnimator.ofFloat(0.0f, 0.1f, 0.2f, 0.3f, 0.4f, 0.5f, 0.6f, 0.7f, 0.8f, 0.9f, 1.0f)
            mAnimator?.duration = 500
            mAnimator?.addUpdateListener {
                if (it.animatedValue is Float) {
                    mAlbumGuideSubscribe?.startOpen(it.animatedValue as Float)
                }
            }
            mAnimator?.start()
            hasAnimator = true
        } else {
            mAlbumGuideSubscribe?.startOpen(1.toFloat())
        }
        HandlerManager.postOnUIThreadDelay (mAlbumGuideSubscribeRunnable, 10000)
    }

    private var mAlbumGuideSubscribeRunnable = {
        if (!mIsAlbumSubscribeHasClicked) {
            hideAlbumGuideView()
        }
    }

    /**
     *  和上面的是两种不同样式，这种是关注引导
     */
    private fun canShowAlbumGuideSubscribe(): Boolean {
        return !isToday()
                && !(curSoundInfo?.albumInfo?.isFavorite ?: false)
                && !TextUtils.isEmpty(getAlbumGuideContentInfo())
    }

    private fun getAlbumGuideContentInfo(): String? {
        return mPlayPageMinorData?.guideSubscribeText
    }

    private fun getOldAlbumGuideContentInfo(): String? {
        return mPlayPageMinorData?.oldGuideSubscribeText
    }

    private fun updateAlbumGuideViews() {
        if (!TextUtils.isEmpty(getAlbumGuideContentInfo())) {
            mGuideSubscribeContent?.text =  getAlbumGuideContentInfo()
            if (mAlbumGuideFirstShowAfterDataLoaded) {
                mAlbumGuideFirstShowAfterDataLoaded = false
                traceOnSubscribeShown()
            }
        }
    }

    private fun doClose() {
        hideAlbumGuideView()
        traceOnSubscribeClick("关闭")
    }

    private fun hideAlbumGuideView() {
        hasAnimator = false
        mIsAlbumSubscribeHasClicked = false
        HandlerManager.removeCallbacks(mAlbumGuideSubscribeRunnable)
        if (mAnimator?.isRunning == true) mAnimator?.cancel()
        mIvArrow.visibility = View.GONE
        mAlbumGuideSubscribe?.visibility = View.GONE
        MMKVUtil.getInstance().saveString(BarBelowTitleComponent.KEY_ALBUM_GUIDE_SUBSCRIBE, getToday())
        coverComponentsManager?.updateAttachedComponentsOnly()
    }

    private fun initFollowGuideView() {
        val viewStub: ViewStub = mContentView?.findViewById(R.id.main_vs_follow_guide) ?: return
        mVFollowGuideRootView = viewStub.inflate()
        mVFollowGuideRootView?.let {
            mIvGuideAnchorAvatar = it.findViewById(R.id.main_iv_avatar)
            mTvGuideContent = it.findViewById(R.id.main_tv_guide_content)
            mTvGuideFollowBtn = it.findViewById(R.id.main_tv_follow_btn)
            mTvGuideCloseBtn = it.findViewById(R.id.main_tv_follow_close)
            mTvGuideCloseBtn?.setOnClickListener(mOnClickListener)
            mIvGuideAnchorAvatar?.setOnClickListener(mOnClickListener)
            mTvGuideFollowBtn?.setOnClickListener(mOnClickListener)
            mTvGuideContent?.setOnClickListener(mOnClickListener)
        }
    }

    private fun bindFollowGuideData() {
        ImageManager.from(mContext).displayImage(mIvGuideAnchorAvatar, curSoundInfo?.userInfo?.smallLogo,
            com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default)
        val guideText = mPlayPageMinorData?.guideText
        mTvGuideContent?.text = if (guideText.isNullOrEmpty()) "" else guideText
        if (mFollowGuideFirstShowAfterDataLoaded) {
            mFollowGuideFirstShowAfterDataLoaded = false
            traceFollowGuideViewViewed()
        }
    }

    private fun traceFollowGuideViewViewed() {
        val soundInfo = curSoundInfo ?: return
        if (isVisible && mVFollowGuideRootView?.visibility == View.VISIBLE) {
            // 新声音播放页_引导关注toast  弹框展示
            XMTraceApi.Trace()
                .setMetaId(45378)
                .setServiceId("dialogView")
                .put("currPage", "newPlay")
                .put("currTrackId", soundInfo.trackInfo?.trackId?.toString() ?: "")
                .createTrace()
        }
    }

    private fun onJoinMcRootClick() {
        val mcInfo = mPlayPageMinorData?.mcInfo ?: return
        val url = if (isMcHasRoomId()) mcInfo.landingUrl else mcInfo.landingScheduleUrl
        if (!url.isNullOrEmpty()) {
            ToolUtil.clickUrlAction(mFragment, url, this.mMyClubView)
        }
        val trace = XMTraceApi.Trace()
            .click(38508)
            .put("anchorId", mcInfo.anchorId.toString())
            .put(
                "status",
                if (mcInfo.roomId > 0) "1" else "0"
            )
            .put("currTrackId", curSoundInfo?.trackInfo?.trackId.toString())
            .put("currPage", "newPlay")
        if (mcInfo.roomId > 0) {
            trace.put("roomId", mcInfo.roomId.toString())
        }
        if (mcInfo.scheduleId > 0) {
            trace.put("scheduleId", mcInfo.scheduleId.toString())
        }
        trace.createTrace()
    }

    private fun onJoinMcBtnClick() {
        val mcInfo = mPlayPageMinorData?.mcInfo ?: return
        val isRequestSubscribe = TextUtils.equals(mTvMcJoinBtn?.text, "预约")
        if (isMcHasRoomId()) {
            val url = mcInfo.landingUrl
            if (!url.isNullOrEmpty()) {
                ToolUtil.clickUrlAction(mFragment, url, this.mMyClubView)
            }
        } else {
            val params = HashMap<String, String>()
            params["scheduleId"] = mcInfo.scheduleId.toString()
            params["subscribe"] = isRequestSubscribe.toString()
            MainCommonRequest.getMyClubScheduleResult(
                params,
                object : IDataCallBack<MyClubSubscribeModel> {

                    override fun onSuccess(`object`: MyClubSubscribeModel?) {
                        if (!canUpdateUi()) {
                            return
                        }
                        val isMcHasRoomId: Boolean =
                            `object` != null && !`object`.landingUrl.isNullOrEmpty() && `object`.roomId > 0
                        CustomToast.showSuccessToast(`object`?.message)
                        // 发送广播通知
                        val intent =
                            Intent(ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_NOTIFY)
                        val bundle = Bundle()
                        if (isMcHasRoomId) {
                            // 已开播
                            bundle.putLong(
                                ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_SCHEDULE_ID,
                                mPlayPageMinorData?.mcInfo?.scheduleId!!
                            )
                            `object`?.let {
                                bundle.putLong(
                                    ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_ROOM_ID,
                                    it.roomId
                                )
                                bundle.putString(
                                    ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_LANDING_URL,
                                    it.landingUrl
                                )
                                bundle.putString(
                                    ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_PARTICIPANTSINFO,
                                    it.participantsInfo
                                )
                            }
                        } else {
                            bundle.putLong(
                                ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_SCHEDULE_ID,
                                mPlayPageMinorData?.mcInfo?.scheduleId!!
                            )
                            bundle.putBoolean(
                                ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE_SUBSCRIBED,
                                isRequestSubscribe
                            )
                        }
                        intent.putExtra(
                            ActionConstants.ACTION_MY_CLUB_SCHEDULE_SUBSCRIBE_BUNDLE,
                            bundle
                        )
                        LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext())
                            .sendBroadcast(intent)
                    }

                    override fun onError(code: Int, message: String?) {
                        if (code == 50) {
                            return
                        }
                        if (code == 107) {
                            CustomToast.showFailToast(message)
                        } else {
                            CustomToast.showFailToast(if (isRequestSubscribe) "预约失败" else "取消预约失败")
                        }
                    }
                })
        }
        // 新声音播放页-myClub按钮	  点击事件
        val trace = XMTraceApi.Trace()
            .click(41689)
            .put("anchorId", mcInfo.anchorId.toString())
            .put(
                "status",
                if (mcInfo.roomId > 0) "1" else "0"
            )
            .put("currTrackId", curSoundInfo?.trackInfo?.trackId.toString())
            .put("Item", mTvMcJoinBtn?.text as String?)
            .put("currPage", "newPlay")
        if (mcInfo.roomId > 0) {
            trace.put("roomId", mcInfo.roomId.toString())
        }
        if (mcInfo.scheduleId > 0) {
            trace.put("scheduleId", mcInfo.scheduleId.toString())
        }
        trace.createTrace()
    }

    private fun toAuthorPage(view: View, tipsText: CharSequence = "") {
        val userInfo = curSoundInfo?.userInfo ?: return
        mFragment.startFragment(
            newAnchorSpaceFragment(
                userInfo.uid, -1
            ), view
        )
        trackAnchorClick("", tipsText.toString())
    }

    private fun trackAnchorClick(url: String, tipsText: String) {
        curSoundInfo?.trackInfo?.let {
            // 新声音播放页-主播  点击事件
            XMTraceApi.Trace()
                .click(45712)
                .put("currTrackId", it.trackId.toString())
                .put("anchorId", curAnchorId.toString())
                .put("currPage", "newPlay")
                .put("tipsText", tipsText)
                .put("status", if (CoverComponentsManager.isCoverFullScreen()) "全屏" else "半屏")
                .createTrace()
        }
    }

    private fun closeFollowGuide() {
        mVFollowGuideRootView?.visibility = View.GONE
        mIvArrow?.visibility = View.GONE
        MMKVUtil.getInstance(BarBelowTitleComponent.FOLLOW_GUIDE_MMKV_FILE_NAME).saveBoolean(curAlbumId.toString(), false)
        updateUI()

        val trackFromPlayer = XmPlayerManager.getInstance(context).currSound as? Track
        val traceApi = XMTraceApi.Trace()
            .click(49018)
            .put("trackId", PlayCommentUtil.getCurTrackId(curSoundInfo).toString())
            .put("albumId", PlayCommentUtil.getCurAlbumId(curSoundInfo).toString())
            .put("anchorId", PlayCommentUtil.getCurAnchorId(curSoundInfo).toString())
            .put("currPage", "newPlay")
            .put("moduleName", "引导关注toast")
            .put("albumCategoryId", curSoundInfo?.albumInfo?.categoryId?.toString() ?: "")
            .put("status", if (CoverComponentsManager.isCoverFullScreen()) "全屏" else "半屏")
            .put("style", "顶部悬浮")
        if (trackFromPlayer != null) {
            if (!trackFromPlayer.recTrack.isNullOrEmpty()) {
                traceApi.put("rec_track", trackFromPlayer.recTrack)
            }
            if (!trackFromPlayer.recSrc.isNullOrEmpty()) {
                traceApi.put("rec_src", trackFromPlayer.recSrc)
            }
        }
        traceApi.createTrace()
    }

    private fun followAnchor(moduleName: String) {
        val userInfo = curSoundInfo?.userInfo ?: return
        val otherInfo = curSoundInfo?.otherInfo ?: return
        val isCurrentFollowed = otherInfo.isFollowed
        AnchorFollowManage.followV3WithLoginCheckDeferredAction(
            activity,
            userInfo.uid,
            isCurrentFollowed,
            AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE,
            2,
            object : IDataCallBack<Boolean?> {
                override fun onSuccess(`object`: Boolean?) {
                    if (!canUpdateUi()) {
                        return
                    }
                    if (`object` == null) {
                        return
                    }
                    otherInfo.isFollowed = `object`
                    if (`object`) {
                        CustomToast.showSuccessToast("关注成功")
                    }
                }

                override fun onError(code: Int, message: String) {}
            }, true, "newPlay"
        )
        val trackFromPlayer = XmPlayerManager.getInstance(context).currSound as? Track
        // 新声音播放页-关注  点击事件
        val traceApi = XMTraceApi.Trace()
            .click(39183)
            .put("currTrackId", PlayCommentUtil.getCurTrackId(curSoundInfo).toString())
            .put("currAlbumId", PlayCommentUtil.getCurAlbumId(curSoundInfo).toString())
            .put("anchorId", PlayCommentUtil.getCurAnchorId(curSoundInfo).toString())
            .put("currPage", "newPlay")
            .put("moduleName", moduleName)
            .put("albumCategoryId", curSoundInfo?.albumInfo?.categoryId?.toString() ?: "")
            .put("status", if (CoverComponentsManager.isCoverFullScreen()) "全屏" else "半屏")
            .put("style", if (mIsAboveCover) "顶部悬浮" else "")

        PlayPageInternalServiceManager.getInstance().getService(
            IXPlayFragmentService::class.java
        )?.let {
            traceApi.put("trackForm", if (it.isVideoMode) "video" else "track")
        }

        if (trackFromPlayer != null) {
            if (!trackFromPlayer.recTrack.isNullOrEmpty()) {
                traceApi.put("rec_track", trackFromPlayer.recTrack)
            }
            if (!trackFromPlayer.recSrc.isNullOrEmpty()) {
                traceApi.put("rec_src", trackFromPlayer.recSrc)
            }
        }
        traceApi.createTrace()
    }

    private val mMinorDataCallback by lazy {
        object : IDataCallBack<PlayPageMinorData> {
            override fun onSuccess(data: PlayPageMinorData?) {
                mPlayPageMinorData = data
                if (canFragmentUpdateUi()) {
                    mHasMcStatViewed = false
                    updateComponentsOrUpdateUI()
                }
            }

            override fun onError(code: Int, message: String?) {
                if (canFragmentUpdateUi()) {
                    mHasMcStatViewed = false
                    updateComponentsOrUpdateUI()
                }
            }
        }
    }

    private val mOnAudioPlayFragmentScrollStateChangeListener =
        IScrollListenerCallBack { _, scrollState ->
            if (scrollState == AbsListView.OnScrollListener.SCROLL_STATE_IDLE) {
                traceMcItemView(0)
                traceFollowGuideViewViewed()
                traceOnSubscribeShown()
            }
        }

    private val mOnClickListener by lazy {
        View.OnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@OnClickListener
            }
            when (it) {
                this.mLiveRootView -> {
                    val liveInfo = mPlayPageMinorData?.playLiveInfo ?: return@OnClickListener
                    val iting = liveInfo.iting ?: return@OnClickListener
                    LastAudioPlayListCache.saveAudioPlayListCache(mContext)
                    ToolUtil.clickUrlAction(
                        mFragment, iting,
                        this.mLiveRootView
                    )
                    val soundInfo = curSoundInfo

                    XMTraceApi.Trace()
                        .click(24718)
                        .put("currPage", "newPlay")
                        .put("roomId", liveInfo.roomId.toString())
                        .put("liveRoomType", liveInfo.bizType.toString())
                        .put("liveCategoryId", liveInfo.subBizType)
                        .put("anchorId", soundInfo?.userInfo?.uid.toString())
                        .put("currTrackId", soundInfo?.trackInfo?.trackId.toString())
                        .put("currAlbumId", soundInfo?.albumInfo?.albumId.toString())
                        .put("albumTitle", soundInfo?.albumInfo?.title)
                        .put(
                            "LiveBroadcastState",
                            if (liveInfo.currentAnchorIsLiving) "正在直播" else "推荐直播"
                        )
                        .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                        .createTrace()
                }
                this.mMyClubView -> onJoinMcRootClick()
                mTvMcJoinBtn -> onJoinMcBtnClick()
                mIvGuideAnchorAvatar -> toAuthorPage(it)
                mTvGuideContent -> toAuthorPage(it, mTvGuideContent?.text ?: "")
                mTvGuideFollowBtn -> followAnchor("引导关注toast")
                mTvGuideCloseBtn -> closeFollowGuide()
                mGuideSubscribeBtn -> doSubscribe()
                mGuideSubscribeContent, mAlbumGuideSubscribe -> {
                    traceOnSubscribeBlankClick(true)
                    mIsAlbumSubscribeHasClicked = true
                }
                mGuideSubscribeClose -> doClose()
            }
        }
    }

    private fun doSubscribe() {
        mIsAlbumSubscribeHasClicked = true
        hideAlbumGuideView()
        traceOnSubscribeClick("订阅")
        AlbumEventManage.doCollectActionV3WithLoginCheckDeferredAction(
            AlbumCollectParam(
                "newPlay",
                AnchorCollectManage.SUBSCRIBE_BIZ_TYPE_AUDIO_PLAY_50004,
                AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE,
                8,
                curSoundInfo?.toAlbumM()
            ), mFragment, object : ICollectWithFollowStatusCallback {

            override fun getFollowSubBizType(): Int {
                return 8
            }

            override fun onCollectSuccess(code: Int, isCollected: Boolean) {
                if (isCollected) {
                    curSoundInfo?.albumInfo?.isFavorite = true
                }
            }

            override fun onError() {
            }

            override fun followDialogAction(status: Int) {
                when (status) {
                    ICollectWithFollowStatusCallback.STATUS_SHOW -> {
//                        // 新声音播放页_关注  弹框展示
//                        XMTraceApi.Trace()
//                                .setMetaId(43115)
//                                .setServiceId("dialogView")
//                                .put("trackId", curTrackId.toString())
//                                .put("albumId", curAlbumId.toString())
//                                .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
//                                .put(
//                                        "albumCategoryId",
//                                        curSoundInfo?.albumInfo?.categoryId?.toString()
//                                )
//                                .put("currPage", "newPlay")
//                                .createTrace()
                    }
                    ICollectWithFollowStatusCallback.STATUS_FOLLOW_SUCCESS -> {
                        curSoundInfo?.otherInfo?.isFollowed = true
                        updateUI()
                    }
                }
            }

            override fun getTrackId(): Long {
                return curTrackId
            }

            override fun followDialogBtnClick(clickBtn: Int, extra: Any?) {
                when (clickBtn) {
                    IFollowDialogCallback.CLICK_CLOSE -> {
//                        // 新声音播放页_关注-关闭  弹框控件点击
//                        XMTraceApi.Trace()
//                                .setMetaId(43118)
//                                .setServiceId("dialogClick")
//                                .put("trackId", curTrackId.toString())
//                                .put("albumId", curAlbumId.toString())
//                                .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
//                                .put(
//                                        "albumCategoryId",
//                                        curSoundInfo?.albumInfo?.categoryId?.toString()
//                                )
//                                .put("currPage", "newPlay")
//                                .createTrace()
                    }
                    IFollowDialogCallback.CLICK_FOLLOW -> {
//                        // 新声音播放页_关注-关注  弹框控件点击
//                        XMTraceApi.Trace()
//                                .setMetaId(43116)
//                                .setServiceId("dialogClick")
//                                .put("trackId", curTrackId.toString())
//                                .put("albumId", curAlbumId.toString())
//                                .put(
//                                        "albumCategoryId",
//                                        curSoundInfo?.albumInfo?.categoryId?.toString()
//                                )
//                                .put("item", if (extra == true) "关注" else "取消关注")
//                                .put("currPage", "newPlay")
//                                .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
//                                .createTrace()
                    }
                    IFollowDialogCallback.CLICK_I_KNOW -> {
//                        // 新声音播放页_关注-我知道了  弹框控件点击
//                        XMTraceApi.Trace()
//                                .setMetaId(43117)
//                                .setServiceId("dialogClick")
//                                .put("trackId", curTrackId.toString())
//                                .put("albumId", curAlbumId.toString())
//                                .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
//                                .put(
//                                        "albumCategoryId",
//                                        curSoundInfo?.albumInfo?.categoryId?.toString()
//                                )
//                                .put("currPage", "newPlay")
//                                .createTrace()
                    }
                }
            }
            }, "newPlay"
        )
        // 新声音播放页-订阅  点击事件
        XMTraceApi.Trace()
                .click(43103)
                .put("trackId", curTrackId.toString())
                .put("albumId", curAlbumId.toString())
                .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
                .put("albumCategoryId", curSoundInfo?.albumInfo?.categoryId?.toString())
                .put("currPage", "newPlay")
                .createTrace()
    }

    private val mFollowAnchorListener =
        AnchorFollowManage.IFollowAnchorListener { uid: Long, follow: Boolean ->
            if (curSoundInfo?.userInfo?.uid == uid) {
                curSoundInfo?.otherInfo?.isFollowed = follow
                if (canFragmentUpdateUi()) {
                    updateComponentsOrUpdateUI()
                }
            }
        }

    private fun isToday(): Boolean {
        val lastTime = MMKVUtil.getInstance().getString(BarBelowTitleComponent.KEY_ALBUM_GUIDE_SUBSCRIBE, "")
        return !TextUtils.isEmpty(lastTime) && lastTime == getToday()
    }

    private fun getToday(): String? {
        val calendar = Calendar.getInstance()
        val year = calendar[Calendar.YEAR]
        val month = calendar[Calendar.MONTH] + 1
        val day = calendar[Calendar.DAY_OF_MONTH]
        return "$year-$month-$day"
    }

    private fun traceOnSubscribeClick(item: String) {
        if (mAlbumGuideSubscribe == null || mAlbumGuideSubscribe?.visibility != View.VISIBLE) {
            return
        }
        // 新声音播放页-引导订阅专辑box  点击事件
        XMTraceApi.Trace()
                .click(49822) // 用户点击时上报
                .put("currPage", "newPlay")
                .put("currTrackId", "${curSoundInfo?.trackInfo?.trackId ?: 0}")
                .put("currAlbumId", "${curSoundInfo?.albumInfo?.albumId ?: 0}")
                .put("Item", item) // 订阅｜关闭
                .put("anchorId", "$curAnchorId")
                .createTrace()
    }

    private fun traceOnSubscribeShown() {
        if (!isVisible || mAlbumGuideSubscribe == null || mAlbumGuideSubscribe?.visibility != View.VISIBLE) {
            return
        }
        // 新声音播放页-引导订阅专辑box  控件曝光
        XMTraceApi.Trace()
                .setMetaId(49823)
                .setServiceId("slipPage")
                .put("currPage", "newPlay")
                .put("currTrackId", "${curSoundInfo?.trackInfo?.trackId ?: 0}")
                .put("currAlbumId", "${curSoundInfo?.albumInfo?.albumId ?: 0}")
                .put("Item", "订阅") // 订阅｜关闭
                .put("anchorId", "$curAnchorId")
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
            )
            .put(XmRequestIdManager.CONT_TYPE, "newPlayVideoPlayListItem")
            .put(XmRequestIdManager.CONT_ID, "${curSoundInfo?.trackInfo?.trackId ?: 0}")
                .createTrace()

        XMTraceApi.Trace()
                .setMetaId(49823)
                .setServiceId("slipPage")
                .put("currPage", "newPlay")
                .put("currTrackId", "${curSoundInfo?.trackInfo?.trackId ?: 0}")
                .put("currAlbumId", "${curSoundInfo?.albumInfo?.albumId ?: 0}")
                .put("Item", "关闭") // 订阅｜关闭
                .put("anchorId", "$curAnchorId")
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
            )
            .put(XmRequestIdManager.CONT_TYPE, "newPlayVideoPlayListItem")
            .put(XmRequestIdManager.CONT_ID, "${curSoundInfo?.trackInfo?.trackId ?: 0}")
            .createTrace()
    }

    private fun traceOnSubscribeBlankClick(isGuide: Boolean) {
        // 新声音播放页-引导订阅专辑box-引导语  点击事件
        XMTraceApi.Trace()
            .click(51716) // 用户点击时上报
            .put("currPage", "newPlay")
            .put("currTrackId", "${curSoundInfo?.trackInfo?.trackId ?: 0}")
            .put("currAlbumId", "${curSoundInfo?.albumInfo?.albumId ?: 0}")
            .put("anchorId", "$curAnchorId")
            .put("categoryId", curSoundInfo?.albumInfo?.categoryId?.toString())
            .put("styleItem", if (isGuide) "引导式" else "固定式")
            .put(
                XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(
                    XmRequestPage.PAGE_PLAY_PAGE))
            .createTrace()
    }
}