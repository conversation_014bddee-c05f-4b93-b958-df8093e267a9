package com.ximalaya.ting.android.main.albumModule.album;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.PagerAdapter;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.ViewPagerInScroll;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.AnchorOtherAlbumsModel;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.ui.AlbumTagUtilNew;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adapter.album.item.AbsWoTingAdapter;
import com.ximalaya.ting.android.host.adapter.track.base.IAlbumFraNewDataProvider;
import com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class AlbumRecListFraAnchorOtherAlbumsPart {

    private Context context;
    private AlbumRecListFragment fragment;
    private ListView listView;
    private AnchorOtherAlbumsModel model;
    private long albumId;
    private boolean hasInflate;

    public static boolean init(AlbumRecListFragment fragment, ListView listView) {
        if (listView.getAdapter() != null)
            return false;
        AlbumRecListFraAnchorOtherAlbumsPart part = new AlbumRecListFraAnchorOtherAlbumsPart(fragment, listView);
        return part.hasInflate;
    }

    private AlbumRecListFraAnchorOtherAlbumsPart(AlbumRecListFragment fragment, ListView listView) {
        this.context = fragment.getContext();
        this.fragment = fragment;
        this.listView = listView;
        parse();
        if (model != null && !ToolUtil.isEmptyCollects(model.albums)) {
            initViews();
            hasInflate = true;
        }
    }

    private void parse() {
        Bundle args = fragment.getArguments();
        if (args != null && args.containsKey(AlbumRecListFragment.ARGS_ANCHOR_OTHER_ALBUMS)) {
            albumId = args.getLong(BundleKeyConstants.KEY_ALBUM_ID);
            String jsonString = args.getString(AlbumRecListFragment.ARGS_ANCHOR_OTHER_ALBUMS);
            if(TextUtils.isEmpty(jsonString))
                return;
            try {
                JSONObject jsonObject = new JSONObject(jsonString);
                model = AnchorOtherAlbumsModel.parse(jsonObject);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void initViews() {
        LinearLayout headerRoot = new LinearLayout(context);
        headerRoot.setOrientation(LinearLayout.VERTICAL);
        AbsListView.LayoutParams headerLP = new AbsListView.LayoutParams(
                AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT
        );
        headerRoot.setLayoutParams(headerLP);
        listView.addHeaderView(headerRoot);

        TextView titleView = new TextView(context);
        titleView.setText("主播其他专辑");
        titleView.setTextSize(16);
        titleView.setTextColor(context.getResources().getColor(R.color.main_color_111111_cfcfcf));
        int padding = BaseUtil.dp2px(context, 15);
        titleView.setPadding(padding, padding, padding, 0);
        ViewGroup.LayoutParams titleLP = new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT
        );
        headerRoot.addView(titleView, titleLP);

        ViewPagerInScroll viewPager = new ViewPagerInScroll(context);
        viewPager.setClipChildren(false);
        viewPager.setPadding(padding, 0, padding, 0);
        viewPager.setPageMargin(BaseUtil.dp2px(context, 20));
        viewPager.setDisallowInterceptTouchEventView(headerRoot, true);
        ViewGroup.LayoutParams pagerLP = new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, BaseUtil.dp2px(context, 95)
        );
        viewPager.setAdapter(new AlbumPagerAdapter(model));
        headerRoot.addView(viewPager, pagerLP);

        new UserTracking().setSrcPage("album").setSrcPageId(albumId).setModuleType("主播其他专辑").setID("5316")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
    }

    class AlbumPagerAdapter extends PagerAdapter {
        List<Object> dataList;
        LayoutInflater inflater;

        public AlbumPagerAdapter(AnchorOtherAlbumsModel model) {
            inflater = LayoutInflater.from(context);
            dataList = new ArrayList<>(model.albums.size() + 1);
            dataList.addAll(model.albums);
            if (model.isShowMoreButton) {
                dataList.add(Boolean.TRUE);
            }
        }

        @Override
        public int getCount() {
            return dataList.size();
        }

        @Override
        public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
            return view == object;
        }

        @NonNull
        @Override
        public Object instantiateItem(@NonNull ViewGroup container, int position) {
            View pagerItemView;
            final Object data = dataList.get(position);
            if (data instanceof AlbumM) {
                final AlbumM album = (AlbumM) data;
                pagerItemView = inflater.inflate(R.layout.main_album_rec_list_anchor_other_album_item, container, false);
                ImageView albumCover = pagerItemView.findViewById(R.id.main_iv_album_cover);
                ImageView albumLabel = pagerItemView.findViewById(R.id.main_iv_album_pay_cover_tag);
                TextView albumTitle = pagerItemView.findViewById(R.id.main_tv_album_title);
                TextView albumSubtitle = pagerItemView.findViewById(R.id.main_tv_album_subtitle);
                TextView playTimes = pagerItemView.findViewById(R.id.main_tv_play_times);
                TextView trackCount = pagerItemView.findViewById(R.id.main_tv_track_count);

                ImageManager.from(context).displayImage(albumCover, album.getValidCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album);
                AlbumTagUtilNew.getInstance().loadImage(albumLabel, album.getAlbumSubscriptValue());
                albumTitle.setText(AbsWoTingAdapter.getRichTitle(context, album, null));
                albumSubtitle.setText(album.getAlbumIntro());
                playTimes.setText(StringUtil.getFriendlyNumStr(album.getPlayCount()));
                trackCount.setText(StringUtil.getFriendlyNumStr(album.getIncludeTrackCount()));
                pagerItemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        AlbumEventManage.startMatchAlbumFragment(album, AlbumEventManage.FROM_OTHER,
                                ConstantsOpenSdk.PLAY_FROM_OTHER, album.getRecommentSrc(),
                                album.getRecTrack(), -1, fragment.getActivity());

                        new UserTracking("album", "album").setSrcPageId(albumId).setSrcModule("主播其他专辑").setItemId(album.getId())
                                .setID("5317").statIting(UserTracking.APP_NAME_EVENT, UserTracking.SERVICE_ID_ALBUM_PAGE);
                    }
                });
            } else {
                TextView moreItemView = new TextView(context);
                moreItemView.setText("查看更多");
                moreItemView.setTextSize(12);
                moreItemView.setGravity(Gravity.CENTER);
                moreItemView.setBackgroundResource(R.drawable.main_bg_solid_f3f4f5);
                moreItemView.setTextColor(context.getResources().getColor(R.color.main_color_666666_888888));
                ViewGroup.LayoutParams lp = new ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
                );
                moreItemView.setLayoutParams(lp);
                moreItemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Fragment parentFra = fragment.getParentFragment();
                        if (parentFra != null && parentFra instanceof IAlbumFraNewDataProvider) {
                            IAlbumFraNewDataProvider dataProvider = (IAlbumFraNewDataProvider) parentFra;
                            AlbumM albumM = dataProvider.getAlbumM();
                            if (albumM != null && albumM.getAnnouncer() != null && albumM.getAnnouncer().getAnnouncerId() > 0) {
                                AlbumListFragment albumList = AlbumListFragment
                                        .newInstanceByUid(albumM.getAnnouncer().getAnnouncerId(), -1);
                                fragment.startFragment(albumList);

                                new UserTracking("album","button").setSrcPageId(albumId).setSrcModule("主播其他专辑").setItemId("more").setID("5318")
                                        .statIting(UserTracking.APP_NAME_EVENT, UserTracking.SERVICE_ID_ALBUM_PAGE);
                            }
                        }
                    }
                });
                pagerItemView = moreItemView;
            }
            container.addView(pagerItemView);
            return pagerItemView;
        }

        @Override
        public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
            container.removeView((View) object);
        }

        @Override
        public float getPageWidth(int position) {
            if (dataList.size() == 1) {
                return 1f;
            }

            Object data = dataList.get(position);
            if (data instanceof AlbumM) {
                return 0.6f;
            } else {
                return 0.27f;
            }
        }
    }
}
