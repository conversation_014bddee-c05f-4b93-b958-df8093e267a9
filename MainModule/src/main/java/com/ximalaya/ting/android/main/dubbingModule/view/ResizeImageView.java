package com.ximalaya.ting.android.main.dubbingModule.view;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.widget.ImageView;

/**
 * Created by le.xin on 2018/8/10.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17097256298
 */
public class ResizeImageView extends ImageView {
    public ResizeImageView(Context context) {
        super(context);
    }

    public ResizeImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public ResizeImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if(getLayoutParams() != null) {
            if(getLayoutParams().width > 0) {
                widthMeasureSpec = MeasureSpec.makeMeasureSpec(getLayoutParams().width , MeasureSpec.getMode(widthMeasureSpec));
            } else if(getLayoutParams().height > 0) {
                heightMeasureSpec = MeasureSpec.makeMeasureSpec(getLayoutParams().height ,MeasureSpec.getMode(heightMeasureSpec));
            }
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }
}
