package com.ximalaya.ting.android.main.anchorModule.v5.works.albumCollection

import android.view.View
import android.widget.TextView
import com.airbnb.epoxy.SimpleEpoxyModel
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil

class CollectionTitleModel(
    private val titleText: String,
    private val hasMore: Boolean,
    private val clickListener: View.OnClickListener?
): SimpleEpoxyModel(R.layout.main_layout_anchor_collection_title) {

    override fun bind(view: View) {
        super.bind(view)

        view.findViewById<TextView>(R.id.main_tv_title).text = titleText
        view.findViewById<View>(R.id.main_tv_more).also {
            if (hasMore) {
                ViewStatusUtil.setVisible(View.VISIBLE, it)
            } else {
                ViewStatusUtil.setVisible(View.GONE, it)
            }
            it.setOnClickListener(clickListener)
        }
    }

    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as CollectionTitleModel

        if (titleText != other.titleText) return false
        if (hasMore != other.hasMore) return false

        return true
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + titleText.hashCode()
        result = 31 * result + hasMore.hashCode()
        return result
    }
}