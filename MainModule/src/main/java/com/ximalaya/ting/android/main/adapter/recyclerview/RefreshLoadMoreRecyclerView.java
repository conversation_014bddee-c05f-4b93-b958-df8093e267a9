package com.ximalaya.ting.android.main.adapter.recyclerview;

import android.content.Context;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.IntDef;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.collection.SparseArrayCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.OrientationHelper;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.main.R;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by caiyu on 2017/12/26.
 */

public class RefreshLoadMoreRecyclerView extends RecyclerView {

    public static final int NO_VIEW = 0;
    public static final int FOOTER_TYPE_LOADING = 1;
    public static final int FOOTER_TYPE_LOAD_MORE = 2;
    public static final int FOOTER_TYPE_NO_MORE = 3;
    public static final int HEADER_TYPE_LOADING = 4;
    public static final int HEADER_TYPE_LOAD_MORE = 5;
    public static final int EMPTY_TYPE_NO_CONTENT = -1;
    public static final int EMPTY_TYPE_NO_NETWORK = -2;
    public static final int EMPTY_TYPE_SERVER_ERROR = -3;

    public static final int NO_LOAD_TYPE = 0;
    public static final int LOAD_TYPE_REFRESH = 1;
    public static final int LOAD_TYPE_MORE_NEXT = 2;
    public static final int LOAD_TYPE_MORE_PRE = 3;

    public static final int AUTO_LOAD_MORE = 0;
    public static final int CLICK_LOAD_MORE = 1;

    @IntDef({NO_VIEW, FOOTER_TYPE_LOADING, FOOTER_TYPE_LOAD_MORE, FOOTER_TYPE_NO_MORE,
            HEADER_TYPE_LOADING, HEADER_TYPE_LOAD_MORE,
            EMPTY_TYPE_NO_CONTENT, EMPTY_TYPE_NO_NETWORK, EMPTY_TYPE_SERVER_ERROR})
    @Retention(RetentionPolicy.SOURCE)
    public @interface ExtraViewType {
    }

    @IntDef({NO_LOAD_TYPE, LOAD_TYPE_REFRESH, LOAD_TYPE_MORE_PRE, LOAD_TYPE_MORE_NEXT})
    @Retention(RetentionPolicy.SOURCE)
    public @interface LoadType {
    }

    @IntDef({AUTO_LOAD_MORE, CLICK_LOAD_MORE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface TriggerLoadMoreType {
    }

    /**
     * 下拉刷新时联动处理的组件
     */
    private SwipeRefreshLayout mSwipeRefreshLayout;

    /**
     * 用户设置进来的不含footer,header的Adapter，作为setAdapter方法的参数
     *
     * @see #setAdapter(Adapter)
     */
    private BaseRecyclerAdapter mInnerAdapter;

    /**
     * 经过包装的包含footer,header的Adapter，通过getAdapter方法获取的是此Adapter
     *
     * @see #getAdapter()
     */
    private RecyclerAdapterWrapper mAdapterWrapper;

    private LinearLayoutManager mLayoutManager;

    private @ExtraViewType
    int mCurrentFooterType = NO_VIEW;
    private @ExtraViewType
    int mCurrentHeaderType = NO_VIEW;

    /**
     * RecyclerView的footer容器，用于包含footer,empty类型的子View
     */
    private final FrameLayout mFooterContainer;

    /**
     * RecyclerView的header容器，用于包含header类型的子View
     */
    private final FrameLayout mHeaderContainer;

    /**
     * 缓存对应各个footer,header,empty类型的子View
     */
    private final SparseArrayCompat<View> mViewsCache = new SparseArrayCompat<>();

    private boolean mSupportLoadNextPage = false;
    private @TriggerLoadMoreType int mTriggerLoadMoreType = AUTO_LOAD_MORE;
    private boolean mSupportLoadPrePage = false;
    private boolean mSupportLoadMoreInAdvance = true;
    private OnRefreshListener mOnRefreshListener;
    private OnLoadNextPageListener mOnLoadNextPageListener;
    private OnLoadPrePageListener mOnLoadPrePageListener;
    private final SparseArray<OnExtraViewCreatedListener> mExtraViewCreatedListeners = new SparseArray<>();
    private EmptyCondition mEmptyCondition;

    /**
     * 当前是否处于加载状态
     */
    private boolean mIsLoading = false;

    /**
     * 用于记录当前loading状态处理完成后，是否还有下一次刷新动作，以及下一次动作是否展示loading圈
     */
    private @LoadType int mNextLoadType = NO_LOAD_TYPE;
    private boolean mNextShowLoading;

    /**
     * 是否有下一页
     */
    private boolean mHasNextPage = false;

    /**
     * 是否有上一页
     */
    private boolean mHasPrePage = false;

    /**
     * 当前的加载类型
     */
    private @LoadType
    int mCurLoadType = NO_LOAD_TYPE;

    private boolean isLoadMoreEnabled = false;
    private boolean isLastTouchAtBottom = false;
    private boolean isLastTouchAtTop = false;

    private int mLoadingHeaderLayoutRes = R.layout.main_footer_loading;
    private int mLoadingFooterLayoutRes = R.layout.main_footer_loading;
    private int mLoadMoreHeaderLayoutRes = R.layout.main_view_pull_down_load_more;
    private int mLoadMoreFooterLayoutRes = R.layout.main_footer_load_more_hint;
    private int mNoMoreFooterLayoutRes = R.layout.main_footer_no_more_hint;
    private int mNoContentFooterLayoutRes = 0;
    private int mNoNetworkFooterLayoutRes = 0;
    private String mNoContentTitle;
    private String mNoContentBtnText;
    private Runnable mNoContentClickAction;
    private boolean mShouldAlwaysShowNoMore = false;
    
    private boolean mAutoExpandNoContentLayout = true;

    private int mNotDisplayedItemCountWhenLoadMore = 6;   //提前加载下一页时，未显示的item最大数量

    public RefreshLoadMoreRecyclerView(Context context) {
        this(context, null);
    }

    public RefreshLoadMoreRecyclerView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RefreshLoadMoreRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mFooterContainer = new FrameLayout(getContext());
        mFooterContainer.setLayoutParams(new LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        mHeaderContainer = new FrameLayout(getContext());
        mHeaderContainer.setLayoutParams(new LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
    }

    @Override
    public void setLayoutManager(LayoutManager layoutManager) {
        super.setLayoutManager(layoutManager);
        if (layoutManager instanceof LinearLayoutManager) {
            mLayoutManager = (LinearLayoutManager) layoutManager;
        } else {
            mLayoutManager = null;
        }
    }

    @Override
    public void setAdapter(Adapter adapter) {
        if (adapter instanceof BaseRecyclerAdapter) {
            if (getLayoutManager() == null) {
                setLayoutManager(mLayoutManager = new LinearLayoutManager(getContext()));
            }
            mInnerAdapter = (BaseRecyclerAdapter) adapter;
            mAdapterWrapper = new RecyclerAdapterWrapper(mInnerAdapter);
            mAdapterWrapper.addFooterView(mFooterContainer);
            super.setAdapter(mAdapterWrapper);
            setItemAnimator(null);
            addOnScrollListener(mRecyclerScrollListener);
        } else {
            super.setAdapter(adapter);
        }
    }

    public void bindSwipeRefreshLayout(@Nullable SwipeRefreshLayout swipeRefreshLayout) {
        mSwipeRefreshLayout = swipeRefreshLayout;
        if (mSwipeRefreshLayout != null) {
            mSwipeRefreshLayout.setColorSchemeResources(com.ximalaya.ting.android.host.R.color.host_color_xmRed);
            if (mOnRefreshListener != null) {
                mSwipeRefreshLayout.setOnRefreshListener(() -> performRefresh(false));
            }
        }
    }

    public void setOnRefreshListener(@Nullable OnRefreshListener onRefreshListener) {
        mOnRefreshListener = onRefreshListener;
        if (mSwipeRefreshLayout != null) {
            mSwipeRefreshLayout.setOnRefreshListener(() -> {
                performRefresh(false);
            });
        }
    }

    public void setOnLoadNextPageListener(@Nullable OnLoadNextPageListener onLoadNextPageListener) {
        if (!mSupportLoadNextPage) {
            setSupportLoadNextPage(true);
        }
        mOnLoadNextPageListener = onLoadNextPageListener;
    }

    public void setOnLoadPrePageListener(@Nullable OnLoadPrePageListener onLoadPrePageListener) {
        if (!mSupportLoadPrePage) {
            setSupportLoadPrePage(true);
        }
        mOnLoadPrePageListener = onLoadPrePageListener;
    }

    /**
     * 设置是否支持加载下一页，如果需要临时禁用加载下一页功能，则调用此方法。
     * 初始化时调setOnLoadNextPageListener即可，无需此方法。
     *
     * @param supportLoadNextPage
     * @see #setOnLoadNextPageListener(OnLoadNextPageListener)
     */
    public void setSupportLoadNextPage(boolean supportLoadNextPage) {
        mSupportLoadNextPage = supportLoadNextPage;
    }

    public void setSupportLoadPrePage(boolean canLoadPrePage) {
        mSupportLoadPrePage = canLoadPrePage;
        if (!mSupportLoadPrePage) {
            if (mAdapterWrapper != null) {
                mAdapterWrapper.removeHeaderView(mHeaderContainer);
            }
        }
    }

    public void setTriggerLoadMoreType(@TriggerLoadMoreType int type) {
        mTriggerLoadMoreType = type;
        mLoadMoreFooterLayoutRes = (type == AUTO_LOAD_MORE ?
                R.layout.main_footer_load_more_hint :
                R.layout.main_footer_click_load_more_standard);
    }

    public void setEmptyCondition(EmptyCondition emptyCondition) {
        mEmptyCondition = emptyCondition;
    }

    public void setSupportLoadMoreInAdvance(boolean supportLoadMoreInAdvance) {
        mSupportLoadMoreInAdvance = supportLoadMoreInAdvance;
    }

    public void setExtraViewCreatedListener(@ExtraViewType int type, OnExtraViewCreatedListener listener) {
        mExtraViewCreatedListeners.put(type, listener);
    }

    public <E> void notifyLoadSuccess(List<E> backedData) {
        notifyLoadSuccess(backedData, hasMoreNext());
    }

    public <E> void notifyLoadSuccess(List<E> backedData, boolean hasNext) {
        notifyLoadSuccess(backedData, hasNext, hasMorePre());
    }

    /**
     * 数据请求成功后通知刷新UI：取消Loading状态，并且自动把数据设置到合适的位置
     *
     * @param backedData 返回的列表数据
     * @param hasNextPage    列表底部是否还有更多未加载
     * @param hasPrePage     列表头部是否还有更多未加载
     * @return 数据是否成功设置到RecyclerView
     */
    @SuppressWarnings("unchecked")
    public <E> boolean notifyLoadSuccess(List<E> backedData, boolean hasNextPage, boolean hasPrePage) {
        if (!mIsLoading || mAdapterWrapper == null || mInnerAdapter == null) {
            return false;
        }
        if (backedData == null) {
            stopCurrentLoading();
            return false;
        }
        mIsLoading = false;

        if (getScrollState() == SCROLL_STATE_DRAGGING && !isLoadMoreEnabled) {
            isLoadMoreEnabled = true;
        }

        if (mCurLoadType == LOAD_TYPE_REFRESH) {   //下拉刷新
            if (mNextLoadType != LOAD_TYPE_REFRESH || !mNextShowLoading) {
                if (mSwipeRefreshLayout != null) {
                    mSwipeRefreshLayout.setRefreshing(false);
                }
            }
            if (backedData != mInnerAdapter.getData()) {
                mInnerAdapter.setData(backedData);
            } else {    //如果是在子类直接操作mData对象，则无需对数据进行处理，直接刷新
                mAdapterWrapper.notifyDataSetChanged();
            }
        } else if (mCurLoadType == LOAD_TYPE_MORE_NEXT) {    //上拉加载更多
            if (getScrollState() == SCROLL_STATE_SETTLING && isFooterVisible()) {
                stopScroll();
            }
            mInnerAdapter.addData(backedData);
        } else if (mCurLoadType == LOAD_TYPE_MORE_PRE) {  //下拉加载更多
            if (getScrollState() == SCROLL_STATE_SETTLING && isHeaderVisible()) {
                stopScroll();
            }
            if (backedData != mInnerAdapter.getData()) {  //如果是在子类直接操作mData对象，则无需再次处理
                int curHeadOffset = -1;
                if (getChildCount() >= 2 && getChildAt(0) == mHeaderContainer) {
                    curHeadOffset = getChildAt(1).getTop();
                }
                mInnerAdapter.addData(0, backedData);
                if (curHeadOffset >= 0) {
                    if (getLayoutManager() instanceof LinearLayoutManager) {
                        ((LinearLayoutManager) getLayoutManager()).scrollToPositionWithOffset(
                                mAdapterWrapper.getHeadersCount() + backedData.size(), curHeadOffset
                        );
                    } else {
                        getLayoutManager().scrollToPosition(
                                mAdapterWrapper.getHeadersCount() + backedData.size());
                    }
                }
            } else {
                mAdapterWrapper.notifyDataSetChanged();
            }
        }

        if (isEmptyList()) {
            showEmptyView(EMPTY_TYPE_NO_CONTENT);
        } else {
            updateIfHasMore(hasNextPage, hasPrePage, mShouldAlwaysShowNoMore
                    || mCurLoadType == LOAD_TYPE_MORE_NEXT);
        }
        performNextLoadActionIfNeed();
        return true;
    }

    public void notifyLoadError(int errorType) {
        notifyLoadError(errorType,"");
    }

    public void notifyLoadError(int errorType, String errorMessage) {
        notifyLoadError(errorType, errorMessage, false);
    }

    /**
     * 通知加载错误，取消loading状态，并且显示错误提示
     *
     * @param errorType 错误类型
     */
    public void notifyLoadError(int errorType, String errorMessage, boolean clearDataOnRefresh) {
        if (!mIsLoading) {
            return;
        }
        mIsLoading = false;

        if (getScrollState() == SCROLL_STATE_DRAGGING && !isLoadMoreEnabled) {
            isLoadMoreEnabled = true;
        }

        if (mCurLoadType == LOAD_TYPE_REFRESH) {   //下拉刷新
            if (mNextLoadType != LOAD_TYPE_REFRESH || !mNextShowLoading) {
                if (mSwipeRefreshLayout != null) {
                    mSwipeRefreshLayout.setRefreshing(false);
                }
            }
            if (clearDataOnRefresh && !mInnerAdapter.getData().isEmpty()) {
                mInnerAdapter.clearData();
            }
        }
        if (isEmptyList()) {  //列表没有数据，显示emptyView
            showEmptyView(EMPTY_TYPE_NO_NETWORK);
        } else {    //列表有数据，显示Toast
            CustomToast.showFailToast("网络请求失败");
            if (mCurLoadType == LOAD_TYPE_MORE_NEXT) {
                showFooterView(FOOTER_TYPE_LOAD_MORE);
            } else if (mCurLoadType == LOAD_TYPE_MORE_PRE) {
                showHeaderView(HEADER_TYPE_LOAD_MORE);
            }
        }
        performNextLoadActionIfNeed();
    }

    /**
     * 直接结束所有请求，恢复到loading前的状态，不进行数据设置，也不通知错误
     */
    public void stopLoading() {
        if (!mIsLoading) {
            return;
        }
        mIsLoading = false;

        if (getScrollState() == SCROLL_STATE_DRAGGING && !isLoadMoreEnabled) {
            isLoadMoreEnabled = true;
        }

        if (mCurLoadType == LOAD_TYPE_REFRESH || mNextLoadType == LOAD_TYPE_REFRESH) {
            if (mSwipeRefreshLayout != null) {
                mSwipeRefreshLayout.setRefreshing(false);
            }
        }
        if (mCurLoadType == LOAD_TYPE_MORE_NEXT || mNextLoadType == LOAD_TYPE_MORE_NEXT) {
            showFooterView(FOOTER_TYPE_LOAD_MORE);
        }
        if (mCurLoadType == LOAD_TYPE_MORE_PRE || mNextLoadType == LOAD_TYPE_MORE_PRE) {
            showHeaderView(HEADER_TYPE_LOAD_MORE);
        }
        mNextLoadType = NO_LOAD_TYPE;
        mNextShowLoading = false;
    }

    /**
     * 仅结束当前请求，恢复到loading前的状态，不进行数据设置，也不通知错误
     */
    public void stopCurrentLoading() {
        if (!mIsLoading) {
            return;
        }
        mIsLoading = false;

        if (getScrollState() == SCROLL_STATE_DRAGGING && !isLoadMoreEnabled) {
            isLoadMoreEnabled = true;
        }

        if (mCurLoadType == LOAD_TYPE_REFRESH) {   //下拉刷新
            if (mNextLoadType != LOAD_TYPE_REFRESH || !mNextShowLoading) {
                if (mSwipeRefreshLayout != null) {
                    mSwipeRefreshLayout.setRefreshing(false);
                }
            }
        } else {
            if (!isEmptyList()) {   //列表有数据，更新header/footer
                if (mCurLoadType == LOAD_TYPE_MORE_NEXT) {
                    if (mNextLoadType != LOAD_TYPE_MORE_NEXT || !mNextShowLoading) {
                        showFooterView(FOOTER_TYPE_LOAD_MORE);
                    }
                } else if (mCurLoadType == LOAD_TYPE_MORE_PRE) {
                    if (mNextLoadType != LOAD_TYPE_MORE_PRE || !mNextShowLoading) {
                        showHeaderView(HEADER_TYPE_LOAD_MORE);
                    }
                }
            }
        }
        performNextLoadActionIfNeed();
    }

    /**
     * 模拟下拉刷新操作
     */
    public void performRefresh() {
        performRefresh(true, false);
    }

    public void performRefresh(boolean isShowLoading) {
        performRefresh(isShowLoading, false);
    }

    public void performRefresh(boolean isShowLoading, boolean clearDataFirst) {
        if (mOnRefreshListener == null) {
            return;
        }
        if (mSwipeRefreshLayout != null && isShowLoading) {
            mSwipeRefreshLayout.setEnabled(true);
            mSwipeRefreshLayout.setRefreshing(true);
        }
        if (!mIsLoading) {
            mIsLoading = true;
            mCurLoadType = LOAD_TYPE_REFRESH;
            if (clearDataFirst) {
                hideHeaderView();
                hideFooterView();
                mInnerAdapter.clearData();
            }
            mOnRefreshListener.onRefresh();
        } else {
            mNextLoadType = LOAD_TYPE_REFRESH;
            mNextShowLoading = isShowLoading;
        }
    }

    public void performLoadNextPage(boolean isShowLoading) {
        if (!mSupportLoadNextPage) {
            return;
        }
        if (isShowLoading) {
            showFooterView(FOOTER_TYPE_LOADING);
        }
        if (!mIsLoading) {
            if (mHasNextPage) {
                mIsLoading = true;
                mCurLoadType = LOAD_TYPE_MORE_NEXT;
                if (mOnLoadNextPageListener != null) {
                    mOnLoadNextPageListener.onLoadNextPage();
                }
            }
        } else {
            mNextLoadType = LOAD_TYPE_MORE_NEXT;
            mNextShowLoading = isShowLoading;
        }
    }

    public void performLoadPrePage(boolean isShowLoading) {
        if (!mSupportLoadPrePage) {
            return;
        }
        if (isShowLoading) {
            showHeaderView(HEADER_TYPE_LOADING);
        }
        if (!mIsLoading) {
            if (mHasPrePage) {
                mIsLoading = true;
                mCurLoadType = LOAD_TYPE_MORE_PRE;
                if (mOnLoadPrePageListener != null) {
                    mOnLoadPrePageListener.onLoadPrePage();
                }
            }
        } else {
            mNextLoadType = LOAD_TYPE_MORE_PRE;
            mNextShowLoading = isShowLoading;
        }
    }

    private void performNextLoadActionIfNeed() {
        if (mNextLoadType == NO_LOAD_TYPE) {
            return;
        }
        int nextLoadType = mNextLoadType;
        mNextLoadType = NO_LOAD_TYPE;
        mNextShowLoading = false;
        switch (nextLoadType) {
            case LOAD_TYPE_REFRESH:
                performRefresh(false);
                break;
            case LOAD_TYPE_MORE_NEXT:
                performLoadNextPage(false);
                break;
            case LOAD_TYPE_MORE_PRE:
                performLoadPrePage(false);
                break;
            default:
                break;
        }
    }

    public <E> void setNewData(List<E> newData) {
        setNewData(newData, hasMoreNext());
    }

    public <E> void setNewData(List<E> newData, boolean hasNextPage) {
        setNewData(newData, hasNextPage, hasMorePre());
    }

    /**
     * 不通过耗时的请求，直接设置新数据，比如从本地缓存取
     */
    @SuppressWarnings("unchecked")
    public <E> void setNewData(List<E> newData, boolean hasNextPage, boolean hasPrePage) {
        if (mInnerAdapter == null || mAdapterWrapper == null || newData == null) {
            return;
        }
        stopLoading();
        mInnerAdapter.setData(newData);

        if (isEmptyList()) {
            showEmptyView(EMPTY_TYPE_NO_CONTENT);
        } else {
            updateIfHasMore(hasNextPage, hasPrePage, mShouldAlwaysShowNoMore);
        }
    }

    public <E> void addData(int index, E newData) {
        if (mInnerAdapter == null || mAdapterWrapper == null || mIsLoading) {
            return;
        }
        boolean isSuccess = mInnerAdapter.addData(index, newData);
        if (isSuccess && mInnerAdapter.getData().size() == 1) {
            updateIfHasMore(false, false, false);
        }
    }

    public <E> void addData(int index, List<E> newData, boolean hasNextPage, boolean hasPrePage) {
        if (mInnerAdapter == null || mAdapterWrapper == null || mIsLoading) {
            return;
        }
        boolean isSuccess = mInnerAdapter.addData(index, newData);
        if (isSuccess) {
            updateIfHasMore(hasNextPage, hasPrePage, false);
        }
    }

    public <E> E removeData(int position) {
        if (mInnerAdapter == null || mAdapterWrapper == null || mIsLoading) {
            return null;
        }
        Object dataRemoved = mInnerAdapter.removeData(position);
        if (dataRemoved != null) {
            if (isEmptyList()) {
                showEmptyView(EMPTY_TYPE_NO_CONTENT);
            }
        }
        return (E) dataRemoved;
    }

    public void updateIfHasMore(boolean hasNextPage, boolean hasPrePage, boolean canShowNoMoreHint) {
        if (mSupportLoadNextPage) {
            mHasNextPage = hasNextPage;
            if (mHasNextPage) {
                showFooterView(FOOTER_TYPE_LOAD_MORE);
            } else {
                if (canShowNoMoreHint) {
                    showFooterView(FOOTER_TYPE_NO_MORE);
                } else {
                    hideFooterView();
                }
            }
        } else {
            hideFooterView();
        }
        if (mSupportLoadPrePage) {
            mHasPrePage = hasPrePage;
            if (mHasPrePage) {
                showHeaderView(HEADER_TYPE_LOAD_MORE);
            } else {
                hideHeaderView();
            }
            if (mSwipeRefreshLayout != null && getParent() == mSwipeRefreshLayout) {
                mSwipeRefreshLayout.setEnabled(!mHasPrePage);
            }
        }
    }

    private final OnScrollListener mRecyclerScrollListener = new OnScrollListener() {

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                if (!mIsLoading) {
                    isLoadMoreEnabled = true;
                    isLastTouchAtBottom = isFooterVisible();
                    isLastTouchAtTop = isHeaderVisible();
                }
            } else if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                if (mIsLoading || !isLoadMoreEnabled || isEmptyList()) {
                    return;
                }
                if (mSupportLoadNextPage && mTriggerLoadMoreType == AUTO_LOAD_MORE
                        && isFooterVisible()
                        && mFooterContainer.getBottom() <= getHeight() - getPaddingBottom()
                        && isLastTouchAtBottom
                        && mHasNextPage) {
                    isLoadMoreEnabled = false;
                    showFooterView(FOOTER_TYPE_LOADING);
                    mIsLoading = true;
                    mCurLoadType = LOAD_TYPE_MORE_NEXT;
                    if (mOnLoadNextPageListener != null) {
                        mOnLoadNextPageListener.onLoadNextPage();
                    }
                } else if (mSupportLoadPrePage && isHeaderVisible()
                        && mHeaderContainer.getTop() >= getPaddingTop()
                        && isLastTouchAtTop
                        && mHasPrePage) {
                    isLoadMoreEnabled = false;
                    showHeaderView(HEADER_TYPE_LOADING);
                    mIsLoading = true;
                    mCurLoadType = LOAD_TYPE_MORE_PRE;
                    if (mOnLoadPrePageListener != null) {
                        mOnLoadPrePageListener.onLoadPrePage();
                    }
                } else {
                    isLoadMoreEnabled = false;
                }
            }
        }

        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            if (mIsLoading || !isLoadMoreEnabled || isEmptyList()) {
                return;
            }
            if (mSupportLoadNextPage
                    && mTriggerLoadMoreType == AUTO_LOAD_MORE
                    && !isLastTouchAtBottom
                    && mHasNextPage
                    && (canLoadNextPageInAdvance() || isFooterVisible())
                    && dy >= 0) {
                isLoadMoreEnabled = false;
                showFooterView(FOOTER_TYPE_LOADING);
                mIsLoading = true;
                mCurLoadType = LOAD_TYPE_MORE_NEXT;
                if (mOnLoadNextPageListener != null) {
                    mOnLoadNextPageListener.onLoadNextPage();
                }
            } else if (mSupportLoadPrePage
                    && !isLastTouchAtTop
                    && mHasPrePage
                    && (canLoadPrePageInAdvance() || isHeaderVisible())
                    && dy <= 0) {
                isLoadMoreEnabled = false;
                showHeaderView(HEADER_TYPE_LOADING);
                mIsLoading = true;
                mCurLoadType = LOAD_TYPE_MORE_PRE;
                if (mOnLoadPrePageListener != null) {
                    mOnLoadPrePageListener.onLoadPrePage();
                }
            }

        }
    };

    private boolean isHeaderVisible() {
        return getChildAt(0) == mHeaderContainer;
    }

    private boolean isFooterVisible() {
        return getChildAt(getChildCount() - 1) == mFooterContainer;
    }

    private boolean canLoadPrePageInAdvance() {
//        if (!mSupportLoadMoreInAdvance)
//            return false;
//        if (mLayoutManager == null || mAdapterWrapper == null)
//            return false;
//
//        int firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition();
//        return firstVisibleItemPosition-mAdapterWrapper.getHeadersCount() <= mMaxItemCountToAllowLoadMore;
        return false;
    }

    private boolean canLoadNextPageInAdvance() {
        if (!mSupportLoadMoreInAdvance) {
            return false;
        }
        if (mLayoutManager == null || mAdapterWrapper == null || mInnerAdapter == null) {
            return false;
        }

        int lastVisibleItemPosition = mLayoutManager.findLastVisibleItemPosition();
        return mInnerAdapter.getData().size()-
                (lastVisibleItemPosition-mAdapterWrapper.getHeadersCount()+1) <= mNotDisplayedItemCountWhenLoadMore;
    }

    /**
     * 设置对应类型的empty,footer,header
     *
     * @param type
     * @param layoutRes
     */
    public void setExtraView(@ExtraViewType int type, @LayoutRes int layoutRes) {
        ViewGroup parentView = null;
        switch (type) {
            case FOOTER_TYPE_LOAD_MORE:
                if (mLoadMoreFooterLayoutRes == layoutRes) {
                    return;
                }
                mLoadMoreFooterLayoutRes = layoutRes;
                parentView = mFooterContainer;
                break;
            case FOOTER_TYPE_LOADING:
                if (mLoadingFooterLayoutRes == layoutRes) {
                    return;
                }
                mLoadingFooterLayoutRes = layoutRes;
                parentView = mFooterContainer;
                break;
            case FOOTER_TYPE_NO_MORE:
                if (mNoMoreFooterLayoutRes == layoutRes) {
                    return;
                }
                mNoMoreFooterLayoutRes = layoutRes;
                parentView = mFooterContainer;
                break;
            case HEADER_TYPE_LOAD_MORE:
                if (mLoadMoreHeaderLayoutRes == layoutRes) {
                    return;
                }
                mLoadMoreHeaderLayoutRes = layoutRes;
                parentView = mHeaderContainer;
                break;
            case HEADER_TYPE_LOADING:
                if (mLoadingHeaderLayoutRes == layoutRes) {
                    return;
                }
                mLoadingHeaderLayoutRes = layoutRes;
                parentView = mHeaderContainer;
                break;
            case EMPTY_TYPE_NO_CONTENT:
                if (mNoContentFooterLayoutRes == layoutRes) {
                    return;
                }
                mNoContentFooterLayoutRes = layoutRes;
                parentView = mFooterContainer;
                break;
            case EMPTY_TYPE_NO_NETWORK:
            case EMPTY_TYPE_SERVER_ERROR:
                if (mNoNetworkFooterLayoutRes == layoutRes) {
                    return;
                }
                mNoNetworkFooterLayoutRes = layoutRes;
                parentView = mFooterContainer;
                break;
            default:
                break;
        }
        //缓存中已存在该类型的View，需要及时从xml映射并替换
        if (parentView != null) {
            View oldView = mViewsCache.get(type);
            if (oldView != null) {//初始化为空，不会inflateView
                View newView = layoutRes != 0 ? LayoutInflater.from(getContext()).inflate(layoutRes, parentView, false) : null;
                if (newView != null) {
                    if (mExtraViewCreatedListeners.get(type) != null) {
                        mExtraViewCreatedListeners.get(type).onViewCreated(newView);
                    }
                }
                mViewsCache.put(type, newView);
                if (parentView.getChildAt(0) == oldView) {
                    parentView.removeView(oldView);
                    if (newView != null) {
                        parentView.addView(newView);
                    }
                }
            }
        }
    }

    public @Nullable View getInflatedExtraView(@ExtraViewType int viewType) {
        return mViewsCache.get(viewType);
    }

    public int getNoMoreFooterLayoutRes() {
        return mNoMoreFooterLayoutRes;
    }

    public int getNoContentFooterLayoutRes() {
        return mNoContentFooterLayoutRes;
    }

    public void setNoContentTitle(String noContentTitle) {
        mNoContentTitle = noContentTitle;
    }

    public void setNoContentTitle(@StringRes int noContentTitleRes) {
        if (getContext() != null) {
            mNoContentTitle = getContext().getString(noContentTitleRes);
        }
    }

    public void setNoContentButton(@StringRes int btnTextRes, Runnable noContentClickAction) {
        if (getContext() != null) {
            mNoContentBtnText = getContext().getString(btnTextRes);
        }
        mNoContentClickAction = noContentClickAction;
    }

    public void setNoContentButton(String btnText, Runnable noContentClickAction) {
        mNoContentBtnText = btnText;
        mNoContentClickAction = noContentClickAction;
    }

    public BaseRecyclerAdapter getInnerAdapter() {
        return mInnerAdapter;
    }

    @SuppressWarnings("unchecked")
    public @NonNull
    <T> List<T> getData() {
        if (mInnerAdapter != null) {
            return mInnerAdapter.getData();
        }
        return new ArrayList<>();
    }

    public SwipeRefreshLayout getSwipeRefreshLayout() {
        return mSwipeRefreshLayout;
    }

    /**
     * @return 返回当前页面状态是否为加载中状态
     */
    public boolean isLoading() {
        return mIsLoading;
    }

    /**
     * 获取当前页面的加载类型
     *
     * @return
     */
    public @LoadType
    int getCurrentLoadType() {
        return mCurLoadType;
    }

    /**
     * 底部是否有更多数据未加载
     *
     * @return
     */
    public boolean hasMoreNext() {
        return mSupportLoadNextPage && mHasNextPage;
    }

    /**
     * 顶部是否有更多数据未加载
     *
     * @return
     */
    public boolean hasMorePre() {
        return mSupportLoadPrePage && mHasPrePage;
    }

    public void addHeaderView(View headerView) {
        if (!mSupportLoadPrePage && mAdapterWrapper != null) {
            mAdapterWrapper.addHeaderView(headerView);
        }
    }

    public void adaptHeadViews(RecyclerAdapterWrapper.ViewWrapper... viewWrappers) {
         if (mAdapterWrapper != null) {
            mAdapterWrapper.adaptHeadViews(viewWrappers);
        }
    }

    public void removeHeaderView(View headerView) {
        if (mAdapterWrapper != null) {
            mAdapterWrapper.removeHeaderView(headerView);
        }
    }

    public void addFooterView(View footerView) {
        if (!mSupportLoadNextPage && mAdapterWrapper != null) {
            hideFooterView();
            mAdapterWrapper.addFooterView(footerView);
        }
    }

    public void removeFooterView(View footerView) {
        if (mAdapterWrapper != null) {
            mAdapterWrapper.removeFooterView(footerView);
        }
    }

    public int getHeadersCount() {
        return mAdapterWrapper != null ? mAdapterWrapper.getHeadersCount() : 0;
    }

    public void updateAllItems() {
        if (mInnerAdapter != null) {
            mInnerAdapter.updateAllItems();
        }
    }

    /**
     * 隐藏所有的footerView
     */
    public void hideFooterView() {
        if (mFooterContainer.getChildCount() > 0) {
            mFooterContainer.removeAllViews();
        }
        mCurrentFooterType = NO_VIEW;
    }

    /**
     * 根据当前需要的footerType设置footerView
     *
     * @param footerType
     */
    public void showFooterView(@ExtraViewType int footerType) {
        if (mCurrentFooterType == footerType || mFooterContainer == null) {
            return;
        }

        if (mFooterContainer.getChildCount() > 0) {
            mFooterContainer.removeAllViews();
        }
        View newFooter = inflateExtraView(footerType, mFooterContainer);
        if (newFooter != null) {
            mFooterContainer.addView(newFooter);
        }
        mCurrentFooterType = footerType;
    }

    /**
     * 隐藏header
     */
    private void hideHeaderView() {
        if (mAdapterWrapper == null || mAdapterWrapper.getHeadersCount() == 0) {
            return;
        }

        if (mHeaderContainer.getChildCount() > 0) {
            mHeaderContainer.removeAllViews();
        }
//        mAdapterWrapper.removeHeaderView(mHeaderContainer);
        mCurrentHeaderType = NO_VIEW;
    }

    /**
     * 显示对应类型的headerView
     *
     * @param headerType
     */
    private void showHeaderView(@ExtraViewType int headerType) {
        if(mAdapterWrapper == null || mHeaderContainer == null)return;
        if (mCurrentHeaderType == headerType) {
            return;
        }

        if (mAdapterWrapper.getHeadersCount() == 0) {
            mAdapterWrapper.addHeaderView(mHeaderContainer);
        }
        if (mHeaderContainer.getChildCount() > 0) {
            mHeaderContainer.removeAllViews();
        }
        View newHeader = inflateExtraView(headerType, mHeaderContainer);
        if (newHeader != null) {
            mHeaderContainer.addView(newHeader);
        }
        mCurrentHeaderType = headerType;
    }

    private boolean isEmptyList() {
        if (mEmptyCondition != null) {
            return mEmptyCondition.isDataEmpty();
        }
        return mInnerAdapter == null || mInnerAdapter.getData().isEmpty();
    }

    /**
     * 根据当前的类型获取相应的view
     *
     * @param type
     * @param parentView
     * @return
     */
    private View inflateExtraView(@ExtraViewType int type, FrameLayout parentView) {
        View view = null;
        if (mViewsCache.get(type) != null) {
            view = mViewsCache.get(type);
        }
        if (view != null) {
            if (mExtraViewCreatedListeners.get(type) != null) {
                mExtraViewCreatedListeners.get(type).onViewCreated(view);
            }
            return view;
        }

        int layoutId = 0;
        switch (type) {
            case FOOTER_TYPE_LOAD_MORE:
                layoutId = mLoadMoreFooterLayoutRes;
                break;
            case FOOTER_TYPE_LOADING:
                layoutId = mLoadingFooterLayoutRes;
                break;
            case FOOTER_TYPE_NO_MORE:
                layoutId = mNoMoreFooterLayoutRes;
                break;
            case HEADER_TYPE_LOADING:
                layoutId = mLoadingHeaderLayoutRes;
                break;
            case HEADER_TYPE_LOAD_MORE:
                layoutId = mLoadMoreHeaderLayoutRes;
                break;
            case EMPTY_TYPE_NO_CONTENT:
                layoutId = mNoContentFooterLayoutRes;
                break;
            case EMPTY_TYPE_NO_NETWORK:
            case EMPTY_TYPE_SERVER_ERROR:
                layoutId = mNoNetworkFooterLayoutRes;
                break;
            default:
                break;
        }
        if (layoutId != 0) {
            view = LayoutInflater.from(getContext()).inflate(layoutId, parentView, false);
            mViewsCache.put(type, view);
            /*if (layoutId == R.layout.chit_view_no_network) {
                view.findViewById(R.id.chit_btn_no_network_retry).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        hideFooterView();
                        performRefresh();
                    }
                });
            } else if (layoutId == R.layout.chit_view_no_content_with_button) {
                TextView tvTitle = view.findViewById(R.id.chit_tv_no_content_title);
                if (!TextUtils.isEmpty(mNoContentTitle)) {
                    tvTitle.setText(mNoContentTitle);
                }
                TextView tvBtn = view.findViewById(R.id.chit_btn_no_content);
                if (!TextUtils.isEmpty(mNoContentBtnText)) {
                    tvBtn.setVisibility(VISIBLE);
                    tvBtn.setText(mNoContentBtnText);
                    tvBtn.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (mNoContentClickAction != null) {
                                mNoContentClickAction.run();
                            }
                        }
                    });
                } else {
                    tvBtn.setVisibility(GONE);
                }
            } else */if (layoutId == R.layout.main_footer_click_load_more_standard) {
                view.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        performLoadNextPage(true);
                    }
                });
            }
            if (mExtraViewCreatedListeners.get(type) != null) {
                mExtraViewCreatedListeners.get(type).onViewCreated(view);
            }
        } else {
            view = null;
        }

        return view;
    }

    /**
     * 根据类型设置当前空白view
     *
     * @param type
     */
    protected void showEmptyView(@ExtraViewType final int type) {
        if (mFooterContainer == null) {
            return;
        }
        if (mCurrentFooterType == type) {
            return;
        }

        hideHeaderView();
        if (mFooterContainer.getChildCount() > 0) {
            mFooterContainer.removeAllViews();
        }
        View newFooter = inflateExtraView(type, mFooterContainer);
        if (newFooter != null) {
            if (mAutoExpandNoContentLayout && getLayoutParams() != null && getLayoutParams().height != ViewGroup.LayoutParams.WRAP_CONTENT) {
                if (getMeasuredHeight() > 0) {
                    newFooter.getLayoutParams().height = getMeasuredHeight() - getPaddingBottom();
                    mFooterContainer.addView(newFooter);
                } else {
                    addOnLayoutChangeListener(new OnLayoutChangeListener() {
                        @Override
                        public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                            if (bottom - top > 0) {
                                removeOnLayoutChangeListener(this);
                                newFooter.getLayoutParams().height = bottom - top - getPaddingBottom();
                                if (type == mCurrentFooterType) {
                                    mFooterContainer.addView(newFooter);
                                }
                            }
                        }
                    });
                }
            } else {
                mFooterContainer.addView(newFooter);
            }
        }
        mCurrentFooterType = type;
    }

    public interface OnExtraViewCreatedListener {
        void onViewCreated(@NonNull View inflatedView);
    }

    public interface EmptyCondition {
        boolean isDataEmpty();
    }

    public void setNotDisplayedItemCountWhenLoadMore(int notDisplayedItemCountWhenLoadMore) {
        if (notDisplayedItemCountWhenLoadMore > 0) {
            mNotDisplayedItemCountWhenLoadMore = notDisplayedItemCountWhenLoadMore;
        }
    }
    
    public void setAutoExpandNoContentLayout(boolean autoExpandNoContentLayout) {
        mAutoExpandNoContentLayout = autoExpandNoContentLayout;
    }

    public void setShouldAlwaysShowNoMore(boolean shouldAlwaysShowNoMore) {
        mShouldAlwaysShowNoMore = shouldAlwaysShowNoMore;
    }

    public void scrollToDataPosition(int dataPos, boolean needAnchorTop) {
        if (needAnchorTop) {
            scrollToDataPosition(dataPos, 0);
        } else  {
            scrollToPosition(getHeadersCount() + dataPos);
        }
    }

    public void scrollToDataPosition(int dataPos, int offset) {
        if (mInnerAdapter == null || mLayoutManager == null ||
                dataPos < 0 || dataPos >= mInnerAdapter.getData().size()) {
            return;
        }
        mLayoutManager.scrollToPositionWithOffset(getHeadersCount() + dataPos, offset);
    }

    public void scrollToTop() {
        if (mLayoutManager == null) {
            return;
        }
        mLayoutManager.scrollToPositionWithOffset(0, 0);
    }

    public int[] getFirstVisibleDataPositionWithOffset() {
        if (mLayoutManager == null || getChildCount() == 0 ||
                mInnerAdapter == null || mInnerAdapter.getData().isEmpty()) {
            return null;
        }

        int firstPos = mLayoutManager.findFirstVisibleItemPosition();
        int firstDataPos = firstPos - getHeadersCount();
        if (firstDataPos < 0) {
            return new int[]{0, 0};
        }

        View firstChild = getChildAt(0);
        if (firstChild.getTop() >= 0) {
            return new int[]{firstDataPos, firstChild.getTop()};
        }

        if (getChildCount() < 2 || firstDataPos + 1 >= mInnerAdapter.getData().size()) {
            return null;
        }

        View secondChild = getChildAt(1);
        return new int[]{firstDataPos + 1, secondChild.getTop()};
    }

    public int[] getFirstCompletelyVisibleDataPositionWithOffset() {
        if (mLayoutManager == null || getChildCount() == 0 ||
                mInnerAdapter == null || mInnerAdapter.getData().isEmpty()) {
            return null;
        }

        int[] pos = new int[2];
        pos[0] = mLayoutManager.findFirstCompletelyVisibleItemPosition();
        if (pos[0] < getHeadersCount() || pos[0] >= getHeadersCount() + mInnerAdapter.getData().size()) {
            return null;
        }

        pos[0] -= getHeadersCount();
        OrientationHelper orientationHelper = OrientationHelper.createOrientationHelper(mLayoutManager, RecyclerView.VERTICAL);
        int fromIndex = 0;
        int toIndex = getHeadersCount() + mInnerAdapter.getData().size() +
                (mAdapterWrapper == null ? 0 : mAdapterWrapper.getFootersCount());
        final int start = orientationHelper.getStartAfterPadding();
        final int end = orientationHelper.getEndAfterPadding();
        final int next = toIndex > fromIndex ? 1 : -1;
        for (int i = fromIndex; i != toIndex; i += next) {
            final View child = getChildAt(i);
            final int childStart = orientationHelper.getDecoratedStart(child);
            final int childEnd = orientationHelper.getDecoratedEnd(child);
            if (childStart < end && childEnd > start) {
                if (childStart >= start && childEnd <= end) {
                    pos[1] = childStart;
                    return pos;
                }
            }
        }
        return pos;
    }
}
