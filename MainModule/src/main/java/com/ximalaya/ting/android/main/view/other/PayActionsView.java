package com.ximalaya.ting.android.main.view.other;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.util.common.PackageUtil;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

/**
 * 支付方式选择控件
 *
 * <AUTHOR> on 16/9/12.
 */
public class PayActionsView extends LinearLayout implements View.OnClickListener {

    public static final int WAY_XIDIAN = 0;
    public static final int WAY_ALIPAY = 1;
    public static final int WAY_WECHAT = 2;

    private int currentPayWay = WAY_ALIPAY;

    private TextView xidianPayTv;
    private TextView aliPayTv;
    private TextView wechatPayTv;
    private TextView morePayWayTv;

    private final Context mContext;

    private OnSwitchPayWayListener mSwitchListener;

    public PayActionsView(Context context) {
        this(context, null);
    }

    public PayActionsView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        initUI();
    }

    public int getCurrentPayWay() {
        return currentPayWay;
    }

    private void initUI() {
        View mView = View.inflate(mContext, R.layout.main_item_pay_module, this);
        setOrientation(VERTICAL);
        xidianPayTv = (TextView) findViewById(R.id.main_tv_xidian_pay);
        aliPayTv = (TextView) findViewById(R.id.main_tv_ali_pay);
        wechatPayTv = (TextView) findViewById(R.id.main_tv_wechat_pay);
        morePayWayTv = (TextView) findViewById(R.id.main_more_pay_way);
        initListener();
        choosePayWay(WAY_XIDIAN);
    }

    private void initListener() {
        xidianPayTv.setOnClickListener(this);
        aliPayTv.setOnClickListener(this);
        wechatPayTv.setOnClickListener(this);
        morePayWayTv.setOnClickListener(this);
        AutoTraceHelper.bindData(xidianPayTv,"");
        AutoTraceHelper.bindData(aliPayTv,"");
        AutoTraceHelper.bindData(wechatPayTv,"");
        AutoTraceHelper.bindData(morePayWayTv,"");
    }


    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.main_tv_xidian_pay) {
            choosePayWay(WAY_XIDIAN);

        } else if (i == R.id.main_tv_ali_pay) {
            choosePayWay(WAY_ALIPAY);

        } else if (i == R.id.main_tv_wechat_pay) {
            if(!PackageUtil.isAppInstalled(BaseApplication.getMyApplicationContext(), PackageUtil.PACKAGE_WECHAT)){
                CustomToast.showFailToast(R.string.main_install_wechat_first);
                return;
            }
            choosePayWay(WAY_WECHAT);
        } else if (i == R.id.main_more_pay_way) {
            morePayWayTv.setVisibility(View.GONE);
            wechatPayTv.setVisibility(View.VISIBLE);
        }
    }

    private void choosePayWay(int payWay) {
        currentPayWay = payWay;
        if (mSwitchListener != null) {
            mSwitchListener.switchPayWay(payWay);
        }
        switch (payWay) {
            case WAY_XIDIAN:
                xidianPayTv.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable.main_icon_xidian), null, LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_select), null);
                aliPayTv.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_alipay), null, LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_unselect), null);
                wechatPayTv.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_wechat_pay), null, LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_unselect), null);
                break;
            case WAY_ALIPAY:
                xidianPayTv.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable.main_icon_xidian), null, LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_unselect), null);
                aliPayTv.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_alipay), null, LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_select), null);
                wechatPayTv.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_wechat_pay), null, LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_unselect), null);
                break;
            case WAY_WECHAT:
                xidianPayTv.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable.main_icon_xidian), null, LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_unselect), null);
                aliPayTv.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_alipay), null, LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_unselect), null);
                wechatPayTv.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_wechat_pay), null, LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_select), null);
                break;
            default:
                break;
        }
    }

    public void setOnSwitchPayWayListener(OnSwitchPayWayListener listener) {
        mSwitchListener = listener;
    }

    public void setXidianLeft(double xidianleft) {
        xidianPayTv.setText(String.format("%s(%s喜点)", mContext.getString(R.string.main_xidianleft), String.valueOf(StringUtil.subZeroAndDot(xidianleft, 2))));
    }

    public interface OnSwitchPayWayListener {
        void switchPayWay(int payWay);
    }

}
