package com.ximalaya.ting.android.main.dialog

import com.google.gson.Gson
import com.ximalaya.ting.android.host.model.album.RecInfo
import com.ximalaya.ting.android.host.util.kt.realString
import com.ximalaya.ting.android.main.adapter.find.recommendStaggered.RecommendStaggeredTraceManager
import com.ximalaya.ting.android.main.model.rec.RecommendAlbumItem
import com.ximalaya.ting.android.main.model.rec.RecommendIndexFeedVideoItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendMyClubModel
import com.ximalaya.ting.android.main.model.rec.RecommendSpecialItem
import com.ximalaya.ting.android.main.model.rec.RecommendTrackItem
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import org.json.JSONObject

/**
 * Created by changle.fang on 2021/11/19.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
object RecommendFragmentDislikeAnchorTraceManager {

    fun traceOnShow(data: RecommendItemNew?, dislikeType: String, metaId: Int, traceJson: JSONObject?) {
        data ?: return
        var recTrack = ""
        var recSrc = ""
        var reasonSrc = ""
        var reasonTrack = ""
        var recommendedLanguage = ""
        var albumId = ""
        var anchorId = ""
        var mark = 0.0
        var subscribe = 0L
        var recInfo: RecInfo? = null
        var trackId = ""
        var status = ""
        var specialId = 0L
        var playCount = ""
        var isBrand = ""
        var roomId = 0L
        var videoId = 0L
        if (data.item is RecommendAlbumItem) {
            (data.item as? RecommendAlbumItem)?.apply {
                recInfo = this.recInfo
                recTrack = this.recTrack
                recSrc = recommentSrc
                reasonSrc = recInfo?.reasonSrc ?: ""
                reasonTrack = recInfo?.reasonTrack ?: ""
                recommendedLanguage = recInfo?.recReason ?: ""
                albumId = id.toString()
                anchorId = uid.toString()
                mark = score
                subscribe = subscribeCount
                isBrand = isBrand().toString()
            }
        } else if (data.item is RecommendTrackItem) {
            (data.item as? RecommendTrackItem)?.apply {
                recInfo = this.recInfo
                recTrack = recInfo?.recTrack ?: ""
                recSrc = recInfo?.recSrc ?: ""
                reasonSrc = recInfo?.reasonSrc ?: ""
                reasonTrack = recInfo?.reasonTrack ?: ""
                recommendedLanguage = recInfo?.recReason ?: ""
                albumId = album?.albumId?.toString() ?: ""
                anchorId = uid.toString()
                trackId = dataId.toString()
                playCount = this.playCount.toString()
                isBrand = "false"
            }
        } else if (data.item is RecommendSpecialItem) {
            (data.item as? RecommendSpecialItem)?.apply {
                recInfo = this.recInfo
                recTrack = recInfo?.recTrack ?: ""
                recSrc = recInfo?.recSrc ?: ""
                specialId = this.specialId
                playCount = this.playCount.toString()
                anchorId = uid.toString()
            }
        } else if (data.item is RecommendMyClubModel) {
            status = "对谈中"
            (data.item as? RecommendMyClubModel)?.apply {
                roomId = this.roomId
            }
        } else if (data.item is RecommendIndexFeedVideoItem) {
            (data.item as? RecommendIndexFeedVideoItem)?.apply {
                var recommendIndexFeedVideoItem = data.item as RecommendIndexFeedVideoItem
                recTrack = recommendIndexFeedVideoItem.recTrack
                recSrc = recommendIndexFeedVideoItem.recSrc
                recommendedLanguage = recommendIndexFeedVideoItem.recReason ?: ""
                albumId = recommendIndexFeedVideoItem.subElements[0]?.refId?.toString() ?: ""
                mark = recommendIndexFeedVideoItem.subElements[0]?.quality?.score?.toDouble() ?: 0.0
                subscribe = recommendIndexFeedVideoItem.subElements[0]?.interact?.subscribed?.toLong() ?: 0L
                videoId = recommendIndexFeedVideoItem.refId
            }
        } else {
            // 兜底取json数据
            (data.jsonObject?.optJSONObject("item"))?.apply {
                recInfo = Gson().fromJson<RecInfo>(
                    this.optString("recInfo"),
                    RecInfo::class.java
                )
                recTrack = recInfo?.recTrack ?: ""
                recSrc = recInfo?.recSrc ?: ""
                specialId = this.optLong("specialId", 0)
                playCount = "${this.optLong("playCount", 0)}"
                anchorId = "${this.optLong("uid", 0)}"
            }
        }
        // 信息流（双列）负反馈  弹框展示
        val trace = XMTraceApi.Trace()
                .setMetaId(metaId)
                .setServiceId("dialogView")
                .put("rec_track", recTrack)
                .put("rec_src", recSrc)
                .put("ubtTraceId", data.ubtTraceId ?: "")
                .put("recommendedLanguage", recommendedLanguage)
                .put("albumId", albumId)
                .put("isBrand", isBrand)
                .put("anchorId", anchorId)
                .put("trackId", trackId)
                .put("positionNew", data.newPos.toString())
                .put("style", RecommendStaggeredTraceManager.getStyle())
                .put("currPage", "newHomePage")
                .put("contentType", data.itemType)
        if (videoId > 0) {
            trace.put("videoId", videoId.toString())
        }
        when (dislikeType) {
            RecommendPageBottomDialog.DISLIKE_TYPE_ALBUM -> {
                trace.put("mark", mark.toString())
                        .put("subscribe", subscribe.toString())
            }
            RecommendPageBottomDialog.DISLIKE_TYPE_MYCLUB -> {
                trace.put("status", status)
                        .put("roomId", roomId.toString())
            }
            RecommendPageBottomDialog.DISLIKE_TYPE_SPECIAL -> {
                trace.put("specialId", specialId.toString())
            }
            RecommendPageBottomDialog.DISLIKE_TYPE_TRACK, RecommendPageBottomDialog.DISLIKE_TYPE_VIDEO -> {
                trace.put("playCount", playCount)
            }
        }
        traceJson?.let { json ->
            json.keys().forEach {
//                        if (it != "reason_track" && it != "reason_src") {
                trace.put(it, json.optString(it, "").realString(""))
//                        }
            }
        }
        trace.createTrace()
    }

    fun traceOnItemClick(data: RecommendItemNew?, metaId: Int, dislikeType: String, itemName: String, traceJson: JSONObject?) {
        data ?: return
        var recTrack = ""
        var recSrc = ""
        var reasonSrc = ""
        var reasonTrack = ""
        var recommendedLanguage = ""
        var albumId = ""
        var anchorId = ""
        var mark = 0.0
        var subscribe = 0L
        var recInfo: RecInfo? = null
        var trackId = ""
        var status = ""
        var specialId = 0L
        var playCount = ""
        var isBrand = ""
        var roomId = 0L
        var videoId = 0L
        if (data.item is RecommendAlbumItem) {
            (data.item as? RecommendAlbumItem)?.apply {
                recInfo = this.recInfo
                recTrack = this.recTrack
                recSrc = recommentSrc
                reasonSrc = recInfo?.reasonSrc ?: ""
                reasonTrack = recInfo?.reasonTrack ?: ""
                recommendedLanguage = recInfo?.recReason ?: ""
                albumId = id.toString()
                anchorId = uid.toString()
                mark = score
                subscribe = subscribeCount
                isBrand = isBrand().toString()
            }
        } else if (data.item is RecommendTrackItem) {
            (data.item as? RecommendTrackItem)?.apply {
                recInfo = this.recInfo
                recTrack = recInfo?.recTrack ?: ""
                recSrc = recInfo?.recSrc ?: ""
                reasonSrc = recInfo?.reasonSrc ?: ""
                reasonTrack = recInfo?.reasonTrack ?: ""
                recommendedLanguage = recInfo?.recReason ?: ""
                albumId = album?.albumId?.toString() ?: ""
                anchorId = uid.toString()
                trackId = dataId.toString()
                playCount = this.playCount.toString()
                isBrand = "false"
            }
        } else if (data.item is RecommendSpecialItem) {
            (data.item as? RecommendSpecialItem)?.apply {
                recInfo = this.recInfo
                recTrack = recInfo?.recTrack ?: ""
                recSrc = recInfo?.recSrc ?: ""
                specialId = this.specialId
                playCount = this.playCount.toString()
                anchorId = uid.toString()
            }
        } else if (data.item is RecommendMyClubModel) {
            status = "对谈中"
            (data.item as? RecommendMyClubModel)?.apply {
                roomId = this.roomId
            }
        } else if (data.item is RecommendIndexFeedVideoItem) {
            (data.item as? RecommendIndexFeedVideoItem)?.apply {
                var recommendIndexFeedVideoItem = data.item as RecommendIndexFeedVideoItem
                recTrack = recommendIndexFeedVideoItem.recTrack
                recSrc = recommendIndexFeedVideoItem.recSrc
                recommendedLanguage = recommendIndexFeedVideoItem.recReason ?: ""
                albumId = recommendIndexFeedVideoItem.subElements[0]?.refId?.toString() ?: ""
                mark = recommendIndexFeedVideoItem.subElements[0]?.quality?.score?.toDouble() ?: 0.0
                subscribe = recommendIndexFeedVideoItem.subElements[0]?.interact?.subscribed?.toLong() ?: 0L
                videoId = recommendIndexFeedVideoItem.refId
            }
        } else {
            // 兜底取json数据
            (data.jsonObject?.optJSONObject("item"))?.apply {
                recInfo = Gson().fromJson<RecInfo>(
                    this.optString("recInfo"),
                    RecInfo::class.java
                )
                recTrack = recInfo?.recTrack ?: ""
                recSrc = recInfo?.recSrc ?: ""
                specialId = this.optLong("specialId", 0)
                playCount = "${this.optLong("playCount", 0)}"
                anchorId = "${this.optLong("uid", 0)}"
            }
        }
        // 信息流（双列）负反馈-不感兴趣  弹框控件点击
        val trace = XMTraceApi.Trace()
                .setMetaId(metaId)
                .setServiceId("dialogClick")
                .put("rec_track", recTrack)
                .put("rec_src", recSrc)
                .put("ubtTraceId", data.ubtTraceId ?: "")
                .put("recommendedLanguage", recommendedLanguage)
                .put("albumId", albumId)
                .put("isBrand", isBrand)
                .put("anchorId", anchorId)
                .put("trackId", trackId)
                .put("positionNew", data.newPos.toString())
                .put("item", itemName)
                .put("currPage", "newHomePage")
                .put("style", RecommendStaggeredTraceManager.getStyle())
                .put("contentType", data.itemType)
        if (videoId > 0) {
            trace.put("videoId", videoId.toString())
        }
        when (dislikeType) {
            RecommendPageBottomDialog.DISLIKE_TYPE_ALBUM -> {
                trace.put("mark", mark.toString())
                        .put("subscribe", subscribe.toString())
            }
            RecommendPageBottomDialog.DISLIKE_TYPE_MYCLUB -> {
                trace.put("status", status)
                        .put("roomId", roomId.toString())
            }
            RecommendPageBottomDialog.DISLIKE_TYPE_SPECIAL -> {
                trace.put("specialId", specialId.toString())
            }
            RecommendPageBottomDialog.DISLIKE_TYPE_TRACK, RecommendPageBottomDialog.DISLIKE_TYPE_VIDEO -> {
                trace.put("playCount", playCount)
            }
        }
        traceJson?.let { json ->
            json.keys().forEach {
//                        if (it != "reason_track" && it != "reason_src") {
                trace.put(it, json.optString(it, "").realString(""))
//                        }
            }
        }
        trace.createTrace()
    }
}