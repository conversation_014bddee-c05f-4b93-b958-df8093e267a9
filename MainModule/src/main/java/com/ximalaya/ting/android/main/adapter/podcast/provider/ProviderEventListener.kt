package com.ximalaya.ting.android.main.adapter.podcast.provider

import android.view.View
import com.ximalaya.ting.android.main.model.podcast.v2.IPodRankCellInfo
import com.ximalaya.ting.android.main.model.podcast.v2.PodFeedCategory
import com.ximalaya.ting.android.main.model.podcast.v2.PodGuessTrackInfo
import com.ximalaya.ting.android.main.model.podcast.v2.PodJinGangInfo
import com.ximalaya.ting.android.main.model.podcast.v2.PodListenAlbumInfo
import com.ximalaya.ting.android.main.model.podcast.v2.PodListenAlbumTrackInfo
import com.ximalaya.ting.android.main.model.podcast.v2.PodListenTrackInfo
import com.ximalaya.ting.android.main.model.podcast.v2.PodMoreItemInfo
import com.ximalaya.ting.android.main.model.podcast.v2.PodMoreTopicItemInfo
import com.ximalaya.ting.android.main.model.podcast.v2.PodRichTitleInfo
import com.ximalaya.ting.android.main.model.podcast.v2.PodSpecSubscribeTrackInfo
import com.ximalaya.ting.android.main.model.podcast.v2.PodSpecialTrackInfo
import com.ximalaya.ting.android.main.model.podcast.v2.PodTopicTrackInfo

/**
 * Created by xiaolei on 2023/3/13.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13701664636
 */
open class ProviderEventListener : IPodTopicTitleListener, IPodTopicTrackListener,
    IPodSpecTrackListener, IPodTrackTabClickListener, IPodGuessTrackListener, IPodNoNetWorkListener,
    IPodRankEventListener, IPodMoreListener, IPodMoreTopicListener, IPodSpecSubTrackListener, IPodJinGangEventListener,
    IPodListenAlbumListener, IPodListenTrackListener, IPodPrivateRadioEventListener {
    override fun onTopicTitleClicked(title: PodRichTitleInfo) {
    }

    override fun onTopicTitleShown(title: PodRichTitleInfo) {
    }

    override fun onFollowClicked(
        track: PodSpecialTrackInfo,
        callBack: (Boolean) -> Unit,
        position: Int
    ) {
    }

    override fun onSpecTrackClicked(track: PodSpecialTrackInfo, view: View, position: Int) {
    }

    override fun onSpecPlayClicked(track: PodSpecialTrackInfo, view: View, position: Int) {
    }

    override fun onSpecAnchorClicked(track: PodSpecialTrackInfo, position: Int) {
    }

    override fun onSpecTrackShown(track: PodSpecialTrackInfo, view: View, position: Int) {
    }

    override fun onTabClicked(tab: PodFeedCategory, position: Int) {
    }

    override fun onTabAllClicked(list: List<PodFeedCategory>, clickCallback: (PodFeedCategory) -> Unit) {
    }

    override fun onTabShown(tab: PodFeedCategory, position: Int) {
    }

    override fun onTopicTrackClicked(track: PodTopicTrackInfo, view: View, position: Int) {
    }

    override fun onTopicTrackPlayClicked(track: PodTopicTrackInfo, view: View, position: Int) {
    }

    override fun onTopicAnchorClicked(track: PodTopicTrackInfo, view: View, position: Int) {
    }

    override fun onTopicTrackShown(track: PodTopicTrackInfo, view: View, position: Int) {
    }

    override fun onGuessTrackClicked(track: PodGuessTrackInfo, view: View, position: Int) {
    }

    override fun onGuessPlayClicked(track: PodGuessTrackInfo, view: View, position: Int) {
    }

    override fun onGuessTrackShown(track: PodGuessTrackInfo, view: View, position: Int) {
    }

    override fun onNoNetWorkClicked() {
    }

    override fun onRankTrackShown(position: Int, item: IPodRankCellInfo) {
    }

    override fun onRankTrackClicked(position: Int, item: IPodRankCellInfo, view: View) {
    }

    override fun onRankAnchorClicked(position: Int, item: IPodRankCellInfo) {
    }

    override fun onRankMoreClicked(index: Int) {
    }

    override fun onRankTrackPlayClicked(position: Int, item: IPodRankCellInfo, view: View) {
    }

    override fun onRankSubscribeClicked(position: Int, item: IPodRankCellInfo, view: View, subscribeBack: (Boolean) -> Unit) {
    }

    override fun onMoreTopicTrackShown(item: PodMoreTopicItemInfo) {
    }

    override fun onMoreTopicTrackClick(item: PodMoreTopicItemInfo) {
    }

    override fun onMoreItemShown(item: PodMoreItemInfo) {
    }

    override fun onMoreItemClicked(item: PodMoreItemInfo) {
    }

    override fun onPodJinGangItemShown(item: PodJinGangInfo, position: Int) {
    }

    override fun onPodJinGangItemClick(item: PodJinGangInfo, position: Int) {
    }

    override fun onSpecSubTrackShown(info: PodSpecSubscribeTrackInfo, position: Int) {
    }

    override fun onSpecAlbumClick(info: PodSpecSubscribeTrackInfo, position: Int) {
    }

    override fun onSpecSubscribeClick(
        info: PodSpecSubscribeTrackInfo,
        position: Int,
        subscribeBack: (Boolean) -> Unit
    ) {
    }

    override fun onSpecSubTrackPlayClick(view: View, info: PodSpecSubscribeTrackInfo, position: Int) {
    }

    override fun onSpecSubTrackClick(view: View, info: PodSpecSubscribeTrackInfo, position: Int) {
    }

    override fun onListenAlbumClicked(track: PodListenAlbumInfo, view: View, position: Int) {
    }

    override fun onListenAlbumShown(track: PodListenAlbumInfo, view: View, position: Int) {
    }

    override fun onListenSubscribeClick(
        info: PodListenAlbumInfo,
        position: Int,
        subscribeBack: (Boolean) -> Unit
    ) {
    }

    override fun onListenTrackClicked(track: PodListenTrackInfo, view: View, position: Int) {
    }

    override fun onListenTrackPlayClicked(
        track: PodListenTrackInfo,
        view: View,
        position: Int
    ) {
    }

    override fun onListenTrackAlbumClicked(track: PodListenTrackInfo, view: View, position: Int) {
    }

    override fun onListenTrackShown(track: PodListenTrackInfo, view: View, position: Int) {
    }

    override fun onPrivateRadioItemShown(item: PodGuessTrackInfo) {
    }

    override fun onPrivateRadioItemClicked(item: PodGuessTrackInfo) {
    }

    override fun onPrivateRadioItemPlayClicked(
        item: PodGuessTrackInfo,
        list: List<PodGuessTrackInfo>
    ) {
    }
}