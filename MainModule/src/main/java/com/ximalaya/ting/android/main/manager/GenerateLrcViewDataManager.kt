package com.ximalaya.ting.android.main.manager

import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager.getInstance

/**
 * Create by {jian.kang} on 10/21/22
 * <AUTHOR>
 */
object GenerateLrcViewDataManager {
    var isFullScreen = false
    var dataLoadSuccess = false

    fun isAiDoc(): Boolean {
        return getInstance().soundInfo?.docInfo?.docType == PlayingSoundInfo.DocInfo.TYPE_AI_DOC
    }
}