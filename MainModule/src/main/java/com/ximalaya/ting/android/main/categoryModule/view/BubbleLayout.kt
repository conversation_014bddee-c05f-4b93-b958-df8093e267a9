package com.ximalaya.ting.android.main.categoryModule.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import android.graphics.Shader
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Created by changle.fang on 2021/9/30.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
class BubbleLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var mWidth = 0
    private var mHeight = 0
    private var mBubbleHeight = 8.dp
    private var mBubbleWidth = 20.dp
    private var mRectF = RectF(0f, mBubbleHeight.toFloat() - 1.dp, 0f, 0f)
    private var mStrokeRectF = RectF(0f, mBubbleHeight.toFloat(), 0f, 0f)
    private var mPath = Path()
    private var mStrokePath = Path()
    private var mTrianglePath = Path()
    private var mPaint = Paint()
    private var mStrokePaint = Paint()
    private var mTrianglePaint = Paint()
    private var mDp8 = 8.dp.toFloat()
    var mDeltaX = 0f
        set(value) {
            field = value
            setPath()
            invalidate()
        }

    init {
        setWillNotDraw(false)
        mStrokePaint.apply {
            isAntiAlias = true
            isDither = true
            style = Paint.Style.STROKE
            strokeWidth = 1f
        }
        mPaint.apply {
            isAntiAlias = true
            isDither = true
            color = Color.parseColor(if (BaseFragmentActivity.sIsDarkMode) "#ff222222" else "#fff6f7f8")
        }
        mTrianglePaint.apply {
            isAntiAlias = true
            isDither = true
            shader = LinearGradient(0f, 0f, 0f, mBubbleHeight.toFloat(),
                    if (BaseFragmentActivity.sIsDarkMode) 0xff363636.toInt() else 0xffe9e9e9.toInt(),
                    if (BaseFragmentActivity.sIsDarkMode) 0xff222222.toInt() else 0xfff6f7f8.toInt(),
                    Shader.TileMode.CLAMP)
            strokeWidth = 1f
            style = Paint.Style.FILL_AND_STROKE
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        mWidth = measuredWidth
        mHeight = measuredHeight
        Logger.e("bubble width", "$mWidth height:$mHeight")
        mRectF.right = mWidth.toFloat()
        mRectF.bottom = mHeight.toFloat() - 1.dp
        mStrokeRectF.right = mWidth.toFloat()
        mStrokeRectF.bottom = mHeight.toFloat()
        setPath()
    }

    private fun setPath() {
        mPath.apply {
            reset()
            addRoundRect(mRectF, mDp8, mDp8, Path.Direction.CW)
        }
        mStrokePath.apply {
            reset()
            addRoundRect(mStrokeRectF, mDp8, mDp8, Path.Direction.CW)
        }
        mPaint.shader = LinearGradient(0f, mBubbleHeight.toFloat(), 0f, mRectF.bottom,
                if (BaseFragmentActivity.sIsDarkMode) 0xff222222.toInt() else 0xfff6f7f8.toInt(),
                if (BaseFragmentActivity.sIsDarkMode) 0xff222222.toInt() else 0xffeef0f1.toInt(),
                Shader.TileMode.CLAMP)
        mTrianglePath.apply {
            reset()
            moveTo(mDeltaX - mBubbleWidth / 2, mBubbleHeight.toFloat() + 1.dp)
            lineTo(mDeltaX, 0f)
            lineTo(mDeltaX + mBubbleWidth / 2, mBubbleHeight.toFloat() + 1.dp)
            close()
        }
    }

    override fun onDraw(canvas: Canvas?) {
        if (mWidth == 0 || mHeight == 0) {
            measure(0, 0)
        }
        super.onDraw(canvas)
        drawBg(canvas)
    }

    private fun drawBg(canvas: Canvas?) {
//        canvas?.drawPath(mStrokePath, mStrokePaint)
//        canvas?.drawPath(mPath, mPaint)
//        canvas?.drawPath(mTrianglePath, mTrianglePaint)
    }
}