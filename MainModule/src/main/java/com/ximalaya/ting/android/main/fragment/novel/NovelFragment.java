package com.ximalaya.ting.android.main.fragment.novel;

import android.os.Bundle;

import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.MainBottomTabProvider;
import com.ximalaya.ting.android.host.manager.NovelTabAbManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2023/12/1
 * Description：
 */
public class NovelFragment extends BaseFragment2 implements MainBottomTabProvider {
    @Override
    protected String getPageLogicName() {
        return "NovelFragment";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setFilterStatusBarSet(true);
        Router.getActionByCallback(Configure.BUNDLE_RN, (Router.SimpleBundleInstallCallback) bundleModel -> {
            if (bundleModel == null || (bundleModel != Configure.rnBundleModel) || !canUpdateUi()) {
                return;
            }
            Bundle args = NovelTabAbManager.INSTANCE.getBundleArgs(false);
            BaseFragment mRnFragment = null;
            if (ConstantsOpenSdk.isDebug && MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost.KEY_DEBUG_FREE_LISTEN_TOGGLE, false)) {
                try {
                    mRnFragment = Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN).getFragmentAction()
                            .newRNFragment("rntest", args);
                } catch (Exception e) {
                    Logger.e("NovelFragment", "e = " + e);
                    e.printStackTrace();
                }
            } else {
                try {
                    mRnFragment = Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN).getFragmentAction()
                            .newRNFragment("rn", args, fragment -> {
                                return false;
                            });
                } catch (Exception e) {
                    Logger.e("NovelFragment", "e = " + e);
                    e.printStackTrace();
                }
            }
            addFragment(getActivity(), mRnFragment, 0, 0, R.id.main_novel_rn_container);
        });
    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_novel;
    }

    private void addFragment(FragmentActivity activity, BaseFragment fragment,
                             int inAnim, int outAnim, int containerId) {
        if (fragment == null || fragment.isAddFix())
            return;

        if (activity == null || activity.isFinishing() || activity.isDestroyed()) return;

        fragment.setIsAdd(true);
        FragmentManager manager = getChildFragmentManager();
        if (manager == null) {
            return;
        }
        FragmentTransaction transaction = manager.beginTransaction();
        if (inAnim != 0 && outAnim != 0) {
            transaction.setCustomAnimations(inAnim, outAnim, inAnim, outAnim);
        }
        transaction.replace(containerId, fragment, "novel_webview_container");
        transaction.commitAllowingStateLoss();
    }

    @Override
    public boolean enableAdapt() {
        return false;
    }
}
