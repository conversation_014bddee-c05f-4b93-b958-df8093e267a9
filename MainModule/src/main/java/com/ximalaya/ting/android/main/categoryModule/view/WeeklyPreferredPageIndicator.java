package com.ximalaya.ting.android.main.categoryModule.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.main.R;

import java.util.ArrayList;
import java.util.List;

public class WeeklyPreferredPageIndicator extends LinearLayout {

    private int mPageSize;
    private int mCurSelectedIndex;
    private List<View> mViews = new ArrayList<>();

    public WeeklyPreferredPageIndicator(Context context) {
        this(context, null);
    }

    public WeeklyPreferredPageIndicator(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WeeklyPreferredPageIndicator(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setGravity(Gravity.CENTER_HORIZONTAL);
    }

    private void initViews() {
        removeAllViews();
        mViews.clear();
        mCurSelectedIndex = 0;
        for (int i = 0; i < mPageSize; i++) {
            View view = createView();
            updateViewStatus(view, i == mCurSelectedIndex);
            mViews.add(view);
            addView(view);
        }
    }

    private void updateViewStatus(View view, boolean isSelected) {
        view.setBackgroundResource(isSelected ? R.drawable.main_weekly_preferred_page_indicator_selected
                : R.drawable.main_weekly_preferred_page_indicator_normal);
        view.getLayoutParams().width = BaseUtil.dp2px(getContext(), 15);
        view.setLayoutParams(view.getLayoutParams());
    }

    private View createView() {
        return LayoutInflater.from(getContext()).inflate(
                R.layout.main_view_weekly_preferred_page_indicator, this, false);
    }

    public void setPageSize(int pageSize) {
        if (mPageSize != pageSize) {
            mPageSize = pageSize;
            initViews();
        }
    }

    public void setCurSelectedIndex(int index) {
        mCurSelectedIndex = index;
        for (int i = 0; i < mViews.size(); i++) {
            View view = mViews.get(i);
            updateViewStatus(view, mCurSelectedIndex == i);
        }

    }
}
