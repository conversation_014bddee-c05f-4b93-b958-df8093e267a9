package com.ximalaya.ting.android.main.model.album;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.model.album.AlbumM;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 2017/10/29.
 * <AUTHOR>
 */

public class SingleListenNote {
    private String moduleTitle;
    @SerializedName("coverPathBig")
    private String coverPath;
    @SerializedName("footnote")
    private String footnote;
    @SerializedName("specialId")
    private int specialId;
    @SerializedName("noteTitle")
    private String noteTitle;
    @SerializedName("albums")
    private List<AlbumM> albumMList;
    @SerializedName("playCount")
    private long playCount;
    @SerializedName("id")
    private long id;
    @SerializedName("jumpAllUrl")
    private String jumpAllUrl;
    @SerializedName("contentType")
    private int contentType; // 1 专辑 2 声音

    public transient String xmRequestId;

    public SingleListenNote(JSONObject jObj) {
        try {
            JSONArray jsonArray = jObj.optJSONArray("albums");
            if (jsonArray != null && jsonArray.length() > 0) {
                albumMList = new ArrayList<>();
                coverPath = jObj.optString("coverPathBig");
                footnote = jObj.optString("footnote");
                specialId = jObj.optInt("specialId");
                noteTitle = jObj.optString("noteTitle");
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject obj = jsonArray.optJSONObject(i);
                    AlbumM albumM = new AlbumM(obj.toString());
                    albumMList.add(albumM);
                }
                playCount = jObj.optLong("playCount");
                id = jObj.optLong("id");
                jumpAllUrl = jObj.optString("jumpAllUrl");
                contentType = jObj.optInt("contentType");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public String getCoverPath() {
        return coverPath;
    }

    public String getFootnote() {
        return footnote;
    }

    public String getNoteTitle() {
        return noteTitle;
    }

    public int getSpecialId() {
        return specialId;
    }

    public List<AlbumM> getAlbumMList() {
        return albumMList;
    }

    public String getModuleTitle() {
        return moduleTitle;
    }

    public void setModuleTitle(String moduleTitle) {
        this.moduleTitle = moduleTitle;
    }

    public long getPlayCount() {
        return playCount;
    }

    public void setPlayCount(long playCount) {
        this.playCount = playCount;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getJumpAllUrl() {
        return jumpAllUrl;
    }

    public void setJumpAllUrl(String jumpAllUrl) {
        this.jumpAllUrl = jumpAllUrl;
    }

    public int getContentType() {
        return contentType;
    }

    public void setContentType(int contentType) {
        this.contentType = contentType;
    }
}
