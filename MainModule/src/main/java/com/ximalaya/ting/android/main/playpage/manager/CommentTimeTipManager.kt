package com.ximalaya.ting.android.main.playpage.manager

import android.graphics.drawable.GradientDrawable
import android.text.SpannableString
import android.text.Spanned
import android.text.style.UnderlineSpan
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.play.CommentModel
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.util.CommentTraceUtil
import com.ximalaya.ting.android.main.view.comment.util.CommentListUtil
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Created on 2024/7/26.
 * <AUTHOR>
 * @email <EMAIL>
 */
class CommentTimeTipManager(private var rootView: View?, private var listener: OnClickListener?) {
    private var tvTime: TextView? = null
    private var tvTimeTitle: TextView? = null
    private var commentId: Long = 0
    private var commentPlayPosition: Long = 0
    private var traceInfo: CommentTipTraceInfo? = null

    companion object {
        const val MMKV_KEY_TIP_SHOW_HASED_DAY = "MMKV_KEY_TIP_SHOW_HASED_DAY_"
    }

    init {
        tvTime = rootView?.findViewById(R.id.main_tv_time)
        tvTimeTitle = rootView?.findViewById(R.id.main_tv_time_title)
        tvTime?.setOnClickListener {
            listener?.onCommentTimeTipClick(commentId, commentPlayPosition)
            hideCommentTimeTip()
            traceInfo?.let {
                CommentTraceUtil.clickTimeTip64580(it.currPage,
                    it.currTrackId,
                    it.currAlbumId,
                    it.requestId,
                    it.playStatus,
                    it.trackDuration,
                    it.progressBarTime)
            }
        }
    }

    private val delayHideTimeTipRunnable: Runnable = Runnable {
        rootView?.visibility = View.GONE
    }

    fun showCommentTimeTip(
        color: Int,
        timeStr: String,
        model: CommentModel?,
        isByUserClick: Boolean,
        playPosition: Long = 0,
        traceInfo: CommentTipTraceInfo? = null,
        isAbsorbColor: Boolean = false
    ) {
        this.traceInfo = traceInfo
        if (!isByUserClick && !isNeedShow()) {
            // 非用户点击&&当天已经出现过了
            return
        }
        saveCommentTipShowStateDay()
        this.commentId = model?.id ?: 0
        this.commentPlayPosition = playPosition
        val spanStr = SpannableString(timeStr)
        spanStr.setSpan(UnderlineSpan(), 0, timeStr.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        tvTime?.setTextColor(color)
        tvTime?.text = spanStr
        rootView?.visibility = View.VISIBLE
        if (isAbsorbColor) {
            tvTimeTitle?.setTextColor(
                ContextCompat.getColor(
                    ToolUtil.getCtx(),
                    R.color.main_color_ffffff
                )
            )
            GradientDrawable().apply {
                cornerRadius = 8.dp.toFloat()
                setColor(ContextCompat.getColor(
                    ToolUtil.getCtx(),
                    R.color.host_color_c0000000
                ))
                rootView?.background = this
            }
        }
        HandlerManager.removeCallbacks(delayHideTimeTipRunnable)
        HandlerManager.postOnUIThreadDelay(delayHideTimeTipRunnable, CommentListUtil.getCommentTipShowDuration().toLong() * 1000)
        traceInfo?.let {
            CommentTraceUtil.traceTimeTipShow64581(
                it.currPage,
                it.currTrackId,
                it.currAlbumId,
                it.requestId,
                it.playStatus,
                it.trackDuration,
                it.progressBarTime
            )
        }
    }

    fun hideCommentTimeTip() {
        HandlerManager.removeCallbacks(delayHideTimeTipRunnable)
        if (rootView != null) {
            rootView?.visibility = View.GONE
        }
    }

    interface OnClickListener {
        fun onCommentTimeTipClick(commentId: Long, commentPlayPosition: Long)
    }

    private fun isNeedShow(): Boolean {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val todayString = dateFormat.format(Date())
        return !MMKVUtil.getInstance().getBoolean(MMKV_KEY_TIP_SHOW_HASED_DAY + todayString, false)
    }

    private fun saveCommentTipShowStateDay() {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val todayString = dateFormat.format(Date())
        MMKVUtil.getInstance().saveBoolean(MMKV_KEY_TIP_SHOW_HASED_DAY + todayString, true)
    }
}

data class CommentTipTraceInfo(
    val currPage: String? = "",
    val currTrackId: String? = "",
    val currAlbumId: String? = "",
    val requestId: String? = "",
    val playStatus: String? = "",
    val trackDuration: String? = "",
    val progressBarTime: String? = ""
)