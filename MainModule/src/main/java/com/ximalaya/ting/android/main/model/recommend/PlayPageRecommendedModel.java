package com.ximalaya.ting.android.main.model.recommend;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.model.ad.AnchorAlbumAd;

import java.util.List;

/**
 * Created by roc on 2022/10/27.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 19512388102
 */
public class PlayPageRecommendedModel {


    @SerializedName("associationTracks")
    private List<AssociationTracks> associationTracks;
    @SerializedName("associationAlbumsInfo")
    private List<AssociationAlbumsInfo> associationAlbumsInfo;
    @SerializedName("offset")
    private int offset;

    public List<AssociationTracks> getAssociationTracks() {
        return associationTracks;
    }

    public void setAssociationTracks(List<AssociationTracks> associationTracks) {
        this.associationTracks = associationTracks;
    }

    public List<AssociationAlbumsInfo> getAssociationAlbumsInfo() {
        return associationAlbumsInfo;
    }

    public void setAssociationAlbumsInfo(List<AssociationAlbumsInfo> associationAlbumsInfo) {
        this.associationAlbumsInfo = associationAlbumsInfo;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public class AssociationTracks {
        private Object traceId;
        private String coverLarge;
        private String coverSmall;
        private String coverMiddle;
        private String reasonContent;
        private long lastTrackUpdateTime;
        private long trackId;
        private long albumId;
        private String title;
        private long duration;
        private long playCount;
        private String recSrc;
        private String recTrack;
        private String albumTitle;
        private RecommendAdInfo adInfo;
        private int moduleType; // 1 内容. 2 大图广告, 3 小图广告
        private boolean isAdClosed = false;

        public String getAlbumTitle() {
            return albumTitle;
        }

        public void setAlbumTitle(String albumTitle) {
            this.albumTitle = albumTitle;
        }

        public Object getTraceId() {
            return traceId;
        }

        public void setTraceId(Object traceId) {
            this.traceId = traceId;
        }

        public String getCoverLarge() {
            return coverLarge;
        }

        public void setCoverLarge(String coverLarge) {
            this.coverLarge = coverLarge;
        }

        public String getCoverSmall() {
            return coverSmall;
        }

        public void setCoverSmall(String coverSmall) {
            this.coverSmall = coverSmall;
        }

        public String getCoverMiddle() {
            return coverMiddle;
        }

        public void setCoverMiddle(String coverMiddle) {
            this.coverMiddle = coverMiddle;
        }

        public String getReasonContent() {
            return reasonContent;
        }

        public void setReasonContent(String reasonContent) {
            this.reasonContent = reasonContent;
        }

        public long getLastTrackUpdateTime() {
            return lastTrackUpdateTime;
        }

        public void setLastTrackUpdateTime(long lastTrackUpdateTime) {
            this.lastTrackUpdateTime = lastTrackUpdateTime;
        }

        public long getTrackId() {
            return trackId;
        }

        public void setTrackId(long trackId) {
            this.trackId = trackId;
        }

        public long getAlbumId() {
            return albumId;
        }

        public void setAlbumId(long albumId) {
            this.albumId = albumId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public long getDuration() {
            return duration;
        }

        public void setDuration(long duration) {
            this.duration = duration;
        }

        public long getPlayCount() {
            return playCount;
        }

        public void setPlayCount(long playCount) {
            this.playCount = playCount;
        }

        public String getRecSrc() {
            return recSrc;
        }

        public void setRecSrc(String recSrc) {
            this.recSrc = recSrc;
        }

        public String getRecTrack() {
            return recTrack;
        }

        public void setRecTrack(String recTrack) {
            this.recTrack = recTrack;
        }

        public RecommendAdInfo getAdInfo() {
            return adInfo;
        }

        public void setAdInfo(RecommendAdInfo adInfo) {
            this.adInfo = adInfo;
        }

        public int getModuleType() {
            return moduleType;
        }

        public void setModuleType(int moduleType) {
            this.moduleType = moduleType;
        }

        public boolean isAdClosed() {
            return isAdClosed;
        }

        public void setAdClosed(boolean adClosed) {
            isAdClosed = adClosed;
        }
    }

    public class AssociationAlbumsInfo {
        private Object traceId;
        private String coverMiddle;
        private String albumSubscript;
        private String reasonContent;
        private long albumId;
        private String title;
        private String coverSmall;
        private float score;
        private String recSrc;
        private long playsCounts;
        private String recTrack;
        private String tag;
        private String coverLarge;
        private RecommendAdInfo adInfo;
        private int moduleType; // 1 内容. 2 大图广告, 3 小图广告

        public Object getTraceId() {
            return traceId;
        }

        public void setTraceId(Object traceId) {
            this.traceId = traceId;
        }

        public String getCoverMiddle() {
            return coverMiddle;
        }

        public void setCoverMiddle(String coverMiddle) {
            this.coverMiddle = coverMiddle;
        }

        public String getAlbumSubscript() {
            return albumSubscript;
        }

        public void setAlbumSubscript(String albumSubscript) {
            this.albumSubscript = albumSubscript;
        }

        public String getReasonContent() {
            return reasonContent;
        }

        public void setReasonContent(String reasonContent) {
            this.reasonContent = reasonContent;
        }

        public long getAlbumId() {
            return albumId;
        }

        public void setAlbumId(long albumId) {
            this.albumId = albumId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getCoverSmall() {
            return coverSmall;
        }

        public void setCoverSmall(String coverSmall) {
            this.coverSmall = coverSmall;
        }

        public float getScore() {
            return score;
        }

        public void setScore(float score) {
            this.score = score;
        }

        public String getRecSrc() {
            return recSrc;
        }

        public void setRecSrc(String recSrc) {
            this.recSrc = recSrc;
        }

        public long getPlaysCounts() {
            return playsCounts;
        }

        public void setPlaysCounts(long playsCounts) {
            this.playsCounts = playsCounts;
        }

        public String getRecTrack() {
            return recTrack;
        }

        public void setRecTrack(String recTrack) {
            this.recTrack = recTrack;
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public String getCoverLarge() {
            return coverLarge;
        }

        public void setCoverLarge(String coverLarge) {
            this.coverLarge = coverLarge;
        }

        public RecommendAdInfo getAdInfo() {
            return adInfo;
        }

        public void setAdInfo(RecommendAdInfo adInfo) {
            this.adInfo = adInfo;
        }

        public int getModuleType() {
            return moduleType;
        }

        public void setModuleType(int moduleType) {
            this.moduleType = moduleType;
        }
    }

    public static class RecommendAdInfo {
        private AnchorAlbumAd adData;
        private String coverMiddle;
        private String coverLarge;
        private String coverSmall;
        private String albumTitle;
        private String albumSubTitle;
        private String trackTitle;
        private long playsCount;
        private int trackDuration;
        private double albumScore;
        private int listType; // 0: 专辑， 1：声音
        private boolean isAdClosed;

        public AnchorAlbumAd getAdData() {
            return adData;
        }

        public void setAdData(AnchorAlbumAd adData) {
            this.adData = adData;
        }

        public String getCoverMiddle() {
            return coverMiddle;
        }

        public void setCoverMiddle(String coverMiddle) {
            this.coverMiddle = coverMiddle;
        }

        public String getCoverLarge() {
            return coverLarge;
        }

        public void setCoverLarge(String coverLarge) {
            this.coverLarge = coverLarge;
        }

        public String getCoverSmall() {
            return coverSmall;
        }

        public void setCoverSmall(String coverSmall) {
            this.coverSmall = coverSmall;
        }

        public String getAlbumTitle() {
            return albumTitle;
        }

        public void setAlbumTitle(String albumTitle) {
            this.albumTitle = albumTitle;
        }

        public String getAlbumSubTitle() {
            return albumSubTitle;
        }

        public void setAlbumSubTitle(String albumSubTitle) {
            this.albumSubTitle = albumSubTitle;
        }

        public String getTrackTitle() {
            return trackTitle;
        }

        public void setTrackTitle(String trackTitle) {
            this.trackTitle = trackTitle;
        }

        public long getPlaysCount() {
            return playsCount;
        }

        public void setPlaysCount(long playsCount) {
            this.playsCount = playsCount;
        }

        public int getTrackDuration() {
            return trackDuration;
        }

        public void setTrackDuration(int trackDuration) {
            this.trackDuration = trackDuration;
        }

        public double getAlbumScore() {
            return albumScore;
        }

        public void setAlbumScore(double albumScore) {
            this.albumScore = albumScore;
        }

        public int getListType() {
            return listType;
        }

        public void setListType(int listType) {
            this.listType = listType;
        }

        public boolean isAdClosed() {
            return isAdClosed;
        }

        public void setAdClosed(boolean adClosed) {
            isAdClosed = adClosed;
        }
    }

}
