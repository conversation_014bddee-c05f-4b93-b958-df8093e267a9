package com.ximalaya.ting.android.main.model.pay;

import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.util.common.ToolUtil;

import java.io.Serializable;

public class WholeAlbumPurchaseChannelBuyAlbum implements Serializable {
    public static final String RN_ORDER_PARAMS_KEY_BUNDLE = "bundle";
    public static final String RN_ORDER_PARAMS_KEY_PAGE_NAME = "pageName";
    public static final String RN_ORDER_PARAMS_KEY_TYPE = "type";
    public static final String RN_ORDER_PARAMS_KEY_ITEM_ID = "itemId";
    public static final String RN_ORDER_PARAMS_KEY_DOMAIN = "domain";
    public static final String RN_ORDER_PARAMS_KEY_BUSINESS_TYPE_ID = "businessTypeId";
    public static final String RN_ORDER_PARAMS_KEY_PRICE_TYPE_ENUM = "priceTypeEnum";
    public static final String RN_ORDER_PARAMS_KEY_CONTEXT = "context";
    public static final String RN_ORDER_PARAMS_KEY_ITEMS = "items";

    @SerializedName("behavior")
    public BuyAlbumBehavior behavior;
    @SerializedName("price")
    public WholeAlbumPurchasePrice price;

    public static class BuyAlbumBehavior extends WholeAlbumPurchaseBehavior {
        @SerializedName("orderInfo")
        public JsonObject orderParams;
        @SerializedName("buttonText")
        public String buttonText;
    }

    /**
     * 是否可以是用兑换券直接兑换此专辑
     * */
    public boolean isCouponGetDirectly() {
        if (null == price || ToolUtil.isEmptyCollects(price.supportPromotions)) {
            return false;
        }
        for (WholeAlbumPurchasePrice.SupportPromotion promotion : price.supportPromotions) {
            if (null == promotion) {
                continue;
            }
            if (WholeAlbumPurchasePrice.SupportPromotion.TYPE_EXCHANGE.equals(promotion.promotionType)) {
                return true;
            }
        }
        return false;
    }
}
