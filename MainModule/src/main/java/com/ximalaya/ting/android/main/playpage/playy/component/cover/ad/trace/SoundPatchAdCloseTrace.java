package com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.trace;

import android.os.Build;

import com.ximalaya.ting.android.adsdk.XmAdSDK;
import com.ximalaya.ting.android.adsdk.base.log.AdLogger;
import com.ximalaya.ting.android.adsdk.bridge.importsdk.IXmLogger;
import com.ximalaya.ting.android.adsdk.bridge.importsdk.ImportSDKHelper;
import com.ximalaya.ting.android.adsdk.util.ContextUtils;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.util.ADABTestUtil;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class SoundPatchAdCloseTrace {
    private static final String XLOG_TYPE = "XmAd";

    private static String lastLogStr;

    public static void traceEvent(int padding,
                                  int realWidth,
                                  int realHeight,
                                  Advertis advertis) {
        HashMap<String, String> commonData = new HashMap<>();
        commonData.put("type", "AdClose");
        HashMap<String, String> optData = new HashMap<>();
        optData.put("padding", String.valueOf(padding));
        optData.put("realWidth", String.valueOf(realWidth));
        optData.put("realHeight", String.valueOf(realHeight));
        report(optData, commonData, advertis);
    }

    public static void traceEvent(String paddingJson,
                                  int realWidth,
                                  int realHeight,
                                  Advertis advertis) {
        HashMap<String, String> commonData = new HashMap<>();
        commonData.put("type", "AdClose");
        HashMap<String, String> optData = new HashMap<>();
        optData.put("padding", paddingJson);
        optData.put("realWidth", String.valueOf(realWidth));
        optData.put("realHeight", String.valueOf(realHeight));
        report(optData, commonData, advertis);
    }

    public static void traceEvent(String message,
                                  Advertis advertis) {
        HashMap<String, String> commonData = new HashMap<>();
        commonData.put("type", "AdClose");
        HashMap<String, String> optData = new HashMap<>();
        optData.put("message", message);
        report(optData, commonData, advertis);
    }

    private static void report(Map<String, String> optdata, Map<String, String> commonData, Advertis advertis) {
        boolean enable = ADABTestUtil.getSoundPatchAdCloseTraceEnable();
        if (!enable) {
            return;
        }
        IXmLogger builder = ImportSDKHelper.newXmLogger(XLOG_TYPE, "adTrace");
        try {
            if (optdata == null) {
                optdata = new HashMap<>();
            }
            if (optdata != null) {
                JSONObject jsonObject = new JSONObject();
                Iterator<Map.Entry<String, String>> iterator = optdata.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, String> next = iterator.next();
                    String key = next.getKey();
                    String value = next.getValue();
                    jsonObject.put(key, value);
                }
                builder.put("opt", jsonObject.toString());
            }
        } catch (Throwable e) {
        }
        try {
            if (commonData != null) {
                Iterator<Map.Entry<String, String>> iterator = commonData.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, String> next = iterator.next();
                    String key = next.getKey();
                    String value = next.getValue();
                    builder.put(key, value);
                }
            }
        } catch (Throwable e) {
        }
        if (advertis != null) {
            try {
                builder.put("planid", advertis.getPlanId() + "");
                builder.put("trackId", advertis.getTrackId() + "");
                builder.put("downloadAppname", advertis.getDownloadAppName());
                builder.put("downloadPkgname", advertis.getAppPackageName());
                builder.put("responseid", advertis.getResponseId() + "");
                builder.put("isdsp", AdManager.isThirdAd(advertis) + "");
                builder.put("dspPositionId", advertis.getDspPositionId());
                builder.put("adType", advertis.getAdtype() + "");
                builder.put("adItemId", advertis.getAdid() + "");
            } catch (Throwable e) {
                builder.put("exception", e.toString());
            }
        }
        try {
            builder.put("os", "Android");
            builder.put("os_version", Build.VERSION.RELEASE);
            builder.put("app_vn", ContextUtils.getAppVersionName());
            builder.put("app_vc", ContextUtils.getAppVersionCode() + "");
            if (XmAdSDK.getInstance().getAdConfig().getXmSelfConfig() != null) {
                String uid = XmAdSDK.getInstance().getAdConfig().getXmSelfConfig().uid();
                if (!"0".equals(uid)) {
                    builder.put("uid", uid);
                }
            }
        } catch (Throwable e) {
        }

        if (XmAdSDK.getInstance().getAdConfig() != null) {
            builder.put("adAppId", XmAdSDK.getInstance().getAdConfig().getAppId());
        }
        String logContent = builder.getLogContent();
        if (AdLogger.isDebug) {
            AdLogger.log("SoundPatchAdClose=" + builder.getLogContent());
        }
        if (logContent != null && logContent.equals(lastLogStr)){
            return;
        } else {
            lastLogStr = logContent;
        }
        builder.toLog();
    }
}
