package com.ximalaya.ting.android.main.model.commerical;

import android.os.Parcel;
import android.os.Parcelable;

public class FindPartnerInfo implements Parcelable {
    public long categoryId;
    public String categoryTitle;
    public int commentCount;
    public String contentDesc; // 搭子要求
    public long fansCount; // 粉丝数
    public String logoPic; // 主播头像
    public int month;
    public int mvpGrade; // 主播创作者等级
    private long myclubScheduleId; //myclub活动ID
    private Integer myclubState; //myclub状态；0-不存在；1-未开启；2-热聊中；3-即将开聊；4-结束可再次创建；5-永久结束
    public String nickname; // nickname
    public int status; // 0-未审核；1-已审核；2-审核失败；3-已完成
    public long talkId; // 话题卡片id
    public String tenDay; // 旬
    public String title; // 主题
    public long uid;
    public int year;
    public String sharingUrl; // 分享地址

    public long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryTitle() {
        return categoryTitle;
    }

    public void setCategoryTitle(String categoryTitle) {
        this.categoryTitle = categoryTitle;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public String getContentDesc() {
        return contentDesc;
    }

    public void setContentDesc(String contentDesc) {
        this.contentDesc = contentDesc;
    }

    public long getFansCount() {
        return fansCount;
    }

    public void setFansCount(long fansCount) {
        this.fansCount = fansCount;
    }

    public String getLogoPic() {
        return logoPic;
    }

    public void setLogoPic(String logoPic) {
        this.logoPic = logoPic;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getMvpGrade() {
        return mvpGrade;
    }

    public void setMvpGrade(int mvpGrade) {
        this.mvpGrade = mvpGrade;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getTalkId() {
        return talkId;
    }

    public void setTalkId(long talkId) {
        this.talkId = talkId;
    }

    public String getTenDay() {
        return tenDay;
    }

    public void setTenDay(String tenDay) {
        this.tenDay = tenDay;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public String getSharingUrl() {
        return sharingUrl;
    }

    public void setSharingUrl(String sharingUrl) {
        this.sharingUrl = sharingUrl;
    }

    protected FindPartnerInfo(Parcel in) {
        categoryId = in.readLong();
        categoryTitle = in.readString();
        commentCount = in.readInt();
        contentDesc = in.readString();
        fansCount = in.readLong();
        logoPic = in.readString();
        month = in.readInt();
        mvpGrade = in.readInt();
        nickname = in.readString();
        status = in.readInt();
        talkId = in.readLong();
        tenDay = in.readString();
        title = in.readString();
        uid = in.readLong();
        year = in.readInt();
        sharingUrl = in.readString();
    }

    public static final Creator<FindPartnerInfo> CREATOR = new Creator<FindPartnerInfo>() {
        @Override
        public FindPartnerInfo createFromParcel(Parcel in) {
            return new FindPartnerInfo(in);
        }

        @Override
        public FindPartnerInfo[] newArray(int size) {
            return new FindPartnerInfo[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(categoryId);
        dest.writeString(categoryTitle);
        dest.writeInt(commentCount);
        dest.writeString(contentDesc);
        dest.writeLong(fansCount);
        dest.writeString(logoPic);
        dest.writeInt(month);
        dest.writeInt(mvpGrade);
        dest.writeString(nickname);
        dest.writeInt(status);
        dest.writeLong(talkId);
        dest.writeString(tenDay);
        dest.writeString(title);
        dest.writeLong(uid);
        dest.writeInt(year);
        dest.writeString(sharingUrl);
    }

    public Integer getMyclubState() {
        return myclubState;
    }

    public void setMyclubState(Integer myclubState) {
        this.myclubState = myclubState;
    }

    public long getMyclubScheduleId() {
        return myclubScheduleId;
    }

    public void setMyclubScheduleId(long myclubScheduleId) {
        this.myclubScheduleId = myclubScheduleId;
    }
}

