package com.ximalaya.ting.android.main.model.category;


import android.os.Parcel;
import android.os.Parcelable;

import com.ximalaya.ting.android.host.util.common.ToolUtil;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SimpleAggregateRankBean implements Parcelable{
    private long rankClusterId;
    private long rankingListId;
    private long categoryId;
    private String displayName;
    private String contentType;
    private String rankingRule;

    protected SimpleAggregateRankBean(Parcel in) {
        rankClusterId = in.readLong();
        rankingListId = in.readLong();
        categoryId = in.readLong();
        displayName = in.readString();
        contentType = in.readString();
        rankingRule = in.readString();
    }

    public long getRankClusterId() {
        return rankClusterId;
    }

    public void setRankClusterId(long rankClusterId) {
        this.rankClusterId = rankClusterId;
    }

    public long getRankingListId() {
        return rankingListId;
    }

    public void setRankingListId(long rankingListId) {
        this.rankingListId = rankingListId;
    }

    public long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(long categoryId) {
        this.categoryId = categoryId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getRankingRule() {
        return rankingRule;
    }

    public void setRankingRule(String rankingRule) {
        this.rankingRule = rankingRule;
    }

    @Override
    public String toString() {
        return "SimpleAggregateRankBean{" +
                "rankClusterId=" + rankClusterId +
                ", rankingListId=" + rankingListId +
                ", categoryId=" + categoryId +
                ", displayName='" + displayName + '\'' +
                ", contentType='" + contentType + '\'' +
                ", rankingRule='" + rankingRule + '\'' +
                '}';
    }

    public static SimpleAggregateRankBean getById(long rankingListId, List<SimpleAggregateRankBean> rankBeans) {
        if (ToolUtil.isEmptyCollects(rankBeans)) {
            return null;
        }
        for (SimpleAggregateRankBean bean : rankBeans) {
            if (bean.getRankingListId() == rankingListId) {
                return bean;
            }
        }
        return null;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(rankClusterId);
        dest.writeLong(rankingListId);
        dest.writeLong(categoryId);
        dest.writeString(displayName);
        dest.writeString(contentType);
        dest.writeString(rankingRule);
    }

    public static final Creator<SimpleAggregateRankBean> CREATOR = new Creator<SimpleAggregateRankBean>() {
        @Override
        public SimpleAggregateRankBean createFromParcel(Parcel in) {
            return new SimpleAggregateRankBean(in);
        }

        @Override
        public SimpleAggregateRankBean[] newArray(int size) {
            return new SimpleAggregateRankBean[size];
        }
    };
}
