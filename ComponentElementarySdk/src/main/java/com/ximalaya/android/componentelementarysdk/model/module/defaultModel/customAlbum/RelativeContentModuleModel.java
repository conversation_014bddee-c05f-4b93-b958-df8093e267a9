package com.ximalaya.android.componentelementarysdk.model.module.defaultModel.customAlbum;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;
import com.ximalaya.android.componentelementarysdk.model.module.BaseModuleModel;
import com.ximalaya.android.componentelementarysdk.util.SdkBaseUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class RelativeContentModuleModel extends BaseModuleModel {
    @SerializedName("contents")
    public List<ContentItem> contents;                      // 内容列表，如果列表为空，客户端隐藏模块

    public List<ContentItem> localTopAreaContents;
    public List<ContentItem> localBottomAreaContents;

    public int localCurrentPosition = 0;

    @Override
    public BaseModuleModel parseDataFromJson(JsonObject jsonObject) {
        try {
            RelativeContentModuleModel result = new Gson().fromJson(jsonObject.toString(), RelativeContentModuleModel.class);

            if (null != result && null != result.contents) {
                for (ContentItem item : result.contents) {
                    if (null == item) {
                        continue;
                    }
                    if (ContentItem.CONTENT_TYPE_LISTEN_LIST.equals(item.contentType)) {
                        if (null == result.localBottomAreaContents) {
                            result.localBottomAreaContents = new ArrayList<>();
                        }
                        result.localBottomAreaContents.add(item);
                    } else {
                        if (null == result.localTopAreaContents) {
                            result.localTopAreaContents = new ArrayList<>();
                        }
                        result.localTopAreaContents.add(item);
                    }
                }
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return this;
    }

    public static class ContentItem implements Serializable {
        public static final String CONTENT_TYPE_ALBUM = "album";
        public static final String CONTENT_TYPE_TRACK = "track";
        public static final String CONTENT_TYPE_LISTEN_LIST = "native_listen_list";
        public static final String CONTENT_TYPE_PRODUCT = "product";

        @SerializedName("contentType")
        public String contentType;                          // 内容类型，album:专辑，track:声音
                                                            // native_listen_list：native听单
                                                            // product:商品
        @SerializedName("contentId")
        public long contentId;                              // 内容id，专辑类型是专辑id，声音类型是声音id
                                                            // 商品是商品id，听单是听单id
        @SerializedName("title")
        public String title;                                // 内容标题，目前所有类型都有
        @SerializedName("briefDesc")
        public String briefDesc;                            // 简介，只有听单类型有
        @SerializedName("url")
        public String url;                                  // 落地页地址，有的话优先用这个,没有就是默认的
                                                            // 专辑类型默认跳专辑页，声音类型默认跳播放页
                                                            // 听单默认跳听单落地页，没有默认的不支持跳转
        @SerializedName("coverPath")
        public String coverPath;                            // 封面图片，除了听单类型其他都有
        @SerializedName("price")
        public String price;                                // 价格，只有商品类型有
        @SerializedName("isMultipleItem")
        public boolean isMultipleItem;                                // 只有商品类型有, 是否有多个商品，如果是，price就是最低价, 否则说明只有一个价格
        @SerializedName("tag")
        public String tag;                                  // 标签，除了听单类型其他都有
        @SerializedName("listenlistContents")
        public List<ListenListContentItem> listenListContents;// 听单内容，不超过3个

        public String getJumpUrl() {
            if (!TextUtils.isEmpty(url)) {
                return url;
            }
            if (CONTENT_TYPE_LISTEN_LIST.equals(contentType)) {
                // opType
                int opType = 0;
                ListenListContentItem item = SdkBaseUtil.Common.Companion.safelyGetItemFromList(listenListContents, 0);
                if (null != item) {
                    if (ListenListContentItem.LISTEN_LIST_CONTENT_TYPE_TRACK.equals(item.contentType)) {
                        opType = 2;
                    } else if (ListenListContentItem.LISTEN_LIST_CONTENT_TYPE_ALBUM.equals(item.contentType)) {
                        opType = 3;
                    }
                }
                return "iting://open?msg_type=146&album_id=" + contentId + "&opType=" + opType;
            }
            if (CONTENT_TYPE_ALBUM.equals(contentType)) {
                return "iting://open?msg_type=13&album_id=" + contentId;
            }
            if (CONTENT_TYPE_TRACK.equals(contentType)) {
                return "iting://open?msg_type=11&track_id=" + contentId;
            }
            return null;
        }
    }

    public static class ListenListContentItem implements Serializable {
        public static final String LISTEN_LIST_CONTENT_TYPE_ALBUM = "album";
        public static final String LISTEN_LIST_CONTENT_TYPE_TRACK = "track";
        @SerializedName("contentType")
        public String contentType;                          // 内容类型，album:专辑，track:声音
        @SerializedName("contentId")
        public long contentId;                              // 内容id，专辑类型是专辑id，声音类型是声音id
        @SerializedName("coverPath")
        public String coverPath;                            // 封面图片
    }
}
