package com.ximalaya.android.componentelementarysdk.creator

import android.view.View
import com.ximalaya.android.componentelementarysdk.model.config.BaseConfigModel
import com.ximalaya.android.componentelementarysdk.model.module.BaseModuleModel
import com.ximalaya.android.componentelementarysdk.model.pageInfo.PageInfo

/**
 * Created by 5Greatest on 2021.11.02
 *
 * <AUTHOR>
 *   On 2021/11/2
 */
interface ICreatorListenRelativeFunction {
    fun setListenOnView(view: View?, baseConfigModuleModel: BaseConfigModel?, baseDataModuleModel: BaseModuleModel?, pageInfo: PageInfo): View?
}