package com.ximalaya.ting.android.live.host.components.redpacketrain

import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.live.common.lib.base.util.LiveCommonITingUtil
import com.ximalaya.ting.android.live.common.lib.entity.BaseRoomDetail
import com.ximalaya.ting.android.live.common.lib.entity.LiveSpringFestivalMessage
import com.ximalaya.ting.android.live.common.lib.utils.LiveTimeUtil
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants
import com.ximalaya.ting.android.live.host.R
import com.ximalaya.ting.android.live.host.components.BaseLiveComponent
import com.ximalaya.ting.android.live.host.components.IBaseLiveHostInteraction
import com.ximalaya.ting.android.live.host.view.LiveRedPacketRainDialog

/**
 * 春节打年兽玩法红包雨组件.
 * <AUTHOR>
 * @email  <EMAIL>
 */
class LiveRedPacketRainComponent : BaseLiveComponent<BaseRoomDetail>(),
    ILiveRedPacketRainComponent {

    private var countDownTv: TextView? = null
    private var redPacketRainIv: ImageView? = null
    private var redPacketRainLayout: ConstraintLayout? = null
    private var popupWindow: PopupWindow? = null
    private var dialog: LiveRedPacketRainDialog? = null

    private var mCountDownTimer: CountDownTimer? = null
    private var mNewYearActivityData: LiveSpringFestivalMessage? = null
    private var mCountDownSecond: Long = 0L

    override fun initComponentViewAfterInflated(view: View) {
        super.initComponentViewAfterInflated(view)
        countDownTv = findViewById(R.id.red_packet_rain_count_tv)
        redPacketRainIv = findViewById(R.id.red_packet_rain_iv)
        redPacketRainLayout = findViewById(viewStubId)

        redPacketRainIv?.setOnClickListener {
            if (isFull) {
                val interaction = getComponentInteractionSafety(
                    IBaseLiveHostInteraction::class.java)
                interaction?.requestPlayMode(PlayerConstants.PLAYMODE_WINDOW)
            }
            showRedPacketRainDialog()
        }
    }

    private fun showRedPacketRainDialog() {
        // 展示弹窗
        if (dialog?.isShowing == true) {
            return
        }
        dialog = LiveRedPacketRainDialog.newInstance(
            mNewYearActivityData?.petIcon ?: "",
            mNewYearActivityData?.petName ?: "",
            mCountDownSecond
        )
        childFragmentManager?.let {
            dialog?.startCountDownTimer(mCountDownSecond)
            dialog?.show(it, "LiveRedPacketRainDialog")
        }

    }

    private fun loadRedPacketRainData(data: LiveSpringFestivalMessage) {
        redPacketRainLayout?.visibility = View.VISIBLE
        mNewYearActivityData = data
        startCountDownTimer(data.packetRemainTime, data.packetRainUrl)
    }

    private fun startCountDownTimer(packetRemainTime: Long?, packetRainUrl: String?) {
        packetRemainTime ?: return
        mCountDownTimer?.cancel()
        mCountDownTimer = object : CountDownTimer(packetRemainTime + 300, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                mCountDownSecond = millisUntilFinished / 1000L
                countDownTv?.text = LiveTimeUtil.formatTimeMS(mCountDownSecond)
                if (mCountDownSecond == SECOND_296) {
                    showTips()
                }
            }

            override fun onFinish() {
                redPacketRainLayout?.visibility = View.GONE
                mCountDownTimer?.cancel()
                openH5RainPage(packetRainUrl)
            }

        }.start()
    }

    private fun showTips() {
        if (popupWindow == null) {
            val view =
                LayoutInflater.from(context).inflate(R.layout.livehost_red_packet_rain_tips, null)
            popupWindow = PopupWindow(
                view,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        }
        popupWindow?.showAsDropDown(
            redPacketRainLayout,
            BaseUtil.dp2px(context, 14f),
            BaseUtil.dp2px(context, 5f)
        )
        HandlerManager.obtainMainHandler()
            .postDelayed(dismissPopTipsRunnable, POP_WINDOWS_DISMISS_TIME)
    }

    private val dismissPopTipsRunnable = Runnable {
        popupWindow?.dismiss()
    }

    /**
     * 打开h5红包雨页面
     */
    private fun openH5RainPage(packetRainUrl: String?) {
        packetRainUrl ?: return
        LiveCommonITingUtil.handleITing(activity, packetRainUrl)
    }

    override fun onReceiveSpringFestivalMsg(msg: LiveSpringFestivalMessage) {
        CommonLiveLogger.d(TAG, "$msg")
        if (msg.status == STATUS_3) {
            // 加载红包雨组件
            runAfterViewInflate()
            loadRedPacketRainData(msg)
        } else if (msg.status == STATUS_4) {
            openH5RainPage(msg.packetRainUrl)
        }
    }

    private fun release() {
        mCountDownTimer?.cancel()
        popupWindow?.dismiss()
        HandlerManager.obtainMainHandler().removeCallbacks(dismissPopTipsRunnable)
        if (dialog?.isShowing == true) {
            dialog?.dismiss()
        }
        redPacketRainLayout?.visibility = View.GONE
    }

    override fun onSwitchRoom(newRoomId: Long, newArgs: Bundle?) {
        super.onSwitchRoom(newRoomId, newArgs)
        release()
    }


    override fun onDestroy() {
        super.onDestroy()
        release()
    }

    companion object {
        private const val TAG = "LiveRedPacketRainComponent"
        private const val STATUS_3 = 3    // 直播间红包倒计时
        private const val STATUS_4 = 4    //红包雨播放中【持续10s】
        private const val SECOND_296 = 296L // 300s
        private const val POP_WINDOWS_DISMISS_TIME = 3000L
    }
}