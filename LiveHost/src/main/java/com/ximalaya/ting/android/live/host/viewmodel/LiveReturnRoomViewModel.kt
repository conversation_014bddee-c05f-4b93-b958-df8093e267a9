package com.ximalaya.ting.android.live.host.viewmodel

import androidx.lifecycle.ViewModel
import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger
import com.ximalaya.ting.android.live.common.lib.base.request.CommonRequestForCommon
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/1/29.
 * <AUTHOR>
 * @email  <EMAIL>
 * desc   : 最小化后返回房间
 */
class LiveReturnRoomViewModel : ViewModel() {


    /**
     * 请求接口，业务方根据场景，推送消息到房间
     */
    fun getBusinessPush(roomId: Long, bizType: Int) {
        CommonRequestForCommon.getBusinessPush(roomId, bizType, object : IDataCallBack<Boolean> {
            override fun onSuccess(data: Boolean?) {
                CommonLiveLogger.d(TAG, "data:$data")
            }

            override fun onError(code: Int, message: String?) {
                CommonLiveLogger.d(TAG, "onError:$code, $message")
            }
        })
    }

    companion object {
        private const val TAG = "LiveSpringFestivalViewModel"
    }
}