package com.ximalaya.ting.android.live.host.dialog;

import android.content.Context;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;


public class LiveHostConfirmDialog extends XmBaseDialog {

    private IClickListener mClickListener;
    private TextView mTvContent;
    private String mContent;
    private String mTitle;
    private TextView tvConfirm;
    private TextView tvCancel;
    private TextView mTitleTv;

    private LiveHostConfirmDialog(@NonNull Context context) {
        super(context, com.ximalaya.ting.android.host.R.style.host_share_dialog);
    }

    public LiveHostConfirmDialog(@NonNull Context context, String title,
                                 String content,
                                 IClickListener cancelListener) {
        this(context);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        mContent = content;
        mTitle = title;
        this.mClickListener = cancelListener;
        initUi();
    }

    private void initUi() {

        View view = View.inflate(getContext(), com.ximalaya.ting.android.live.common.R.layout.livecomm_dialog_common_v2, null);
        mTitleTv = view.findViewById(com.ximalaya.ting.android.live.host.R.id.live_title);
        mTvContent = view.findViewById(com.ximalaya.ting.android.live.host.R.id.live_content);
        tvConfirm = view.findViewById(com.ximalaya.ting.android.live.host.R.id.live_ok);
        tvCancel = view.findViewById(com.ximalaya.ting.android.live.host.R.id.live_cancel);
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mClickListener != null) {
                    mClickListener.onClickCancel();
                }
                LiveHostConfirmDialog.this.dismiss();
            }
        });
        tvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mClickListener != null) {
                    mClickListener.onClickOK();
                }
                LiveHostConfirmDialog.this.dismiss();
            }
        });

        if (!TextUtils.isEmpty(mContent)){
            mTvContent.setText(mContent);
            mTvContent.setVisibility(View.VISIBLE);
        }

        if (!TextUtils.isEmpty(mTitle)){
            mTitleTv.setText(mTitle);
            mTitleTv.setVisibility(View.VISIBLE);
        }
        setContentView(view);
        setCanceledOnTouchOutside(true);
        setCancelable(true);
        if (getWindow() != null) {
            WindowManager.LayoutParams lp = getWindow().getAttributes();
            int screenWidth = BaseUtil.getScreenWidth(getContext());
            lp.width = screenWidth - BaseUtil.dp2px(getContext(), 100);
            lp.gravity = Gravity.CENTER;
            getWindow().setAttributes(lp);
        }
    }

    public void setTitle(String text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        mTitle = text;
        mTitleTv.setText(mTitle);
        mTitleTv.setVisibility(View.VISIBLE);
    }
    public void setTitleStyle(Typeface tf){
        mTitleTv.setTypeface(tf);
    }

  public void setOkText(String text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        tvConfirm.setText(text);
    }

    public void setOkVisibility(int visibility){
        if(tvConfirm!=null){
            tvConfirm.setVisibility(visibility);
        }
    }

    public void setContentText(String text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        mTvContent.setText(text);
        mTvContent.setVisibility(View.VISIBLE);
    }

    public void setCancelText(String text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        tvCancel.setText(text);
    }

    public interface IClickListener {
        void onClickOK();
        void onClickCancel();
    }
}
