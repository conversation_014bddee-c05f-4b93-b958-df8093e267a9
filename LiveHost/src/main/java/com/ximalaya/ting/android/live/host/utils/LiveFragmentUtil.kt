package com.ximalaya.ting.android.live.host.utils

import androidx.fragment.app.Fragment
import com.ximalaya.ting.android.framework.fragment.BaseFragment
import com.ximalaya.ting.android.framework.fragment.ManageFragment
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.live.host.scrollroom.fragment.LiveScrollFragment
import kotlin.reflect.jvm.jvmName

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18321019958
 * @wiki
 * @server
 * @since 2025/2/25
 */
object LiveFragmentUtil {


    @JvmStatic
    fun hasLiveRoomFragmentInStack():Boolean{
        (MainApplication.getMainActivity() as? MainActivity)?.let {
            return it.manageFragment?.hasPageInStack(LiveScrollFragment::class.java)?:false
        }
        return false
    }

    @JvmStatic
    fun thereThisClassOfFragmentInStack(fragment: Class<*>?):Boolean{
        fragment ?: return false
        (MainApplication.getMainActivity() as? MainActivity)?.let {
            return it.manageFragment?.hasPageInStack(fragment)?:false
        }
        return false
    }

    @JvmStatic
    fun isThisFragmentInStack(fragment: Fragment?):Boolean{
        return fragment?.parentFragment != null && fragment.parentFragment == (MainApplication.getMainActivity() as? MainActivity)?.manageFragment
    }


    fun getManagerFragment():ManageFragment?{
        return (MainApplication.getMainActivity() as? MainActivity)?.manageFragment
    }


    fun getManagerTopFragment():Fragment?{
        return (MainApplication.getMainActivity() as? MainActivity)?.manageFragment?.currentFragment
    }


    @JvmStatic
    fun getManagerFragmentStackSize():Int{
        return (MainApplication.getMainActivity() as? MainActivity)?.manageFragment?.stackNum ?:0
    }

    fun getMainActivity():MainActivity?{
        return (MainApplication.getMainActivity() as? MainActivity)
    }

    fun getCurrentLiveRoomFragment(): BaseFragment?{
        return getManagerFragment()?.findFragment(LiveScrollFragment::class.jvmName)
    }

}