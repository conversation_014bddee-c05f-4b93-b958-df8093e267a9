package com.ximalaya.ting.android.live.host.dialog;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.other.BaseLoadDialogFragment;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.host.R;
import com.ximalaya.ting.android.live.host.constant.Constants;

/**
 * 是否回到直播间继续直播弹框
 *
 * <AUTHOR>
 */
public class GoLivingDialogFragment extends BaseLoadDialogFragment implements View.OnClickListener {

    private int mMediaType; //直播类型
    private long mLiveId; //直播id
    private long mRoomId; //房间id
    private int mLiveDeviceType; //直播设备信息
    protected MainActivity activity;
    private boolean isCourse; // 是否是课程直播

    private ImageView mCloseIv;
    private TextView mFinishTv;
    private TextView mGoLivingTv;
    private TextView mCancelTv;

    public static GoLivingDialogFragment newInstance(Context context, long id, long roomId, int liveDeviceType,
                                                     int mediaType, boolean isCourse) {
        GoLivingDialogFragment fragment = new GoLivingDialogFragment();
        Bundle bundle = new Bundle();
        bundle.putLong(Constants.KEY_LIVE_ID, id);
        bundle.putLong(Constants.KEY_LIVE_ROOM_ID, roomId);
        bundle.putInt(Constants.KEY_LIVE_DEVICE_TYPE, liveDeviceType);
        bundle.putInt(Constants.KEY_LIVE_MEDIA_TYPE, mediaType);
        bundle.putBoolean(Constants.KEY_LIVE_IS_COURSE, isCourse);
        fragment.setArguments(bundle);
        if (context instanceof MainActivity) {
            fragment.activity = (MainActivity) context;
        } else if (MainApplication.getTopActivity() instanceof MainActivity) {
            fragment.activity = (MainActivity) MainApplication.getTopActivity();
        }
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        setStyle(DialogFragment.STYLE_NO_TITLE, com.ximalaya.ting.android.host.R.style.host_share_dialog);
        parseBundle();
        super.onCreate(savedInstanceState);
    }

    private void parseBundle() {
        Bundle bundle = getArguments();
        if (null != bundle) {
            mLiveDeviceType = bundle.getInt(Constants.KEY_LIVE_DEVICE_TYPE);
            mLiveId = bundle.getLong(Constants.KEY_LIVE_ID);
            mRoomId = bundle.getLong(Constants.KEY_LIVE_ROOM_ID);
            mMediaType = bundle.getInt(Constants.KEY_LIVE_MEDIA_TYPE);
            isCourse = bundle.getBoolean(Constants.KEY_LIVE_IS_COURSE);
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.live_host_dialog_go_living_room;
    }

    @Override
    public void onStart() {
        if (getDialog() == null) {
            return;
        }
        Window window = getDialog().getWindow();
        if (null != window) {
            window.setDimAmount(0.5f);
        }
        super.onStart();
    }

    @Override
    protected void initUi(View view, Bundle savedInstanceState) {
        mCloseIv = (ImageView) findViewById(R.id.live_iv_close);
        mFinishTv = (TextView) findViewById(R.id.live_tv_finish);
        mGoLivingTv = (TextView) findViewById(R.id.live_tv_confirm);
        mCancelTv = (TextView) findViewById(R.id.live_tv_cancel);

        ViewStatusUtil.setVisible(mLiveDeviceType == Constants.MOBILE_LIVE ? View.VISIBLE : View.INVISIBLE, mCloseIv);
        if ((mLiveDeviceType == Constants.MOBILE_LIVE)) {
            mGoLivingTv.setVisibility(View.VISIBLE);
            mFinishTv.setVisibility(View.VISIBLE);

            mFinishTv.setTextColor(getResourcesSafe().getColor(com.ximalaya.ting.android.live.common.R.color.live_red_f77254));
            mFinishTv.setBackgroundResource(0);


        } else {
            mGoLivingTv.setVisibility(View.GONE);
            mFinishTv.setVisibility(View.VISIBLE);

            mFinishTv.setTextColor(getResourcesSafe().getColor(com.ximalaya.ting.android.live.common.R.color.live_color_333333_cfcfcf));
            mFinishTv.setBackgroundResource(R.drawable.live_bg_go_living_btn);
        }

        mCancelTv.setVisibility(View.GONE);

        mCloseIv.setOnClickListener(this);
        mFinishTv.setOnClickListener(this);
        mGoLivingTv.setOnClickListener(this);
        mCancelTv.setOnClickListener(this);
    }

    @Override
    protected void loadData() {

    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        if (v == mFinishTv) {

            dismiss();

            if (mClickCallback != null) {
                mClickCallback.onBottomButtonClick();
            }
        } else if (v == mGoLivingTv) {

            dismiss();

            if (mClickCallback != null) {
                mClickCallback.onTopButtonClick();
            }
        } else if (v == mCancelTv || v == mCloseIv) {
            dismiss();
        }
    }


    public boolean isShowing() {
        return getDialog() != null && getDialog().isShowing();
    }


    private IClickItemButton mClickCallback;

    public void setClickCallback(IClickItemButton callback) {

        mClickCallback = callback;

    }


    public interface IClickItemButton {

        void onTopButtonClick();

        void onBottomButtonClick();

    }

}
