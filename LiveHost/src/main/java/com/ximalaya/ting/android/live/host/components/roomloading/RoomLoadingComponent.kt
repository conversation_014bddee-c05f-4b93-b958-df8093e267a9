package com.ximalaya.ting.android.live.host.components.roomloading

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.util.AttributeSet
import android.view.*
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.live.PersonLiveBase
import com.ximalaya.ting.android.host.util.server.NetworkUtils
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.live.common.lib.base.constants.CdnStatus
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType
import com.ximalaya.ting.android.live.common.lib.entity.BaseRoomDetail
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil
import com.ximalaya.ting.android.live.common.view.SinWaveLoadingView
import com.ximalaya.ting.android.live.host.R
import com.ximalaya.ting.android.live.host.components.BaseLiveComponent
import com.ximalaya.ting.android.live.host.utils.LiveHostViewUtil
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper

/**
 * 直播间加载中管理组件
 *
 * <AUTHOR>
 * @since 2023/2/1
 */
class RoomLoadingComponent : BaseLiveComponent<BaseRoomDetail?>(), IRoomLoadingComponent,
    View.OnClickListener {

    private val mLoadingView by lazy {
        SinWaveLoadingView(MainApplication.getMainActivity()).apply {
            setLineColor(Color.WHITE)
            setLineRoundCornerRatio(0.4f)
            setAllColumnWidthRatio(0.4f)
            setLineMiniHeightRatio(0.2f)
            setLoadDuration(2000)
            visibility = View.INVISIBLE
        }
    }

    private var mWaitAnchorLoadingView: View? = null

    private var mLoadingLayout: LinearLayout? = null

    private var mLoadingDescription: String? = ""

    private var mCdnStatus: Int = -1

    private var mRoomStatus: Int = -1

    private val mLoadErrorCommonDes by lazy {
        "当前无网络，请检查网络后重试"
    }

    private var mRoomLoadErrorDesTv: TextView? = null

    private val mLoadingDescriptionView by lazy {
        TextView(MainApplication.getMainActivity()).apply {
            setTextColor(Color.WHITE)
            gravity = Gravity.CENTER
        }
    }

    private var mLoadFailView: ViewGroup? = null

    private val mUIShowTask = Runnable {
        showLoadingView()
    }

    private var mShowLoadingViewTask = Runnable {
        HandlerManager.removeCallbacks(mUIShowTask)
        HandlerManager.postOnUIThread(mUIShowTask)
    }

    private fun showLoadingView() {
        if (!canUpdateUi()) {
            return
        }
        hideRequestErrorView()
        runAfterViewInflate {
            mLoadingView.bringToFront()
            if (mLoadingDescription?.isNotEmpty() == true) {
                if (mLoadingDescriptionView.parent == null) {
                    mLoadingLayout?.addView(
                        mLoadingDescriptionView,
                        LinearLayout.LayoutParams(
                            ViewGroup.LayoutParams.WRAP_CONTENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        ).apply {
                            topMargin =
                                BaseUtil.dp2px(MainApplication.getMyApplicationContext(), 10f)
                            gravity = Gravity.CENTER_HORIZONTAL
                        })
                }
                ViewStatusUtil.setVisible(View.VISIBLE, mLoadingDescriptionView)
                UIStateUtil.safelySetText(mLoadingDescriptionView, mLoadingDescription!!)
            } else {
                ViewStatusUtil.setVisible(View.INVISIBLE, mLoadingDescriptionView)
            }
            ViewStatusUtil.setVisible(View.VISIBLE, mLoadingView)
        }
    }

    override fun bindData(hostData: BaseRoomDetail) {
        super.bindData(hostData)
        mRoomStatus = hostData.status
        if (getHostData()?.mediaType == LiveMediaType.TYPE_AUDIO) {
            updateCdnStatus(mCdnStatus)
        }
    }

    override fun createComponentView(
        inflater: LayoutInflater,
        attrs: AttributeSet?,
        container: ViewGroup?
    ): View? {

        mLoadingLayout = LinearLayout(MainApplication.getMainActivity()).apply {

            addView(
                mLoadingView,
                LinearLayout.LayoutParams(
                    BaseUtil.dp2px(context, 30f),
                    BaseUtil.dp2px(context, 20f)
                ).apply {
                    gravity = Gravity.CENTER_HORIZONTAL
                }
            )

            layoutParams = RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                addRule(RelativeLayout.CENTER_IN_PARENT)
            }
            gravity = Gravity.CENTER
            orientation = LinearLayout.VERTICAL

        }
        return mLoadingLayout
    }

    override fun showRequestLoadingImmediately(vararg args: String?) {
        if (!canUpdateUi()) {
            return
        }
        mLoadingDescription = if (args.isNotEmpty()) args[0] else ""
        HandlerManager.removeBackgroundThreadDelay(mShowLoadingViewTask)
        showLoadingView()
    }

    override fun onDestroy() {
        super.onDestroy()
        hideRequestLoading()
        hideRequestErrorView()
    }

    override fun onSwitchRoom(newRoomId: Long, newArgs: Bundle?) {
        super.onSwitchRoom(newRoomId, newArgs)
        mCdnStatus = -1
        mRoomStatus = -1
        hideRequestLoading()
        hideRequestErrorView()
        hideWaitAnchorLoading()
    }

    override fun showRequestLoadingDelay(vararg args: String?) {
        if (!canUpdateUi()) {
            return
        }
        if (args.isNotEmpty()) {
            mLoadingDescription = args[0]
        }
        HandlerManager.removeBackgroundThreadDelay(mShowLoadingViewTask)
        ViewStatusUtil.setVisible(View.GONE, mLoadingLayout)
        HandlerManager.postOnBackgroundThreadDelay(mShowLoadingViewTask, 500)
    }

    override fun hideRequestLoading() {
        HandlerManager.removeBackgroundThreadDelay(mShowLoadingViewTask)
        HandlerManager.removeCallbacks(mUIShowTask)
        mLoadingDescription = ""
        ViewStatusUtil.setVisible(View.GONE, mLoadingLayout)
    }

    override fun showNoContent() {

    }

    override fun initLoadingComponent() {

    }

    @SuppressLint("InflateParams")
    override fun showRequestErrorView(vararg args: String?) {
        if (!canUpdateUi()) {
            return
        }
        HandlerManager.removeBackgroundThreadDelay(mShowLoadingViewTask)
        getComponentInteraction(IRoomLoadingInteraction::class.java).onRoomLoadErrorViewShow(false)
        if (mLoadFailView == null) {
            var parent = componentContainer
            if (parent == null) {
                if (componentContainerId != 0) {
                    parent = findViewById(componentContainerId)
                }
            }
            if (parent == null) {
                return
            }
            mLoadFailView = LayoutInflater.from(MainApplication.getMyApplicationContext())
                .inflate(R.layout.livehost_layout_room_load_fail, null) as ViewGroup
            if (mLoadFailView == null) {
                return
            }
            LiveHostViewUtil.addInCenterParent(mLoadFailView, parent)
            mRoomLoadErrorDesTv = findViewById(R.id.live_room_load_error_des)
            val retryTv = findViewById<View>(R.id.live_retryTv) as TextView
            retryTv.setOnClickListener(this)
            mLoadFailView?.visibility = View.VISIBLE
            hostData?.let { AutoTraceHelper.bindData(retryTv, AutoTraceHelper.MODULE_DEFAULT, it) }
        } else {
            if (mLoadFailView?.parent != null) {
                ViewStatusUtil.setVisible(View.VISIBLE, mLoadFailView)
            }
        }
        if (args.isNotEmpty()) {
            val des = args[0]
            mRoomLoadErrorDesTv?.text = des
        } else if (NetworkUtils.isNetworkAvaliable(BaseApplication.getMyApplicationContext())) {
            mRoomLoadErrorDesTv?.text = "直播信息获取失败, 请稍后重试"
        } else {
            mRoomLoadErrorDesTv?.text = mLoadErrorCommonDes
        }
    }

    override fun hideRequestErrorView() {
        if (mLoadFailView?.parent != null) {
            val parent = mLoadFailView!!.parent as ViewGroup
            parent.removeView(mLoadFailView)
            mLoadFailView = null
        }
    }


    override fun updateCdnStatus(cdnStatus: Int) {
        mCdnStatus = cdnStatus
        if (cdnStatus == CdnStatus.CDN_STATUS_STOP && mRoomStatus == PersonLiveBase.LIVE_STATUS_ING
            && !isPremiere
        ) {
            runAfterViewInflate {
                showWaitAnchorLoading()
            }
        } else {
            hideWaitAnchorLoading()
        }
    }

    override fun updateRoomStatus(roomStatus: Int) {
        mRoomStatus = roomStatus
    }

    private fun showWaitAnchorLoading() {
        runAfterViewInflate {
            if (mWaitAnchorLoadingView == null) {
                val waitLoadingVs = findViewById<ViewStub>(R.id.live_load_live_anchor_connecting)
                mWaitAnchorLoadingView = waitLoadingVs?.inflate()
            }
            hideRequestLoading()
            ViewStatusUtil.setVisible(View.VISIBLE, mWaitAnchorLoadingView)
        }

    }

    private fun hideWaitAnchorLoading() {
        if (mWaitAnchorLoadingView != null) {
            ViewStatusUtil.setVisible(View.GONE, mWaitAnchorLoadingView)
        }
    }

    override fun onClick(v: View?) {
        if (v!!.id == R.id.live_retryTv) {
            getComponentInteraction(IRoomLoadingInteraction::class.java).onRoomRetryLoadDetailBtnClick()
        }
    }
}