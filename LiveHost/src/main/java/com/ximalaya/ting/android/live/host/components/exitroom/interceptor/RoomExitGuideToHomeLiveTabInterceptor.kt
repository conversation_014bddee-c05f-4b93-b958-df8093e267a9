package com.ximalaya.ting.android.live.host.components.exitroom.interceptor

import android.content.Intent
import android.os.Bundle
import android.view.View
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant.LiveRoomBizType
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager
import com.ximalaya.ting.android.host.model.community.DyncFollowModel.LiveRoom
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType
import com.ximalaya.ting.android.live.common.lib.manager.LiveFollowInfoManager
import com.ximalaya.ting.android.live.common.lib.manager.broadcast.LiveLocalBroadcastManager
import com.ximalaya.ting.android.live.host.R
import com.ximalaya.ting.android.live.host.components.exitroom.interceptor.dialog.RoomExitGuideToHomeLiveTabDialog
import com.ximalaya.ting.android.live.host.components.exitroom.interceptor.dialog.RoomExitGuideToHomeLiveTabDialogBase
import com.ximalaya.ting.android.live.host.components.exitroom.interceptor.dialog.RoomExitGuideToHomeLiveTabDialogDouYin
import com.ximalaya.ting.android.live.host.request.CommonRequestForLiveHost
import com.ximalaya.ting.android.live.host.scrollroom.biz.guide.ExitRoomGuideManager
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.XMTraceApi


/**
 * @date   2024/9/2
 * <AUTHOR>
 */
class RoomExitGuideToHomeLiveTabInterceptor(container: IExitRoomInterceptorContainer) :
    RoomExitShowTipInterceptor(container), View.OnClickListener {

    var showingialog: RoomExitGuideToHomeLiveTabDialogBase? = null

    override fun showInterceptorPop(data: ExitRoomInterceptorRspData) {
        if (showingialog != null) {
            showingialog?.dismiss()
        }
        container.getChildFragmentManager()?.let {
            createDialog(data)?.apply {
                ExitRoomGuideManager.setRoomCenterScrollGuideShowed()
                <EMAIL> = this
                this.data = data
                this.show(it, "RoomExitGuideToHomeLiveTabInterceptor")
                this.okListener = this@RoomExitGuideToHomeLiveTabInterceptor
                this.cancelListener = this@RoomExitGuideToHomeLiveTabInterceptor
                // 直播间-直播tab引导弹窗  控件曝光
                // 直播间-直播tab引导弹窗  控件曝光

                HandlerManager.postOnUIThreadDelay({
                    XMTraceApi.Trace()
                        .setMetaId(65270)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "liveRoom")
                        .put(
                            "isDuplicateView",
                            "2"
                        ) // 0，1，2 字段文档见：https://alidocs.dingtalk.com/i/nodes/jb9Y4gmKWr7lEp50HZ55dyaZVGXn6lpz?utm_scene=team_space&utm_source=dingdoc_doc&utm_medium=dingdoc_doc_plugin_card
                        .put("liveId", container.getLiveId().toString())
                        .put("roomId", container.getRoomId().toString())
                        .put(
                            "liveRoomType",
                            getTraceBizType().toString()
                        ) // 直播类型   （1：个人音频直播，4：个人视频直播，5 PGC聊天室，6 UGC语音房，10000：课程直播）
                        .put("anchorId", container.getHostUid().toString())
                        .put("dialogTitle", getTileString())
                        .createTrace()
                },500)
            }
        }
    }

    private fun createDialog(data: ExitRoomInterceptorRspData): RoomExitGuideToHomeLiveTabDialogBase? {
        return if (data.showLiveTab) {
            RoomExitGuideToHomeLiveTabDialogDouYin(container)
        } else if (data.showTip) {
            RoomExitGuideToHomeLiveTabDialog(container)
        } else {
            null
        }
    }

    private fun getTraceBizType(): Int {
        if (container.getRoomBizType() == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO && container.getLiveMediaType() == LiveMediaType.TYPE_VIDEO) {
            return BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO
        }
        return container.getRoomBizType()
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.live_host_room_exit_cancel_button -> {
                val tag =
                    v.getTag(com.ximalaya.ting.android.framework.R.id.framework_view_holder_data)
                if (tag == RoomExitGuideToHomeLiveTabDialogDouYin.GO_TAB) {
                    notifyCloseRoom()
                    HandlerManager.postOnUIThreadDelay({
                        goHomeLiveTab()
                    }, 500)
                    traceClickEvent("退出直播间")
                } else {
                    notifyCloseRoom()
                    traceClickEvent("退出")
                }
            }

            R.id.live_host_room_exit_ok_button -> {
                val tag =
                    v.getTag(com.ximalaya.ting.android.framework.R.id.framework_view_holder_data)
                if (tag == RoomExitGuideToHomeLiveTabDialogDouYin.FOLLOW_AND_GO_TAB) {
                    if (container.getRoomBizType() == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_PGC) {
                        followPGCRoomAndToHomeTab()
                    } else {
                        followAnchorAndToHomeTab()
                    }
                    traceClickEvent("关注并退出")
                } else {
                    notifyCloseRoom()
                    HandlerManager.postOnUIThreadDelay({
                        goHomeLiveTab()
                    }, 500)
                    traceClickEvent("去看看")
                }
            }

            R.id.live_exit_room_layout -> {
                traceClickEvent("浮层内区域")
            }

            else -> {
                traceClickEvent("浮层外区域")
            }
        }
        this.showingialog = null
    }

    private fun followPGCRoomAndToHomeTab() {
        CommonRequestForLiveHost.favoriteEntHallRoom(
            true,
            container.getRoomId(),
            object : IDataCallBack<Boolean> {
                override fun onSuccess(success: Boolean?) {
                    if (success == true) {
                        ToastManager.showSubmitSuccessToast("关注成功")
                        val intent = Intent(LiveLocalBroadcastManager.ACTION.UPDATE_FAVORITE_STATE)
                        intent.putExtra(LiveLocalBroadcastManager.EXTRA.FAVORITE, true)
                        LiveLocalBroadcastManager.send(intent)
                        refreshHomeFollowModule()
                    }
                    notifyCloseRoom()
                    HandlerManager.postOnUIThreadDelay({
                        goHomeLiveTab()
                    }, 500)
                }

                override fun onError(code: Int, message: String?) {
                    notifyCloseRoom()
                    HandlerManager.postOnUIThreadDelay({
                        goHomeLiveTab()
                    }, 500)
                }
            })
    }

    private fun refreshHomeFollowModule() {
        val intent = Intent(LiveLocalBroadcastManager.ACTION.REFRESH_HOME_FOLLOW_MODULE)
        LiveLocalBroadcastManager.send(intent)
    }

    private fun followAnchorAndToHomeTab() {
        val specificParams = LiveFollowInfoManager.getInstance().followParams
        AnchorFollowManage.followV3(
            MainApplication.getMainActivity(), container.getHostUid(), false,
            AnchorFollowManage.FOLLOW_BIZ_TYPE_LIVE_ROOM,
            AnchorFollowManage.FOLLOW_BIZ_TYPE_LIVE_EXIT_FOR_DOU_YIN_CHANNEL_GUIDE, specificParams,
            object : IDataCallBack<Boolean?> {
                override fun onSuccess(aBoolean: Boolean?) {
                    notifyCloseRoom()
                    HandlerManager.postOnUIThreadDelay({
                        goHomeLiveTab()
                    }, 500)
                }

                override fun onError(code: Int, message: String) {
                    notifyCloseRoom()
                    HandlerManager.postOnUIThreadDelay({
                        goHomeLiveTab()
                    }, 500)
                }
            }, true, true
        )
    }

    private fun traceClickEvent(event: String) {
        XMTraceApi.Trace()
            .click(65269) // 用户点击时上报
            .put("currPage", "liveRoom")
            .put("liveId", container.getLiveId().toString())
            .put("roomId", container.getRoomId().toString())
            .put(
                "liveRoomType",
                getTraceBizType().toString()
            ) // 直播类型   （1：个人音频直播，4：个人视频直播，5 PGC聊天室，6 UGC语音房，10000：课程直播）
            .put("anchorId", container.getHostUid().toString())
            .put("Item", event)
            .put("dialogTitle", showingialog?.getTileString())
            .createTrace()
    }

    private fun goHomeLiveTab() {
//        val iting = "iting://open?msg_type=198&tab=直播"
//        LiveCommonITingUtil.handleITing(MainApplication.getMainActivity(), iting)
        gotoHomePageWithTab(MainApplication.getMainActivity() as MainActivity);
    }

    private fun gotoHomePageWithTab(mainActivity: MainActivity?) {
        if (mainActivity == null) {
            return
        }
        val m = mainActivity.manageFragment ?: return
        if (mainActivity.isPlayFragmentVisible) {
            mainActivity.hidePlayFragment(null)
        }
        if (m.mStacks.size > 0) {
            mainActivity.clearAllFragmentFromManageFragment()
            if (mainActivity.tabFragmentManager != null && mainActivity.tabFragmentManager.currentTab != TabFragmentManager.TAB_HOME_PAGE) {
                val bundle1 = Bundle()
                mainActivity.checkRadio(TabFragmentManager.TAB_HOME_PAGE, bundle1)
                HandlerManager.postOnUIThreadDelay({ gotoHomePageWithTab(mainActivity) }, 500)
                return
            } else {
                HandlerManager.postOnUIThreadDelay({ gotoHomePageWithTab(mainActivity) }, 500)
                return
            }
        } else {
            if (mainActivity.tabFragmentManager != null && mainActivity.tabFragmentManager.currentTab != TabFragmentManager.TAB_HOME_PAGE) {
                val bundle1 = Bundle()
                mainActivity.checkRadio(TabFragmentManager.TAB_HOME_PAGE, bundle1)
                HandlerManager.postOnUIThreadDelay({ checkToLive() }, 500)
                return
            } else {
                HandlerManager.postOnUIThreadDelay({
                    checkToLive()
                }, 500)
                return
            }
        }
    }

    private fun checkToLive() {
        // 当前已选中 HomePageFragment
        val mainActivity: MainActivity? = MainApplication.getMainActivity() as? MainActivity
        try {
            mainActivity?.showFragmentInMainFragment(TabFragmentManager.TAB_HOME_PAGE, null)
            val fragmentAction =
                Router.getActionRouter<MainActionRouter>(Configure.BUNDLE_MAIN)!!
                    .fragmentAction
            val currentFra = mainActivity?.tabFragmentManager?.currFragment
            if (fragmentAction != null) {
                fragmentAction.switchTabWithName(currentFra, "直播")
            } else if (currentFra != null) {
                var bundle = currentFra.arguments
                if (bundle == null) {
                    bundle = Bundle()
                }
                bundle.putString(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_NAME, "直播")
                currentFra.arguments = bundle
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun switchRoom() {
        super.switchRoom()
        showingialog?.dismissAllowingStateLoss()
        showingialog = null
    }

    override fun exitRoom() {
        super.exitRoom()
        showingialog?.dismissAllowingStateLoss()
        showingialog = null
    }

    override fun onOrientationChange(land:Boolean) {
        super.onOrientationChange(land)
        if(land){
            showingialog?.dismissAllowingStateLoss()
            showingialog = null
        }
    }
}