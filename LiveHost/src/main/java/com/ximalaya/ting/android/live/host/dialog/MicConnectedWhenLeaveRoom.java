package com.ximalaya.ting.android.live.host.dialog;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.host.R;

/**
 * Created by qianmenchao on 2021/8/30.
 *
 * <AUTHOR>
 */
public class MicConnectedWhenLeaveRoom extends GoLivingDialogFragment {

    public static final String KEY_MESSAGE = "live_message_txt";
    public static final String KEY_OK_BUTTON_TXT = "live_ok_button_txt";
    public static final String KEY_CANCEL_BUTTON_TXT = "live_cancel_button_txt";

    public static MicConnectedWhenLeaveRoom newInstance(Context context, String message, String bottomButton, String topButton) {
        MicConnectedWhenLeaveRoom fragment = new MicConnectedWhenLeaveRoom();
        Bundle bundle = new Bundle();
        bundle.putString(KEY_MESSAGE, message);
        bundle.putString(KEY_OK_BUTTON_TXT, bottomButton);
        bundle.putString(KEY_CANCEL_BUTTON_TXT, topButton);
        fragment.setArguments(bundle);
        if (context instanceof MainActivity) {
            fragment.activity = (MainActivity) context;
        } else if (MainApplication.getTopActivity() instanceof MainActivity) {
            fragment.activity = (MainActivity) MainApplication.getTopActivity();
        }
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void initUi(View view, Bundle savedInstanceState) {
        super.initUi(view, savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle == null) {
            return;
        }
        TextView startNew = (TextView) findViewById(R.id.live_tv_finish);
        TextView backNow = (TextView) findViewById(R.id.live_tv_confirm);
        TextView content = (TextView) findViewById(R.id.live_tv_notice_title);
        content.setGravity(Gravity.CENTER);
        String message = bundle.getString(KEY_MESSAGE);
        String ok = bundle.getString(KEY_OK_BUTTON_TXT);
        String cancel = bundle.getString(KEY_CANCEL_BUTTON_TXT);

        if (TextUtils.isEmpty(message)) {
            ViewStatusUtil.setVisible(View.GONE, content);
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, content);
            content.setText(message);
        }

        if (TextUtils.isEmpty(ok)) {
            ViewStatusUtil.setVisible(View.GONE, startNew);
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, startNew);
            startNew.setText(ok);
        }

        if (TextUtils.isEmpty(cancel)) {
            ViewStatusUtil.setVisible(View.GONE, backNow);
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, backNow);
            backNow.setText(cancel);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mOnDestroyHandle != null) {
            mOnDestroyHandle.onReady();
        }
    }
}
