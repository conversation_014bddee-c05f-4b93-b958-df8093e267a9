package com.ximalaya.ting.android.live.host.presenter.listener;

import com.ximalaya.ting.android.live.host.fragment.room.IBaseRoom;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonWithdrawChatMsg;
import com.ximalaya.ting.android.live.lib.chatroom.manager.IRmMessageDispatcherManager;

/**
 * 长连接撤回消息监听器
 *
 * @email <EMAIL>
 * @phoneNumber 13032178206
 * des:
 */
public class RmWithDrawMessageReceivedListener implements IRmMessageDispatcherManager.IRmMessageReceivedListener.IWithdrawChatMsgReceivedListener {

    private final IBaseRoom.IView mBaseRoomComponent;

    public RmWithDrawMessageReceivedListener(IBaseRoom.IView baseRoomComponent) {
        mBaseRoomComponent = baseRoomComponent;
    }

    @Override
    public void onWithdrawChatMsgReceived(CommonWithdrawChatMsg message) {
        if (message == null
                || message.userId <= 0
                || message.msgId <= 0
                || mBaseRoomComponent == null || !mBaseRoomComponent.canUpdateUi()) {
            return;
        }
        mBaseRoomComponent.onReceiveWithdrawChatMsg(message);
    }
}
