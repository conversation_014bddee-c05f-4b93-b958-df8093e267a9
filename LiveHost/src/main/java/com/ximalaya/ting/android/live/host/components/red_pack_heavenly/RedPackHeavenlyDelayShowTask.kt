package com.ximalaya.ting.android.live.host.components.red_pack_heavenly

import com.ximalaya.ting.android.xmutil.Logger

/**
 *
 * 天选红包延迟获取红包信息任务
 * 区别于 VideoRoomDelayLoadRedPackHeavenlyTask
 * 该任务主要用于已经在房间内的用户 避免qps过高
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621066329
 * @wiki
 * @server
 * @since 2024/10/8
 */
class RedPackHeavenlyDelayShowTask(val component: IRedPackHeavenlyComponent) : Runnable {
    override fun run() {
        try {
            Logger.d(
                "RedPackHeavenlyDelayResultTask",
                "mDelayOpenCommonH5DialogTask, 收到信令48，开始获取红包信息 ,用于log 计算延时时间"
            )
            component.requestRedPackHeavenlyList()
        } catch (e: InterruptedException) {
            e.printStackTrace()
        }
    }
}