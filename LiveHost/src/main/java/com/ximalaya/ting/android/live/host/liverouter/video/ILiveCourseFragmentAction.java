package com.ximalaya.ting.android.live.host.liverouter.video;

import android.app.Activity;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;

/**
 * 课程直播 Fragment 路由接口.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817870952
 */
public interface ILiveCourseFragmentAction {

    /**
     * 付费直播列表页面
     */
    void startPaidLiveListPage(Activity activity);

    /**
     * 打开课程直播间
     */
    void startLiveCourseHomeFragment(Activity activity, int catId, int liveId);

    /**
     * 课程直播间
     *
     * @param liveId     直播场次id
     * @param isLand     是否横屏
     * @param albumId    专辑id
     * @param playSource 播放来源
     * @return 课程直播间 Fragment
     */
    BaseFragment newLiveCourseRoomFragment(long liveId, boolean isLand, long albumId, int playSource);
}
