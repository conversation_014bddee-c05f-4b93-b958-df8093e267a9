package com.ximalaya.ting.android.live.host.components;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.live.host.R;
import com.ximalaya.ting.android.live.host.components.exitroom.ExitRoomComponent;
import com.ximalaya.ting.android.live.host.components.roombackground.RoomBackgroundComponent;
import com.ximalaya.ting.android.live.host.components.slideclearscreen.SlideClearScreenComponent;
import com.ximalaya.ting.android.live.host.liverouter.LiveRouter;
import com.ximalaya.ting.android.live.lifecycle.Component;
import com.ximalaya.ting.android.live.lifecycle.ComponentInitArgs;
import com.ximalaya.ting.android.live.lifecycle.ComponentLoaderConfig;
import com.ximalaya.ting.android.live.lifecycle.IComponentConfiguration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 直播间组件配置。
 *
 * <AUTHOR>
 */
public interface IBaseRoomCompConfig extends IComponentConfiguration {

    /**
     * 直播间消息流组件上方，玩法容器投影占位
     */
    String COMPONENT_CHAT_LIST_STUB = "ChatListStubComponent";

    /**
     * 直播间消息流组件，包括 A 区和 B 区
     */
    String COMPONENT_CHAT_LIST = "ChatListComponent";

    /**
     * C 区引导组件
     */
    String COMPONENT_CHAT_LIST_GUIDE = "ChatListGuideComponent";

    /**
     * 直播间输入面板组件
     */
    String COMPONENT_INPUT_PANEL = "InputPanelComponent";

    /**
     * 礼物大动画组件
     */
    String COMPONENT_BIG_GIFT = "BigGiftComponent";

    /**
     * 飘屏横幅组件
     */
    String COMPONENT_FLOAT_SCREEN = "FloatScreenComponent";

    /**
     * 弹幕组件
     */
    String COMPONENT_BULLET = "BulletComponent";

    /**
     * 去购买飘屏横幅组件
     */
    String COMPONENT_SHOPPING_FLOAT = "ShoppingFloatComponent";

    /**
     * 热词组件
     */
    String COMPONENT_HOT_WORD = "HotWordComponent";

    /**
     * 带货讲解中挂件组件
     */
    String COMPONENT_SELL_GOOD = "SellGoodsComponent";

    /**
     * 直播间右侧区域组件
     */
    String COMPONENT_RIGHT_AD = "RoomRightComponent";

    /**
     * 视频直播全屏播放弹幕组件
     */
    String COMPONENT_BARRAGE = "LiveBarrageComponent";

    /**
     * 新春打年兽组件
     */
    String COMPONENT_SPRING_FESTIVAL = "LiveSpringFestival";

    /**
     * 道具玩法特效层，如 423 活动碎屏特效
     */
    String COMPONENT_ANIM_LAYER = "LiveAnimLayerComponent";

    /**
     * pk组件动画，如血瓶，彩蛋等
     */
    String COMPONENT_PK_ANIM = "LivePkAnimComponent";

    /**
     * 返回上一直播间组件
     */
    String COMPONENT_RETURN_ROOM = "ReturnRoomComponent";

    /**
     * 退出直播间组件
     */
    String COMPONENT_EXIT_ROOM = "ExitRecordComponent";

    /**
     * 官方直播间组件
     */
    String COMPONENT_OFFICIAL = "OfficialComponent";

    /**
     * 头部组件
     */
    String COMPONENT_HEADER = "LamiaHeaderComponent";

    /**
     * 福袋组件
     */
    String COMPONENT_LUCKY_BAG = "LiveLuckyBagComponent";

    /**
     * 主播任务组件
     */
    String COMPONENT_HOST_TASK = "HostTaskComponent";

    /**
     * 主播端倒计时动画组件
     */
    String COMPONENT_HOST_COUNT_DOWN = "HostCountDownComponent";

    /**
     * 直播间加载中组件
     */
    String COMPONENT_ROOM_LOADING = "RoomLoadingComponent";

    /**
     * 红包组件
     */
    String COMPONENT_RED_PACK = "RedPackComponent";

    /**
     * 天选红包组件
     */
    String COMPONENT_RED_PACK_HEAVENLY = "RedPackHeavenlyComponent";

    /**
     * 直播间投票组件
     */
    String COMPONENT_VOTE = "LiveVoteComponent";

    /**
     * 直播间通用弹窗组件
     */
    String COMPONENT_COMMON_POP_DIALOG = "CommonPopDialogComponent";

    /**
     * 直播间底部工具栏组件
     */
    String COMPONENT_BOTTOM_BAR = "BottomBarComponent";

    /**
     * 直播间背景组件
     */
    String COMPONENT_ROOM_BACKGROUND = "RoomBackgroundComponent";

    /**
     * 直播间私信组件
     */
    String COMPONENT_PRIVATE_CHAT_SHOW = "PrivateChatComponent";

    /**
     * 新人引导组件
     */
    String COMPONENT_NEWBIE_GUIDE = "NewbieGuideComponent";

    /**
     * 优惠卷组件
     */
    String COMPONENT_COUPON = "CouponComponent";

    /**
     * 带货商品列表组件
     */
    String COMPONENT_SEAL_LIST = "SealListComponent";

    /**
     * 进场座驾组件
     */
    String COMPONENT_ENTER_ANIM = "EnterRoomAnimComponent";

    /**
     * 高级进场和礼物 POP 条动画组件
     */
    String COMPONENT_ENTER_GIFT_POP = "EnterAndGiftPopComponent";

    /**
     * 直播间通用调音面板
     */
    String COMPONENT_COMMON_TUNING = "CommonTuningComponent";

    /**
     * 娱乐派对用户资料卡片
     */
    String COMPONENT_USER_INFO_PANEL = "EntUserInfoPanelComponent";

    /**
     * 个播生日场抽奖组件
     */
    String COMPONENT_BIRTHDAY = "BirthDayComponent";

    /**
     * 视频播放组件
     */
    String COMPONENT_VIDEO_PLAYER = "VideoPlayerComponent";

    /**
     * 播放失败组件
     */
    String COMPONENT_PLAYER_FAIL = "PlayerFailComponent";

    /**
     * 礼物面板组件
     */
    String COMPONENT_GIFT_PANEL = "GiftPanelComponent";

    /**
     * Pia 戏组件
     */
    String COMPONENT_PIA_PANEL = "PiaModeComponent";

    /**
     * 直播间通用引导组件
     */
    String COMPONENT_COMMON_GUIDE = "CommonGuideComponent";

    /**
     * 去购买专辑组件
     */
    String COMPONENT_COMMON_BUY_ALBUM = "BuyAlbumComponent";

    /**
     * 弹幕游戏控制面板组件
     */
    String COMPONENT_GAME_CONTROL = "GameControlComponent";

    /**
     * AI惩罚消息
     */
    String COMPONENT_AI_PENALTY = "AIPenaltyComponent";

    /**
     * 红包雨
     */
    String COMPONENT_RED_PACKET_RAIN = "RedPacketRainComponent";

    /**
     * 直播间双击送小心心、发送互动表情，交互组件
     */
    String COMPONENT_CLICK_COMBO_EFFECT = "ClickComboEffectComponent";

    /**
     * 清屏滑动组件
     */
    String COMPONENT_SLIDE_CLEAR_SCREEN = "SlideClearScreenComponent";

    /**
     * 预约组件
     */
    String COMPONENT_SUBSCRIBE_ROOM = "SubscribeRoomComponent";

    /**
     * 关播后更多直播推荐组件
     */
    String COMPONENT_RECOMMEND = "RecommendLiveComponent";


    /**
     * 初始化组件配置信息
     *
     * @param map 组件配置信息
     */
    void initComponentConfig(@NonNull Map<String, ComponentInitArgs> map);

    /**
     * 返回键处理优先级
     *
     * @return 组件处理返回键的优先级，后添加的后处理，目前，退出组件最后处理
     **/
    ArrayList<String> getBackPressPriorities();

    /**
     * 详情分发时，有一些组件必须调用这个它的bindData方法，即使之前这个组件从未被调用过，如果是这样的组件，在这里配置一下
     *
     * @return 返回需要调用 bindData 方法的组件名称
     **/
    ArrayList<String> getComponentsWhoseBindDataMustCallAfterDetailSuccess();

    /**
     * 第 1 批次加载组件 如有变动需找谊宣审批
     */
    ArrayList<ComponentLoaderConfig> getFirstLoaderComponents();

    /**
     * 第 2 批次加载组件
     */
    ArrayList<ComponentLoaderConfig> getSecondLoaderComponents();

    /**
     * 组件配置清单
     */
    abstract class BaseCompConfig implements IBaseRoomCompConfig{
        Map<String, ComponentInitArgs> mComponentConfig;

        protected ArrayList<String> mComponentBackPressPriorities;

        @Override
        public final ComponentInitArgs getComponentConfig(String component) {
            if (mComponentConfig == null) {
                mComponentConfig = new HashMap<>();
                initComponentConfig(mComponentConfig);
            }
            return mComponentConfig.get(component);
        }

        @Override
        public int getComponentBackPressPriority(String name) {
            if (mComponentBackPressPriorities == null) {
                mComponentBackPressPriorities = getBackPressPriorities();
            }
            if (mComponentBackPressPriorities != null && mComponentBackPressPriorities.contains(name)) {
                return mComponentBackPressPriorities.indexOf(name) + 1;
            }
            return Component.COMMON_BACK_PRESS_PRIORITY;
        }

    }

    abstract class BaseRoomCompConfig extends BaseCompConfig {

        @Override
        public void initComponentConfig(@NonNull Map<String, ComponentInitArgs> map) {
            map.put(COMPONENT_ROOM_BACKGROUND, new ComponentInitArgs()
                    .setLayoutInflateWhenUse(true)
                    .setNoUiComponent(true)
                    .setClazz(RoomBackgroundComponent.class));

            map.put(COMPONENT_SLIDE_CLEAR_SCREEN, new ComponentInitArgs()
                    .setLayoutInflateWhenUse(true)
                    .setViewStubId(R.id.live_slide_clear_screen_view_stub)
                    .setComponentLayoutId(R.layout.live_host_slide_clear_screen_contrlller)
                    .setClazz(SlideClearScreenComponent.class));

            map.put(COMPONENT_EXIT_ROOM, new ComponentInitArgs()
                    .setNoUiComponent(true)
                    .setLayoutInflateWhenUse(false)
                    .setClazz(ExitRoomComponent.class));


            try {
                //推荐直播组件依赖的首页的卡片adapter，下沉到liveHost中比较困难，只能先这样简单处理
                map.put(COMPONENT_RECOMMEND, new ComponentInitArgs()
                        .setComponentLayoutId(R.layout.livehost_layout_recommend)
                        .setViewStubId(R.id.livecomm_room_layer_9)
                        .setLayoutInflateWhenUse(true)
                        .setClazz(LiveRouter.getLamiaAction().getRecommendLiveComponent()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public ArrayList<String> getComponentsWhoseBindDataMustCallAfterDetailSuccess() {
            ArrayList<String> list = new ArrayList<>();
            list.add(COMPONENT_EXIT_ROOM);
            return list;
        }

        @Override
        public ArrayList<ComponentLoaderConfig> getFirstLoaderComponents() {
            return new ArrayList<>();
        }

        @Override
        public ArrayList<ComponentLoaderConfig> getSecondLoaderComponents() {
            return new ArrayList<>();
        }
    }

    abstract class BaseGroupComponentConfig extends BaseCompConfig {
        @Override
        public void initComponentConfig(@NonNull Map<String, ComponentInitArgs> map) {

        }
    }
}
