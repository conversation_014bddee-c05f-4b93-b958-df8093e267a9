<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@+id/live_layout_menu"
    android:layout_width="40dp"
    android:layout_height="38dp"
    tools:layout_height="39dp"
    android:clipChildren="false"
    tools:background="#000"
    android:layout_marginTop="2dp"
    android:layout_marginStart="2dp"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginLeft="2dp">

    <ImageView
        android:id="@+id/live_iv_menu"
        android:layout_width="36dp"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:scaleType="fitCenter"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/live_iv_menu_dot"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_centerVertical="true"
        android:layout_marginBottom="40.5dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="2dp"
        android:src="@drawable/livehost_red_dot"
        android:visibility="visible"
        tools:visibility="visible"
        android:layout_alignParentRight="true" />
</RelativeLayout>