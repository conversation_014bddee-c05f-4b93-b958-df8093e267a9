<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:visibility="gone"
    tools:visibility="visible"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content">

    <!--没关注任何人    -->
    <RelativeLayout
        android:id="@+id/live_rl_no_one_attention"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="30dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:paddingBottom="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="- 暂无关注的人，看看其他精彩直播 -"
            android:textColor="@color/live_color_999999_888888"
            android:textSize="14sp" />

    </RelativeLayout>


    <!--关注的人暂未开播    -->
    <RelativeLayout
        android:id="@+id/live_rl_no_one_in_live"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="30dp"
        android:paddingBottom="30dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/live_tv_no_one_in_live"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:text="- 关注的人暂未开播 -"
            android:textColor="@color/live_color_999999_888888"
            android:textSize="14sp" />



    </RelativeLayout>

    <!--关注的人暂无开播动态    -->
    <RelativeLayout
        android:id="@+id/live_rl_no_one_have_activity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="30dp"
        android:paddingBottom="30dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/live_tv_no_one_have_activity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:text="- 关注的人暂无开播动态 -"
            android:textColor="@color/live_color_999999_888888"
            android:textSize="14sp" />



    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>