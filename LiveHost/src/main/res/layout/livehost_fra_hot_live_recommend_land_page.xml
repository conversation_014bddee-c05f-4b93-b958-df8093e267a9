<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/framework_color_ffffff_131313"
    android:orientation="vertical">

    <View
        android:id="@+id/live_sb"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:contentDescription="@string/framework_title_bar_contentDescription"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#33000000"
        tools:layout_height="20dp" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_title_bar_root"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        app:layout_constraintTop_toBottomOf="@id/live_sb">

        <ImageView
            android:id="@+id/live_back_btn"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginStart="6dp"
            android:contentDescription="返回"
            android:paddingHorizontal="10dp"
            android:src="@drawable/livehost_ic_arrow_return_left_line_regular_24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/live_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="50dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/livehost_color_131313_FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/live_back_btn"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="热门直播" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/live_recommend_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_title_bar_root" />
</androidx.constraintlayout.widget.ConstraintLayout>