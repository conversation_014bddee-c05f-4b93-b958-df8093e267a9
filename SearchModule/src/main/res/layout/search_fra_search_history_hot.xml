<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:psts="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ximalaya.ting.android.host.view.PullToRefreshScrollView
        android:id="@+id/search_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="60dp"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/search_top_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <RelativeLayout
                    android:layout_marginTop="4dp"
                    android:id="@+id/search_rl_search_ad_banner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    android:visibility="gone">

                    <com.ximalaya.ting.android.framework.view.image.RoundImageView
                        android:adjustViewBounds="true"
                        android:layout_centerHorizontal="true"
                        android:id="@+id/search_iv_ad_banner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        psts:corner_radius="4dp"
                        android:scaleType="fitCenter" />

                    <TextView
                        android:layout_alignRight="@+id/search_iv_ad_banner"
                        android:layout_width="20dp"
                        android:layout_height="12dp"
                        android:text="广告"
                        android:layout_alignBottom="@+id/search_iv_ad_banner"
                        android:gravity="center"
                        android:textSize="8sp"
                        android:layout_marginRight="2dp"
                        android:layout_marginBottom="2dp"
                        android:background="@drawable/search_bg_corner_2_4d000000"
                        android:textColor="#B3FFFFFF"/>

                </RelativeLayout>

                <TextView
                    android:id="@+id/search_label_history"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/search_rl_search_ad_banner"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="7dp"
                    android:fontFamily="sans-serif-light"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:text="搜索历史"
                    android:textColor="@color/search_color_111111_cfcfcf"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_below="@+id/search_rl_search_ad_banner"
                    android:id="@+id/search_clear_history"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="5dp"
                    android:layout_marginTop="5dp"
                    android:contentDescription="@string/search_content_description_clear_history_text"
                    android:padding="10dp"
                    android:tint="@color/host_color_999999_66666b"
                    android:scaleType="fitXY"
                    android:src="@drawable/host_ic_standard_title_delete" />

                <com.ximalaya.ting.android.host.view.layout.FlowLayout
                    android:id="@+id/search_history_section_one"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/search_label_history"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp" />

                <com.ximalaya.ting.android.host.view.layout.FlowLayout
                    android:id="@+id/search_history_section_two"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/search_history_section_one"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/search_recommend_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/search_history_section_two"
                    android:layout_marginTop="13dp"
                    android:layout_marginBottom="12dp"
                    android:layout_marginLeft="16dp"
                    android:includeFontPadding="false"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:text="推荐搜索"
                    android:textColor="@color/search_color_111111_cfcfcf"
                    android:textSize="16sp"
                    android:fontFamily="sans-serif-light"
                    android:textStyle="bold"
                    android:visibility="gone"/>

                <com.ximalaya.ting.android.host.view.layout.FlowLayout
                    android:id="@+id/search_recommend_hotwords"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/search_recommend_title"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp" />

            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="4dp"/>

            <com.astuetz.PagerSlidingTabStrip
                android:id="@+id/search_tab"
                style="@style/host_my_pager_sliding_tab_strip_style"
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:layout_marginLeft="3dp"
                android:background="@null"
                android:clipToPadding="false"
                psts:pstsActivateTabTextBold="true"
                psts:pstsActivateTextColor="@color/search_color_111111_cfcfcf"
                psts:pstsDeactivateTextColor="@color/search_color_999999_888888"
                psts:pstsIndicatorColor="@color/search_color_f86442"
                psts:pstsIndicatorCornerRadius="1dp"
                psts:pstsIndicatorHeight="4dp"
                psts:pstsIndicatorWidth="20dp"
                psts:pstsShouldExpand="false"
                psts:pstsTextAllCaps="false"
                psts:pstsSmoothScroll="true"
                psts:pstsTabSwitch="true" />

            <com.ximalaya.ting.android.host.view.other.MyViewPager
                android:id="@+id/search_view_pager"
                android:layout_width="match_parent"
                android:layout_height="430dp"
                android:layout_marginTop="8dp"/>

            <RelativeLayout
                android:id="@+id/search_rl_ad_banner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp">

                <com.ximalaya.ting.android.search.view.ad.SearchAdView
                    android:visibility="gone"
                    android:layout_marginBottom="25dp"
                    android:id="@+id/search_ad_banner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </RelativeLayout>

        </LinearLayout>

    </com.ximalaya.ting.android.host.view.PullToRefreshScrollView>

    <ViewStub
        android:id="@+id/search_vs_no_network"
        android:layout_width="300dp"
        android:layout_height="300dp"
        android:layout_gravity="center"
        android:inflatedId="@+id/search_id_search_network_error"
        android:layout="@layout/host_no_net_layout" />
</merge>
