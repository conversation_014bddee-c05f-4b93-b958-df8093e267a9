<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/search_filter_layout"
        layout="@layout/search_layout_filter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:visibility="visible" />

    <com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
        android:id="@id/host_id_stickynavlayout_innerscrollview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:cacheColorHint="@color/search_transparent"
        android:clipToPadding="false"
        android:divider="@null"
        android:dividerHeight="0dp"
        android:fadingEdge="none"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:listSelector="@color/search_transparent"
        android:paddingBottom="80dp" />

    <include layout="@layout/search_view_search_right_bottom" />

    <View
        android:id="@+id/search_v_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/search_color_b3000000"
        android:visibility="gone"/>
</merge>