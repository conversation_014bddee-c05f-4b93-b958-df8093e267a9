<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="@dimen/search_card_horizontal_margin"
    android:paddingRight="@dimen/search_card_horizontal_margin"
    android:descendantFocusability="blocksDescendants"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/search_item_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/search_color_card_bg"
        app:layout_constraintTop_toTopOf="parent">

        <com.ximalaya.ting.android.search.view.SquareBorderImageView
            android:id="@+id/search_iv_cover"
            android:layout_width="92dp"
            android:layout_height="92dp"
            android:layout_marginStart="@dimen/search_card_content_horizontal_margin"
            android:layout_marginTop="12dp"
            android:scaleType="centerCrop"
            android:src="@drawable/host_default_album"
            app:corner_radius="4dp"
            app:border_width="0.5dp"
            app:border_color="#80cccccc"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <com.ximalaya.ting.android.host.view.MyRoundImageView
            android:id="@+id/search_iv_cover_tag"
            android:layout_width="92dp"
            android:layout_height="27dp"
            android:scaleType="centerCrop"
            app:leftBottomRadius_x="4dp"
            app:leftBottomRadius_y="4dp"
            app:rightBottomRadius_x="4dp"
            app:rightBottomRadius_y="4dp"
            app:layout_constraintStart_toStartOf="@+id/search_iv_cover"
            app:layout_constraintEnd_toEndOf="@+id/search_iv_cover"
            app:layout_constraintBottom_toBottomOf="@+id/search_iv_cover"/>

        <TextView
            android:id="@+id/search_tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="@dimen/search_card_content_horizontal_margin"
            android:ellipsize="end"
            android:maxLines="2"
            style="@style/host_style_text_list_title_search_2024"
            tools:text="Beoplay EX耳机 全新上市 自适应主动降噪第五代真无限"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/search_iv_cover"
            app:layout_constraintTop_toTopOf="@+id/search_iv_cover"
            app:layout_constraintBottom_toTopOf="@id/search_tags_layout"
            app:layout_constraintEnd_toEndOf="parent"/>

        <com.ximalaya.ting.android.host.view.layout.FlowLayout
            android:id="@+id/search_tags_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="12dp"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="@+id/search_tv_title"
            app:layout_constraintTop_toBottomOf="@+id/search_tv_title"
            app:layout_constraintBottom_toTopOf="@id/search_tv_pay_price"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/search_barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="search_iv_cover,search_tv_pay_price" />

        <TextView
            android:id="@+id/search_price_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="1dp"
            android:textColor="@color/search_color_ff4646"
            android:textSize="12sp"
            android:text="￥"
            app:layout_constraintStart_toStartOf="@+id/search_tv_title"
            app:layout_constraintBottom_toBottomOf="@+id/search_tv_pay_price"/>

        <TextView
            android:id="@+id/search_tv_pay_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:textColor="@color/search_color_ff4646"
            android:textSize="16sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-light"
            tools:text="2999"
            app:layout_constraintStart_toEndOf="@+id/search_price_tag"
            app:layout_constraintTop_toBottomOf="@id/search_tags_layout"
            app:layout_constraintBottom_toBottomOf="@id/search_iv_cover"/>

        <TextView
            android:id="@+id/search_tv_market_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginBottom="1dp"
            android:textColor="@color/host_color_acacaf_66666b"
            android:textSize="12sp"
            tools:text="￥4999"
            app:layout_constraintStart_toEndOf="@+id/search_tv_pay_price"
            app:layout_constraintBottom_toBottomOf="@+id/search_tv_pay_price"/>

        <ImageView
            android:id="@+id/search_iv_vip_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginBottom="2dp"
            app:layout_goneMarginStart="6dp"
            android:src="@drawable/search_product_vip"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintStart_toEndOf="@+id/search_tv_market_price"
            app:layout_constraintBottom_toBottomOf="@+id/search_tv_pay_price"/>

        <Space
            android:id="@+id/search_v_space"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            app:layout_constraintTop_toBottomOf="@id/search_barrier"/>

        <View
            android:id="@+id/search_divider"
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="@dimen/search_card_content_horizontal_margin"
            android:background="@color/host_color_lineColor2_black"
            app:layout_constraintStart_toStartOf="@+id/search_tv_title"
            app:layout_constraintTop_toBottomOf="@+id/search_v_space"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>