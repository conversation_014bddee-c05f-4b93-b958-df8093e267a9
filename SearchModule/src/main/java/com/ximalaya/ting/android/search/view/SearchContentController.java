package com.ximalaya.ting.android.search.view;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.search.utils.SearchUiUtils;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;

/**
 * Created by ervin.li on 2018/11/23.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public abstract class SearchContentController<T extends RecyclerView.ViewHolder, D> implements ISearchListContentController {
    protected RecyclerView.Adapter adapter;
    protected ISearchListContentProvider<D> listContentProvider;
    private IListContentItemClickedListener<T, D> listContentItemClickedListener;
    protected CharSequence title;
    protected List<D> list;
    protected int dividerHeight;
    private View bottomDivider;
    private RecyclerView contentRv;
    private TextView headTitleTv;

    abstract void onItemClick(int position, D d, T holder);

    public ISearchListContentProvider<D> getContentListProvider() {
        return listContentProvider;
    }

    public SearchContentController(ISearchListContentProvider<D> listContentProvider) {
        if (listContentProvider == null) {
            Logger.e(new Exception("empty content provider"));
            return;
        }
        this.listContentProvider = listContentProvider;
        this.list = listContentProvider.getList();
        this.dividerHeight = listContentProvider.getBottomLineHeight();
        this.title = listContentProvider.getTitle();
    }

    public SearchContentController(CharSequence title, List<D> list, int dividerHeight) {
        this.title = title;
        this.list = list;
        this.dividerHeight = dividerHeight;
        this.listContentProvider = new ISearchListContentProvider<D>() {
            @Override
            public List<D> getList() {
                return SearchContentController.this.list;
            }

            @Override
            public CharSequence getTitle() {
                return SearchContentController.this.title;
            }

            @Override
            public int getBottomLineHeight() {
                return SearchContentController.this.dividerHeight;
            }
        };
    }

    public SearchContentController(CharSequence title, int dividerHeight) {
        this.title = title;
        this.dividerHeight = dividerHeight;
        this.listContentProvider = new ISearchListContentProvider<D>() {
            @Override
            public List<D> getList() {
                return SearchContentController.this.list;
            }

            @Override
            public CharSequence getTitle() {
                return SearchContentController.this.title;
            }

            @Override
            public int getBottomLineHeight() {
                return SearchContentController.this.dividerHeight;
            }
        };
    }


    public void updateList(List<D> list) {
        this.list = list;
        notifyAdapter();
    }

    public void updateUi(List<D> list) {
        this.list = list;
        SearchUiUtils.setTextWithVisibleStateAuto(headTitleTv, title);
        SearchUiUtils.setViewHeight(bottomDivider, dividerHeight);
        if (contentRv != null && getAdapter() != null) {
            contentRv.setAdapter(getAdapter());
        }
    }

    public void addAllListWithNotify(List<D> list) {
        if (list == null) {
            this.list = list;
        } else {
            this.list.addAll(list);
        }
        notifyAdapter();
    }

    @Override
    public void notifyAdapter() {
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
    }

    abstract T onCreateViewHolder(@NonNull ViewGroup parent, int viewType);

    abstract void onBindViewHolder(T holder, int position);

    abstract D getItem(int position);

    @Override
    public RecyclerView.Adapter getAdapter() {
        if (adapter == null) {
            adapter = new RecyclerView.Adapter<T>() {
                @NonNull
                @Override
                public T onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
                    return SearchContentController.this.onCreateViewHolder(parent, viewType);
                }

                @Override
                public void onBindViewHolder(@NonNull final T holder, int position) {
                    final D d = getItem(position);
                    SearchUiUtils.setOnClickListener(holder.itemView, new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (listContentItemClickedListener != null) {
                                listContentItemClickedListener.onItemClick(holder.getAdapterPosition(), d, holder);
                            } else {
                                onItemClick(holder.getAdapterPosition(), d, holder);
                            }
                        }
                    });
                    SearchContentController.this.onBindViewHolder(holder, position);
                }

                @Override
                public int getItemCount() {
                    return ToolUtil.isEmptyCollects(SearchContentController.this.list) ? 0 : SearchContentController.this.list.size();
                }
            };
        }
        return adapter;
    }

    @Override
    public void initViewStyleAndData(TextView headTitleTv, RecyclerView contentRv, View bottomDivider) {
        this.headTitleTv = headTitleTv;
        this.contentRv = contentRv;
        this.bottomDivider = bottomDivider;
    }

    public interface IListContentItemClickedListener<T extends RecyclerView.ViewHolder, D> {
        void onItemClick(int position, D d, T holder);
    }

    public void setOnItemClickListener(IListContentItemClickedListener<T, D> listener) {
        this.listContentItemClickedListener = listener;
    }

}
