package com.ximalaya.ting.android.search.page;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.search.model.SearchAlbum;
import com.ximalaya.ting.android.search.model.SearchDocsList;
import com.ximalaya.ting.android.search.model.SearchOfflineData;
import com.ximalaya.ting.android.search.SearchConstants;
import com.ximalaya.ting.android.search.base.BaseSearchFragment;
import com.ximalaya.ting.android.search.utils.SearchTraceUtils;
import com.ximalaya.ting.android.search.utils.SearchUiUtils;
import com.ximalaya.ting.android.search.view.SearchAlbumHorizontalContentController;
import com.ximalaya.ting.android.search.view.SearchAlbumVerticalContentController;
import com.ximalaya.ting.android.search.view.SearchContentController;
import com.ximalaya.ting.android.search.view.SearchListContentView;
import com.ximalaya.ting.android.search.view.holder.SearchAlbumViewHolder;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.search.R;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by ervin.li on 2018/11/23.
 * 专辑下架信息页面
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class SearchOfflineFragment extends BaseSearchFragment<SearchOfflineData> implements View.OnClickListener, SearchContentController.IListContentItemClickedListener<SearchAlbumViewHolder, SearchAlbum> {
    private SearchListContentView mSuggestAlbumListContentView;
    private SearchAlbumVerticalContentController mSuggestAlbumListController;

    private SearchListContentView mOfflineAlbumListContentView;
    private SearchAlbumHorizontalContentController mOfflineAlbumListController;
    private String keyword;


    public SearchOfflineFragment() {
        super(true, null);
    }

    @Override
    protected LoadCompleteType updatePage(SearchOfflineData data) {
        if (data == null) return LoadCompleteType.NOCONTENT;
        boolean noContent = true;
        SearchDocsList<SearchAlbum> offlineAlbums = data.getOfflineAlbums();
        if (offlineAlbums != null && !ToolUtil.isEmptyCollects(offlineAlbums.getDocs()) && mOfflineAlbumListController != null) {
            SearchUiUtils.setVisible(View.VISIBLE, mOfflineAlbumListContentView);
            mOfflineAlbumListController.updateUi(offlineAlbums.getDocs());
            noContent = false;
        } else {
            SearchUiUtils.setVisible(View.GONE, mOfflineAlbumListContentView);
        }
        List<SearchAlbum> recommendAlbums = data.getRecommendAlbums();
        if (!ToolUtil.isEmptyCollects(recommendAlbums) && mSuggestAlbumListController != null) {
            SearchUiUtils.setVisible(View.VISIBLE, mSuggestAlbumListContentView);
            mSuggestAlbumListController.updateUi(recommendAlbums);
            noContent = false;
        } else {
            SearchUiUtils.setVisible(View.GONE, mSuggestAlbumListContentView);
        }
        return noContent ? LoadCompleteType.NOCONTENT : LoadCompleteType.OK;
    }

    @Override
    protected SearchOfflineData parse(String json, long timeStamp) {
        if (TextUtils.isEmpty(json)) return null;
        SearchOfflineData searchOfflineData = null;
        try {
            searchOfflineData = SearchOfflineData.parse(json);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return searchOfflineData;
    }

    @Override
    protected String getPageLogicName() {
        return SearchOfflineFragment.class.getSimpleName();
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        parseArgs(getArguments());
        SearchUiUtils.setText((TextView) findViewById(com.ximalaya.ting.android.host.R.id.title_tv), R.string.search_search_offline_page_title);
        SearchUiUtils.setOnClickListener(findViewById(com.ximalaya.ting.android.host.R.id.back_btn), this);
        SearchUiUtils.setVisible(View.VISIBLE, findViewById(com.ximalaya.ting.android.host.R.id.title_bar_divide));

        mOfflineAlbumListContentView = findViewById(R.id.search_slcv_search_offline_top_album);
        mSuggestAlbumListContentView = findViewById(R.id.search_slcv_search_suggest_album);

        mOfflineAlbumListController = new SearchAlbumHorizontalContentController(getString(R.string.search_search_offline_title), 6);
        mOfflineAlbumListContentView.setContentViewController(mOfflineAlbumListController);
        mOfflineAlbumListContentView.setDisallowInterceptTouchEventView(getSlideView());

        mSuggestAlbumListController = new SearchAlbumVerticalContentController(getString(R.string.search_search_recommend), 0);
        mSuggestAlbumListContentView.setContentViewController(mSuggestAlbumListController);
        mSuggestAlbumListController.setOnItemClickListener(this);
    }

    @Override
    protected void loadData() {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        Map<String, String> params = new HashMap<>();
        params.put(SearchConstants.SEARCH_KEYWORD, keyword);
        loadData(UrlConstants.getInstanse().getSearchOfflineAlbumUrl(), params);
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    protected void parseArgs(Bundle args) {
        if (args == null) return;
        keyword = args.getString(SearchConstants.SEARCH_KEYWORD);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.search_fra_search_offline;
    }

    public static SearchOfflineFragment newInstance(String keyword) {
        SearchOfflineFragment fragment = new SearchOfflineFragment();
        Bundle args = new Bundle();
        args.putString(SearchConstants.SEARCH_KEYWORD, keyword);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == com.ximalaya.ting.android.host.R.id.back_btn) {
            finish();
        }
    }

    @Override
    public void onItemClick(int position, SearchAlbum o, SearchAlbumViewHolder holder) {
        if (o == null) return;
        SearchTraceUtils.tracePageClick("unshelveAlbum", SearchTraceUtils.getKeyWord(), "recommendAlbum", null,"album", String.valueOf(o.getId()));
        AlbumEventManage.startMatchAlbumFragment(o.getId(), AlbumEventManage.FROM_SEARCH,
                ConstantsOpenSdk.PLAY_FROM_SEARCH, "", "", -1, getActivity());
    }
}
