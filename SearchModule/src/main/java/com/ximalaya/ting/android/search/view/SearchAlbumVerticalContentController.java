package com.ximalaya.ting.android.search.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.RectF;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.ui.AlbumTagUtilNew;
import com.ximalaya.ting.android.search.R;
import com.ximalaya.ting.android.search.model.SearchAlbum;
import com.ximalaya.ting.android.search.utils.SearchUiUtils;
import com.ximalaya.ting.android.search.view.holder.SearchAlbumViewHolder;

import java.util.List;

/**
 * Created by ervin.li on 2018/11/23.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class SearchAlbumVerticalContentController extends SearchContentController<SearchAlbumViewHolder, SearchAlbum> {

    public SearchAlbumVerticalContentController(ISearchListContentProvider<SearchAlbum> listContentProvider) {
        super(listContentProvider);
    }

    public SearchAlbumVerticalContentController(CharSequence title, List<SearchAlbum> list, int dividerHeight) {
        super(title, list, dividerHeight);
    }

    public SearchAlbumVerticalContentController(CharSequence title, int dividerHeight) {
        super(title, dividerHeight);
    }


    @Override
    void onItemClick(int position, SearchAlbum searchAlbum, SearchAlbumViewHolder holder) {

    }

    @Override
    SearchAlbumViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        Context context = parent.getContext();
        View view = LayoutInflater.from(context).inflate(R.layout.search_item_search_vertical_content_recommend, parent, false);
        return new SearchAlbumViewHolder(view);
    }

    @Override
    void onBindViewHolder(SearchAlbumViewHolder holder, int position) {
        SearchAlbum searchAlbum = getItem(position);
        if (holder == null || holder.itemView == null || searchAlbum == null) return;
        CharSequence title = searchAlbum.getIsFinished() == 2 ? ToolUtil.getTitleWithPicAheadCenterAlign(holder.itemView.getContext(), searchAlbum.getTitle(), R.drawable.search_tag_complete_new) : searchAlbum.getTitle();
        SearchUiUtils.setText(holder.title, title);
        SearchUiUtils.setTextWithVisibleStateAuto(holder.subTitle, searchAlbum.getIntro());
        SearchUiUtils.displayImage(holder.cover, searchAlbum.getCoverPath());
        SearchUiUtils.setText(holder.play, StringUtil.getFriendlyNumStr(searchAlbum.getPlay()));
        SearchUiUtils.setText(holder.track, SearchUiUtils.getTrackCountStr(searchAlbum.getTracks()));
        AlbumTagUtilNew.getInstance().loadImage(holder.coverTag, searchAlbum.getAlbumSubscriptValue());
    }

    @Override
    SearchAlbum getItem(int position) {
        List<SearchAlbum> list = listContentProvider != null ? listContentProvider.getList() : null;
        return !ToolUtil.isEmptyCollects(list) && position < list.size() && position >= 0 ? list.get(position) : null;
    }


    @Override
    public void initViewStyleAndData(TextView headTitleTv, RecyclerView contentRv, View bottomDivider) {
        super.initViewStyleAndData(headTitleTv, contentRv, bottomDivider);
        if (contentRv != null) {
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(contentRv.getContext()) {
                @Override
                public boolean canScrollVertically() {
                    return false;
                }
            };
            linearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            contentRv.setLayoutManager(linearLayoutManager);
            contentRv.addItemDecoration(SearchUiUtils.createItemDecoration(Color.parseColor("#F3F4F5"), 94, new RectF(0, 0, 0, 0.5f)));
        }
    }
}
