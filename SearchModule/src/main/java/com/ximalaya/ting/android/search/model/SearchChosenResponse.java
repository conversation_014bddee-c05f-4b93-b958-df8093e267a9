package com.ximalaya.ting.android.search.model;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.search.SearchConstants;

import org.json.JSONObject;

/**
 * Created by ervin.li on 2018/11/5.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class SearchChosenResponse {
    private boolean isNoCopyRightForAlbum;
    private SearchCenterWord rq;
    private int numFound;
    private SearchRiskTips riskTips;
    private SearchRecommendQ recommendQ;
    private int totalPage;
    private String searchMeta;

    public boolean isNoCopyRightForAlbum() {
        return isNoCopyRightForAlbum;
    }

    public void setNoCopyRightForAlbum(boolean noCopyRightForAlbum) {
        isNoCopyRightForAlbum = noCopyRightForAlbum;
    }

    public SearchCenterWord getRq() {
        return rq;
    }

    public int getNumFound() {
        return numFound;
    }

    public void setNumFound(int numFound) {
        this.numFound = numFound;
    }

    public void setRq(SearchCenterWord rq) {
        this.rq = rq;
    }

    public SearchRiskTips getRiskTips() {
        return riskTips;
    }

    public void setRiskTips(SearchRiskTips riskTips) {
        this.riskTips = riskTips;
    }

    public SearchRecommendQ getRecommendQ() {
        return recommendQ;
    }

    public void setRecommendQ(SearchRecommendQ recommendQ) {
        this.recommendQ = recommendQ;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public String getSearchMeta() {
        return searchMeta;
    }

    public void setSearchMeta(String searchMeta) {
        this.searchMeta = searchMeta;
    }

    public static SearchChosenResponse parse(JSONObject data) {
        if (data.has(SearchConstants.RESPONSE)) {
            try {
                JSONObject jsonObject = data.optJSONObject(SearchConstants.RESPONSE);
                if (jsonObject != null) {
                    SearchChosenResponse searchChosenResponse = new SearchChosenResponse();
                    searchChosenResponse.setNoCopyRightForAlbum(jsonObject.optBoolean(SearchConstants.IS_NO_COPY_RIGHT_FOR_ALBUM));
                    //中心词，搜索词包含多个搜索词时提示用户其他搜索词
                    SearchCenterWord centerWord = null;
                    String cw = jsonObject.optString(SearchConstants.CENTER_WORDS);
                    if (!TextUtils.isEmpty(cw)) {
                        centerWord = new SearchCenterWord(cw);
                    }
                    searchChosenResponse.setNumFound(jsonObject.optInt("numFound"));
                    searchChosenResponse.setRq(centerWord);
                    if (jsonObject.has(SearchConstants.RISK_TIPS)) {
                        searchChosenResponse.setRiskTips(SearchRiskTips.parse(jsonObject.optString(SearchConstants.RISK_TIPS)));
                    }
                    if (jsonObject.has(SearchConstants.RECOMMENDQ)) {
                        searchChosenResponse.setRecommendQ(SearchRecommendQ.parse(jsonObject.optString(SearchConstants.RECOMMENDQ)));
                    }
                    if (jsonObject.has("totalPage")) {
                        int totalPage = jsonObject.optInt("totalPage");
                        searchChosenResponse.setTotalPage(totalPage);
                    }
                    if (jsonObject.has(SearchConstants.SEARCH_META)) {
                        searchChosenResponse.setSearchMeta(jsonObject.optString(SearchConstants.SEARCH_META));
                    }
                    return searchChosenResponse;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}

