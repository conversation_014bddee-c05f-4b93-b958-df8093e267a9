package com.ximalaya.ting.android.live.video.components.chatlist.chat

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import androidx.core.view.updateLayoutParams
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.live.common.chatlist.base.IChatListDataTypeConfig
import com.ximalaya.ting.android.live.common.chatlist.base.MultiTypeListDataType
import com.ximalaya.ting.android.live.common.chatlist.constant.ChatItemViewType
import com.ximalaya.ting.android.live.common.chatlist.constant.ChatListViewConstant
import com.ximalaya.ting.android.live.common.chatlist.msg.RoleType
import com.ximalaya.ting.android.live.common.chatlist.msg.course.CourseChatListMessageDispatcher
import com.ximalaya.ting.android.live.common.chatlist.msg.dispatcher.IChatListMessageDispatcher
import com.ximalaya.ting.android.live.common.chatlist.trace.trackCourseAnnounceItemClick
import com.ximalaya.ting.android.live.common.chatlist.trace.trackLiveFollowViewClick
import com.ximalaya.ting.android.live.common.chatlist.trace.trackLiveJoinClubViewClick
import com.ximalaya.ting.android.live.common.chatlist.trace.trackLiveShareViewClick
import com.ximalaya.ting.android.live.common.chatlist.view.delegate.CourseChatItemViewDelegate
import com.ximalaya.ting.android.live.common.chatlist.view.listener.ICourseChatItemClickListener
import com.ximalaya.ting.android.live.common.chatlist.viewtype.courseItemViewType
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo
import com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionInfo
import com.ximalaya.ting.android.live.common.lib.entity.LiveViewPositionType
import com.ximalaya.ting.android.live.common.lib.utils.LiveRouterUtil
import com.ximalaya.ting.android.live.common.lib.utils.LiveViewUtil
import com.ximalaya.ting.android.live.common.lib.utils.isVisible
import com.ximalaya.ting.android.live.common.lib.utils.showOrGone
import com.ximalaya.ting.android.live.common.view.viewpostion.LiveViewPositionManager
import com.ximalaya.ting.android.live.host.components.chatlist.BaseChatListComponent
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatMessage
import com.ximalaya.ting.android.live.lib.chatroom.entity.custom.CustomPublishTopicMessage
import com.ximalaya.ting.android.live.lib.livetopic.AnnounceData
import com.ximalaya.ting.android.live.lib.livetopic.AnnounceDataCache
import com.ximalaya.ting.android.live.video.R
import com.ximalaya.ting.android.live.video.components2.ICourseDispatchData
import com.ximalaya.ting.android.live.video.constanst.UIMode
import com.ximalaya.ting.android.live.video.data.model.CourseRoomDetail
import com.ximalaya.ting.android.live.video.util.LiveCourseUtils

/**
 * 课程直播聊天列表组件，负责消息收发的展示与互动
 *
 * Created by zoey on 2024/3/18.
 * <AUTHOR>
 * @email <EMAIL>
 */
class CourseChatListComponent :
    BaseChatListComponent<CourseChatItemViewDelegate, CourseRoomDetail>(),
    ICourseChatListComponent, ICourseChatItemClickListener,
    LiveViewPositionManager.IPositionListener {

    @UIMode
    private var uiMode: Int = 0
    private var videoWidth: Int = 0
    private var videoHeight: Int = 0

    private var isStatusPanelVisible: Boolean = false
    private var isTrySeeEndPanelVisible: Boolean = false

    private var landLayoutParams: RelativeLayout.LayoutParams? = null
    private var portLayoutParams: RelativeLayout.LayoutParams? = null
    private var fullLayoutParams: RelativeLayout.LayoutParams? = null

    // 是否是主讲区
    private var isLecture = false

    // 初始进入直播时的直播状态
    private var originalStatus = 0

    // 是否已经添加首条告知消息
    private var isAddFirstWarning = false
    private val firstWarningTask = Runnable {
        if (!isAddFirstWarning) {
            produceFirstMessage(activity, hostUid, hostData?.anchorName ?: "")
            sendAnnounceTopicMsg()
            isAddFirstWarning = true
        }
    }

    override fun onCreateView(view: View?) {
        super.onCreateView(view)
        LiveViewPositionManager.getInstance().addListener(
            this,
            LiveViewPositionType.TYPE_BOTTOM,
            LiveViewPositionType.TYPE_SUBSCRIBE_PANEL,
            LiveViewPositionType.TYPE_COURSE_AUTH_BOTTOM
        )
    }

    override fun readDispatchDataFromHost() {
        super.readDispatchDataFromHost()
        try {
            uiMode = componentHost.dispatchData.getValue(ICourseDispatchData.KEY_UI_MODE)
            updateUIMode(uiMode)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun bindData(hostData: CourseRoomDetail) {
        super.bindData(hostData)

        chatListContainer?.setRoomInfo(roomId)
        chatListContainer?.setLiveRoomBizType(roomBizType)
        itemDelegate?.setRoomInfo(roomId, hostUid, liveMediaType)

        originalStatus = hostData.status
        HandlerManager.postOnUIThreadDelay(firstWarningTask, 800)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initComponentViewAfterInflated(view: View) {
        super.initComponentViewAfterInflated(view)

        this.videoWidth = LiveViewUtil.getScreenWidth()
        this.videoHeight = LiveViewUtil.getScreenWidth() * 9 / 16

        chatListContainer?.setOnTouchListener { _, _ ->
            getComponentInteractionSafety(
                ICourseChatListComponentInteraction::class.java
            )?.onTouchChatList()
            false
        }
        LiveViewPositionManager.getInstance().addListener(this,LiveViewPositionType.TYPE_SUBSCRIBE_PANEL)
    }

    override fun onSwitchRoom(newRoomId: Long, newArgs: Bundle?) {
        super.onSwitchRoom(newRoomId, newArgs)

        originalStatus = 0

        isAddFirstWarning = false
        HandlerManager.removeCallbacks(firstWarningTask)
    }

    override fun onDestroy() {
        super.onDestroy()
        LiveViewPositionManager.getInstance().removeListener(this,
            LiveViewPositionType.TYPE_BOTTOM,
            LiveViewPositionType.TYPE_SUBSCRIBE_PANEL,
            LiveViewPositionType.TYPE_COURSE_AUTH_BOTTOM)
        HandlerManager.removeCallbacks(firstWarningTask)
    }

    override fun createItemDelegate(): CourseChatItemViewDelegate {
        return CourseChatItemViewDelegate(this)
    }

    override fun createMessageDispatcher(): IChatListMessageDispatcher {
        return CourseChatListMessageDispatcher(hostUid)
    }

    override fun getDataTypeConfig(): IChatListDataTypeConfig {
        return object : IChatListDataTypeConfig {

            private val default = listOf(MultiTypeListDataType.TYPE_DEFAULT)
            private val both = listOf(
                MultiTypeListDataType.TYPE_DEFAULT,
                MultiTypeListDataType.TYPE_COURSE_LECTURE
            )

            override fun getDataTypeConfig(): List<Int> {
                return both
            }

            override fun getDataType(msg: CommonChatMessage?): List<Int> {
                if (msg == null) return listOf()

                if (msg.mGroupType == 1 || (msg.mSender?.mIsAdmin == true || msg.mSender?.mIsHost == true)) {
                    return both
                }
                return default
            }
        }
    }

    override fun onOrientationChange(orientation: Int, isSameOrientation: Boolean) {
        super.onOrientationChange(orientation, isSameOrientation)
        updateChatListLayout()

        chatListContainer?.getListData()?.filter {
            it.mMsgType == CommonChatMessage.MESSAGE_TYPE_PIC
        }?.also { picMsgList ->
            if (picMsgList.isNotEmpty()) {
                picMsgList.forEach {
                    it.isScreenLand = isFull
                }
                chatListContainer?.notifyAreaADataSetChanged()
            }
        }
        scrollListToBottom(false)
    }

    override fun onCurrentUserInfoChange(userInfo: LiveUserInfo?) {
        if ((userInfo == null) || (userInfo.uid <= 0)) {
            updateRoleType(RoleType.AUDIENCE)
            return
        }

        when {
            userInfo.uid == hostUid -> updateRoleType(RoleType.HOST)
            userInfo.isOperatorIsAdmin -> updateRoleType(RoleType.ADMIN)
            else -> updateRoleType(RoleType.AUDIENCE)
        }
    }

    override fun onItemClick(msg: CommonChatMessage, view: View, position: Int) {
        if (invalidPlayAuth()) return

        chatListContainer?.getListData()?.getOrNull(position)?.also { targetMsg ->
            if (targetMsg.mMsgType == CommonChatMessage.MESSAGE_TYPE_PIC) {
                showBigImg(position)
            }
        }
    }

    override fun onITingMessageClick(iTingUrl: String, position: Int) {
        (BaseApplication.getMainActivity() as? MainActivity)?.also {
            LiveRouterUtil.startWebViewFragment(it, iTingUrl, false)
        }
    }

    override fun onMoreMsgDetailClick(msg: CommonChatMessage, position: Int, type: Int) {
        if (invalidPlayAuth()) return

        chatListContainer?.getListData()?.getOrNull(position)?.also { targetMsg ->
            if (targetMsg.itemViewType == ChatItemViewType.TYPE_COMMON_TOPIC) {
                getCourseInteraction()?.showAnnounce()
                trackCourseAnnounceItemClick()
            }
        }
    }

    override fun onItemLongClick(msg: CommonChatMessage, view: View, position: Int): Boolean {
        if (invalidPlayAuth()) return false

        chatListContainer?.getListData()?.getOrNull(position)?.also { targetMsg ->
            getCourseInteraction()?.sendAtMsg(targetMsg.senderName, targetMsg.senderUid)
        }
        return true
    }

    override fun onItemSendFailClick(msg: CommonChatMessage, view: View, position: Int) {
        handleResendMsg(position)
    }

    override fun onJoinFansClubClick(msg: CommonChatMessage, position: Int) {
        // 点击「我也加入」唤起粉丝团页面
        getCourseInteraction()?.showFansClubDialogFragment()
        trackLiveJoinClubViewClick()
    }

    override fun onFollowHostClick() {
        hostInteraction?.followCurrentAnchorOrPresident(
            AnchorFollowManage.FOLLOW_BIZ_TYPE_LIVE_ROOM_FOLLOW_MESSAGE_GUIDE_VIEW
        )
        trackLiveFollowViewClick()
    }

    override fun onShareRoomClick() {
        getCourseInteraction()?.shareRoom()
        trackLiveShareViewClick()
    }

    override fun onQueryHistoryMsg() {
        val msgId = chatListContainer?.getListData()?.getOrNull(0)?.mMsgId ?: 0
        val pageCount = getQueryHistoryMsgPageCount()

        if (isLecture) {
            getCourseInteraction()?.queryHistoryMessage(
                0, 0, msgId, 1, pageCount, false
            )
        } else if (hasHistoryMsg && (hostData?.status == 1 || isLecture)) {
            getCourseInteraction()?.queryHistoryMessage(
                hostData?.liveStartAt ?: 0, hostData?.liveStopAt ?: 0, msgId, 0, pageCount, true
            )
        }
    }

    override fun onCacheMessageReceived(msgList: List<CommonChatMessage>?) {
        if (!isAddFirstWarning && hostData != null) {
            val uid = hostUid
            val nickname = hostData.anchorName
            HandlerManager.removeCallbacks(firstWarningTask)
            produceFirstMessage(activity, uid, nickname)
            sendAnnounceTopicMsg()
            isAddFirstWarning = true
        }

        super.onCacheMessageReceived(msgList)
    }

    override fun msgAddFilter(msg: CommonChatMessage?): Boolean {
        if (msg == null) return false
        return msg.itemViewType in courseItemViewType
    }

    override fun switchLecture(isLecture: Boolean) {
        this.isLecture = isLecture
        chatListContainer?.setAreaADataType(if (isLecture) {
            MultiTypeListDataType.TYPE_COURSE_LECTURE
        } else {
            MultiTypeListDataType.TYPE_DEFAULT
        })
        chatListContainer?.showOrHideAreaB(!isLecture)
        CustomToast.showToast(if (isLecture) "当前仅显示主播和管理员评论" else "当前显示全员评论")
    }

    override fun isLecture(): Boolean {
        return isLecture
    }

    override fun setChatListVisible(visible: Boolean) {
        chatListContainer?.showOrGone(visible)
    }

    override fun isHideChat(): Boolean {
        return chatListContainer?.isVisible() != true
    }

    override fun updateVideoSize(width: Int, height: Int) {
        this.videoWidth = width
        this.videoHeight = height
    }

    override fun changeClearScreenStatus(isClear: Boolean) {
        if (hostData == null || !canUpdateUi()) return
        chatListContainer?.showOrGone(!isClear)
    }

    override fun updateUIMode(@UIMode uiMode: Int) {
        this.uiMode = uiMode
        when (uiMode) {
            UIMode.UI_LAND -> {
                updateLandLayout()
                updateChatLayoutRightMargin()
            }
            UIMode.UI_PORT -> {
                updatePortLayout()
                updateChatLayoutRightMargin()
            }
            UIMode.UI_FULL -> updateFullLayout()
            else -> {}
        }
    }

    override fun updateChatLayoutRightMargin() {
        if (!canUpdateUi() || isFull) return

        var needNotify = false
        chatListContainer?.updateLayoutParams<RelativeLayout.LayoutParams> {
            val targetMargin = (if (getCourseInteraction()?.isMicViewShow() == true) {
                108
            } else if (getCourseInteraction()?.isGoodsOperationViewShow() == true) {
                108
            } else {
                70
            }).dp
            needNotify = targetMargin < rightMargin
            rightMargin = targetMargin
        }
        // 宽度由小变大时需要触发重新布局，否则会出现 Item 宽度还是小宽度的问题
        if (needNotify) chatListContainer?.notifyAreaADataSetChanged()
    }

    override fun updateChatListLayout() {
        updateUIMode(uiMode)
    }

    override fun updateJoinFansClubMsgStatus(isJoin: Boolean) {
        updateTargetMsg(ChatItemViewType.TYPE_COMMON_JOIN_FANS_CLUB, true) {
            isCustomActionConsumed = isJoin
            true
        }
    }

    override fun updateFollowHostStatus(isFollowed: Boolean) {
        updateTargetMsg(ChatItemViewType.TYPE_COMMON_FOLLOW_HOST, true) {
            isCustomActionConsumed = isFollowed
            true
        }
    }

    override fun onLiveStatusPanelVisibilityChanged(visible: Boolean) {
        this.isStatusPanelVisible = visible
        updateUIMode(this.uiMode)
    }

    override fun trySeeEndPanelVisibilityChanged(visible: Boolean) {
        this.isTrySeeEndPanelVisible = visible
        updateUIMode(this.uiMode)
    }

    /**
     * 根据主播信息产生进入直播间的首条提醒消息
     *
     * @param context  上下文
     * @param hostUid  主播uid
     * @param hostNick 主播昵称
     * @return 消息本体
     */
    private fun produceFirstMessage(
        context: Context?, hostUid: Long, hostNick: String,
    ) {
        val cxt: Context = (context ?: MainApplication.getMyApplicationContext()) ?: return
        if (cxt.resources == null) return

        val firstMsg = CommonChatMessage()
        firstMsg.setItemViewType(ChatItemViewType.TYPE_COMMON_SYSTEM_RULE_HINT)

        val msgContent: String
        val replace: String
        if (hostUid == UserInfoMannage.getUid()) {
            msgContent =
                cxt.resources.getString(com.ximalaya.ting.android.live.host.R.string.live_chatroom_first_notice_person_host)
            replace = msgContent
        } else {
            msgContent =
                cxt.resources.getString(com.ximalaya.ting.android.live.host.R.string.live_chatroom_first_notice_person)
            replace = if (!TextUtils.isEmpty(hostNick)) {
                msgContent.replace(msgContent.substring(4, 8), hostNick)
            } else {
                msgContent.replace(msgContent.substring(4, 8), "欢乐")
            }
        }

        firstMsg.mMsgContent = replace
        firstMsg.mColor = ChatListViewConstant.COLOR_NORMAL_CONTENT
        onMessageReceived(firstMsg)
    }

    /**
     * 消息流展示公告话题消息
     */
    private fun sendAnnounceTopicMsg() {
        if (hostData?.description.isNullOrBlank() && hostData?.topic.isNullOrBlank()) return

        CustomPublishTopicMessage().apply {
            title = "公告："
            txt = hostData?.description ?: ""
            topicName = hostData?.topic ?: ""
            tagId = hostData?.topicId ?: 0
        }.let {
            CourseChatListMsgProducer.produceMessage(ChatItemViewType.TYPE_COMMON_TOPIC, it)
        }?.also {
            onMessageReceived(it)
        }

        AnnounceDataCache.put(hostUid, roomId, AnnounceData().also {
            it.content = hostData.description
            it.topicId = hostData?.topicId
            it.topicTitle = hostData?.topic
        })
    }

    /**
     * 是否没有观看权益
     */
    private fun invalidPlayAuth(): Boolean {
        val havePlayAuth = getCourseInteraction()?.isHavePlayAuth() ?: false
        if (havePlayAuth.not()) {
            CustomToast.showFailToast(R.string.live_video_click_no_auth)
        }
        return havePlayAuth.not()
    }

    private fun updatePortLayout() {
        if (chatListContainer == null) {
            return
        }
        if (portLayoutParams == null) {
            portLayoutParams = RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            portLayoutParams?.rightMargin = 70.dp
            // 竖屏视频时，高度固定为手机屏幕高度 27%，但不能直接设置固定高度，需要转换为 MarginTop
            val bottomHeight = 60.dp
            val excludeChatListHeight = (BaseUtil.getScreenHeight(context) * 0.73F).toInt()
            portLayoutParams?.topMargin =
                excludeChatListHeight - bottomHeight + BaseUtil.getStatusBarHeight(context)
        }

        portLayoutParams?.also {
            updateLayoutAbove(it)
            chatListContainer?.layoutParams = it
        }
    }

    private fun updateLandLayout() {
        if (chatListContainer == null) {
            return
        }
        if (landLayoutParams == null) {
            landLayoutParams = RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            landLayoutParams?.rightMargin = 70.dp
        }

        if (context != null && context.resources != null && videoWidth > 0) {
            val subscribeInfo = LiveViewPositionManager.getInstance().getInfoForType(LiveViewPositionType.TYPE_SUBSCRIBE_PANEL)
            if(subscribeInfo != null && subscribeInfo.isVisible){
                var panelHeight = 250.dp
                try {
                    panelHeight = subscribeInfo.height
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                val panelMarginTop = 130.dp
                landLayoutParams?.topMargin =
                    (panelHeight + panelMarginTop + BaseUtil.getStatusBarHeight(context))
                return
            }

            if (isStatusPanelVisible || isTrySeeEndPanelVisible) {
                var panelHeight = 250.dp
                try {
                    panelHeight = context.resources.getDimension(
                        R.dimen.livevideo_course_room_status_panel_height
                    ).toInt()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                var panelMarginTop = 130.dp
                try {
                    panelMarginTop = context.resources.getDimension(
                        if (isTrySeeEndPanelVisible) R.dimen.livevideo_course_try_see_end_panel_margin_top else R.dimen.livevideo_course_room_status_panel_margin_top
                    ).toInt()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                landLayoutParams?.topMargin =
                    (panelHeight + panelMarginTop + 20.dp + BaseUtil.getStatusBarHeight(context))
            } else {
                val width = if (isFull)
                    LiveViewUtil.getScreenHeight()
                else
                    LiveViewUtil.getScreenWidth()

                // 视频播放器的 MarginTop 包含了状态栏高度，对于聊天组件来说需要减去，同时需要 20dp 的间距
                var videoPlayerMarginTop = 173.dp
                try {
                    videoPlayerMarginTop = context.resources.getDimension(
                        R.dimen.live_video_video_margin_top
                    ).toInt()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                val videoPlayerHeight = width * videoHeight / videoWidth

                landLayoutParams?.topMargin =
                    (videoPlayerHeight + videoPlayerMarginTop + 20.dp)
            }
        }

        landLayoutParams?.also {
            updateLayoutAbove(it)
            chatListContainer?.layoutParams = it
        }
    }

    private fun updateLayoutAbove(landLayoutParams: RelativeLayout.LayoutParams) {
        landLayoutParams.addRule(RelativeLayout.ABOVE, R.id.livecomm_chat_list_guide)
        landLayoutParams.bottomMargin = 2.dp
    }

    private fun updateFullLayout() {
        if (chatListContainer == null) {
            return
        }
        if (fullLayoutParams == null) {
            val marginTop = 16.dp
            val marginEnd = 6.dp
            val marginBottom = (LiveViewPositionManager.getInstance()
                .getInfoForType(LiveViewPositionType.TYPE_BOTTOM)?.height?.minus(12.dp)
                ?: marginTop)
            // 全屏时右侧预留出来的宽度
            val rightAreaWidth = LiveCourseUtils.getFullscreenRightPanelWidth()

            fullLayoutParams = RelativeLayout.LayoutParams(
                rightAreaWidth - marginEnd,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            fullLayoutParams?.addRule(RelativeLayout.ALIGN_PARENT_RIGHT)
            fullLayoutParams?.setMargins(0, marginTop, marginEnd, marginBottom)
        }
        chatListContainer?.layoutParams = fullLayoutParams
    }

    private fun getCourseInteraction(): ICourseChatListComponentInteraction? {
        return getComponentInteractionSafety(ICourseChatListComponentInteraction::class.java)
    }

    override fun onLayoutChange(info: LiveViewPositionInfo?) {

        val authCheckVisible = LiveViewPositionManager.getInstance()
            .getInfoForType(LiveViewPositionType.TYPE_COURSE_AUTH_BOTTOM)?.isVisible

        findViewById<View>(
            R.id.livecomm_chat_list_guide
        )?.updateLayoutParams<RelativeLayout.LayoutParams> {
            bottomMargin = if (authCheckVisible == true) {
                addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
                12.dp + (LiveViewPositionManager.getInstance()
                    .getInfoForType(LiveViewPositionType.TYPE_COURSE_AUTH_BOTTOM)?.height ?: 0)
            } else {
                addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
                0.dp + (LiveViewPositionManager.getInstance()
                    .getInfoForType(LiveViewPositionType.TYPE_BOTTOM)?.height ?: 0)
            }
        }
        if(info?.type == LiveViewPositionType.TYPE_SUBSCRIBE_PANEL){
            updateUIMode(uiMode)
        }
    }

}