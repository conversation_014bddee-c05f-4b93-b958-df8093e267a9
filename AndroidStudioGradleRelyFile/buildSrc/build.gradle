apply plugin: 'groovy'

project.afterEvaluate {
    println("buildSrc 在执行--------------")
    copyFile(new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/config_plugin.gradle"), new File(project.projectDir.absolutePath + "/plugins.gradle"))
    apply from: rootProject.rootDir.absolutePath + '/plugins.gradle'
    // 生成维护文件：
    //配置文件拷贝
    File projectConfigFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/sourceDepProjects.gradle")
    File projectDestFile = new File(project.projectDir.getParent() + "/sourceDepProjects.gradle")
    if (projectConfigFile.exists() && (!projectDestFile.exists() || projectConfigFile.lastModified() > projectDestFile.lastModified())) {
        projectDestFile.createNewFile()
        copyFile(projectConfigFile, projectDestFile)
    }

    File configFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/configNewFramework.sh")
    if (configFile.exists()) {
        File destFile = new File(project.projectDir.getParent() + "/configNewFramework.sh")
        if (!destFile.exists() || configFile.lastModified() > destFile.lastModified()) {
            if (!destFile.exists()) {
                destFile.createNewFile()
            }
            copyFile(configFile, destFile)
        }
    }
    // 执行配置
    if (project.ext.find("initDep") == null || !project.ext.get("initDep").toBoolean()) {
        project.ext.set("initDep", true)
        println("执行配置-----------")
        def pth = project.projectDir.getParent() + "/configNewFramework.sh " + project.projectDir.getParent()
        def p = "chmod +x " + pth
        p.execute()
        println("配置路径:" + pth)
        def process = ['sh', '-c', "${pth}"].execute()
        process.waitFor()
        println("配置结束。。。。。。。。。。")
    }


    //build.gradle
    //服务端有更新以服务端为准（ AndroidStudioGradleRelyFile文件夹下） 否则以本地配置（root目录下）为准
    File srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/build.gradle")
    File destFile = new File(project.projectDir.getParent() + "/build.gradle")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/config.gradle")
    destFile = new File(project.projectDir.getParent() + "/config.gradle")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/config_plugin.gradle")
    destFile = new File(project.projectDir.getParent() + "/config_plugin.gradle")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

//    srcFile = new File(project.rootProject.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/jenkinsBuildBundle.properties")
//    destFile = new File(project.projectDir.getParent() + "/jenkinsBuildBundle.properties")
//    if (srcFile.lastModified() > destFile.lastModified()) {
//        copyFile(srcFile, destFile)
//        destFile.setLastModified(srcFile.lastModified())
//    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/gradle.properties")
    destFile = new File(project.projectDir.getParent() + "/gradle.properties")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/make_maven.sh")
    destFile = new File(project.projectDir.getParent() + "/make_maven.sh")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

//    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/make_plugin_apk.bat")
//    destFile = new File(project.projectDir.getParent() + "/make_plugin_apk.bat")
//    if (srcFile.lastModified() > destFile.lastModified()) {
//        copyFile(srcFile, destFile)
//        destFile.setLastModified(srcFile.lastModified())
//    }

//    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/make_plugin_apk.sh")
//    destFile = new File(project.projectDir.getParent() + "/make_plugin_apk.sh")
//    if (srcFile.lastModified() > destFile.lastModified()) {
//        copyFile(srcFile, destFile)
//        destFile.setLastModified(srcFile.lastModified())
//    }

//    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/make_plugin_apk_noproguard.sh")
//    destFile = new File(project.projectDir.getParent() + "/make_plugin_apk_noproguard.sh")
//    if (srcFile.lastModified() > destFile.lastModified()) {
//        copyFile(srcFile, destFile)
//        destFile.setLastModified(srcFile.lastModified())
//    }

//    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/make_plugin_apk_not_buildin.sh")
//    destFile = new File(project.projectDir.getParent() + "/make_plugin_apk_not_buildin.sh")
//    if (srcFile.lastModified() > destFile.lastModified()) {
//        copyFile(srcFile, destFile)
//        destFile.setLastModified(srcFile.lastModified())
//    }

//    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/pull_all_bundle.sh")
//    destFile = new File(project.projectDir.getParent() + "/pull_all_bundle.sh")
//    if (srcFile.lastModified() > destFile.lastModified()) {
//        copyFile(srcFile, destFile)
//        destFile.setLastModified(srcFile.lastModified())
//    }

//    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/refresh_dependency.bat")
//    destFile = new File(project.projectDir.getParent() + "/refresh_dependency.bat")
//    if (srcFile.lastModified() > destFile.lastModified()) {
//        copyFile(srcFile, destFile)
//        destFile.setLastModified(srcFile.lastModified())
//    }

//    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/refresh_dependency.sh")
//    destFile = new File(project.projectDir.getParent() + "/refresh_dependency.sh")
//    if (srcFile.lastModified() > destFile.lastModified()) {
//        copyFile(srcFile, destFile)
//        destFile.setLastModified(srcFile.lastModified())
//    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/settings.gradle")
    destFile = new File(project.projectDir.getParent() + "/settings.gradle")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }


    def safeProductionDir = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/safeProduction/")
    def destSafeProduction = new File(project.projectDir.getParent() + "/safeProduction/")
    srcFile = new File(safeProductionDir, "hook-version.txt")
    destFile = new File(destSafeProduction, "hook-version.txt")

    println("safeProduction >>> " + srcFile.exists() + ", " + destFile.exists())
    if (!destFile.exists() || srcFile.lastModified() > destFile.lastModified()) {
        safeProductionDir.listFiles(new FileFilter() {
            @Override
            boolean accept(File file) {
                def dst = new File(destSafeProduction, file.name)
                copyFile(file, dst)
                return false
            }
        })

        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/copy_git_hook.gradle")
    destFile = new File(project.projectDir.getParent() + "/copy_git_hook.gradle")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/util.gradle")
    destFile = new File(project.projectDir.getParent() + "/util.gradle")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

//    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/bundle_util.gradle")
//    destFile = new File(project.projectDir.getParent() + "/bundle_util.gradle")
//    if (srcFile.lastModified() > destFile.lastModified()) {
//        copyFile(srcFile, destFile)
//        destFile.setLastModified(srcFile.lastModified())
//    }

//    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/set_bundle_version.py")
//    destFile = new File(project.projectDir.getParent() + "/set_bundle_version.py")
//    if (srcFile.lastModified() > destFile.lastModified()) {
//        copyFile(srcFile, destFile)
//        destFile.setLastModified(srcFile.lastModified())
//    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/make_host.sh")
    destFile = new File(project.projectDir.getParent() + "/make_host.sh")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/make_bundle.sh")
    destFile = new File(project.projectDir.getParent() + "/make_bundle.sh")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/remote_make_bundle.sh")
    destFile = new File(project.projectDir.getParent() + "/remote_make_bundle.sh")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/set_tag.sh")
    destFile = new File(project.projectDir.getParent() + "/set_tag.sh")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }


    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/config_third_party.gradle")
    destFile = new File(project.projectDir.getParent() + "/config_third_party.gradle")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/mainframer.sh")
    destFile = new File(project.projectDir.getParent() + "/mainframer.sh")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/remote_run.sh")
    destFile = new File(project.projectDir.getParent() + "/remote_run.sh")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/.mainframer/ignore")
    destFile = new File(project.projectDir.getParent() + "/.mainframer/ignore")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/.mainframer/remoteignore")
    destFile = new File(project.projectDir.getParent() + "/.mainframer/remoteignore")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/checkout_bundles.sh")
    destFile = new File(project.projectDir.getParent() + "/checkout_bundles.sh")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    srcFile = new File(project.projectDir.getParent() + "/XAndroidFramework/AndroidStudioGradleRelyFile/py_bundle_map.py")
    destFile = new File(project.projectDir.getParent() + "/py_bundle_map.py")
    if (srcFile.lastModified() > destFile.lastModified()) {
        copyFile(srcFile, destFile)
        destFile.setLastModified(srcFile.lastModified())
    }

    //// 生成一些通用脚本
    // check_out_all_bundle.sh check_out_all_to_new_branch.sh pull_all_bundle.sh make_bundle.sh merge_one_branch_to_another.sh

    // check_out_all_bundle.sh check_out_all_to_new_branch.sh
    String label = "#include_bundles="
    def nowPlugins = plugin_paths.keySet()
    def bundleStr = ""
    for (String str : nowPlugins) {
        bundleStr = bundleStr + str + ","
    }
    boolean needReCreate = false
    String oldStr = null

    ///////////////check_out_all_bundle.sh//////////////////////////
//    File checkoutAllBundle = new File(project.rootDir.getParent(), "check_out_all_bundle.sh")
//    // 找到plugin 标记的行
//    String oldStr = null
//    boolean needReCreate = false
//    if (!checkoutAllBundle.exists()) {
//        needReCreate = true
//    } else {
//        for (String str : checkoutAllBundle.readLines()) {
//            if (str.startsWith(label)) {
//                oldStr = str.substring(label.length())
//                break
//            }
//        }
//        if (oldStr == null) {
//            needReCreate = true
//        } else {
//            String[] olds = oldStr.split(",")
//            if (olds.length != nowPlugins.size()) {
//                needReCreate = true;
//            } else {
//                needReCreate = !checkEq(Arrays.asList(olds), nowPlugins)
//            }
//        }
//    }
//
//
//    println("check_out_all_bundle-----" + needReCreate)
//
//    if (needReCreate) {
//        checkoutAllBundle.delete()
//        checkoutAllBundle.createNewFile()
//        StringBuilder builder = new StringBuilder("#!/usr/bin/env bash\n:#!/bin/bash\n")
//        builder.append("#以下内容为自动生成，请勿轻易修改\n")
//        builder.append(label).append(bundleStr).append("\n")
//        builder.append("#Canary  Developer master").append("\n")
//                .append("branch=\n\n")
//                .append("if [[ \"\$#\" -lt 1 ]]; then\n" +
//                        "    echo '你可以手动输入一个参数作为需要切换的分支'\n" +
//                        "else\n" +
//                        "    branch=\"\$1\"\n" +
//                        "fi\n\n")
//                .append("echo \$branch\n\n")
//
//                .append(createCheckoutScript("XAndroidFramework", false))
//                .append(createCheckoutScript("TingMainHost", false))
//
//        plugin_paths.each {
//            builder.append(createCheckoutScript(it.value.bundle, false))
//        }
//
//        checkoutAllBundle.write(builder.toString())
//        "chmod +x ${checkoutAllBundle.absolutePath}".execute()
//        builder.delete(0, builder.length())
//    }
    /////////////////check_out_all_to_new_branch.sh//////////////////////////
//    needReCreate = false
//    File checkoutAllBundleNew = new File(project.rootDir.getParent(), "check_out_all_to_new_branch.sh")
//    // 找到plugin 标记的行
//    oldStr = null
//    if (!checkoutAllBundleNew.exists()) {
//        needReCreate = true
//    } else {
//        for (String str : checkoutAllBundleNew.readLines()) {
//            if (str.startsWith(label)) {
//                oldStr = str.substring(label.length())
//                break
//            }
//        }
//        if (oldStr == null) {
//            needReCreate = true
//        } else {
//            String[] olds = oldStr.split(",")
//            if (olds.length != nowPlugins.size()) {
//                needReCreate = true;
//            } else {
//                needReCreate = !checkEq(Arrays.asList(olds), nowPlugins)
//            }
//        }
//    }
//
//    println("check_out_all_bundle_to_new-----" + needReCreate)
//
//    if (needReCreate) {
//        checkoutAllBundleNew.delete()
//        checkoutAllBundleNew.createNewFile()
//        StringBuilder builder = new StringBuilder("#!/usr/bin/env bash\n:#!/bin/bash\n")
//        builder.append(label).append(bundleStr).append("\n")
//        builder.append("#以下内容为自动生成，请勿轻易修改\n")
//        builder.append("#Canary  Developer master").append("\n")
//                .append("branch=\n\n").append("echo \$branch\n\n")
//                .append(createCheckoutScript("XAndroidFramework", true))
//                .append(createCheckoutScript("TingMainHost", true))
//
//        plugin_paths.each {
//            builder.append(createCheckoutScript(it.value.bundle, true))
//        }
//
//        checkoutAllBundleNew.write(builder.toString())
//        "chmod +x ${checkoutAllBundleNew.absolutePath}".execute()
//        builder.delete(0, builder.length())
//    }

    //////////////////make_bundle.sh/////////////////////////
    needReCreate = false
    File makeBundle = new File(project.rootDir.getParent(), "make_bundle.sh")
    // 找到plugin 标记的行
    oldStr = null
    if (!makeBundle.exists()) {
        needReCreate = true
    } else {
        for (String str : makeBundle.readLines()) {
            if (str.startsWith(label)) {
                oldStr = str.substring(label.length())
                break
            }
        }
        if (oldStr == null) {
            needReCreate = true
        } else {
            String[] olds = oldStr.split(",")
            if (olds.length != nowPlugins.size()) {
                needReCreate = true;
            } else {
                needReCreate = !checkEq(Arrays.asList(olds), nowPlugins)
            }
        }
    }


    println("make_bundle-----" + needReCreate)

    if (needReCreate) {
        makeBundle.delete()
        makeBundle.createNewFile()
        StringBuilder builder = new StringBuilder("#!/bin/bash\nset -e\n")
        builder.append("#以下内容为自动生成，请勿轻易修改\n")
        builder.append(label).append(bundleStr).append("\n")
        builder.append("if [ \"\$#\" -lt 1 ]; then").append("\n")
                .append("   echo \"需要指定编译的bundleName 例如: main live car 等， 可以一次传递多个\"\n")
                .append("   exit\n")
                .append("fi\n")
                .append("make_bundle_by_name() {\n")
                .append("   name=\"\$1\"\n")

        plugin_paths.each {
            builder.append(createMakeBundleScript(it.key, it.value.bundle, it.value.app))
        }

        builder.append("}\n")
                .append("# 打bundle 包先停止当前的打包进程，重启进程来打\n" +
                        "./gradlew --stop\n")
                .append("for i in \"\$@\"; do\n" +
                        "    make_bundle_by_name \$i\n" +
                        "done\n")
                .append("adb shell am force-stop com.ximalaya.ting.android\n" +
                        "adb shell am start --es \"run_from\" \"make_bundle\" -n com.ximalaya.ting.android/com.ximalaya.ting.android.host.activity.WelComeActivity")
        makeBundle.write(builder.toString())
        "chmod +x ${makeBundle.absolutePath}".execute()
        builder.delete(0, builder.length())

    }

    //////////////////remote_make_bundle.sh/////////////////////////
    needReCreate = false
    File remoteMakeBundle = new File(project.rootDir.getParent(), "remote_make_bundle.sh")
    // 找到plugin 标记的行
    oldStr = null
    if (!remoteMakeBundle.exists()) {
        needReCreate = true
    } else {
        for (String str : remoteMakeBundle.readLines()) {
            if (str.startsWith(label)) {
                oldStr = str.substring(label.length())
                break
            }
        }
        if (oldStr == null) {
            needReCreate = true
        } else {
            String[] olds = oldStr.split(",")
            if (olds.length != nowPlugins.size()) {
                needReCreate = true;
            } else {
                needReCreate = !checkEq(Arrays.asList(olds), nowPlugins)
            }
        }
    }

    println("remote_make_bundle-----" + needReCreate)

    if (needReCreate) {
        remoteMakeBundle.delete()
        remoteMakeBundle.createNewFile()
        StringBuilder builder = new StringBuilder("#!/bin/bash\nset -e\n")
        builder.append("#以下内容为自动生成，请勿轻易修改\n")
        builder.append(label).append(bundleStr).append("\n")
        builder.append("if [ \"\$#\" -lt 1 ]; then").append("\n")
                .append("   echo \"需要指定编译的bundleName 例如: main live car 等， 可以一次传递多个\"\n")
                .append("   exit\n")
                .append("fi\n")
                .append("make_bundle_by_name() {\n")
                .append("   name=\"\$1\"\n")

        plugin_paths.each {
            builder.append(createRemoteMakeBundleScript(it.key, it.value.bundle, it.value.app))
        }

        builder.append("}\n")
                .append("# 打bundle 包先停止当前的打包进程，重启进程来打\n" +
                        "./gradlew --stop\n")
                .append("for i in \"\$@\"; do\n" +
                        "    make_bundle_by_name \$i\n" +
                        "done\n")
                .append("adb shell am force-stop com.ximalaya.ting.android\n" +
                        "adb shell am start --es \"run_from\" \"make_bundle\" -n com.ximalaya.ting.android/com.ximalaya.ting.android.host.activity.WelComeActivity")
        remoteMakeBundle.write(builder.toString())
        "chmod +x ${remoteMakeBundle.absolutePath}".execute()
        builder.delete(0, builder.length())
    }

    ///////////////////merge_one_branch_to_another.sh/////////////////////
//    needReCreate = false
//    File mergeOneToOther = new File(project.rootDir.getParent(), "merge_one_branch_to_another.sh")
//    // 找到plugin 标记的行
//    oldStr = null
//    if (!mergeOneToOther.exists()) {
//        needReCreate = true
//    } else {
//        for (String str : mergeOneToOther.readLines()) {
//            if (str.startsWith(label)) {
//                oldStr = str.substring(label.length())
//                break
//            }
//        }
//        if (oldStr == null) {
//            needReCreate = true
//        } else {
//            String[] olds = oldStr.split(",")
//            if (olds.length != nowPlugins.size()) {
//                needReCreate = true;
//            } else {
//                needReCreate = !checkEq(Arrays.asList(olds), nowPlugins)
//            }
//        }
//    }
//
//
//    println("merge_one_branch_to_another-----" + needReCreate)
//
//    if (needReCreate) {
//        mergeOneToOther.delete()
//        mergeOneToOther.createNewFile()
//        StringBuilder builder = new StringBuilder("#!/usr/bin/env bash\n:#!/bin/bash\n")
//        builder.append("#以下内容为自动生成，请勿轻易修改\n")
//        builder.append(label).append(bundleStr).append("\n")
//        builder.append("#Canary  Developer master").append("\n\n")
//                .append("fromBranch=\n")
//                .append("toBranch=\n\n")
//                .append("echo \"merge \$fromBranch into \$toBranch\"\n\n")
//                .append("checkOutBranch() {\n")
//                .append("    branch=\"\$1\"\n")
//                .append(justCheckoutScript("XAndroidFramework"))
//                .append(justCheckoutScript("TingMainHost"))
//
//        plugin_paths.each {
//            builder.append(justCheckoutScript(it.value.bundle))
//        }
//        builder.append("}\n\n")
//                .append("checkOutBranch \$fromBranch\n" +
//                        "checkOutBranch \$toBranch\n\n")
//                .append(mergeScript("XAndroidFramework"))
//                .append(mergeScript("TingMainHost"))
//        plugin_paths.each {
//            builder.append(mergeScript(it.value.bundle))
//        }
//
//        mergeOneToOther.write(builder.toString())
//        "chmod +x ${mergeOneToOther.absolutePath}".execute()
//        builder.delete(0, builder.length())
//    }

//    /////////////////pull_all_bundle.sh//////////////////////////
//    needReCreate = false
//    File pullAllBundle = new File(project.rootDir.getParent(), "pull_all_bundle.sh")
//    // 找到plugin 标记的行
//    oldStr = null
//    if (!pullAllBundle.exists()) {
//        needReCreate = true
//    } else {
//        for (String str : pullAllBundle.readLines()) {
//            if (str.startsWith(label)) {
//                oldStr = str.substring(label.length())
//                break
//            }
//        }
//        if (oldStr == null) {
//            needReCreate = true
//        } else {
//            String[] olds = oldStr.split(",")
//            if (olds.length != nowPlugins.size()) {
//                needReCreate = true;
//            } else {
//                needReCreate = !checkEq(Arrays.asList(olds), nowPlugins)
//            }
//        }
//    }
//
//
//    println("pull_all_bundle-----" + needReCreate)
//
//    if (needReCreate) {
//        if (pullAllBundle.exists()) {
//            pullAllBundle.delete()
//        }
//        pullAllBundle.createNewFile()
//        StringBuilder builder = new StringBuilder("#!/bin/bash\n" +
//                "set -e\n\n")
//        builder.append("#以下内容为自动生成，请勿轻易修改\n")
//        builder.append(label).append(bundleStr).append("\n")
//                .append("#以 XAndroidFramework的分支名为基准，切换将所有bundle的分支切换到和它一样的分支\n" +
//                        "#你可以输入一个分支名称，将所有bundle 切换到你输入的分支名\n\n")
//        builder.append("if [[ \"\$#\" -lt 1 ]]; then\n" +
//                "   cd XAndroidFramework\n" +
//                "   branch=\$(git branch | sed -n -e 's/^\\* \\(.*\\)/\\1/p')\n" +
//                "   cd ../\n" +
//                "else\n" +
//                "    branch=\"\$1\"\n" +
//                "fi\n").append("\n")
//                .append("echo \"checkout to branch \" \${branch}\n\n")
//                .append(createCheckoutScript("XAndroidFramework", false))
//                .append(createCheckoutScript("TingMainHost", false))
//
//        plugin_paths.each {
//            builder.append(createCheckoutScript(it.value.bundle, false))
//        }
//
//        pullAllBundle.write(builder.toString())
//        "chmod +x ${pullAllBundle.absolutePath}".execute()
//        builder.delete(0, builder.length())
//    }
    //////////////////////// set tag.sh ///////////////////////////
//    needReCreate = false
//    File setTag = new File(project.rootDir.getParent(), "set_tag.sh")
//    // 找到plugin 标记的行
//    oldStr = null
//    if (!setTag.exists()) {
//        needReCreate = true
//    } else {
//        for (String str : setTag.readLines()) {
//            if (str.startsWith(label)) {
//                oldStr = str.substring(label.length())
//                break
//            }
//        }
//        if (oldStr == null) {
//            needReCreate = true
//        } else {
//            String[] olds = oldStr.split(",")
//            if (olds.length != nowPlugins.size()) {
//                needReCreate = true;
//            } else {
//                needReCreate = !checkEq(Arrays.asList(olds), nowPlugins)
//            }
//        }
//    }
//
//
//    println("set_tag-----" + needReCreate)
//
//    if (needReCreate) {
//        if (setTag.exists()) {
//            setTag.delete()
//        }
//        setTag.createNewFile()
//        StringBuilder builder = new StringBuilder("#!/bin/bash\n" +
//                "set -e\n\n")
//        builder.append("#以下内容为自动生成，请勿轻易修改\n")
//        builder.append(label).append(bundleStr).append("\n")
//                .append("#设置所有bundle 的统一tag，请输入一个tag 名称\n\n\n")
//        builder.append("if [ \"\$#\" -lt 1 ]; then\n" +
//                "    echo \"需要传递一个tag name 作为参数\"\n" +
//                "    exit\n" +
//                "fi\n\n")
//                .append("tag_name=\"\$1\"\n\n")
//                .append("echo \"set all bundle tag \" \$tag_name\n\n")
//                .append("source check_out_all_bundle.sh master\n\n\n")
//                .append(createTagScript("XAndroidFramework"))
//                .append(createTagScript("TingMainHost"))
//
//        plugin_paths.each {
//            builder.append(createTagScript(it.value.bundle))
//        }
//
//        setTag.write(builder.toString())
//        "chmod +x ${setTag.absolutePath}".execute()
//        builder.delete(0, builder.length())
//    }

}

static void copyFile(File srcFile, File destFile) {
    destFile.getParentFile().mkdirs()
    def inputStream = srcFile.newInputStream()
    def outputStream = destFile.newOutputStream()
    outputStream << inputStream
    inputStream.close()
}

static boolean checkEq(List<String> old, Set<String> now) {
    for (String str : now) {
        if (!old.contains(str)) {
            return false
        }
    }
    return true
}

static String createCheckoutScript(String bundle, boolean ifNew) {
    StringBuilder builder = new StringBuilder()
    builder.append("if [ ! -d \"${bundle}\" ]; then").append("\n")
            .append("  ").append("<NAME_EMAIL>:android/${bundle}.git\n")
            .append("  ").append("cd ${bundle}\n")
            .append("  ").append("git checkout ${ifNew ? "-b" : ""} \$branch\n")
            .append("  ").append("cd ../\n")
            .append("else\n")
            .append("  cd ${bundle}\n")
            .append("  git fetch -vp\n")
            .append("  git checkout ${ifNew ? "-b" : ""} \$branch\n")
            .append("  git pull\n")
            .append("  cd ../\n")
            .append("fi\n")
    return builder.toString()
}

static String createMakeBundleScript(String bundleName, String bundle, String app) {
    StringBuilder stringBuilder = new StringBuilder()
    stringBuilder.append("   if [ \$name == \"${bundleName}\" ]\n")
            .append("   then\n")
            .append("      echo \"build ${bundleName} bundle\"\n")
            .append("      ./gradlew -PpIsProguard=false -PBUILD_PLUGIN_APK=true -PBUILD_PLUGIN_${bundleName.toUpperCase()}=true -PFORCE_COPY_SO=true :${bundle}:${app}:clean\n")
            .append("      ./gradlew -PpIsProguard=false -PBUILD_PLUGIN_APK=true -PBUILD_PLUGIN_${bundleName.toUpperCase()}=true -PFORCE_COPY_SO=true :${bundle}:${app}:assembleRelease\n")
            .append("      v8=\"TingMainHost/Application/libs/armeabi/v8_lib${bundleName}.so\"\n")
            .append("      v7=\"TingMainHost/Application/libs/armeabi/v7_lib${bundleName}.so\"\n")
            .append("      if [ -f \"\$v8\" ]; then\n" +
                    "         adb push TingMainHost/Application/libs/armeabi/v8_lib${bundleName}.so /sdcard/Android/data/com.ximalaya.ting.android/cache/lib/v8_lib${bundleName}.so\n" +
                    "      fi\n" +
                    "      if [ -f \"\$v7\" ]; then\n" +
                    "         adb push TingMainHost/Application/libs/armeabi/v7_lib${bundleName}.so /sdcard/Android/data/com.ximalaya.ting.android/cache/lib/v7_lib${bundleName}.so\n" +
                    "      fi\n")
            .append("   fi\n")
    return stringBuilder.toString()
}

static String createRemoteMakeBundleScript(String bundleName, String bundle, String app) {
    StringBuilder stringBuilder = new StringBuilder()
    stringBuilder.append("   if [ \$name == \"${bundleName}\" ]\n")
            .append("   then\n")
            .append("      echo \"build ${bundleName} bundle\"\n")
            .append("      ./mainframer.sh 'source /etc/profile && ./gradlew -PpIsProguard=false -PBUILD_PLUGIN_APK=true -PBUILD_PLUGIN_${bundleName.toUpperCase()}=true -PFORCE_COPY_SO=true :${bundle}:${app}:clean'\n")
            .append("      ./mainframer.sh 'source /etc/profile && ./gradlew -PpIsProguard=false -PBUILD_PLUGIN_APK=true -PBUILD_PLUGIN_${bundleName.toUpperCase()}=true -PFORCE_COPY_SO=true :${bundle}:${app}:assembleRelease'\n")
            .append("      v8=\"TingMainHost/Application/libs/armeabi/v8_lib${bundleName}.so\"\n")
            .append("      v7=\"TingMainHost/Application/libs/armeabi/v7_lib${bundleName}.so\"\n")
            .append("      if [ -f \"\$v8\" ]; then\n" +
                    "         adb push TingMainHost/Application/libs/armeabi/v8_lib${bundleName}.so /sdcard/Android/data/com.ximalaya.ting.android/cache/lib/v8_lib${bundleName}.so\n" +
                    "      fi\n" +
                    "      if [ -f \"\$v7\" ]; then\n" +
                    "         adb push TingMainHost/Application/libs/armeabi/v7_lib${bundleName}.so /sdcard/Android/data/com.ximalaya.ting.android/cache/lib/v7_lib${bundleName}.so\n" +
                    "      fi\n")
            .append("   fi\n")
    return stringBuilder.toString()
}

static String justCheckoutScript(String bundle) {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("    if [ ! -d \"${bundle}\" ]; then\n" +
            "      <NAME_EMAIL>:android/${bundle}.git\n" +
            "      cd ${bundle}\n" +
            "      git checkout \$branch\n" +
            "      cd ../\n" +
            "    else\n" +
            "       cd ${bundle}\n" +
            "       git fetch -vp\n" +
            "       git checkout \$branch\n" +
            "       git pull\n" +
            "       cd ../\n" +
            "    fi\n")

    return stringBuilder.toString()
}

static String mergeScript(String bundle) {
    return new StringBuilder("    cd ${bundle}\n" +
            "    git merge \$fromBranch\n" +
            "    cd ../\n\n").toString()
}

static String createTagScript(String bundle) {
    return new StringBuilder("cd ${bundle}\n" +
            "git tag \$tag_name\n" +
            "git push origin \$tag_name\n" +
            "cd ../\n\n").toString()
}

