## Project-wide Gradle settings.
#
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
#Wed Feb 24 13:46:52 CST 2016
#systemProp.http.proxyHost=*************
#systemProp.http.proxyPort=8118
org.gradle.daemon=true
#org.gradle.jvmargs=-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=y,address=5005
org.gradle.jvmargs=-Xmx8192m -XX:MaxPermSize=8192m -XX:MaxMetaspaceSize=8192m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.configureondemand=false
org.gradle.caching=true

kotlin.incremental=true
kotlin.incremental.java=true
kotlin.caching.enabled=true
kotlin.parallel.tasks.in.project=true
kotlin.incremental.useClasspathSnapshot=true
#kotlin.build.report.output=file

kapt.include.compile.classpath=false
kapt.incremental.apt=true
kapt.use.worker.api=true

# kotlin ??????, /build/reports/kotlin-build/
#kotlin.build.report.enable=true
#kotlin.build.report.verbose=true
#kotlin.build.report.output=file

android.enableBuildCache=true
## Disables R8 for Android Library modules only.
android.enableR8.libraries=false
## Disables R8 for all modules.
android.enableR8=false
android.enableR8.fullMode=false

android.useAndroidX=true
android.enableJetifier=true
android.jetifier.blacklist=.*-buildfast-release.*

#\u5305\u4FE1\uFFFD?
GROUP_ID=com.ximalaya.ting.android
# Licence\u4FE1\u606F
PROJ_LICENCE_NAME=The Apache Software License, Version 2.0
PROJ_LICENCE_URL=http://www.apache.org/licenses/LICENSE-2.0.txt
PROJ_LICENCE_DEST=repo
LOCAL_REPO_URL=http://************:8082/artifactory/host/
ARTIFACTORY_USER_NAME=
ARTIFACTORY_PASSWORD=
ANDROID_TOOLS_BUILD_GRADLE=4.1.3
KOTLIN_PLUGIN_VERSION=1.7.0
KOTLIN_COROUTINE_VERSION=1.6.4
KSP_VERSION=1.7.20-1.0.8
ksp.incremental=true
ksp.incremental.log=true
ksp.incremental.intermodule=true
android.enableResourceOptimizations=false
#org.gradle.unsafe.configuration-cache=true
#org.gradle.unsafe.configuration-cache-problems=warn
# \u4E3B\u5E94\u7528\u53CA\u6240\u6709\u5B50\u5E94\u7528\u5305\u542B\u7684abi\uFF0C\u4E2A\u4EBA\u672C\u5730\u5F00\u53D1\uFF0C\u53EA\u60F3\u8981\u5176\u4E2D\u4E00\u4E2A\uFF0C\u53EF\u5728local.properties\u4E2D\u914D\u7F6EABIS=xxx
ABIS=armeabi-v7a,arm64-v8a
##############
##\u662F\u5426\u6253\u5F00\u4E8C\u65B9\u5E93\u6E90\u7801\u4F9D\u8D56\u5F00\u5173\uFF0C\u60F3\u8981\u914D\u7F6E\u6E90\u7801\u4F9D\u8D56\uFF0C\u8BF7\u9605\u8BFB\uFF1A
# http://thoughts.ximalaya.com/workspaces/5cf254d1be825bee8c312071/docs/5e8732fdf6aaaf0001e84295
##\u914D\u7F6E\u4E00\u6B21\uFF0C\u4E00\u952E\u5207\u6362\u6E90\u7801\u4F9D\u8D56\u548Cmaven\u4F9D\u8D56
OPEN_NEW_FRAMEWORK_SOURCE=false
##############
CLEAN_ALL=false
#\u90E8\u7F72\u5728\u516C\u53F8artifactory\u4E0A\u7684\u5907\u7528\u4ED3\u5E93start
#LOCAL_REPO_URL=http://artifactory.ximalaya.com/artifactory/ximalaya-android-local/
#ARTIFACTORY_USER_NAME=android-developer
#ARTIFACTORY_PASSWORD=9876054321
#\u90E8\u7F72\u5728\u516C\u53F8artifactory\u4E0A\u7684\u5907\u7528\u4ED3\u5E93end
# \u6CE8\u610F\uFF1A-P\u53C2\u6570\u4F20\u9012\u8FC7\u6765\u7684property\u5728\u89E3\u6790settings.gradle\u4E4B\u524D\u751F\u6548\u3002 -P\u53C2\u6570\u8BBE\u7F6E\u7684property\u4F1A\u8986\u76D6
# gradle.properties\u4E2D\u7684\u540C\u540Dproperty,gradle\u811A\u672C\u4E2D\u901A\u8FC7ext\u5B9A\u4E49\u7684property\u4F1A\u8986\u76D6-P\u8BBE\u7F6E\u7684property
# maven\u670D\u52A1\u5668\u6302\u6389\u65F6\u5C06\u6B64\u53D8\u91CF\u6539\u4E3Afalse
USE_LOCAL_MAVEN_SERVER=true
REPO_VERSION=

REPO_BRANCH=canary
#\u7F16\u8BD1\u9884\u88C5\u5305
COMPILE_PRE_INSTALL=false
#\u5168\u91CF\u4EE3\u7801\u7F16\u8BD1
ALL_COMPILE_SRC=false
#\u7F16\u8BD1\u5355\u4E2Abundle
BUILD_PLUGIN_APK=false
#\u7EBF\u4E0A\u6253\u5305 host \u4EE5\u53CA\u9700\u8981buildIn\u7684bundle
BUILD_HOST_APK=false

####### \u672C\u5730\u5F00\u53D1\u63A7\u5236\u5F00\u5173  ############
#\u5728\u672C\u5730\u5F00\u53D1\u6253\u5305\uFF0C\u5BF9\u4E8E\u6BCF\u4E2Abundle\u7684\u4E0D\u540C\u7F16\u8BD1\u65B9\u5F0F\uFF0C\u5C06\u5BFC\u81F4\u8FD9\u4E2Abundle \u52A0\u8F7D\u7684\u65B9\u5F0F\u4E0D\u540C\u3002
#\u672C\u5730\u5F00\u53D1\u65F6\uFF0C\u5FC5\u987B\u5230local.properties \u6DFB\u52A0\u5F00\u5173\uFF1ADEV_BUNDLE_MODE=true
#\u4E00\u822C\u6765\u8BF4\uFF0Cbundle \u6709\u56DB\u79CD\u4E0D\u540C\u7684\u52A0\u8F7D\u65B9\u5F0F\u5206\u522B\u4E3A\uFF1A
# 1\u3001\u4EE3\u7801\u76F4\u63A5\u6253\u5165app \u4E2D\uFF0C\u4E3AbuildIn\u52A0\u8F7D\u65B9\u5F0F\uFF0C
# 2\u3001\u5355\u4E2Abundle \u72EC\u7ACB\u6253\u6210so\u6587\u4EF6\uFF0C\u53EF\u4EE5\u653E\u5230libs\uFF0Csd\u5361\u548Cnet\uFF0C\u4E5F\u5206\u522B\u5BF9\u5E94\u4E09\u603B\u4E0D\u540C\u7684\u52A0\u8F7D\u6A21\u5F0F
# \u4E0B\u9762\u662F\u6BCF\u4E00\u79CD\u52A0\u8F7D\u6A21\u5F0F\u914D\u7F6E\u65B9\u5F0F\uFF0C\u53EF\u901A\u8FC7\u4FEE\u6539\u8FD9\u4E2A\u53D8\u91CF\u6765\u6539\u53D8bundle \u88AB\u6253\u5165\u7684\u65B9\u5F0F\u548C\u52A0\u8F7D\u65B9\u5F0F
# \u591A\u4E2Abundle \u53EF\u4EE5\u9017\u53F7\u9694\u5F00\uFF0Cbundle\u540D\u79F0\u5343\u4E07\u522B\u586B\u9519\u4E86\u3002\u53E6\u5916\uFF0C\u4E3A\u4E86\u6253\u5355\u72EC\u7684bundle \u65F6\u80FD sync \u4EE3\u7801\uFF0C
# \u6DFB\u52A0\u4E86 SYNC_BUNDLE \u53D8\u91CF
# **** \u4E0B\u9762\u8FD9\u4E9B\u914D\u7F6E\uFF0C\u5EFA\u8BAE\u62F7\u8D1D\u4E00\u4EFD\u5230local.properties \u4E2D\uFF0C\u65B9\u4FBF\u81EA\u5DF1\u4FEE\u6539\u81EA\u5DF1\u7684\u6253\u5305\u63A7\u5236\u800C\u4E0D\u5F71\u54CD\u4ED6\u4EBA

#\u9700\u8981\u540C\u6B65\u4EE3\u7801\u7684bundle
SYNC_BUNDLE=main
#bundle \u4EE3\u7801\u76F4\u63A5\u6253\u5165apk,main \u9ED8\u8BA4\u662FbuildIn
BUILD_IN_BUNDLE=main
#bundle \u88AB\u6253\u6210so\u6587\u4EF6\u653E\u5230lib\u4E0B\u9762
BUILD_TO_LIB_BUNDLE=
#bundle \u88AB\u6253\u6210so \u6587\u4EF6\u653E\u5230sd \u5361\u4E2D
BUILD_TO_SD_BUNDLE=
#bundle \u88AB\u6253\u6210so \u6587\u4EF6\u653E\u5230net \uFF0C\u8FD9\u91CC\u9700\u8981\u624B\u52A8\u4E0A\u4F20so \u5230net \u6765\u6D4B\u8BD5
BUILD_TO_NET_BUNDLE=

####### \u672C\u5730\u5F00\u53D1\u63A7\u5236\u5F00\u5173 end ############

## \u4E0B\u9762\u662F\u6240\u6709\u7684 bundle \u540D\u79F0\u5E38\u91CF\uFF0C\u8BF7\u6B63\u786E\u586B\u5199\uFF0C\u8FD9\u4E2A\u5E38\u91CF\u5728\u591A\u4E2A\u5730\u65B9\u4F1A\u88AB\u7528\u5230
BUNLDE_MAIN=main
BUNDLE_LIVE=live
BUNDLE_CHAT=chat
BUNDLE_RECORD=record
BUNDLE_CAR=car
BUNDLE_SMART_DEVICE=smartdevice
BUNDLE_WATCH=watch
BUNDLE_SUPPORT_CHAT=supportchat
BUNDLE_WEIKE=weike
BUNDLE_VIDEO=video
BUNDLE_RN=reactnative
BUNDLE_ZONE=zone
BUNDLE_RADIO=radio
BUNDLE_ALI_AUTH=aliauth
BUNDLE_GAME_AD=gamead
BUNDLE_FEED=feed
BUNDLE_RN_UNION_PAY=rnunionpay
BUNDLE_SEA=sea
BUNDLE_SEARCH=search
BUNDLE_SOUND_NETWORK=soundnetwork
BUNDLE_MUSIC=music
BUNDLE_SHOOT=shoot
BUNDLE_CARTOON=cartoon
BUNDLE_LOGIN=login
BUNDLE_KIDS=kids
BUNDLE_ELDERLY=elderly
BUNDLE_READ=read
BUNDLE_SOUNDNETWORK=soundnetwork
BUNDLE_LITEAPP=liteapp
BUNDLE_MYLISTEN=mylisten
BUNDLE_SPEECH_RECOGNITION=speechrecognition
BUNDLE_VIP=vip
BUNDLE_LOAN=loan
BUNDLE_COMMERCIAL=commercial


### \u8FD9\u51E0\u4E2A\u914D\u7F6E  \u53EF\u4EE5\u79FB\u52A8\u5230\u81EA\u5DF1\u7684bundle \u4E0B\u9762\u53BB\uFF1F
LIVE_INCLUDE_ENT = true
LIVE_INCLUDE_COURSE = true
LIVE_INCLUDE = true

AD_INCLUDE_BAIDU = true

OPEN_BUGTAGS=false
MATRIX_VERSION=0.5.1
# true \u7F16\u8BD1\u6E90\u4EE3\u7801\uFF0Cfalse\u7F16\u8BD1aar
CMOPILE_NEW_ANDROID_FRAMEWORK_SRC=false
INCLUDE_NEW_ANDROID_FRAMEWORK_SRC=false
NEW_ANDROID_FRAMEWORK_REPO_VERSION=1.0.4
# \u662F\u5426\u4F9D\u8D56\u64AD\u653E\u5668\u6E90\u7801\u6253\u5305
INCLUDE_PLAYER_SOURCE_COMPILE=false

##----\u4EE5\u4E0B\u662F \u4E8C\u65B9\u5E93\u4F9D\u8D56\u4FE1\u606F\uFF0C\u6240\u4F9D\u8D56\u7684\u4E8C\u65B9\u5E93\u7248\u672C\u90FD\u5728\u8FD9\u91CC\u58F0\u660E\uFF0C\u4FBF\u4E8E\u67E5\u627E\u548C\u4FEE\u6539---##
## \u5728\u4E3Bapp\u4E2D\uFF0C\u4E8C\u65B9\u5E93\u4F9D\u8D56\u4F7F\u7528\u7EDF\u4E00\u7684\u540E\u7F00\uFF0C\u4FBF\u4E8E\u7BA1\u7406 ##
NEW_FRAMEWORK_REPO_BRANCH=developer
# \u52A0\u5BC6\u5E93
ENCRYPT_SERVICE_REPO_BRANCH=developer
ENCRYPT_SERVICE_REPO_VERSION=2.9.23
XMUTIL_REPO_VERSION=2.2.47

EMULATOR_REPO_VERSION=2.0.1
# mxlog manager \u7248\u672C
XMLOGMANAGER_REPO_BRANCH=developer
XMLOGMANAGER_REPO_VRSION=2.1.33

SHARE_REPO_VERSION=2.0.24
LOGIN_REPO_BRANCH=developer-wx
LOGIN_REPO_VERSION=1.1.5
#???
XMPUSHSERVICE_REPO_VRSION=4.0.46
#PagerSlidingTabStrip
PAGERSLIDINGTABSTRIP_REPO_VERSION=2.0.18
#PullToRefresh
PULLTOREFRESH_REPO_VERSION=2.0.18
# DNS cache \u5E93
DNS_CACHE_REPP_VERSION=2.1.3
DNS_CACHE_REPO_BRANCH=developer
#HTTP_CLIENT_VERSION=1.0.6
# \u914D\u7F6E\u4E2D\u5FC3
CONFIGURE_CENTER_VERSION=3.2.6
#\u4F4D\u7F6E\u5B9A\u4F4D\u670D\u52A1
LOCATION_REPO_BRANCH=developer
LOCATION_SERVICE_VERSION=2.0.6
#\u5F39\u5C4F
FIREWORK_REPO_BRANCH=developer
FIREWORK_VERSION=2.3.8
#\u64AD\u653E\u5E93X
XMEDIA_REPO_BRANCH=developer
XMEDIA_REPO_VERSION=2.4.339
#Hybrid_View
HYBRID_VIEW_REPO_BRANCH=developer
HYBRID_VIEW_REPO_VRSION=3.9.31

#PacketCapture VPNSERVICE
PACKET_CAPTURE_REPO_BRANCH=developer
PACKET_CAPTURE_REPO_VRSION=2.0.0

#ALPHAMOVIE
ALPHAMOVIE_REPO_BRANCH=developer
ALPHAMOVIE_REPO_VRSION=2.0.10

HTTP_CLIENT_VERSION=1.1.14

# XmRecorder
XM_RECORDER_REPO_BRANCH=developer
XM_RECORDER_REPO_VERSION=3.0.44

# XmVideo \u89C6\u9891\u57FA\u7840\u5E93
XM_VIDEO_REPO_BRANCH=developer
XM_VIDEO_REPO_VERSION=1.0.21

# XmMMKV \u5E93
XmMMKV_REPO_BRANCE=developer
XmMMKV_REPO_VERSION=2.0.13

# \u56FE\u7247\u88C1\u526A
UCROP_REPO_BRANCH=developer
UCROP_REPO_VRSION=1.0.7

# DanmaKu
DANMAKU_REPO_BRANCH=developer
DANMAKU_REPO_VRSION=1.0.6

# LauncherBadge
LAUNCHER_BADGE_REPO_BRANCH=developer
LAUNCHER_BADGE_REPO_VRSION=1.0.1

##-------------- \u53EF\u89C6\u5316\u57CB\u70B9\u5E93 ------------##
## \u53EF\u89C6\u5316\u57CB\u70B9
XMTRACE_REPO_BRANCH=developer
XMTRACE_REPO_VERSION=2.2.63


## \u53EF\u89C6\u5316\u57CB\u70B9 \u6253\u70B9
XM_POINT_TRACE_REPO_BRANCH=developer
XM_POINT_TRACE_REPO_VERSION=2.0.41

## \u53EF\u89C6\u5316\u57CB\u70B9 \u6253\u70B9No op
XM_POINT_TRACE_NO_OP_REPO_BRANCH=developer
XM_POINT_TRACE_NO_OP_REPO_VERSION=1.0.1

#--- \u98CE\u63A7\u53C2\u6570 ----#
XM_RISK_COLLECTOR_REPO_BRANCH=developer
XM_RISK_COLLECTOR_REPO_VERSION=2.0.100

#--- DAU\u7EDF\u8BA1 ----#
XM_DAU_STAT_REPO_BRANCH=developer
XM_DAU_STAT_REPO_VERSION=2.0.12

#--- \u654F\u611F\u7CFB\u7EDF\u63A5\u53E3\u6B27\u8C03\u7528 ----#
XM_SYSTEM_INVOKE_REPO_BRANCH=developer
XM_SYSTEM_INVOKE_REPO_VERSION=2.0.2

##\u2014\u2014\u2014\u2014\u2014\u2014\u2014\u2014\u2014\u2014\u2014 apm \u76F8\u5173\u5E93 --------------##
##XmApm
XMAPM_REPO_BRANCH=developer
XMAPM_REPO_VERSION=2.1.11

## cpu \u76D1\u63A7
CPU_REPO_BRANCH=developer
CPU_REPO_VERSION=2.0.8

#PersonalEvent \u5E93
PERSONAL_EVENT_VERSION=2.0.33
PERSONAL_EVENT_BRANCE=developer

## \u5185\u5B58\u76D1\u63A7
MEMORY_REPO_BRANCH=developer
MEMORY_REPO_VERSION=2.0.71

## \u56FE\u7247\u5185\u5B58\u76D1\u63A7
BITMAPMONITOR_REPO_BRANCH=developer
BITMAPMONITOR_REPO_VERSION=1.0.3

## shark \u5206\u6790
MEMORY_SHARK_REPO_BRANCH=developer
MEMORY_SHARK_REPO_VERSION=2.0.24

## \u5BF9\u8C61\u76D1\u63A7
OBJECT_MONITOR_REPO_BRANCH=developer
OBJECT_MONITOR_REPO_VERSION=1.0.23

# \u7F51\u7EDC\u76D1\u63A7
NETWORK_REPO_BRANCH=developer
NETWORK_REPO_VERSION=2.0.110

# \u7535\u91CF\u76D1\u63A7
BATTERY_MONITOR_REPO_BRANCH=developer
BATTERY_MONITOR_REPO_VERSION=1.0.48


## \u6D41\u91CF\u76D1\u63A7
FLOW_REPO_BRANCH=developer
FLOW_REPO_VERSION=2.0.1

## FPS\u76D1\u63A7
FPS_REPO_BRANCH=developer
FPS_REPO_VERSION=2.2.0

## FragmentMonitor
FRAGMENT_REPO_BRANCH=developer
FRAGMENT_REPO_VERSION=2.0.8

## FPS\u76D1\u63A7
STARTUP_REPO_BRANCH=developer
STARTUP_REPO_VERSION=2.3.2

## inflate\u76D1\u63A7
INFLATE_REPO_BRANCH=developer
INFLATE_VERSION=2.0.6

# evilmethod
EVILMETHOD_REPO_BRANCH=developer
EVILMETHOD_REPO_VERSION=2.2.0

# filesize
FILESIZE_MONITOE_REPO_BRANCH=developer
FILESIZE_MONITOE_REPO_VERSION=2.0.21

# watchdog
WATCHDOG_REPO_BRANCH=developer
WATCHDOG_REPO_VERSION=2.0.1

# iomonitor
IO_MONITOR_REPO_BRANCH=developer
IO_MONITOR_REPO_VERSION=2.0.12

# XmBootMonitor \u5E93
XmBootMonitor_REPO_BRANCE=developer
XmBootMonitor_REPO_VERSION=2.0.1

# XmBootMonitorNoOp \u5E93
XmBootMonitorNoOp_REPO_BRANCE=developer
XmBootMonitorNoOp_REPO_VERSION=1.0.2

# RemoteLog
REMOTE_LOG_REPO_BRANCH=developer
REMOTE_LOG_REPO_VERSION=2.0.2

# apm base
APM_BASE_REPO_BRANCE=developer
APM_BASE_REPO_VERSION=2.0.13

##\u2014\u2014\u2014\u2014\u2014\u2014\u2014\u2014\u2014\u2014\u2014 apm \u76F8\u5173\u5E93 --------------##
# im-core \u559C\u9A6C\u57FA\u7840\u8FDE\u63A5\u5E93
XMIMSDK_REPO_BRANCH=developer
XMIMSDK_REPO_VERSION=2.2.14
# \u98CE\u63A7\u6ED1\u5757\u9A8C\u8BC1\u5F39\u7A97
XM_RISK_VERIFY_REPO_BRANCH=developer
XM_RISK_VERIFY_REPO_VERSION=2.0.16
#android.debug.obsoleteApi=true
## ------- \u4E8C\u65B9\u5E93\u7248\u672C\u4FE1\u606F end---------##
#vivo\u624B\u673A\u4E0D\u652F\u6301\u76F4\u63A5\u4F7F\u7528run\u7684\u65B9\u5F0F(testOnly)\u7684\u5305
android.injected.testOnly = false
## ---- \u76F4\u64AD\u4E2D\u53F0 sdk ---- ##
XMLIVESDK_IMCLIENTLIB_REPO_BRANCH=developer-all
XMLIVESDK_IMCLIENTLIB_REPO_VERSION=4.7.4
## ---- \u5168\u57DFRTC sdk ---- ##
XMRTCSDK_PUBSUB_REPO_BRANCH=developer
XMRTCSDK_PUBSUB_REPO_VERSION=0.0.5
## ---- ASR sdk ---- ##
XMLIVESDK_ASR_SDK_REPO_BRANCH=developer
XMLIVESDK_ASR_SDK_REPO_VERSION=0.0.1
## ---- Cronet sdk ---- ##
XMCRONETSDK_REPO_BRANCH=developer
XMCRONETSDK_REPO_VERSION=0.0.1
#--------------------------------------------------------------#
### ----------------------------------###
### --------- \u4F9D\u8D56\u52A0\u5BC6\u5E93 ---------###
### ----------------------------------###
#
# \u4E0A\u4F20\u5E93
XM_OBJECT_UPLOAD_REPO_BRANCH=developer
XM_OBJECT_UPLOAD_REPO_VERSION=2.0.24
# \u5C0F\u7A0B\u5E8F
XM_LITEAPP_VERSION=1.5.7.4-SNAPSHOT

# \u767B\u5F55\u5E93
XM_LOGIN_SERVICE_VERSION=3.2.4

# app tool box \u72EC\u7ACB\u5E93
APP_TOOL_BOX_REPO_BRANCH=developer
APP_TOOL_BOX_REPO_VRSION=2.0.1

# xm hybrid\u8D44\u6E90\u52A0\u901F
XM_WEB_STATIC_REPO_BRANCH=developer
XM_WEB_STATIC_RES_REPO_VRSION=3.0.4
#\u7EBF\u7A0Bhook\u5E93
THREAD_HOOK_REPO_BRANCH=developer
THREAD_HOOK_REPO_VRSION=1.0.1
#\u7EBF\u7A0B\u76D1\u63A7\u5E93
THREAD_TRACKER_REPO_BRANCH=developer
THREAD_TRACKER_REPO_VRSION=1.0.0

# \u7535\u5B50\u4E66\u89E3\u6790\u5E93
XM_BOOK_LIBRARY_BRANCH=developer
XM_BOOK_LIBRARY_VERSION=1.0.17

# cookie controller
COOKIE_CONTROLLER_REPO_BRANCH=developer
COOKIE_CONTROLLER_REPO_VERSION=1.1.7

# speed up dex2oat
SPEED_DEX2OAT_VERSION=1.1.3
SPEED_DEX2OAT_BRANCH=developer

# linear alloc
XM_LINEAR_ALLOC_BRANCH=developer
XM_LINEAR_ALLOC_VERSION=1.0.2

#\u9690\u79C1\u4FDD\u62A4\u5E93
XM_PRIVACY_PROTECTOR_VERSION=1.2.2
XM_PRIVACY_PROTECTOR_BRANCH=developer

#\u5929\u773Csdk
XM_PRECISEYE_VERSION=1.1.6
XM_PRECISEYE_BRANCH=developer


#\u589E\u957Fsdk
XM_GROWTH_VERSION=1.2.6
XM_GROWTH_BRANCH=developer

#oaid sdk
OAID_SDK_VERSION=2.3.0
OAID_SDK_BRANCH=developer

#Gson TypeAdapter\u751F\u6210\u5E93
XM_TYPE_ADAPTER_VERSION=1.0.1
XM_TYPE_ADAPTER_BRANCH=developer

#XmKOOM
XM_KOOM_VERSION=2.0.7

#\u8BED\u97F3\u5524\u9192\u5E93
XM_VOICE_WAKE_REPO_BRANCH=developer
XM_VOICE_WAKE_VERSION=1.0.57

#\u8BED\u97F3\u5524\u9192\u56DE\u8C03\u5E93
XM_VOICE_WAKE_CALLBACK_REPO_BRANCH=developer
XM_VOICE_WAKE_CALLBACK_VERSION=1.0.31

#\u79BB\u7EBF\u8D44\u6E90\u5E93
XM_OFFLINE_RESOURCE_VERSION=1.1.2.44

# XUID
XUID_REPO_BRANCH=developer
XUID_REPO_VRSION=1.3.27

# XUID-XTICKET
XTICKET_REPO_BRANCH=developer
XTICKET_REPO_VRSION=1.0.0

# \u9501\u76D1\u63A7
LOCK_WATCHER_REPO_BRANCH=canary
LOCK_WATCHER_REPO_VRSION=1.0.29
LOCK_WATCHER_NO_OP_REPO_BRANCH=developer
LOCK_WATCHER_NO_OP_REPO_VRSION=1.0.5

XM_AGENT_VERSION=1.0.31.69

#android.nonTransitiveRClass=true
#android.experimental.nonTransitiveAppRClass=true

EPOXY_VERSION=4.6.4

# ??????
isCiServer=false