#!/usr/local/bin/python
import os
import glob
import sys

ProjectRoot = "" 
TotalCompressFileSize = 0


def isUsed(text):
    usedFileList = ["yw_1222_0355"]

    if text in usedFileList:
        return True

    try:
        if int(text[-1]) in range(10):
            return True
    except:
        pass	


    for root, dirs, files in os.walk(ProjectRoot):
        arrs = root.split("/")
        if "build" not in arrs:
            for singleFile in files:
                if singleFile.endswith("java") or singleFile.endswith("xml"):
                    for line in open(root + "/" + singleFile):
                        if text in line:
                            return True
    return False

def getResDirs():
    resDirs = []
    for root, dirs, files in os.walk(ProjectRoot):
        # print "root: %s, dirs: %s, files: %s"  %(root, dirs, files)

        arrs = root.split("/")
        if arrs[-1] == "res" and "build" not in arrs:
            print ("%s" % (root))
            resDirs.append(root)
    return resDirs

def delUnusedDrawable(resDir):
    global TotalCompressFileSize
    for root, dirs, files in os.walk(resDir):
        for drawable in files:
            if drawable.endswith("png"):
                arr = drawable.split(".")
                if len(arr) != 2:
                    continue
                drawableName = arr[0]
                if not isUsed(drawableName):
                    absPath = os.path.join(root, drawable)
                    print (absPath)
                    TotalCompressFileSize = TotalCompressFileSize + os.path.getsize(absPath)
                    os.remove(absPath)

if __name__ == "__main__":
    # global ProjectRoot
    ProjectRoot = sys.argv[1]
    resDirs = getResDirs()

    for resDir in resDirs:
        delUnusedDrawable(resDir)
    print ("TotalCompressFileSize:%d" % TotalCompressFileSize)
