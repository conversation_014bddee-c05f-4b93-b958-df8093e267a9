package com.ximalaya.ting.android.opensdk.model.album;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.opensdk.datatrasfer.XimalayaResponse;

import java.util.List;

/**
 * 
 * ClassName:SearchAlbumList Function: TODO ADD FUNCTION Reason: TODO ADD REASON
 * 
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015 2015-7-7 下午6:46:45
 * 
 * @see
 */
public class SearchAlbumList extends XimalayaResponse
{
	@SerializedName("total_page")
	private int totalPage;
	@SerializedName("total_count")
	private int totalCount;
	@SerializedName("category_id")
	private int categoryId;
	@SerializedName("tag_name")
	private String tagName;
	private List<Album> albums;

	public int getTotalPage()
	{
		return totalPage;
	}

	public void setTotalPage(int totalPage)
	{
		this.totalPage = totalPage;
	}

	public int getTotalCount()
	{
		return totalCount;
	}

	public void setTotalCount(int totalCount)
	{
		this.totalCount = totalCount;
	}
	
	public int getCategoryId()
	{
		return categoryId;
	}

	public void setCategoryId(int categoryId)
	{
		this.categoryId = categoryId;
	}

	public String getTagName()
	{
		return tagName;
	}

	public void setTagName(String tagName)
	{
		this.tagName = tagName;
	}

	public List<Album> getAlbums()
	{
		return albums;
	}

	public void setAlbums(List<Album> albums)
	{
		this.albums = albums;
	}

	@Override
	public String toString()
	{
		return "SearchAlbumList [totalPage=" + totalPage + ", totalCount="
				+ totalCount + ", albums=" + albums + "]";
	}

}
