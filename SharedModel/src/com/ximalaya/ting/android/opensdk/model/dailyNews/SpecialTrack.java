package com.ximalaya.ting.android.opensdk.model.dailyNews;

import android.os.Parcel;
import android.os.Parcelable;

public class SpecialTrack implements Parcelable {
    private long id;
    private String title;

    protected SpecialTrack(Parcel in) {
        id = in.readLong();
        title = in.readString();
    }

    public static final Creator<SpecialTrack> CREATOR = new Creator<SpecialTrack>() {
        @Override
        public SpecialTrack createFromParcel(Parcel in) {
            return new SpecialTrack(in);
        }

        @Override
        public SpecialTrack[] newArray(int size) {
            return new SpecialTrack[size];
        }
    };

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.id);
        dest.writeString(this.title);
    }
}
