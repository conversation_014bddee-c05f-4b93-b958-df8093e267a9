<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.read.view.SlideDeleteLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/read_slide_delete_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:readIosStyle="true"
    app:readLeftSlide="true"
    app:readSlideEnable="true">

    <LinearLayout
        android:id="@+id/read_ll_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/read_item_title"
            style="@style/read_tv_title_ver"
            android:layout_width="match_parent"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="12dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:singleLine="true"
            android:textSize="15dp"
            tools:text="第一章 隐藏的技能" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/read_left_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:scaleType="fitXY"
                android:src="@drawable/read_ic_ting_read_coll_select"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/read_item_title" />

            <TextView
                android:id="@+id/read_item_subtitle"
                style="@style/read_tv_sub_title_ver_666"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:lineSpacingMultiplier="1.25"
                android:maxLines="3"
                android:textColor="#333333"
                android:textSize="13dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/read_left_icon"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/read_item_title"
                tools:text="隔壁是一个教堂，经常举办各种宗教仪式，我从小听习惯了隔壁是一个教堂，经常举办各种宗教仪式，我从小听习惯了隔壁是一个教堂，我从小听习惯了隔壁是一个教堂经常举办各种宗教仪式，我从小听习惯了" />

        </LinearLayout>

        <View
            android:id="@+id/read_item_bottom_line"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="12dp"
            android:background="@color/host_color_eeeeee_1b1b1b"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </LinearLayout>

    <TextView
        android:id="@+id/read_item_tv_remove"
        android:layout_width="48dp"
        android:layout_height="match_parent"
        android:background="#b3FF4444"
        android:gravity="center"
        android:text="删除"
        android:textColor="#ffffff"
        android:textSize="12dp" />

</com.ximalaya.ting.android.read.view.SlideDeleteLayout>