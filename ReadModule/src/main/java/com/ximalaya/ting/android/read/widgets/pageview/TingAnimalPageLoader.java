package com.ximalaya.ting.android.read.widgets.pageview;

import android.graphics.Bitmap;
import android.text.TextUtils;

import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.read.bean.TingReadAnchorBean;
import com.ximalaya.ting.android.read.bean.TingReadChapterBean;
import com.ximalaya.ting.android.read.bean.TingScrollPageType;
import com.ximalaya.ting.android.read.listener.IPrepareDisplayListener;
import com.ximalaya.ting.android.read.manager.TingPlayPageAnimalManager;
import com.ximalaya.ting.android.read.utils.XMUtils;
import com.ximalaya.ting.android.read.widgets.pageview.listener.ITingReadView;
import com.ximalaya.ting.android.read.widgets.pageview.listener.OnPageChangeListener;
import com.ximalaya.ting.android.read.widgets.pageview.listener.OnParseChapterListener;
import com.ximalaya.ting.android.read.widgets.pageview.listener.OnThemeChangeListener;
import com.ximalaya.ting.android.read.widgets.pageview.parse.ITingParseListener;

import java.util.ArrayList;
import java.util.List;

/**
 * 阅读器的编排者
 */
public class TingAnimalPageLoader implements ITingPageLoader {
    // 监听器
    protected OnPageChangeListener mPageChangeListener;
    //当前的阅读页面
    private ITingReadView curReadView;
    // 当前显示的页
    private TingReadPage mCurPage;
    // 当前章节的页面列表
    private List<TingReadPage> mCurPageList;

    private IPrepareDisplayListener mPrepareDisplayListener;

    public void setPrepareDisplayListener(IPrepareDisplayListener mPrepareDisplayListener) {
        this.mPrepareDisplayListener = mPrepareDisplayListener;
    }

    public TingReadTextHelper getReadTextHelper() {
        return null;
    }

    @Override
    public boolean prevCurChapterPage() {
        return false;
    }

    public ITingReadView getCurReadView() {
        return curReadView;
    }

    public TingAnimalPageLoader(TingPageView pageView) {
        // 页面显示类 禁止直接操作
        this.curReadView = pageView;
    }

    public void init() {

    }

    private boolean isPrepare;

    public boolean isPrepare() {
        return isPrepare;
    }

    /**
     * 设置解析视图的大小
     */
    public void prepareDisplay(int w, int h) {
        isPrepare = true;
        // 重置 PageMode
        if (curReadView != null) {
            curReadView.setPageMode(TingReadSettingManager.getInstance().getPageMode());
        }

        HandlerManager.postOnUIThread(() -> {
            if (mPrepareDisplayListener != null) {
                mPrepareDisplayListener.onPrepare();
            }
        });
    }

    /**
     * 绘制当前页面
     */
    public void drawCurPage(boolean isUpdate) {
        if (curReadView != null) {
            curReadView.drawCurPage(isUpdate);
        }
    }

    /**
     * 打开一个章节 重量级
     */
    public void openChapter(TingReadChapterBean bean, boolean isPreLoad, TingReadAnchorBean anchorBean, int openType, ITingParseListener parseListener) {
        if (curReadView == null || !isPrepare) {
            return;
        }

        mCurPageList = loadPages(anchorBean);

        if (!dealLoadPageList()) {
            if (parseListener != null) {
                parseListener.onParseFailed(new Exception(""));
            }
            return;
        }

        if (parseListener != null) {
            parseListener.onParseSuccess(mCurPageList);
        }

        HandlerManager.postOnUIThread(() -> {
            if (dealLoadPageList()) {
                // 创建一个播放页的截图界面
                mCurPage = mCurPageList.get(0);
            } else {
                return;
            }
            drawCurPage(false);

            if (mOnParseChapterListener != null) {
                mOnParseChapterListener.openChapterSuccess();
            }
        });
    }

    private List<TingReadPage> loadPages(TingReadAnchorBean anchorBean) {
        if (anchorBean == null || !TingPlayPageAnimalManager.isAllowRunAnimal(anchorBean.getEnterFrom(), anchorBean.getPlayPageBitmap())) {
            return null;
        }

        List<TingReadPage> list = new ArrayList<>();

        TingReadPage firstPage = new TingReadPage();
        firstPage.pageType = TingReadPage.VALUE_STRING_PLAY_PAGE_IMAGE_TYPE;
        firstPage.setPageIndex(0);
        firstPage.setMaxPageIndex(1);
        firstPage.setBitmap(anchorBean.getPlayPageBitmap());
        list.add(firstPage);

        TingReadPage nextPage = new TingReadPage();
        nextPage.pageType = TingReadPage.VALUE_STRING_NO_PAGE_TYPE;
        nextPage.setPageIndex(1);
        nextPage.setMaxPageIndex(1);
        nextPage.setAllowDrawBgColor(false);
        list.add(nextPage);

        return list;
    }

    /**
     * 跳转到指定的页
     */
    public void skipToPage(int pos, boolean isAutoScroll) {

    }


    /**
     * 翻到下一页
     */
    public boolean skipToNextPage() {
        if (dealLoadPageList()) {
            return curReadView.autoNextPage();
        } else {
            return false;
        }
    }

    /**
     * 更新时间
     */
    public void updateTime() {

    }

    /**
     * 更新电量
     * 避免过度刷新 1分钟刷一次得了
     */
    public void updateBattery(int level) {

    }

    /**
     * 设置文字相关参数
     */
    public void setTextSize(int textSize) {
    }

    /**
     * 设置页面样式
     *
     * @param pageStyle:页面样式
     */
    public void setPageStyle(PageStyle pageStyle) {

    }

    /**
     * 翻页动画
     *
     * @param pageMode:翻页模式
     * @see PageMode
     */
    public void setPageMode(PageMode pageMode) {
        curReadView.setPageMode(pageMode);
    }

    /**
     * 设置页面切换监听
     */
    public void setOnPageChangeListener(OnPageChangeListener listener) {
        mPageChangeListener = listener;
    }


    public void setIsChapterOpen(boolean isOpened) {

    }


    /**
     * 绘制非竖直滚动页面
     */
    public void drawPage(Bitmap bitmap, boolean isUpdate) {
        if (mCurPageList != null
                && mCurPage != null
                && curReadView instanceof TingPageView) {
            ((TingPageView) curReadView).realOnlyDrawContentPage(bitmap, isUpdate, mCurPage);
        }
    }

    /**
     * 翻阅上一页
     */
    public boolean prev() {
        if (curReadView == null) {
            return false;
        }

        if (dealLoadPageList()) {
            TingReadPage prevPage = getPrevPage();
            if (prevPage != null) {
                mCurPage = prevPage;
                curReadView.drawNextPage();
                return true;
            } else {
                return false;
            }
        }

        if (!hasPrevChapter()) {
            return false;
        }
        mCurPage = new TingReadPage();
        curReadView.drawNextPage();
        return true;
    }

    private boolean hasPrevChapter() {
        return false;
    }

    /**
     * 翻到下一页
     *
     * @return 是否允许翻页
     */
    public boolean next(boolean isAutoScroll) {
        if (curReadView == null) {
            return false;
        }
        // 以下情况禁止翻页
        if (!dealLoadPageList()) {
            return false;
        }
        // 先查看是否存在下一页
        TingReadPage nextPage = getNextPage(isAutoScroll);

        if (nextPage != null) {
            mCurPage = nextPage;
            curReadView.drawNextPage();
            return true;
        }

        if (!hasNextChapter()) {
            return false;
        }
        mCurPage = new TingReadPage();
        curReadView.drawNextPage();
        return true;
    }

    @Override
    public boolean nextCurChapterPage() {
        return false;
    }

    private boolean hasNextChapter() {
        return false;
    }

    /**
     * 判断排版后数据
     * 判断数据列表，改变当前状态
     */
    private boolean dealLoadPageList() {
        return XMUtils.isListValid(mCurPageList);
    }

    // 取消翻页
    public void pageCancel() {
    }

    /**
     * @return 获取上一个页面
     */
    private TingReadPage getPrevPage() {
        return null;
    }

    /**
     * @return 获取下一的页面
     */
    private TingReadPage getNextPage(boolean isAutoScroll) {
        int pos = mCurPage.getPageIndex() + 1;
        if (pos >= mCurPageList.size()) {
            return null;
        }
        TingReadPage txtPage = mCurPageList.get(pos);
        if (mPageChangeListener != null) {
            mPageChangeListener.onPageChange(pos, txtPage, isAutoScroll ? TingScrollPageType.TYPE_AUTO_NEXT : TingScrollPageType.TYPE_NEXT);
        }
        return txtPage;
    }


    /**
     * 关闭书本
     */
    public void closeBook() {
        clearList(mCurPageList);
        mCurPageList = null;
        curReadView = null;
        mCurPage = null;
    }

    private void clearList(List<TingReadPage> list) {
        if (list != null) {
            list.clear();
        }
    }


    public void setOnThemeChangeListener(OnThemeChangeListener themeChangeListener) {

    }

    public TingReadPage getCurPage() {
        return mCurPage;
    }

    public List<TingReadPage> getPageList() {
        return mCurPageList;
    }

    /**
     * 是否是最后一个文字界面 (排除无文字的界面)
     *
     * @param curPage 当期界面
     * @return true 是
     */
    public boolean isLastTextPage(TingReadPage curPage) {
        if (curPage == null || mCurPageList == null || mCurPageList.isEmpty()) {
            return false;
        }

        int size = mCurPageList.size();
        for (int i = size - 1; i >= 0; i--) {
            TingReadPage page = mCurPageList.get(i);
            // 拿到最后一个文字界面
            if (page != null && TextUtils.equals(TingReadPage.VALUE_STRING_NORMAL_PAGE, page.getPageType())) {
                return page == curPage;
            }
        }

        return false;
    }

    /**
     * 是否是最后一个界面
     *
     * @param curPage 当期界面
     * @return true 是
     */
    public boolean isLastPage(TingReadPage curPage) {
        if (curPage == null || mCurPageList == null || mCurPageList.isEmpty()) {
            return false;
        }

        return mCurPageList.get(mCurPageList.size() - 1) == curPage;
    }

    public void clearAdPage() {

    }

    private OnParseChapterListener mOnParseChapterListener;

    public void setParseChapterListener(OnParseChapterListener listener) {
        mOnParseChapterListener = listener;
    }
}
