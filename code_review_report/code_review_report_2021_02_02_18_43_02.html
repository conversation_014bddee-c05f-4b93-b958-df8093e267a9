<html lang=zh-cn>
    <head>
    <meta charset=UTF-8>
    </head><body>
branch: Developer<br>from: 53a5915ca459e5389792d8b1727695f701ea7c14 Merge branch 'auto_merge_branch_WuJi' into 'Canary' 2021-01-26 18:09:42 +0800<br>to: fd421df569c957e43955bb084ef844b9f7314dde Merge branch 'auto_merge_branch_le' into 'Developer' 2021-02-01 14:08:54 +0800

<h2>Jayne</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/542b59b076e" target=_blank>Merge branch 'auto_merge_branch_changle_fang' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e4bfcdcf6ac" target=_blank>[feat] 1、UploadPhotoManager上传失败回调参数调整；2、搜索付费专辑点击下载时改为跳转到rn购买页面</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/29c176bac62" target=_blank>Merge branch 'auto_merge_branch_zhupeipei' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/96db6be3b3b" target=_blank>Merge branch 'auto_merge_branch_zhupeipei' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0e44019e334" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/23817239af5" target=_blank>Merge branch 'auto_merge_branch_changle_fang' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2b31bfe3515" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/dee6ae11ae7" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c20bdda9383" target=_blank>Merge branch 'auto_merge_branch_changle_fang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/737b0324fba" target=_blank>Merge branch 'auto_merge_branch_zhupeipei' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6f6f901adb7" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a783fb1f662" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bcf2d5fe12e" target=_blank>merge from Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d2b4def410b" target=_blank>Merge branch 'Canary' into FeatureTabManagerNew</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/454df33cf60" target=_blank>Merge branch 'auto_merge_branch_changle_fang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2f41e407363" target=_blank>[feat] 定制榜二期需求开发</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/97eaa039aec" target=_blank>Merge branch 'Canary' into FeatureTabManagerNew</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1c5d4c4651c" target=_blank>Merge branch 'Canary' into FeatureTabManagerNew</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7a6ab4ab85f" target=_blank>[feat] 底tab红点展示逻辑梳理</a></li>
</ul>
<h2>Kezhongyang</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fd421df569c" target=_blank>Merge branch 'auto_merge_branch_le' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fb3bdf55792" target=_blank>Merge branch 'auto_merge_branch_le' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/dfeb57df959" target=_blank>Merge branch 'auto_merge_branch_le' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/879b398e547" target=_blank>Merge branch 'auto_merge_branch_Kezy' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b7262379f57" target=_blank>Merge branch 'auto_merge_branch_le' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b97ecd26843" target=_blank>Merge branch 'auto_merge_branch_le' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bfbfbea2d0f" target=_blank>Merge branch 'auto_merge_branch_le' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8806adcf91a" target=_blank>Merge branch 'auto_merge_branch_le' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5526ea066c9" target=_blank>Merge branch 'auto_merge_branch_le' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6b722ae8c83" target=_blank>Merge branch 'auto_merge_branch_le' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7fd7b1412ac" target=_blank>Merge branch 'auto_merge_branch_le' into 'Canary'</a></li>
</ul>
<h2>Kezy</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0e5bde9fc89" target=_blank>[feat] 免广告4期 - 更多浮窗入口icon 暗黑模式资源</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ef96f3a84bb" target=_blank>Merge branch 'Developer' of gitlab.ximalaya.com:android/MainBundle into Developer Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9e77b1ebcea" target=_blank>[fix] 免广告4期- 弹窗UI微调，图片按比例显示（但在华为honor 9x上弹窗会弹跳一下）</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2554f6de019" target=_blank>Merge branch 'Developer' of gitlab.ximalaya.com:android/MainBundle into Developer Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c666d14dced" target=_blank>[fix] 免广告4期- 弹窗UI微调</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3f251c9721a" target=_blank>Merge branch 'Developer' of gitlab.ximalaya.com:android/MainBundle into Developer Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/95b04fd5fe4" target=_blank>[fix] 播放页新版优化 -- 1、新增 撒花广告为非大图广告过滤， 2、判断是否是大图广告取字段 soundtype</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/16d424a9b3e" target=_blank>[fix] 播放页广告上报logType=tingClose 时，区分: 前插视频、播放页icon样式弹幕等 && 无用log删除</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b634d537660" target=_blank>Merge branch 'Developer' of gitlab.ximalaya.com:android/MainBundle into Developer Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1c90c799145" target=_blank>[fix] 免广告4期， 强提醒免广告入口展示后，再展示泡泡条免广告入口 上报数据异常</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e0bf658ca41" target=_blank>Merge branch 'Canary' into Canary_ke_7.3.9_free_ad Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4c986648f32" target=_blank>[fix] 免广告4期 - 埋点修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cbd2e614d60" target=_blank>Merge branch 'Canary' into Canary_ke_7.3.9_free_ad Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f26d5afc750" target=_blank>[feat] 免广告4期 - 强提醒上报</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/49766293967" target=_blank>[fix] 免广告4期 - UI调整 && 埋点字段添加</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/90f07356117" target=_blank>Merge branch 'Canary' into Canary_ke_7.3.9_free_ad Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9ee5515174d" target=_blank>Merge branch 'Canary' into Canary_ke_7.3.9_free_ad Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4723aefe9cd" target=_blank>[fix] 免广告4期 - UI调整 && 埋点核对</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/628b9ebac50" target=_blank>[fix] 免广告4期，测试bug修复-埋点相关</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/208c1c87adc" target=_blank>Merge branch 'Canary' into Canary_ke_7.3.9_free_ad Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d6004e73357" target=_blank>Merge branch 'Canary' into Canary_ke_7.3.9_free_ad Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5276ce7fb87" target=_blank>[feat] 免广告4期 - 埋点实现</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/00e1fbcc3c2" target=_blank>Merge branch 'Canary' into Canary_ke_7.3.9_free_ad Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/de832fd9147" target=_blank>Merge branch 'Canary' into Canary_ke_7.3.9_free_ad Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/37355bde5b4" target=_blank>[feat] 免广告4期 - 看视频时长默认值和免广告时长默认值</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/75226202262" target=_blank>[feat] 免广告四期 - 看视频免广告，看广告时长由后台下发，以及相关逻辑修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/700309c0f4b" target=_blank>Merge branch 'Canary' into Canary_ke_7.3.9_free_ad Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/57b3cc68e15" target=_blank>Merge branch 'Canary' into Canary_ke_7.3.9_free_ad Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c88375adb6b" target=_blank>Merge branch 'Canary' into Canary_ke_7.3.9_free_ad Signed-off-by: Kezy <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d25b84a5450" target=_blank>[feat] 免广告四期 - 播放页更多浮层，添加固定免广告入口</a></li>
</ul>
<h2>WolfXu</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0d2c4078b01" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4fdc8c85418" target=_blank>[fix] 推荐页ui修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/73336a6d9a7" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/03b1f0062e5" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/153e1ef4bb4" target=_blank>[feat] 推荐页今日热点支持封面和声音标题轮播</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cbe4d7ed317" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6e67ad9e3ee" target=_blank>[fix] 推荐页声音条去掉简介</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/df73c1b19e8" target=_blank>[fix] 播放页直播和一起听入口按钮ui调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/79c2682bd99" target=_blank>Merge branch 'auto_merge_branch_zhupeipei' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/98cba51a647" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e97eca6a5b0" target=_blank>[fix] 推荐页ui修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3e71502da84" target=_blank>[fix] 推荐页ui修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b59c4173ce4" target=_blank>[feat] 糖葫芦去掉圆角</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cea83b056ab" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/184f0069891" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cbf4b15b223" target=_blank>[feat] 播放页一起听和直播入口按钮样式修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f59e6624f5d" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ecd6b36f9ff" target=_blank>[feat] 推荐页视频和听单色块上增加光晕</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8a8fa27dca3" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/05ce23263c8" target=_blank>[fix] 解决播放页专辑标题箭头显示问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/76086381485" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3e0308aa128" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9b74c382ed2" target=_blank>Merge branch 'auto_merge_branch_ervin_li' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2a77047b4be" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/92f4e2769ee" target=_blank>[fix] 推荐页ui修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d3dc9b6dc8d" target=_blank>[fix] 推荐页ui修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fb7dc11dc56" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5276bf03018" target=_blank>[fix] 推荐页ui修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/579d402ae25" target=_blank>[feat] 首页tab ui修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/068576670e5" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/612440ebba7" target=_blank>[feat] 推荐页暗黑模式适配</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/937f7a718e0" target=_blank>[feat] 推荐页猜你喜欢兼容老样式</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ae3b044ce28" target=_blank>Merge branch 'auto_merge_branch_felix_chen' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/64e1b2d78b4" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7f254d43506" target=_blank>[feat] 播放页一起听入口按钮文案由配置中心控制</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7105f4dfb6f" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/756dacccb43" target=_blank>[fix] 推荐页打标</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/eed8741d2fe" target=_blank>Merge branch 'auto_merge_branch_zhupeipei' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/524c297067c" target=_blank>Merge branch 'auto_merge_branch_le' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f714034b845" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1fb31605c4a" target=_blank>[fix] 推荐页ui走查修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7d7e55ab7fc" target=_blank>[feat] 推荐页ui验收修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/11035acd01d" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b683fe19ba1" target=_blank>[fix] 推荐页ui走查修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4cd134f892c" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/65126f35155" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b8d47824317" target=_blank>[fix] 推荐页ui走查修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/94c6d182145" target=_blank>Merge branch 'auto_merge_branch_zhupeipei' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f42eada10fb" target=_blank>[fix] 推荐页ui走查修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bc403c4c391" target=_blank>Merge branch 'Canary' into FeatureRecommendPageRevision</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/aea30e10144" target=_blank>[feat] 首页搜索按钮背景修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fa5a432109e" target=_blank>[feat] 首页搜索按钮背景修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/101b58b1f2c" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureRecommendPageRevision</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/71c9e1f4796" target=_blank>[feat] 推荐页ui改版兴趣卡片模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b0599903937" target=_blank>[fix] 推荐页ui走查修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d0d4b4fef93" target=_blank>[fix] 推荐页ui走查修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/697795a833b" target=_blank>[fix] 推荐页ui走查修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fdca46399b8" target=_blank>[fix] 推荐页ui走查修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fa4ecb7478a" target=_blank>[fix] 推荐页ui走查修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7a3d1ec57e3" target=_blank>[fix] 推荐页专辑模块更多按钮高度没对齐</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3b8062ecceb" target=_blank>[feat] 推荐页榜单适配屏幕宽度</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ef82c171443" target=_blank>[feat] 推荐页横滑模块适配，保持显示3个多一点</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a730c4270df" target=_blank>[feat] 推荐页热播榜ui修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4bd803d2970" target=_blank>[fix] 解决播放页箭头颜色问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8de5caf8287" target=_blank>[fix] 解决播放页箭头颜色问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/aec102c5b66" target=_blank>[feat] 推荐页删除经典必听榜相关逻辑</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/483b74f96fc" target=_blank>Merge branch 'Canary' into FeatureRecommendPageRevision</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/64c0ba9f3bc" target=_blank>[feat] 推荐页声音条播放按钮替换</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/55cd8354335" target=_blank>Merge branch 'Canary' into FeatureRecommendPageRevision</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/04075922063" target=_blank>[feat] 推荐页改版暗黑模式适配</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5251e78328f" target=_blank>[feat] 推荐页改版暗黑模式正在直播和最近常听处理</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0570621bbb5" target=_blank>[feat] 推荐页改版删除看小说模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3b05708a217" target=_blank>[feat] 推荐页改版删除好友推荐模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4d56f25365c" target=_blank>[feat] 推荐页改版删除不要的模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d0490ac06f0" target=_blank>[feat] 推荐页改版删除不要的模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/620516dc009" target=_blank>[feat] 首页搜索按钮修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a95bd1859c0" target=_blank>[feat] 推荐页改版模块圆角</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/36ea0d18122" target=_blank>[feat] 推荐页改版今日推荐模块阴影</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f415a7a1cf7" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureRecommendPageRevision</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f04aec52994" target=_blank>[feat] 推荐页改版今日推荐模块阴影</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/26dad4e7aae" target=_blank>[feat] 推荐页改版今日推荐模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bf6cfa8ce61" target=_blank>[feat] 推荐页改版今日推荐模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3e09609e35f" target=_blank>[feat] 推荐页改版热播榜热度去掉热度文字</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/57c468ee2ab" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureRecommendPageRevision</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/263c77e394d" target=_blank>[feat] 推荐页改版删除方块运营位和方块运营位一键听相关代码</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f4d0822c114" target=_blank>[feat] 推荐页改版专辑条、声音条、直播条和其他模块间分割线</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8a71adf9aba" target=_blank>[feat] 推荐页改版删除方块运营位相关模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b7778959b51" target=_blank>[feat] 推荐页改版文章模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d1d7ba968bf" target=_blank>[feat] 推荐页改版问答模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1daebbb530f" target=_blank>[feat] 推荐页警告清理</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6a6b0072b48" target=_blank>[feat] 推荐页改版最近常听</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7a451ec9a74" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureRecommendPageRevision</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/66636131626" target=_blank>[feat] 推荐页改版视频模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4d53b3d6619" target=_blank>[feat] 推荐页改版视频模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/874081296de" target=_blank>[feat] 推荐页改版最近常听和视频模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f6ebf71c249" target=_blank>[feat] 推荐页改版删除听单列表</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/911e74c2f6e" target=_blank>[feat] 推荐页改版今日推荐模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5fb659272e0" target=_blank>[feat] 推荐页改版今日推荐模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/45cd3022e63" target=_blank>[feat] 推荐页改版广告大图</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8e817057e72" target=_blank>[feat] 推荐页改版单行猜你喜欢</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a117d1eeee7" target=_blank>[feat] 推荐页改版双行猜你喜欢</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f54cee96aef" target=_blank>[feat] 推荐页改版去掉渐变条</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9db27d33282" target=_blank>[feat] 推荐页改版一般专辑模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7c08ff6e199" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureRecommendPageRevision</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cf73cb8e90b" target=_blank>[fix] 解决设置铃声可能无效的问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c2660b03ddb" target=_blank>[feat] 推荐页改版私人fm列表</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a3fcbdb4a96" target=_blank>[feat] 推荐页改版主播模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7b484c72ab8" target=_blank>[feat] 推荐页改版分类词</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9e1aa48baea" target=_blank>[feat] 推荐页改版私人fm</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a5a4caa160c" target=_blank>[feat] 推荐页改版私人fm</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fcc4691b1b3" target=_blank>[feat] 推荐页改版视频</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a07be4993e1" target=_blank>[feat] 推荐页改版视频</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7b2ca0cb153" target=_blank>[feat] 推荐页改版去掉自定义听单</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7a7986d03fc" target=_blank>[feat] 推荐页改版听单</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/267a8f153ca" target=_blank>[feat] 推荐页改版热播榜和热搜榜背景图</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fde3f11533b" target=_blank>[feat] 推荐页改版热播榜</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/365d44149bc" target=_blank>[feat] 推荐页改版热搜榜</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/dbb597f5104" target=_blank>[feat] 推荐页改版语音房模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/98507e8b80f" target=_blank>[feat] 推荐页改版直播模块更多</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/372afb426c7" target=_blank>[feat] 推荐页改版直播模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/153435faf21" target=_blank>[feat] 推荐页改版直播模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7ba96f84a81" target=_blank>[feat] 推荐页改版直播条</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4385b81ed3f" target=_blank>[feat] 推荐页改版声音条</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5f559f327e0" target=_blank>[feat] 推荐页改版声音条</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ec5df68bd59" target=_blank>[feat] 推荐页改版声音条</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ca53b6911bf" target=_blank>[feat] 推荐页改版专辑条</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e0c96648132" target=_blank>[feat] 推荐页改版声音条</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b635f0177a6" target=_blank>[feat] 推荐页改版声音条</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2f1e6e6bb9e" target=_blank>[feat] 推荐页改版专辑条</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/05c37ca3e14" target=_blank>[feat] 推荐页改版专辑条</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6f6d0c202f8" target=_blank>[feat] 推荐页改版专辑条</a></li>
</ul>
<h2>WuJi</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f49ccb6bda2" target=_blank>Merge branch 'Developer' of http://gitlab.ximalaya.com/android/MainBundle into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2d3769e05e0" target=_blank>[fix] 调整加购购物车时的传参</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/47a470678a2" target=_blank>Merge branch 'Developer' into WholeAlbumReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/468f97b52d2" target=_blank>Merge branch 'Developer' into WholeAlbumReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/90e91dae1a2" target=_blank>[fix] 为tab中提供spananbleString</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ce20d8b3689" target=_blank>Merge branch 'Canary' into WholeAlbumReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0bfc9ee4078" target=_blank>Merge branch 'Canary' into WholeAlbumReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/604bffdea98" target=_blank>[fix] 在专辑售前页推荐子Tab的接口请求中增加categoryId</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0930f4693ce" target=_blank>[fix] 调整大促角标的高度</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/06af7aa25e3" target=_blank>[fix] 调整分割线高度</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/42bbbce7cbb" target=_blank>Merge branch 'Canary' into WholeAlbumReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/85f8c67ffe6" target=_blank>[fix] 调整分割线高度</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8ae37079a64" target=_blank>[fix] 处理提测过程中的Npe</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ae45caeab13" target=_blank>[fix] 处理提测过程中的Npe</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cbf10777b29" target=_blank>[fix] 处理ui验收的问题和增加可视化埋点数据</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0b11c38c5fa" target=_blank>Merge branch 'Canary' into WholeAlbumReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/51ca6337b8b" target=_blank>Merge branch 'Canary' into WholeAlbumReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/477c6c1841f" target=_blank>[fix] 处理提测bug</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/da9a4c27f76" target=_blank>Merge branch 'Canary' into WholeAlbumReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/44b7832b566" target=_blank>[feat] 专辑售前页新增相关专辑推荐tab</a></li>
</ul>
<h2>changle.fang</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/28b6fdea1db" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7393e4fdadd" target=_blank>[fix] 1、tab下筛选词跳转调整 2、修复异常</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bed770638a5" target=_blank>Merge branch 'auto_merge_branch_Jayne' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/41eb9793fac" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/90c0934cf16" target=_blank>[feat] 青少年模式不展示皮肤</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0bc5028f5ad" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4c11f79f1f2" target=_blank>Merge branch 'FeatureTabManagerNew' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1188c27e214" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/07536ca253d" target=_blank>Merge branch 'FeatNewProduct' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/72dfd626dd0" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/95d08f8be69" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5e705b382ad" target=_blank>[fix] ui调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5b57affd4d7" target=_blank>Merge branch 'Canary' into FeatureTabManagerNew Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a98bb2d5ea2" target=_blank>Merge branch 'Canary' into FeatureTabManagerNew Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/079295d641a" target=_blank>Merge branch 'Canary' into FeatureTabManagerNew Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ca907c40d33" target=_blank>Merge branch 'Canary' into FeatureTabManagerNew Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fc51a97f9e5" target=_blank>Merge branch 'Canary' into FeatNewProduct Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/653b3a71c58" target=_blank>[feat] 新品扶持更多跳转由服务端控制</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4799ea2d0a1" target=_blank>Merge branch 'Canary' into FeatNewProduct Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2a76984f066" target=_blank>Merge branch 'Canary' into FeatNewProduct Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d7f7f82dd5c" target=_blank>[feat] 新品推荐iting</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d23d4c864b4" target=_blank>Merge branch 'Canary' into FeatNewProduct Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/072b3cb81b6" target=_blank>Merge branch 'Canary' into FeatNewProduct Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/43910af7a45" target=_blank>[feat] 新品推荐模块</a></li>
</ul>
<h2>chendekun</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/af090220acb" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0cf6056dfaa" target=_blank>[fix] ui调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0b216c24df9" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8b5c9fe0cdc" target=_blank>[fix] 修复一起听问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3ec0b195d14" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0a1ce210260" target=_blank>[fix] 一起听问题修复</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f72e8d3fe8c" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5eaaba58088" target=_blank>[fix] 创建房间参数传递错误</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/adc9e1ce0b0" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ac2337aa933" target=_blank>[fix] 一起听ui调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/14ac7e6cea5" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/26309d0c21d" target=_blank>[fix] 一起听问题修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/634fcca75a1" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/795346df57d" target=_blank>[fix] 一起听问题修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/912a6a165fa" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f754a0de88c" target=_blank>[feat] 一起听埋点补充</a></li>
</ul>
<h2>duruochen</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6a0bd292f5a" target=_blank>Merge remote-tracking branch 'origin/Canary' into 0.1.07_openlive Signed-off-by: duruochen <<EMAIL>></a></li>
</ul>
<h2>ervin.li</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/02e605026c2" target=_blank>[fix] 增加cookie黑名单展示</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3e79f0645c2" target=_blank>Merge branch 'Developer' of gitlab.ximalaya.com:android/MainBundle into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/434aa038616" target=_blank>[fix] 增加关闭自动扫描隐私风险开关</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4e6c39bb639" target=_blank>[fix] 解决域名链接测试页面无法获取焦点问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/16dd47edc6e" target=_blank>[docs] 提交cr报告</a></li>
</ul>
<h2>feiwen</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/27ef84bbc4c" target=_blank>[fix] 修复三星折叠屏上评论内容显示不全问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d9e72c9740e" target=_blank>[fix] 修复三星折叠屏上评论内容显示不全问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e66bc4479ff" target=_blank>Merge branch 'auto_merge_branch_Jayne' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3ecb1d1c268" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a6458d907e5" target=_blank>Merge branch 'auto_merge_branch_changle_fang' into 'Canary'</a></li>
</ul>
<h2>felix.chen</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/327cad3a4e1" target=_blank>Merge branch 'Developer' of gitlab.ximalaya.com:android/MainBundle into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ed596bb74a5" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b901fd383c7" target=_blank>[fix] 解决空指针</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f0ed2c629c5" target=_blank>Merge branch 'auto_merge_branch_felix_chen' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/35e4752bdd3" target=_blank>[feat] 图片转换</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/97bc1ee5a13" target=_blank>Merge branch 'auto_merge_branch_ervin_li' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3cb194ddbf3" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/624877572f5" target=_blank>Merge branch 'auto_merge_branch_felix_chen' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4fa35b0ca5a" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/64ce6050086" target=_blank>Merge branch 'Canary' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4593c54076d" target=_blank>Merge branch 'auto_merge_branch_felix_chen' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/537f2c9d0ea" target=_blank>Merge branch 'auto_merge_branch_felix_chen' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d40cad41d6f" target=_blank>Merge branch 'Canary' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7f59f698af8" target=_blank>Merge branch 'Canary' into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/42c9f291633" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e38d5e38abd" target=_blank>Merge branch 'auto_merge_branch_Kezy' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/64d4a3ef444" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/749d3ed2d65" target=_blank>Merge branch 'auto_merge_branch_le' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9b3417ed14a" target=_blank>Merge remote-tracking branch 'origin/Feature-CustomNotification' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/22e8b0c6ab2" target=_blank>Merge branch 'Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7960dd6c611" target=_blank>Merge remote-tracking branch 'origin/Feature-CustomNotification' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5bf9f789bbe" target=_blank>Merge remote-tracking branch 'origin/Feature-CustomNotification' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7af8b910520" target=_blank>Merge branch 'Canary' into Feature-CustomNotification</a></li>
</ul>
<h2>harry.shi</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8d9f88f8607" target=_blank>Merge branch 'auto_merge_branch_felix_chen' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7a07ab7f03a" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b869c446d6e" target=_blank>Merge branch 'auto_merge_branch_WuJi' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/34a84afc8e0" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a58278820bc" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4e81f0d1b0c" target=_blank>Merge branch 'auto_merge_branch_zhaowuwei' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4547644e450" target=_blank>Merge branch 'auto_merge_branch_zhupeipei' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d494b9aff0b" target=_blank>Merge branch 'auto_merge_branch_zhaowuwei' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9ad1138a842" target=_blank>Merge branch 'auto_merge_branch_feiwen' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ebfd0cd740b" target=_blank>Merge branch 'auto_merge_branch_WuJi' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6b416eabbab" target=_blank>Merge branch 'auto_merge_branch_felix_chen' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/458feac9050" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Canary'</a></li>
</ul>
<h2>jackqin</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d7ce4ea3380" target=_blank>Merge branch 'auto_merge_branch_ervin_li' into 'Canary'</a></li>
</ul>
<h2>kaikai.zhang</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/537b8f9d45f" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2aec75fc7c0" target=_blank>[fix] 修复 bugly bug #40678166</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f0a546bc517" target=_blank>[feat] 增加华为新系统判断方法</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4180cbba3aa" target=_blank>[feat] 增加华为新系统判断方法</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cd581d5e3ec" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/75c0e3fb07e" target=_blank>Merge branch 'auto_merge_branch_zhangshaohua' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9f0979aa718" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1a3372e8c98" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9b7bdb6ce3c" target=_blank>[fix] 咔嚓长按进入笔记,切到咔嚓视频默认封面为黑色</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/42bd0e37959" target=_blank>[fix] 咔嚓长按进入笔记,切到咔嚓视频默认封面为黑色</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/206b93753fa" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/83156c4ef00" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/64a48414240" target=_blank>[fix] 咔嚓截选到最大提示,每同显示 一次</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/98a5a706a9a" target=_blank>[fix] 听友圈跳转更改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4f373d71ecf" target=_blank>[fix] 创建带文本咔嚓完成，回到精彩片段页面没有显示文本</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5a8a5a6f916" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a340f071f0c" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c5c8c52e0d0" target=_blank>[fix] 包名路径不对</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8bd6ed59fcf" target=_blank>[feat] 华为设备并且系统是EMUI 11.1及以上版本, 通知栏样式设置入口将隐藏</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6958222ebcb" target=_blank>Merge branch 'auto_merge_branch_feiwen' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f3cb0b45394" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f2366da4aa1" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4852d982322" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1f433e700b5" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/65f9d9a254c" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f7112be28f9" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9e32a72fea2" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/28183144318" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/13c66e9621c" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3a74643f152" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/583eebee034" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a3a6d1feab9" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/92783766b8c" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9779a83a837" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0c8de23af84" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4e48a69b23e" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/863a8a93beb" target=_blank>[feat] merge canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0287112d6ee" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7a333175fcb" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/08a0c0f7c70" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/214e42d2ba9" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8885b0f0883" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b1ed4cb3787" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4a9df961184" target=_blank>[fix] 修复bugly #40685850 java.lang.IllegalArgumentException</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3958629036f" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e6e8e36e3cb" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/28928ce219c" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b348742f3d2" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/18693c00249" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4fb33e3b32d" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6ba87dbc0a8" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d5d8e6d53d5" target=_blank>[feat] 华为手机默认使用系统样式</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d4b6be36fda" target=_blank>[feat] 通知栏样式更改生效</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/47507401cb5" target=_blank>[feat] 通知栏样式跨进程修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6137ba44496" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4069e38f91d" target=_blank>[feat] merge canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c940445513e" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fdfaba579cb" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/57ea059b61f" target=_blank>[fix] 通知栏样式变化逻辑优化</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c0785d4bdc9" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b0541ff8033" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/db279f24858" target=_blank>[feat] merge canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c66619e8b71" target=_blank>[feat] 通知栏样式-系统样式</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9728750d3a4" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f7b4334665f" target=_blank>[feat] 通知栏样式更改 init</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/258b9b066dd" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4546125756e" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/dd076b12f71" target=_blank>[feat] 通知栏样式更改入口</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/38f12b595d5" target=_blank>[feat] 通知栏样式更改入口</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7e35cc8c00e" target=_blank>[feat] 通知栏样式更改入口</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/016b7e0c11d" target=_blank>[feat] 通知栏样式更改入口</a></li>
</ul>
<h2>le</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/01984cdf62c" target=_blank>[fix] 联合霸屏关闭上报多次问题修复</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/28b6fdea1db" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7393e4fdadd" target=_blank>[fix] 1、tab下筛选词跳转调整 2、修复异常</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6d07e99abc7" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5d31a64dd98" target=_blank>[fix] 联合霸屏关闭按钮添加上报</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8838d74ab62" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f25cf04fb0e" target=_blank>[feat] 首页大图广告UI修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f5b386c3b08" target=_blank>[feat] 滑到猜你喜欢模块的时候就上报了猜你喜欢,没有实际真实上报</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bed770638a5" target=_blank>Merge branch 'auto_merge_branch_Jayne' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a45c33ace82" target=_blank>Merge branch 'auto_merge_branch_Kezy' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9c810a3e37f" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/13c1c46cf85" target=_blank>[feat] 1.二次曝光暂停视频有问题,2.贴片正在展示广告时,点击播放页大图的广点通视频广告有声,返回到播放页的时候贴片广告没有声音</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1dcc1d9dc1c" target=_blank>[fix] 修复单行猜你喜欢出现上报多次和没有上报的情况</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7b1f24dbda5" target=_blank>[feat] 声音解码模式可以配置</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7be49e90bc0" target=_blank>Merge branch 'auto_merge_branch_Kezy' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/41eb9793fac" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/90c0934cf16" target=_blank>[feat] 青少年模式不展示皮肤</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/49776608ab3" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4bf2a7ce814" target=_blank>[fix] 评论页广点通图片广告 第一次打开评论页的时候必现空白</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7d7ca77ec5d" target=_blank>Merge branch 'auto_merge_branch_Kezy' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2963f251746" target=_blank>[fix] 播放页推荐专辑上报在有小黄条的情况下,提前上报展示问题修复</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b3e78a1e796" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4cf5626938e" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/de5c5b7a5d4" target=_blank>[fix] 巨幕广告点击上报类型错误</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2c383106803" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0a2b866f7cc" target=_blank>[fix] 贴片视频广告在看视频免广告过程中如果关闭看视频窗口出现广告视频不能继续播放的问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3301283a6bb" target=_blank>Merge branch 'auto_merge_branch_Kezy' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ca6fdb297ed" target=_blank>Merge branch 'auto_merge_branch_Kezy' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0bc5028f5ad" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4c11f79f1f2" target=_blank>Merge branch 'FeatureTabManagerNew' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fe407a079d2" target=_blank>Merge branches 'CCCBranchForLe' and 'Canary' of http://gitlab.ximalaya.com/android/MainBundle into Canary Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/db68c6a8d52" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/799f7e7b4a5" target=_blank>Merge branches 'Canary' and 'HomeDropDownAdDropDown' of http://gitlab.ximalaya.com/android/MainBundle into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/47e67a2e31b" target=_blank>Merge branches 'Canary' and 'feature_elderly_ad_video' of http://gitlab.ximalaya.com/android/MainBundle into Canary Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/60cda6be1c4" target=_blank>Merge branches 'Canary' and 'DubbingForLe' of http://gitlab.ximalaya.com/android/MainBundle into Canary Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1188c27e214" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/07536ca253d" target=_blank>Merge branch 'FeatNewProduct' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f93cc498910" target=_blank>Merge branch 'auto_merge_branch_ervin_li' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/72dfd626dd0" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6e83055cc68" target=_blank>Merge branches 'Canary' and 'DubbingForLe' of gitlab.ximalaya.com:android/MainBundle into DubbingForLe Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6d30497f7f2" target=_blank>Merge branch 'Canary' of gitlab.ximalaya.com:android/MainBundle into HomeDropDownAdDropDown</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1a83e24d275" target=_blank>Merge branches 'Canary' and 'feature_elderly_ad_video' of gitlab.ximalaya.com:android/MainBundle into feature_elderly_ad_video</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/387a8d7dfae" target=_blank>[fix] 修复二次曝光的时候视频类没有展示标题问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/95d08f8be69" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5e705b382ad" target=_blank>[fix] ui调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a1291479273" target=_blank>[feat] 播放页大图未展示上报问题修复</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5b57affd4d7" target=_blank>Merge branch 'Canary' into FeatureTabManagerNew Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a98bb2d5ea2" target=_blank>Merge branch 'Canary' into FeatureTabManagerNew Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c0889299d6d" target=_blank>Merge branches 'Canary' and 'feature_elderly_ad_video' of gitlab.ximalaya.com:android/MainBundle into feature_elderly_ad_video Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ab89ed640a0" target=_blank>Merge branches 'Canary' and 'HomeDropDownAdDropDown' of gitlab.ximalaya.com:android/MainBundle into HomeDropDownAdDropDown Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4d466478b91" target=_blank>Merge branches 'CCCBranchForLe' and 'Canary' of gitlab.ximalaya.com:android/MainBundle into CCCBranchForLe Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1683d630dcc" target=_blank>Merge branches 'CCCBranchForLe' and 'Canary' of gitlab.ximalaya.com:android/MainBundle into CCCBranchForLe Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8ce317a12d1" target=_blank>Merge branches 'Canary' and 'HomeDropDownAdDropDown' of gitlab.ximalaya.com:android/MainBundle into HomeDropDownAdDropDown Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/069ecc97a36" target=_blank>Merge branches 'Canary' and 'feature_elderly_ad_video' of gitlab.ximalaya.com:android/MainBundle into feature_elderly_ad_video</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9f5c0201e2d" target=_blank>[fix] 老年模式bug修复</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/079295d641a" target=_blank>Merge branch 'Canary' into FeatureTabManagerNew Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ed0047a8fda" target=_blank>Merge branches 'Canary' and 'HomeDropDownAdDropDown' of gitlab.ximalaya.com:android/MainBundle into HomeDropDownAdDropDown Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b3ea3329a60" target=_blank>[feat] 修复null指针</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/38b83130af4" target=_blank>Merge branches 'CCCBranchForLe' and 'Canary' of gitlab.ximalaya.com:android/MainBundle into CCCBranchForLe Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/62efe8ef735" target=_blank>[feat] 在线实时播放视频添加缓存</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e307f27626b" target=_blank>Merge branches 'Canary' and 'HomeDropDownAdDropDown' of gitlab.ximalaya.com:android/MainBundle into HomeDropDownAdDropDown</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0394bb96d2f" target=_blank>[feat] 首页大图位置视频广告添加</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b603135b3c9" target=_blank>[feat] 播放页大图位置视频广告添加</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ca907c40d33" target=_blank>Merge branch 'Canary' into FeatureTabManagerNew Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/484dd9a0e2d" target=_blank>Merge branches 'Canary' and 'feature_elderly_ad_video' of gitlab.ximalaya.com:android/MainBundle into feature_elderly_ad_video Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/520caa88810" target=_blank>[feat] 播放页大图广告重构</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f383f9f1e36" target=_blank>[feat] 播放页大图广告重构,将不同样式的广告分离开</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bf3590da924" target=_blank>Merge branches 'HomeDropDownAdDropDown' and 'feature_elderly_ad_video' of gitlab.ximalaya.com:android/MainBundle into HomeDropDownAdDropDown Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/531baabc0eb" target=_blank>[feat] 老年模式UI调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/da3a5368150" target=_blank>Merge remote-tracking branch 'origin/FeatureRecommendPageRevision' into FeatureRecommendPageRevision Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/50cfee39b4b" target=_blank>[feat] 首页橱窗广告UI调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fdc6f9674b7" target=_blank>Merge branches 'Canary' and 'feature_elderly_ad_video' of gitlab.ximalaya.com:android/MainBundle into feature_elderly_ad_video Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/876e6c48779" target=_blank>[feat] 老年模式播放页广告适配</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5e7f5b8e7cb" target=_blank>Merge branches 'feature_elderly2' and 'feature_elderly_ad_video' of gitlab.ximalaya.com:android/MainBundle into feature_elderly_ad_video Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/337202a7b1c" target=_blank>[feat] 广告视频功能标准化,适配声音视频贴片</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c3aed85d3cb" target=_blank>Merge branch 'AdVideoOptimization' of gitlab.ximalaya.com:android/MainBundle into feature_elderly_ad_video Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cf73cf51c0e" target=_blank>[feat] 老年模式广告添加,上报字段添加</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fc51a97f9e5" target=_blank>Merge branch 'Canary' into FeatNewProduct Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/653b3a71c58" target=_blank>[feat] 新品扶持更多跳转由服务端控制</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9e886361f42" target=_blank>Merge branches 'BBBBranchForLe' and 'Canary' of gitlab.ximalaya.com:android/MainBundle into BBBBranchForLe Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b0063af46fe" target=_blank>Merge branch 'feature_elderly2' of gitlab.ximalaya.com:android/MainBundle into BBBBranchForLe Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9dc759fb603" target=_blank>[feat] 老年模式广告添加</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4799ea2d0a1" target=_blank>Merge branch 'Canary' into FeatNewProduct Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8749aaec342" target=_blank>[feat] 广告视频播放组件封装</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2a76984f066" target=_blank>Merge branch 'Canary' into FeatNewProduct Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d7f7f82dd5c" target=_blank>[feat] 新品推荐iting</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d23d4c864b4" target=_blank>Merge branch 'Canary' into FeatNewProduct Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e6a8842fcce" target=_blank>Merge branches 'Developer' and 'DubbingForLe' of gitlab.ximalaya.com:android/MainBundle into DubbingForLe Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/072b3cb81b6" target=_blank>Merge branch 'Canary' into FeatNewProduct Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/43910af7a45" target=_blank>[feat] 新品推荐模块</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f4855fa2359" target=_blank>Merge branches 'CCCBranchForLe' and 'Canary' of gitlab.ximalaya.com:android/MainBundle into CCCBranchForLe Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c7fab252fd9" target=_blank>Merge branches 'CCCBranchForLe' and 'Canary' of gitlab.ximalaya.com:android/MainBundle into CCCBranchForLe Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1f708556f25" target=_blank>[feat] 付费解锁音贴提示功能完成</a></li>
</ul>
<h2>liudekai</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5199958d40c" target=_blank>Merge branch 'auto_merge_branch_zhupeipei' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d1d93ccb9c3" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fe9e5849b15" target=_blank>[fix] 修复空指针</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a0ef679125f" target=_blank>[fix] 个人页积分入口背景增加AB</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b67663431d1" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3dc2e9fe434" target=_blank>[fix] 个人页账号等级图标替换</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/126f922e85f" target=_blank>[fix] 账号页积分签到入口大小调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a0d5158a56d" target=_blank>[fix] 账号页UI走查</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f88cb7c99e2" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fd37563502c" target=_blank>[feat] 账号页创作服务icon替换</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/62f7742bf6d" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2 Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/21d4322bc48" target=_blank>[fix] 账号页改版UI走查</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/da14955e7d9" target=_blank>[fix] 删除重复color资源</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0b7cb304d75" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bcac9b57205" target=_blank>[feat] 账号页创作服务添加打标</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c2925634979" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/14adc2436da" target=_blank>[feat] 账号页创作服务添加打标</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7485c2ac5d5" target=_blank>[fix] 账号页模式切换适配皮肤包</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/39d6fdfdb37" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2 Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/730f80c12ae" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/18959274902" target=_blank>[fix] 账号页创作服务UI调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e58ed79df50" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2 Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/84739e3f787" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2 Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b039074d6e1" target=_blank>[fix] 账号页改版UI走查</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/dc5bfa4281d" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/11337e40411" target=_blank>[fix] 账号页处理部分警告</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7ed4a5bd2dd" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9f7c57e0f8b" target=_blank>[fix] 账号页用户昵称UI调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d5e165c40ed" target=_blank>[fix] 账号页创作活动icon UI调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fdc70f29ba0" target=_blank>[refactor] 我页task与fragment分离</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fbdef142c5c" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2 Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/62a74bd7b23" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2 Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/17c88ea7c94" target=_blank>[feat] 账号页主播成长指南数据请求、展示、点击时间调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cce7d026321" target=_blank>[feat] 账号页小雅书房icon UI调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/89247ca388f" target=_blank>[refactor] 账号页代码格式化，清除部分警告</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cbb7a23a0e5" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f0f9325e45f" target=_blank>[fix] 账号页UI调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8aeccfe3325" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2 Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/35756629926" target=_blank>[fix] 账号页UI调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6f6870845a0" target=_blank>[fix] 账号页创作服务UI调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c97152c72f2" target=_blank>[fix] 账号页Vip条UI调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0683280e912" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2 Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1df321d9fc5" target=_blank>[feat] 删除多余的布局参数</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/083fb976969" target=_blank>[feat] 账号页用户昵称和标签布局调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e802039e163" target=_blank>[feat] 账号页积分UI调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0f80b9e016b" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeatureMineV2 Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3e4632899dd" target=_blank>[feat] 账号页头部改版UI调整，适配暗黑</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0aeebd45945" target=_blank>[feat] 账号页头部UI改版</a></li>
</ul>
<h2>neil.qian</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/eaae98ec145" target=_blank>Merge branch 'Developer' of gitlab.ximalaya.com:android/MainBundle into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/eb35dda8b3b" target=_blank>[live][fix][充值页退出隐藏输入法]</a></li>
</ul>
<h2>zhangkaikai</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/537b8f9d45f" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cd581d5e3ec" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/75c0e3fb07e" target=_blank>Merge branch 'auto_merge_branch_zhangshaohua' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9f0979aa718" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1a3372e8c98" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/206b93753fa" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/83156c4ef00" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5a8a5a6f916" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a340f071f0c" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6958222ebcb" target=_blank>Merge branch 'auto_merge_branch_feiwen' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f2366da4aa1" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c66619e8b71" target=_blank>[feat] 通知栏样式-系统样式</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9728750d3a4" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f7b4334665f" target=_blank>[feat] 通知栏样式更改 init</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/258b9b066dd" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4546125756e" target=_blank>Merge remote-tracking branch 'origin/Canary' into Feature-CustomNotification</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/dd076b12f71" target=_blank>[feat] 通知栏样式更改入口</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/38f12b595d5" target=_blank>[feat] 通知栏样式更改入口</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7e35cc8c00e" target=_blank>[feat] 通知栏样式更改入口</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/016b7e0c11d" target=_blank>[feat] 通知栏样式更改入口</a></li>
</ul>
<h2>zhangshaohua</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6b05dafbaf3" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fd2edded90f" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/edaa6d5293b" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9c54a6754cb" target=_blank>Merge branch 'Developer' of gitlab.ximalaya.com:android/MainBundle into Developer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cbb365b4f83" target=_blank>[fix] iting跳转创建圈子必须先登录</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d5afaf8a9f1" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/050bbaff22b" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a173923b75e" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1eafd980eb1" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a58ee489b32" target=_blank>Merge branch 'auto_merge_branch_zhangshaohua' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5f2bca3d68d" target=_blank>Merge branch 'Canary' of gitlab.ximalaya.com:android/MainBundle into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/37c4725d0d9" target=_blank>[feat] 去除个人页feed流相关无用代码</a></li>
</ul>
<h2>zhaowuwei</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/96374fa5e55" target=_blank>Merge branches '0.1.07_openlive' and 'Developer' of gitlab.ximalaya.com:android/MainBundle into 0.1.07_openlive</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ce64bace617" target=_blank>[feat] 直播充值消费增加限制，合并canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7acc232bd8c" target=_blank>[feat] 直播充值消费增加限制</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/99797110ad5" target=_blank>Merge branches '0.1.07_openlive' and 'Canary' of gitlab.ximalaya.com:android/MainBundle into 0.1.07_openlive</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/903e3006df5" target=_blank>[feat] 直播充值消费增加文案说明'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/76c60cb7323" target=_blank>Merge branch 'Canary' of gitlab.ximalaya.com:android/MainBundle into 0.1.07_openlive</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/455e22b72c0" target=_blank>Merge branches '0.1.07_openlive' and 'Canary' of gitlab.ximalaya.com:android/MainBundle into 0.1.07_openlive</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5eb6e4a5eba" target=_blank>[feat] 直播开播整合，替换新API</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2fafd626022" target=_blank>[feat] 直播模块增加充值消费限制处理</a></li>
</ul>
<h2>zhupeipei</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/86238753270" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: zhupeipei <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/efe04221f35" target=_blank>[fix] catch取色异常</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4ca2a4c0ef4" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: zhupeipei <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d4b55162694" target=_blank>[feat] 今日热点ui调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6feed3c4347" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ed0fd35f245" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: zhupeipei <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/dd701a3a95a" target=_blank>[feat] 我听下载icon替换</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/96d684900fd" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d346902c969" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/91c7af96421" target=_blank>Merge remote-tracking branch 'origin/Developer' into Developer Signed-off-by: zhupeipei <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d79ca15d242" target=_blank>[feat] 我听tab样式修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/300463b590d" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e6a1faa5ea6" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/12e0c22b24b" target=_blank>[feat] 我听tab样式修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4c1a3656217" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Developer'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e3b124451c4" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/df16b841eaf" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: zhupeipei <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/415505fc5c5" target=_blank>[feat] 我听糖葫芦暗黑支持</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0152f7247a1" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: zhupeipei <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fe6e1a71764" target=_blank>[feat] 私人FM 桌面快捷方式和快捷入口进入需要刷新播放列表</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e1997dc1436" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/befa7de8cb6" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/460e2edebe4" target=_blank>Merge branch 'feat_mylisten_ui_opt' into Canary Signed-off-by: zhupeipei <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/974fb414528" target=_blank>Merge remote-tracking branch 'origin/Canary' into feat_mylisten_ui_opt</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/91cb6feb0b3" target=_blank>[feat] 我听糖葫芦打标添加</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1324f0bc0e5" target=_blank>Merge remote-tracking branch 'origin/Canary' into feat_mylisten_ui_opt</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/be3e9203ab8" target=_blank>[feat] 我听ui修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3e361c72a0f" target=_blank>[feat] catch rv的一个崩溃</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6ecbaa7a58c" target=_blank>[feat] 更新数展示修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f14c91308b7" target=_blank>[feat] 糖葫芦请求接口添加</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4c55be3dd43" target=_blank>[feat] 替换我听糖葫芦的默认图</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3a895d212b9" target=_blank>[feat] 替换我听糖葫芦的默认图</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3406ef62e9d" target=_blank>Merge remote-tracking branch 'origin/Canary' into feat_mylisten_ui_opt Signed-off-by: zhupeipei <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/75930158999" target=_blank>[feat] 添加订阅样式修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/af0454c83df" target=_blank>[feat] 我听改版ui修改</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d937ff5e630" target=_blank>[feat] 我听直播条只展示更多问题fix</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2db4b4c8265" target=_blank>[feat] 我听订阅ui改版</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/98a818682a2" target=_blank>Merge remote-tracking branch 'origin/Canary' into feat_mylisten_ui_opt Signed-off-by: zhupeipei <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/54c3e4f70d2" target=_blank>[feat] 我听订阅ui改版</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1f0a5ac24d1" target=_blank>[feat] 我听订阅ui改版</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/190c39e2fd4" target=_blank>[feat] 我听ui改版 听更新和电子书tab</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e35f0afcc25" target=_blank>[feat] 我听ui改版 暗黑模式适配</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1a0df527637" target=_blank>[feat] 我听ui改版</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a099362bf8f" target=_blank>[feat] 置顶背景色 先干掉</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/770f744c70f" target=_blank>[feat] 糖葫芦ui调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/97ad396315f" target=_blank>[feat] 糖葫芦调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2e8b2b30372" target=_blank>[feat] 我听头部背景调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4e09f3f3539" target=_blank>[feat] 去掉皮肤包的逻辑</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/dfd93f32254" target=_blank>[feat] 我听直播条优化</a></li>
</ul>
</body></html>
