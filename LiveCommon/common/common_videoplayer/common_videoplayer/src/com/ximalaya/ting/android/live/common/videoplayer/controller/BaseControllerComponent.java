package com.ximalaya.ting.android.live.common.videoplayer.controller;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.common.R;
import com.ximalaya.ting.android.live.common.lib.base.constants.CdnStatus;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.common.view.ScaleRelativeLayout;


/**
 * Created by duruochen on 2020/4/27.
 */
public abstract class BaseControllerComponent extends RelativeLayout implements IBaseControllerComponent, View.OnClickListener, ScaleRelativeLayout.DispatchTouchEventListener {

    /**
     * 当前拉流分辨率
     */
    protected PlayerConstants.ResolutionRatio mCurrentReolutionRatio;

    /**
     * 当前视频播放类型
     */
    protected int mCurrentPlayType;
    protected IOnBaseComponentListener mControllerCompListener;

    protected RelativeLayout mLayoutLoading;
    protected View mLoadingView;

    protected TextView mLoadingTipTv;
    protected View mCourseLoadingView;
    protected TextView mTvFinish;
    protected RelativeLayout mFinishLayout;
    protected String mFinishText;

    protected View mFailLayout;
    protected int mBizType;

    protected static final int HIDE_INTERVAL = 5000; // 隐藏控件的时间间隔
    protected long mHideInterval = HIDE_INTERVAL;

    protected IControllerCallback mControllerCallback;    // 播放控件回调接口

    protected ScaleRelativeLayout mScaleRelativeLayout;

    protected @CdnStatus
    int mCdnStatus;

    /**
     * 是否正在准备资源
     */
    protected boolean isPreparing;

    public BaseControllerComponent(Context context) {
        this(context, null);
    }

    public BaseControllerComponent(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BaseControllerComponent(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    abstract protected int getLayoutId();

    private void initView(Context context) {
        if (getLayoutId() > 0) LayoutInflater.from(context).inflate(getLayoutId(), this);
        mLayoutLoading = (RelativeLayout) LayoutInflater.from(context).inflate(R.layout.live_video_view_loading, null);
        addView(mLayoutLoading, 0, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));

        mLoadingView = findViewById(R.id.live_video_loading_view);
        mLoadingTipTv = findViewById(R.id.live_video_tv_tips);
        mFinishLayout = findViewById(R.id.live_video_finish_rl);
        mTvFinish = findViewById(R.id.live_video_finish_tv);

        mCourseLoadingView = findViewById(R.id.live_video_course_loading);
        mCourseLoadingView.setVisibility(GONE);

        initMyUi(context);
    }


    protected void initMyUi(Context context) {
    }


    public void setBizType(int bizType) {
        this.mBizType = bizType;
    }

    @Override
    public void setLoadingComponentListener(IOnBaseComponentListener loadingComponentListener) {
        this.mControllerCompListener = loadingComponentListener;
    }

    @Override
    public void showPreparing(boolean isFirst) {
        mFinishText = "";
        isPreparing = true;
        showLoading();
    }

    @Override
    public void hidePreparing() {
        isPreparing = false;
        hideLoading();
    }

    @Override
    public void showLoading() {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                ViewStatusUtil.setVisible(VISIBLE, mLoadingView, mLayoutLoading);
                ViewStatusUtil.setVisible(GONE, mTvFinish, mFailLayout, mFinishLayout, mCourseLoadingView);
                if (mControllerCallback != null) {
                    mControllerCallback.setPlayerFailVisible(false);
                }
            }
        });
    }

    @Override
    public void hideLoading() {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                ViewStatusUtil.setVisible(GONE, mLayoutLoading);
            }
        });
    }

    @Override
    public void playFinished() {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                mLayoutLoading.setVisibility(VISIBLE);
                if (mBizType != BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE) {
                    ViewStatusUtil.setVisible(VISIBLE, mFinishLayout);
                    ViewStatusUtil.setVisible(GONE, mLoadingView, mLoadingTipTv, mFailLayout, mTvFinish);
                    if (mControllerCallback != null) {
                        mControllerCallback.setPlayerFailVisible(false);
                    }
                }

            }
        });
    }


    @Override
    public void playError() {
        if (mCdnStatus == CdnStatus.CDN_STATUS_STOP) {
            return;
        }
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                mLayoutLoading.setVisibility(VISIBLE);
                ViewStatusUtil.setVisible(VISIBLE, mFailLayout);
                if (mControllerCallback != null) {
                    mControllerCallback.setPlayerFailVisible(true);
                }
                ViewStatusUtil.setVisible(GONE, mTvFinish, mLoadingView, mLoadingTipTv, mFinishLayout);
            }
        });
    }

    @Override
    public void setResolutionRatio(PlayerConstants.ResolutionRatio resolutionRatio) {
        if (resolutionRatio == null) {
            return;
        }
        mCurrentReolutionRatio = resolutionRatio;
        switch (resolutionRatio) {
            case LANDSCAPE_16_9:
            case LANDSCAPE_4_3:
            case LANDSCAPE:
            case LANDSCAPE_16_9_FOLD:
                RelativeLayout.LayoutParams lParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                lParams.addRule(CENTER_IN_PARENT);
                mTvFinish.setLayoutParams(lParams);
                break;
            case PORTRAIT:
            case PORTRAIT_TOP:
            case PORTRAIT_CENTER:
            case PORTRAIT_FOLD:
                RelativeLayout.LayoutParams pParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                pParams.addRule(CENTER_HORIZONTAL);
                pParams.topMargin = BaseUtil.dp2px(getContext(), 286);
                mTvFinish.setLayoutParams(pParams);
                break;

        }
    }

    @Override
    public void updatePlayType(int playType) {
        mCurrentPlayType = playType;
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
    }

    @Override
    public void changeClearScreenStatus(boolean isClear) {

    }

    @Override
    public void removeFromParent() {
        if (getLayoutRootView() == null) {
            return;
        }
        ViewParent viewParent = getLayoutRootView().getParent();
        if (!(viewParent instanceof ViewGroup)) {
            return;
        }
        ((ViewGroup) viewParent).removeView(getLayoutRootView());
    }

    @Override
    public void onDispatchTouchEvent(MotionEvent event) {
    }

    @Override
    public void setCallback(IControllerCallback callback) {
        mControllerCallback = callback;
    }

    @Override
    public void updateCdnStatus(@CdnStatus int cdnStatus) {
        mCdnStatus = cdnStatus;
        if (cdnStatus == CdnStatus.CDN_STATUS_STOP) {
            ViewStatusUtil.setVisible(VISIBLE, mLoadingView, mLoadingTipTv, mLayoutLoading);
            ViewStatusUtil.setVisible(GONE, mTvFinish, mFailLayout, mFinishLayout, mCourseLoadingView);
            if (mControllerCallback != null) {
                mControllerCallback.setPlayerFailVisible(false);
            }
        } else if (cdnStatus == CdnStatus.CDN_STATUS_START) {
            ViewStatusUtil.setVisible(GONE, mLoadingView, mLoadingTipTv, mLayoutLoading);
        }
    }

}
