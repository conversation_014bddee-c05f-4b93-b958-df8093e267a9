// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: Users/lewis/project/live/LiveBundle/LiveCommon/common/common_proto/pb/PK.Base.proto at 314:1
package PK.Base;

import com.squareup.wire.FieldEncoding;
import com.squareup.wire.Message;
import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.ProtoReader;
import com.squareup.wire.ProtoWriter;
import com.squareup.wire.WireField;
import com.squareup.wire.internal.Internal;
import java.io.IOException;
import java.lang.Boolean;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import okio.ByteString;

public final class PkReport extends Message<PkReport, PkReport.Builder> {
  public static final ProtoAdapter<PkReport> ADAPTER = new ProtoAdapter_PkReport();

  private static final long serialVersionUID = 0L;

  public static final Boolean DEFAULT_ISSHOWRESULTEFFECT = false;

  public static final String DEFAULT_VIPINTEGRALS = "";

  public static final String DEFAULT_BYECONTENT = "";

  @WireField(
      tag = 1,
      adapter = "com.squareup.wire.ProtoAdapter#BOOL",
      label = WireField.Label.REQUIRED
  )
  public final Boolean isShowResultEffect;

  @WireField(
      tag = 2,
      adapter = "PK.Base.UserInfo#ADAPTER"
  )
  public final UserInfo vipUser;

  @WireField(
      tag = 3,
      adapter = "com.squareup.wire.ProtoAdapter#STRING"
  )
  public final String vipIntegrals;

  @WireField(
      tag = 4,
      adapter = "com.squareup.wire.ProtoAdapter#STRING"
  )
  public final String byeContent;

  public PkReport(Boolean isShowResultEffect, UserInfo vipUser, String vipIntegrals, String byeContent) {
    this(isShowResultEffect, vipUser, vipIntegrals, byeContent, ByteString.EMPTY);
  }

  public PkReport(Boolean isShowResultEffect, UserInfo vipUser, String vipIntegrals, String byeContent, ByteString unknownFields) {
    super(ADAPTER, unknownFields);
    this.isShowResultEffect = isShowResultEffect;
    this.vipUser = vipUser;
    this.vipIntegrals = vipIntegrals;
    this.byeContent = byeContent;
  }

  @Override
  public Builder newBuilder() {
    Builder builder = new Builder();
    builder.isShowResultEffect = isShowResultEffect;
    builder.vipUser = vipUser;
    builder.vipIntegrals = vipIntegrals;
    builder.byeContent = byeContent;
    builder.addUnknownFields(unknownFields());
    return builder;
  }

  @Override
  public boolean equals(Object other) {
    if (other == this) return true;
    if (!(other instanceof PkReport)) return false;
    PkReport o = (PkReport) other;
    return unknownFields().equals(o.unknownFields())
        && isShowResultEffect.equals(o.isShowResultEffect)
        && Internal.equals(vipUser, o.vipUser)
        && Internal.equals(vipIntegrals, o.vipIntegrals)
        && Internal.equals(byeContent, o.byeContent);
  }

  @Override
  public int hashCode() {
    int result = super.hashCode;
    if (result == 0) {
      result = unknownFields().hashCode();
      result = result * 37 + isShowResultEffect.hashCode();
      result = result * 37 + (vipUser != null ? vipUser.hashCode() : 0);
      result = result * 37 + (vipIntegrals != null ? vipIntegrals.hashCode() : 0);
      result = result * 37 + (byeContent != null ? byeContent.hashCode() : 0);
      super.hashCode = result;
    }
    return result;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append(", isShowResultEffect=").append(isShowResultEffect);
    if (vipUser != null) builder.append(", vipUser=").append(vipUser);
    if (vipIntegrals != null) builder.append(", vipIntegrals=").append(vipIntegrals);
    if (byeContent != null) builder.append(", byeContent=").append(byeContent);
    return builder.replace(0, 2, "PkReport{").append('}').toString();
  }

  public static final class Builder extends Message.Builder<PkReport, Builder> {
    public Boolean isShowResultEffect;

    public UserInfo vipUser;

    public String vipIntegrals;

    public String byeContent;

    public Builder() {
    }

    public Builder isShowResultEffect(Boolean isShowResultEffect) {
      this.isShowResultEffect = isShowResultEffect;
      return this;
    }

    public Builder vipUser(UserInfo vipUser) {
      this.vipUser = vipUser;
      return this;
    }

    public Builder vipIntegrals(String vipIntegrals) {
      this.vipIntegrals = vipIntegrals;
      return this;
    }

    public Builder byeContent(String byeContent) {
      this.byeContent = byeContent;
      return this;
    }

    @Override
    public PkReport build() {
      if (isShowResultEffect == null) {
        throw Internal.missingRequiredFields(isShowResultEffect, "isShowResultEffect");
      }
      return new PkReport(isShowResultEffect, vipUser, vipIntegrals, byeContent, super.buildUnknownFields());
    }
  }

  private static final class ProtoAdapter_PkReport extends ProtoAdapter<PkReport> {
    ProtoAdapter_PkReport() {
      super(FieldEncoding.LENGTH_DELIMITED, PkReport.class);
    }

    @Override
    public int encodedSize(PkReport value) {
      return ProtoAdapter.BOOL.encodedSizeWithTag(1, value.isShowResultEffect)
          + (value.vipUser != null ? UserInfo.ADAPTER.encodedSizeWithTag(2, value.vipUser) : 0)
          + (value.vipIntegrals != null ? ProtoAdapter.STRING.encodedSizeWithTag(3, value.vipIntegrals) : 0)
          + (value.byeContent != null ? ProtoAdapter.STRING.encodedSizeWithTag(4, value.byeContent) : 0)
          + value.unknownFields().size();
    }

    @Override
    public void encode(ProtoWriter writer, PkReport value) throws IOException {
      ProtoAdapter.BOOL.encodeWithTag(writer, 1, value.isShowResultEffect);
      if (value.vipUser != null) UserInfo.ADAPTER.encodeWithTag(writer, 2, value.vipUser);
      if (value.vipIntegrals != null) ProtoAdapter.STRING.encodeWithTag(writer, 3, value.vipIntegrals);
      if (value.byeContent != null) ProtoAdapter.STRING.encodeWithTag(writer, 4, value.byeContent);
      writer.writeBytes(value.unknownFields());
    }

    @Override
    public PkReport decode(ProtoReader reader) throws IOException {
      Builder builder = new Builder();
      long token = reader.beginMessage();
      for (int tag; (tag = reader.nextTag()) != -1;) {
        switch (tag) {
          case 1: builder.isShowResultEffect(ProtoAdapter.BOOL.decode(reader)); break;
          case 2: builder.vipUser(UserInfo.ADAPTER.decode(reader)); break;
          case 3: builder.vipIntegrals(ProtoAdapter.STRING.decode(reader)); break;
          case 4: builder.byeContent(ProtoAdapter.STRING.decode(reader)); break;
          default: {
            FieldEncoding fieldEncoding = reader.peekFieldEncoding();
            Object value = fieldEncoding.rawProtoAdapter().decode(reader);
            builder.addUnknownField(tag, fieldEncoding, value);
          }
        }
      }
      reader.endMessage(token);
      return builder.build();
    }

    @Override
    public PkReport redact(PkReport value) {
      Builder builder = value.newBuilder();
      if (builder.vipUser != null) builder.vipUser = UserInfo.ADAPTER.redact(builder.vipUser);
      builder.clearUnknownFields();
      return builder.build();
    }
  }
}
