// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: Users/lewis/project/live/LiveBundle/LiveCommon/common/common_proto/pb/PK.XChat.proto at 472:1
package PK.XChat;

import com.squareup.wire.FieldEncoding;
import com.squareup.wire.Message;
import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.ProtoReader;
import com.squareup.wire.ProtoWriter;
import com.squareup.wire.WireField;
import com.squareup.wire.internal.Internal;
import java.io.IOException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import okio.ByteString;

public final class MicPkStopReq extends Message<MicPkStopReq, MicPkStopReq.Builder> {
  public static final ProtoAdapter<MicPkStopReq> ADAPTER = new ProtoAdapter_MicPkStopReq();

  private static final long serialVersionUID = 0L;

  public static final Long DEFAULT_UNIQUEID = 0L;

  @WireField(
      tag = 1,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64"
  )
  public final Long uniqueId;

  public MicPkStopReq(Long uniqueId) {
    this(uniqueId, ByteString.EMPTY);
  }

  public MicPkStopReq(Long uniqueId, ByteString unknownFields) {
    super(ADAPTER, unknownFields);
    this.uniqueId = uniqueId;
  }

  @Override
  public Builder newBuilder() {
    Builder builder = new Builder();
    builder.uniqueId = uniqueId;
    builder.addUnknownFields(unknownFields());
    return builder;
  }

  @Override
  public boolean equals(Object other) {
    if (other == this) return true;
    if (!(other instanceof MicPkStopReq)) return false;
    MicPkStopReq o = (MicPkStopReq) other;
    return unknownFields().equals(o.unknownFields())
        && Internal.equals(uniqueId, o.uniqueId);
  }

  @Override
  public int hashCode() {
    int result = super.hashCode;
    if (result == 0) {
      result = unknownFields().hashCode();
      result = result * 37 + (uniqueId != null ? uniqueId.hashCode() : 0);
      super.hashCode = result;
    }
    return result;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    if (uniqueId != null) builder.append(", uniqueId=").append(uniqueId);
    return builder.replace(0, 2, "MicPkStopReq{").append('}').toString();
  }

  public static final class Builder extends Message.Builder<MicPkStopReq, Builder> {
    public Long uniqueId;

    public Builder() {
    }

    public Builder uniqueId(Long uniqueId) {
      this.uniqueId = uniqueId;
      return this;
    }

    @Override
    public MicPkStopReq build() {
      return new MicPkStopReq(uniqueId, super.buildUnknownFields());
    }
  }

  private static final class ProtoAdapter_MicPkStopReq extends ProtoAdapter<MicPkStopReq> {
    ProtoAdapter_MicPkStopReq() {
      super(FieldEncoding.LENGTH_DELIMITED, MicPkStopReq.class);
    }

    @Override
    public int encodedSize(MicPkStopReq value) {
      return (value.uniqueId != null ? ProtoAdapter.UINT64.encodedSizeWithTag(1, value.uniqueId) : 0)
          + value.unknownFields().size();
    }

    @Override
    public void encode(ProtoWriter writer, MicPkStopReq value) throws IOException {
      if (value.uniqueId != null) ProtoAdapter.UINT64.encodeWithTag(writer, 1, value.uniqueId);
      writer.writeBytes(value.unknownFields());
    }

    @Override
    public MicPkStopReq decode(ProtoReader reader) throws IOException {
      Builder builder = new Builder();
      long token = reader.beginMessage();
      for (int tag; (tag = reader.nextTag()) != -1;) {
        switch (tag) {
          case 1: builder.uniqueId(ProtoAdapter.UINT64.decode(reader)); break;
          default: {
            FieldEncoding fieldEncoding = reader.peekFieldEncoding();
            Object value = fieldEncoding.rawProtoAdapter().decode(reader);
            builder.addUnknownField(tag, fieldEncoding, value);
          }
        }
      }
      reader.endMessage(token);
      return builder.build();
    }

    @Override
    public MicPkStopReq redact(MicPkStopReq value) {
      Builder builder = value.newBuilder();
      builder.clearUnknownFields();
      return builder.build();
    }
  }
}
