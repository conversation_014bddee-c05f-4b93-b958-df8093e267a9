// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: Users/lewis/project/live/LiveBundle/LiveCommon/common/common_proto/pb/ENT.XChat.proto at 414:1
package ENT.XChat;

import ENT.Base.UserType;
import RM.Base.MuteType;
import RM.Base.SdkInfo;
import RM.Base.UserStatus;
import com.squareup.wire.FieldEncoding;
import com.squareup.wire.Message;
import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.ProtoReader;
import com.squareup.wire.ProtoWriter;
import com.squareup.wire.WireField;
import com.squareup.wire.internal.Internal;
import java.io.IOException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import okio.ByteString;

public final class FastConnectRsp extends Message<FastConnectRsp, FastConnectRsp.Builder> {
  public static final ProtoAdapter<FastConnectRsp> ADAPTER = new ProtoAdapter_FastConnectRsp();

  private static final long serialVersionUID = 0L;

  public static final Integer DEFAULT_RESULTCODE = 0;

  public static final String DEFAULT_REASON = "";

  public static final UserStatus DEFAULT_STATUS = UserStatus.USER_STATUS_OFFLINE;

  public static final MuteType DEFAULT_MUTETYPE = MuteType.MUTE_TYPE_UNMUTE;

  public static final UserType DEFAULT_USERTYPE = UserType.USER_TYPE_MICUSER;

  public static final Integer DEFAULT_MICNO = 0;

  public static final Long DEFAULT_TIMESTAMP = 0L;

  public static final Long DEFAULT_UNIQUEID = 0L;

  @WireField(
      tag = 1,
      adapter = "com.squareup.wire.ProtoAdapter#INT32",
      label = WireField.Label.REQUIRED
  )
  public final Integer resultCode;

  @WireField(
      tag = 2,
      adapter = "com.squareup.wire.ProtoAdapter#STRING"
  )
  public final String reason;

  @WireField(
      tag = 3,
      adapter = "RM.Base.SdkInfo#ADAPTER"
  )
  public final SdkInfo sdkInfo;

  @WireField(
      tag = 4,
      adapter = "RM.Base.UserStatus#ADAPTER"
  )
  public final UserStatus status;

  @WireField(
      tag = 5,
      adapter = "RM.Base.MuteType#ADAPTER"
  )
  public final MuteType muteType;

  @WireField(
      tag = 6,
      adapter = "ENT.Base.UserType#ADAPTER"
  )
  public final UserType userType;

  @WireField(
      tag = 7,
      adapter = "com.squareup.wire.ProtoAdapter#INT32"
  )
  public final Integer micNo;

  @WireField(
      tag = 8,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64"
  )
  public final Long timestamp;

  @WireField(
      tag = 9,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64"
  )
  public final Long uniqueId;

  public FastConnectRsp(Integer resultCode, String reason, SdkInfo sdkInfo, UserStatus status, MuteType muteType, UserType userType, Integer micNo, Long timestamp, Long uniqueId) {
    this(resultCode, reason, sdkInfo, status, muteType, userType, micNo, timestamp, uniqueId, ByteString.EMPTY);
  }

  public FastConnectRsp(Integer resultCode, String reason, SdkInfo sdkInfo, UserStatus status, MuteType muteType, UserType userType, Integer micNo, Long timestamp, Long uniqueId, ByteString unknownFields) {
    super(ADAPTER, unknownFields);
    this.resultCode = resultCode;
    this.reason = reason;
    this.sdkInfo = sdkInfo;
    this.status = status;
    this.muteType = muteType;
    this.userType = userType;
    this.micNo = micNo;
    this.timestamp = timestamp;
    this.uniqueId = uniqueId;
  }

  @Override
  public Builder newBuilder() {
    Builder builder = new Builder();
    builder.resultCode = resultCode;
    builder.reason = reason;
    builder.sdkInfo = sdkInfo;
    builder.status = status;
    builder.muteType = muteType;
    builder.userType = userType;
    builder.micNo = micNo;
    builder.timestamp = timestamp;
    builder.uniqueId = uniqueId;
    builder.addUnknownFields(unknownFields());
    return builder;
  }

  @Override
  public boolean equals(Object other) {
    if (other == this) return true;
    if (!(other instanceof FastConnectRsp)) return false;
    FastConnectRsp o = (FastConnectRsp) other;
    return unknownFields().equals(o.unknownFields())
        && resultCode.equals(o.resultCode)
        && Internal.equals(reason, o.reason)
        && Internal.equals(sdkInfo, o.sdkInfo)
        && Internal.equals(status, o.status)
        && Internal.equals(muteType, o.muteType)
        && Internal.equals(userType, o.userType)
        && Internal.equals(micNo, o.micNo)
        && Internal.equals(timestamp, o.timestamp)
        && Internal.equals(uniqueId, o.uniqueId);
  }

  @Override
  public int hashCode() {
    int result = super.hashCode;
    if (result == 0) {
      result = unknownFields().hashCode();
      result = result * 37 + resultCode.hashCode();
      result = result * 37 + (reason != null ? reason.hashCode() : 0);
      result = result * 37 + (sdkInfo != null ? sdkInfo.hashCode() : 0);
      result = result * 37 + (status != null ? status.hashCode() : 0);
      result = result * 37 + (muteType != null ? muteType.hashCode() : 0);
      result = result * 37 + (userType != null ? userType.hashCode() : 0);
      result = result * 37 + (micNo != null ? micNo.hashCode() : 0);
      result = result * 37 + (timestamp != null ? timestamp.hashCode() : 0);
      result = result * 37 + (uniqueId != null ? uniqueId.hashCode() : 0);
      super.hashCode = result;
    }
    return result;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append(", resultCode=").append(resultCode);
    if (reason != null) builder.append(", reason=").append(reason);
    if (sdkInfo != null) builder.append(", sdkInfo=").append(sdkInfo);
    if (status != null) builder.append(", status=").append(status);
    if (muteType != null) builder.append(", muteType=").append(muteType);
    if (userType != null) builder.append(", userType=").append(userType);
    if (micNo != null) builder.append(", micNo=").append(micNo);
    if (timestamp != null) builder.append(", timestamp=").append(timestamp);
    if (uniqueId != null) builder.append(", uniqueId=").append(uniqueId);
    return builder.replace(0, 2, "FastConnectRsp{").append('}').toString();
  }

  public static final class Builder extends Message.Builder<FastConnectRsp, Builder> {
    public Integer resultCode;

    public String reason;

    public SdkInfo sdkInfo;

    public UserStatus status;

    public MuteType muteType;

    public UserType userType;

    public Integer micNo;

    public Long timestamp;

    public Long uniqueId;

    public Builder() {
    }

    public Builder resultCode(Integer resultCode) {
      this.resultCode = resultCode;
      return this;
    }

    public Builder reason(String reason) {
      this.reason = reason;
      return this;
    }

    public Builder sdkInfo(SdkInfo sdkInfo) {
      this.sdkInfo = sdkInfo;
      return this;
    }

    public Builder status(UserStatus status) {
      this.status = status;
      return this;
    }

    public Builder muteType(MuteType muteType) {
      this.muteType = muteType;
      return this;
    }

    public Builder userType(UserType userType) {
      this.userType = userType;
      return this;
    }

    public Builder micNo(Integer micNo) {
      this.micNo = micNo;
      return this;
    }

    public Builder timestamp(Long timestamp) {
      this.timestamp = timestamp;
      return this;
    }

    public Builder uniqueId(Long uniqueId) {
      this.uniqueId = uniqueId;
      return this;
    }

    @Override
    public FastConnectRsp build() {
      if (resultCode == null) {
        throw Internal.missingRequiredFields(resultCode, "resultCode");
      }
      return new FastConnectRsp(resultCode, reason, sdkInfo, status, muteType, userType, micNo, timestamp, uniqueId, super.buildUnknownFields());
    }
  }

  private static final class ProtoAdapter_FastConnectRsp extends ProtoAdapter<FastConnectRsp> {
    ProtoAdapter_FastConnectRsp() {
      super(FieldEncoding.LENGTH_DELIMITED, FastConnectRsp.class);
    }

    @Override
    public int encodedSize(FastConnectRsp value) {
      return ProtoAdapter.INT32.encodedSizeWithTag(1, value.resultCode)
          + (value.reason != null ? ProtoAdapter.STRING.encodedSizeWithTag(2, value.reason) : 0)
          + (value.sdkInfo != null ? SdkInfo.ADAPTER.encodedSizeWithTag(3, value.sdkInfo) : 0)
          + (value.status != null ? UserStatus.ADAPTER.encodedSizeWithTag(4, value.status) : 0)
          + (value.muteType != null ? MuteType.ADAPTER.encodedSizeWithTag(5, value.muteType) : 0)
          + (value.userType != null ? UserType.ADAPTER.encodedSizeWithTag(6, value.userType) : 0)
          + (value.micNo != null ? ProtoAdapter.INT32.encodedSizeWithTag(7, value.micNo) : 0)
          + (value.timestamp != null ? ProtoAdapter.UINT64.encodedSizeWithTag(8, value.timestamp) : 0)
          + (value.uniqueId != null ? ProtoAdapter.UINT64.encodedSizeWithTag(9, value.uniqueId) : 0)
          + value.unknownFields().size();
    }

    @Override
    public void encode(ProtoWriter writer, FastConnectRsp value) throws IOException {
      ProtoAdapter.INT32.encodeWithTag(writer, 1, value.resultCode);
      if (value.reason != null) ProtoAdapter.STRING.encodeWithTag(writer, 2, value.reason);
      if (value.sdkInfo != null) SdkInfo.ADAPTER.encodeWithTag(writer, 3, value.sdkInfo);
      if (value.status != null) UserStatus.ADAPTER.encodeWithTag(writer, 4, value.status);
      if (value.muteType != null) MuteType.ADAPTER.encodeWithTag(writer, 5, value.muteType);
      if (value.userType != null) UserType.ADAPTER.encodeWithTag(writer, 6, value.userType);
      if (value.micNo != null) ProtoAdapter.INT32.encodeWithTag(writer, 7, value.micNo);
      if (value.timestamp != null) ProtoAdapter.UINT64.encodeWithTag(writer, 8, value.timestamp);
      if (value.uniqueId != null) ProtoAdapter.UINT64.encodeWithTag(writer, 9, value.uniqueId);
      writer.writeBytes(value.unknownFields());
    }

    @Override
    public FastConnectRsp decode(ProtoReader reader) throws IOException {
      Builder builder = new Builder();
      long token = reader.beginMessage();
      for (int tag; (tag = reader.nextTag()) != -1;) {
        switch (tag) {
          case 1: builder.resultCode(ProtoAdapter.INT32.decode(reader)); break;
          case 2: builder.reason(ProtoAdapter.STRING.decode(reader)); break;
          case 3: builder.sdkInfo(SdkInfo.ADAPTER.decode(reader)); break;
          case 4: {
            try {
              builder.status(UserStatus.ADAPTER.decode(reader));
            } catch (ProtoAdapter.EnumConstantNotFoundException e) {
              builder.addUnknownField(tag, FieldEncoding.VARINT, (long) e.value);
            }
            break;
          }
          case 5: {
            try {
              builder.muteType(MuteType.ADAPTER.decode(reader));
            } catch (ProtoAdapter.EnumConstantNotFoundException e) {
              builder.addUnknownField(tag, FieldEncoding.VARINT, (long) e.value);
            }
            break;
          }
          case 6: {
            try {
              builder.userType(UserType.ADAPTER.decode(reader));
            } catch (ProtoAdapter.EnumConstantNotFoundException e) {
              builder.addUnknownField(tag, FieldEncoding.VARINT, (long) e.value);
            }
            break;
          }
          case 7: builder.micNo(ProtoAdapter.INT32.decode(reader)); break;
          case 8: builder.timestamp(ProtoAdapter.UINT64.decode(reader)); break;
          case 9: builder.uniqueId(ProtoAdapter.UINT64.decode(reader)); break;
          default: {
            FieldEncoding fieldEncoding = reader.peekFieldEncoding();
            Object value = fieldEncoding.rawProtoAdapter().decode(reader);
            builder.addUnknownField(tag, fieldEncoding, value);
          }
        }
      }
      reader.endMessage(token);
      return builder.build();
    }

    @Override
    public FastConnectRsp redact(FastConnectRsp value) {
      Builder builder = value.newBuilder();
      if (builder.sdkInfo != null) builder.sdkInfo = SdkInfo.ADAPTER.redact(builder.sdkInfo);
      builder.clearUnknownFields();
      return builder.build();
    }
  }
}
