// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: RM.Base.Ext.proto at 85:1
package RM.Base;

import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.WireEnum;
import java.lang.Override;

public enum ModeShowType implements WireEnum {
  SHOW_TYPE_ANCHOR(1),

  SHOW_TYPE_AUDIENCE(2);

  public static final ProtoAdapter<ModeShowType> ADAPTER = ProtoAdapter.newEnumAdapter(ModeShowType.class);

  private final int value;

  ModeShowType(int value) {
    this.value = value;
  }

  /**
   * Return the constant for {@code value} or null.
   */
  public static ModeShowType fromValue(int value) {
    switch (value) {
      case 1: return SHOW_TYPE_ANCHOR;
      case 2: return SHOW_TYPE_AUDIENCE;
      default: return null;
    }
  }

  @Override
  public int getValue() {
    return value;
  }
}
