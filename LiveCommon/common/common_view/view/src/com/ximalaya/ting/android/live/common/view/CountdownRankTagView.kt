package com.ximalaya.ting.android.live.common.view

import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.DrawableRes
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.live.common.R
import com.ximalaya.ting.android.live.common.lib.utils.gone
import com.ximalaya.ting.android.live.common.lib.utils.isVisible
import com.ximalaya.ting.android.live.common.lib.utils.show
import com.ximalaya.ting.android.live.common.lib.utils.updateLayoutParams

/**
 * desc: 榜单倒计时标签自定义 View
 *
 * 使用场景案例：小时榜单倒计时，提示当前第几名、还有多久结束
 *
 * Created by zoey on 2022/3/3.
 * <AUTHOR>
 * @email <EMAIL>
 */
class CountdownRankTagView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : LinearLayout(context, attrs, defStyle) {

    @DrawableRes
    private var tagBackground: Int = R.drawable.live_countdown_rank_tag_default_bg

    @DrawableRes
    private var countdownBackground: Int = R.drawable.live_countdown_rank_tag_countdown_default_bg

    private var countdownTv: TextView? = null
    private var countdownDescTv: TextView? = null

    init {
        initAttrs(attrs)
        inflateView()
    }

    private fun initAttrs(attrs: AttributeSet?) {
        if (attrs == null) return
        context.obtainStyledAttributes(attrs, R.styleable.CountdownRankTagView).also { ta ->
            tagBackground = ta.getResourceId(
                R.styleable.CountdownRankTagView_tagBackground,
                R.drawable.live_countdown_rank_tag_default_bg
            )
            countdownBackground = ta.getResourceId(
                R.styleable.CountdownRankTagView_countdownBackground,
                R.drawable.live_countdown_rank_tag_countdown_default_bg
            )
            ta.recycle()
        }
    }

    private fun inflateView() {
        LayoutInflater.from(context).inflate(
            R.layout.livecomm_countdown_rank_tag, this, true
        )
        countdownTv = findViewById(R.id.live_countdown_tv)
        countdownDescTv = findViewById(R.id.live_countdown_rank_desc_tv)

        setBackgroundResource(tagBackground)
        countdownTv?.setBackgroundResource(countdownBackground)
    }

    /**
     * 设置倒计时文案为 [countdownTxt]，eg：23:34
     */
    fun setCountdown(countdownTxt: String?) {
        if (countdownTxt.isNullOrBlank()) {
            countdownTv?.gone()
            background?.mutate()?.alpha = 255
            setCountdownDescMarginStart(8)
        } else {
            showCountdownTv()
            setCountdownDescMarginStart(4)
            countdownTv?.text = countdownTxt
            background?.mutate()?.alpha = 178
        }
    }

    /**
     * 动画展开倒计时 View
     */
    private fun showCountdownTv() {
        if (countdownTv?.isVisible() == true) return

        countdownTv?.show()
        countdownTv?.updateLayoutParams<LayoutParams> {
            width = 0
        }
        val countdownTvWidth = context.resources.getDimensionPixelSize(
            R.dimen.livecomm_countdown_rank_tag_width
        )
        ValueAnimator.ofInt(0, countdownTvWidth).also {
            it.duration = 500
            it.addUpdateListener { va ->
                (va.animatedValue as? Int)?.also { value ->
                    countdownTv?.updateLayoutParams<LayoutParams> {
                        width = value
                    }
                    invalidate()
                }
            }
            it.start()
        }
    }

    private fun setCountdownDescMarginStart(marginStartDp: Int) {
        countdownDescTv?.updateLayoutParams<LayoutParams> {
            marginStart = marginStartDp.dp
        }
    }

    /**
     * 设置倒计时描述文案为 [countdownDescTxt]，eg：小时榜第23名
     */
    fun setCountdownDesc(countdownDescTxt: String?) {
        if (countdownDescTxt == null) return
        countdownDescTv?.text = countdownDescTxt
    }
}