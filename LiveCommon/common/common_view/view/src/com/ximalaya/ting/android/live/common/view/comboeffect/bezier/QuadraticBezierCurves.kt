package com.ximalaya.ting.android.live.common.view.comboeffect.bezier

import android.graphics.Path
import android.graphics.PointF

/**
 * 二阶贝塞尔曲线，曲线由起始点 [startPoint]，终止点 [endPoint] 以及一个控制点 [controlPoint] 所定义
 *
 * Created by zoey on 2024/01/09.
 * <AUTHOR>
 * @email <EMAIL>
 */
class QuadraticBezierCurves(
    startPoint: PointF, endPoint: PointF, private val controlPoint: PointF
) : BezierCurves(startPoint, endPoint) {

    override val movePath: Path = Path().also {
        it.moveTo(startPoint.x, startPoint.y)
        it.quadTo(controlPoint.x, controlPoint.y, endPoint.x, endPoint.y)
    }

    override val debugMovePath: Path = Path().also {
        it.addCircle(startPoint.x, startPoint.y, 4f, Path.Direction.CW)
        it.addCircle(endPoint.x, endPoint.y, 4f, Path.Direction.CW)

        it.moveTo(startPoint.x, startPoint.y)
        it.quadTo(controlPoint.x, controlPoint.y, endPoint.x, endPoint.y)
    }

    override val debugControlPath: Path = Path().also {
        it.addCircle(controlPoint.x, controlPoint.y, 2f, Path.Direction.CW)

        it.moveTo(startPoint.x, startPoint.y)
        it.lineTo(controlPoint.x, controlPoint.y)
        it.lineTo(endPoint.x, endPoint.y)
    }
}