package com.ximalaya.ting.android.live.common.lib.base.mvi

import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant.LiveRoomBizType
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType
import com.ximalaya.ting.android.live.common.lib.entity.ILiveRoomDetail
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo
import com.ximalaya.ting.android.live.lifecycle.BizViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * ViewModel基类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15026804470
 * @wiki Wiki网址放在这里
 * @server 服务端开发人员放在这里
 * @since 2024/10/30
 */
abstract class LiveBaseViewModel<S : IUiState, E : IEffect> :
    BizViewModel(), ILiveBaseViewModel<S, E> {

    protected var hostData: ILiveRoomDetail? = null

    private val _uiStateFlow by lazy { MutableStateFlow(initUiState()) }
    override val uiStateFlow: StateFlow<S> = _uiStateFlow

    private val _effect = Channel<E>(Channel.BUFFERED)
    override val effect: Flow<E> = _effect.receiveAsFlow()

    private val _intent = Channel<IIntent>(Channel.UNLIMITED)

    init {
        handleIntents()
    }

    private fun handleIntents() {
        scope.launch {
            _intent.receiveAsFlow().collect { intent ->
                if (!isActive) return@collect
                handleIntent(intent)
            }
        }
    }

    override fun sendIntent(intent: IIntent) {
        scope.launch {
            _intent.send(intent)
        }
    }

    override fun handleIntent(intent: IIntent) {}

    protected abstract fun initUiState(): S

    protected fun sendUiState(copy: S.() -> S) {
        _uiStateFlow.update { copy(_uiStateFlow.value) }
    }

    protected suspend fun sendEffect(effect: E) {
        _effect.send(effect)
    }

    protected fun tryEmitEffect(effect: E): Boolean {
        return _effect.trySend(effect).isSuccess
    }

    override fun <T> bindData(hostData: T) {
        if (hostData is ILiveRoomDetail) {
            this.hostData = hostData
        }
    }

    override fun onCurrentUserInfoChange(currentLoginUserInfo: LiveUserInfo) {
    }

    @LiveMediaType
    override fun getLiveMediaType(): Int {
        return hostData?.getMediaType() ?: LiveMediaType.TYPE_AUDIO
    }

    override fun getRoomId(): Long {
        return hostData?.getRoomId() ?: 0
    }

    @LiveRoomBizType
    override fun getRoomBizType(): Int {
        return hostData?.getRoomBizType() ?: LiveRoomBizType.LIVE_TYPE_UNSET
    }

    override fun getHostUid(): Long {
        return hostData?.hostUid ?: 0
    }

    override fun getLiveId(): Long {
        return hostData?.liveId ?: 0
    }

    protected fun <T : Any> requestDataWithFlow(
        request: suspend () -> Flow<ResponseResult<T>>,
        successCallback: suspend (ResponseResult<T>) -> Unit,
        failCallback: suspend (ResponseResult<T>) -> Unit,
    ) {
        scope.launch {
            try {
                request().collect() { response ->
                    if (!isActive) {
                        failCallback(response)
                        return@collect
                    }
                    when (response) {
                        is ResponseResult.Success -> {
                            successCallback(response)
                        }
                        is ResponseResult.Failure -> {
                            failCallback(response)
                        }
                    }
                }

            } catch (e: Exception) {
                failCallback(ResponseResult.Failure(errMsg = e.message.toString()))
            }
        }
    }

    override fun resetData() {
        super.resetData()
        hostData = null
        sendUiState {
            initUiState()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        _effect.close()
        _intent.close()
    }

}