package com.ximalaya.ting.android.live.common.view

import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout.LayoutParams
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import com.ximalaya.ting.android.live.ad.view.webview.RoomH5BannerManager
import com.ximalaya.ting.android.live.common.view.cycleviewpager2.indicator.Indicator
import com.ximalaya.ting.android.live.common.view.viewpager.AutoScrollViewPager
import com.ximalaya.ting.android.live.common.view.viewpager.ILoopPagerAdapter

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18321019958
 * @wiki
 * @server
 * @since 2025/5/8
 */
interface ILoopRecyclerView {
    fun getView(): View
    fun getViewPager(): View?
    fun stopSwapAndSetToCurrentItem()
    fun startSwapViewPager()
    fun stopSwapViewPager()
    fun setEnableSingleFrameScroll(enable:Boolean)
    fun setImportantForAccessibility(mode: Int)
    fun setEnableAutoScroll(enable: Boolean)
    fun setSwapDuration(d:Int)
    fun setOffscreenPageLimit2(offscreenPageLimit: Int)
    fun setLayoutParams2(lp:ViewGroup.LayoutParams?)
    fun getLayoutParams2():ViewGroup.LayoutParams?
    fun getCurrentItem():Int
    fun getCurrentRealItem():Int
    fun setCurrentItem(position:Int,smooth:Boolean)
    fun setCurrentItemForDataPosition(position:Int,smooth:Boolean)
    fun setLoopPagerAdapter(adapter:ILoopPagerAdapter<AutoScrollViewPager.ViewPagerItem<*>>);
    fun setAdapter2(adapter:RecyclerView.Adapter<RecyclerView.ViewHolder>);
    fun addOnPageChangeListener(l:ViewPager.OnPageChangeListener)
    fun setPageMargin(m:Int)
    fun setClipChildren(c:Boolean)
    fun setIndicator(indicator: Indicator?,indicatorLayoutParams: LayoutParams?)
    fun notifyDataSetChanged()
}