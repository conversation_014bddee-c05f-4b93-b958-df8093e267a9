package com.ximalaya.ting.android.live.common.input.request

import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.manager.request.CommonRequestM.baseGetRequest
import com.ximalaya.ting.android.host.util.GsonUtils
import com.ximalaya.ting.android.live.common.input.model.LiveUserBulletBean
import com.ximalaya.ting.android.live.common.lib.base.request.LiveUrlConstants
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONObject

/**
 * Created by zhangying on 2022/4/7.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18862631722
 */
class CommonRequestForLiveBullet : CommonRequestM() {

    companion object {
        const val TAG = "CommonRequestForLiveBullet"
    }

    /**
     * 获取弹幕模版
     */
    fun getUserBulletTemplate(
        params: Map<String, String>,
        callback: IDataCallBack<Map<String, LiveUserBulletBean?>>
    ) {
        baseGetRequest(LiveUrlConstants.getInstance().userBulletTemplateUrl, params, callback) {
            parseUserBulletTemplate(it)
        }
    }


    private fun parseUserBulletTemplate(json: String?): Map<String, LiveUserBulletBean?>? {
        Logger.i(
            TAG,
            "parseUserBulletTemplate = $json"
        )
        if (json.isNullOrBlank()) return null

        val bulletMap = mutableMapOf<String, LiveUserBulletBean?>()

        try {
            val jsonObject = JSONObject(json)
            if (jsonObject.optInt("ret") == 0) {
                val data = jsonObject.optString("data")
                if (data.isNullOrBlank()) return null
                val dataObject = JSONObject(data)
                if (dataObject.has("ordinary")) {
                    val ordinaryBullet = dataObject.optString("ordinary")
                    val ordinaryBulletList =
                        GsonUtils.parseJson(ordinaryBullet,LiveUserBulletBean::class.java)
                    bulletMap["ordinary"] = ordinaryBulletList
                }

                if (dataObject.has("fans")) {
                    val fansBullet = dataObject.optString("fans")
                    val fansBulletList =
                        GsonUtils.parseJson(fansBullet,LiveUserBulletBean::class.java)
                    bulletMap["fans"] = fansBulletList
                }

                if (dataObject.has("noble")) {
                    val nobleBullet = dataObject.optString("noble")
                    val nobleBulletList =
                        GsonUtils.parseJson(nobleBullet,LiveUserBulletBean::class.java)
                    bulletMap["noble"] = nobleBulletList
                }
            }
            return bulletMap
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }


}