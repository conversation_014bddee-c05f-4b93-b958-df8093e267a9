<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <declare-styleable name="TwoItemTab">
        <attr name="selectedTitleTextSize" format="integer" />
        <attr name="titleTextSize" format="integer" />
        <attr name="selectedTextColor" format="color" />
        <attr name="selectedTextBold" format="boolean" />
        <attr name="unSelectedTextColor" format="color" />
        <attr name="indicatorColor" format="color" />
        <attr name="indicatorMarginToBottom" format="dimension" />
        <attr name="indicatorHeight" format="dimension" />
        <attr name="textMarginToCenter" format="dimension" />
        <attr name="indicatorWidth" format="dimension" />
        <attr name="indicatorSpanExceedTitle" format="dimension" />
        <attr name="titleTextStyleIsBold" format="boolean" />
        <attr name="titleTextPadding" format="dimension" />
        <attr name="titleStretchEnable" format="boolean" />
    </declare-styleable>

    <declare-styleable name="LiveTitleLayout">
        <attr name="live_back_text" format="string" />
        <attr name="live_title_text" format="string" />
        <attr name="live_title_layout" format="reference" />
        <attr name="live_title_color" format="color" />
        <attr name="live_right_color" format="color" />
        <attr name="live_title_size" format="integer" />
        <attr name="live_right_size" format="integer" />
        <attr name="live_under_line_enable" format="boolean" />
        <attr name="live_right_background" format="color" />
        <attr name="live_back_drawable" format="reference" />
        <attr name="live_right_drawable" format="reference" />
        <attr name="live_fix_status_bar" format="boolean" />
        <attr name="live_right_view" format="enum">
            <enum name="TEXT_VIEW" value="0" />
            <enum name="IMAGE_VIEW" value="1" />
        </attr>
        <attr name="live_back_view" format="enum">
            <enum name="TEXT_VIEW" value="0" />
            <enum name="IMAGE_VIEW" value="1" />
        </attr>
        <attr name="live_right_text" format="string" />
    </declare-styleable>

    <declare-styleable name="ClipCornerRelativeLayout">
        <attr name="live_root_corner_dimen" format="dimension" />
    </declare-styleable>

    <declare-styleable name="CounterTextView">
        <attr name="autoStart" format="boolean" />
        <attr name="counter_duration" format="string" />
        <attr name="prefix" format="string" />
        <attr name="suffix" format="string" />
        <attr name="live_counter_start_color" format="color" />
        <attr name="live_counter_end_color" format="color" />
        <attr name="enable_gradient" format="boolean" />
    </declare-styleable>

    <declare-styleable name="BannerView">
        <attr name="autoScroll" format="boolean"/>
        <attr name="autoScrollInterval" format="integer"/>
        <attr name="enableUserTouch" format="boolean"/>
        <attr name="pageSwitchTime" format="integer"/>
    </declare-styleable>

    <declare-styleable name="LiveVerticalSeekBar">
        <attr name="live_vs_progress_drawable" format="reference" />
        <attr name="live_vs_thumb_drawable" format="reference" />
    </declare-styleable>

    <declare-styleable name="LiveDefaultSoundWaveView" tools:ignore="ResourceName">
        <!-- 音浪颜色 -->
        <attr name="waveColorConfig" format="enum">
            <enum name="red" value="0" />
            <enum name="blue" value="1" />
        </attr>
        <!-- 头像大小，决定音浪圆初始动画的半径大小 -->
        <attr name="avatarSize" format="dimension" />
        <!-- 音浪圆大小扩大的倍数 -->
        <attr name="waveExpansionCoefficient" format="float" />
        <!-- 音浪峰值透明度 -->
        <attr name="wavePeakAlpha" format="float" />
        <!-- 圆线框宽度 -->
        <attr name="waveStrokeWidth" format="dimension" />
        <!-- 一个圆完整的动画时长，单位秒 -->
        <attr name="waveDuration" format="float" />
        <!-- 触发下一个圆动画的时长，单位秒 -->
        <attr name="waveTriggerDuration" format="float" />
    </declare-styleable>

    <declare-styleable name="LiveSoundWaveView" tools:ignore="ResourceName">
        <!-- 默认音浪大小，默认值为自定义 View 宽度 -->
        <attr name="defaultWaveSize" format="dimension" />
        <!-- 自定义音浪大小，默认值为自定义 View 宽度 -->
        <attr name="customWaveSize" format="dimension" />


        <!-- 重新定义引用 LiveDefaultSoundWaveView 自定义属性，XML 中才能智能提示 -->
        <!-- 音浪颜色 -->
        <attr name="waveColorConfig" />
        <!-- 头像大小，决定音浪圆初始动画的半径大小 -->
        <attr name="avatarSize" />
        <!-- 音浪圆大小扩大的倍数 -->
        <attr name="waveExpansionCoefficient" />
        <!-- 音浪峰值透明度 -->
        <attr name="wavePeakAlpha" />
        <!-- 圆线框宽度 -->
        <attr name="waveStrokeWidth" />
        <!-- 一个圆完整的动画时长，单位秒 -->
        <attr name="waveDuration" />
        <!-- 触发下一个圆动画的时长，单位秒 -->
        <attr name="waveTriggerDuration" />

        <!-- 重新定义引用 SVGAView 自定义属性，XML 中才能智能提示 -->
        <attr name="live_source" />
        <attr name="live_autoPlay" />
        <attr name="live_antiAlias" />
        <attr name="live_loopCount" />
        <attr name="live_clearsAfterStop" />
        <attr name="live_fillMode" />
    </declare-styleable>
    
    <declare-styleable name="GradientMaskView" tools:ignore="ResourceName">
        <attr name="maskStartColor" format="color" />
        <attr name="maskEndColor" format="color" />
        <attr name="maskGradientAngle" format="enum">
            <enum name="TOP_BOTTOM" value="1" />
            <enum name="TR_BL" value="2" />
            <enum name="RIGHT_LEFT" value="3" />
            <enum name="BR_TL" value="4" />
            <enum name="BOTTOM_TOP" value="5" />
            <enum name="BL_TR" value="6" />
            <enum name="LEFT_RIGHT" value="7" />
            <enum name="TL_BR" value="8" />
        </attr>
        <attr name="maskCornerRadius" format="dimension" />
        <attr name="maskCornerType" format="enum">
            <enum name="NULL" value="1" />
            <enum name="FULL" value="2" />
            <enum name="TOP_ONLY" value="3" />
            <enum name="BOTTOM_ONLY" value="4" />
            <enum name="LEFT_ONLY" value="5" />
            <enum name="RIGHT_ONLY" value="6" />
        </attr>
    </declare-styleable>
</resources>