package com.ximalaya.ting.android.live.common.view.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.annotation.ColorInt
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.live.common.R
import kotlin.math.roundToLong

/**
 * 直播间统一通用默认「麦位波/音浪」，默认为红色，当在 PK 场景中，另一方可设置为蓝色
 *
 * [设计规范](https://alidocs.dingtalk.com/i/nodes/jkB7yl4ZK3vV6Djjxrgv8PMX2O6oxqw0?dontjump=true)如下：
 *
 * - 音浪线宽：[DEFAULT_STROKE_WIDTH_DP] dp (支持自定义)
 * - 大小变化：半径在 [DEFAULT_ANIMATION_DURATION] 秒 (支持自定义) 内从 1 倍扩大到 [DEFAULT_EXPANSION_COEFFICIENT] 倍 (支持自定义)
 * - 透明度变化：透明度在 [DEFAULT_ANIMATION_DURATION] 秒 (支持自定义) 内从 0 到 [DEFAULT_PEAK_ALPHA] (支持自定义) 再到 0
 * - 动画递进：当第一个音浪运动到 [DEFAULT_ANIMATION_TRIGGER_DURATION] 秒 (支持自定义) 时，通知第二个音浪开始动画，以此类推
 * - 结束规则：音量小于 19 分贝，不再播放新的动画，当前正在播放的动画自然结束
 * - 自定义参数：一般情况下无须修改自定义参数，按设计规范即可，特殊情况可视情况设置
 *
 * 使用规范：
 * - 在 XML 中需要指明头像大小参数 [R.styleable.LiveDefaultSoundWaveView_avatarSize]，用于计算音浪动画的起始大小，在 JAVA 代码中，可通过 [setAvatarSize] 方法设置
 *
 * 注意：因为音浪扩散大小默认为头像的 [DEFAULT_EXPANSION_COEFFICIENT] 倍，请预留好视图控件的大小避免被裁剪，或者设置 clip 为 false
 *
 * Created by zoey on 2023/1/9.
 * <AUTHOR>
 * @email <EMAIL>
 */
class LiveDefaultSoundWaveView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : View(context, attrs, defStyle) {

    // 当前音浪颜色
    private var waveColor: WaveColor = WaveColor.RED

    // 初始动画时一个音浪的半径
    private var startCircleRadius: Float = 1.dpFloat

    // 默认音浪大小的扩大系数
    private var expansionCoefficient: Float = DEFAULT_EXPANSION_COEFFICIENT

    // 音浪线框宽度
    private var waveStrokeWidth: Float = DEFAULT_STROKE_WIDTH_DP.dpFloat

    // 一个音浪完整的动画时长，单位秒
    private var animationDuration: Float = DEFAULT_ANIMATION_DURATION

    // 一个音浪运动到中间时的峰值透明度
    private var wavePeakAlpha: Float = DEFAULT_PEAK_ALPHA

    // 触发下一个音浪动画的时长，单位秒
    private var animationTriggerDuration: Float = DEFAULT_ANIMATION_TRIGGER_DURATION

    // 音浪画笔
    private val wavePaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).also {
            it.style = Paint.Style.STROKE
            it.strokeWidth = waveStrokeWidth
            it.color = waveColor.color
            it.alpha = 0
        }
    }

    // 音浪队列
    private val waveList: MutableList<Wave> = ArrayList()

    // 触发式下，等待添加的新音浪
    private var waitForSpawn: Boolean = false

    // 开关式下，是否持续生产出新音浪
    private var continualSpawn: Boolean = false
    private val continualSpawnTask: Runnable = object : Runnable {
        override fun run() {
            if (continualSpawn) {
                val succeed = spawnWave()
                if (succeed.not()) {
                    postDelayed(this, 100L)
                } else {
                    postDelayed(this, (animationTriggerDuration * 1000).toLong())
                }
            }
        }
    }

    // 触发持续音浪的时间戳
    private var continualSpawnTriggerTs: Long = 0L
    private val continualSpawnSafetyValveTask: Runnable = Runnable {
        val gap = System.currentTimeMillis() - continualSpawnTriggerTs
        if (gap >= DEFAULT_CONTINUAL_SPAWN_SAFETY_VALVE_DURATION * 1000) {
            forceStop()
        }
    }

    init {
        val typeArray = context.obtainStyledAttributes(attrs, R.styleable.LiveDefaultSoundWaveView)
        waveColor = typeArray.getInt(
            R.styleable.LiveDefaultSoundWaveView_waveColorConfig, 0
        ).let {
            when (it) {
                0 -> WaveColor.RED
                1 -> WaveColor.BLUE
                else -> WaveColor.RED
            }
        }
        startCircleRadius = (typeArray.getDimensionPixelSize(
            R.styleable.LiveDefaultSoundWaveView_avatarSize, 0
        ) / 2F).coerceAtLeast(1.dpFloat)
        expansionCoefficient = typeArray.getFloat(
            R.styleable.LiveDefaultSoundWaveView_waveExpansionCoefficient,
            DEFAULT_EXPANSION_COEFFICIENT
        ).coerceAtLeast(1F)
        wavePeakAlpha = typeArray.getFloat(
            R.styleable.LiveDefaultSoundWaveView_wavePeakAlpha,
            DEFAULT_PEAK_ALPHA
        ).coerceAtLeast(0F)
        waveStrokeWidth = typeArray.getDimensionPixelSize(
            R.styleable.LiveDefaultSoundWaveView_waveStrokeWidth,
            DEFAULT_STROKE_WIDTH_DP.dp
        ).coerceAtLeast(0).toFloat()
        animationDuration = typeArray.getFloat(
            R.styleable.LiveDefaultSoundWaveView_waveDuration,
            DEFAULT_ANIMATION_DURATION
        ).coerceAtLeast(0F)
        animationTriggerDuration = typeArray.getFloat(
            R.styleable.LiveDefaultSoundWaveView_waveTriggerDuration,
            DEFAULT_ANIMATION_TRIGGER_DURATION
        ).coerceAtLeast(0F)
        typeArray.recycle()
    }

    /**
     * 设置当前头像大小
     */
    @Suppress("unused")
    fun setAvatarSize(avatarWidthDp: Float) {
        this.startCircleRadius = avatarWidthDp.dpFloat / 2
        invalidate()
    }

    /**
     * 设置当前音浪颜色为 [waveColor]
     */
    @Suppress("unused")
    fun setWaveColor(waveColor: WaveColor) {
        this.waveColor = waveColor
        wavePaint.color = waveColor.color
        invalidate()
    }

    /**
     * 设置音浪峰值透明度 [peakAlpha]
     */
    @Suppress("unused")
    fun setWavePeakAlpha(peakAlpha: Float) {
        this.wavePeakAlpha = peakAlpha
        invalidate()
    }

    /**
     * 设置当前音浪线宽为 [strokeWidthDp] dp
     */
    @Suppress("unused")
    fun setWaveStrokeWidth(strokeWidthDp: Float) {
        this.waveStrokeWidth = strokeWidthDp.dpFloat
        wavePaint.strokeWidth = waveStrokeWidth
        invalidate()
    }

    /**
     * 设置一个音浪动画的时长为 [durationSeconds] 秒
     */
    @Suppress("unused")
    fun setWaveAnimationDuration(durationSeconds: Float) {
        this.animationDuration = durationSeconds
        invalidate()
    }

    /**
     * 设置触发生成下一个音浪的间隔时长为 [durationSeconds] 秒
     */
    @Suppress("unused")
    fun setWaveAnimationTriggerDuration(durationSeconds: Float) {
        this.animationTriggerDuration = durationSeconds
        invalidate()
    }

    /**
     * 设置音浪从初始到动画结束，其大小所需的扩大倍数系数 [coefficient]
     */
    @Suppress("unused")
    fun setWaveExpansionCoefficient(coefficient: Float) {
        this.expansionCoefficient = coefficient
        invalidate()
    }

    /**
     * 当前检测到在说话，该方法为触发式的动画调用
     */
    @Suppress("unused")
    fun detectSpeaking() {
        spawnWave()
    }

    /**
     * 持续说话，该方法为开关式的动画调用，[needStopProtection] 为 false 时，停止说话时需手动调用 stop 方法
     *
     * 一般默认情况下，为防止出现业务侧调用该方法后意外地未再调用 stop 方法而导致音浪动画一直播放的问题，触发持续绘制后，会启动强制停止动画的安全自检任务
     */
    @JvmOverloads
    @Suppress("unused")
    fun continualSpeaking(needStopProtection: Boolean = true) {
        continualSpawnTriggerTs = System.currentTimeMillis()

        if (needStopProtection) {
            // 强制停止动画的安全自检任务
            removeCallbacks(continualSpawnSafetyValveTask)
            postDelayed(
                continualSpawnSafetyValveTask,
                (DEFAULT_CONTINUAL_SPAWN_SAFETY_VALVE_DURATION * 1000L).roundToLong()
            )
        }

        if (continualSpawn) return

        continualSpawn = true
        continualSpawnTask.run()
    }

    /**
     * 停止说话、终止动画方法
     */
    @Suppress("unused")
    fun forceStop() {
        waitForSpawn = false
        continualSpawn = false
        removeCallbacks(continualSpawnTask)
        removeCallbacks(continualSpawnSafetyValveTask)
    }

    /**
     * 生成一个新的音浪，返回是否成功添加
     */
    private fun spawnWave(): Boolean {
        return if (waveList.lastOrNull()?.canSpawn() != false) {
            waveList.add(Wave())
            invalidate()
            true
        } else {
            false
        }
    }

    /**
     * 计算获取所需扩大的半径距离
     */
    private fun getExpansionRadius(): Float {
        return (expansionCoefficient - 1F) * startCircleRadius
    }

    override fun onDraw(canvas: Canvas) {
        val iterator = waveList.iterator()
        while (iterator.hasNext()) {
            val wave = iterator.next()
            if (wave.isValid().not()) {
                iterator.remove()
                continue
            }

            canvas.save()
            wavePaint.alpha = wave.alpha
            canvas.drawCircle(width / 2F, height / 2F, wave.radius, wavePaint)
            canvas.restore()
        }
        if (waveList.isNotEmpty()) postInvalidateDelayed(10)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        forceStop()
    }

    enum class WaveColor(@ColorInt val color: Int) {
        RED(Color.parseColor("#FF4477")),
        BLUE(Color.parseColor("#1BBAD8"))
    }

    /**
     * 一个音浪圆的对象定义类，包含当前所需绘制信息
     */
    private inner class Wave {

        // 音浪初始创建时间戳
        private val birthTs = System.currentTimeMillis()

        // 当前绘制半径
        val radius: Float
            get() {
                val timePassed = (System.currentTimeMillis() - birthTs).toFloat()
                val progress = timePassed / (animationDuration * 1000)
                val increasedRadius = getExpansionRadius() * progress
                return startCircleRadius + increasedRadius
            }

        // 当前绘制透明度
        val alpha: Int
            get() {
                // 为了和 radius 时间戳保持一致，用 radius 算出当前位置
                val progress = (radius - startCircleRadius) / getExpansionRadius()
                return when {
                    progress <= 0.5F -> {
                        (510 * wavePeakAlpha * progress).toInt()
                    }
                    else -> {
                        ((-510 * wavePeakAlpha * progress) + 510 * wavePeakAlpha).toInt()
                    }
                }
            }

        /**
         * 是否过了触发下一个圆动画的时长
         */
        fun canSpawn(): Boolean {
            return (System.currentTimeMillis() - birthTs) >= (animationTriggerDuration * 1000)
        }

        /**
         * 当前圆生命周期是否有效，并且检查是否需要生成下一个圆
         */
        fun isValid(): Boolean {
            return (System.currentTimeMillis() - birthTs) < (animationDuration * 1000)
        }
    }

    companion object {
        // 默认音浪线宽 dp 值
        private const val DEFAULT_STROKE_WIDTH_DP = 2

        // 默认音浪动画时长，单位秒
        private const val DEFAULT_ANIMATION_DURATION = 1.4F

        // 默认触发下一个音浪动画时长，单位秒
        private const val DEFAULT_ANIMATION_TRIGGER_DURATION = 1F

        // 默认音浪大小扩大倍数系数
        private const val DEFAULT_EXPANSION_COEFFICIENT = 1.3F

        // 默认音浪峰值透明度
        private const val DEFAULT_PEAK_ALPHA = 1F

        // 触发持续音浪后却不调用 stop，安全自检停止动画的检测时长，单位秒
        private const val DEFAULT_CONTINUAL_SPAWN_SAFETY_VALVE_DURATION = 1F
    }
}