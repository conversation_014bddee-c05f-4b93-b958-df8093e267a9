package com.ximalaya.ting.android.live.common.view.dialog;

import android.content.Context;
import android.graphics.drawable.Drawable;
import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ListView;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.listener.OnListViewScrollListener;
import com.ximalaya.ting.android.host.listener.OnRecyclerViewScrollListener;
import com.ximalaya.ting.android.host.view.OnEdgeListenerScrollView;
import com.ximalaya.ting.android.host.view.layout.VerticalSlideRelativeLayout;
import com.ximalaya.ting.android.live.common.R;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * desc: 支持下拉消失的 dialog
 *
 * <AUTHOR> (zhangshixin)
 * @email <EMAIL>
 * @phoneNumber 18789440700
 * @since 2019/4/1.
 */
public abstract class AbsVerticalSlideDialog extends LiveBaseStateDialog implements
        VerticalSlideRelativeLayout.ISlideListener {

    protected VerticalSlideRelativeLayout mSlideRelativeLayout;
    private int height;
    /**
     * 自定义弹框背景
     */
    private int mBgResource;
    private Drawable mBgDrawable;

    public AbsVerticalSlideDialog(@NonNull Context context) {
        super(context);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.livecomm_dialog_vertical_slide_base;
    }

    @CallSuper
    @Override
    protected void initViews() {
        mSlideRelativeLayout = findViewById(R.id.live_vertical_slide);
        setSlideTopHeight(BaseUtil.dp2px(getContext(), 40));

        mSlideRelativeLayout.setSlideListen(this);

        if (mBgResource > 0) {
            mSlideRelativeLayout.setBackgroundResource(mBgResource);
        }

        if (mBgDrawable != null) {
            mSlideRelativeLayout.setBackground(mBgDrawable);
        }

        mSlideRelativeLayout.addView(getContentView());
    }

    protected abstract View getContentView();

    @Override
    public void onSlideOut() {
        if (mSlideRelativeLayout != null) {
            mSlideRelativeLayout.setScrollY(0);
        }
        UIStateUtil.hideViews(getDecorView());

        if (isShowing()) {
            dismiss();
        }
    }

    @Override
    protected void onStart() {
        UIStateUtil.showViews(getDecorView());
        super.onStart();
    }

    private View getDecorView() {
        return getWindow() != null ? getWindow().getDecorView() : null;
    }

    /**
     * 将内部滚动控件滚动事件与 {@link VerticalSlideRelativeLayout} 绑定在一起
     *
     * @param subScrollerView 内部滚动控件
     */
    public void bindSubScrollerView(View subScrollerView) {
        bindSubScrollerViewImpl(subScrollerView);
    }

    private void bindSubScrollerViewImpl(View scrollerView) {
        if (null == scrollerView) {
            Logger.e(new NullPointerException("scrollerView must be nonnull"));
            return;
        }

        // 不同的滚动控件需要各自实现滚动到顶部事件监听
        if (scrollerView instanceof ListView) {
            ListView listView = (ListView) scrollerView;
            OnListViewScrollListener onListViewScrollListener = new OnListViewScrollListener
                    (listView);
            listView.setOnScrollListener(onListViewScrollListener);
            onListViewScrollListener.bindOnSubScrollerScrollListener(mSlideRelativeLayout);
        } else if (scrollerView instanceof OnEdgeListenerScrollView) {
            OnEdgeListenerScrollView scrollView = (OnEdgeListenerScrollView) scrollerView;
            scrollView.bindOnSubScrollerScrollListener(mSlideRelativeLayout);
        } else if (scrollerView instanceof RecyclerView) {
            RecyclerView recyclerView = (RecyclerView) scrollerView;
            OnRecyclerViewScrollListener onRecyclerViewScrollListener =
                    new OnRecyclerViewScrollListener(recyclerView);
            recyclerView.addOnScrollListener(onRecyclerViewScrollListener);
            onRecyclerViewScrollListener.bindOnSubScrollerScrollListener(mSlideRelativeLayout);
        }
    }

    /**
     * 设置从顶部开始滑动时，顶部的区域高度
     *
     * @param topHeight
     */
    public void setSlideTopHeight(int topHeight) {
        if (topHeight < 0 && ConstantsOpenSdk.isDebug) {
            throw new IllegalArgumentException("topHeight 不能小于 0!");
        }
        if (mSlideRelativeLayout != null) {
            mSlideRelativeLayout.setTopHeight(topHeight);
        }
    }

    public void setOnlyScrollFromTop(boolean onlyScrollFromTop) {
        if (mSlideRelativeLayout != null) {
            mSlideRelativeLayout.setOnlyScrollFromTop(onlyScrollFromTop);
        }
    }

    public void setTopHeight(int height) {
        if (mSlideRelativeLayout != null) {
            mSlideRelativeLayout.setTopHeight(height);
        }
    }
}
