package com.ximalaya.ting.android.live.common.view.dialog;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.live.common.R;
import com.ximalaya.ting.android.live.common.dialog.base.LiveXmBaseDialog;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * 底部菜单弹窗
 *
 * <AUTHOR>
 **/
public class BottomMenuDialog extends LiveXmBaseDialog implements DialogInterface.OnShowListener {
    public interface ItemViewClickListener {
        void OnItemViewClick(View view, int position);
    }

    static class MenuAdapter extends HolderAdapter<ItemHolder> {
        private int rightRes;
        private ItemViewClickListener mItemClickListener;

        public void setItemClickListener(ItemViewClickListener mItemClickListener) {
            this.mItemClickListener = mItemClickListener;
        }

        public void setRightRes(int rightRes) {
            this.rightRes = rightRes;
        }

        @Override
        public void setListData(List<ItemHolder> data) {
            super.setListData(data);
        }

        public MenuAdapter(Context context, List<ItemHolder> listData, int rightRes) {
            super(context, listData);
            this.rightRes = rightRes;
        }

        @Override
        public void onClick(View view, ItemHolder itemHolder, int position, BaseViewHolder holder) {
            if (mItemClickListener != null) {
                mItemClickListener.OnItemViewClick(view, position);
            }
        }

        @Override
        public int getConvertViewId() {
            return R.layout.livecomm_item_bottom_memu;
        }

        @Override
        public BaseViewHolder buildHolder(View convertView) {
            ViewHolder holder = new ViewHolder();
            holder.title = (TextView) convertView
                    .findViewById(R.id.live_menu_item_title);
            holder.icon = (ImageView) convertView
                    .findViewById(R.id.live_menu_item_icon);
            holder.border = convertView
                    .findViewById(R.id.live_divider);
            holder.roundImageView = (RoundImageView) convertView.findViewById(R.id.live_menu_item_round_icon);
            holder.rightIcon = (ImageView) convertView.findViewById(R.id.live_menu_item_right_icon);
            return holder;
        }

        @Override
        public void bindViewDatas(BaseViewHolder holder, ItemHolder itemHolder, int position) {
            ViewHolder holder1 = (ViewHolder) holder;
            holder1.title.setText(itemHolder.title);
            if (itemHolder.iconRes != 0) {
                holder1.roundImageView.setVisibility(View.GONE);
                holder1.icon.setVisibility(View.VISIBLE);
                holder1.icon.setImageResource(listData.get(position).iconRes);
            } else {
                holder1.roundImageView.setVisibility(View.VISIBLE);
                holder1.icon.setVisibility(View.GONE);
                ImageManager.from(context).displayImage(holder1.roundImageView, listData.get(position).url, R.drawable.live_default_avatar_132,
                        BaseUtil.dp2px(context, 30), BaseUtil.dp2px(context, 30));
            }
            if (position == listData.size() - 1) {
                holder1.border.setVisibility(View.GONE);
            } else {
                holder1.border.setVisibility(View.VISIBLE);
            }
            if (rightRes == 0) {
                holder1.rightIcon.setVisibility(View.GONE);
            } else {
                holder1.rightIcon.setVisibility(View.VISIBLE);
                holder1.rightIcon.setImageResource(rightRes);
                setClickListener(holder1.rightIcon, itemHolder, position, holder1);
            }
        }
    }

    public static class ViewHolder extends HolderAdapter.BaseViewHolder {
        public ImageView icon;
        public RoundImageView roundImageView;
        public TextView title;
        public View border;
        public ImageView rightIcon;
    }

    public interface ExtraCallback {
        void execute(String caption, ViewHolder holder);
    }

    public static class ItemHolder {
        public String title;
        public int iconRes;
        public String url;
        public int id = -1;

        public ItemHolder(String title, int iconRes) {
            this.title = title;
            this.iconRes = iconRes;
        }

        public ItemHolder(int id, String title, int iconRes) {
            this.title = title;
            this.iconRes = iconRes;
            this.id = id;
        }

        public ItemHolder(String title, String url) {
            this.title = title;
            this.url = url;
        }

        @Override
        public String toString() {
            return "title = " + title + ",url = " + url + ",res = " + iconRes + ",id = " + id;
        }
    }

    private Activity mContext;
    private ListView mListView;
    private View mBtnClose;
    private List<ItemHolder> mSelections;
    private MenuAdapter mAdapter;
    private String mTitle = "请选择需要的操作";
    private OnItemClickListener mListener;
    private ExtraCallback mCallback;
    private boolean isPad = false;
    private int rightIconRes = 0;
    private ItemViewClickListener mItemViewClickListener;

    public BottomMenuDialog(Activity context, List<ItemHolder> titles,
                            OnItemClickListener listener) {
        super(context, com.ximalaya.ting.android.host.R.style.host_bottom_action_dialog);
        this.mContext = context;
        this.mSelections = titles;
        this.mListener = listener;
    }

    public BottomMenuDialog(Activity context, List<ItemHolder> titles, int rightIconRes,
                            OnItemClickListener listener) {
        super(context, com.ximalaya.ting.android.host.R.style.host_bottom_action_dialog);
        this.mContext = context;
        this.rightIconRes = rightIconRes;
        this.mSelections = titles;
        this.mListener = listener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.livecomm_dialog_bottom_menu);
        Window dialogWindow = getWindow();
        if (dialogWindow != null) {
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            dialogWindow.setGravity(Gravity.BOTTOM);
            dialogWindow.setAttributes(lp);
            dialogWindow.setWindowAnimations(R.style.host_bottom_slide_and_fade_animation);
        }
        mListView = (ListView) findViewById(R.id.live_listview);
        mBtnClose = findViewById(R.id.live_close_btn);
        mBtnClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        if (mSelections == null) mSelections = new ArrayList<>();
        mAdapter = new MenuAdapter(getContext(), mSelections, rightIconRes);
        mListView.setAdapter(mAdapter);
        if (mItemViewClickListener != null) {
            mAdapter.setItemClickListener(mItemViewClickListener);
        }
        setOnShowListener(this);
        AutoTraceHelper.bindData(mBtnClose, "", AutoTraceHelper.MODULE_DEFAULT);
    }

    public void setHeaderTitle(String title) {
        mTitle = title;
        if (isShowing()) {
            if (!TextUtils.isEmpty(mTitle)) {
                findViewById(R.id.live_title_tv).setVisibility(View.VISIBLE);
                ((TextView) findViewById(R.id.live_title_tv)).setText(mTitle);
            } else {
                findViewById(R.id.live_title_tv).setVisibility(View.GONE);
            }
        }
    }

    public void setRightIconRes(int rightIconRes) {
        this.rightIconRes = rightIconRes;
        if (mAdapter != null) {
            mAdapter.setRightRes(rightIconRes);
            if (isShowing()) {
                mAdapter.notifyDataSetChanged();
            }
        }
    }

    public void setListItemViewClickListener(ItemViewClickListener listener) {
        mItemViewClickListener = listener;
        if (mAdapter != null) {
            mAdapter.setItemClickListener(listener);
        }
    }

    public void setSelections(List<ItemHolder> selections) {
        this.mSelections = selections;
        if (null != mAdapter) {
            mAdapter.setListData(mSelections);
            fixListViewHeight();
            mAdapter.notifyDataSetChanged();
        }
    }

    public void notifyDataSetChanged() {
        if (null != mAdapter) {
            fixListViewHeight();
            mAdapter.notifyDataSetChanged();
        }
    }

    public List<ItemHolder> getSelections() {
        return mSelections;
    }

    public void setOnItemClickListener(AdapterView.OnItemClickListener l) {
        mListener = l;
    }

    public void setSelection(int index, ItemHolder selection) {
        if (mSelections == null || index < 0 || index > mSelections.size()) {
            return;
        }
        mSelections.set(index, selection);
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }

    private static final int MAX_ITEM_VISIBLE = 5;

    public void fixListViewHeight() {
        int listViewHeight = 0;
        int adaptCount = mSelections.size();
        if (adaptCount > MAX_ITEM_VISIBLE) {
            adaptCount = MAX_ITEM_VISIBLE;
        }
        listViewHeight = adaptCount * BaseUtil.dp2px(getContext(), 50);
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) this.mListView.getLayoutParams();
        layoutParams.width = RelativeLayout.LayoutParams.MATCH_PARENT;
        layoutParams.height = listViewHeight;
        mListView.setLayoutParams(layoutParams);
    }

    @Override
    public void show() {
        super.show();
        mListView.setOnItemClickListener(mListener);
    }

    @Override
    public void onShow(DialogInterface dialog) {
        if (!TextUtils.isEmpty(mTitle)) {
            findViewById(R.id.live_title_tv).setVisibility(View.VISIBLE);
            ((TextView) findViewById(R.id.live_title_tv)).setText(mTitle);
        } else {
            findViewById(R.id.live_title_tv).setVisibility(View.GONE);
        }
    }

    public void startLoading(boolean loading) {
        if (!isShowing()) return;
        findViewById(R.id.live_selection_progress).setVisibility(loading ? View.VISIBLE : View.GONE);
    }
}