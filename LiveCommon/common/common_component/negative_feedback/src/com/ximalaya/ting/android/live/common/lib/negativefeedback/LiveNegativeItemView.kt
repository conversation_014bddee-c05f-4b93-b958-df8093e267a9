package com.ximalaya.ting.android.live.common.lib.negativefeedback

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.ui.DrawableUtil
import com.ximalaya.ting.android.live.common.R
import com.ximalaya.ting.android.live.common.lib.negativefeedback.LiveNegativeFeedbackModel.LiveNegativeFeedbackOptionGroup
import com.ximalaya.ting.android.xmutil.Logger

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18321019958
 * @wiki
 * @server
 * @since 2024/12/2
 */
class LiveNegativeItemView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : RelativeLayout(context, attrs) ,IFeedbackClickListener{

    var onFeedbackClickListener: IFeedbackClickListener? = null

    companion object{
        val backColor = Color.parseColor("#F7F9FC")
        val nightBackColor = Color.parseColor("#108d8d91")
    }

    var label: LiveNegativeFeedbackOptionGroup? = null
        set(value) {
            field = value
            update()
        }
    val icon: ImageView by lazy {
        findViewById(R.id.live_feed_back_icon)
    }
    val title: TextView by lazy {
        findViewById(R.id.live_feed_back_title)
    }

    init {
        LayoutInflater.from(context).inflate(R.layout.live_pop_feedback_item,this,true)
    }

    private val labelView: RecyclerView by lazy {
        findViewById<RecyclerView>(R.id.live_feed_back_item_rv).apply {
            layoutManager = GridLayoutManager(context, 2)
            addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    super.getItemOffsets(outRect, view, parent, state)
                    outRect.set(4.dp, 4.dp,4.dp, 4.dp)
                }
            })
        }
    }

    val adapter: LabelAdapter by lazy {
        LabelAdapter().apply {
            labelView.adapter = this
            onClick = this@LiveNegativeItemView
        }
    }

    override fun onClick(group:LiveNegativeFeedbackOptionGroup?,item: LiveNegativeFeedbackModel.LiveNegativeFeedbackOption?, view: View?) {
        onFeedbackClickListener?.onClick(label,item,view)
    }

    fun update() {
        Logger.d("qmc_test5", "LiveNegativeItemView update")
        title.text = label?.name
        if(BaseFragmentActivity2.sIsDarkMode){
            ImageManager.from(MainApplication.getMyApplicationContext()).displayImage(icon,label?.iconNight,0)
        }else{
            ImageManager.from(MainApplication.getMyApplicationContext()).displayImage(icon,label?.iconDay,0)
        }
        adapter.items = label?.subOptions
    }

    class LabelAdapter : RecyclerView.Adapter<LabelViewHolder>() ,OnClickListener{

        var onClick: IFeedbackClickListener? = null

        var items: MutableList<LiveNegativeFeedbackModel.LiveNegativeFeedbackOption>? = null

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LabelViewHolder {
            return LabelViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.live_feed_back_item_label,parent,false))
        }

        override fun getItemCount(): Int {
            return items?.size ?: 0
        }

        override fun onBindViewHolder(holder: LabelViewHolder, position: Int) {
            Logger.d("qmc_test5", "LabelAdapter onBindViewHolder")
            holder.textView.text = items?.get(position)?.name
            holder.itemView.setOnClickListener(this)
            holder.itemView.tag = items?.get(position)
        }

        override fun onClick(v: View?) {
            onClick.let {
                it?.onClick(null,v?.tag as? LiveNegativeFeedbackModel.LiveNegativeFeedbackOption, v)
            }
        }
    }

    class LabelViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textView: TextView = itemView.findViewById(R.id.live_feed_back_label)
        init {
            if(BaseFragmentActivity2.sIsDarkMode){
                textView.background = DrawableUtil.newGradientDrawable(nightBackColor,4.dp)
            }else{
                textView.background = DrawableUtil.newGradientDrawable(backColor,4.dp)
            }
            textView.gravity = Gravity.CENTER
        }
    }
}
interface  IFeedbackClickListener{
    fun onClick(group:LiveNegativeFeedbackOptionGroup?,item:LiveNegativeFeedbackModel.LiveNegativeFeedbackOption?, view:View?)
}