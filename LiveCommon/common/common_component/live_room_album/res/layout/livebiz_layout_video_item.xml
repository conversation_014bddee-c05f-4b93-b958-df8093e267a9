<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/live_biz_video_item"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/live_biz_photo_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:scaleType="centerCrop"
        app:corner_radius="10dp"/>

    <ImageView
        android:id="@+id/live_biz_photo"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitCenter" />

    <ImageView
        android:id="@+id/live_biz_video_play"
        android:layout_width="88dp"
        android:layout_height="88dp"
        android:scaleType="fitCenter"
        android:layout_centerInParent="true"
        android:src="@drawable/livecomm_ic_video_play" />

    <View
        android:id="@+id/live_album_bottom_bg"
        android:layout_width="match_parent"
        android:layout_height="124dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/live_album_bottom_bg" />


    <!-- 当进度条放大高度比普通状态下滑块高度小时，拖动后放大效果有问题，这是由于不会触发onSizeChange，所以通知设置放大后的progressDrawable和滑块drawable，触发onSizeChange会调用到的方法 -->
    <com.ximalaya.ting.android.host.view.ScaleableSeekBar
        android:id="@+id/live_video_seek_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="24dp"
        android:contentDescription="播放进度条"
        android:maxHeight="4dp"
        android:minHeight="4dp"
        android:paddingStart="17dp"
        android:paddingEnd="17dp"
        android:paddingBottom="16dp"
        android:paddingTop="40dp"
        android:progress="0"
        android:progressDrawable="@drawable/host_audio_play_page_v2_seekbar"
        android:thumb="@drawable/live_video_browse_seekbar_control"
        android:thumbOffset="0dp"
        app:scaleableSeekBar_KeyPointColor="@color/host_color_ccffffff"
        app:scaleableSeekBar_ProgressScaleHeight="8dp"
        app:scaleableSeekBar_ProgressScaleResource="@drawable/host_audio_play_page_y_seekbar"
        app:scaleableSeekBar_RestoreTimeMS="100"
        app:scaleableSeekBar_ThumbScaleResource="@drawable/live_video_browse_seekbar_control_scaled" />

    <ImageView
        android:id="@+id/live_biz_video_mute"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/live_biz_video_unmute"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="74dp"
        android:layout_marginLeft="16dp"/>

    <TextView
        android:id="@+id/live_video_progress_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="78dp"
        android:layout_centerHorizontal="true"
        android:textColor="@color/host_color_80ffffff"
        android:textStyle="bold"
        android:shadowRadius="2"
        android:shadowColor="@color/live_color_black_20"
        android:textSize="20sp"
        android:visibility="gone"/>

    <ProgressBar
        android:id="@+id/live_biz_progress"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        tools:visibility="visible" />


</RelativeLayout>