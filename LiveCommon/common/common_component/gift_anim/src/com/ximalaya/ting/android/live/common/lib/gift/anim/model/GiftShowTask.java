package com.ximalaya.ting.android.live.common.lib.gift.anim.model;

import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.entity.BigSvgReplaceFrameModel;
import com.ximalaya.ting.android.live.common.lib.entity.GiftExtInfo;
import com.ximalaya.ting.android.live.common.lib.entity.LiveOfficialWelcomeMessage;
import com.ximalaya.ting.android.live.common.lib.gift.anim.contants.AnimResourceType;
import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.live.common.lib.manager.anim.play.AnimPlayPriority;
import com.ximalaya.ting.android.live.common.lib.manager.anim.play.IPlayAnimTask;
import com.ximalaya.ting.android.live.common.lib.utils.LiveTextUtil;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatGiftMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatUser;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonGiftBoxListMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonReceiverOpenGift;
import com.ximalaya.ting.android.live.lib.chatroom.entity.ReplaceFrameBean;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomBigSvgMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomComboBigGiftMessage;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 礼物特效播放任务类
 *
 * <AUTHOR>
 */
public class GiftShowTask implements IGiftShowTask {
    private static final String TAG = "GiftShowTask";

    /**
     * 任务id
     */
    public String taskId;

    /**
     * 礼物id
     */
    public long giftId;

    /**
     * 送的宝箱或上上签礼物数量，目前用于上上签特效中的描述信息
     */
    public long sendBoxGiftNum;

    /**
     * 宝箱信息
     */
    public List<BoxInfo> boxInfo;


    /**
     * 宝箱本身开出结果动画集合
     */
    private List<List<CommonReceiverOpenGift>> receiverOpenGiftList;

    /**
     * 送出的礼物名称
     */
    public String giftName;

    /**
     * 震动礼物扩展信息
     */
    public GiftExtInfo giftExtInfo;

    /**
     * 对于宝箱和上上签是开出的礼物数量；对于其他是送出的礼物数量, 用于显示大动画播放的次数
     */
    private long giftNum;

    /**
     * 送出礼物的数量，用于显示大动画底部文案
     */
    private long sendGiftNum;

    /**
     * 用于上上签自身的抽签动画
     */
    public long openGiftNum;

    /**
     * 对于宝箱和上上签开出的礼物名称
     */
    public String openGiftName;

    /**
     * 礼物封面
     */
    public String giftCoverPath;

    /**
     * 送礼者uid
     */
    public long senderUid;

    /**
     * 送礼者昵称
     */
    public String senderName;

    /**
     * 送礼者头像
     */
    public String senderAvatarPath;

    /**
     * 收礼者信息, 用于seatPanel判断是否显示礼物icon
     */
    private long receiverUid;


    /**
     * 收礼者昵称
     */
    private String receiverName;

    /**
     * 默认头像
     */
    public int defaultAvatar = -1;

    /**
     * 喜钻值
     */
    public double xiDiamondWorth;

    public static final long DEFAULT_SHOW_TIME = 3000;

    public long showDuration = DEFAULT_SHOW_TIME;

    public String conseUnifiedNo;

    /**
     * svg 大动画本地路径，包括连击或批量送触发特效、非礼物触发特效（PK道具使用特效）
     */
    public String localSvgPath;

    /**
     * mp4 大动画本地路径，包括连击或批量送触发特效、非礼物触发特效（PK道具使用特效）
     */
    public String localMp4Path;

    /**
     * asset目录
     */
    public String assetName;

    /**
     * 替换Svg中指定帧的目标图片
     */
    public String mMyAvatar;

    /**
     * 动画里的某一帧需要替换
     */
    private boolean needReplaceFrame;

    /**
     * 替换帧需要被裁剪成圆形，默认是 true
     */
    public boolean needCircleReplaceFrame = true;

    /**
     * 是否交友模式礼物
     */
    public boolean isFriendsMode = false;

    /**
     * 是否娱乐厅礼物
     */
    private boolean isEntGift = false;

    /**
     * 连击或批量送特效，动画只展示一次，默认 false
     */
    public boolean isComboBigGift = false;

    /**
     * 是否是上上签礼物
     */
    public boolean isLotGift = false;

    /**
     * 替换多帧的信息
     */
    public List<ReplaceFrameBean> mReplaceFrame;

    /**
     * 贵族送礼特效模板id
     */
    public int nobleTemplateId;

    /**
     * 礼物动效模板id
     */
    public long animationId;
    /**
     * 任务创建时间，优先级相同时按任务创建时间排序
     */
    public long createTimeMs;

    /**
     * 特效资源下载状态，本地缓存没有该特效资源时，会先下载特效资源到本地缓存，然后再播放本地缓存特效资源
     */
    public @ResourceDownloadStatus
    int resourceDownloadStatus = ResourceDownloadStatus.STATUS_UNSET;

    /**
     * 子任务，如宝箱礼物场景，包括播放宝箱特效和播放宝箱开出的礼物特效，子任务用来播放宝箱开出的礼物特效
     */
    public List<IPlayAnimTask> childrenTask;
    public int mBizType;
    public int mDynamicType;
    public long mLiveId;
    /**
     * 商品价格
     * 从GiftLoader 中获取， 目前主要使用于礼物截图
     */
    public int mPrice;
    public long mRoomId;
    public String mBigSvgMessageText;

    /**
     * 目前暂时存的是冠名礼物后的扩展数据
     * {
     * "type": // 枚举,1，namingSuccess 冠名成功， 2，冠名成功后赠送，afterNamingSuccess
     * "data" :{}
     * }
     */
    public Map<String, String> extData;

    /**
     * 宝箱接收者的要替换图片的key
     */
    public String boxReceiverKey;

    public static String EXT_DATA_KEY = "type";
    public static String EXT_DATA_VALUE_RoomWeeklyNamingSuccessAfterSendGift = "roomWeeklyNamingSuccessAfterSendGift";
    public static String EXT_DATA_VALUE_AlreadyRoomWeeklyNaming = "alreadyRoomWeeklyNaming";

    /**
     * 资源类型和资源下载路径信息
     * 资源类型见 {@link AnimResourceType}
     */
    private Pair<Integer, String> resourceTypeAndPath;

    /**
     * 优先级
     */
    private int priority;
    /**
     * 是否来自非礼物消息
     */
    public boolean isFromNoGiftMessage;

    /**
     * 临时添加的字段，用于判断是否是将h265转成h264，降级处理的task
     */
    public boolean convertOldH265TaskToH264;

    /**
     * 是否为主视区播放动画
     */
    private boolean isFromMainAreaPlay = false;

    private String mainAreaReplaceBitmapKey = "";

    private int mainAreaReplaceBitmapId = 0;

    // 收礼人uid列表
    private List<Long> receiverList;

    // 是否是互动礼物类型
    private boolean interactiveGift;

    // 互动礼物显示大动画底部的数量
    private long interactiveSendGiftNum;

    public long getInteractiveSendGiftNum() {
        return interactiveSendGiftNum;
    }

    public void setInteractiveSendGiftNum(long interactiveSendGiftNum) {
        this.interactiveSendGiftNum = interactiveSendGiftNum;
    }

    public boolean isInteractiveGift() {
        return interactiveGift;
    }

    public void setInteractiveGift(boolean interactiveGift) {
        this.interactiveGift = interactiveGift;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public GiftShowTask() {

    }

    public static GiftShowTask createBigSvgMessageTask(CommonChatRoomBigSvgMessage bigSvgMessage, BaseGiftLoader<?> giftLoader) {
        GiftShowTask task = new GiftShowTask();
        // 由于非礼物资源消息并没有给出giftNum，且此场景目前只会是单次动画，因此设置为1
        task.setGiftNum(1);
        task.setSendGiftNum(1);
        task.animationId = bigSvgMessage.getTemplateId();
        task.mBigSvgMessageText = bigSvgMessage.getTxt();
        if (bigSvgMessage.getType() == BigSvgReplaceFrameModel.TYPE_NEED_REPLACE_IMAGE) {
            if (!TextUtils.isEmpty(bigSvgMessage.getReplaceUrl())) {
                task.mMyAvatar = bigSvgMessage.getReplaceUrl();
                task.setNeedReplaceFrame(true);
            }
        } else if (bigSvgMessage.getType() == BigSvgReplaceFrameModel.TYPE_MULTIPLE_IMG_TXT) {
            if (!ToolUtil.isEmptyCollects(bigSvgMessage.getReplace())) {
                task.mReplaceFrame = bigSvgMessage.getReplace();
            }
        }
        task.mBizType = giftLoader.getBizType();
        task.mDynamicType = giftLoader.getDynamicType();
        task.mLiveId = giftLoader.getLiveId();
        task.mRoomId = giftLoader.getRoomId();
        task.createTimeMs = System.currentTimeMillis();
        int animateType = bigSvgMessage.animateType;
        CommonLiveLogger.d(TAG, "bigSvgMessage animateType:$animateType, entAnimateType:${bigSvgMessage.entAnimateType}");
        if (CommonChatRoomBigSvgMessage.isAccumulationAnimType(animateType) || CommonChatRoomBigSvgMessage.isHatAnimType(bigSvgMessage.entAnimateType)) {
            task.priority = AnimPlayPriority.PRIORITY_40;
        } else if (CommonChatRoomBigSvgMessage.isTimingAnimType(animateType) || CommonChatRoomBigSvgMessage.isLoverAnimType(bigSvgMessage.entAnimateType)) {
            task.priority = AnimPlayPriority.PRIORITY_30;
        } else if (CommonChatRoomBigSvgMessage.isOpenAnimType(animateType) || CommonChatRoomBigSvgMessage.isCPRelationshipAnimType(animateType)) {
            CommonLiveLogger.d(TAG, "bigSvgMessage uid:" + bigSvgMessage.senderId);
            if (bigSvgMessage.senderId == UserInfoMannage.getUid()) {
                task.priority = AnimPlayPriority.PRIORITY_50;
            } else {
                task.priority = AnimPlayPriority.PRIORITY_60;
            }
        } else {
            task.priority = AnimPlayPriority.PRIORITY_60;
        }
        task.isFromNoGiftMessage = true;
        return task;
    }

    /**
     * 服务端返回的次序, 用于pop条显示
     **/
    public long consecutiveIndex = 0;


    public boolean isConsecutive() {
        return !TextUtils.isEmpty(conseUnifiedNo);
    }

    /**
     * 官播间showTask
     *
     * @param info       LiveOfficialWelcomeMessage
     * @param giftLoader BaseGiftLoader
     */
    public GiftShowTask(LiveOfficialWelcomeMessage info, BaseGiftLoader<?> giftLoader) {
        if (info == null) {
            return;
        }
        this.isComboBigGift = true;
        this.animationId = info.getTemplateId();
        this.createTimeMs = System.currentTimeMillis();
        this.mBizType = giftLoader.getBizType();
        this.mDynamicType = giftLoader.getDynamicType();
        this.mLiveId = giftLoader.getLiveId();
        this.mRoomId = giftLoader.getRoomId();
        this.priority = AnimPlayPriority.PRIORITY_10;
    }

    /**
     * 通过模版id来构建giftShowTask
     *
     * @param templateId 模版id
     * @param giftLoader BaseGiftLoader
     */
    public GiftShowTask(long templateId, BaseGiftLoader<?> giftLoader) {
        this.isComboBigGift = true;
        this.animationId = templateId;
        this.createTimeMs = System.currentTimeMillis();
        this.mBizType = giftLoader.getBizType();
        this.mDynamicType = giftLoader.getDynamicType();
        this.mLiveId = giftLoader.getLiveId();
        this.mRoomId = giftLoader.getRoomId();
        this.priority = AnimPlayPriority.PRIORITY_10;
        Logger.i(TAG, "combo-gift: " + localSvgPath);
    }

    /**
     * 音视频直播
     */
    public GiftShowTask(CommonChatRoomComboBigGiftMessage info, BaseGiftLoader<?> giftLoader) {
        if (info == null) {
            return;
        }

        this.isComboBigGift = true;
        this.senderUid = info.senderId;
        this.giftId = info.giftId;
        if (info.quantity < 1) {
            this.setGiftNum(1);
            this.setSendGiftNum(1);
        } else {
            this.setGiftNum(info.quantity);
            this.setSendGiftNum(info.quantity);
        }
        this.senderName = info.sendernn;

        GiftInfoCombine.GiftInfo giftDetailInfo = giftLoader.getGift(this.giftId);
        if (giftDetailInfo != null) {
            this.mPrice = giftDetailInfo.getPrice();
            this.giftName = giftDetailInfo.name;
        } else {
            this.giftName = "连击礼物";
        }

        this.mBigSvgMessageText = info.getTxt();
        if (info.getType() == BigSvgReplaceFrameModel.TYPE_NEED_REPLACE_IMAGE) {
            if (!TextUtils.isEmpty(info.getReplaceUrl())) {
                this.mMyAvatar = info.getReplaceUrl();
                this.setNeedReplaceFrame(true);
            }
        } else if (info.getType() == BigSvgReplaceFrameModel.TYPE_MULTIPLE_IMG_TXT) {
            if (!ToolUtil.isEmptyCollects(info.getReplace())) {
                this.mReplaceFrame = info.getReplace();
            }
        }

        // 小礼物连击触发的特效模板id
        this.animationId = info.templateId;
        this.createTimeMs = System.currentTimeMillis();
        this.mBizType = giftLoader.getBizType();
        this.mDynamicType = giftLoader.getDynamicType();
        this.mLiveId = giftLoader.getLiveId();
        this.mRoomId = giftLoader.getRoomId();

        if (info.senderId == UserInfoMannage.getUid()) {
            this.priority = AnimPlayPriority.PRIORITY_50;
        } else {
            this.priority = AnimPlayPriority.PRIORITY_60;
        }

        Logger.i(TAG, "combo-gift: " + this);
    }

    public GiftShowTask(CommonChatGiftMessage info, BaseGiftLoader<?> giftLoader) {
        if (info == null) {
            return;
        }
        this.isFriendsMode = info.isFriendMode;
        this.giftId = info.mGiftId;
        this.giftName = giftLoader.getGiftName(info.mGiftId);
        this.giftExtInfo = giftLoader.getGiftExtInfo(info.mGiftId);

        this.giftCoverPath = giftLoader.getGiftPath(info.mGiftId);
        if (info.mQuantity < 1) {
            this.setGiftNum(1);
            this.setSendGiftNum(1);
        } else {
            this.setGiftNum(info.mQuantity);
            this.setSendGiftNum(info.mQuantity);
        }
        this.openGiftNum = giftNum;
        // 设置送礼者信息
        this.senderUid = info.mSender.mUid;
        this.senderName = LiveTextUtil.getStringWithDefault(info.mSender.mNickname, "");
        // 设置接收者信息
        if (ToolUtil.isEmptyCollects(info.mReceiverList)) {
            this.receiverUid = 0;
            this.receiverName = "";
        } else {
            // 交友模式信令目前仅支持单麦上用户，不支持全麦
            this.receiverUid = info.mReceiverList.get(0).mUid;
            this.receiverName = info.mReceiverList.get(0).mNickname;
        }
        this.taskId = this.senderUid + this.giftName + SystemClock.currentThreadTimeMillis();
        this.conseUnifiedNo = info.mGiftConseUnifiedNo;
        this.consecutiveIndex = info.mHits;
        if (info.mDuration > 0) {
            this.showDuration = info.mDuration * 1000L;
        }
        if (giftNum > 1) {
            showDuration += getBatchAnimationDuration(giftNum);
        }
        this.nobleTemplateId = info.mNobleTemplateId;

        GiftInfoCombine.GiftInfo giftDetailInfo = giftLoader.getLocalGift(giftId);
        if (this.xiDiamondWorth <= 0 && giftDetailInfo != null) {
            this.xiDiamondWorth = giftDetailInfo.xiDiamondWorth;
        }
        if (giftDetailInfo != null) {
            this.mPrice = giftDetailInfo.getPrice();
        }
        setNeedReplaceFrame(info.mIsBoxGift);
        this.mReplaceFrame = info.replaceFrameBean;
        this.animationId = giftLoader.getGiftAnimationId(giftId);
        this.createTimeMs = System.currentTimeMillis();
        this.mBizType = giftLoader.getBizType();
        this.mDynamicType = giftLoader.getDynamicType();
        this.mLiveId = giftLoader.getLiveId();
        this.mRoomId = giftLoader.getRoomId();
        this.extData = info.extData;
        if (info.mSender != null) {
            if (info.mSender.mUid == UserInfoMannage.getUid()) {
                this.priority = AnimPlayPriority.PRIORITY_50;
            } else {
                this.priority = AnimPlayPriority.PRIORITY_60;
            }
        }
    }

    public GiftShowTask(GiftShowTask oldTask) {
        this.giftId = oldTask.giftId;
        this.giftName = oldTask.giftName;
        this.setGiftNum(oldTask.giftNum);
        this.setSendGiftNum(oldTask.sendGiftNum);
        this.senderUid = oldTask.senderUid;
        this.senderName = oldTask.senderName;
        this.senderAvatarPath = oldTask.senderAvatarPath;
        this.giftCoverPath = oldTask.giftCoverPath;
        this.taskId = this.senderUid + this.giftName + SystemClock.currentThreadTimeMillis();
        this.nobleTemplateId = oldTask.nobleTemplateId;
        this.animationId = oldTask.animationId;
        this.createTimeMs = System.currentTimeMillis();
        this.mBizType = oldTask.mBizType;
        this.mDynamicType = oldTask.mDynamicType;
        this.mLiveId = oldTask.mLiveId;
        this.mRoomId = oldTask.mRoomId;
        this.extData = oldTask.extData;
        this.receiverName = oldTask.receiverName;
        this.consecutiveIndex = oldTask.consecutiveIndex;
        this.conseUnifiedNo = oldTask.conseUnifiedNo;
        this.showDuration = oldTask.showDuration;
        this.xiDiamondWorth = oldTask.xiDiamondWorth;
        this.receiverUid = oldTask.receiverUid;
    }

    /**
     * 宝箱集合消息构建task
     *
     * @param giftBoxListMessage CommonGiftBoxListMessage
     * @param giftPreLoader      BaseGiftLoader
     */
    public GiftShowTask(CommonGiftBoxListMessage giftBoxListMessage, BaseGiftLoader<?> giftPreLoader) {
        if (giftBoxListMessage == null || giftPreLoader == null) {
            return;
        }

        this.giftId = giftBoxListMessage.getGiftBoxId() != null ? giftBoxListMessage.getGiftBoxId() : 0L;
        this.giftName = giftPreLoader.getGiftName(giftId);
        boolean isPgc = giftPreLoader.getShowType().equals(ParamsConstantsInLive.GIFT_SHOW_TYPE_HALL);
        // 设置接收者信息, 如果一个人的时候显示昵称
        if (giftBoxListMessage.getMicCnt() != null && giftBoxListMessage.getMicCnt() == 1 && isPgc) {
            if (!ToolUtil.isEmptyCollects(giftBoxListMessage.getOpenGifts())) {
                List<CommonReceiverOpenGift> commonReceiverOpenGifts = giftBoxListMessage.getOpenGifts().get(0);
                if (!ToolUtil.isEmptyCollects(commonReceiverOpenGifts)) {
                    CommonReceiverOpenGift commonReceiverOpenGift = commonReceiverOpenGifts.get(0);
                    CommonChatUser receiver = commonReceiverOpenGift.getReceiver();
                    if (receiver != null) {
                        this.setReceiverUid(receiver.mUid);
                        this.receiverName = LiveTextUtil.getStringWithDefault(receiver.mNickname, "");
                    }
                }
            }
        } else {
            this.receiverUid = 0;
            this.receiverName = "";
        }
        this.boxReceiverKey = giftBoxListMessage.getBoxReceiverKey();
        if (giftBoxListMessage.getOpenGifts() != null) {
            this.setGiftNum(giftBoxListMessage.getOpenGifts().size());
        } else {
            this.setGiftNum(1);
        }
        this.setSendGiftNum(giftBoxListMessage.getQuantity() != null ? giftBoxListMessage.getQuantity() : 1);

        ArrayList<BoxInfo> boxInfos = new ArrayList<>();
        ArrayList<List<CommonReceiverOpenGift>> receiverOpenGiftList = new ArrayList<>();
        if (giftBoxListMessage.getOpenGifts() != null) {
            for (List<CommonReceiverOpenGift> receiverOpenGifts : giftBoxListMessage.getOpenGifts()) {
                int openGiftSize = receiverOpenGifts.size();
                for (CommonReceiverOpenGift openGift : receiverOpenGifts) {
                    if (openGift == null) {
                        continue;
                    }

                    // 构建宝箱开出结果BoxInfo集合
                    int openGiftId = openGift.getGiftId() != null ? openGift.getGiftId() : 0;
                    BoxInfo boxInfo = new BoxInfo();
                    boxInfo.giftId = openGiftId;
                    GiftInfoCombine.GiftInfo giftInfo = giftPreLoader.getGift(openGiftId);
                    if (giftInfo != null && openGift.getReceiver() != null) {
                        boxInfo.setReceiveUid(openGift.getReceiver().mUid);
                        boxInfo.setReceiveName(openGift.getReceiver().mNickname);
                    }
                    if (giftInfo != null) {
                        this.mPrice = giftInfo.getPrice();
                    }
                    boxInfo.setOpenGiftQuantity(openGift.getQuantity() != null ? openGift.getQuantity() : 1);
                    boxInfos.add(boxInfo);

                    // 设置字体，用于宝箱开出结果展示数量字体的设置
                    switch (openGiftSize) {
                        case 1:
                            openGift.setFontSize(20);
                            break;
                        case 2:
                            openGift.setFontSize(18);
                            break;
                        case 3:
                            openGift.setFontSize(16);
                            break;
                        default:
                            openGift.setFontSize(14);
                    }
                }

                receiverOpenGiftList.add(receiverOpenGifts);
            }
        }
        this.boxInfo = boxInfos;
        setReceiverOpenGiftList(receiverOpenGiftList);

        if (giftBoxListMessage.getSender() != null) {
            this.senderUid = giftBoxListMessage.getSender().mUid;
            this.senderName = LiveTextUtil.getStringWithDefault(giftBoxListMessage.getSender().mNickname, "");
            if (giftBoxListMessage.getSender().mUid == UserInfoMannage.getUid()) {
                this.priority = AnimPlayPriority.PRIORITY_50;
            } else {
                this.priority = AnimPlayPriority.PRIORITY_60;
            }
        } else {
            this.priority = AnimPlayPriority.PRIORITY_60;
        }
        this.taskId = this.senderUid + this.giftName + SystemClock.currentThreadTimeMillis();
        GiftInfoCombine.GiftInfo giftDetailInfo = giftPreLoader.getGift(giftId);
        if (this.xiDiamondWorth <= 0 && giftDetailInfo != null) {
            this.xiDiamondWorth = giftDetailInfo.xiDiamondWorth;
        }
        if (giftDetailInfo != null) {
            this.mPrice = giftDetailInfo.getPrice();
        }
        setNeedReplaceFrame(true);


        if (giftBoxListMessage.getTemplateId() != null && giftBoxListMessage.getTemplateId() > 0) {
            this.animationId = giftBoxListMessage.getTemplateId();
        } else {
            this.animationId = giftPreLoader.getGiftAnimationId(giftId);
        }

        this.createTimeMs = System.currentTimeMillis();
        this.mBizType = giftPreLoader.getBizType();
        this.mDynamicType = giftPreLoader.getDynamicType();
        this.mLiveId = giftPreLoader.getLiveId();
        this.mRoomId = giftPreLoader.getRoomId();
    }

    /**
     * 宝箱集合消息里的特殊处理：任意门礼物，由于开出的结果数据结构不一样，所以需要特殊处理
     */
    public GiftShowTask(CommonGiftBoxListMessage giftBoxListMessage, List<List<CommonReceiverOpenGift>> commonReceiverOpenGifts, BaseGiftLoader<?> giftPreLoader) {
        if (giftBoxListMessage == null) {
            return;
        }

        this.giftId = giftBoxListMessage.getGiftBoxId() != null ? giftBoxListMessage.getGiftBoxId() : 0L;
        this.giftName = giftPreLoader.getGiftName(giftId);
        boolean isPgc = giftPreLoader.getShowType().equals(ParamsConstantsInLive.GIFT_SHOW_TYPE_HALL);
        // 只有pgc的时候，才显示收礼人昵称信息
        if (isPgc) {
            // 因为这里是任意门礼物，开出的结果是ABABAB的形式，所以都需要展示收礼人昵称
            if (commonReceiverOpenGifts != null && !commonReceiverOpenGifts.isEmpty()) {
                List<CommonReceiverOpenGift> commonReceiverOpenGifts1 = commonReceiverOpenGifts.get(0);
                if (commonReceiverOpenGifts1 != null && !commonReceiverOpenGifts1.isEmpty()) {
                    CommonReceiverOpenGift commonReceiverOpenGift = commonReceiverOpenGifts1.get(0);
                    if (commonReceiverOpenGift != null) {
                        CommonChatUser receiver = commonReceiverOpenGift.getReceiver();
                        if (receiver != null) {
                            this.setReceiverUid(receiver.mUid);
                            this.receiverName = LiveTextUtil.getStringWithDefault(receiver.mNickname, "");
                        }
                    }
                }
            }
        } else {
            this.receiverUid = 0;
            this.receiverName = "";
        }
        this.boxReceiverKey = giftBoxListMessage.getBoxReceiverKey();
        if (commonReceiverOpenGifts != null) {
            CommonLiveLogger.d(TAG, "同一个uid下开出宝箱的次数：" + commonReceiverOpenGifts.size());
            this.setGiftNum(commonReceiverOpenGifts.size());
        } else {
            this.setGiftNum(1);
        }

        int quantity = giftBoxListMessage.getQuantity() != null ? giftBoxListMessage.getQuantity() : 1;
        if (giftBoxListMessage.getMicCnt() != null && giftBoxListMessage.getMicCnt() > 0) {
            quantity = quantity / giftBoxListMessage.getMicCnt();
        }
        this.setSendGiftNum(quantity);
        // 构建boxInfo
        ArrayList<BoxInfo> boxInfos = new ArrayList<>();
        ArrayList<List<CommonReceiverOpenGift>> receiverOpenGiftList = new ArrayList<>();
        if (commonReceiverOpenGifts != null) {
            for (List<CommonReceiverOpenGift> receiverOpenGifts : commonReceiverOpenGifts) {
                int openGiftSize = commonReceiverOpenGifts.size();
                CommonLiveLogger.d(TAG, "宝箱开出结果的数据有多少个:" + openGiftSize);
                for (CommonReceiverOpenGift openGift : receiverOpenGifts) {
                    if (openGift == null) {
                        continue;
                    }

                    // 构建宝箱开出结果BoxInfo集合
                    int openGiftId = openGift.getGiftId() != null ? openGift.getGiftId() : 0;
                    BoxInfo boxInfo = new BoxInfo();
                    boxInfo.giftId = openGiftId;
                    GiftInfoCombine.GiftInfo giftInfo = giftPreLoader.getGift(openGiftId);
                    if (giftInfo != null && openGift.getReceiver() != null) {
                        boxInfo.setReceiveUid(openGift.getReceiver().mUid);
                        boxInfo.setReceiveName(openGift.getReceiver().mNickname);
                    }
                    if (giftInfo != null) {
                        this.mPrice = giftInfo.getPrice();
                    }
                    boxInfo.setOpenGiftQuantity(openGift.getQuantity() != null ? openGift.getQuantity() : 1);
                    boxInfos.add(boxInfo);

                    // 设置字体，用于宝箱开出结果展示数量字体的设置
                    switch (openGiftSize) {
                        case 1:
                            openGift.setFontSize(20);
                            break;
                        case 2:
                            openGift.setFontSize(18);
                            break;
                        case 3:
                            openGift.setFontSize(16);
                            break;
                        default:
                            openGift.setFontSize(14);
                    }
                }
                receiverOpenGiftList.add(receiverOpenGifts);
            }
        }
        this.boxInfo = boxInfos;
        setReceiverOpenGiftList(receiverOpenGiftList);

        if (giftBoxListMessage.getSender() != null) {
            this.senderUid = giftBoxListMessage.getSender().mUid;
            this.senderName = LiveTextUtil.getStringWithDefault(giftBoxListMessage.getSender().mNickname, "");
            if (giftBoxListMessage.getSender().mUid == UserInfoMannage.getUid()) {
                this.priority = AnimPlayPriority.PRIORITY_50;
            } else {
                this.priority = AnimPlayPriority.PRIORITY_60;
            }
        } else {
            this.priority = AnimPlayPriority.PRIORITY_60;
        }
        this.taskId = this.senderUid + this.giftName + SystemClock.currentThreadTimeMillis();
        GiftInfoCombine.GiftInfo giftDetailInfo = giftPreLoader.getGift(giftId);
        if (this.xiDiamondWorth <= 0 && giftDetailInfo != null) {
            this.xiDiamondWorth = giftDetailInfo.xiDiamondWorth;
        }
        if (giftDetailInfo != null) {
            this.mPrice = giftDetailInfo.getPrice();
        }
        setNeedReplaceFrame(true);

        if (giftBoxListMessage.getTemplateId() != null && giftBoxListMessage.getTemplateId() > 0) {
            this.animationId = giftBoxListMessage.getTemplateId();
        } else {
            this.animationId = giftPreLoader.getGiftAnimationId(giftId);
        }

        this.createTimeMs = System.currentTimeMillis();
        this.mBizType = giftPreLoader.getBizType();
        this.mDynamicType = giftPreLoader.getDynamicType();
        this.mLiveId = giftPreLoader.getLiveId();
        this.mRoomId = giftPreLoader.getRoomId();
    }


    @NonNull
    @Override
    public String toString() {
        return "GiftShowTask{" +
                "taskId='" + taskId + '\'' +
                ", giftId=" + giftId +
                ", sendBoxGiftNum=" + sendBoxGiftNum +
                ", boxInfo=" + boxInfo +
                ", giftName='" + giftName + '\'' +
                ", giftNum=" + giftNum +
                ", openGiftNum=" + openGiftNum +
                ", openGiftName='" + openGiftName + '\'' +
                ", giftCoverPath='" + giftCoverPath + '\'' +
                ", senderUid=" + senderUid +
                ", senderName='" + senderName + '\'' +
                ", resourceTypeAndPath=" + resourceTypeAndPath +
                ", receiverUid=" + receiverUid +
                ", receiverName='" + receiverName + '\'' +
                ", defaultAvatar=" + defaultAvatar +
                ", xiDiamondWorth=" + xiDiamondWorth +
                ", showDuration=" + showDuration +
                ", conseUnifiedNo='" + conseUnifiedNo + '\'' +
                ", localSvgPath='" + localSvgPath + '\'' +
                ", localMp4Path='" + localMp4Path + '\'' +
                ", assetName='" + assetName + '\'' +
                ", mMyAvatar='" + mMyAvatar + '\'' +
                ", needReplaceFrame=" + needReplaceFrame +
                ", needCircleReplaceFrame=" + needCircleReplaceFrame +
                ", isFriendsMode=" + isFriendsMode +
                ", isEntGift=" + isEntGift +
                ", isComboBigGift=" + isComboBigGift +
                ", isLotGift=" + isLotGift +
                ", mReplaceFrame=" + mReplaceFrame +
                ", nobleTemplateId=" + nobleTemplateId +
                ", animationId=" + animationId +
                ", downloadTime=" + createTimeMs +
                ", consecutiveIndex=" + consecutiveIndex +
                '}';
    }

    public static long getBatchAnimationDuration(long giftCount) {
        if (giftCount <= 10) {
            return 1000;
        } else if (giftCount <= 66) {
            return 3000;
        } else if (giftCount <= 100) {
            return 3500;
        } else if (giftCount <= 233) {
            return 5000;
        } else if (giftCount <= 520) {
            return 7000;
        } else {
            return 12000;
        }
    }

    public void setNeedReplaceFrame(boolean needReplaceFrame) {
        this.needReplaceFrame = needReplaceFrame;
    }

    public boolean isNeedReplaceFrame() {
        return needReplaceFrame;
    }

    public boolean openedGift() {
        return boxInfo != null && giftId > 0;
    }

    @Override
    public void setResourceTypeAndPath(Pair<Integer, String> resourceTypeAndPath) {
        this.resourceTypeAndPath = resourceTypeAndPath;
    }

    @Nullable
    @Override
    public Pair<Integer, String> getResourceTypeAndPath() {
        return resourceTypeAndPath;
    }

    public String getBigSvgMessageText() {
        return mBigSvgMessageText;
    }

    @Override
    public String getTaskId() {
        return this.taskId;
    }

    @Override
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    @Override
    public long getGiftId() {
        return this.giftId;
    }

    @Override
    public void setGiftId(long giftId) {
        this.giftId = giftId;
    }

    @Override
    public String getGiftName() {
        return giftName;
    }

    @Override
    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    @Override
    public long getGiftNum() {
        return this.giftNum;
    }

    @Override
    public void setGiftNum(long giftNum) {
        this.giftNum = giftNum;
    }

    @Override
    public long getSenderUid() {
        return this.senderUid;
    }

    @Override
    public long getCreateTimeMs() {
        return createTimeMs;
    }

    @Override
    public List<IPlayAnimTask> getChildrenTask() {
        return childrenTask;
    }

    @Override
    public int getResourceDownloadStatus() {
        return resourceDownloadStatus;
    }

    @Override
    public void setResourceDownloadStatus(@ResourceDownloadStatus int resourceDownloadStatus) {
        this.resourceDownloadStatus = resourceDownloadStatus;
    }

    @Override
    public void setSenderUid(long senderUid) {
        this.senderUid = senderUid;
    }

    @Override
    public String getSenderName() {
        return this.senderName;
    }

    @Override
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    @Override
    public long getReceiverUid() {
        return this.receiverUid;
    }

    @Override
    public void setReceiverUid(long receiverUid) {
        this.receiverUid = receiverUid;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    @Override
    public long getAnimationId() {
        return animationId;
    }

    public void setAnimationId(long animationId) {
        this.animationId = animationId;
    }


    @Override
    public String getAssetName() {
        return this.assetName;
    }

    @Override
    public void setAssetName(String assetName) {
        this.assetName = assetName;
    }

    @Override
    public boolean isEntGift() {
        return this.isEntGift;
    }

    @Override
    public void setIsEntGift(boolean isEntGift) {
        this.isEntGift = isEntGift;
    }

    public static GiftShowTask buildOpenedGiftTask(GiftShowTask info, BaseGiftLoader<?> giftLoader, long giftId) {
        if (info != null && info.boxInfo != null) {
            GiftShowTask task = new GiftShowTask(info);
            task.setNeedReplaceFrame(false);
            task.setGiftNum(info.giftNum);
            task.setSendGiftNum(info.sendGiftNum);
            task.giftId = giftId;
            GiftInfoCombine.GiftInfo openedGiftDetail = giftLoader.getGift(task.giftId);
            if (openedGiftDetail != null) {
                task.giftCoverPath = openedGiftDetail.coverPath;
                task.mPrice = openedGiftDetail.getPrice();
                if (task.xiDiamondWorth <= 0) {
                    task.xiDiamondWorth = openedGiftDetail.xiDiamondWorth;
                }
                task.giftName = openedGiftDetail.name;
            }
            task.senderUid = info.senderUid;
            task.senderName = info.senderName;
            task.conseUnifiedNo = info.conseUnifiedNo;
            task.nobleTemplateId = info.nobleTemplateId;
            task.setNeedReplaceFrame(false);
            task.animationId = giftLoader.getGiftAnimationId(task.giftId);
            task.priority = info.priority;
            task.extData = info.extData;
            task.createTimeMs = info.createTimeMs;
            task.setReceiverUid(info.receiverUid);
            return task;
        }
        return null;
    }

    @Override
    public int getBizType() {
        return mBizType;
    }

    @Override
    public boolean convertOldH265TaskToH264() {
        return convertOldH265TaskToH264;
    }

    public int getDynamicType() {
        return mDynamicType;
    }

    public long getLiveId() {
        return mLiveId;
    }


    public long getRoomId() {
        return mRoomId;
    }

    @Override
    public int getPrice() {
        return mPrice;
    }

    public void setPrice(int mPrice) {
        this.mPrice = mPrice;
    }

    public void setBizType(int mBizType) {
        this.mBizType = mBizType;
    }

    public void setDynamicType(int mDynamicType) {
        this.mDynamicType = mDynamicType;
    }

    public void setLiveId(long mLiveId) {
        this.mLiveId = mLiveId;
    }

    public void setRoomId(long mRoomId) {
        this.mRoomId = mRoomId;
    }

    public boolean isFromMainAreaPlay() {
        return isFromMainAreaPlay;
    }

    public void setFromMainAreaPlay(boolean fromMainAreaPlay) {
        isFromMainAreaPlay = fromMainAreaPlay;
    }

    public String getMainAreaReplaceBitmapKey() {
        return mainAreaReplaceBitmapKey;
    }

    public void setMainAreaReplaceBitmapKey(String mainAreaReplaceBitmapKey) {
        this.mainAreaReplaceBitmapKey = mainAreaReplaceBitmapKey;
    }

    public int getMainAreaReplaceBitmapId() {
        return mainAreaReplaceBitmapId;
    }

    public void setMainAreaReplaceBitmapId(int mainAreaReplaceBitmapId) {
        this.mainAreaReplaceBitmapId = mainAreaReplaceBitmapId;
    }

    public static GiftShowTask convertOldH265TaskToH264(GiftShowTask oldTask) {
        GiftShowTask resTask = new GiftShowTask();
        resTask.giftId = oldTask.giftId;
        resTask.giftName = oldTask.giftName;
        resTask.setGiftNum(oldTask.giftNum);
        resTask.setSendGiftNum(oldTask.sendGiftNum);
        resTask.senderUid = oldTask.senderUid;
        resTask.senderName = oldTask.senderName;
        resTask.senderAvatarPath = oldTask.senderAvatarPath;
        resTask.giftCoverPath = oldTask.giftCoverPath;
        resTask.taskId = resTask.senderUid + resTask.giftName + SystemClock.currentThreadTimeMillis();
        resTask.nobleTemplateId = oldTask.nobleTemplateId;
        resTask.animationId = oldTask.animationId;
        resTask.isComboBigGift = oldTask.isComboBigGift;
        resTask.createTimeMs = System.currentTimeMillis();
        resTask.mBizType = oldTask.mBizType;
        resTask.mDynamicType = oldTask.mDynamicType;
        resTask.mLiveId = oldTask.mLiveId;
        resTask.mRoomId = oldTask.mRoomId;
        resTask.priority = oldTask.priority;
        resTask.convertOldH265TaskToH264 = true;
        return resTask;
    }


    public long getSendGiftNum() {
        return sendGiftNum;
    }

    public void setSendGiftNum(long sendGiftNum) {
        this.sendGiftNum = sendGiftNum;
    }

    public List<Long> getReceiverList() {
        return receiverList;
    }

    public void setReceiverList(List<Long> receiverList) {
        this.receiverList = receiverList;
    }


    public List<List<CommonReceiverOpenGift>> getReceiverOpenGiftList() {
        return receiverOpenGiftList;
    }

    public void setReceiverOpenGiftList(List<List<CommonReceiverOpenGift>> receiverOpenGiftList) {
        this.receiverOpenGiftList = receiverOpenGiftList;
    }

    /**
     * 宝箱开出的礼物信息
     */
    public static class BoxInfo {
        public long giftId;
        public boolean prize;
        public String content;

        /**
         * 开出宝箱的结果数量，默认为 1
         */
        private int openGiftQuantity = 1;

        /**
         * 收礼人uid
         */
        private long receiveUid;

        /**
         * 收礼人昵称
         */
        private String receiveName;

        public BoxInfo() {

        }

        public String getReceiveName() {
            return receiveName;
        }

        public void setReceiveName(String receiveName) {
            this.receiveName = receiveName;
        }

        public long getReceiveUid() {
            return receiveUid;
        }

        public void setReceiveUid(long receiveUid) {
            this.receiveUid = receiveUid;
        }

        public int getOpenGiftQuantity() {
            return openGiftQuantity;
        }

        public void setOpenGiftQuantity(int openGiftQuantity) {
            this.openGiftQuantity = openGiftQuantity;
        }

        @NonNull
        @Override
        public String toString() {
            return "BoxInfo{" +
                    "giftId=" + giftId +
                    ", prize=" + prize +
                    ", content='" + content + '\'' +
                    ", openGiftQuantity=" + openGiftQuantity +
                    ", receiveUid=" + receiveUid +
                    '}';
        }
    }

}
