package com.ximalaya.ting.android.live.common.lib.gift.anim;

import static com.ximalaya.ting.android.live.common.lib.manager.broadcast.LiveLocalBroadcastManager.EXTRA.GIFT_PRICE;

import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Pair;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.alphamovie.Constants;
import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger;
import com.ximalaya.ting.android.common.lib.logger.ConnectLogUtilWrapper;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.XmPictureUrlUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.live.common.R;
import com.ximalaya.ting.android.live.common.lib.avatarcache.ChatUserAvatarCache;
import com.ximalaya.ting.android.live.common.lib.configcenter.LiveSettingManager;
import com.ximalaya.ting.android.live.common.lib.configcenter.entity.LiveAnimPlayerSetting;
import com.ximalaya.ting.android.live.common.lib.gift.anim.callback.IAnimationCallback;
import com.ximalaya.ting.android.live.common.lib.gift.anim.contants.AnimResourceType;
import com.ximalaya.ting.android.live.common.lib.gift.anim.eva.LiveEvaView;
import com.ximalaya.ting.android.live.common.lib.gift.anim.model.GiftShowTask;
import com.ximalaya.ting.android.live.common.lib.gift.anim.mp4.Mp4GiftView;
import com.ximalaya.ting.android.live.common.lib.gift.anim.svg.SVGAView;
import com.ximalaya.ting.android.live.common.lib.gift.anim.webp.FloatingImageView;
import com.ximalaya.ting.android.live.common.lib.gift.anim.webp.LiveWebpGiftView;
import com.ximalaya.ting.android.live.common.lib.gift.download.AnimationPathSelector;
import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.live.common.lib.manager.anim.play.AnimPlayQueueManager;
import com.ximalaya.ting.android.live.common.lib.manager.anim.play.IAnimStateCallback;
import com.ximalaya.ting.android.live.common.lib.manager.anim.play.IPlayAnimTask;
import com.ximalaya.ting.android.live.common.lib.manager.anim.play.IResolveTaskView;
import com.ximalaya.ting.android.live.common.lib.manager.broadcast.LiveLocalBroadcastManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveTextUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.common.view.widget.RelativeLayoutEx;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.ref.WeakReference;
import java.util.Locale;
import java.util.Map;

/**
 * 播放礼物特效的自定义View，支持特效 svga、mp4 , yyeva, webp。
 * Note：
 * 1、基于礼物特效改进，礼物特效支持 svga、mp4 和 yyeva, webp 四种格式
 * 2、当播放一个礼物特效失败时，降级为直播间泡泡条特效；失败的原因包括礼物特效资源不存在、自定义View内部出现异常等
 * 3、需要将礼物特效播放关键路径打点日志上传，上传到 APM 系统
 *
 * <AUTHOR>
 */
public class SuperGiftLayout extends RelativeLayoutEx
        implements IResolveTaskView, IAnimationCallback {
    private static final String TAG = "SuperGiftLayout";

    public static final String XDCS_TAG_BIG_GIFT = "BigGift";

    public static final int MESSAGE_ON_START = 0;
    public static final int MESSAGE_ON_STOP = 1;
    public static final int MESSAGE_ON_ERROR = 2;
    public static final int MESSAGE_ON_DESTROY = 3;
    public static final int MESSAGE_ON_ANIMATION_START = 4;

    public static final String MULTI_SEND = "x";

    /**
     * 播放 svga 特效的 View
     */
    private SVGAView mSVGAView;
    /**
     * evaView
     */
    private LiveEvaView mEvaView;
    /**
     * 播放 mp4 特效的 View
     */
    private Mp4GiftView mMp4GiftView;
    /**
     * 打赏用户的头像
     */
    protected RoundImageView mSenderAvatar;
    /**
     * 打赏用户的昵称
     */
    protected TextView mSenderNickname;
    protected TextView liveSend;
    /**
     * 送礼描述信息，如"送出海洋之心"
     * 或者 收礼人昵称
     */
    protected TextView mSendDescContent;

    private ImageView mGiftIcon;

    /**
     * 打赏礼物的数量，如"X66"
     */
    protected TextView mGiftNum;
    /**
     * 包含打赏用户的头像、昵称、礼物数量和描述信息的容器，位于屏幕头部
     */
    private RelativeLayout mHeader;

    // 非礼物底部条
    private RelativeLayout mNoHeader;
    private TextView mNoGiftTips;


    /**
     * 播放 mp4 特效的 View 的夫容器
     */
    private FrameLayout mMp4Root;

    /**
     * 用来标识 SuperGiftLayout 是否完成 UI 初始化
     */
    boolean childHasInit = false;
    /**
     * 内部 Handler，用来分发处理生命周期逻辑
     */
    private InnerHandler mHandler;

    /**
     * 用来描述动效的任务，由任务队列管理器管理。当收到礼物消息时被构造入队，同时按一定策略触发出队来播放礼物特效
     */
    protected GiftShowTask mCurrentTask;

    /**
     * 用来标识 SuperGiftLayout 是否 Attach to window
     */
    private boolean hasAttachToWindow = false;
    /**
     * 用户来标识 SuperGiftLayout 是否销毁
     */
    private boolean isDestroy = false;
    /**
     * 动画播放状态回调
     */
    private ISuperGiftCallback mCallback;
    /**
     * 任务状态回调
     */
    private IAnimStateCallback mTaskStateCallback;
    /**
     * 用来标识是否执行特效播放任务中
     */
    private volatile boolean isTaskProcessing = false;
    /**
     * 用来记录礼物特效当前已播放次数
     */
    protected int mCurrentNumberIndex = 0;
    /**
     * 用来记录礼物特效总的播放次数
     */
    protected long mCurrentGiftShowTimes = 0;
    /**
     * 用来标识 SuperGiftLayout 是否处于 Paused 状态，由外部 Activity 或 Fragment 设置，
     * 同 Activity 或 Fragment 的生命周期保持一致
     */
    private volatile boolean isPaused = false;
    private BaseGiftLoader mGiftLoader;
    /**
     * 是否来自动效层
     */
    private boolean isFromAnimLayer;

    private LiveWebpGiftView liveWebpGiftView;


    public interface IRoomSeatPositionCallback {
        @Nullable
        Map<Long, Rect> roomSeatPositionCallback();
    }

    public void setIRoomSeatPositionCallback (IRoomSeatPositionCallback callback){
        if (liveWebpGiftView != null) {
            liveWebpGiftView.setRoomSeatPositionCallBack(callback);
        }
    }

    public SuperGiftLayout(Context context) {
        this(context, null);
    }

    public SuperGiftLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initMySelf();
    }

    public SuperGiftLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    private void initMySelf() {
        mHandler = new InnerHandler(this);
        LayoutInflater.from(getContext()).inflate(R.layout.livecommon_layout_super_gift_header, this);
        mHeader = findViewById(R.id.live_header);

        mSenderAvatar = findViewById(R.id.live_super_gift_avatar);
        mSenderNickname = findViewById(R.id.live_super_gift_name);
        liveSend = findViewById(R.id.live_send);
        mSendDescContent = findViewById(R.id.live_super_gift_send_tip);
        mGiftIcon = findViewById(R.id.live_super_gift_icon);
        mGiftNum = findViewById(R.id.live_super_gift_send_num);
        mSVGAView = findViewById(R.id.live_super_svg);
        mSVGAView.setAnimationCallback(this);
        mEvaView = findViewById(R.id.live_eva_view);
        mEvaView.setAnimationCallback(this);

        liveWebpGiftView = findViewById(R.id.live_webp_gift_view);
        FloatingImageView liveFloatingView = findViewById(R.id.live_floating_view);
        FrameLayout webpBloomLayout = findViewById(R.id.live_webp_bloom_layout);
        liveWebpGiftView.setAnimationCallback(this);
        liveWebpGiftView.setFloatIngView(liveFloatingView, webpBloomLayout);

        mNoHeader = findViewById(R.id.live_no_header);
        mNoGiftTips = findViewById(R.id.live_no_gift_tips);

        childHasInit = true;
    }

    /**
     * 设置礼物特效播放回调
     *
     * @param Callback 礼物特效播放回调
     */
    public void setCallback(ISuperGiftCallback Callback) {
        this.mCallback = Callback;
    }

    /**
     * 设置礼物加载器
     *
     * @param giftLoader 礼物加载器
     */
    public void setGiftLoader(BaseGiftLoader giftLoader) {
        mGiftLoader = giftLoader;
        if (null != mSVGAView) {
            mSVGAView.setGiftLoader(giftLoader);
        }
        if (mEvaView != null) {
            mEvaView.setGiftLoader(giftLoader);
        }
        if (liveWebpGiftView != null) {
            liveWebpGiftView.setGiftLoader(giftLoader);
        }
    }

    public void setIsFromAnimLayer(boolean isFromAnimLayer) {
        this.isFromAnimLayer = isFromAnimLayer;
    }


    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        isDestroy = false;
        hasAttachToWindow = true;

        Logger.i(TAG, "onAttachedToWindow" + ", hashCode = " + hashCode());
    }

    /**
     * 重置 SuperGiftLayout
     */
    public void reset() {
        setTaskProcessing(false);
        resetIndex();
        if (mSVGAView != null) {
            mSVGAView.stop();
        }
        if (null != mMp4GiftView) {
            mMp4GiftView.stop();
        }
        setVisibility(GONE);
        UIStateUtil.hideViews(mHeader, mNoHeader);
    }

    @Override
    protected void onDetachedFromWindow() {
        hasAttachToWindow = false;
        super.onDetachedFromWindow();

        Logger.i(TAG, "onDetachedFromWindow, isDestroy = " + isDestroy
                + ", hashCode = " + hashCode());

        if (!isDestroy) {
            destroy();
        }
    }

    @Override
    public void resolveTask(IPlayAnimTask msg, IAnimStateCallback callback) {
        Logger.d(TAG, "resolveTask = " + msg);
        if (msg instanceof GiftShowTask) {
            mTaskStateCallback = callback;
            setGiftTask((GiftShowTask) msg);
        } else {
            if (callback != null) {
                if (msg != null) {
                    callback.onAnimError("msg isn't GiftShowTask:" + msg.getClass());
                } else {
                    callback.onAnimError("msg isn't GiftShowTask, msg is null");
                }
            }
        }
    }

    @Override
    public void switchRoom() {
        if (mSVGAView != null) {
            mSVGAView.stop();
        }
        if (mMp4GiftView != null) {
            mMp4GiftView.stop();
        }
        if (mHandler != null) {
            mHandler.removeMessages(MESSAGE_ON_ERROR);
            mHandler.removeMessages(MESSAGE_ON_START);
            mHandler.removeMessages(MESSAGE_ON_STOP);
            mHandler.removeMessages(MESSAGE_ON_DESTROY);
            mHandler.removeMessages(MESSAGE_ON_ANIMATION_START);
        }
        setTaskProcessing(false);
        resetIndex();
        setVisibility(GONE);
    }

    @Override
    public void showErrorMessage(IPlayAnimTask task) {
        if (mCallback != null && task instanceof GiftShowTask) {
            GiftShowTask giftTask = (GiftShowTask) task;
            mCallback.onFail(giftTask, "错误状态码：" + task.getResourceDownloadStatus());
        }
    }

    @Override
    public void release() {
        LiveXdcsUtil.doXDCS(SuperGiftLayout.XDCS_TAG_BIG_GIFT, "SuperGiftLayout release");

        if (mSVGAView != null) {
            mSVGAView.stop();
        }
        if (mMp4GiftView != null) {
            mMp4GiftView.stop();
        }
        if (mHandler != null) {
            mHandler.removeMessages(MESSAGE_ON_ERROR);
            mHandler.removeMessages(MESSAGE_ON_START);
            mHandler.removeMessages(MESSAGE_ON_STOP);
            mHandler.removeMessages(MESSAGE_ON_DESTROY);
            mHandler.removeMessages(MESSAGE_ON_ANIMATION_START);
        }
        setTaskProcessing(false);
        resetIndex();
        setVisibility(GONE);
    }

    /**
     * 设置播放礼物特效任务，触发特效播放
     *
     * @param task 播放礼物特效任务
     */
    public void setGiftTask(final GiftShowTask task) {
        Logger.d(TAG, "setGiftTask = " + task);

        if (task == null) {
            if (mTaskStateCallback != null) {
                mTaskStateCallback.onAnimError("task is null");
            }
            if (mCallback != null) {
                mCallback.onFail(null, "task is null");
            }
            return;
        }

        mCurrentTask = task;
        if (task.isComboBigGift) {
            mCurrentGiftShowTimes = 1;
        } else {
            if (UserInfoMannage.getUid() == task.senderUid) {
                mCurrentGiftShowTimes = mCurrentTask.getGiftNum();
            } else {
                int animationPlayCount = LiveSettingManager.getLiveAnimationPlayCountConfig();
                if (animationPlayCount == 0 || mCurrentTask.getGiftNum() < animationPlayCount) {
                    mCurrentGiftShowTimes = mCurrentTask.getGiftNum();
                } else {
                    mCurrentGiftShowTimes = animationPlayCount;
                }
            }
        }
        String animationPath;
        if (task.getResourceTypeAndPath() != null) {
            animationPath = task.getResourceTypeAndPath().second;
        } else {
            animationPath = AnimationPathSelector.getAnimResTypeAndDownloadPathByTemplateId(task.animationId).second;
        }
        if (TextUtils.isEmpty(animationPath)) {
            CustomToast.showDebugFailToast("礼物动画地址获取失败！" + task.giftId);
            LiveXdcsUtil.doXDCS(XDCS_TAG_BIG_GIFT, "礼物动画地址失败,资源id：" + task.animationId);
            ConnectLogUtilWrapper.log(task.getBizType(), "礼物动画地址失败,资源id：" + task.animationId);
        }

        scheduleNextAnimation();

        if (null != mTaskStateCallback) {
            mTaskStateCallback.onAnimStart();
        }
    }

    /**
     * 销毁 SuperGiftLayout，以下场景会销毁该 View。
     * <p>
     * 1、当SuperGiftLayout detach window 时销毁
     * 2、当调用方主动调用 destroy 方法时销毁
     */
    public void destroy() {
        if (!childHasInit) {
            return;
        }
        if (mSVGAView != null) {
            mSVGAView.stop();
        }
        if (mMp4GiftView != null) {
            mMp4GiftView.destroy();
        }
        setTaskProcessing(false);
        resetIndex();
        setVisibility(GONE);
        isDestroy = true;

        Logger.i(TAG, "destroy" + ", hashCode = " + hashCode());
    }

    private boolean canUpdateUi() {
        return !isDestroy && hasAttachToWindow;
    }

    /**
     * 根据播放特效任务信息调度播放过程
     */
    protected void scheduleNextAnimation() {
        Logger.i(TAG, "scheduleNextAnimation");

        if (isTaskProcessing() || !canUpdateUi() || !childHasInit) {
            // 上报日志
            LiveXdcsUtil.doXDCS(SuperGiftLayout.XDCS_TAG_BIG_GIFT, TAG
                    + ", isTaskProcessing=" + isTaskProcessing()
                    + ", canUpdateUi=" + canUpdateUi()
                    + ", isDestroy=" + isDestroy
                    + ", hasAttachToWindow=" + hasAttachToWindow);

            if (mTaskStateCallback != null) {
                mTaskStateCallback.onAnimError(TAG
                        + ", isTaskProcessing=" + isTaskProcessing()
                        + ", canUpdateUi=" + canUpdateUi()
                        + ", isDestroy=" + isDestroy
                        + ", hasAttachToWindow=" + hasAttachToWindow);
            }
            return;
        }

        if ((mCurrentNumberIndex++) < mCurrentGiftShowTimes) {
            if (getVisibility() != VISIBLE) {
                setVisibility(VISIBLE);
            }
            setTaskProcessing(true);

            ISuperGiftView processView = getProcessView();

            if (processView == null || mCurrentTask == null) {
                LiveXdcsUtil.doXDCS(SuperGiftLayout.XDCS_TAG_BIG_GIFT,
                        TAG + "scheduleNextAnimation error, mProcessView="
                                + processView + ", mCurrentTask?=" + mCurrentTask);

                setTaskProcessing(false);
                resetIndex();

                if (null != mTaskStateCallback) {
                    mTaskStateCallback.onAnimError(TAG + "scheduleNextAnimation error, mProcessView="
                            + processView + ", mCurrentTask?=" + mCurrentTask);
                }

                return;
            }

            ConnectLogUtilWrapper.log(mCurrentTask.getBizType(), "processView:" + processView.getClass());
            processView.setCurrentNumberIndex(mCurrentNumberIndex);
            processView.preparePackAndStart(mCurrentTask, new ISuperGiftView.ProcessCallback() {
                @Override
                public void onFail(GiftShowTask task, String errorMsg) {
                    setTaskProcessing(false);
                    resetIndex();
                    setVisibility(GONE);

                    LiveXdcsUtil.doXDCS(SuperGiftLayout.XDCS_TAG_BIG_GIFT,
                            "SuperGiftLayout, preparePackAndStart onFail, GiftShowTask=" + task);

                    if (null != mTaskStateCallback) {
                        mTaskStateCallback.onAnimError(errorMsg);
                    }

                    if (!isAttachedToWindowEx()) {
                        return;
                    }

                    if (task != null) {
                        Pair<Integer, String> animResTypeAndPath;
                        if (task.getResourceTypeAndPath() != null) {
                            animResTypeAndPath = task.getResourceTypeAndPath();
                        } else {
                            animResTypeAndPath = AnimationPathSelector.getAnimResTypeAndDownloadPathByTemplateId(task.animationId);
                        }

                        String animationPath = animResTypeAndPath.second;
                        ConnectLogUtilWrapper.log(task.getBizType(), "大动画解析失败！对应的animateId为：" + task.animationId + ", 对应的animatePath为：" + animationPath);
                        Logger.i("动画", "正在执行的动画优先级= " + task.getPriority() + ", 播放异常：" + task.getAnimationId() + ", 异常信息：" + errorMsg);
                        // H265 task降级特殊处理
                        if (animResTypeAndPath.first == AnimResourceType.TYPE_MP4_H265) {
                            GiftShowTask h264Task = GiftShowTask.convertOldH265TaskToH264(task);
                            AnimPlayQueueManager.getInstance().addTask(h264Task);
                            Logger.i(TAG, "H265 Gift Play Error!, ErrMsg = " + errorMsg);
                        } else {
                            if (mCallback != null) {
                                mCallback.onFail(task, errorMsg);
                            }

                            CustomToast.showDebugFailToast("大动画解析失败！对应的animateId为：" + task.animationId);
                            Logger.i(TAG, "大动画解析失败！对应的animateId为：" + task.animationId + ", 对应的animatePath为：" + animationPath);
                        }
                    }
                }

                @Override
                public void destroy() {
                    Logger.i(TAG, "preparePackAndStart destroy");
                    LiveXdcsUtil.doXDCS(SuperGiftLayout.XDCS_TAG_BIG_GIFT,
                            TAG + "preparePackAndStart destroy ");

                    setTaskProcessing(false);
                    resetIndex();
                }

                @Override
                public boolean isPause() {
                    return isPaused;
                }

                @Override
                public boolean attached() {
                    return isAttachedToWindowEx();
                }
            });
        } else {
            resetIndex();
            if (null != mTaskStateCallback) {
                mTaskStateCallback.onAnimEnd();
            }
            UIStateUtil.hideViews(mHeader, mNoHeader);
        }
    }

    /**
     * 根据要播放数据路径选择合适的播放布局，包括：本地 svga、本地 mp4、网络 svga、网络 mp4、网络 yyeva
     */
    private ISuperGiftView getProcessView() {
        if (mCurrentTask == null) {
            return null;
        }

        boolean adjust = isAdjustPlayerHeight(mCurrentTask);

        if (!TextUtils.isEmpty(mCurrentTask.localMp4Path)) {

            initMp4GiftView(adjust);

            return mMp4GiftView;
        }

        if (!TextUtils.isEmpty(mCurrentTask.localSvgPath)) {
            adjustPlayerViewHeight(adjust, mSVGAView);
            return mSVGAView;
        }
        Pair<Integer, String> animResTypeAndPath;
        if (mCurrentTask.getResourceTypeAndPath() != null) {
            animResTypeAndPath = mCurrentTask.getResourceTypeAndPath();
        } else {
            animResTypeAndPath = AnimationPathSelector.getAnimResTypeAndDownloadPathByTemplateId(mCurrentTask.animationId);
        }
        if (animResTypeAndPath.first == AnimResourceType.TYPE_EVA) {
            return mEvaView;
        } else if (animResTypeAndPath.first == AnimResourceType.TYPE_WEBP) {
            return liveWebpGiftView;
        } else if (!TextUtils.isEmpty(animResTypeAndPath.second)) {
            if ("mp4".equalsIgnoreCase(XmPictureUrlUtil.getFileSuffix(animResTypeAndPath.second))) {
                initMp4GiftView(adjust);
                return mMp4GiftView;
            }
        }

        // 默认返回 SVGAView
        adjustPlayerViewHeight(adjust, mSVGAView);
        return mSVGAView;
    }

    /**
     * 调整播放器的高度
     */
    private void adjustPlayerViewHeight(boolean adjustPlayerHeight, View view) {
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout
                .LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT
                , ViewGroup.LayoutParams.MATCH_PARENT);

        layoutParams.addRule(Gravity.CENTER);
        if (adjustPlayerHeight) {
            LiveAnimPlayerSetting liveAnimPlayerSetting = LiveSettingManager.getLiveAnimPlayerSetting();
            if (liveAnimPlayerSetting == null) {
                layoutParams.topMargin = BaseUtil.dp2px(getContext(), 130);
            } else {
                layoutParams.topMargin = BaseUtil.dp2px(getContext(), liveAnimPlayerSetting.getShifting());
            }
        }
        view.setLayoutParams(layoutParams);
    }

    /**
     * 是否调整播放器的高度
     */
    private boolean isAdjustPlayerHeight(GiftShowTask showTask) {
        if (mGiftLoader != null) {
            LiveAnimPlayerSetting liveAnimPlayerSetting = LiveSettingManager.getLiveAnimPlayerSetting();
            if (liveAnimPlayerSetting == null) {
                return false;
            }
            if (liveAnimPlayerSetting.getOpen()) {
                GiftInfoCombine.GiftInfo giftInfo = mGiftLoader.getGift(showTask.giftId);
                if (giftInfo == null) {
                    return false;
                }
                CommonLiveLogger.d(TAG, "giftInfo.xiDiamondWorth:" + giftInfo.xiDiamondWorth);
                return giftInfo.xiDiamondWorth < liveAnimPlayerSetting.getWorth() && giftInfo.animationId > 0;
            }
        }
        return false;
    }

    private void initMp4GiftView(boolean adjustPlayerHeight) {
        if (null == mMp4Root) {
            mMp4Root = findViewById(R.id.live_fl_super_mp4);
        }

        mMp4Root.removeAllViews();

        mMp4GiftView = new Mp4GiftView(getContext(), null);
        if (!isFromAnimLayer) {
            adjustPlayerViewHeight(adjustPlayerHeight, mMp4GiftView);
        }
        mMp4GiftView.setScaleType(Constants.CENTER_CROP);
        mMp4GiftView.setAnimationCallback(this);
        mMp4GiftView.setTopLinearAlphaRange(0.2f);
        mMp4GiftView.setAutoDismiss(isFromAnimLayer);

        mMp4Root.addView(mMp4GiftView);
    }

    private void resetIndex() {
        mCurrentNumberIndex = 0;
        mCurrentGiftShowTimes = 0;
        mCurrentTask = null;
    }

    /**
     * 礼物特效播放回调接口
     */
    public interface ISuperGiftCallback {
        /**
         * 播放失败时回调该方法
         *
         * @param task 播放礼物特效任务信息
         */
        void onFail(GiftShowTask task, String errorMsg);

        /**
         * 开始播放时回调
         *
         * @param giftId 礼物id
         */
        void onStart(long giftId);

        /**
         * 播放结束时回调
         *
         * @param giftId 礼物id
         */
        void onStop(long giftId);
    }

    @Override
    public void onStart() {
        if (!childHasInit) {
            return;
        }
        if (mCurrentTask != null) {
            long giftId = mCurrentTask.giftId;
            Message message = mHandler.obtainMessage(MESSAGE_ON_START);
            message.arg1 = (int) giftId;
            message.sendToTarget();
        }
    }

    @Override
    public void onStop() {
        if (!childHasInit) {
            return;
        }
        if (!isTaskProcessing) {
            return;
        }

        if (mCurrentTask == null) {
            return;
        }
        long giftId = mCurrentTask.giftId;
        Message message = mHandler.obtainMessage(MESSAGE_ON_STOP);
        message.arg1 = (int) giftId;
        message.sendToTarget();
    }

    @Override
    public void onDestroy() {

    }

    @Override
    public void onAlphaAnimationStart() {
        if (!childHasInit) {
            return;
        }
        mHandler.obtainMessage(MESSAGE_ON_ANIMATION_START).sendToTarget();
    }

    public void pause() {

    }

    public void resume() {
        isPaused = false;
    }

    @Override
    public void onError(int errorCode, Object o) {
        LiveXdcsUtil.doXDCS("BigGift",
                "play anim error, code=" + errorCode, true);
        if (!childHasInit) {
            return;
        }
        if (!isTaskProcessing) {
            return;
        }
        Message message = mHandler.obtainMessage(MESSAGE_ON_ERROR);
        message.obj = o;
        message.arg1 = errorCode;
        message.sendToTarget();
    }

    public synchronized boolean isTaskProcessing() {
        return isTaskProcessing;
    }

    public synchronized void setTaskProcessing(boolean taskProcessing) {
        isTaskProcessing = taskProcessing;
    }

    /**
     * 内部 Handler 处理特效的生命周期
     */
    private static class InnerHandler extends Handler {
        private final WeakReference<SuperGiftLayout> mLayoutRef;

        InnerHandler(SuperGiftLayout layout) {
            super(Looper.getMainLooper());
            mLayoutRef = new WeakReference<>(layout);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            SuperGiftLayout layout = mLayoutRef.get();
            if (layout == null || !layout.canUpdateUi()) {
                return;
            }

            switch (msg.what) {
                case MESSAGE_ON_START:
                    layout.handleOnStart(msg);
                    break;

                case MESSAGE_ON_STOP:
                    layout.handleOnStop(msg);
                    break;

                case MESSAGE_ON_ERROR:
                    layout.handleOnError(msg);
                    break;

                case MESSAGE_ON_DESTROY:
                    layout.handleOnDestroy();
                    break;

                case MESSAGE_ON_ANIMATION_START:
                    layout.handleOnAnimationStart();
                    break;

                default:
                    break;
            }
        }
    }

    // 从InnerHandler移动出来的处理方法，由于InnerHandler已改为静态类，不能直接访问外部类方法
    private void handleOnStart(Message msg) {
        Logger.i(TAG, "-------------       HandleMessage   MESSAGE_ON_START  ------------- ");

        if (!canUpdateUi() || mCurrentTask == null) {
            return;
        }

        Logger.i("动画", "正在执行的动画优先级= " + mCurrentTask.getPriority() + ",动画id:" + mCurrentTask.animationId + ", 详情= " + mCurrentTask);
        ConnectLogUtilWrapper.log(mCurrentTask.getBizType(), "AnimPlayQueueManager execute onAnimStart，task = " + mCurrentTask);

        if (mCallback != null) {
            long giftId = msg.arg1;
            mCallback.onStart(giftId);
            giftAnimStartToVibrate(giftId, mCurrentTask);
        }

        if (mCurrentTask.isFromNoGiftMessage) {
            showNoGiftUi();
        } else {
            showGiftUi();
        }
    }

    private void handleOnStop(Message msg) {
        Logger.i(TAG, "-------------       HandleMessage   MESSAGE_ON_STOP  ------------- ");

        setTaskProcessing(false);
        if (!isAttachedToWindowEx()) {
            return;
        }

        if (mCallback != null) {
            long giftId = msg.arg1;
            mCallback.onStop(giftId);
        }
        if (!isFromAnimLayer) {
            if (null != mMp4GiftView) {
                removeView(mMp4GiftView);
                mMp4GiftView.release();
                mMp4GiftView = null;
            }
            if (null != mMp4Root) {
                mMp4Root.removeAllViews();
            }
        }

        scheduleNextAnimation();
    }

    private void handleOnError(Message msg) {
        Logger.i(TAG, "-------------       HandleMessage   MESSAGE_ON_ERROR  -------------, errorCode = " + msg.arg1 + ", obj = " + msg.obj);
        boolean isRemark = LiveSettingManager.handleOnError();
        LiveXdcsUtil.doXDCS(SuperGiftLayout.XDCS_TAG_BIG_GIFT, "errorCode= " + msg.arg1 + ", obj = " + msg.obj + ", isRemark = " + isRemark);
        setTaskProcessing(false);
        if (!isAttachedToWindowEx()) {
            return;
        }
        UIStateUtil.hideViews(mHeader, mNoHeader);

        // 播放错误的时候，如果移除了，会导致剩余的无法播放. 这里先配置配置中心开关观察线上情况
        if (!isRemark) {
            if (null != mMp4GiftView) {
                removeView(mMp4GiftView);
                mMp4GiftView.release();
                mMp4GiftView = null;
            }
        }

        scheduleNextAnimation();
    }

    private void handleOnDestroy() {
        Logger.i(TAG, "-------------       HandleMessage   MESSAGE_ON_DESTROY  ------------- ");

        setTaskProcessing(false);
        if (mTaskStateCallback != null) {
            mTaskStateCallback.onAnimEnd();
        }
        if (!isAttachedToWindowEx()) {
            return;
        }

        UIStateUtil.hideViews(mHeader, mNoHeader);
        resetIndex();
    }

    private void handleOnAnimationStart() {
        Logger.i(TAG, "-------------       HandleMessage   MESSAGE_ON_ANIMATION_START  ------------- ");
    }

    /**
     * 展示非礼物动画UI
     */
    private void showNoGiftUi() {
        UIStateUtil.hideViews(mHeader);
        if (!TextUtils.isEmpty(mCurrentTask.getBigSvgMessageText())) {
            UIStateUtil.showViews(mNoHeader);
            mNoGiftTips.setText(mCurrentTask.getBigSvgMessageText());
        } else {
            UIStateUtil.hideViews(mNoHeader);
        }
    }

    /**
     * 展示礼物大动画UI
     */
    private void showGiftUi() {
        if (mCurrentTask.senderUid <= 0) {
            return;
        }
        UIStateUtil.showViews(mHeader);

        ChatUserAvatarCache.self().displayImage(mSenderAvatar, mCurrentTask.senderUid, R.drawable.live_default_avatar_88);
        if (mGiftLoader != null) {
            mGiftLoader.getGift(mCurrentTask.giftId, new BaseGiftLoader.OnGetGiftCallback() {
                @Override
                public void onSuccess(GiftInfoCombine.GiftInfo info) {
                    if (!canUpdateUi()) {
                        return;
                    }
                    if (info != null) {
                        ImageManager.from(getContext()).displayImage(mGiftIcon, info.coverPath, R.drawable.live_common_ic_gift_default);
                    }
                }

                @Override
                public void onError(long giftId) {

                }
            });
        }

        mSenderNickname.setText(mCurrentTask.senderName);

        if (TextUtils.isEmpty(mCurrentTask.getReceiverName())) {
            liveSend.setText("送出");
            mSendDescContent.setTextColor(ContextCompat.getColor(getContext(), R.color.live_color_b3ffffff));
            mSendDescContent.setText(String.format("%s", mCurrentTask.giftName));
        } else {
            liveSend.setText("送给");
            mSendDescContent.setTextColor(ContextCompat.getColor(getContext(), R.color.live_color_ffFAE244));
            mSendDescContent.setText(String.format("%s", mCurrentTask.getReceiverName()));
        }


        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mGiftIcon.getLayoutParams();
        long sendGiftNumShow = mCurrentTask.getSendGiftNum();
        // 由于互动礼物被设置成连击礼物， SendGiftNum会被设置为1，因此这里需要特殊处理
        if (mCurrentTask.isInteractiveGift()) {
            sendGiftNumShow =  mCurrentTask.getInteractiveSendGiftNum();
        }

        if (sendGiftNumShow > 1) {
            UIStateUtil.showViews(mGiftNum);
            mGiftNum.setText(String.format(Locale.getDefault(), "%s%d", MULTI_SEND, sendGiftNumShow));
            LiveTextUtil.setTypeface(mGiftNum, LiveTextUtil.FONT_NAME_LIVE_NUMBER_BOLD);
            if (layoutParams != null) {
                layoutParams.rightMargin = BaseUtil.dp2px(getContext(), 4);
                mGiftIcon.setLayoutParams(layoutParams);
            }
        } else {
            UIStateUtil.hideViews(mGiftNum);
            if (layoutParams != null) {
                layoutParams.rightMargin = BaseUtil.dp2px(getContext(), 8);
                mGiftIcon.setLayoutParams(layoutParams);
            }
        }
    }

    /**
     * 礼物动画开始时，发送震动广播
     *
     * @param giftId 礼物id
     * @param task   任务信息
     */
    private void giftAnimStartToVibrate(long giftId, GiftShowTask task) {
        // giftId <= 0 代表是非礼物特效
        if (giftId <= 0) {
            return;
        }
        Intent intent = new Intent(LiveLocalBroadcastManager.EXTRA.VIBRATE_GIFT_ACTION);
        intent.putExtra(LiveLocalBroadcastManager.EXTRA.GIFT_ID, giftId);
        if (task != null) {
            intent.putExtra(LiveLocalBroadcastManager.EXTRA.GIFT_NAME, task.giftName);
            intent.putExtra(LiveLocalBroadcastManager.EXTRA.GIFT_SENDER_NAME, task.senderName);
        }
        //设置礼物价值，可能为 0
        if (mGiftLoader != null) {
            mGiftLoader.getGift(giftId, new BaseGiftLoader.OnGetGiftCallback() {
                @Override
                public void onSuccess(GiftInfoCombine.GiftInfo info) {
                    if (null != info) {
                        intent.putExtra(GIFT_PRICE, info.getPrice());
                    }
                    LiveLocalBroadcastManager.send(intent);
                }

                @Override
                public void onError(long giftId) {
                    LiveLocalBroadcastManager.send(intent);
                }
            });
        }
        Logger.i(TAG, "giftAnimStartToVibrate, onStart ");
    }

}
