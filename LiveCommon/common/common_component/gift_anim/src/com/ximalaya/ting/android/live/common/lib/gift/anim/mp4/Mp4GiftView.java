package com.ximalaya.ting.android.live.common.lib.gift.anim.mp4;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.android.exoplayer2.ExoPlaybackException;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.live.common.lib.gift.anim.ISuperGiftView;
import com.ximalaya.ting.android.live.common.lib.gift.anim.SuperGiftLayout;
import com.ximalaya.ting.android.live.common.lib.gift.anim.callback.IAnimationCallback;
import com.ximalaya.ting.android.live.common.lib.gift.anim.contants.AnimErrorCodeConstants;
import com.ximalaya.ting.android.live.common.lib.gift.anim.contants.AnimResourceType;
import com.ximalaya.ting.android.live.common.lib.gift.anim.model.GiftShowTask;
import com.ximalaya.ting.android.live.common.lib.gift.download.AnimationPathSelector;
import com.ximalaya.ting.android.live.common.lib.manager.anim.play.IPlayAnimTask;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.io.File;
import java.lang.ref.WeakReference;

/**
 * 播放 mp4 动画自定义View
 *
 * <AUTHOR>
 */
public class Mp4GiftView extends LiveAlphaMovieView implements ISuperGiftView, LiveAlphaMovieView
        .OnVideoStartedListener, LiveAlphaMovieView.OnVideoEndedListener {
    private IAnimationCallback mFrameCallback;
    private final Mp4GiftParser mMp4GiftParser;
    private WeakReference<ProcessCallback> mCallbackWeakReference;
    /**
     * 动画结束时候，是否自动消失
     */
    private boolean isAutoDismiss;

    public Mp4GiftView(Context context) {
        this(context, null);
    }

    public Mp4GiftView(Context context, AttributeSet attrs) {
        super(context, attrs);
        setOnVideoStartedListener(this);
        setOnVideoEndedListener(this);
        mMp4GiftParser = new Mp4GiftParser();
    }

    @Override
    public void onVideoStarted() {
        if (mFrameCallback != null) {
            mFrameCallback.onStart();
        }
    }

    public void setAutoDismiss(boolean isAutoDismiss) {
        this.isAutoDismiss = isAutoDismiss;
    }


    @Override
    public void onVideoEnded() {
        if (isAutoDismiss) {
            if (mFrameCallback != null) {
                mFrameCallback.onAlphaAnimationStart();
                mFrameCallback.onStop();
            }
        } else {
            ObjectAnimator objectAnimator = AnimationUtil.buildAlphaObjectAnimator(this, 1.0F, 0.0F);
            objectAnimator.setDuration(500);
            objectAnimator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    if (mFrameCallback != null) {
                        mFrameCallback.onStop();
                    }
                }
            });
            if (mFrameCallback != null) {
                mFrameCallback.onAlphaAnimationStart();
            }
            objectAnimator.start();
        }
    }

    @Override
    public void setAnimationCallback(IAnimationCallback callback) {
        this.mFrameCallback = callback;
    }

    @Override
    public void destroy() {
        stop();
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void preparePackAndStart(final GiftShowTask task, final ProcessCallback callback) {
        mCallbackWeakReference = new WeakReference<>(callback);
        String animationPath;
        if (task.getResourceTypeAndPath() != null) {
            animationPath = task.getResourceTypeAndPath().second;
        } else {
            animationPath = AnimationPathSelector.getAnimResTypeAndDownloadPathByTemplateId(task.animationId).second;
        }
        if (!TextUtils.isEmpty(animationPath)) {

            mMp4GiftParser.parse(task, new IDataCallBack<String>() {
                @Override
                public void onSuccess(@Nullable String object) {
                    if (TextUtils.isEmpty(object)) {
                        if (callback != null) {
                            callback.onFail(task, "mMp4GiftParser object isEmpty");
                        }
                    } else {
                        playGiftAnimPath(object, task);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (callback != null) {
                        callback.onFail(task, "code:" + code + ", message:" + message);
                    }

                    if (mFrameCallback != null) {
                        mFrameCallback.onError(code, message);
                    }

                    LiveXdcsUtil.doXDCS(SuperGiftLayout.XDCS_TAG_BIG_GIFT,
                            "Mp4GiftView, preparePackAndStart onError, code=" + code
                                    + ", message=" + message);
                }
            });

            setAlpha(1.0f);
        } else if (!TextUtils.isEmpty(task.localMp4Path)) {
            mMp4GiftParser.parse(new File(task.localMp4Path), new IDataCallBack<String>() {
                @Override
                public void onSuccess(@Nullable String object) {
                    if (TextUtils.isEmpty(object)) {
                        if (callback != null) {
                            callback.onFail(task, "mMp4GiftParser object isEmpty");
                        }
                    } else {
                        playPath(object);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (callback != null) {
                        callback.onFail(task, "code:" + code + ", message:" + message);
                    }

                    if (mFrameCallback != null) {
                        mFrameCallback.onError(code, message);
                    }

                    LiveXdcsUtil.doXDCS(SuperGiftLayout.XDCS_TAG_BIG_GIFT,
                            "Mp4GiftView, preparePackAndStart onError, code=" + code
                                    + ", message=" + message);
                }
            });

            setAlpha(1.0f);
        } else {
            if (callback != null) {
                callback.onFail(task, "mp4 path empty");
            }

            if (mFrameCallback != null) {
                mFrameCallback.onError(-1, "mp4 path empty");
            }
        }
    }

    @Override
    public void setCurrentNumberIndex(int currentNumberIndex) {

    }

    public void playGiftAnimPath(String path, GiftShowTask task) {
        if (!TextUtils.isEmpty(path) && new File(path).exists()) {

            setOnErrorListener((mp, what, extra, exception) -> {
                boolean isH265;
                if (task.getResourceTypeAndPath() != null) {
                    isH265 = task.getResourceTypeAndPath().first == AnimResourceType.TYPE_MP4_H265;
                } else {
                    isH265 = AnimationPathSelector.getAnimResTypeAndDownloadPathByTemplateId(task.animationId).first == AnimResourceType.TYPE_MP4_H265;
                }

                if (isH265) {

                    boolean isH265FormatErr = false;
                    String exoExceptionStr = "";

                    if (exception instanceof ExoPlaybackException) {
                        ExoPlaybackException exoException = (ExoPlaybackException) exception;
                        if (exoException.type == ExoPlaybackException.TYPE_SOURCE
                                || exoException.type == ExoPlaybackException.TYPE_RENDERER) {
                            //渲染 or 源文件的问题，均需要H265回退处理
                            isH265FormatErr = true;
                        }

                        exoExceptionStr = produceErrorDetail(exoException);
                    }

                    if (mFrameCallback != null) {
                        mFrameCallback.onError(isH265FormatErr ? AnimErrorCodeConstants.AnimResH265NotSupport
                                        : AnimErrorCodeConstants.AnimResH265PlayError,
                                "H265 onError!" + exoExceptionStr);
                    }
                    if (mCallbackWeakReference != null && mCallbackWeakReference.get() != null) {
                        mCallbackWeakReference.get().onFail(task, "H265 onError!" + exoExceptionStr);
                    }


                } else {
                    if (mFrameCallback != null) {
                        mFrameCallback.onError(0, "MediaPlayer onError!");
                    }
                    if (mCallbackWeakReference != null && mCallbackWeakReference.get() != null) {
                        mCallbackWeakReference.get().onFail(task, "MediaPlayer onError!what: " + what + ",extra: " + extra);
                    }
                }


                return false;
            });

            setVideoFromSD(path);
        } else {
            if (mFrameCallback != null) {
                mFrameCallback.onError(0, "path not exists!");
            }
            if (mCallbackWeakReference != null && mCallbackWeakReference.get() != null) {
                mCallbackWeakReference.get().onFail(null, "playPath() path not exists!");
            }
        }
    }

    public void playPath(String path) {
        if (!TextUtils.isEmpty(path) && new File(path).exists()) {
            setOnErrorListener((mp, what, extra) -> {
                if (mFrameCallback != null) {
                    mFrameCallback.onError(0, "MediaPlayer onError!");
                }
                if (mCallbackWeakReference != null && mCallbackWeakReference.get() != null) {
                    mCallbackWeakReference.get().onFail(null, "MediaPlayer onError!what: " + what + ",extra: " + extra);
                }
                return false;
            });
            setVideoFromSD(path);
        } else {
            if (mFrameCallback != null) {
                mFrameCallback.onError(0, "path not exists!");
            }
            if (mCallbackWeakReference != null && mCallbackWeakReference.get() != null) {
                mCallbackWeakReference.get().onFail(null, "playPath() path not exists!");
            }
        }
    }

    public void playPath(IPlayAnimTask showTask) {
        String animationPath;
        if (showTask.getResourceTypeAndPath() != null) {
            animationPath = showTask.getResourceTypeAndPath().second;
        } else {
            animationPath = AnimationPathSelector.getAnimResTypeAndDownloadPathByTemplateId(showTask.getAnimationId()).second;
        }
        if (!TextUtils.isEmpty(animationPath)) {
            mMp4GiftParser.parse(showTask, new IDataCallBack<String>() {
                @Override
                public void onSuccess(@Nullable String object) {
                    if (TextUtils.isEmpty(object)) {
                        if (mFrameCallback != null) {
                            mFrameCallback.onError(0, "资源文件不存在");
                        }
                    } else {
                        playPath(object);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (mFrameCallback != null) {
                        mFrameCallback.onError(code, message);
                    }
                }
            });
        } else {
            if (mFrameCallback != null) {
                mFrameCallback.onError(-1, "mp4 path empty");
            }
        }
    }


    /**
     * 获取exo报错的信息文本
     *
     * @param exception 报错异常
     * @return 信息文本
     */
    private String produceErrorDetail(@NonNull ExoPlaybackException exception) {

        StringBuilder resSb = new StringBuilder("ExoPlaybackException: ");
        switch (exception.type) {
            case ExoPlaybackException.TYPE_SOURCE:
                resSb.append("TYPE_SOURCE, ");
                break;
            case ExoPlaybackException.TYPE_RENDERER:
                resSb.append("TYPE_RENDERER, ");
                break;
            case ExoPlaybackException.TYPE_UNEXPECTED:
                resSb.append("TYPE_UNEXPECTED, ");
                break;
            case ExoPlaybackException.TYPE_REMOTE:
                resSb.append("TYPE_REMOTE, ");
                break;
            default:
                break;
        }

        resSb.append("Time:").append(exception.timestampMs);

        if (exception.getCause() != null) {
            resSb.append("Cause: ").append(exception.getCause().toString()).append("-");
        }

        resSb.append("rendererName: ").append(exception.rendererName != null ? exception.rendererName : "").append(", ")
                .append("rendererIndex: ").append(exception.rendererIndex).append(", ")
                .append("rendererFormatSupport: ").append(exception.rendererFormatSupport).append(" ");
        if (exception.rendererFormat != null) {
            resSb.append("rendererFormat: ").append(exception.rendererFormat);
        }

        return resSb.toString();
    }


}
