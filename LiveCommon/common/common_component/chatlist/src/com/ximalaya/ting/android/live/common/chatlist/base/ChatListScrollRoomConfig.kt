package com.ximalaya.ting.android.live.common.chatlist.base

import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants

/**
 * 直播间与聊天列表上下滑切换直播间相关配置
 *
 * Created by zoey on 2023/8/9.
 * <AUTHOR>
 * @email <EMAIL>
 */
object ChatListScrollRoomConfig {

    /**
     * 上次在聊天列表的事件时间戳，单位毫秒
     */
    @JvmStatic
    var lastChatListMotionEventTs = 0L

    /**
     * 当前距离上次在聊天列表的事件时间间隔，单位毫秒
     */
    @JvmStatic
    val lastChatListMotionEventGap: Long
        get() {
            return System.currentTimeMillis() - lastChatListMotionEventTs
        }

    /**
     * 直播间聊天列表 (已滑到顶部或底部) 由列表滑动到触发上下滑切换直播间最小的时间间隔，单位毫秒
     */
    @JvmStatic
    val chatListActivateScrollRoomGap: Long
        get() {
            return getConfig("chatListFreezeGap", 0).toLong()
        }

    /**
     * 从直播间聊天列表滑动到列表外滑动，触发上下滑切换直播间阻尼增大的最短时间间隔，单位毫秒
     */
    @JvmStatic
    val chatListCriticalConditionGap: Long
        get() {
            return getConfig("chatListCriticalGap", 0).toLong()
        }

    /**
     * 直播间短距离快速滑动 (FLING) 触发的最小距离，越大越难触发，单位 dp
     */
    @JvmStatic
    val minDistanceForFling: Int
        get() {
            return getConfig("minFlingDistance", 65)
        }

    /**
     * 直播间短距离快速滑动 (FLING) 时触发切换直播间的最小速度，越大越难触发，单位 dp
     */
    @JvmStatic
    val minFlingVelocity: Int
        get() {
            return getConfig("minFlingVelocity", 1200)
        }

    /**
     * 边界条件下 (距离滑动消息列表时间间隔在规定时间内) 直播间短距离快速滑动 (FLING) 触发的最小距离，越大越难触发，单位 dp
     */
    @JvmStatic
    val criticalMinDistanceForFling: Int
        get() {
            return getConfig("criticalMinFlingDistance", 130)
        }

    /**
     * 边界条件下 (距离滑动消息列表时间间隔在规定时间内) 直播间短距离快速滑动 (FLING) 时触发切换直播间的最小速度，越大越难触发，单位 dp
     */
    @JvmStatic
    val criticalMinFlingVelocity: Int
        get() {
            return getConfig("criticalMinFlingVelocity", 8000)
        }

    private fun getConfig(key: String, default: Int): Int {
        val configJson = ConfigureCenter.getInstance().getJson(
            CConstants.Group_live.GROUP_NAME, "live_chat_scroll_room_config"
        ) ?: return default

        return configJson.optInt(key, default)
    }
}