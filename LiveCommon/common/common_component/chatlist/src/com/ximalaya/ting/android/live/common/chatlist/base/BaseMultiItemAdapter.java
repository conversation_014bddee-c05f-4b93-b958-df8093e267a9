package com.ximalaya.ting.android.live.common.chatlist.base;

import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.live.common.chatlist.view.item.base.BaseItemView;
import com.ximalaya.ting.android.live.common.chatlist.view.delegate.BaseChatItemViewDelegate;

import java.util.List;

/**
 * BaseMultiItemAdapter。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @wiki Wiki网址放在这里
 * @server 服务端开发人员放在这里
 * @since 2019/3/29
 */
public abstract class BaseMultiItemAdapter<T extends IMultiItem> extends BaseAdapter<T, BaseViewHolder<T>> {

    protected BaseChatItemViewDelegate<T> mItemViewDelegate;

    public BaseMultiItemAdapter(List<T> dataList, BaseChatItemViewDelegate<T> delegate) {
        super(dataList);
        mItemViewDelegate = delegate;
    }

    @Override
    protected BaseViewHolder<T> onCreateDefineViewHolder(@NonNull ViewGroup parent, int viewType) {
        BaseItemView<T> itemView = mItemViewDelegate.onCreateItem(parent, viewType);
        // itemView 注册滚动监听
        addScrollStateListener(itemView);

        // 创建 ViewHolder
        BaseViewHolder<T> vh = itemView.getViewHolder();
        // ViewHolder 绑定 Adapter
        vh.setAdapter(this);

        return vh;
    }

    @Override
    protected int getDefineViewType(int position) {
        IMultiItem data = mListData.get(position);
        return data.getItemViewType().ordinal();
    }
}
