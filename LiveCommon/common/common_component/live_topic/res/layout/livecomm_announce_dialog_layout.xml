<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/live_topic_announce_root_fl"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_topic_announce_root_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/livecomm_bottom_sheet_background">

        <Space
            android:id="@+id/live_topic_toolbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/live_dimen_50"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/live_topic_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/live_dimen_15dp"
            android:text="@string/host_cancel"
            android:textColor="@color/live_color_999999"
            android:textSize="@dimen/live__text_size_15"
            app:layout_constraintBottom_toBottomOf="@+id/live_topic_toolbar"
            app:layout_constraintStart_toStartOf="@+id/live_topic_toolbar"
            app:layout_constraintTop_toTopOf="@+id/live_topic_toolbar" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/live_topic_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/live_dimen_15dp"
            android:text="@string/host_confirm"
            android:textColor="@color/livecomm_color_FF4646"
            android:textSize="@dimen/live__text_size_15"
            app:layout_constraintBottom_toBottomOf="@+id/live_topic_toolbar"
            app:layout_constraintEnd_toEndOf="@+id/live_topic_toolbar"
            app:layout_constraintTop_toTopOf="@+id/live_topic_toolbar" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/live_topic_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/livecomm_announcement_title"
            android:textColor="@color/live_black_000000"
            android:textSize="@dimen/livecomm_text_size_17"
            app:layout_constraintBottom_toBottomOf="@+id/live_topic_toolbar"
            app:layout_constraintEnd_toEndOf="@+id/live_topic_toolbar"
            app:layout_constraintStart_toStartOf="@+id/live_topic_toolbar"
            app:layout_constraintTop_toTopOf="@+id/live_topic_toolbar" />


        <com.ximalaya.ting.android.live.lib.livetopic.view.LiveScrollView
            android:id="@+id/live_topic_announce_scrollview"
            android:layout_width="match_parent"
            android:layout_height="@dimen/live_dimen_0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/live_topic_toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/live_announce_topic_select_layout"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/livecomm_dimen_40">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/live_select_topic"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/live_dimen_15dp"
                        android:text="@string/livecomm_topic"
                        android:textColor="@color/live_color_333333"
                        android:textSize="@dimen/live__text_size_15"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                    <LinearLayout
                        android:id="@+id/live_select_ll"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/livecomm_dimen_40"
                        android:background="@drawable/livecomm_topic_select_drawable"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingStart="@dimen/live_padding_8"
                        android:paddingTop="@dimen/live_dimen_5dp"
                        android:paddingEnd="@dimen/live_dimen_5dp"
                        android:paddingBottom="@dimen/live_dimen_5dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">


                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/live_select_content"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:gravity="end"
                            android:lines="1"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:textColor="@color/live_color_333333"
                            android:textSize="@dimen/live_size_13"
                            tools:text="怎么拍最好看圣最好看圣诞" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/live_select_topic_delete"
                            android:layout_width="@dimen/live_dimen_15dp"
                            android:layout_height="@dimen/live_dimen_15dp"
                            android:layout_marginStart="@dimen/live_dimen_10dp"
                            android:background="@drawable/livecomm_topic_delete" />
                    </LinearLayout>


                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/live_select_topic_next"
                        android:layout_width="@dimen/livecomm_dimen_7"
                        android:layout_height="@dimen/live_dimen_12dp"
                        android:layout_marginEnd="@dimen/live_dimen_15dp"
                        android:background="@drawable/livecomm_topic_next"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/live_topic_recyclerview"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/livecomm_dimen_128" />

                <LinearLayout
                    android:id="@+id/live_announce_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/live_topic_announce_title"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/livecomm_dimen_40"
                        android:layout_marginStart="@dimen/live_dimen_15dp"
                        android:gravity="center_vertical"
                        android:text="@string/livecomm_announcement_content"
                        android:textColor="@color/live_color_333333"
                        android:textSize="@dimen/live__text_size_15" />


                    <LinearLayout
                        android:id="@+id/live_announce_input_content"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/livecomm_dimen_240"
                        android:layout_marginStart="@dimen/live_dimen_15dp"
                        android:layout_marginEnd="@dimen/live_dimen_15dp"
                        android:background="@drawable/livecomm_announce_input_background"
                        android:orientation="vertical">


                        <com.ximalaya.ting.android.live.lib.livetopic.view.LiveScrollEditText
                            android:id="@+id/live_input_et"
                            style="@style/host_edit_text_cursor_drawable"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/host_transparent"
                            android:gravity="top"
                            android:hint="@string/livecomm_announcement_default_hint"
                            android:inputType="textMultiLine"
                            android:lineSpacingExtra="@dimen/live_dimen_6"
                            android:maxHeight="@dimen/livecomm_dimen_210"
                            android:paddingLeft="@dimen/live_dimen_15dp"
                            android:paddingTop="@dimen/live_dimen_10dp"
                            android:paddingRight="@dimen/live_dimen_15dp"
                            android:paddingBottom="@dimen/livecomm_dimen_4"
                            android:scrollbars="vertical"
                            android:text="@string/livecomm_announcement_default"
                            android:textColor="@color/live_color_333333"
                            android:textColorHint="@color/host_color_888888"
                            android:textSize="@dimen/live_size_14"
                            tools:text="这是一条评论这是一条评论这是一条评论这是一条评论这是一条评论" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/live_input_number"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:minHeight="@dimen/live__dimen_30dp"
                            android:paddingBottom="5dp"
                            android:layout_marginRight="@dimen/live_dimen_10dp"
                            android:gravity="end|bottom"
                            android:text="0/500"
                            android:textColor="@color/live_color_999999"
                            android:textSize="@dimen/live__text_size_12" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </com.ximalaya.ting.android.live.lib.livetopic.view.LiveScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>