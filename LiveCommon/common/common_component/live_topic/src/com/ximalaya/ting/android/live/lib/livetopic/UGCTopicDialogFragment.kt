package com.ximalaya.ting.android.live.lib.livetopic

import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.fragment.other.BaseCustomDialogFragment
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.host.util.ui.DrawableUtil
import com.ximalaya.ting.android.live.common.R
import com.ximalaya.ting.android.live.common.lib.utils.gone
import com.ximalaya.ting.android.live.lib.livetopic.LiveAnnouncementDialog.Companion.USER_TYPE_HOST
import com.ximalaya.ting.android.live.lib.livetopic.LiveAnnouncementDialog.Companion.USER_TYPE_USER
import com.ximalaya.ting.android.live.lib.livetopic.bean.SOURCE
import com.ximalaya.ting.android.live.lib.livetopic.bean.LiveTopicModel
import com.ximalaya.ting.android.live.lib.livetopic.span.LiveTopicCenterImageSpan
import com.ximalaya.ting.android.live.lib.livetopic.track.trackSelectTopicOnTopicSquare
import com.ximalaya.ting.android.live.lib.livetopic.track.trackTopicSquareShow
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import java.text.DecimalFormat
import java.util.*
import kotlin.collections.HashMap

/**
 * desc: UGC 房间中「话题」弹窗，可供：创建 UGC 房间时选择、UGC 房间中话题修改
 *
 * Created by zoey on 2022/1/4.
 * <AUTHOR>
 * @email <EMAIL>
 */
class UGCTopicDialogFragment : BaseCustomDialogFragment() {

    private var dataList: MutableList<LiveTopicModel?> = mutableListOf()

    private var topicAdapter: TopicAdapter? = null

    // 点击项后自动关闭弹窗
    private var autoClose: Boolean = true

    // 是否显示返回按钮
    private var showBackBtn: Boolean = true

    // 当前直播间 recordId
    private var currentRecordId: Long = -1

    // 当前选中的话题 id
    private var currentTopicId: Long = -1

    // 话题选择回调
    private var topicChosenCallback: ITopicChosenCallback? = null

    private var bizType: Int = 1

    private var mNeedLoadTopic: Boolean? = false

    private var mShowHeight: Int? = -1

    private var mUserType: Int? = USER_TYPE_USER

    private var mSrcChannel: SOURCE = SOURCE.CHANNEL_1

    companion object {

        private const val PARAMS_AUTO_CLOSE = "autoClose"
        private const val PARAMS_SHOW_BACK = "showBack"
        private const val PARAMS_RECORD_ID = "recordId"
        private const val PARAMS_TOPIC_ID = "topicId"
        private const val PARAMS_TOPIC_BIZTYPE = "bizType"

        private const val PARAMS_TOPIC_NEED_LOAD = "isNeedLoadTopic"
        private const val PARAMS_TOPIC_LIST = "topicList"
        private const val PARAMS_SHOW_HEIGHT = "showHeight"
        private const val PARAMS_USER_TYPE = "user_type"

        /**
         * 显示话题广场弹窗:
         *
         * 根据 [currentRecordId] 当前的直播 id 显示不同的按钮文案，如果没有默认 -1
         *
         * 根据 [currentTopicId] 当前选择的话题显示选中的话题，如果没有默认 -1
         *
         * [autoClose] 为 true 时，点击列表项即关闭当前弹窗
         *
         * 选择回调 [topicChosenCallback]，选择切换话题时回调最新的 topicId，取消选择话题时 isCancel 为 true
         */
        @JvmStatic
        @JvmOverloads
        fun show(
            fragmentManager: FragmentManager,
            currentRecordId: Long = -1, currentTopicId: Long = -1,
            autoClose: Boolean = true, showBackBtn: Boolean = true,
            topicChosenCallback: ITopicChosenCallback, bizType: Int = 1,
            isNeedLoadTopic: Boolean? = true,
            recommendTopicList: List<LiveTopicModel?>? = null,
            showHeight: Int? = -1,
            userType: Int? = USER_TYPE_USER,
            srcChannel: SOURCE = SOURCE.CHANNEL_1
        ): UGCTopicDialogFragment {
            return UGCTopicDialogFragment().also {
                it.arguments = Bundle().apply {
                    putBoolean(PARAMS_AUTO_CLOSE, autoClose)
                    putBoolean(PARAMS_SHOW_BACK, showBackBtn)
                    putLong(PARAMS_RECORD_ID, currentRecordId)
                    putLong(PARAMS_TOPIC_ID, currentTopicId)
                    putInt(PARAMS_TOPIC_BIZTYPE, bizType)
                    putBoolean(PARAMS_TOPIC_NEED_LOAD, isNeedLoadTopic ?: true)
                    putInt(PARAMS_USER_TYPE, userType ?: USER_TYPE_USER)
                    if (recommendTopicList?.size ?: 0 > 0) {
                        putParcelableArrayList(
                            PARAMS_TOPIC_LIST,
                            recommendTopicList as? ArrayList<LiveTopicModel?>
                        )
                    }
                    putInt(PARAMS_SHOW_HEIGHT, showHeight ?: -1)
                }
                it.mSrcChannel = srcChannel
                it.topicChosenCallback = topicChosenCallback
                it.show(fragmentManager, "UGCTopicDialogFragment")
            }
        }
    }

    override fun getContainerLayoutId(): Int = R.layout.liveugc_fra_topic_dialog

    override fun getCustomLayoutParams(): FragmentDialogParams {
        return super.getCustomLayoutParams().apply {
            style = com.ximalaya.ting.android.host.R.style.host_bottom_action_dialog
            animationRes = com.ximalaya.ting.android.host.R.style.host_popup_window_animation_fade
            (if (mShowHeight ?: -1 > 0) (mShowHeight ?: 480.dp) else 480.dp).also { height = it }
            canceledOnTouchOutside = true
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置无背景，否则父类会设置背景并设有顶部 padding
        parentNeedBg = false

        arguments?.also {
            autoClose = it.getBoolean(PARAMS_AUTO_CLOSE, true)
            showBackBtn = it.getBoolean(PARAMS_SHOW_BACK, true)
            currentRecordId = it.getLong(PARAMS_RECORD_ID, -1L)
            currentTopicId = it.getLong(PARAMS_TOPIC_ID, -1L)
            bizType = it.getInt(PARAMS_TOPIC_BIZTYPE, 1)
            mNeedLoadTopic = it.getBoolean(PARAMS_TOPIC_NEED_LOAD, true)
            val list =
                it.getParcelableArrayList<LiveTopicModel?>(PARAMS_TOPIC_LIST) as? List<LiveTopicModel?>
            list?.let { modellist ->
                dataList.clear()
                dataList.addAll(modellist.filterNotNull())
            }
            mShowHeight = it.getInt(PARAMS_SHOW_HEIGHT, -1)
            mUserType = it.getInt(PARAMS_USER_TYPE, USER_TYPE_USER)
        }
    }

    override fun initUi(view: View?, savedInstanceState: Bundle?) {
        view?.background = DrawableUtil.GradientDrawableBuilder()
            .cornerRadius(10.dpFloat, 0F, 10.dpFloat, 0F)
            .color(Color.WHITE)
            .build()

        view?.findViewById<View?>(R.id.live_ugc_img_topic_back)?.also {
            if (showBackBtn.not()) {
                it.gone()
                return@also
            }

            it.setOnClickListener {
                dismiss()
            }
        }

        view?.findViewById<RecyclerView?>(R.id.live_ugc_rc_topic)?.also { rc ->
            rc.adapter = TopicAdapter().also { adapter ->
                topicAdapter = adapter
            }
        }
    }

    override fun loadData() {
        if (mNeedLoadTopic == true) {
            onPageLoadingCompleted(LoadCompleteType.LOADING)

            val hashMap = HashMap<String, String>().apply {
                put("tagId", currentTopicId.toString())
                put("bizType", bizType.toString())
            }
            CommonRequestForLiveTopic.getLiveRecommendTopic(
                hashMap,
                object : IDataCallBack<List<LiveTopicModel?>> {
                    override fun onSuccess(topicList: List<LiveTopicModel?>?) {
                        if (topicList.isNullOrEmpty()) {
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT)
                        } else {
                            onPageLoadingCompleted(LoadCompleteType.OK)
                            dataList.clear()
                            dataList.addAll(topicList)
                            topicAdapter?.notifyItemRangeInserted(0, dataList.size)
                        }
                    }

                    override fun onError(code: Int, message: String?) {
                        onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR)
                        CustomToast.showDebugFailToast("加载话题失败($code): $message")
                    }
                }
            )
        }
    }

    private fun isLiving(): Boolean = currentRecordId > 0

    private fun getDrawable(@DrawableRes resId: Int): Drawable? {
        return ResourcesCompat.getDrawable(resources, resId, null)
    }

    inner class TopicAdapter : RecyclerView.Adapter<TopicAdapter.ViewHolder>() {

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val index = itemView.findViewById<TextView?>(R.id.live_ugc_topic_tv_index)
            val title = itemView.findViewById<TextView?>(R.id.live_ugc_topic_tv_title)
            val desc = itemView.findViewById<TextView?>(R.id.live_ugc_topic_tv_desc)
            val heat = itemView.findViewById<TextView?>(R.id.live_ugc_topic_tv_heat)
            val action = itemView.findViewById<TextView?>(R.id.live_ugc_topic_tv_action)
            val heatIv = itemView.findViewById<AppCompatImageView?>(R.id.live_topic_iv_heat)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder = ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.liveugc_item_dialog_topic, parent, false)
        )

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.itemView.background = DrawableUtil.GradientDrawableBuilder()
                .color(Color.parseColor("#F6F7F8"))
                .cornerRadius(8.dpFloat)
                .build()

            val currentTopic = dataList.getOrNull(position)

            if (position <= 2) {
                holder.index?.text = createIndexSpan((position + 1))
            } else {
                holder.index?.text = (position + 1).toString()
            }
            holder.title?.text = currentTopic?.title
            holder.desc?.text = currentTopic?.desc
            holder.heat?.text = currentTopic?.popularity?.formatHeat()

            if (currentTopic?.topicFlagIcon?.isBlank() == false) {
                ImageManager.from(context)
                    .displayImage(holder.heatIv, currentTopic.topicFlagIcon, -1)
                holder.heatIv?.visibility = View.VISIBLE
            } else {
                holder.heatIv?.visibility = View.GONE
            }

            val actionChosen = currentTopic?.choose ?: false
            val owner = mUserType == USER_TYPE_HOST
            holder.action?.also { actionBtn ->
                actionBtn.setOnClickListener {

                    trackSelectTopicOnTopicSquare(
                        roomType = bizType,
                        userType = mUserType ?: USER_TYPE_USER,
                        srcChannel = mSrcChannel.channel,
                        text = actionBtn.text.toString()
                    )
                    currentTopic?.also { topic ->
                        if (actionChosen) {
                            // 取消选择，不关闭弹窗
                            if (isLiving()) {
                                // 正在直播点击「正在聊」
                                CustomToast.showSuccessToast("已经在聊这个话题啦~")
                                topicChosenCallback?.onTopicOnTalkClick(topic.title ?: "")
                            } else {
                                // 开播点击「取消选择」
                                topicChosenCallback?.onTopicChosen(
                                    topic.tagId ?: -1, topic.title ?: "", true
                                )
                                CustomToast.showSuccessToast("取消成功")
                                currentTopic.choose = false
                                notifyItemChanged(position)
                            }
                            return@setOnClickListener
                        }
                        topicChosenCallback?.onTopicChosen(
                            topic.tagId ?: -1, topic.title ?: "", false
                        )

                        if (autoClose) dismiss()
                    }
                }

                if (actionChosen) {
                    actionBtn.background = getDrawable(R.drawable.liveugc_bg_topic_selected)
                    actionBtn.setTextColor(Color.parseColor("#999999"))
                } else {
                    actionBtn.background = getDrawable(R.drawable.liveugc_bg_topic_unselected)
                    actionBtn.setTextColor(Color.parseColor("#FFFFFF"))
                }

                if (isLiving()) {
                    // 正在直播间中
                    actionBtn.text = if (owner) {
                        if (actionChosen) "正在聊" else "选择话题"
                    } else {
                        if (actionChosen) "正在聊" else "我想聊"
                    }
                } else {
                    // 开播
                    actionBtn.text = if (actionChosen) "取消选择" else "选择话题"
                }
            }
        }

        override fun getItemCount(): Int = dataList.size

        private fun Int.formatHeat(): String {
            val heatValue = if (this > 10000) {
                "${DecimalFormat("#.#").format(this / 10000F)}万"
            } else {
                "$this"
            }
            return "$heatValue 热度"
        }

        private fun createIndexSpan(topicIndex: Int): SpannableStringBuilder {
            val builder = SpannableStringBuilder(topicIndex.toString())
            when (topicIndex) {
                1 -> {
                    val drawable = ContextCompat.getDrawable(
                        MainApplication.mAppInstance,
                        R.drawable.livecomm_topic_first
                    )
                    drawable?.apply {
                        setBounds(0, 0, 32.dp, 12.dp)
                        val span = LiveTopicCenterImageSpan(this)
                        builder.setSpan(
                            span,
                            0,
                            builder.length,
                            SpannableStringBuilder.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                        return builder
                    }
                }
                2 -> {
                    val drawable = ContextCompat.getDrawable(
                        MainApplication.mAppInstance,
                        R.drawable.livecomm_topic_second
                    )
                    drawable?.apply {
                        setBounds(0, 0, 32.dp, 12.dp)
                        val span = LiveTopicCenterImageSpan(this)
                        builder.setSpan(
                            span,
                            0,
                            builder.length,
                            SpannableStringBuilder.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                        return builder
                    }
                }
                3 -> {
                    val drawable = ContextCompat.getDrawable(
                        MainApplication.mAppInstance,
                        R.drawable.livecomm_topic_third
                    )
                    drawable?.apply {
                        setBounds(0, 0, 32.dp, 12.dp)
                        val span = LiveTopicCenterImageSpan(this)
                        builder.setSpan(
                            span,
                            0,
                            builder.length,
                            SpannableStringBuilder.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                        return builder
                    }
                }
            }
            return builder
        }
    }

    /**
     * 话题选择接口回调
     */
    interface ITopicChosenCallback {

        /**
         * 操作回调，选择/取消选择的话题 [topicName]，id 为 [topicId]，取消选择话题时 [isCancel] 为 true
         */
        fun onTopicChosen(topicId: Long, topicName: String, isCancel: Boolean)

        /**
         * 操作回调，点击当前正在聊的话题 [topicName]
         */
        fun onTopicOnTalkClick(topicName: String)
    }


    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        trackTopicSquareShow(
            bizType,
            mUserType ?: USER_TYPE_USER,
            mSrcChannel.channel
        )
    }
}