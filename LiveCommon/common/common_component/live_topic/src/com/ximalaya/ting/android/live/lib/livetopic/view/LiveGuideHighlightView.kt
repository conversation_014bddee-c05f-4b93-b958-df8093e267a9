package com.ximalaya.ting.android.live.lib.livetopic.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.util.AttributeSet
import android.view.View

/**
 * 引导的高光 View，允许指定位置挖空一个圆作为高亮提示
 *
 * Created by zoey on 2024/08/05.
 * <AUTHOR>
 * @email <EMAIL>
 */
class LiveGuideHighlightView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : View(context, attrs, defStyle) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val holeXFermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
    private var holeRadius = 0f
    private var holeX = 0f
    private var holeY = 0f
    private var bitmap: Bitmap? = null
    private var offscreenCanvas: Canvas? = null

    init {
        paint.color = Color.parseColor("#4D000000")
    }

    fun setHoleParams(view: View, radius: Float): PointF {
        val location = IntArray(2)
        view.getLocationOnScreen(location)
        holeX = location[0] + view.width / 2f
        holeY = location[1] + view.height / 2f
        holeRadius = radius
        invalidate()
        return PointF(holeX - holeRadius, holeY - holeRadius)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        bitmap?.recycle()
        bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        offscreenCanvas = Canvas(bitmap!!)
    }

    override fun onDraw(canvas: Canvas) {
        bitmap?.let { bmp ->
            offscreenCanvas?.let { offCanvas ->
                offCanvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
                offCanvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), paint)

                paint.xfermode = holeXFermode
                offCanvas.drawCircle(holeX, holeY, holeRadius, paint)
                paint.xfermode = null

                canvas.drawBitmap(bmp, 0f, 0f, null)
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        bitmap?.recycle()
        bitmap = null
        offscreenCanvas = null
    }
}