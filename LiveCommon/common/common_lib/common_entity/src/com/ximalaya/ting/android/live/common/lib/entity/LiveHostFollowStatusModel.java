package com.ximalaya.ting.android.live.common.lib.entity;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * desc：主播关注状态model
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021-11-17
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17602190319
 */
public class LiveHostFollowStatusModel {
    //主播id
    public long toUid;
    //关注关系的枚举：1-单向关注 2-双向关注(好友关系) 3-未关注
    public @FollowType int relationType;

    public boolean isUnfollow() {
        return relationType == FollowType.TYPE_FOLLOW_UNFOLLOW;
    }

    @IntDef
    @Retention(RetentionPolicy.SOURCE)
    public @interface FollowType {
        int TYPE_FOLLOW_SINGLE = 1;
        int TYPE_FOLLOW_MUTUAL = 2;
        int TYPE_FOLLOW_UNFOLLOW = 3;
    }
}
