package com.ximalaya.ting.android.live.common.lib.entity;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;

import java.util.List;

/**
 * desc：更多菜单
 *
 * <AUTHOR>
 */
public class LiveMenuData {

    /**
     * 提示内容
     */
    public String tips;

    /**
     * 是否展示提示
     */
    public boolean displayTips;

    /**
     * tips类型
     */
    public @ITipsType int tipsType;

    /**
     * tips id
     */
    public long tipsMenuId;
    public RoomMenuList roomMenuList;

    public static class RoomMenuList {
        public List<InteractionMenus> interactionMenus;
        public List<DecorationMenus> decorationMenus;
        public List<FunctionMenus> functionMenus;
        //红点数
        public int interactionMenusRedPointCount;
        public int decorationMenusRedPointCount;
        public int functionMenusRedPointCount;

        @NonNull
        @Override
        public String toString() {
            return "RoomMenuList{" +
                    "interactionMenus=" + interactionMenus +
                    ", decorationMenus=" + decorationMenus +
                    ", functionMenus=" + functionMenus +
                    ", interactionMenusRedPointCount=" + interactionMenusRedPointCount +
                    ", decorationMenusRedPointCount=" + decorationMenusRedPointCount +
                    ", functionMenusRedPointCount=" + functionMenusRedPointCount +
                    '}';
        }
    }

    public static class InteractionMenus {
        public long categoryId;
        public String categoryName;
        public List<MenuList> menuList;
    }

    public static class DecorationMenus {
        public long categoryId;
        public String categoryName;
        public List<MenuList> menuList;
    }

    public static class FunctionMenus {
        public long categoryId;
        /**
         * 功能名称：更多、装扮
         */
        public String categoryName;
        /**
         * icon为彩色的按钮集合，在第一排展示
         */
        public List<MenuList> menuList;
    }

    public static class MenuList {
        public int category;
        public int code;
        public String iconUrl;
        public long id;
        public String name;
        public boolean redPoint;
        public String url;
        public int urlType;
        public String desc;
    }

    @NonNull
    @Override
    public String toString() {
        return "LiveMenuData{" +
                "tips='" + tips + '\'' +
                ", displayTips=" + displayTips +
                ", tipsType=" + tipsType +
                ", roomMenuList=" + roomMenuList +
                '}';
    }

    @IntDef
    public @interface ITipsType {
        int TYPE_INTERACTION = 1;
        int TYPE_DECORATE = 2;
        int TYPE_MORE = 3;
        int TYPE_OTHER = 4;
    }
}
