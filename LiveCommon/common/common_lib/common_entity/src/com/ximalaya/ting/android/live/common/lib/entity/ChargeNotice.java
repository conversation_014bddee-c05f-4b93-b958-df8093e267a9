package com.ximalaya.ting.android.live.common.lib.entity;

import androidx.annotation.Nullable;
import android.text.TextUtils;

import com.ximalaya.ting.android.live.common.lib.base.request.CommonRequestForCommon;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import org.json.JSONObject;

/**
 * 充返提升.
 *
 * <AUTHOR>
 */

public class ChargeNotice {
    public String balanceNotEnoughTip;
    public String onEnterRoomTip;
    public boolean popTipOnEnterRoom;

    public static ChargeNotice parse(String json) {
        ChargeNotice res = null;
        if (!TextUtils.isEmpty(json)) {
            try {
                JSONObject object = new JSONObject(json);
                JSONObject data = object.optJSONObject("data");
                if (data != null) {
                    res = new ChargeNotice();
                    res.balanceNotEnoughTip = data.optString("balanceNotEnoughTip");
                    res.onEnterRoomTip = data.optString("onEnterRoomTip");
                    res.popTipOnEnterRoom = data.optBoolean("popTipOnEnterRoom");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return res;
    }

    private static ChargeNotice config;

    public static void getConfigAsync(final IDataCallBack<ChargeNotice> runnable) {
        if (config == null) {
            CommonRequestForCommon.getChargeNotice(new IDataCallBack<ChargeNotice>() {
                @Override
                public void onSuccess(@Nullable ChargeNotice chargeNotice) {
                    config = chargeNotice;
                    if (runnable != null) {
                        runnable.onSuccess(chargeNotice);
                    }
                }

                @Override
                public void onError(int i, String s) {
                    if (runnable != null) {
                        runnable.onError(i, s);
                    }
                }
            });
        } else {
            if (runnable != null) {
                runnable.onSuccess(config);
            }
        }
    }

    public static ChargeNotice getConfig() {
        return config;
    }

    public boolean noticed = false;
}
