package com.ximalaya.ting.android.live.common.lib.entity;

import androidx.annotation.Keep;

/**
 * desc: 娱乐厅业务网相关个人信息接口
 *
 * http://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/doom/entertain/entertain-web-api.md#%E5%A8%B1%E4%B9%90%E5%8E%85%E4%B8%AA%E4%BA%BA%E4%BF%A1%E6%81%AF%E6%9F%A5%E8%AF%A2
 * <AUTHOR>
 */
@Keep
public class EntBizUserInfo {
    /**
     * 直播间id
     */
    private long roomId;
    /**
     * 用户id
     */
    private long uid;
    /**
     * 目标是否禁言 true:是
     */
    private boolean targetIsForbbident;
    /**
     *  角色，1:房主 3:主持人 5:管理员 9:普通用户
     *  {@link PGCRoleType}
     */
    private int roleType;

    public long getRoomId() {
        return roomId;
    }

    public EntBizUserInfo setRoomId(long roomId) {
        this.roomId = roomId;
        return this;
    }

    public long getUid() {
        return uid;
    }

    public EntBizUserInfo setUid(long uid) {
        this.uid = uid;
        return this;
    }

    public boolean isTargetIsForbbident() {
        return targetIsForbbident;
    }

    public EntBizUserInfo setTargetIsForbbident(boolean targetIsForbbident) {
        this.targetIsForbbident = targetIsForbbident;
        return this;
    }

    public int getRoleType() {
        return roleType;
    }

    public EntBizUserInfo setRoleType(int roleType) {
        this.roleType = roleType;
        return this;
    }
}
