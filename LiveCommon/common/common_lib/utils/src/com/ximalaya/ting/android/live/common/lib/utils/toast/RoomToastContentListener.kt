package com.ximalaya.ting.android.live.common.lib.utils.toast

/**
 * @Date   2023/11/28
 * @Email  <EMAIL>
 * <AUTHOR>
 */
interface RoomToastContentListener {
    fun onEnterAnimStart(toast: RoomToast?,contextView: RoomToastContextView)
    fun onEnterAnimEnd(toast: RoomToast?,contextView: RoomToastContextView)
    fun onKeepStart(toast: RoomToast?,contextView: RoomToastContextView)
    fun onKeepEnd(toast: RoomToast?,contextView: RoomToastContextView)
    fun onExitAnimStart(toast: RoomToast?,contextView: RoomToastContextView)
    fun onExitAnimEnd(toast: RoomToast?,contextView: RoomToastContextView)
}