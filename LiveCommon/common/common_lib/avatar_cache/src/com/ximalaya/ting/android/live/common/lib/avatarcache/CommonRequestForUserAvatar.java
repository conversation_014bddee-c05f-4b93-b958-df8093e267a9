package com.ximalaya.ting.android.live.common.lib.avatarcache;

import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.util.HashMap;

/**
 * desc:用户头像网络请求
 * Created by x<PERSON><PERSON><PERSON> on 2019-12-11.
 *
 * <AUTHOR>
 * @email <EMAIL>
 *
 * @phoneNumber 17602190319
 */
public class CommonRequestForUserAvatar extends CommonRequestM {

    /***
     *  根据UIDs,查询头像列表，返回smallLogo列表
     *
     *  wiki:http://gitlab.ximalaya.com/tandy.zhang/xchat-guide/tree/master/room_chat/live-chat-user/chat-user-web
     **/
    public static void getAvatarBySplitUid(HashMap<String, String> params,
                                           IDataCallBack<ChatUserAvatarList> callBack) {
        baseGetRequest(LiveUserAvatarUrlConstants.getInstance().getUserAvatarByMultiUid(), params, callBack, new
                IRequestCallBack<ChatUserAvatarList>() {
                    @Override
                    public ChatUserAvatarList success(String s) throws Exception {
                        return ChatUserAvatarList.parseList(s);
                    }
                });
    }

    /***
     *  根据单个UID,查询middle和small两种规格的头像
     *  wiki:http://gitlab.ximalaya.com/tandy.zhang/xchat-guide/tree/master/room_chat/live-chat-user/chat-user-web
     **/
    public static void getMiddleAvatarBySingleUid(HashMap<String, String> params,
                                           IDataCallBack<MiddleAvatar> callBack) {
        baseGetRequest(LiveUserAvatarUrlConstants.getInstance().getUserAvatarBySingleUid(), params, callBack, new
                IRequestCallBack<MiddleAvatar>() {
                    @Override
                    public MiddleAvatar success(String s) throws Exception {
                        return MiddleAvatar.parse(s);
                    }
                });
    }
}
