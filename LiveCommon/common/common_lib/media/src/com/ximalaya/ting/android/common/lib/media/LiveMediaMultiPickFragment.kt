package com.ximalaya.ting.android.common.lib.media

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.os.Bundle
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.ximalaya.ting.android.common.lib.media.adapter.MediaItemAdapter
import com.ximalaya.ting.android.common.lib.media.data.MediaDataItem
import com.ximalaya.ting.android.common.lib.media.data.MediaUiItem
import com.ximalaya.ting.android.common.lib.media.data.PhotoMediaItem
import com.ximalaya.ting.android.common.lib.media.data.VideoEditInfo
import com.ximalaya.ting.android.common.lib.media.data.VideoMediaItem
import com.ximalaya.ting.android.common.lib.media.decorate.MediaItemDecoration
import com.ximalaya.ting.android.common.lib.media.listener.IMediaAdapterListener
import com.ximalaya.ting.android.common.lib.media.util.MediaBizLog
import com.ximalaya.ting.android.common.lib.media.util.getVideoClipThumbFilePath
import com.ximalaya.ting.android.common.lib.media.viewmodel.LiveMediaMultiPickViewModel
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listener.IFragmentFinish
import com.ximalaya.ting.android.host.listener.IPhotoAction
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router.IBundleInstallCallback
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.TitleBar
import com.ximalaya.ting.android.host.view.ImageViewer2
import com.ximalaya.ting.android.live.common.R
import com.ximalaya.ting.android.live.common.lib.utils.ifNullOrBlank
import com.ximalaya.ting.android.live.common.lib.utils.kt_ext.modifyIf
import com.ximalaya.ting.android.live.common.lib.utils.show
import com.ximalaya.ting.android.live.common.lib.utils.showOrGone
import java.io.File
import java.io.FileOutputStream

/**
 * 直播多媒体文件多选选择页面，支持「图片」、「视频」单选或多选
 *
 * Created by zoey on 2024/07/18.
 * <AUTHOR>
 * @email <EMAIL>
 */
class LiveMediaMultiPickFragment private constructor() : BaseFragment2(true, null),
    IPhotoAction, IMediaAdapterListener, IFragmentFinish {

    private var isBackPressed: Boolean = false
    private var disableBack: Boolean = true
    private var finishBtn: TextView? = null

    private var selectType: MediaType = MediaType.TYPE_SELECT_PHOTO
    private var selectWithCamera: Boolean = true
    private var selectWithVideoEdit: Boolean = true
    private var selectWithVideoEditFirstFrame: Boolean = false
    private var selectMaxSize: Int = DEFAULT_SELECT_SIZE
    private var selectFilterConfig: MediaItemFilterConfig? = null

    private var mediaListView: RecyclerView? = null
    private var mediaListAdapter: MediaItemAdapter? = null

    private var mediaMetadataRetriever: MediaMetadataRetriever? = null

    private val photoPreviewer: ImageViewer2 by lazy {
        ImageViewer2(context).apply {
            setIsFullScreen(true)
            setFullSlideAble(true)
            setDefaultImageUrl(R.drawable.livecomm_bg_album_loading_white)
        }
    }

    private val mediaViewModel: LiveMediaMultiPickViewModel by lazy {
        ViewModelProvider(this).get(LiveMediaMultiPickViewModel::class.java)
    }

    private var isLoadingMore: Boolean = false

    private val loadingDescView: TextView? by lazy {
        loadingView?.findViewById<TextView?>(R.id.host_loading_view_progress_tv)?.also {
            it.show()
        }
    }

    private val showSelector: Boolean
        get() {
            // 视频单选时，点击直接进入编辑页，不需要选择
            return !(selectType == MediaType.TYPE_SELECT_VIDEO && selectMaxSize == 1)
        }

    override fun onBackPressed(): Boolean {
        isBackPressed = true
        return super.onBackPressed()
    }

    override fun onFinishCallback(cls: Class<*>?, fid: Int, params: Array<Any?>?) {
        if (LiveVideoEditCoverFragment::class.java != cls) return

        // 视频剪辑数据返回
        val videoData = (params?.getOrNull(0) as? VideoMediaItem)
        if (videoData == null) {
            if (!showSelector) {
                // 取消选中
                mediaViewModel.clearSelectedItem()
            }
        } else {
            setFinishCallBackData(videoData)
            finish()
        }
    }

    override fun getPageLogicName(): String = "LiveMediaMultiPickFragment"

    override fun isShowPlayButton(): Boolean = false

    override fun getTitleBarResourceId(): Int {
        return R.id.livecomm_media_pick_title_bar
    }

    override fun setTitleBar(titleBar: TitleBar?) {
        super.setTitleBar(titleBar)
        titleBar?.removeView(TitleBar.ActionType.BACK)
        titleBar?.addAction(TitleBar.ActionType.BACK()) {
            isBackPressed = true
            finishFragment()
        }

        val tagFinish = "tagFinish"
        val finishAction = TitleBar.ActionType(
            tagFinish, TitleBar.RIGHT, R.string.livecomm_media_title_bar_finish_single, 0,
            R.color.livecomm_color_media_title_bar_finish_btn, TextView::class.java
        )
        finishAction.setFontSize(16)
        titleBar?.addAction(finishAction) {
            finishAction()
        }

        titleBar?.update()
        if (titleBar != null && titleBar.back != null) {
            titleBar.back.contentDescription = "返回"
        }
        finishBtn = titleBar?.getActionView(tagFinish) as? TextView
        context?.also {
            finishBtn?.setTextColor(
                ContextCompat.getColorStateList(
                    it, R.color.livecomm_color_media_title_bar_finish_btn
                )
            )
        }

        setTitle(R.string.livecomm_media_title)
    }

    override fun onMyResume() {
        super.onMyResume()
        (activity as? MainActivity)?.addPhotoActionListener(this)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        (activity as? MainActivity)?.removePhotoActionListener(this)
    }

    override fun onDestroy() {
        mediaMetadataRetriever?.release()
        mediaMetadataRetriever = null
        if (!disableBack || !isBackPressed) {
            setFinishCallBackData(mediaViewModel.getSelectedMediaListData())
        }
        super.onDestroy()
    }

    override fun initUi(savedInstanceState: Bundle?) {
        MediaBizLog.init()

        parseArguments()

        updateFinishBtnStatus()

        mediaListView = findViewById(R.id.livecomm_media_pick_list)
        mediaListView?.adapter = MediaItemAdapter(
            selectWithCamera, mediaViewModel.getMediaListData(), this
        ).also {
            mediaListAdapter = it
        }
        (mediaListView?.layoutManager as? GridLayoutManager)?.spanSizeLookup =
            object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when (mediaListAdapter?.getItemViewType(position)) {
                        ITEM_TYPE_FOOTER -> MEDIA_SPAN_COUNT
                        else -> 1
                    }
                }
            }
        mediaListView?.addItemDecoration(MediaItemDecoration(MEDIA_SPAN_COUNT, 1.dp, true))
        mediaListView?.addOnScrollListener(object : OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                (recyclerView.layoutManager as? GridLayoutManager)?.also { lm ->
                    val dataPosition = lm.findLastVisibleItemPosition().modifyIf(selectWithCamera) {
                        it - 1
                    }
                    if (dataPosition <= mediaViewModel.getMediaListData().size - 6) {
                        loadMore()
                    }
                }
            }
        })

        mediaViewModel.getHasMore().observe(this) {
            if (it == false) {
                mediaListAdapter?.addFooter()
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun loadData() {
        mediaViewModel.checkPermission(
            this,
            succeedAction = {
                onPageLoadingCompleted(LoadCompleteType.LOADING)
                context?.also {
                    mediaViewModel.refreshMedia(
                        it, selectType, MEDIA_LOAD_PAGE_COUNT, selectFilterConfig, showSelector,
                        onProgressCallback = { current, count ->
                            loadingDescView?.text = context?.getString(
                                R.string.livecomm_media_loading_desc, (current * 100 / count)
                            )
                        },
                        onFinishCallback = {
                            mediaListAdapter?.notifyDataSetChanged()
                            onPageLoadingCompleted(LoadCompleteType.OK)
                        }
                    )
                }
            },
            failedAction = {
                finishFragment()
            }
        )
    }

    override fun getContainerLayoutId(): Int = R.layout.livecomm_fra_media_picker

    override fun catchPhoto(requestCode: Int, data: Intent?) {
        if (selectWithCamera.not()) return
        mediaViewModel.onShotCallback(context, requestCode, data, selectFilterConfig,
            photoSucceedAction = {
                disableBack = false
                finishFragment()
            },
            videoSucceedAction = {
                gotoVideoEditPage()
            }
        )
    }

    override fun cropPhoto() {}

    override fun canceled() {}

    override fun onShotClicked() {
        when (selectType) {
            MediaType.TYPE_SELECT_PHOTO -> {
                mediaViewModel.goShotPhoto(this)
            }

            MediaType.TYPE_SELECT_VIDEO -> {
                mediaViewModel.goShotVideo(this)
            }

            MediaType.TYPE_SELECT_MIXED -> {
                CustomToast.showToast("暂不支持图片/视频混合拍摄")
            }
        }
    }

    override fun onShotItemSelect(): Boolean {
        if (mediaViewModel.getSelectedMediaListData().size >= selectMaxSize) {
            val desc: String? = when (selectType) {
                MediaType.TYPE_SELECT_PHOTO -> {
                    context?.getString(
                        R.string.livecomm_media_item_photo_overflow_desc, selectMaxSize
                    )
                }

                MediaType.TYPE_SELECT_VIDEO -> {
                    context?.getString(
                        R.string.livecomm_media_item_video_overflow_desc, selectMaxSize
                    )
                }

                MediaType.TYPE_SELECT_MIXED -> {
                    context?.getString(
                        R.string.livecomm_media_item_mixed_overflow_desc, selectMaxSize
                    )
                }
            }
            if (desc.isNullOrBlank().not()) {
                CustomToast.showFailToast(desc)
            }
            return false
        }
        return true
    }

    override fun onMediaItemSelect(module: MediaDataItem?, selected: Boolean): Boolean {
        module ?: return false

        if (selected && mediaViewModel.getSelectedMediaListData().size >= selectMaxSize) {
            val desc: String? = when (selectType) {
                MediaType.TYPE_SELECT_PHOTO -> {
                    context?.getString(
                        R.string.livecomm_media_item_photo_overflow_desc, selectMaxSize
                    )
                }

                MediaType.TYPE_SELECT_VIDEO -> {
                    context?.getString(
                        R.string.livecomm_media_item_video_overflow_desc, selectMaxSize
                    )
                }

                MediaType.TYPE_SELECT_MIXED -> {
                    context?.getString(
                        R.string.livecomm_media_item_mixed_overflow_desc, selectMaxSize
                    )
                }
            }
            if (desc.isNullOrBlank().not()) {
                CustomToast.showFailToast(desc)
            } else if (module is MediaUiItem && (module.isUnsatisfied().value == true)) {
                CustomToast.showFailToast(module.getUnsatisfiedDesc().ifNullOrBlank { "不可选" })
            }
            return false
        }

        if (module is MediaUiItem && (module.isUnsatisfied().value == true)) {
            CustomToast.showFailToast(module.getUnsatisfiedDesc().ifNullOrBlank { "不可选" })
            return false
        }

        mediaViewModel.addOrRemoveMediaItem(module, selected, selectMaxSize)

        notifyEnableSelectChanged(mediaViewModel.getSelectedMediaListData().size < selectMaxSize)
        updateFinishBtnStatus()
        return true
    }

    override fun onPhotoPreview(module: PhotoMediaItem?) {
        // 预览图片
        module ?: return
        photoPreviewer.setData(listOf(module.getMediaPath()), false)
        photoPreviewer.show(0)
    }

    override fun onVideoPreview(module: VideoMediaItem?) {
        module ?: return

        if (module.isUnsatisfied().value == true) {
            CustomToast.showFailToast(module.getUnsatisfiedDesc().ifNullOrBlank { "不可选" })
            return
        }

        if (!showSelector) {
            // 直接跳转视频编辑页
            mediaViewModel.addOrRemoveMediaItem(module, true, selectMaxSize)
            gotoVideoEditPage()
        } else {
            // 暂不支持视频预览
        }
    }

    private fun parseArguments() {
        arguments?.also {
            selectType = it.getParcelable(ARGUMENT_SELECT_TYPE) ?: MediaType.TYPE_SELECT_PHOTO
            selectWithCamera = it.getBoolean(
                ARGUMENT_SELECT_WITH_CAMERA, true
            ).modifyIf(selectType == MediaType.TYPE_SELECT_MIXED) {
                // 混合模式暂不支持拍摄
                false
            }
            selectMaxSize = it.getInt(ARGUMENT_SELECT_SIZE, DEFAULT_SELECT_SIZE)
            selectWithVideoEdit = it.getBoolean(
                ARGUMENT_SELECT_WITH_VIDEO_EDIT, true
            ).modifyIf(selectType != MediaType.TYPE_SELECT_VIDEO || selectMaxSize > 1) {
                // 多选时暂不支持视频编辑
                false
            }
            selectWithVideoEditFirstFrame = it.getBoolean(
                ARGUMENT_SELECT_WITH_VIDEO_EDIT_FIRST_FRAME, false
            )
            selectFilterConfig = it.getParcelable(ARGUMENT_SELECT_FILTER) as? MediaItemFilterConfig
        }
    }

    private fun updateFinishBtnStatus() {
        finishBtn?.isEnabled = mediaViewModel.getSelectedMediaListData().isNotEmpty()
        finishBtn?.text = when {
            selectWithVideoEdit -> {
                context?.getString(R.string.livecomm_media_title_bar_next_step)
            }

            selectMaxSize > 1 -> {
                context?.getString(
                    R.string.livecomm_media_title_bar_finish_multiple,
                    mediaViewModel.getSelectedMediaListData().size, selectMaxSize
                )
            }

            else -> {
                context?.getString(R.string.livecomm_media_title_bar_finish_single)
            }
        }
        finishBtn?.showOrGone(showSelector)
    }

    private fun finishAction() {
        if (selectWithVideoEdit) {
            // 选中视频下一步进入编辑
            gotoVideoEditPage()
        } else {
            disableBack = false
            finishFragment()
        }
    }

    private fun gotoVideoEditPage() {
        checkVideoBundle {
            val data = mediaViewModel.getSelectedMediaListData().firstOrNull() as? VideoMediaItem
            if (data == null) {
                CustomToast.showFailToast("未选中视频")
            } else {
                // 跳转选择封面
                val editInfo = data.videoEditInfo ?: VideoEditInfo()
                editInfo.videoCutStartMs = 0
                editInfo.videoCutEndMs = data.getMediaDuration() * 1000L
                data.videoEditInfo = editInfo
                // 选择视频 默认第一帧为封面,不需要跳转到 选择封面
                if (selectWithVideoEditFirstFrame) {
                    generateThumbAndFinish(data)
                } else {
                    val fragment: BaseFragment2 = LiveVideoEditCoverFragment.newPage(data)
                    fragment.setCallbackFinish(this)
                    startFragment(fragment)
                }
            }
        }
    }

    /**
     * 构建第一帧为封面 并直接返回
     */
    private fun generateThumbAndFinish(videoData: VideoMediaItem) {
        try {
            mediaMetadataRetriever = MediaMetadataRetriever()
            try {
//                Router.getActionRouter<VideoActionRouter>(Configure.BUNDLE_VIDEO)?.functionAction
                mediaMetadataRetriever?.setDataSource(videoData.getMediaPath())
            } catch (e: Exception) {
                e.printStackTrace()
            }
            val cxt = context ?: return
            val thumbCacheFile = File(getVideoClipThumbFilePath(cxt, videoData.getMediaId()))
            val thumbBitmap = mediaMetadataRetriever?.getFrameAtTime(
                0L, MediaMetadataRetriever.OPTION_CLOSEST
            )
            if (thumbBitmap == null) {
                CustomToast.showFailToast("封面生成失败")
                return
            }
            FileOutputStream(thumbCacheFile).use {
                thumbBitmap.compress(Bitmap.CompressFormat.JPEG, 100, it)
            }
            thumbBitmap.recycle()
            val editInfo = videoData.videoEditInfo ?: VideoEditInfo()
            editInfo.videoCutThumbPath = thumbCacheFile.absolutePath
            videoData.videoEditInfo = editInfo
            setFinishCallBackData(videoData)
            finish()
        } catch (e: Exception) {
            e.printStackTrace()
            CustomToast.showFailToast("封面生成失败")
            finishFragment()
        }
    }

    private fun checkVideoBundle(succeedAction: () -> Unit) {
        Router.getActionByCallback(Configure.BUNDLE_VIDEO, object : IBundleInstallCallback {
            override fun onInstallSuccess(bundleModel: BundleModel?) {
                succeedAction.invoke()
            }

            override fun onLocalInstallError(t: Throwable?, bundleModel: BundleModel?) {
                CustomToast.showFailToast("视频模块加载失败")
            }

            override fun onRemoteInstallError(t: Throwable?, bundleModel: BundleModel?) {
                CustomToast.showFailToast("视频模块加载失败")
            }
        })
    }

    private fun notifyEnableSelectChanged(enableSelect: Boolean) {
        mediaViewModel.notifyEnableSelect(enableSelect)
    }

    private fun loadMore() {
        if (isLoadingMore) return

        isLoadingMore = true
        context?.also { cxt ->
            mediaViewModel.loadMoreMedia(
                cxt, selectType, selectFilterConfig, showSelector, null,
                onFinishCallback = { pos, count ->
                    isLoadingMore = false
                    mediaListAdapter?.notifyItemRangeInserted(
                        pos.modifyIf(selectWithCamera) { it + 1 }, count
                    )
                }
            )
        }
    }

    companion object {
        /**
         * 选择图片，[maxSize] 大于 1 时非单选，可选择多张图片，但不超过 [MAX_SELECT_SIZE] 张，
         * [withCamera] 为 true 时允许拍摄传图
         * [filter] 为可选的过滤条件
         */
        @JvmStatic
        fun selectPhoto(
            maxSize: Int = DEFAULT_SELECT_SIZE,
            withCamera: Boolean = true,
            filter: MediaItemFilterConfig? = null
        ): LiveMediaMultiPickFragment {
            return LiveMediaMultiPickFragment().also { page ->
                page.arguments = Bundle().also {
                    it.putParcelable(ARGUMENT_SELECT_TYPE, MediaType.TYPE_SELECT_PHOTO)
                    it.putBoolean(ARGUMENT_SELECT_WITH_CAMERA, withCamera)
                    it.putInt(
                        ARGUMENT_SELECT_SIZE,
                        maxSize.coerceIn(DEFAULT_SELECT_SIZE, MAX_SELECT_SIZE)
                    )
                    it.putParcelable(ARGUMENT_SELECT_FILTER, filter)
                }
            }
        }

        /**
         * 选择视频，[maxSize] 大于 1 时非单选，可选择多个视频，但不超过 [MAX_SELECT_SIZE] 个，
         * [withCamera] 为 true 时允许拍摄传视频，
         * [needEdit] 为 true 时可简单剪辑视频，
         * [needEdit] 为 true 且 [firstFrame] 为 true 时跳过编辑视频，选择首帧作为封面图直接返回，
         * [filter] 为可选的过滤条件
         */
        @JvmStatic
        fun selectVideo(
            maxSize: Int = DEFAULT_SELECT_SIZE,
            withCamera: Boolean = true,
            needEdit: Boolean = true,
            firstFrame: Boolean = false,
            filter: MediaItemFilterConfig? = null
        ): LiveMediaMultiPickFragment {
            return LiveMediaMultiPickFragment().also { page ->
                page.arguments = Bundle().also {
                    it.putParcelable(ARGUMENT_SELECT_TYPE, MediaType.TYPE_SELECT_VIDEO)
                    it.putBoolean(ARGUMENT_SELECT_WITH_CAMERA, withCamera)
                    it.putBoolean(ARGUMENT_SELECT_WITH_VIDEO_EDIT, needEdit)
                    it.putBoolean(ARGUMENT_SELECT_WITH_VIDEO_EDIT_FIRST_FRAME, firstFrame)
                    it.putInt(
                        ARGUMENT_SELECT_SIZE,
                        maxSize.coerceIn(DEFAULT_SELECT_SIZE, MAX_SELECT_SIZE)
                    )
                    it.putParcelable(ARGUMENT_SELECT_FILTER, filter)
                }
            }
        }

        /**
         * 混合选择图片/视频，[maxSize] 大于 1 时非单选，可选择多个图片/视频，但不超过 [MAX_SELECT_SIZE] 个
         * [filter] 为可选的过滤条件
         */
        @JvmStatic
        fun selectMixed(
            maxSize: Int = DEFAULT_SELECT_SIZE,
            filter: MediaItemFilterConfig? = null
        ): LiveMediaMultiPickFragment {
            return LiveMediaMultiPickFragment().also { page ->
                page.arguments = Bundle().also {
                    it.putParcelable(ARGUMENT_SELECT_TYPE, MediaType.TYPE_SELECT_MIXED)
                    it.putInt(
                        ARGUMENT_SELECT_SIZE,
                        maxSize.coerceIn(DEFAULT_SELECT_SIZE, MAX_SELECT_SIZE)
                    )
                    it.putParcelable(ARGUMENT_SELECT_FILTER, filter)
                }
            }
        }
    }
}