package com.ximalaya.ting.android.live.common.lib.gift.download.model

/**
 * 礼物特效资源下载实体类.
 *
 * <AUTHOR>
 */

class AnimResDownLoadModel {
    /**
     * 本地路径
     */
    var localPath: String? = null

    /**
     * 文件名
     */
    var localBinaryName: String? = null

    /**
     * 下载网络地址
     */
    var downLoadUrl: String? = null

    /**
     * 下载开始时间
     */
    var downloadStartTime: Long = 0

    /**
     * 立即下载是否执行过回调
     */
    var downloadNowCallback: Boolean = false

    /**
     * 文件大小
     */
    var fileTotal: Long = 0

    override fun toString(): String {
        return "AnimationResourceDownLoadModel(localPath=$localPath, localBinaryName=$localBinaryName, downLoadUrl=$downLoadUrl)"
    }
}
