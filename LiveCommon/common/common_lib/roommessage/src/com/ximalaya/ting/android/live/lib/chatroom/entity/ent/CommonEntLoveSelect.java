package com.ximalaya.ting.android.live.lib.chatroom.entity.ent;

import androidx.annotation.Keep;

/**
 * 相亲模式-心动选择。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @wiki Wiki网址放在这里
 * @server 服务端开发人员放在这里
 * @since 1/17/21
 */
@Keep
public class CommonEntLoveSelect {
    /**
     * 选择麦位号
     */
    public int mMicNo;
    /**
     * 选择用户id
     */
    public long mUserId;
    /**
     * 选择用户昵称
     */
    public String mNickname;
    /**
     * 是否相互选择
     */
    public boolean isPair;
    /**
     * 心动用户的麦位号
     */
    public int mPeerMicNo;
    /**
     * 心动用户id
     */
    public long mPeerUserId;
    /**
     * 心动用户昵称
     */
    public String mPeerNickname;
}
