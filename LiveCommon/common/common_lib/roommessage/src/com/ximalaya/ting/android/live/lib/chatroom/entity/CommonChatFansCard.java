package com.ximalaya.ting.android.live.lib.chatroom.entity;

import com.ximalaya.ting.android.live.common.lib.LiveTemplateManager;
import com.ximalaya.ting.android.live.common.lib.model.LiveTemplateModel;

/**
 * 类的大体描述放在这里。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @wiki Wiki网址放在这里
 * @server 服务端开发人员放在这里
 * @since 2019/4/5
 */
public class CommonChatFansCard {

    public static final int NORMAL_GUARDIAN = 1;
    public static final int GOLD_GUARDIAN = 2;

    public int mLevel;
    public String mName;
    public int type;//1:守护; 2:黄金守护
    public long fansIconId;//粉丝牌图片id
    public boolean isActiveFans = true; //是否是粉丝团活跃用户,默认为true

    public boolean isGold() {
        return type == GOLD_GUARDIAN;
    }

    /**
     * @return 定制粉丝牌图片地址
     */
    public String getCustomIconPath() {
        LiveTemplateModel.TemplateDetail templateModel = LiveTemplateManager.getInstance()
                .getTemplateById(String.valueOf(fansIconId));
        return templateModel != null ? templateModel.getIconPath() : "";
    }
}
