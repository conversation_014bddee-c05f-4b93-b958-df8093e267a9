package com.ximalaya.ting.android.live.lib.crop;

import android.content.Intent;
import android.graphics.Bitmap;

import com.ximalaya.ting.android.host.util.common.ImageCropConfig;

/**
 * Created by qianmenchao on 2022/7/26.
 *
 * <AUTHOR>
 */
public class LiveCropIntent extends Intent {

    public static final String IMAGE_UNSPECIFIED = "image/*";   //图片类型


    public static final int MAX_LIVE_COVER_WIDTH = 1000;


    public LiveCropIntent() {
        setAction("com.android.camera.action.CROP");
    }

    public int getMaxWidth(){
        return MAX_LIVE_COVER_WIDTH;
    }

    // 从PC张弛处了解，PC端封面限制gif不得超过5m，gif尺寸不得超过1000x1000，其他格式没限制，直播间内，统一不超过10m，gif尺寸不超过1000x1000
    // 从IOS梓铭处了解，IOS封面不支持gif，裁剪大小最大1024*1024
    // 这边如果是gif还是会被处理称号jpg

    /**
     *  最大长宽 1000* 1000，
     */
    public void fitParams()  {
        putExtra("crop", "true");
        // aspectX aspectY 是宽高的比例
        ImageCropConfig.Builder builder = new ImageCropConfig.Builder();
        builder.build();
        putExtra("aspectX", builder.getAspectX());
        putExtra("aspectY", builder.getAspectY());
        // outputX outputY 是裁剪图片宽高
        putExtra("outputX", getMaxWidth());
        putExtra("outputY", getMaxWidth());
        putExtra("scale", true);
        putExtra("scaleUpIfNeeded", true);
        putExtra("return-data", false);
        putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString());
    }

//    public static CropImage getPickImage(Uri dataUri) {
//        CropImage image = new CropImage();
//        try {
//
//            final String[] mediaColumns = {
//                    MediaStore.Images.Media.WIDTH,
//                    MediaStore.Images.Media.HEIGHT,
//                    MediaStore.Images.Media.MIME_TYPE,
//                    MediaStore.Images.Media.SIZE,
//                    MediaStore.Images.Media.DATA
//            };
//
//            Activity activity = MainApplication.getMainActivity();
//            if (activity == null) {
//                return image;
//            }
//            Cursor cursor = activity.getContentResolver().query(dataUri,
//                    mediaColumns, null, null, null);//从系统表中查询指定Uri对应的照片
//            if (cursor == null) return image;
//            cursor.moveToFirst();
//            int columnIndexWidth = cursor.getColumnIndex(mediaColumns[0]);
//            int columnIndexHeight = cursor.getColumnIndex(mediaColumns[1]);
//            int columnIndexMimeType = cursor.getColumnIndex(mediaColumns[2]);
//            int columnIndexSize = cursor.getColumnIndex(mediaColumns[3]);
//            int columnIndexPath = cursor.getColumnIndex(mediaColumns[4]);
//            image.width = cursor.getInt(columnIndexWidth);
//            image.height = cursor.getInt(columnIndexHeight);  //获取照片路径
//            image.mimeType = cursor.getString(columnIndexMimeType);
//            image.size = cursor.getInt(columnIndexSize);
//            image.path = cursor.getString(columnIndexPath);
//            cursor.close();
//        }catch (Exception e){
//
//        }
//
//        return image;
//    }
//
//    public static class CropImage {
//
//        public int width;
//
//        public int height;
//
//        public String mimeType;
//
//        public int size;
//
//        public String path;
//
//        @Override
//        public String toString() {
//            return "CropImage{" +
//                    "width=" + width +
//                    ", height=" + height +
//                    ", mimeType='" + mimeType + '\'' +
//                    ", size=" + size +
//                    ", path='" + path + '\'' +
//                    ", sizeM='" + getM() + '\'' +
//                    '}';
//        }
//
//        public boolean isGif(){
//            return !TextUtils.isEmpty(mimeType) && mimeType.equals("image/gif");
//        }
//
//        public int getM(){
//            return (int) (size*1.0f/(1024*1024));
//        }
//    }
}
