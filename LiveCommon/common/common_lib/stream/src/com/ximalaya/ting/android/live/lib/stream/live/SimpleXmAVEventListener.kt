package com.ximalaya.ting.android.live.lib.stream.live

import com.ximalaya.ting.android.liveav.lib.constant.SDKInitStatus
import com.ximalaya.ting.android.liveav.lib.data.SoundLevelInfo
import com.ximalaya.ting.android.liveav.lib.data.StreamInfo
import com.ximalaya.ting.android.liveav.lib.listener.IXmAVEventListener

/**
 * 默认空实现的 [IXmAVEventListener] 接口实现父类
 *
 * Created by zoey on 2023/6/19.
 * <AUTHOR>
 * @email <EMAIL>
 */
open class SimpleXmAVEventListener : IXmAVEventListener {

    override fun onDisconnect(errorCode: Int, roomId: String?) {}

    override fun onReconnect() {}

    override fun onMixStreamConfigUpdate(code: Int) {}

    override fun onPushNetworkQuality(rtt: Int, akbps: Float, quality: Int) {}

    override fun onJoinRoom() {}

    override fun onLeaveRoom() {}

    override fun onKickOut() {}

    override fun onStreamExtraInfoUpdate(streamInfo: StreamInfo?) {}

    override fun onLoginRoomCompletion(streamInfos: MutableList<StreamInfo>?) {}

    override fun onStreamAdd(streamInfos: MutableList<StreamInfo>?) {}

    override fun onStreamDelete(streamInfos: MutableList<StreamInfo>?) {}

    override fun onStreamPlaySuccess(streamId: String?) {}

    override fun onStreamPlayFailed(streamId: String?) {}

    override fun onMixNotExitStreams(streams: MutableList<String>?) {}

    override fun onError(code: Int, stateCode: Int, msg: String?) {}

    override fun onRecvMediaSideInfo(info: String?) {}

    override fun onCaptureSoundLevel(level: Int) {}

    override fun onCaptureOtherSoundLevel(infos: MutableList<SoundLevelInfo>?) {}

    override fun onAudioRecordCallback(
        type: Int,
        bytes: ByteArray?,
        sampleRate: Int,
        channelCount: Int,
        bitDepth: Int
    ) {}

    override fun onPlayNetworkQuality(rtt: Int, akbps: Float, quality: Int) {}

    override fun onSDKInitStatusChanged(status: SDKInitStatus?) {}
}