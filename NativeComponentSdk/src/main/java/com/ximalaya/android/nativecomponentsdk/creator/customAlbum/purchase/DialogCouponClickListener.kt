package com.ximalaya.android.nativecomponentsdk.creator.customAlbum.purchase

import android.view.View
import com.ximalaya.android.componentelementarysdk.constant.CommonConstant
import com.ximalaya.android.componentelementarysdk.material.UniversalDialogMaterial
import com.ximalaya.android.componentelementarysdk.model.module.defaultModel.customAlbum.PurchaseModuleModel
import com.ximalaya.android.componentelementarysdk.model.module.defaultModel.other.Coupon
import com.ximalaya.android.componentelementarysdk.sdkApi.proxy.IClickFunctionProxy
import com.ximalaya.android.componentelementarysdk.util.SdkBaseUtil
import com.ximalaya.android.componentelementarysdk.util.SdkProxyFunctionUtil
import com.ximalaya.android.nativecomponentsdk.R
import com.ximalaya.android.nativecomponentsdk.util.LabelUtil

/**
 * Created by 5Greatest on 2022.01.18
 *
 * <AUTHOR>
 *   On 2022/1/18
 */
@Deprecated("自制页购买模块改为中插条模式，该模块弃用  --2022.09.14")
internal class DialogCouponClickListener(private val material: UniversalDialogMaterial, private val index: Int, private val couponView: View): View.OnClickListener {
    private var requesting: Boolean = false
    override fun onClick(p0: View?) {
        if (!SdkBaseUtil.Common.onClick(p0)) {
            return
        }
        val tag: Any? = p0?.getTag(com.ximalaya.android.componentelementarysdk.R.id.universal_tag_click_model)
        if (tag is PurchaseModuleModel) {
            val coupon: Coupon = SdkBaseUtil.Common.safelyGetItemFromList(tag.extensions?.coupons, index)
                ?: return
            if (!coupon.isAllocatable) {
                return
            }
            if (requesting) {
                SdkProxyFunctionUtil.showToast("正在领取中，请稍等~")
                return
            }
            requesting = true
            val isFunctional: Boolean = SdkProxyFunctionUtil.requestCoupon(coupon.couponId, object : IClickFunctionProxy.AsynchronousClickEventResult<Boolean> {
                override fun returnResult(result: Boolean?) {
                    // 无在意是否领取成功
                    if (true == result) {
                        coupon.isAllocatable = false
                        LabelUtil.CustomAlbumLabel.buildPromotionCoupon(p0.context, coupon, p0)
                        SdkProxyFunctionUtil.showToast(CommonConstant.TextConstant.TEXT_GOTTEN_SUCCESS)
                    }
                    requesting = false
                }
            })
            if (!isFunctional) {
                requesting = false
            }
        }
    }
}