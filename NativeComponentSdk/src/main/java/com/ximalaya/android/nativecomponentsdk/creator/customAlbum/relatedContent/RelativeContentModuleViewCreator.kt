package com.ximalaya.android.nativecomponentsdk.creator.customAlbum.relatedContent

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import com.ximalaya.android.componentelementarysdk.constant.ModuleConstant
import com.ximalaya.android.componentelementarysdk.model.config.BaseConfigModel
import com.ximalaya.android.componentelementarysdk.model.module.BaseModuleModel
import com.ximalaya.android.componentelementarysdk.model.module.defaultModel.customAlbum.RelativeContentModuleModel
import com.ximalaya.android.componentelementarysdk.model.pageInfo.PageInfo
import com.ximalaya.android.componentelementarysdk.util.SdkBaseUtil
import com.ximalaya.android.componentelementarysdk.util.SdkProxyFunctionUtil
import com.ximalaya.android.nativecomponentsdk.R
import com.ximalaya.android.nativecomponentsdk.creator.base.BaseNativeModuleViewCreator
import com.ximalaya.android.nativecomponentsdk.creator.base.BaseNativeModuleViewHelper
import com.ximalaya.android.nativecomponentsdk.creator.customAlbum.calendar.CalendarModuleViewCreator
import com.ximalaya.android.nativecomponentsdk.creator.customAlbum.calendar.CalendarModuleViewHelper
import com.ximalaya.android.nativecomponentsdk.creator.customAlbum.calendar.CalendarPartitionAdapter
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil

class RelativeContentModuleViewCreator: BaseNativeModuleViewCreator(), View.OnClickListener {
    companion object {
        private const val DEFAULT_MODULE_TITLE: String = "作品关联"
    }

    override fun getModuleType(): String {
        return ModuleConstant.Type.TYPE_RELATIVE_CONTENT
    }

    override fun getViewLayoutRes(): Int {
        return R.layout.universal_n_module_custom_album_relative_content_module
    }

    override fun checkIsValidData(pageInfo: PageInfo, baseConfigModuleModel: BaseConfigModel?, baseDataModuleModel: BaseModuleModel?): Boolean {
        if (baseDataModuleModel !is RelativeContentModuleModel) {
            return false
        }
        return baseDataModuleModel.localIsValidFlag && !SdkBaseUtil.Common.isEmptyCollects(baseDataModuleModel.contents)
    }

    override fun bindConfigToView(view: View?, baseConfigModuleModel: BaseConfigModel?, pageInfo: PageInfo): View? {
        return view
    }

    override fun bindDataToView(view: View?, baseDataModuleModel: BaseModuleModel?, pageInfo: PageInfo): View? {
        if (!isOriginalView(view)) {
            return view
        }
        if (checkIsValidData(pageInfo, null, baseDataModuleModel)) {
            SdkBaseUtil.ViewUtil.setVisibility(View.VISIBLE, view)
        } else {
            SdkBaseUtil.ViewUtil.setVisibility(View.GONE, view)
        }
        if (baseDataModuleModel !is RelativeContentModuleModel) {
            return view
        }

        if (baseDataModuleModel.localTopAreaContents.isNullOrEmpty()) {
            ViewStatusUtil.setVisible(
                View.GONE,
                view?.findViewById(R.id.universal_id_relative_content)
            )
        } else {
            ViewStatusUtil.setVisible(
                View.VISIBLE,
                view?.findViewById(R.id.universal_id_relative_content)
            )
            SdkBaseUtil.ViewUtil.setTextString(
                baseDataModuleModel.moduleTitle ?: DEFAULT_MODULE_TITLE,
                view?.findViewById(R.id.universal_id_module_title)
            )
            val bannerArea: RecyclerView? = view?.findViewById(R.id.universal_id_banner)
            bannerArea?.let {
                it.layoutManager =
                    LinearLayoutManager(it.context, LinearLayoutManager.HORIZONTAL, false)
                if (0 >= it.itemDecorationCount) {
                    it.addItemDecoration(RelativeContentModuleAdapter.ItemSpace())
                }
                it.adapter = RelativeContentModuleAdapter(
                    baseDataModuleModel,
                    helper as? RelativeContentModuleViewHelper
                )
            }
        }

        val contentArea: View? = view?.findViewById(R.id.universal_id_content)
        val listenListItem: RelativeContentModuleModel.ContentItem? = SdkBaseUtil.Common.safelyGetItemFromList(baseDataModuleModel.localBottomAreaContents, 0)
        var width: Int = 0
        contentArea?.let {
            width = (SdkProxyFunctionUtil.getScreenWidth(it.context)
                    - it.resources.getDimensionPixelSize(com.ximalaya.ting.android.host.R.dimen.host_x88)) / 3
        }
        if (0 >= listenListItem?.listenListContents?.size?: 0 || width <= 0) {
            SdkBaseUtil.ViewUtil.setVisibility(View.GONE, contentArea)
        } else {
            SdkBaseUtil.ViewUtil.setVisibility(View.VISIBLE, contentArea)
            SdkBaseUtil.ViewUtil.setTextString(listenListItem?.title, contentArea?.findViewById(R.id.universal_id_title))
            val subTitle: TextView? = contentArea?.findViewById(R.id.universal_id_sub_title)
            SdkBaseUtil.ViewUtil.setVisibility(if (TextUtils.isEmpty(listenListItem?.briefDesc)) View.GONE else View.VISIBLE, subTitle)
            SdkBaseUtil.ViewUtil.setTextString(listenListItem?.briefDesc, subTitle)

            contentArea?.findViewById<LinearLayout>(R.id.universal_id_cover_area)?.let { coverArea ->
                coverArea.removeAllViews()
                var count: Int = 0
                listenListItem?.listenListContents?.let { list ->
                    val halfInterval: Int = coverArea.context.resources.getDimensionPixelSize(com.ximalaya.ting.android.host.R.dimen.host_x6)
                    val layoutParam: LinearLayout.LayoutParams = LinearLayout.LayoutParams(width, width)
                    layoutParam.leftMargin = halfInterval
                    layoutParam.rightMargin = halfInterval
                    for (listenListenContentItem in list) {
                        listenListenContentItem?.coverPath ?: continue
                        if (count >= 3) {
                            break
                        }
                        val listenListItemView: View? = LayoutInflater.from(coverArea.context).inflate(R.layout.universal_n_view_custom_album_relative_content_listen_list_item, null)
                        SdkProxyFunctionUtil.displayImage(listenListItemView?.findViewById(R.id.universal_id_cover), listenListenContentItem.coverPath, com.ximalaya.android.componentelementarysdk.R.drawable.universal_ic_default_album, width, width)
                        coverArea.addView(listenListItemView, layoutParam)
                        count ++
                    }
                }
            }

            helper?.markPointFunction(RelativeContentModuleViewHelper.TYPE_SHOW_LISTEN_LIST_AREA, null)
        }

        return view
    }

    override fun setListenOnView(view: View?, baseConfigModuleModel: BaseConfigModel?, baseDataModuleModel: BaseModuleModel?, pageInfo: PageInfo): View? {
        if (!isOriginalView(view)) {
            return view
        }
        if (baseDataModuleModel !is RelativeContentModuleModel) {
            return view
        }

        SdkBaseUtil.ViewUtil.setClickListener(baseDataModuleModel, com.ximalaya.android.componentelementarysdk.R.id.universal_tag_click_model, this, view?.findViewById(R.id.universal_id_content))

        return view
    }

    override fun onClick(v: View?) {
        if (!SdkBaseUtil.Common.onClick(v)) {
            return
        }

        val id: Int = v?.id?: 0
        val tag: Any? = v?.getTag(com.ximalaya.android.componentelementarysdk.R.id.universal_tag_click_model)
        when(id) {
            R.id.universal_id_content -> {
                if (tag is RelativeContentModuleModel) {
                    doOnListenListClicked(tag)
                }
            }
        }
    }

    private fun doOnListenListClicked(relativeContentModuleModel: RelativeContentModuleModel) {
        val listenList: RelativeContentModuleModel.ContentItem = SdkBaseUtil.Common.safelyGetItemFromList(relativeContentModuleModel.localBottomAreaContents, 0)?: return
        helper?.markPointFunction(RelativeContentModuleViewHelper.TYPE_CLICK_LISTEN_LIST_AREA, listenList.contentId)
        SdkProxyFunctionUtil.jumpUrl(listenList.jumpUrl)
    }

    private fun doOnListenListItemClicked(relativeContentModuleModel: RelativeContentModuleModel, position: Int) {

    }

    override fun buildHelper(): BaseNativeModuleViewHelper<out BaseNativeModuleViewHelper.BaseDataProvider>? {
        return RelativeContentModuleViewHelper(object : BaseNativeModuleViewHelper.BaseDataProvider() {
            override fun getPageInfo(): PageInfo? {
                return pageInfo
            }
        })
    }
}