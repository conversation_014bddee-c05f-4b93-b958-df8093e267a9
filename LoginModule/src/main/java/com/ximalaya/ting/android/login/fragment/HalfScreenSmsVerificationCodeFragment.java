package com.ximalaya.ting.android.login.fragment;

import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.activity.login.ChooseCountryActivity;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.manager.account.LoginConfig;
import com.ximalaya.ting.android.host.manager.account.LoginUtil;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.login.ILoginFragmentAction;
import com.ximalaya.ting.android.host.manager.login.LoginHelper;
import com.ximalaya.ting.android.host.model.account.InternationalCodeModel;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.io.Serializable;
import java.lang.ref.WeakReference;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

/**
 * Created by le.xin on 2020/6/24.
 * 半屏免密登录验证码页面
 * <AUTHOR>
 * @email <EMAIL>
 */
public class HalfScreenSmsVerificationCodeFragment extends BaseLoginFragment implements View.OnClickListener {

    private ScheduledExecutorService mService;
    private int mElapsedTime;
    private TextView mRegionNum;
    private TextView mTiming;
//    private TextView mGetVoiceCode;
    private Button mLoginBtn;
    private ImageView mClearAccount;
    private EditText mUseNameEdit;
    private EditText mVerificationCodeEdit;

    private String mLastSendPhoneNum;

    private String pageTitle;

    public static HalfScreenSmsVerificationCodeFragment newInstance(Bundle bundle) {
        HalfScreenSmsVerificationCodeFragment fragment = new HalfScreenSmsVerificationCodeFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("");
        String mPhoneNumber = null;
        String countryCode = "86";
        if (getArguments() != null) {
            mPhoneNumber = getArguments().getString(BundleKeyConstants.KEY_PHONE_NUMBER);
            countryCode = getArguments().getString(BundleKeyConstants.KEY_PHONE_COUNTRY_CODE, "86");
            pageTitle = getArguments().getString("dialogTitle");
        }

        mCountryCode = countryCode;
        mLastSendPhoneNum = mPhoneNumber;

        mRegionNum = findViewById(R.id.login_region_number);
        mRegionNum.setText("+" + mCountryCode);
        mRegionNum.setOnClickListener(this);

        mTiming = (TextView) findViewById(R.id.login_timing);
        mTiming.setOnClickListener(this);
        if(getActivity() != null) {
            getActivity().getWindow().setSoftInputMode(
                    WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        }

//        mGetVoiceCode = (TextView) findViewById(R.id.main_receiver_voice);
//        mGetVoiceCode.setOnClickListener(this);

        mLoginBtn = findViewById(R.id.login_login);
        mLoginBtn.setOnClickListener(this);

        mClearAccount = findViewById(R.id.login_iv_clear_accout);
        mClearAccount.setOnClickListener(this);

        mUseNameEdit = findViewById(R.id.login_username);
        LoginUtil.removePhoneNumSpace(mUseNameEdit);
        mUseNameEdit.addTextChangedListener(userNameWatcher);
        mUseNameEdit.setText(mPhoneNumber);

        mVerificationCodeEdit = findViewById(R.id.login_verification_code);
        mVerificationCodeEdit.requestFocus();
        mVerificationCodeEdit.addTextChangedListener(verificationWatcher);

        TextView gotoOtherLogin = findViewById(R.id.main_other_login_btn);
        gotoOtherLogin.setOnClickListener(this);

        AutoTraceHelper.bindData(mTiming, "");
        AutoTraceHelper.bindData(mClearAccount, "");
//        AutoTraceHelper.bindData(mGetVoiceCode, "");
        AutoTraceHelper.bindData(mLoginBtn, "");
        AutoTraceHelper.bindData(gotoOtherLogin, "");

        startTimingResend();

        new XMTraceApi.Trace()
                .setMetaId(32312)
                .setServiceId("dialogView")
                .put("dialogTitle", pageTitle)
                .createTrace();
    }

    @Override
    public void onMyResume() {
        super.onMyResume();

        requestInputFocus();
    }

    private void requestInputFocus() {
        if (mVerificationCodeEdit != null) {
            mVerificationCodeEdit.setFocusable(true);
            mVerificationCodeEdit.setFocusableInTouchMode(true);
            mVerificationCodeEdit.requestFocus();
            InputMethodManager imm = SystemServiceManager.getInputMethodManager(mContext);
            if (imm != null) {
                imm.showSoftInput(mVerificationCodeEdit, InputMethodManager.SHOW_IMPLICIT);
            }
        }
    }

    private TextWatcher userNameWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            if (s != null && !TextUtils.isEmpty(s.toString())) {
                checkLoginBtnState();

                if (mClearAccount != null) {
                    mClearAccount.setVisibility(View.VISIBLE);
                }

                if (StringUtil.verifyGlobalPhone(mCountryCode, s.toString())
                        && !TextUtils.equals(s.toString(), mLastSendPhoneNum)) {
                    sendAgain();
                }
            } else {
                checkLoginBtnState();

                if (mClearAccount != null) {
                    mClearAccount.setVisibility(View.GONE);
                }
            }
        }
    };

    private void checkLoginBtnState() {
        if(mVerificationCodeEdit == null || mUseNameEdit == null || mLoginBtn == null) {
            return;
        }

        if (mVerificationCodeEdit.length() > 0 && mUseNameEdit.length() > 0) {
            mLoginBtn.setEnabled(true);
        } else {
            mLoginBtn.setEnabled(false);
        }
    }

    private TextWatcher verificationWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            checkLoginBtnState();
        }
    };

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.login_half_screen_sms_verification_code_layout;
    }

    @Override
    public void onDetach() {
        super.onDetach();

    }

    @Override
    public void onDestroyView() {
        stopTiming();

        if(mVerificationCodeEdit != null) {
            mVerificationCodeEdit.removeTextChangedListener(verificationWatcher);
        }

        if(mUseNameEdit != null) {
            mUseNameEdit.removeTextChangedListener(userNameWatcher);
        }

        super.onDestroyView();

    }

    private void startTimingResend() {
        mElapsedTime = 0;
        if (mService == null || mService.isShutdown()) {
            mService = new ScheduledThreadPoolExecutor(1, new ThreadFactory() {
                @Override
                public Thread newThread(@NonNull Runnable r) {
                    return new Thread(r, "HalfScreenSmsVerificationCodeFragment#thread");
                }
            });
        }
        mService.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                mElapsedTime++;
                timingResend();
            }
        }, 0, 1000, TimeUnit.MILLISECONDS);
    }

    private void stopTiming() {
        if (mService != null && !mService.isShutdown()) {
            mService.shutdown();
        }
    }

    private void timingResend() {
        mTiming.post(new Runnable() {
            @Override
            public void run() {

                if (!canUpdateUi())
                    return;

                if (mElapsedTime >= 60) {
                    sendAgain();
                } else {
                    int leftTime = 60 - mElapsedTime;
                    if (leftTime <= 40) {
                        if (TextUtils.equals(mCountryCode, "86")) {
//                            mGetVoiceCode.setVisibility(View.VISIBLE);
                            if (leftTime == 40) {
                                new UserTracking()
                                        .setModuleType("引导用户登录半屏")
                                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
                            }
                        }
                    }

                    mTiming.setEnabled(false);
                    mTiming.setText(leftTime + "s后再次发送");
                    mTiming.setTextColor(getResourcesSafe().getColor(
                            com.ximalaya.ting.android.host.R.color.host_color_999999_888888));
                }
            }
        });
    }

    private void sendAgain() {
        stopTiming();
        mTiming.setText("重新发送");
        mTiming.setEnabled(true);
        mTiming.setTextColor(getResourcesSafe().getColor(R.color.login_color_f86442));
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();

        String phoneNumber = null;

        if(mUseNameEdit != null && mUseNameEdit.getText() != null) {
            phoneNumber = mUseNameEdit.getText().toString();
        }

        if (i == R.id.main_receiver_voice) {
            if (OneClickHelper.getInstance().onLongTimeGapClick(v)) {
                if (StringUtil.verifyGlobalPhone(mCountryCode, phoneNumber)) {
                    new UserTracking()
                            .setItem("button")
                            .setItemId("语音验证码")
                            .setSrcModule("引导用户登录半屏")
                            .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);

                    int sendSmsType = LoginRequest.SEND_SMS_TYPE_FOR_LOGIN;

                    mLastSendPhoneNum = phoneNumber;
                    // 如果是验证手机不需要传城市编号
                    LoginHelper.sendVoiceSmsCode(getmActivity() ,
                            getCountryCodePhoneNum(mCountryCode ,phoneNumber) ,sendSmsType);

                    new XMTraceApi.Trace()
                            .setMetaId(32318)
                            .setServiceId("dialogClick")
                            .put("dialogTitle", pageTitle)
                            .createTrace();
                } else {
                    showFailToast("用户手机号输入有误");
                }
            } else {
                showFailToast(R.string.login_click_voice_code_too_fast);
            }
        } else if (i == R.id.login_timing) {
            mLastSendPhoneNum = phoneNumber;
            getPhoneCheckCode(LoginRequest.SEND_SMS_TYPE_FOR_LOGIN ,
                    phoneNumber, mCountryCode,
                    new WeakReference<HalfScreenSmsVerificationCodeFragment>(this), new IHandleOk() {
                        @Override
                        public void onReady() {
                            startTimingResend();

                            requestInputFocus();
                        }
                    });

            new XMTraceApi.Trace()
                    .setMetaId(32319)
                    .setServiceId("dialogClick")
                    .put("dialogTitle", pageTitle)
                    .createTrace();
        } else if(i == R.id.login_iv_clear_accout) {
            if (mUseNameEdit != null) {
                mUseNameEdit.setText(null);
            }
        } else if(i == R.id.login_login) {
            Editable text = mVerificationCodeEdit.getText();
            if(text != null) {
                doLoginWithoutPwd(phoneNumber, text.toString(), mCountryCode);
            }

            new XMTraceApi.Trace()
                    .setMetaId(32315)
                    .setServiceId("dialogClick")
                    .put("dialogTitle", pageTitle)
                    .createTrace();
        } else if(i == R.id.main_other_login_btn) {
            LoginConfig loginConfig = new LoginConfig();
            loginConfig.useOneKeyLogin = false;
            FragmentActivity activity = getActivity();
            if(!ToolUtil.activityIsValid(activity)) {
                return;
            }

            Intent intent = activity.getIntent();

            if(intent != null) {
                loginConfig.bundle = intent.getExtras();
            }

            new XMTraceApi.Trace()
                    .setMetaId(32316)
                    .setServiceId("dialogClick")
                    .put("dialogTitle", pageTitle)
                    .createTrace();

            UserInfoMannage.gotoLogin(activity, LoginByConstants.LOGIN_BY_FULL_SCREEN ,
                    loginConfig);
        } else if(i == R.id.login_region_number) {
            startActivityForResult(new Intent(getActivity(), ChooseCountryActivity.class), SmsLoginFragment.REQUEST_CODE);
        }
    }

    private boolean handledCancle = false;
    @Override
    public boolean onBackPressed() {
        if(!handledCancle) {
            DialogBuilder dialogBuilder = new DialogBuilder(getActivity())
                    .setMessage("验证码短信可能略有延迟，要再等等吗？")
                    .setOkBtn("再等等")
                    .setcancelApplyToButton(false)
                    .setCancelBtn("不等了", new DialogBuilder.DialogCallback() {
                        @Override
                        public void onExecute() {
                            handledCancle = true;
                            finishFragment();
                        }
                    });

            dialogBuilder.setOnDismissListener(new WeakReference<DialogInterface.OnDismissListener>(mOnDismissListener));
            dialogBuilder
                    .showConfirm();

            return true;
        }

        return super.onBackPressed();
    }

    DialogInterface.OnDismissListener mOnDismissListener = new DialogInterface.OnDismissListener() {
        @Override
        public void onDismiss(DialogInterface dialogInterface) {
            handledCancle = false;
        }
    };

    @Override
    protected String getPageLogicName() {
        return ILoginFragmentAction.PAGE_LOGIC_SMS_VERIFICATION_CODE;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == SmsLoginFragment.REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            if (data != null) {
                Serializable extra = data.getSerializableExtra(BundleKeyConstants.KEY_CODE_MODEL);
                if (extra instanceof InternationalCodeModel) {
                    mCountryCode = ((InternationalCodeModel) extra).countryCode;
                    if (mRegionNum != null) {
                        mRegionNum.setText(String.format("+%s",
                                ((InternationalCodeModel) extra).countryCode));
                    }
                }
            }
            return;
        }

        super.onActivityResult(requestCode, resultCode, data);
    }

}
