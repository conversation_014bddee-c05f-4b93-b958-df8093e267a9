package com.ximalaya.ting.android.login.fragment;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.activity.web.WebActivity;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.login.R;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.lang.ref.WeakReference;

import androidx.fragment.app.FragmentActivity;

/**
 * 类的大体描述放在这里。
 *
 * <AUTHOR>
 * @since 2017/1/3
 */

public class SsoQuickLoginFragment extends BaseLoginFragment implements View.OnClickListener {

    private static final String TAG = SsoQuickLoginFragment.class.getCanonicalName();

    private EditText mEtUsername;
    private EditText mEtPassword;
    private TextView mTvSentCheckCode;
    private ImageView mIvClearUsername;
    private ImageView mIvClearPassword;
    private Button mBtnLogin;
    private final TextWatcher mPasswordTextWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            if (s != null && !TextUtils.isEmpty(s.toString())) {
                if (mBtnLogin != null) {
                    if (mEtUsername != null
                            && mEtUsername.getText() != null
                            && !TextUtils.isEmpty(mEtUsername.getText().toString())
                            ) {
                        mBtnLogin.setEnabled(true);
                    } else {
                        mBtnLogin.setEnabled(false);
                    }
                }
                if (mIvClearPassword != null)
                    mIvClearPassword.setVisibility(View.VISIBLE);
            } else {
                if (mIvClearPassword != null)
                    mIvClearPassword.setVisibility(View.INVISIBLE);
                if (mBtnLogin != null)
                    mBtnLogin.setEnabled(false);
            }
        }
    };
    private TextView mTvAgreement;
    private CountDownTimer time;
    private boolean isCheckCoding = false;
    private final TextWatcher mUsernameTextWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            if (s != null && !TextUtils.isEmpty(s.toString())) {
                if (mTvSentCheckCode != null && !isCheckCoding) {
                    mTvSentCheckCode.setText(R.string.login_get_check_code);
                    mTvSentCheckCode.setBackgroundColor(getColorSafe(com.ximalaya.ting.android.host.R.color.host_color_xmRed));
                    mTvSentCheckCode.setClickable(true);
                }
                if (mBtnLogin != null) {
                    if (mEtPassword != null
                            && mEtPassword.getText() != null
                            && !TextUtils.isEmpty(mEtPassword.getText().toString())) {
                        mBtnLogin.setEnabled(true);
                    } else {
                        mBtnLogin.setEnabled(false);
                    }
                }
                if (mIvClearUsername != null) {
                    mIvClearUsername.setVisibility(View.VISIBLE);
                }
            } else {
                if (mTvSentCheckCode != null && !isCheckCoding) {
                    mTvSentCheckCode.setText(R.string.login_get_check_code);
                    mTvSentCheckCode.setBackgroundColor(Color.parseColor("#D8D8D8"));
                    mTvSentCheckCode.setClickable(false);
                }
                if (mBtnLogin != null) {
                    mBtnLogin.setEnabled(false);
                }
                if (mIvClearUsername != null) {
                    mIvClearUsername.setVisibility(View.INVISIBLE);
                }
            }
        }
    };

    public SsoQuickLoginFragment() {
        super(true, null);
    }

    public static SsoQuickLoginFragment newInstance() {
        return new SsoQuickLoginFragment();
    }

    public static SsoQuickLoginFragment newInstance(Bundle bundle) {
        SsoQuickLoginFragment quickLoginFragment = new SsoQuickLoginFragment();
        quickLoginFragment.setArguments(bundle);
        return quickLoginFragment;
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null){
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.login_head_layout;
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);
        titleBar.removeView(TitleBar.ActionType.BACK);

        String tagCancel = "tagCancel";
        TitleBar.ActionType cancelAction = new TitleBar.ActionType(
                tagCancel, TitleBar.LEFT, com.ximalaya.ting.android.host.R.string.host_cancel, 0,
                com.ximalaya.ting.android.host.R.color.host_orange, TextView.class);
        cancelAction.setFontSize(15);
        titleBar.addAction(cancelAction, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                FragmentActivity fragmentActivity = getActivity();
                if(fragmentActivity != null){
                    fragmentActivity.setResult(Activity.RESULT_CANCELED);
                    fragmentActivity.finish();
                }
            }
        });


        titleBar.update();
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle(getResourcesSafe().getString(R.string.login_sso_title_quick_login));

        mEtUsername = (EditText) findViewById(R.id.login_username);
        mEtPassword = (EditText) findViewById(R.id.login_password);
        mEtUsername.addTextChangedListener(mUsernameTextWatcher);
        mEtPassword.addTextChangedListener(mPasswordTextWatcher);
        mIvClearUsername = (ImageView) findViewById(R.id.login_iv_clear_accout);
        mIvClearUsername.setOnClickListener(this);
        AutoTraceHelper.bindData(mIvClearUsername,"");
        mIvClearPassword = (ImageView) findViewById(R.id.login_iv_clear_pwd);
        mIvClearPassword.setOnClickListener(this);
        AutoTraceHelper.bindData(mIvClearPassword,"");

        mTvSentCheckCode = (TextView) findViewById(R.id.login_tv_check_code);
        mTvSentCheckCode.setOnClickListener(this);
        AutoTraceHelper.bindData(mTvSentCheckCode,"");
        mTvSentCheckCode.setClickable(false);

        mBtnLogin = (Button) findViewById(R.id.login_login);
        mBtnLogin.setOnClickListener(this);
        AutoTraceHelper.bindData(mBtnLogin,"");
        mBtnLogin.setEnabled(false);

        mTvAgreement = (TextView) findViewById(R.id.login_agreement);
        mTvAgreement.setOnClickListener(this);
        AutoTraceHelper.bindData(mTvAgreement,"");

        time = new CountDownTimer(60000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                if (mTvSentCheckCode != null) {
                    mTvSentCheckCode.setClickable(false);//防止重复点击
                    mTvSentCheckCode.setText(millisUntilFinished / 1000 + "s后重发");
                }
            }

            @Override
            public void onFinish() {
                if (mTvSentCheckCode != null) {
                    isCheckCoding = false;
                    mTvSentCheckCode.setText(R.string.login_get_check_code);
                    mTvSentCheckCode.setBackgroundColor(getColorSafe(com.ximalaya.ting.android.host.R.color.host_color_xmRed));
                    mTvSentCheckCode.setClickable(true);
                }
            }
        };
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38524;
        super.onMyResume();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.login_fra_sso_quick_login;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (id == R.id.login_iv_clear_accout) {
            if (mEtUsername != null) {
                mEtUsername.setText("");
            }
            return;
        }
        if (id == R.id.login_iv_clear_pwd) {
            if (mEtPassword != null) {
                mEtPassword.setText("");
            }
            return;
        }
        if (id == R.id.login_tv_check_code) {
            String name = mEtUsername.getText().toString().trim();
            if (!StringUtil.verifiPhone(name)) {
                dialog("用户手机号输入有误！");
                return;
            }
            getPhoneCheckCode(LoginRequest.SEND_SMS_TYPE_FOR_LOGIN,
                    name, null, new WeakReference<BaseLoginFragment>(this), new IHandleOk() {
                @Override
                public void onReady() {
                    if (canUpdateUi()) {
                        mTvSentCheckCode.setBackgroundColor(Color.parseColor("#D8D8D8"));
                        time.start();
                        isCheckCoding = true;
                        CustomToast.showSuccessToast("已发送验证码");
                    }
                }
            });
            return;
        }
        if (id == R.id.login_login) {
            String username = mEtUsername.getText().toString().trim();
            String password = mEtPassword.getText().toString().trim();

            if (TextUtils.isEmpty(username) || TextUtils.isEmpty(password)) {
                dialog(getStringSafe(R.string.login_login_verify_toast_null) + "！");
                return;
            }
            //免密登陆
            if (!StringUtil.verifiPhone(username)) {
                dialog("手机号输入有误！");
                return;
            }
            doLoginWithoutPwd(username, password);
            return;
        }
        if (id == R.id.login_agreement) {
            Intent intent = new Intent(mContext, WebActivity.class);
            intent.putExtra(BundleKeyConstants.KEY_EXTRA_URL, "http://m.ximalaya.com/passport/register_rule");
            startActivity(intent);
        }
    }

    private void dialog(String msg) {
        if (mActivity != null) {
            new DialogBuilder(mActivity).setMessage(msg).showWarning();
        }
    }

}
