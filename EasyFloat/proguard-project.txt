# To enable ProGuard in your project, edit project.properties
# to define the proguard.config property as described in that file.
#
# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in ${sdk.dir}/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the ProGuard
# include property in project.properties.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-keep class com.ximalaya.ting.android.model.** { *; }

# 保持配置类 config 不被混淆
-keep class com.ximalaya.ting.android.easyfloat.data.FloatConfig {*;}

# 保持自定义控件、ContentProvider 不被混淆
-keep public class * extends android.view.View
-keep public class * extends android.content.ContentProvider

# 保持枚举 enum 类不被混淆
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保持反射不被混淆
-keepattributes EnclosingMethod