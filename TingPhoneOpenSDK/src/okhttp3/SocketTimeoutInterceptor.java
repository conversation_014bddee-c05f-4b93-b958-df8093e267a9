package okhttp3;

import android.content.Context;
import android.util.Log;
import androidx.collection.ArrayMap;
import androidx.collection.LruCache;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.timeout.SocketTimeoutModel;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.version.IVersion;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.util.TraceUtil;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;
import okhttp3.internal.connection.RealConnection;

import java.io.IOException;
import java.lang.reflect.Field;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/6/28 16:08
 */
public class SocketTimeoutInterceptor implements Interceptor {
    private static final String TAG = "SocketTimeoutInterceptorTAG";
    private static int sPlan = 0; // 为1的时候才会下发
    private static int sMaxCount = 2;
    private static final List<String> sDomains = new LinkedList<>();

    private static Context sContext = null;

    private static boolean sUploadRepeatDomain = false;
    private static Set<String> sRepeatSet = new HashSet<String>() {
        {
            add("/audio.pcdn.xmcdn.com/audio.pcdn.xmcdn.com/");
            add("/aod.cos.tx.xmcdn.com/aod.cos.tx.xmcdn.com/");
        }
    };

    private SocketTimeoutInterceptor() {
        sPlan = MMKVUtil.getInstance().getInt(PreferenceConstantsInOpenSdk.KEY_FIX_SOCKET_TIMEOUT_EXCEPTION_INT, 0);
        sMaxCount = MMKVUtil.getInstance().getInt(PreferenceConstantsInOpenSdk.KEY_FIX_SOCKET_TIMEOUT_MAX_COUNT, 2);
        String domain_str = MMKVUtil.getInstance().getString(PreferenceConstantsInOpenSdk.KEY_FIX_SOCKET_TIMEOUT_EXCEPTION_DOMAINS, null);
        if (domain_str != null) {
            try {
                String[] domains = domain_str.split(";");
                if (domains != null && domains.length > 0) {
                    for (String domain : domains) {
                        if (domain != null && domain.length() > 0) {
                            sDomains.add(domain);
                            Logger.d(TAG, "SocketTimeoutInterceptor add domain=" + domain);
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        Logger.d(TAG, "SocketTimeoutInterceptor sPlan=" + sPlan + ", sMaxCount=" + sMaxCount);
        sUploadRepeatDomain = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_UPLOAD_REPEAT_DOMAIN, false);
    }

    private static final class HOLDER {
        private static final SocketTimeoutInterceptor INSTANCE = new SocketTimeoutInterceptor();
    }

    public static SocketTimeoutInterceptor getInstance(Context context) {
        if (sContext == null) {
            sContext = context;
        }
        return HOLDER.INSTANCE;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        long startTime = System.currentTimeMillis();
        try {
            checkRepeatDomain(chain);
            return chain.proceed(chain.request());
        } catch (SocketTimeoutException e) {
            processTimeoutException(chain, chain.request(), e, (System.currentTimeMillis() - startTime));
            throw e;
        }
    }

    private void checkRepeatDomain(Chain chain) {
        if (!sUploadRepeatDomain) {
            return;
        }
        if (chain != null && chain.request() != null && chain.request().url != null) {
            String host = chain.request().url.host;
            if (host == null) {
                return;
            }
            if (host.endsWith(".ximalaya.com") || host.equals("imagev2.tx.xmcdn.com") || host.equals("imagev2.xmcdn.com")) {
                return;
            }
            Logger.d(TAG, "SocketTimeoutInterceptor checkRepeatDomain: " + host);
            String url = chain.request().url.toString();
            for (String item : sRepeatSet) {
                if (url.contains(item)) {
                    IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                    if (xdcsPost != null) {
                        xdcsPost.statErrorToXDCS("RepeatDomain", chain.request().url + ",header=" + chain.request().headers.toString() + ",trace=" + Log.getStackTraceString(new Throwable()));
                    }
                    Logger.d(TAG, "SocketTimeoutInterceptor checkRepeatDomain " + url);
                }
            }
        }
    }

    private final LruCache<Integer, Integer> mConnectionTimeoutCache = new LruCache<>(5);
    private final Map<String, Integer> mFailCountAfterFixedMap = new ArrayMap<>();

    private void processTimeoutException(Chain chain, Request request, SocketTimeoutException e, long intervalTime) {
        HttpUrl httpUrl = null;
        if (request == null || chain == null || (httpUrl = request.url()) == null) {
            return;
        }
        boolean isHttps = httpUrl.isHttps();
        String host = httpUrl.host();
        SocketTimeoutModel model = new SocketTimeoutModel();
        model.plan = sPlan;
        model.domain = host;
        model.isHttps = isHttps;
        model.intervalTime = intervalTime;
        model.netConnect = NetworkType.isConnectTONetWork(sContext);
        if (!isHttps || sPlan != 1) {
            model.curFix = false;
            String json = model.toJson();
            if (json != null) {
                XmLogger.log("apm", "sockettimeout", json);
            }
            return;
        }
        boolean fix = false;
        int failCount = -1;
        for (String domain : sDomains) {
            Call call = chain.call();
            if (domain != null && domain.equals(host) && call instanceof RealCall && ((RealCall) call).streamAllocation() != null && ((RealCall) call).streamAllocation().connection() != null) {
                RealConnection connection = ((RealCall) call).streamAllocation().connection();
                // 找到了域名出现SocketTimeout的问题
                // 如果同一个Connection，一段时间内频繁的socket timeout，那么就废弃掉该stream
                int hashCode = connection.hashCode();
                Integer timeOutCount = mConnectionTimeoutCache.get(hashCode);
                String socketInfo = "release";
                if (ConstantsOpenSdk.isDebug) {
                    Socket socket = connection.socket();
                    if (socket != null) {
                        socketInfo = "socket=" + socket.hashCode();
                    }
                    try {
                        Field field = connection.getClass().getDeclaredField("rawSocket");
                        field.setAccessible(true);
                        Socket rawSocket = (Socket) field.get(connection);
                        socketInfo += ", rawSocket code=" + rawSocket.hashCode() + ", " + rawSocket;
                    } catch (Exception e1) {
                        e1.printStackTrace();
                    }
                }
                Logger.d(TAG, "SocketTimeoutInterceptor processTimeoutException key=" + hashCode + ", timeOutCount=" + timeOutCount + ", socket=" + socketInfo + ", url=" + request.url);
                Logger.d("SocketTimeoutInterceptor", "SocketTimeoutInterceptor processTimeoutException key=" + hashCode + ", timeOutCount=" + timeOutCount + ", url=" + request.url, e);
                if (timeOutCount == null) {
                    mConnectionTimeoutCache.put(hashCode, 1);
                } else if (timeOutCount + 1 >= sMaxCount) {
                    fix = true;
                    connection.noNewStreams = true;
                    mConnectionTimeoutCache.remove(hashCode);
                    Logger.d(TAG, "SocketTimeoutInterceptor processTimeoutException noNewStreams");
                    IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                    if (xdcsPost != null) {
                        xdcsPost.statErrorToXDCS("FixSocketTimeoutError", "host=" + host + ", url=" + httpUrl);
                    }
                } else {
                    mConnectionTimeoutCache.put(hashCode, timeOutCount + 1);
                }
                // 这里表示找到需要处理的host
                Integer val = mFailCountAfterFixedMap.get(host);
                if (val != null) {
                    failCount = val + 1;
                    mFailCountAfterFixedMap.put(host, val + 1);
                } else if (fix) {
                    failCount = 0;
                    mFailCountAfterFixedMap.put(host, 0);
                }
                Logger.d(TAG, "SocketTimeoutInterceptor processTimeoutException: " + failCount);

                IVersion versionCallback = RouterServiceManager.getInstance().getService(IVersion.class);
                if (versionCallback != null && !versionCallback.isReleaseVersion()) {
                    model.msg = Log.getStackTraceString(e);
                }
            }
        }
        model.count = failCount;
        model.curFix = fix;
        String json = model.toJson();
        XmLogger.log("apm", "sockettimeout", json);
    }
}