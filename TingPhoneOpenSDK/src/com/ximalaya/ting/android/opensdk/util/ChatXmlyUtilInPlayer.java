package com.ximalaya.ting.android.opensdk.util;

import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.base.IMyDataCallBack;
import com.ximalaya.ting.android.routeservice.service.ICommonBusiService;
import com.ximalaya.ting.android.xmutil.Logger;

import androidx.annotation.Nullable;

/**
 * Created by WolfXu on 2024/5/31.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class ChatXmlyUtilInPlayer {
    private static final int PRE_LOAD_TIME = 20000;
    private static final int MAX_RETRY = 1;
    private static long sCurIdIsLoadingOrLoadedNext = 0;
    private static int sRetryCount = 0;
    private static final String TAG = "ChatXmlyUtilInPlayer";

    public static void onPlayProgress(PlayableModel curPlayableModel, int position, int duration) {
        if (curPlayableModel == null || !curPlayableModel.isKindOfChatXmly()) {
            return;
        }
        long id = curPlayableModel.getDataId();
        if (sCurIdIsLoadingOrLoadedNext == id) {
            return;
        }
        if ((duration - position) < PRE_LOAD_TIME) {
            sCurIdIsLoadingOrLoadedNext = id;
            ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
            if (commonBusiService != null) {
                commonBusiService.loadChatXmlyNextAISection(new IMyDataCallBack<Boolean>() {
                    @Override
                    public void onSuccess(@Nullable Boolean data) {
                    }

                    @Override
                    public void onError(int code, String message) {
                        Logger.i(TAG, "onError: code = " + code + ", message = " + message);
                        if (id == sCurIdIsLoadingOrLoadedNext && sRetryCount < MAX_RETRY) {
                            sRetryCount++;
                            Logger.i(TAG, "onError: retry " + sRetryCount);
                            sCurIdIsLoadingOrLoadedNext = 0;
                        }
                    }
                });
            }
        }
    }

    public static void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        if (lastModel == null || curModel == null) {
            return;
        }
        Logger.i(TAG, "onSoundSwitch");
        ICommonBusiService commonBusiService = RouterServiceManager.getInstance().getService(ICommonBusiService.class);
        if (lastModel.isKindOfChatXmly() && !curModel.isKindOfChatXmly()) {
            if (commonBusiService != null) {
                commonBusiService.notifyNotPlayChatXmly();
            }
        } else if (curModel.isKindOfChatXmly()) {
            sRetryCount = 0;
            if (commonBusiService != null) {
                commonBusiService.notifyChatXmlySoundSwitch(curModel.getDataId());
            }
            XmPlayerService service = XmPlayerService.getPlayerSrvice();
            if (service != null) {
                service.removeListByTrackId(lastModel.getDataId());
                if (ConstantsOpenSdk.isDebug) {
                    if (service.getPlayListControl() != null) {
                        Logger.i(TAG, String.valueOf(service.getPlayListControl().getPlayList()));
                    }
                }
            }
        }
    }
}
