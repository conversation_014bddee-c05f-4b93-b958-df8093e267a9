package com.ximalaya.ting.android.opensdk.util;

import android.net.Uri;
import android.text.TextUtils;
import android.util.Base64;

import android.util.Log;
import androidx.annotation.Nullable;

import androidx.core.util.Pair;
import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.track.ChildAiTimbrePlayUrlInfo;
import com.ximalaya.ting.android.opensdk.model.track.PlayUrlInfo;
import com.ximalaya.ting.android.opensdk.model.track.PlayUrlModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.player.cdn.CdnUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.nio.charset.Charset;
import java.util.List;

/**
 * Created by WolfXu on 4/23/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class PlayUrlUtil {
    private static final String TAG = "PlayUrlUtil";

    public static String decryptDownload(String data, int version) {
        if (version == 2) {
            String url = decryptV2(data);
            if (!TextUtils.isEmpty(url)) {
                return url;
            }
        }
        String url = decrypt(data);
        if (!TextUtils.isEmpty(url)) {
            return url;
        }
        if (canUseDecryptV2()) {
            return decryptV2(data);
        }
        return null;
    }

    public static String decrypt(String data, int version) {
        if (version == 2) {
            String url = decryptV2(data);
            if (!TextUtils.isEmpty(url)) {
                return url;
            }
        }
        String url = decrypt(data);
        if (!TextUtils.isEmpty(url)) {
            return url;
        }
        if (canUseDecryptV2()) {
            return decryptV2(data);
        }
        return null;
    }

    public static String decryptV2(String data) {
        if (TextUtils.isEmpty(data)) {
            return null;
        }
        try {
            return EncryptUtil.getInstance(null).urlDecrypt(data);
        } catch (Exception e) {
            String exceptionMsg = "decrypt_exception,decryptV2-error,data=" + data + "," + Log.getStackTraceString(e);
            Logger.logToFile(exceptionMsg);
            if (uploadErrorMsg()) {
                CdnUtil.statToXDCSError("decrypt_exception", exceptionMsg);
            }
        }
        return null;
    }

    public static String decrypt(String data) {
        if (TextUtils.isEmpty(data)) {
            return null;
        }
        if (ConstantsOpenSdk.isDebug) {
            Logger.i(TAG, "decrypt_exception,data=" + data + ",trace=" + Log.getStackTraceString(new Throwable()));
        }
        String url = null;
        String key = EncryptUtil.getInstance(null).getPrivacyStr(null, "play_url_key");
        try {
            byte[] decodedData = Base64.decode(data, Base64.URL_SAFE);
            byte[] decodedKey = Hex.decodeHex(key.toCharArray());
            byte[] result = EncryptUtil.getInstance(null).aesDecrypt(decodedKey, decodedData);
            url = new String(result, Charset.forName("UTF-8"));
        } catch (Exception e) {
            String exceptionMsg = "decrypt_exception,V1-error,data=" + data + "," + Log.getStackTraceString(e);
            Logger.logToFile(exceptionMsg);
            if (uploadErrorMsg()) {
                CdnUtil.statToXDCSError("decrypt_exception", exceptionMsg);
            }
        }
        return url;
    }

    private static boolean canUseDecryptV2() {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_USE_ANDROID2_FOR_DECRYPT, false);
    }

    private static boolean uploadErrorMsg() {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEM_UPLOAD_ERROR_FOR_DECRYPT_EXCEPTION, true);
    }

    public static String getDecodedUrl(PlayUrlInfo playUrlInfo) {
        if (playUrlInfo == null || TextUtils.isEmpty(playUrlInfo.getUrl())) {
            return null;
        }
        if (!TextUtils.isEmpty(playUrlInfo.getDecodeUrl())) {
            return playUrlInfo.getDecodeUrl();
        }
        String decodedUrl = decrypt(playUrlInfo.getUrl(), playUrlInfo.getVersion());
        playUrlInfo.setDecodeUrl(decodedUrl);
        return decodedUrl;
    }

    public static String getDecodedUrl(ChildAiTimbrePlayUrlInfo playUrlInfo) {
        if (playUrlInfo == null || TextUtils.isEmpty(playUrlInfo.getUrl())) {
            return null;
        }
        if (!TextUtils.isEmpty(playUrlInfo.getDecodeUrl())) {
            return playUrlInfo.getDecodeUrl();
        }
        String decodedUrl = decrypt(playUrlInfo.getUrl(), playUrlInfo.getVersion());
        playUrlInfo.setDecodeUrl(decodedUrl);
        return decodedUrl;
    }

    public static PlayUrlModel getTrackUrlByQualityLevel(Track track, int qualityLevel) {
        if (track == null) {
            return null;
        }
        String trackUrl = null;
        float gain = 0f;
        String type = null;
        String encryptUrl = null;

        boolean getFromInfoList = getUrlFromInfoList();
        if (getFromInfoList) {
            if (track.getPlayUrlInfoList() != null && track.getPlayUrlInfoList().size() > 0) {
                for (PlayUrlInfo urlInfo : track.getPlayUrlInfoList()) {
                    if (urlInfo != null && urlInfo.getQualityLevel() == qualityLevel &&
                            !isTextEmpty(urlInfo.getUrl())) {
                        trackUrl = PlayUrlUtil.getDecodedUrl(urlInfo);
                        gain = urlInfo.getVolumeGain();
                        type = urlInfo.getType();
                        encryptUrl = urlInfo.getUrl();
                        if (urlInfo.isHuaweiSound() && !isTextEmpty(trackUrl)) {
                            try {
                                trackUrl = Uri.parse(trackUrl).buildUpon()
                                        .appendQueryParameter("vivid", "1")
                                        .build().toString();
                                Logger.d("zimotag", "PlayUrlUtil getTrackUrlByQualityLevel " + trackUrl + ", huawei vivid: true");
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } else if (isDolbyUrl(type) && !isTextEmpty(trackUrl)) {
                            try {
                                trackUrl = Uri.parse(trackUrl).buildUpon()
                                        .appendQueryParameter("dolby", "1")
                                        .build().toString();
                                Logger.d("zimotag", "PlayUrlUtil getTrackUrlByQualityLevel " + trackUrl + ", dolby: true");
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        if (!isTextEmpty(trackUrl)) {
                            break;
                        }
                    }
                }
            }
        }

        if (!isTextEmpty(trackUrl)) {
            return new PlayUrlModel(track.getDataId(), trackUrl, gain, type, encryptUrl);
        }

        // 优先取track中直接带的播放地址，目前还有很多其他接口可能会直接带的，就不去解析了。
        // 后续所有接口统一改了之后，再改为全从playUrlList取
        switch (qualityLevel) {
            case Track.TRACK_QUALITY_LOSSLESS:
                break;
            case Track.TRACK_QUALITY_HIGH_PLUS:
                trackUrl = track.getPlayPathHq();
                break;
            case Track.TRACK_QUALITY_HIGH:
                trackUrl = track.getPlayUrl64M4a();
                if (TextUtils.isEmpty(trackUrl)) {
                    trackUrl = track.getPlayUrl64();
                }
                break;
            default:
                trackUrl = track.getPlayUrl24M4a();
                if (TextUtils.isEmpty(trackUrl)) {
                    trackUrl = track.getPlayUrl32();
                }
                break;
        }

        if (getFromInfoList) {
//            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
//            if (xdcsPost != null) {
//                xdcsPost.statErrorToXDCS("getUrlError", "getUrlFail id=" + track.getDataId() + ", title=" + track.getTrackTitle() + ", qualityLevel=" + qualityLevel);
//            }
        }

        if (isTextEmpty(trackUrl) && track.getPlayUrlInfoList() != null && track.getPlayUrlInfoList().size() > 0) {
            for (PlayUrlInfo urlInfo : track.getPlayUrlInfoList()) {
                if (urlInfo != null && urlInfo.getQualityLevel() == qualityLevel &&
                        !isTextEmpty(urlInfo.getUrl())) {
                    trackUrl = PlayUrlUtil.getDecodedUrl(urlInfo);
                    gain = urlInfo.getVolumeGain();
                    type = urlInfo.getType();
                    encryptUrl = urlInfo.getUrl();
                    if (urlInfo.isHuaweiSound() && !isTextEmpty(trackUrl)) {
                        try {
                            trackUrl = Uri.parse(trackUrl).buildUpon()
                                    .appendQueryParameter("vivid", "1")
                                    .build().toString();
                            Logger.d("zimotag", "PlayUrlUtil getTrackUrlByQualityLevel " + trackUrl + ", huawei vivid: true");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    if (!isTextEmpty(trackUrl)) {
                        break;
                    }
                }
            }
        }
        return new PlayUrlModel(track.getDataId(), trackUrl, gain, type, encryptUrl);
    }

    public static boolean isDolbyUrl(String type) {
        return type != null && ("PANORAMIC_DOLBY".equalsIgnoreCase(type));
    }

    private static boolean getUrlFromInfoList() {
        if (ConstantsOpenSdk.isDebug) {
            String urlFromList = SystemUtil.getSystemProperty("debug.xmly.urlFromList", "0");
            if ("1".equals(urlFromList)) {
                return true;
            } else if ("2".equals(urlFromList)) {
                return false;
            }
        }
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_GET_URL_FROM_INFO_LIST, true);
    }

    public static boolean isTextEmpty(String text) {
        return text == null || text.length() == 0 || text.equals("null");
    }

    public static PlayUrlModel getTrackUrlByQualityOrder(Track track, List<Integer> qualityOrder) {
        if (track == null || qualityOrder == null || qualityOrder.isEmpty()) {
            return null;
        }
        PlayUrlModel foundModel = null;
        String trackUrl = null;
        float gain = 0f;
        for (Integer quality : qualityOrder) {
            if (quality != null) {
                PlayUrlModel model = PlayUrlUtil.getTrackUrlByQualityLevel(track, quality);
                if (model != null) {
                    trackUrl = model.url;
                    gain = model.gain;
                } else {
                    trackUrl = null;
                    gain = 0f;
                }
                if (!TextUtils.isEmpty(trackUrl)) {
                    foundModel = model;
                    track.setTrackCurPlayQualityLevel(quality);
                    break;
                }
            }
        }
        return foundModel;
    }

    public static String getTrackUrlByListOrder(List<PlayUrlInfo> playUrlInfos) {
        if (playUrlInfos != null && playUrlInfos.size() > 0) {
            for (PlayUrlInfo urlInfo : playUrlInfos) {
                if (urlInfo != null && !TextUtils.isEmpty(urlInfo.getUrl())) {
                    String trackUrl = PlayUrlUtil.getDecodedUrl(urlInfo);
                    if (!TextUtils.isEmpty(trackUrl)) {
                        return trackUrl;
                    }
                }
            }
        }
        return null;
    }

    public static PlayUrlInfo getTrackByListOrder(List<PlayUrlInfo> playUrlInfos) {
        if (playUrlInfos != null && playUrlInfos.size() > 0) {
            for (PlayUrlInfo urlInfo : playUrlInfos) {
                if (urlInfo != null && !TextUtils.isEmpty(urlInfo.getUrl())) {
                    String trackUrl = PlayUrlUtil.getDecodedUrl(urlInfo);
                    if (!TextUtils.isEmpty(trackUrl)) {
                        return urlInfo;
                    }
                }
            }
        }
        return null;
    }

    @Nullable
    public static String getTrackUrlByType(Track track, String urlType) {
        if (track == null || TextUtils.isEmpty(urlType)) {
            return null;
        }

        String url = null;
        switch (urlType) {
            case Track.TRACK_PLAY_URL_TYPE_FHQ:
                url = track.getPlayPathHq();
                break;
            case Track.TRACK_PLAY_URL_TYPE_MP3_32:
                url = track.getPlayUrl32();
                break;
            case Track.TRACK_PLAY_URL_TYPE_MP3_64:
                url = track.getPlayUrl64();
                break;
            case Track.TRACK_PLAY_URL_TYPE_M4A_24:
                url = track.getPlayUrl24M4a();
                break;
            case Track.TRACK_PLAY_URL_TYPE_M4A_64:
                url = track.getPlayUrl64M4a();
                break;
        }

        if (!TextUtils.isEmpty(url)) {
            return url;
        }

        if (track.getPlayUrlInfoList() == null || track.getPlayUrlInfoList().isEmpty()) {
            return null;
        }

        for (PlayUrlInfo urlInfo : track.getPlayUrlInfoList()) {
            if (urlInfo != null && urlType.equalsIgnoreCase(urlInfo.getType()) && !isTextEmpty(urlInfo.getUrl())) {
                return PlayUrlUtil.getDecodedUrl(urlInfo);
            }
        }

        return null;
    }
}
