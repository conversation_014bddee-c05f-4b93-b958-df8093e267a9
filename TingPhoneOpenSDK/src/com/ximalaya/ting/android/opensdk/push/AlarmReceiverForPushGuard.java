package com.ximalaya.ting.android.opensdk.push;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

public class AlarmReceiverForPushGuard extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (TextUtils.isEmpty(action)) {
            return;
        }
        if (PushGuardPlayerManager.ACTION_PUSH_GUARD_ALARM_TYPE.equals(action) ||
                PushGuardPlayerManager.ACTION_PUSH_GUARD_ALARM_INDEX.equals(action)) {
            PushGuardPlayerManager.getInstance().dealIndexAndTypeChangeLogic(action);
        }
    }
}
