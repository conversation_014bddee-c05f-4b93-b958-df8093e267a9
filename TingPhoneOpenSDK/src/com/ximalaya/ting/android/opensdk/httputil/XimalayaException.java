/**
 * XimalayaException.java
 * com.ximalaya.ting.android.bydcarlib.datatransfer
 *
 *
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.httputil;

import java.util.HashMap;
import java.util.Map;

/**
 * ClassName:XimalayaException Function: TODO ADD FUNCTION Reason: TODO ADD
 * REASON
 * 
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-7-13 下午4:13:35
 * 
 * @see
 */
public class XimalayaException extends Exception
{
	public static final int REQUEST_URL_EMPTY = 1001;
	public static final int SIGNATURE_ERR_BY_EMPTY = 1002;
	public static final int FORM_ENCODE_LAST_ONE = 1003;
	public static final int NOT_INIT = 1004;
	public static final int NOT_HAVE_APPKEY = 1005;
	public static final int GET_SYSTEM_PARAMETER_ERROR = 1006;
	public static final int PARSE_JSON_ERROR = 1007;
	public static final int ENCODE_ERROR = 1008;
	public static final int ERROR_DESC_EMPTY = 1009;
	public static final int GET_TOKEN_FAIL = 1010;
	public static final int HTTP_REQUEST_ERROR = 1011;
	public static final int REQUEST_URL_PARSE_ERROR = 1012;
	public static final int TOKEN_INVALID = 1013;		// token 失效
	public static final int LOGIN_NEED = 1014;		// 需要登录
	public static final int REQUEST_DATA_TOO_LARGE = 1015;
	public static final Map<Integer, String> ERR_MESSAGE_MAP = new HashMap<Integer, String>() {
		{
			put(REQUEST_URL_EMPTY, "request url is empty");
			put(SIGNATURE_ERR_BY_EMPTY, "exception occurs when caculate signature");
			put(FORM_ENCODE_LAST_ONE, "Form encoded body must have at least one part");
			put(NOT_INIT, "you must call #XiMaLaYa.init");
			put(NOT_HAVE_APPKEY, "get appkey error from AndroidManifest.xml metaData");

			put(ERROR_DESC_EMPTY, "parse data error");
			put(GET_TOKEN_FAIL, "get accesstoken fail");
			put(REQUEST_URL_PARSE_ERROR, "request url parse error");
			put(TOKEN_INVALID, "token invalid");
			put(LOGIN_NEED ,"login need");
			put(HTTP_REQUEST_ERROR ,"http error");
		}
	};
	private int mErrorCode;
	private String mErrorMessage;
	public XimalayaException(int errorCode, String errorMessage)
	{
		super(errorMessage);
		this.mErrorCode = errorCode;
		this.mErrorMessage = errorMessage;
	}

	public static final XimalayaException getExceptionByCode(int code) {
		return new XimalayaException(code, ERR_MESSAGE_MAP.get(code));
	}

	public int getErrorCode()
	{
		return mErrorCode;
	}

	public void setErrorCode(int mErrorCode)
	{
		this.mErrorCode = mErrorCode;
	}

	public String getErrorMessage()
	{
		return mErrorMessage;
	}

	public void setErrorMessage(String mErrorMessage)
	{
		this.mErrorMessage = mErrorMessage;
	}
	
	
}
