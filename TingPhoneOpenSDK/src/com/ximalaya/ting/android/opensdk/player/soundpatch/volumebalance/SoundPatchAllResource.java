package com.ximalaya.ting.android.opensdk.player.soundpatch.volumebalance;

import android.text.TextUtils;
import androidx.collection.ArrayMap;
import androidx.core.util.Pair;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.xmutil.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/5/26 19:40
 */
public class SoundPatchAllResource {
    private static final String TAG = "SoundPatchAllResource";
    private Map<String, SoundPatchResource> map = new ArrayMap<>();

    /**
     * 返回指定url对应的gain值
     */
    public double getGain(String url) {
        Logger.d(TAG, "SoundPatchResourceUtil getGain  url=" + url);
        if (TextUtils.isEmpty(url)) {
            return 0;
        }
        for (Map.Entry<String, SoundPatchResource> entry : map.entrySet()) {
            SoundPatchResource soundPatchResource = entry.getValue();
            if (soundPatchResource != null && soundPatchResource.resources != null) {
                for (Map.Entry<String, SoundPatchGainModel> resourceEntry : soundPatchResource.resources.entrySet()) {
                    SoundPatchGainModel soundPatchGainModel = resourceEntry.getValue();
                    Logger.d(TAG, "SoundPatchAllResource getGain: soundPatchGainModel = " + soundPatchGainModel);
                    if (soundPatchGainModel != null && url.equals(soundPatchGainModel.getOriginUrl())) {
                        return soundPatchGainModel.getGain();
                    }
                }

            }
        }
        return 0;
    }

    public double getGain(String category, String subType, String url) {
        SoundPatchResource resources = map.get(category);
        Logger.d(TAG, "SoundPatchAllResource getGain category=" + category + ", subType=" + subType + ", resources=" + resources);
        if (resources != null && resources.resources != null) {
            SoundPatchGainModel model = resources.resources.get(subType);
            Logger.d(TAG, "SoundPatchAllResource getGain: 1");
            if (model != null) {
                Logger.d(TAG, "SoundPatchAllResource getGain 2 gain=" + model.getGain());
                if (url != null && !url.equals(model.getOriginUrl())) {
                    IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                    if (xdcsPost != null) {
                        xdcsPost.statErrorToXDCS("SoundPatchUrlError", "category=" + category + ", subType=" + subType + ", url=" + url + ", originUrl=" + model.getOriginUrl());
                    }
                    Logger.e(TAG, "SoundPatchAllResource getGain url不一致, " + url + ", originUrl=" + model.getOriginUrl());
                }
                return model.getGain();
            }
        }
        Logger.d(TAG, "SoundPatchAllResource getGain: 3");
        return 0;
    }

    public Pair<Double, String> getGainByUrl(String url) {
        Logger.d(TAG, "SoundPatchAllResource getGainByUrl: " + url);
        if (TextUtils.isEmpty(url) || map == null) {
            Logger.d(TAG, "SoundPatchAllResource getGainByUrl: null");
            return null;
        }
        List<String[]> list = new LinkedList<String[]>() {
            {
                add(SoundPatchResourceConstants.AUDITION_FREE_CHILD);
                add(SoundPatchResourceConstants.AUDITION_FREE_FEMALE);
                add(SoundPatchResourceConstants.AUDITION_FREE_MALE);
                add(SoundPatchResourceConstants.AUDITION_PAID_CHILD);
                add(SoundPatchResourceConstants.AUDITION_PAID_FEMALE);
                add(SoundPatchResourceConstants.AUDITION_PAID_MALE);
            }
        };
        for (String[] type : list) {
            if (type != null && type.length == 2) {
                SoundPatchResource res = map.get(type[0]);
                if (res != null && res.resources != null) {
                    SoundPatchGainModel model = res.resources.get(type[1]);
                    if (model != null && model.originUrl != null && model.originUrl.equals(url)) {
                        Logger.d(TAG, "SoundPatchAllResource getGainByUrl: " + model.getGain());
                        return new Pair<>(model.getGain(), type[0]);
                    }
                }
            }
        }
        Logger.d(TAG, "SoundPatchAllResource getGainByUrl end");
        return null;
    }

    public static SoundPatchAllResource parse(String content) {
        try {
            JSONObject jsonObj = new JSONObject(content);
            JSONArray arr = jsonObj.optJSONArray("data");
            if (arr == null) {
                return null;
            }
            SoundPatchAllResource resource = new SoundPatchAllResource();
            int len = arr.length();
            for (int i = 0; i < len; i++) {
                SoundPatchResource model = SoundPatchResource.parse(arr.getJSONObject(i));
                if (model != null) {
                    resource.map.put(model.category, model);
                }
            }
            return resource;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static class SoundPatchResource {
        private String category;
        private Map<String, SoundPatchGainModel> resources;

        public static SoundPatchResource parse(JSONObject jsonObject) {
            if (jsonObject == null) {
                return null;
            }
            String category = jsonObject.optString("category");
            if (TextUtils.isEmpty(category)) {
                return null;
            }
            SoundPatchResource resource = new SoundPatchResource();
            resource.category = category;
            JSONObject json = jsonObject.optJSONObject("resources");
            if (json == null) {
                return null;
            }
            resource.resources = new ArrayMap<>();
            Iterator<String> it = json.keys();
            while (it.hasNext()) {
                String key = it.next();
                if (TextUtils.isEmpty(key)) {
                    continue;
                }
                JSONObject resourceObj = json.optJSONObject(key);
                SoundPatchGainModel model = SoundPatchGainModel.parse(resourceObj);
                if (model == null) {
                    continue;
                }
                resource.resources.put(key, model);
            }
            return resource;
        }
    }

    public static class SoundPatchGainModel {
        private String url;
        private double gain;
        private String originUrl;

        public static SoundPatchGainModel parse(JSONObject jsonObject) {
            if (jsonObject == null) {
                return null;
            }
            SoundPatchGainModel model = new SoundPatchGainModel();
            model.url = jsonObject.optString("url");
            model.gain = jsonObject.optDouble("gain");
            model.originUrl = jsonObject.optString("originUrl");
            if (TextUtils.isEmpty(model.url)) {
                return null;
            }
            return model;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public double getGain() {
            return gain;
        }

        public void setGain(double gain) {
            this.gain = gain;
        }

        public String getOriginUrl() {
            return originUrl;
        }

        public void setOriginUrl(String originUrl) {
            this.originUrl = originUrl;
        }
    }
}