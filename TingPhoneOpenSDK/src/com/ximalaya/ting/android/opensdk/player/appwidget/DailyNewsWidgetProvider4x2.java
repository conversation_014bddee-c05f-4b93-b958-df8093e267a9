package com.ximalaya.ting.android.opensdk.player.appwidget;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.view.View;
import android.widget.RemoteViews;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.appnotification.PendingIntentRequestConstants;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationPendingIntentCreateUtils;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.base.IMyDataCallBack;
import com.ximalaya.ting.android.routeservice.service.push.IWidgetService;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.app.XmAppHelper;

import java.util.List;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2023/4/17
 * Description：
 */
public class DailyNewsWidgetProvider4x2 extends BaseAppWidgetProvider {
    private AppWidgetManager mAppWidgetManager;
    public static int id_default_layout;
    public static int id_control_layout;
    public static int id_widget_root;
    public static int id_news_layout1;
    public static int id_news_tv1;
    public static int id_news_play_iv1;
    public static int id_news_layout2;
    public static int id_news_tv2;
    public static int id_news_play_iv2;
    public static int id_default_tv;
    public static int layout_host_reflect_appwidget_layout;
    public static int layout_host_reflect_appwidget_layout_match;
    public static int drawable_host_reflect_widget_pause;
    public static int drawable_host_reflect_widget_play;
    public static int color_host_reflect_widget_text_color_normal;
    public static int color_host_reflect_widget_text_color_red;

    public static final String TRACE_TAG = "DailyNewsWidgetProvider4x2";
    public static final String EXTRA_TRACK_ID = "extra_track_id";

    @Override
    public void onReceive(Context context, Intent intent) {
        super.onReceive(context, intent);
        if (intent == null || intent.getAction() == null) {
            return;
        }

        if (intent.getAction().equals(ConstantsOpenSdk.WIDGET_ACTION_DAILY_NEWS_PLAY)) {
            long trackId = intent.getLongExtra(EXTRA_TRACK_ID, 0);
            DailyNewsDataProvider.INSTANCE.handleWidgetPlayClick(context, trackId);
        } else if (intent.getAction().equals(ConstantsOpenSdk.WIDGET_ACTION_DAILY_NEWS_REFRESH)) {
            updateWidget(context, true);
        }
    }

    private int getLayoutId() {
        return BaseWidgetUtil.useMatchParentLayout() ? layout_host_reflect_appwidget_layout_match : layout_host_reflect_appwidget_layout;
    }

    private RemoteViews createMyWidget(Context context) {
        try {
            PendingIntent openPendingIntent = null;
            Intent openIntent = null;
            RemoteViews remoteViews = new RemoteViews(context.getPackageName(), getLayoutId());
            openIntent = new Intent(Intent.ACTION_VIEW);
            openIntent.setData(Uri.parse("iting://open?msg_type=74&guiyin=no_ggzs&&oneKeyNormalExtra=1"));
            openIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
            openIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
            openIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_FROM_OTHER_AREA, "热点其他区域");
            openPendingIntent = PendingIntent.getActivity(context, PendingIntentRequestConstants.WIDGET_NEWS4X2_REQUEST_ROOT, openIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
            remoteViews.setOnClickPendingIntent(id_widget_root, openPendingIntent);
            Logger.i("WidgetProvider", "bindWidgetPendingIntent");
            return remoteViews;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void bindBtnPendingIntent(Context context, RemoteViews remoteViews, boolean btn, int btnId, long trackId) {
        if (remoteViews == null) {
            return;
        }
        try {
            PendingIntent broPendingIntent = null;
            Intent broIntent = null;
            if (trackId == 0) {
                remoteViews.setOnClickPendingIntent(btnId, null);
                return;
            }
            if (btn) {
                // 播放按钮
                String actionName = ConstantsOpenSdk.WIDGET_ACTION_DAILY_NEWS_PLAY;
                broIntent = new Intent(context.getApplicationContext(), DailyNewsWidgetProvider4x2.class);
                broIntent.setAction(actionName);
                broIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
                broIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
                broIntent.putExtra(EXTRA_TRACK_ID, trackId);
                int requestCode = btnId == id_news_play_iv1 ? PendingIntentRequestConstants.WIDGET_NEWS4X2_REQUEST_PLAY1 : PendingIntentRequestConstants.WIDGET_NEWS4X2_REQUEST_PLAY2;
                broPendingIntent = PendingIntent.getBroadcast(context, requestCode, broIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                remoteViews.setOnClickPendingIntent(btnId, broPendingIntent);
            } else {
                PendingIntent openPendingIntent = null;
                Intent openIntent = null;
                openIntent = new Intent(Intent.ACTION_VIEW);
                openIntent.setData(Uri.parse("iting://open?msg_type=74&guiyin=no_ggzs&oneKeyNormalExtra=1&channelGroupId=1&toChannelId=80&toTrackId=" + trackId));
                openIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
                openIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
                openIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_FROM_OTHER_AREA, "热点声音条");
                int requestCode = btnId == id_news_layout1 ? PendingIntentRequestConstants.WIDGET_NEWS4X2_REQUEST_TITLE1 : PendingIntentRequestConstants.WIDGET_NEWS4X2_REQUEST_TITLE2;
                openPendingIntent = PendingIntent.getActivity(context, requestCode, openIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
                remoteViews.setOnClickPendingIntent(btnId, openPendingIntent);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        super.onUpdate(context, appWidgetManager, appWidgetIds);
        mAppWidgetManager = appWidgetManager;
        if (appWidgetIds.length > 0) {
            BaseWidgetUtil.traceWidgetNewUpdate(TRACE_TAG);
            updateWidget(context, true);
        }
    }

    private void updateWidget(Context context, boolean refreshData) {
        if (mAppWidgetManager == null) {
            mAppWidgetManager = AppWidgetManager.getInstance(context);
        }
        if (mAppWidgetManager == null) {
            return;
        }
        int[] appWidgetIds = new int[0];
        try {
            appWidgetIds = mAppWidgetManager.getAppWidgetIds(new ComponentName(context, getClass()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (appWidgetIds == null || appWidgetIds.length == 0) {
            return;
        }
        RemoteViews remoteViews = createMyWidget(context);
        if (remoteViews == null) {
            return;
        }
        Logger.i("WidgetProvider", "updateWidget");
        boolean isAgress = MmkvCommonUtil.getInstance(context).getBooleanCompat(
                PreferenceConstantsInOpenSdk.TINGMAIN_KEY_PRIVACY_POLICY_AGREED_NEW);
        if (!isAgress) {
            remoteViews.setTextViewText(id_default_tv, "请授权“喜马拉雅”\n以使用此小组件");
            remoteViews.setViewVisibility(id_default_layout, View.VISIBLE);
            remoteViews.setViewVisibility(id_control_layout, View.GONE);
            commitUpdate(context, remoteViews);
        } else {
            XmAppHelper.runOnWorkThread(() -> setDataToView(context, remoteViews, refreshData));
        }
    }

    private void commitUpdate(Context context, RemoteViews remoteViews) {
        if (mAppWidgetManager == null) {
            mAppWidgetManager = AppWidgetManager.getInstance(context);
        }
        if (mAppWidgetManager == null) {
            return;
        }
        int[] appWidgetIds = new int[0];
        try {
            appWidgetIds = mAppWidgetManager.getAppWidgetIds(new ComponentName(context, getClass()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (appWidgetIds == null || appWidgetIds.length == 0) {
            return;
        }
        Logger.i("WidgetProvider", "commitUpdate");
        try {
            mAppWidgetManager.updateAppWidget(appWidgetIds, remoteViews);
        } catch (Exception e) {
            e.printStackTrace();
            //https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/1362?pid=1
        }
    }

    @Override
    public void onDeleted(Context context, int[] appWidgetIds) {
        super.onDeleted(context, appWidgetIds);
        // 当控件被删除的时候调用该方法
        Logger.i("WidgetProvider", "onDeleted");
    }

    @Override
    public void onDisabled(Context context) {
        super.onDisabled(context);
        // 最后一个widget从屏幕移除
        traceWidget("卸载");
        MMKVUtil.getInstance().saveInt("default_widget_number_key_news_4x2", 0);
        Logger.i("WidgetProvider", "onDisabled");
    }

    @Override
    public void onEnabled(Context context) {
        super.onEnabled(context);
        // 第一个加入到屏幕上
        int num = MMKVUtil.getInstance().getInt("default_widget_number_key_news_4x2", 0);
        if (num == 0) {
            num++;
            MMKVUtil.getInstance().saveInt("default_widget_number_key_news_4x2", num);
        }
        traceWidget("添加");
        Logger.i("WidgetProvider", "onEnabled");
    }

    @Override
    protected void onUpdateWidget(Context context) {
        XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
        if (xmPlayerService != null && xmPlayerService.getCurrPlayModel() instanceof Track && xmPlayerService.getLastPlayModel() instanceof Track) {
            Track currtentTrack = (Track) xmPlayerService.getCurrPlayModel();
            Track lastTrack = (Track) xmPlayerService.getLastPlayModel();
            if (currtentTrack.getChannelId() != 80 && lastTrack.getChannelId() != 80) {
                return;
            }
            if (currtentTrack.getChannelGroupId() != 1 && lastTrack.getChannelGroupId() != 1) {
                return;
            }
        }
        updateWidget(context, false);
    }

    @Override
    public void onInitUI(Context context, Track track) {

    }

    private void setDataToView(Context context, RemoteViews remoteViews, boolean refreshData) {
        if (remoteViews == null) {
            return;
        }
        XmPlayerService xmPlayerService = XmPlayerService.getPlayerSrvice();
        long playingTrackId = 0L;
        if (xmPlayerService != null && xmPlayerService.getCurrPlayModel() instanceof Track) {
            Track cacheTrack = (Track) xmPlayerService.getCurrPlayModel();
            if (cacheTrack.getChannelId() == 80L && cacheTrack.getChannelGroupId() == 1L) {
                playingTrackId = cacheTrack.getDataId();
            }
        }
        DailyNewsDataProvider.INSTANCE.requestDataFromServer(refreshData, playingTrackId, new IMyDataCallBack<List<Track>>() {
            @Override
            public void onSuccess(@Nullable List<Track> data) {
                XmAppHelper.runOnWorkThread(() -> {
                    long playingId = 0L;
                    if (XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().getCurrPlayModel() instanceof Track) {
                        playingId = XmPlayerService.getPlayerSrvice().getCurrPlayModel().getDataId();
                    }
                    if (data == null || data.size() == 0) {
                        remoteViews.setViewVisibility(id_default_layout, View.VISIBLE);
                        remoteViews.setViewVisibility(id_control_layout, View.GONE);
                    } else if (data.size() == 1) {
                        remoteViews.setViewVisibility(id_default_layout, View.GONE);
                        remoteViews.setViewVisibility(id_control_layout, View.VISIBLE);
                        remoteViews.setViewVisibility(id_news_layout2, View.INVISIBLE);
                        remoteViews.setTextViewText(id_news_tv1, data.get(0).getTrackTitle());
                        if (playingId == data.get(0).getDataId()) {
                            remoteViews.setTextColor(id_news_tv1, context.getResources().getColor(color_host_reflect_widget_text_color_red));
                        } else {
                            remoteViews.setTextColor(id_news_tv1, context.getResources().getColor(color_host_reflect_widget_text_color_normal));
                        }
                        if (playingId == data.get(0).getDataId() && XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().isPlaying()) {
                            remoteViews.setImageViewResource(id_news_play_iv1, drawable_host_reflect_widget_pause);
                        } else {
                            remoteViews.setImageViewResource(id_news_play_iv1, drawable_host_reflect_widget_play);
                        }
                        bindBtnPendingIntent(context, remoteViews, true, id_news_play_iv1, data.get(0).getDataId());
                        bindBtnPendingIntent(context, remoteViews, true, id_news_play_iv2, 0);
                        bindBtnPendingIntent(context, remoteViews, false, id_news_layout1, data.get(0).getDataId());
                        bindBtnPendingIntent(context, remoteViews, false, id_news_layout2, 0);
                    } else {
                        remoteViews.setViewVisibility(id_default_layout, View.GONE);
                        remoteViews.setViewVisibility(id_control_layout, View.VISIBLE);
                        remoteViews.setViewVisibility(id_news_layout2, View.VISIBLE);
                        remoteViews.setTextViewText(id_news_tv1, data.get(0).getTrackTitle());
                        remoteViews.setTextViewText(id_news_tv2, data.get(1).getTrackTitle());
                        if (playingId == data.get(0).getDataId()) {
                            remoteViews.setTextColor(id_news_tv1, context.getResources().getColor(color_host_reflect_widget_text_color_red));
                        } else {
                            remoteViews.setTextColor(id_news_tv1, context.getResources().getColor(color_host_reflect_widget_text_color_normal));
                        }
                        if (playingId == data.get(1).getDataId()) {
                            remoteViews.setTextColor(id_news_tv2, context.getResources().getColor(color_host_reflect_widget_text_color_red));
                        } else {
                            remoteViews.setTextColor(id_news_tv2, context.getResources().getColor(color_host_reflect_widget_text_color_normal));
                        }
                        if (playingId == data.get(0).getDataId() && XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().isPlaying()) {
                            remoteViews.setImageViewResource(id_news_play_iv1,  drawable_host_reflect_widget_pause);
                        } else {
                            remoteViews.setImageViewResource(id_news_play_iv1, drawable_host_reflect_widget_play);
                        }
                        if (playingId == data.get(1).getDataId() && XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().isPlaying()) {
                            remoteViews.setImageViewResource(id_news_play_iv2, drawable_host_reflect_widget_pause);
                        } else {
                            remoteViews.setImageViewResource(id_news_play_iv2, drawable_host_reflect_widget_play);
                        }
                        bindBtnPendingIntent(context, remoteViews, true, id_news_play_iv1, data.get(0).getDataId());
                        bindBtnPendingIntent(context, remoteViews, true, id_news_play_iv2, data.get(1).getDataId());
                        bindBtnPendingIntent(context, remoteViews, false, id_news_layout1, data.get(0).getDataId());
                        bindBtnPendingIntent(context, remoteViews, false, id_news_layout2, data.get(1).getDataId());
                    }
                    commitUpdate(context, remoteViews);
                });
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    public static void traceWidget(String type) {
        IWidgetService iWidgetService = RouterServiceManager.getInstance().getService(IWidgetService.class);
        if (iWidgetService != null) {
            iWidgetService.traceWidgetNewClick(TRACE_TAG, type);
        }
    }
}