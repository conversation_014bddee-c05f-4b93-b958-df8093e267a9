package com.ximalaya.ting.android.opensdk.player.advertis;

import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by le.xin on 2020/6/3.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class SoundAdDiffRquestParams {
    public long trackId;
    public long albumId;
    public boolean isAuthorized;
    public long freeListenAlbumId;
    public int mode;
    public int playMethod;
    public boolean duringPlay;
    public boolean isPauseAd;
    public int pageMode = -1;
    public boolean isPreload;
    public long radioId;
    public long scheduleId;
    public boolean containPlay;
    public int adScene;
    public int playerScene;
    public boolean playPageRevision;
    public String newPlayPageVersion; // 0或者不传：旧播放页，1：新RN播放页 ，n：xx版本播放页
    public int sourceType; //广告来源

    public Map<String, String> createRequestParmas() {
        Map<String, String> param = new HashMap<String, String>();
        if(trackId > 0) {
            param.put("trackId", "" + trackId);
        } else if(radioId > 0) {
            param.put("radioId", "" + radioId);
            param.put("scheduleId", "" + scheduleId);
        }
        if (albumId > 0) {
            param.put("albumId", "" + albumId);
        }

        param.put("mode", mode + "");
        // 如果是续播场景
        if (containPlay) {
            isPauseAd = false;
            duringPlay = false;
        }

        param.put("paused", isPauseAd + "");
        param.put("playMethod", playMethod + "");

        if (isPauseAd) {
            duringPlay = false;
        }
        param.put("duringPlay", duringPlay + "");

        param.put("continuePlay", containPlay + "");
        param.put("adScene", adScene + "");
        param.put("playPageRevision", playPageRevision + "");
        param.put("newPlayPageVersion", newPlayPageVersion);
        param.put("playerScene", playerScene + "");
        param.put("freeListenAlbumId", freeListenAlbumId + "");

        if(pageMode >= 0) {
            param.put(Advertis.FIELD_PAGE_MODE, pageMode + "");
        }

        return param;
    }

    @Override
    public String toString() {
        return "SoundAdDiffRquestParams{" +
                "trackId=" + trackId +
                "albumId=" + albumId +
                "isAuthorized=" + isAuthorized +
                ", freeListenAlbumId=" + freeListenAlbumId +
                ", mode=" + mode +
                ", playMethod=" + playMethod +
                ", duringPlay=" + duringPlay +
                ", isPauseAd=" + isPauseAd +
                ", pageMode=" + pageMode +
                ", isPreload=" + isPreload +
                ", radioId=" + radioId +
                ", scheduleId=" + scheduleId +
                ", containPlay=" + containPlay +
                ", adScene=" + adScene +
                ", playerScene=" + playerScene +
                ", playPageRevision=" + playPageRevision +
                ", newPlayPageVersion='" + newPlayPageVersion + '\'' +
                ", sourceType=" + sourceType +
                '}';
    }
}
