package com.ximalaya.ting.android.elderly.fragment.playpage.component;

import android.content.Context;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.view.HapticFeedbackConstants;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.airbnb.lottie.LottieComposition;
import com.airbnb.lottie.LottieCompositionFactory;
import com.airbnb.lottie.LottieListener;
import com.airbnb.lottie.LottieProperty;
import com.airbnb.lottie.model.KeyPath;
import com.airbnb.lottie.value.LottieValueCallback;
import com.ximalaya.ting.android.elderly.R;
import com.ximalaya.ting.android.elderly.fragment.playpage.ElderlyPlayFragment;
import com.ximalaya.ting.android.elderly.manager.ElderlyPlayPageDataManager;
import com.ximalaya.ting.android.elderly.util.ElderlyAudioPlayUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.manager.AccessibilityModeManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.util.view.AccessibilityClassNameUtil;
import com.ximalaya.ting.android.host.view.XmLottieDrawable;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl;

import java.io.File;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2020/11/12
 * Description：
 */
public class ElderlyControlBarComponent {
    private ElderlyPlayFragment mPlayFragment;
    private ViewGroup mVgBackwardBtn;
    private ImageView mIvBackwardCircle;
    private ViewGroup mVgForwardBtn;
    private ImageView mIvForwardCircle;
    private ImageView mIvPlayPrevBtn;
    private ImageView mIvPlayNextBtn;
    private ViewGroup mVgPlayBtn;
    private ImageView mIvPlayBtnCenterIcon;
    private View mVLoadingStatus;

    private XmLottieDrawable mPlayToPauseLottieDrawable;
    private XmLottieDrawable mPauseToPlayLottieDrawable;
    private Context mContext;

    public ElderlyControlBarComponent(Context context, ElderlyPlayFragment fragment, ViewGroup contentView) {
        this.mContext = context;
        this.mPlayFragment = fragment;
        initUi(contentView);
    }
    
    public void initUi(ViewGroup contentView) {
        mVgBackwardBtn = contentView.findViewById(R.id.elderly_vg_play_backward_btn);
        mIvBackwardCircle = contentView.findViewById(R.id.elderly_iv_backward_15_second_circle);
        mVgForwardBtn = contentView.findViewById(R.id.elderly_vg_play_forward_btn);
        mIvForwardCircle = contentView.findViewById(R.id.elderly_iv_forward_15_second_circle);
        mIvPlayPrevBtn = contentView.findViewById(R.id.elderly_iv_play_prev_btn);
        mIvPlayNextBtn = contentView.findViewById(R.id.elderly_iv_play_next_btn);
        mVgPlayBtn = contentView.findViewById(R.id.elderly_vg_play_btn);
        AccessibilityClassNameUtil.setAccessibilityClassName(mVgPlayBtn, AccessibilityClassNameUtil.VIEW_TYPE_BUTTON);
        mIvPlayBtnCenterIcon = contentView.findViewById(R.id.elderly_iv_play_btn_center_icon);
        mVLoadingStatus = contentView.findViewById(R.id.elderly_iv_play_btn_loading);
        loadLottieResourcesForPlayBtnCenterIcon();

        setOnClickListenerAndBindAutoTraceData(mVgBackwardBtn);
        setOnClickListenerAndBindAutoTraceData(mVgForwardBtn);
        setOnClickListenerAndBindAutoTraceData(mIvPlayPrevBtn);
        setOnClickListenerAndBindAutoTraceData(mIvPlayNextBtn);
        setOnClickListenerAndBindAutoTraceData(mVgPlayBtn);
    }

    private void setOnClickListenerAndBindAutoTraceData(View view) {
        view.setOnClickListener(mOnClickListener);
    }


    private void setPlayBtnCenterIconColor(int color) {
        if (mPlayToPauseLottieDrawable != null) {
            mPlayToPauseLottieDrawable.addValueCallback(new KeyPath("**"), LottieProperty.COLOR_FILTER,
                    new LottieValueCallback<>(new PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN)));
        }
        if (mPauseToPlayLottieDrawable != null) {
            mPauseToPlayLottieDrawable.addValueCallback(new KeyPath("**"), LottieProperty.COLOR_FILTER,
                    new LottieValueCallback<>(new PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN)));
        }
    }

    private void loadLottieResourcesForPlayBtnCenterIcon() {
        if (mIvPlayBtnCenterIcon == null) {
            return;
        }
        mPlayToPauseLottieDrawable = new XmLottieDrawable();
        mPlayToPauseLottieDrawable.setScale(0.5f);
        String playToPauseLottiePath = "lottie" + File.separator + "play_page_play_btn_play_to_pause.json";
        LottieCompositionFactory.fromAsset(mContext, playToPauseLottiePath).addListener((composition) -> {
            mPlayToPauseLottieDrawable.setComposition(composition);
            // 如果另一个也加载完成了，就设到控件上
            if (mPauseToPlayLottieDrawable != null && mPauseToPlayLottieDrawable.getComposition() != null) {
                setPlayBtnCenterIconToLottie();
            }
        });
        mPauseToPlayLottieDrawable = new XmLottieDrawable();
        mPauseToPlayLottieDrawable.setScale(0.5f);
        String pauseToPlayLottiePath = "lottie" + File.separator + "play_page_play_btn_pause_to_play.json";
        LottieListener<LottieComposition> pauseToPlayLottieListener = mPauseToPlayLottieDrawable::setComposition;
        LottieCompositionFactory.fromAsset(mContext, pauseToPlayLottiePath).addListener((composition) -> {
            mPauseToPlayLottieDrawable.setComposition(composition);
            // 如果另一个也加载完成了，就设到控件上
            if (mPlayToPauseLottieDrawable != null && mPlayToPauseLottieDrawable.getComposition() != null) {
                setPlayBtnCenterIconToLottie();
            }
        });

    }

    private void setPlayBtnCenterIconToLottie() {
        if (mIvPlayBtnCenterIcon == null) {
            return;
        }
        StateListDrawable stateListDrawable = new StateListDrawable();
        stateListDrawable.addState(new int[]{android.R.attr.state_selected}, mPlayToPauseLottieDrawable);
        stateListDrawable.addState(new int[]{}, mPauseToPlayLottieDrawable);
        if (mIvPlayBtnCenterIcon.isSelected()) {
            mPlayToPauseLottieDrawable.setProgress(1);
        } else {
            mPauseToPlayLottieDrawable.setProgress(1);
        }
        setPlayBtnCenterIconColor(0xffffffff);
        mIvPlayBtnCenterIcon.setImageDrawable(stateListDrawable);
    }

    public void updatePlayStatus() {
        if (mVgPlayBtn == null) {
            return;
        }
        boolean lastSelected = mVgPlayBtn.isSelected();
        boolean isPlaying = XmPlayerManager.getInstance(mContext).isPlaying();
        // selected状态表示播放中
        mVgPlayBtn.setSelected(isPlaying);
        if (lastSelected != mVgPlayBtn.isSelected()) {
            startPlayCenterIconLottie();
        } else {
            if (mIvPlayBtnCenterIcon != null && mIvPlayBtnCenterIcon.getDrawable() instanceof StateListDrawable) {
                Drawable drawable = mIvPlayBtnCenterIcon.getDrawable().getCurrent();
                if (drawable instanceof XmLottieDrawable && ((XmLottieDrawable) drawable).getProgress() != 1) {
                    ((XmLottieDrawable) drawable).setProgress(1);
                }
            }
        }
        mVgPlayBtn.setContentDescription(isPlaying ? "暂停" : "播放");
        if (XmPlayerManager.getInstance(mContext).isLoading()) {
            showLoadingView(true);
        } else {
            showLoadingView(false);
        }
    }

    private void startPlayCenterIconLottie() {
        if (mIvPlayBtnCenterIcon != null && mIvPlayBtnCenterIcon.getDrawable() instanceof StateListDrawable) {
            Drawable drawable = mIvPlayBtnCenterIcon.getDrawable().getCurrent();
            if (drawable instanceof XmLottieDrawable) {
                ((XmLottieDrawable) drawable).playAnimation();
            }
        }
    }

    private void updatePlayBtnStatusIfNeededWhilePlayProgressChanged() {
        // 进度变化时，如果播放按钮不是处于播放状态，可能是有问题的，尝试更新下
        if (!mVgPlayBtn.isSelected()) {
            updatePlayStatus();
        }
    }

    public void updateNextAndPreBtnStatus() {
        boolean hasNext = XmPlayerManager.getInstance(mContext).hasNextSound();
        boolean hasPrevious = XmPlayerManager.getInstance(mContext)
                .hasPreSound();

        XmPlayListControl.PlayMode playMode = XmPlayerManager.getInstance(mContext).getPlayMode();
        if (playMode == XmPlayListControl.PlayMode.PLAY_MODEL_LIST_LOOP
                && !XmPlayerManager.getInstance(mContext).getPlayList()
                .isEmpty()) {
            hasNext = true;
            hasPrevious = true;
        } else if (isFromOneKeyPlay()) {
            hasNext = true;
        }
        if (mIvPlayNextBtn != null && mIvPlayPrevBtn != null) {
            mIvPlayNextBtn.setEnabled(hasNext);
            mIvPlayPrevBtn.setEnabled(hasPrevious);
        }
    }

    private boolean isFromOneKeyPlay() {
        PlayableModel currPlayableModel = XmPlayerManager.getInstance(mContext)
                .getCurrSound();
        if (null != currPlayableModel) {
            // 一键听声音
            return PlayableModel.KIND_TRACK.equals(currPlayableModel.getKind())
                    && ((Track) currPlayableModel).getPlaySource() == ConstantsOpenSdk
                    .PLAY_FROM_ONE_KEY_PLAY;
        }
        return false;
    }



    private void handleForwardClicked() {
        if (mIvForwardCircle != null) {
            AnimationUtil.stopAnimation(mIvForwardCircle);
            AnimationUtil.rotateOnce(mContext, mIvForwardCircle, true);
        }
        int pos = XmPlayerManager.getInstance(mContext).getPlayCurrPositon() + 15 * 1000;
        pos = Math.min(pos, XmPlayerManager.getInstance(mContext).getDuration());
        ElderlyAudioPlayUtil.seekTo(mContext, pos);
    }

    private void handleBackwardClicked() {
        if (mIvBackwardCircle != null) {
            AnimationUtil.stopAnimation(mIvBackwardCircle);
            AnimationUtil.rotateOnce(mContext, mIvBackwardCircle, false);
        }
        ElderlyAudioPlayUtil.backward15Second(mContext);
    }

    public void showLoadingView(boolean show) {
        if (show) {
            AnimationUtil.rotateView(mContext, mVLoadingStatus);
            mVLoadingStatus.setVisibility(View.VISIBLE);
        } else {
            mVLoadingStatus.setVisibility(View.INVISIBLE);
            AnimationUtil.stopAnimation(mVLoadingStatus);
        }
    }

    public void onResume() {
        updatePlayStatus();
        updateNextAndPreBtnStatus();
        if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
            HandlerManager.postOnUIThreadDelay(() -> {
                if (mPlayFragment != null && mPlayFragment.canUpdateUi() && mVgPlayBtn != null) {
                    mVgPlayBtn.requestFocus();
                }
            }, 500);
        }
    }

    public void onPlayStart() {
        showLoadingView(false);
        updatePlayStatus();
    }

    public void onPlayProgress() {
        updatePlayBtnStatusIfNeededWhilePlayProgressChanged();
    }


    public void onSoundSwitch() {
        updatePlayStatus();
        updateNextAndPreBtnStatus();
    }


    private boolean checkCopyright() {
        PlayingSoundInfo soundInfo = mPlayFragment.getCurSoundInfo();
        if (soundInfo != null && soundInfo.trackInfo2TrackM() != null) {
            return soundInfo.trackInfo2TrackM().isHasCopyRight();
        }
        return false;
    }


    public void onStartGetAdsInfo(int playMethod, boolean duringPlay, boolean isPaused) {
        if (!XmPlayerManager.getInstance(mContext).isPlaying()) {
            updateNextAndPreBtnStatus();
            if (!isPaused && !duringPlay) {
                showLoadingView(true);
            }
        }
    }



    private View.OnClickListener mOnClickListener = v -> {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        if (!checkCopyright()) {
            CustomToast.showFailToast(ElderlyPlayPageDataManager.getInstance().getNoCopyrightMsg());
            return;
        }
        if (v == mVgBackwardBtn) {
            handleBackwardClicked();
        } else if (v == mVgForwardBtn) {
            handleForwardClicked();
        } else if (v == mIvPlayPrevBtn) {
            ElderlyAudioPlayUtil.playPrev(mContext);
        } else if (v == mIvPlayNextBtn) {
            ElderlyAudioPlayUtil.playNext(mContext);
        } else if (v == mVgPlayBtn) {
            ElderlyAudioPlayUtil.playOrPause(mContext, mPlayFragment.getCurSoundInfo());
        }
        if (v != null) {
            v.performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY);
        }
    };
}
