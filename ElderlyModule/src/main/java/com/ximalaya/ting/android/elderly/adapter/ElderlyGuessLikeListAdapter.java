package com.ximalaya.ting.android.elderly.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.elderly.R;
import com.ximalaya.ting.android.elderly.data.model.ElderlyGuessLikeModel;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.util.ui.AlbumTagUtilNew;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2020/11/17
 * Description：
 */
public class ElderlyGuessLikeListAdapter extends HolderAdapter<ElderlyGuessLikeModel> {

    public ElderlyGuessLikeListAdapter(Context context, List<ElderlyGuessLikeModel> listData) {
        super(context, listData);
        this.listData = listData;
    }

    @NonNull
    public List<ElderlyGuessLikeModel> getListData() {
        if (listData == null) {
            listData = new ArrayList<>();
        }
        return listData;
    }

    @Override
    public void onClick(View view, ElderlyGuessLikeModel album, int position, BaseViewHolder holder) {
        AlbumEventManage.startMatchAlbumFragment(album.getAlbumId(),
                AlbumEventManage.FROM_OTHERPAGE, ConstantsOpenSdk.PLAY_FROM_NONE,
                null, null, -1, MainApplication.getMainActivity());
        // 老年模式标签二级列表页-专辑卡片  点击事件
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .click(53066) // 用户点击时上报
                .put("currPage", "oldModeTagSecondaryList")
//                .put("tagId", XX) // 若是标签页，则传标签 id，猜你喜欢时不用传
                .put("pageTitle", "猜你喜欢") // 记录页面标题文案，例如：猜你喜欢，小品
                .put("albumId", String.valueOf(album.getAlbumId()))
                .put("positionNew", String.valueOf(position + 1)); // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
        if (album.getTraceData() != null) {
            trace.put("rec_src", album.getTraceData().getRecSrc()) // 推荐字段，若有则传
                    .put("rec_track", album.getTraceData().getRecTrack()) // 推荐字段，若有则传
                    .put("ubtTraceId", album.getTraceData().getUbtTraceId()); // 推荐字段，若有则传
        }
        trace.createTrace();
    }

    @Override
    public int getConvertViewId() {
        return R.layout.elderly_item_list;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new SubscribeViewHolder(convertView);
    }

    @Override
    public void bindViewDatas(BaseViewHolder h, ElderlyGuessLikeModel album, int position) {
        SubscribeViewHolder holder = (SubscribeViewHolder) h;
        AutoTraceHelper.bindData(holder.root, album);

        holder.albumTv.setText(TextUtils.isEmpty(album.getTitle()) ? "" : album.getTitle());

        ImageManager.from(context).displayImage(holder.coverIv, album.getImg(),
                com.ximalaya.ting.android.host.R.drawable.host_default_album,
                BaseUtil.dp2px(context, 100), BaseUtil.dp2px(context, 100));

        holder.divider.setVisibility(position == getCount() - 1 ? View.INVISIBLE : View.VISIBLE);
        ImageManager.from(context).displayImage(holder.boutiqueIv,
                AlbumTagUtilNew.getInstance().getAlbumTagUrl(album.getAlbumSubscript()), -1, null);
        long playCount = album.getPlayCount();
        holder.countTv.setText(StringUtil.getFriendlyNumStr(playCount));
        setClickListener(holder.root, album, position, h);
    }

    public static class SubscribeViewHolder extends BaseViewHolder {
        public View root;
        public View divider;
        public RoundImageView coverIv;
        public ImageView boutiqueIv;
        public TextView albumTv;
        public TextView countTv;


        public SubscribeViewHolder(View convertView) {
            root = convertView;
            divider = convertView.findViewById(R.id.elderly_item_history_divider);
            albumTv = convertView.findViewById(R.id.elderly_item_history_album_tv);
            coverIv = convertView.findViewById(R.id.elderly_item_history_iv);
            boutiqueIv = convertView.findViewById(R.id.elderly_item_album_jp_iv);
            countTv = convertView.findViewById(R.id.elderly_item_count_tv);
        }
    }
}
