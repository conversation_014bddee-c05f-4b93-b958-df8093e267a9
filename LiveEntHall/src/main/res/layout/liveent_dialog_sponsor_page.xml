<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/liveent_bg_dialog_sponsor"
    tools:layout_height="350dp">

    <ImageView
        android:id="@+id/live_ent_sponsor_top_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/live_ent_sponsor_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:ellipsize="end"
        android:maxWidth="100dp"
        android:singleLine="true"
        android:text="***"
        android:textColor="#DFFFF0"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/live_ent_sponsor_title_suffix"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="嘻嘻哈哈就是我" />

    <TextView
        android:id="@+id/live_ent_sponsor_title_suffix"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/live_ent_sponsor_info_title"
        android:textColor="#DFFFF0"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/live_ent_sponsor_title"
        app:layout_constraintTop_toTopOf="@id/live_ent_sponsor_title" />

    <com.ximalaya.ting.android.live.common.view.CountdownTextView
        android:id="@+id/live_ent_sponsor_rested_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:textColor="#80DFFFF0"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_ent_sponsor_title"
        tools:text="剩余时间：3天23时5分" />

    <ImageView
        android:id="@+id/live_ent_sponsor_rule"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="9dp"
        android:layout_marginEnd="4dp"
        android:padding="11dp"
        android:src="@drawable/liveent_ic_sponsor_rule_entry"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/live_ent_sponsor_card_bg"
        android:layout_width="0dp"
        android:layout_height="190dp"
        android:layout_marginHorizontal="19dp"
        android:layout_marginTop="95dp"
        android:scaleType="fitXY"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#3E606A" />

    <TextView
        android:id="@+id/live_ent_sponsor_gift_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="22dp"
        android:text="冠名所需礼物"
        android:textColor="#DFFFF0"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/live_ent_sponsor_card_bg"
        app:layout_constraintTop_toTopOf="@id/live_ent_sponsor_card_bg" />

    <TextView
        android:id="@+id/live_ent_sponsor_gift_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="暂无礼物"
        android:textColor="#99DFFFF0"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="@id/live_ent_sponsor_gift_hint"
        app:layout_constraintTop_toBottomOf="@id/live_ent_sponsor_gift_hint"
        tools:text="玉京仙子" />

    <TextView
        android:id="@+id/live_ent_sponsor_gift_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginBottom="2dp"
        android:textColor="#DFFFF0"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/live_ent_sponsor_gift_name"
        app:layout_constraintStart_toEndOf="@id/live_ent_sponsor_gift_name"
        app:layout_constraintTop_toTopOf="@id/live_ent_sponsor_gift_name"
        tools:text="x16" />

    <TextView
        android:id="@+id/live_ent_sponsor_current_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="45dp"
        android:text="当前冠名"
        android:textColor="#ffdffff0"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/live_ent_sponsor_card_bg"
        app:layout_constraintTop_toBottomOf="@id/live_ent_sponsor_gift_hint" />

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/live_ent_sponsor_current_avatar"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:scaleType="centerCrop"
        android:src="@drawable/liveent_ic_sponsor_default_avatar"
        android:visibility="gone"
        app:border_bg_color="@color/live_transparent"
        app:border_color="#DFFFF0"
        app:border_width="1dp"
        app:corner_radius="21dp"
        app:layout_constraintStart_toStartOf="@id/live_ent_sponsor_card_bg"
        app:layout_constraintTop_toBottomOf="@id/live_ent_sponsor_current_hint"
        app:round_background="true" />

    <ImageView
        android:id="@+id/live_ent_sponsor_current_default_avatar"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/liveent_ic_sponsor_default_avatar"
        app:layout_constraintStart_toStartOf="@id/live_ent_sponsor_card_bg"
        app:layout_constraintTop_toBottomOf="@id/live_ent_sponsor_current_hint" />

    <TextView
        android:id="@+id/live_ent_sponsor_current_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="11dp"
        android:text="虚位以待"
        android:textColor="#99DFFFF0"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/live_ent_sponsor_current_default_avatar"
        app:layout_constraintStart_toEndOf="@id/live_ent_sponsor_current_default_avatar"
        app:layout_constraintTop_toTopOf="@id/live_ent_sponsor_current_default_avatar" />

    <ImageView
        android:id="@+id/live_ent_sponsor_gift"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginTop="74dp"
        android:layout_marginEnd="36dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/live_ent_sponsor_goto_gift"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="36dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="6dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/live_ent_sponsor_current_default_avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/live_ent_sponsor_current_default_avatar"
        tools:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="去冠名"
            android:textColor="#ffdffff0"
            android:textSize="12sp" />

        <ImageView
            android:layout_width="5dp"
            android:layout_height="9dp"
            android:layout_marginStart="3dp"
            android:layout_marginTop="0.7dp"
            android:src="@drawable/liveent_ic_sponsor_nav_arrow_right" />
    </LinearLayout>

    <ImageView
        android:id="@+id/live_ent_sponsor_bottom_bg"
        android:layout_width="match_parent"
        android:layout_height="86dp"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:background="#11161A" />

    <TextView
        android:id="@+id/live_ent_sponsor_bottom_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="29dp"
        android:text="主播未设置冠名礼物哦~"
        android:textColor="#80DFFFF0"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>