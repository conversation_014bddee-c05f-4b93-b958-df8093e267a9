<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/live_ent_cl_seat_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="15dp"
    android:layout_marginRight="15dp"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/seat_preside_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center">

        <!-- 主题卡片容器 -->
        <FrameLayout
            android:id="@+id/theme_card_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.ximalaya.ting.android.live.hall.view.seat.SeatView
            android:id="@+id/live_ent_sv_preside"
            android:layout_width="@dimen/live_ent_seat_width"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.ximalaya.ting.android.live.hall.view.seat.SeatView
        android:id="@+id/live_ent_sv_seat1"
        android:layout_width="@dimen/live_ent_seat_width"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        app:layout_constraintEnd_toStartOf="@id/live_ent_sv_seat2"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/seat_preside_container" />

    <com.ximalaya.ting.android.live.hall.view.seat.SeatView
        android:id="@+id/live_ent_sv_seat2"
        android:layout_width="@dimen/live_ent_seat_width"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@+id/live_ent_sv_seat3"
        app:layout_constraintStart_toEndOf="@id/live_ent_sv_seat1"
        app:layout_constraintTop_toBottomOf="@id/seat_preside_container" />

    <com.ximalaya.ting.android.live.hall.view.seat.SeatView
        android:id="@+id/live_ent_sv_seat3"
        android:layout_width="@dimen/live_ent_seat_width"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@+id/live_ent_sv_seat4"
        app:layout_constraintStart_toEndOf="@id/live_ent_sv_seat2"
        app:layout_constraintTop_toBottomOf="@id/seat_preside_container" />

    <com.ximalaya.ting.android.live.hall.view.seat.SeatView
        android:id="@+id/live_ent_sv_seat4"
        android:layout_width="@dimen/live_ent_seat_width"
        android:layout_height="wrap_content"
        android:layout_marginEnd="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/live_ent_sv_seat3"
        app:layout_constraintTop_toBottomOf="@id/seat_preside_container" />

    <com.ximalaya.ting.android.live.hall.view.seat.SeatView
        android:id="@+id/live_ent_sv_seat5"
        android:layout_width="@dimen/live_ent_seat_width"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@+id/live_ent_sv_seat1"
        app:layout_constraintRight_toRightOf="@+id/live_ent_sv_seat1"
        app:layout_constraintTop_toBottomOf="@id/live_ent_sv_seat1" />

    <com.ximalaya.ting.android.live.hall.view.seat.SeatView
        android:id="@+id/live_ent_sv_seat6"
        android:layout_width="@dimen/live_ent_seat_width"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@+id/live_ent_sv_seat2"
        app:layout_constraintRight_toRightOf="@id/live_ent_sv_seat2"
        app:layout_constraintTop_toBottomOf="@id/live_ent_sv_seat2" />

    <com.ximalaya.ting.android.live.hall.view.seat.SeatView
        android:id="@+id/live_ent_sv_seat7"
        android:layout_width="@dimen/live_ent_seat_width"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@+id/live_ent_sv_seat3"
        app:layout_constraintRight_toRightOf="@id/live_ent_sv_seat3"
        app:layout_constraintTop_toBottomOf="@id/live_ent_sv_seat3" />

    <com.ximalaya.ting.android.live.hall.view.seat.SeatView
        android:id="@+id/live_ent_sv_seat8"
        android:layout_width="@dimen/live_ent_seat_width"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@+id/live_ent_sv_seat4"
        app:layout_constraintRight_toRightOf="@id/live_ent_sv_seat4"
        app:layout_constraintTop_toBottomOf="@id/live_ent_sv_seat4" />

    <com.ximalaya.ting.android.live.hall.view.seat.SeatView
        android:id="@+id/live_ent_sv_guest"
        android:layout_width="@dimen/live_ent_seat_width"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@+id/live_ent_sv_seat4"
        app:layout_constraintRight_toRightOf="@id/live_ent_sv_seat4"
        app:layout_constraintTop_toBottomOf="@id/live_ent_sv_seat4"
        tools:visibility="gone" />
</merge>