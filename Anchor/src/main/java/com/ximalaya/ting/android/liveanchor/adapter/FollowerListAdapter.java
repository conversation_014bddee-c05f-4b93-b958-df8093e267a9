package com.ximalaya.ting.android.liveanchor.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.model.anchor.Anchor;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.live.host.data.manage.AdminFollowListM;
import com.ximalaya.ting.android.live.host.data.manage.AdminListM;
import com.ximalaya.ting.android.liveanchor.R;

import java.util.List;

/**
 * Created by neil.qian on 2016/9/18.
 *
 * <AUTHOR>
 */
public class FollowerListAdapter extends HolderAdapter<Anchor> {
    private ItemClickListener<Anchor> itemClickListener;
    private AdminListM.AdminList<AdminListM.Admin> admins;

    public FollowerListAdapter(Context context, List<Anchor> listData) {
        super(context, listData);
    }

    @Override
    public void onClick(View view, Anchor anchor, int position, BaseViewHolder holder) {
        if (itemClickListener != null) {
            if (!itemClickListener.canClick()) return;
            itemClickListener.onClick(view, anchor, position, (ViewHolder) holder);
            if (view.getId() == R.id.live_item_add) {
                ViewHolder viewHolder = (ViewHolder) holder;
                viewHolder.add.setVisibility(View.GONE);
                viewHolder.addProgress.setVisibility(View.VISIBLE);
            }
        }
    }

    public void setItemClickListener(ItemClickListener<Anchor> itemClickListener) {
        this.itemClickListener = itemClickListener;
    }

    public void setAdmins(AdminListM.AdminList<AdminListM.Admin> admins) {
        this.admins = admins;
    }

    @Override
    public int getConvertViewId() {
        return R.layout.liveanchor_item_compose_live_admin;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        ViewHolder holder = new ViewHolder();
        holder.avatar = (RoundImageView) convertView.findViewById(R.id.live_item_avatar);
        holder.name = (TextView) convertView.findViewById(R.id.live_item_name);
        holder.add = (ImageView) convertView.findViewById(R.id.live_item_add);
        holder.grade = (ImageView) convertView.findViewById(R.id.live_item_grade);
        holder.addProgress = (ProgressBar) convertView.findViewById(R.id.live_add_progress);
        holder.root = (ViewGroup) convertView.findViewById(R.id.live_item_root);
        return holder;
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, Anchor info, int position) {
        ViewHolder hold = (ViewHolder) holder;
        String logo = getAvatarUrlFromAnchor(info);
        if (info.getMiddleLogo() != null) {
            ImageManager.from(context).displayImage(hold.avatar, logo, com.ximalaya.ting.android.live.common.R.drawable.live_default_avatar_132,
                    BaseUtil.dp2px(context, 30), BaseUtil.dp2px(context, 30));
        }
        hold.name.setText(info.getNickName());
        LocalImageUtil.setVipLevelBackGround(hold.grade,info.getUid(),info.isVerified(),info.getAnchorGrade(),info.getVerifyType());
        setClickListener(hold.add, info, position, hold);
        setClickListener(hold.root, info, position, hold);
        if (admins != null && admins.contains(info.getUid())) {
            hold.add.setBackgroundResource(com.ximalaya.ting.android.live.host.R.drawable.live_btn_admin_remove);
            hold.added = true;
        } else {
            hold.add.setBackgroundResource(com.ximalaya.ting.android.live.host.R.drawable.live_btn_admin_add);
            hold.added = false;
        }
        if (info instanceof AdminFollowListM.AdminAnchor) {
            final AdminFollowListM.AdminAnchor adminAnchor = (AdminFollowListM.AdminAnchor) info;
            if (adminAnchor.isTargetIsAdmin()) {
                hold.add.setBackgroundResource(com.ximalaya.ting.android.live.host.R.drawable.live_btn_admin_remove);
                hold.added = true;
            } else {
                hold.add.setBackgroundResource(com.ximalaya.ting.android.live.host.R.drawable.live_btn_admin_add);
                hold.added = false;
            }
        }
        hold.add.setVisibility(View.VISIBLE);
        hold.addProgress.setVisibility(View.GONE);
    }

    private String getAvatarUrlFromAnchor(Anchor anchor) {
        String logo = null;
        if (anchor == null) return logo;
        if (TextUtils.isEmpty(anchor.getLogo())) {
            if (TextUtils.isEmpty(anchor.getMiddleLogo())) {
                if (TextUtils.isEmpty(anchor.getLargeLogo())) {
                    logo = "";
                } else {
                    logo = anchor.getLargeLogo();
                }
            } else {
                logo = anchor.getMiddleLogo();
            }
        } else {
            logo = anchor.getLogo();
        }
        return logo;
    }

    public static class ViewHolder extends BaseViewHolder {
        public RoundImageView avatar;
        public TextView name;
        public ImageView add;
        public ImageView grade;
        public ProgressBar addProgress;
        public ViewGroup root;
        public boolean added;
    }

    public interface ItemClickListener<T> {
        void onClick(View view, T t, int position, ViewHolder holder);

        boolean canClick();
    }
}
