<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@id/video_bottom_bar"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/video_bg_controller_bottom"
    android:gravity="bottom">

    <LinearLayout
        android:id="@+id/video_bottom_bar_inner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/video_iv_play"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="4dp"
            android:contentDescription="播放"
            android:src="@drawable/video_icon_play" />

        <ImageView
            android:id="@+id/video_ic_mute"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginLeft="8dp"
            android:contentDescription="有声、静音"
            android:src="@drawable/video_ic_mute"
            android:visibility="gone"
            tools:visibility="gone" />

        <ImageView
            android:id="@+id/video_iv_next"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="4dp"
            android:alpha="0.35"
            android:src="@drawable/video_ic_next"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@id/tv_current_position"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_marginLeft="4dp"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:text="00:00"
            android:textColor="@android:color/white"
            android:textSize="12sp" />

        <SeekBar
            android:id="@+id/seek_bar"
            style="@style/video_seek_bar_app_theme"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:thumbOffset="8dp"
            tools:progress="0" />

        <TextView
            android:id="@id/tv_duration"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:text="00:00"
            android:textColor="@android:color/white"
            android:layout_marginRight="4dp"
            android:textSize="12sp" />

        <TextView
            android:id="@id/tv_change_resolution"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:text="流畅"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <ImageView
            android:id="@id/tv_switch_orientation"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginRight="8dp"
            android:layout_marginLeft="4dp"
            android:contentDescription="切换全屏"
            android:src="@drawable/video_ic_zoom_land" />
    </LinearLayout>
</LinearLayout>
