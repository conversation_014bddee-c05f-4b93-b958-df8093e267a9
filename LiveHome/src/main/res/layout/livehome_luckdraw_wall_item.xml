<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/livehome_luckdraw_wall_item_bg">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/livehome_luckdraw_room_item_wave"
        android:layout_width="58dp"
        android:layout_height="58dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        app:corner_radius="50dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/live_space0"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintBottom_toBottomOf="@+id/livehome_luckdraw_room_item_wave"
        app:layout_constraintEnd_toEndOf="@+id/livehome_luckdraw_room_item_wave"
        app:layout_constraintStart_toStartOf="@+id/livehome_luckdraw_room_item_wave"
        app:layout_constraintTop_toTopOf="@+id/livehome_luckdraw_room_item_wave" />


    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/livehome_luckdraw_room_item_avatar"
        android:layout_width="46dp"
        android:layout_height="46dp"
        app:corner_radius="100dp"
        app:layout_constraintBottom_toBottomOf="@+id/live_space0"
        app:layout_constraintEnd_toEndOf="@+id/live_space0"
        app:layout_constraintStart_toStartOf="@+id/live_space0"
        app:layout_constraintTop_toTopOf="@+id/live_space0"
        tools:src="@drawable/host_default_avatar_88" />

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/livehome_luckdraw_room_item_ring"
        android:layout_width="46dp"
        android:layout_height="46dp"
        app:corner_radius="50dp"
        android:background="@drawable/livehome_lucky_draw_wall_item_ring"
        app:layout_constraintBottom_toBottomOf="@+id/live_space0"
        app:layout_constraintEnd_toEndOf="@+id/live_space0"
        app:layout_constraintStart_toStartOf="@+id/live_space0"
        app:layout_constraintTop_toTopOf="@+id/live_space0" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="4dp"
        android:layout_toEndOf="@+id/livehome_luckdraw_room_item_avatar"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/livehome_luckdraw_room_item_wave"
        app:layout_constraintLeft_toRightOf="@+id/livehome_luckdraw_room_item_wave"
        app:layout_constraintTop_toTopOf="@+id/livehome_luckdraw_room_item_wave">

        <TextView
            android:singleLine="true"
            android:ellipsize="end"
            android:id="@+id/livehome_luckdraw_room_item_nickname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/livehome_6F3900_FFFFFF"
            android:textSize="14sp"
            tools:text="主播昵称主播昵称" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:background="@drawable/livehome_luckdraw_wall_diamong_bg">

            <ImageView
                android:layout_gravity="center_vertical"
                android:layout_width="12dp"
                android:layout_height="16dp"
                android:src="@drawable/livehome_luckdraw_wall_room_redpacket_small" />

            <TextView
                android:includeFontPadding="false"
                android:gravity="center"
                android:singleLine="true"
                android:ellipsize="end"
                android:id="@+id/livehome_luckdraw_room_diamond_tv"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:background="@drawable/livehome_luckdraw_wall_diamong_bg"
                android:textColor="@color/livehome_6F3900_FFB94E"
                android:textSize="11sp"
                tools:text="9999喜钻" />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>