package com.ximalaya.ting.android.live.home.luckydraw

import android.animation.Animator
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.graphics.Rect
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.ui.AnimationUtil
import com.ximalaya.ting.android.live.common.lib.base.request.LiveUrlConstants
import com.ximalaya.ting.android.live.common.lib.utils.JsonPrintUtil
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper
import com.ximalaya.ting.android.live.common.lib.utils.XmLiveRequestIdHelper
import com.ximalaya.ting.android.live.home.R
import com.ximalaya.ting.android.live.host.fragment.debug.RoomDebugConfigure
import com.ximalaya.ting.android.live.host.liverouter.home.IHomeLuckDrawWall
import com.ximalaya.ting.android.live.host.liverouter.home.IHomeLuckDrawWallCallback
import com.ximalaya.ting.android.live.host.request.CommonRequestForLiveHost
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONObject


/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18321019958
 * @wiki
 * @server
 * @since 2025/1/8
 */
class HomeLuckDrawWall(val fragment: Fragment) : IHomeLuckDrawWall, OnClickListener {

    var data: HomeLucDrawData? = null

    private var rootView: ViewGroup? = null

    private var roomRecyclerView: RecyclerView? = null

    private var headTitle:TextView ? = null
    private var headSubTitle:TextView? = null

    private var headLayout:View ? = null

    private var isExpand:Boolean = true
    private var isAnimation = false

    private var expandIv:ImageView ? = null

    var adapter: HomeLuckDrawWallAdapter = HomeLuckDrawWallAdapter()

    var joinRoomTv:TextView ? = null

    override fun loadData() {
        Logger.d("qmc_test13", "HomeLuckDrawWall loadData")

        requestAndUpdateUi(true)
    }

    private var delayLoadTask:Runnable ? = null

    private fun requestAndUpdateUi(requestUnfold:Boolean){
        Logger.d("qmc_test13","requestAndUpdateUi requestUnfold $requestUnfold")
        RoomDebugConfigure.getBoolFromGroup("首页mock2个红包房间","home")
        if(delayLoadTask != null){
            HandlerManager.removeCallbacks(delayLoadTask)
        }
        delayLoadTask = Runnable {
            if(RoomDebugConfigure.getBoolFromGroup("首页mock红包数据","home")){
                Logger.d("qmc_test13","requestAndUpdateUi mock 数据")
                val data = HomeLucDrawData()
                data.desc = "合计瓜分9999喜钻"
                data.items.add(HomeLuckDrawRoom().apply {
                    nickname = "主播昵称1"
                    amount = 200
                    itingUrl = "iting://open?msg_type=137&liveroom_id=651596&recTrack=498c27c5-19dd-4d25-8925-4f609e84d7e5&recSrc=null&liveMediaType=1&liveStreamUrl=http%3A%2F%2Flive.test.entertain.hw.l1.xmcdn.com%2Flive%2F2-1-651596-651596-0.flv"
                    avatar =
                        "http://imagev2.test.ximalaya.com/storages/0992-audiotest/FB/3B/CAoVOVcDt4g6AABPkgAAK0Dd.png!op_type=5&upload_type=album&device_type=ios&name=large&magick=jpeg"
                })
                if(RoomDebugConfigure.getBoolFromGroup("首页mock2个红包房间","home")){
                    Logger.d("qmc_test13","requestAndUpdateUi mock 2个数据")
                    data.items.add(HomeLuckDrawRoom().apply {
                        nickname = "主播昵称2"
                        amount = 1000
                        itingUrl = "iting://open?msg_type=137&liveroom_id=651596&recTrack=498c27c5-19dd-4d25-8925-4f609e84d7e5&recSrc=null&liveMediaType=1&liveStreamUrl=http%3A%2F%2Flive.test.entertain.hw.l1.xmcdn.com%2Flive%2F2-1-651596-651596-0.flv"
                        avatar =
                            "http://imagev2.test.ximalaya.com/storages/0992-audiotest/FB/3B/CAoVOVcDt4g6AABPkgAAK0Dd.png!op_type=5&upload_type=album&device_type=ios&name=large&magick=jpeg"
                    })
                }
                this.data = data
                updateUi(requestUnfold)
            }else{
                val url = LiveUrlConstants.getInstance().liveLamiaServiceBaseUrl +"/v1/live/tab/super/packets"
                val params = LiveHelper.buildTimeParams()
                CommonRequestForLiveHost.baseGetRequest(url,params, object : IDataCallBack<HomeLucDrawData?> {
                    override fun onSuccess(data: HomeLucDrawData?) {
                        Logger.d("qmc_test13","requestAndUpdateUi onSuccess data $data")
                        <EMAIL> = data
                        <EMAIL>?.xmRequestId = XmLiveRequestIdHelper.getNewXmQuestId()
                        updateUi(requestUnfold)
                    }

                    override fun onError(code: Int, message: String?) {

                    }

                }
                ) { content ->
                    if(ConstantsOpenSdk.isDebug){
                        Logger.d("qmc_test13","requestAndUpdateUi content ${JsonPrintUtil.print(content)}")
                    }
                    val data = JSONObject(content).optString("data")
                    CommonRequestM.sGson.fromJson(
                        data,
                        HomeLucDrawData::class.java
                    )
                }

                this.data = null
                updateUi(requestUnfold)
            }
        }
        HandlerManager.postOnUIThreadDelay(delayLoadTask,200)
    }

    override fun resume() {
        Logger.d("qmc_test13", "HomeLuckDrawWall resume ")
        requestAndUpdateUi(true)
    }

    override fun pause() {
        Logger.d("qmc_test13", "HomeLuckDrawWall pause ")
        contract(false)
    }

    override fun onClick(v: View?) {
        if(this.data?.items?.size == 1){
            if(v?.id == R.id.live_home_luck_wall_head || v?.id == R.id.live_home_luck_join_room){
                if(this.data?.items?.size == 1){
                    XMTraceApi.Trace()
                        .click(67207) // 用户点击时上报
                        .put("currPage", "liveAudio")
                        .put(XmLiveRequestIdHelper.createTracePropsForClick(this.data?.xmRequestId))
                        .put("dialogType", "单直播")
                        .put("Item", "立即前往")
                        .put("liveId", this.data?.items?.get(0)?.liveId.toString()) // 若点击直播间，则传
                        .put("roomId", this.data?.items?.get(0)?.roomId.toString()) // 若点击直播间，则传
                        .createTrace()
                    this.data?.items?.getOrNull(0)?.let {
                        Router.getActionRouter<MainActionRouter>(Configure.BUNDLE_MAIN)!!
                            .functionAction.handleIting(MainApplication.getMainActivity(), Uri.parse(it.itingUrl))
                    }
                }
            }
        }else{
            if (v?.id == R.id.live_home_luck_expand || v?.id == R.id.live_home_luck_wall_head) {
                rootView?.bringToFront()
                handExpandClick()
            }
        }
    }

    private fun initWall() {
        Logger.d("qmc_test12", "HomeLuckDrawWall initWall")
        if (rootView != null) {
            return
        }
        val context = fragment.context ?: return
        val fragmentView = fragment.view as? ViewGroup ?: return
        rootView = fragmentView.findViewById(R.id.livehome_luck_draw_root)
        if (rootView == null) {
            fragmentView.setPadding(0,0,0,3.dp)//app tab 容器bottomMargin = -3dp
            LayoutInflater.from(context).inflate(R.layout.livehome_layout_luckdraw, fragmentView)
            rootView = fragmentView.findViewById(R.id.livehome_luck_draw_root)
            rootView?.tag = HomeLuckDrawWall::class.java.simpleName
        } else {
            rootView?.bringToFront()
            Logger.d("qmc_test12", "HomeLuckDrawWall rootView has init")
        }

        expandIv = rootView?.findViewById(R.id.live_home_luck_expand)
        expandIv?.setOnClickListener(this)

        headLayout = rootView?.findViewById(R.id.live_home_luck_wall_head)
        headLayout?.setOnClickListener(this)

        headTitle = rootView?.findViewById(R.id.live_home_luck_head_title)
        headSubTitle = rootView?.findViewById(R.id.live_home_luck_head_sub_title)

        joinRoomTv = rootView?.findViewById(R.id.live_home_luck_join_room)

        roomRecyclerView = rootView?.findViewById(R.id.livehome_luck_draw_rooms)
        roomRecyclerView?.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                val p = parent.getChildAdapterPosition(view)
                if(p == 0){
                    outRect.set(11.dp, 6.dp, 6.dp, 12.dp)
                }else if(p == adapter.itemCount-1){
                    outRect.set(6.dp, 6.dp, 11.dp, 12.dp)
                }else{
                    outRect.set(6.dp, 6.dp, 6.dp, 12.dp)
                }
            }
        })
        roomRecyclerView?.layoutManager = LinearLayoutManager(context).apply {
            orientation = LinearLayoutManager.HORIZONTAL
        }
        roomRecyclerView?.adapter = adapter
        Logger.d("qmc_test12", "initWall")
        rootView?.alpha = 0.0f
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun updateUi(requestUnfold:Boolean) {
        Logger.d("qmc_test12", "updateUi requestUnfold $requestUnfold rooms "+data?.items)
        if(data?.items?.isNotEmpty() == true){
            initWall()
            rootView?.visibility = View.VISIBLE
            headTitle?.text = data?.headerTitle
            headSubTitle?.text = data?.desc
            if(data?.items?.size == 1){
                Logger.d("qmc_test12", "updateUi 房间只有一个")
                adapter.rooms = null
                adapter.homeLuckDrawWall = null
                expandIv?.visibility = View.GONE
                joinRoomTv?.visibility = View.VISIBLE
                adapter.notifyDataSetChanged()
                rootView?.alpha = 1.0f
                rootView?.translationY = 0f
                getLuckDrawWallCallback()?.onHomeLuckDrawWallHeightChange()

                // 首页_直播-红包底栏  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(67208)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "liveAudio")
                    .put(XmLiveRequestIdHelper.IS_DUPLICATE_VIEW, "2")
                    .put("dialogType", "单直播")
                    .createTrace()
                return
            }
            expandIv?.visibility = View.VISIBLE
            joinRoomTv?.visibility = View.GONE

            val lastExpand = isExpand || adapter.itemCount >0

            adapter.rooms = data?.items
            adapter.homeLuckDrawWall = data
            adapter.notifyDataSetChanged()
            rootView?.alpha = 1.0f
            roomRecyclerView?.alpha = 1.0f
            roomRecyclerView?.visibility = View.VISIBLE
            Logger.d("qmc_test12", "updateUi 有多个房间 lastExpand $lastExpand requestUnfold $requestUnfold")
            if(requestUnfold || !lastExpand){
                postUnfold(false)
            }else{
                getLuckDrawWallCallback()?.onHomeLuckDrawWallHeightChange()
            }
            // 首页_直播-红包底栏  控件曝光
            XMTraceApi.Trace()
                .setMetaId(67208)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "liveAudio")
                .put(XmLiveRequestIdHelper.IS_DUPLICATE_VIEW, "2")
                .put("dialogType", "多直播")
                .createTrace()
        }else{
            initWall()
            adapter.rooms = data?.items
            adapter.homeLuckDrawWall = data
            adapter.notifyDataSetChanged()
            rootView?.visibility = GONE
            getLuckDrawWallCallback()?.onHomeLuckDrawWallHeightChange()
        }
    }

    private fun postUnfold(animation:Boolean=true){
        if(adapter.itemCount <= 0){
            return
        }
        roomRecyclerView?.post {
            contract(animation)
        }
    }

    override fun isWallExpand(): Boolean {
        if(rootView == null){
            return false
        }
        return adapter.itemCount > 1 && rootView?.translationY!! <= 0
    }

    override fun hasData():Boolean {
        return data?.items?.isNotEmpty() == true
    }

    override fun getLuckDrawWallHeight(): Int {
        if(data?.items?.isNotEmpty() == true ){
            return rootView?.measuredHeight ?: 0
        }
        return 0
    }

    override fun getLuckDrawWallHeadHeight(): Int {
        return headLayout?.measuredHeight ?: 0
    }


    private fun handExpandClick(){
        if(isAnimation){
            return
        }
        if(isExpand){
            contract()
            // 首页_直播-红包底栏  点击事件
            XMTraceApi.Trace()
                .click(67207) // 用户点击时上报
                .put("currPage", "liveAudio")
                .put(XmLiveRequestIdHelper.createTracePropsForClick(this.data?.xmRequestId))
                .put("dialogType", "多直播")
                .put("Item", "收起")
                .createTrace()
        }else{
            expand()
            // 首页_直播-红包底栏  点击事件
            XMTraceApi.Trace()
                .click(67207) // 用户点击时上报
                .put("currPage", "liveAudio")
                .put(XmLiveRequestIdHelper.createTracePropsForClick(this.data?.xmRequestId))
                .put("dialogType", "多直播")
                .put("Item", "展开")
                .createTrace()
        }
    }

    private fun expand(animation:Boolean=true) {
        if(adapter.itemCount <=0){
            return
        }
        if(rootView?.translationY!! <=0){
            getLuckDrawWallCallback()?.onHomeLuckDrawWallHeightChange()
            return
        }
        if(isAnimation){
            return
        }
        Logger.d("qmc_test12", "expand")
        isAnimation = true
        roomRecyclerView?.let {
            if(!animation){
                rootView?.translationY = 0f
                isExpand = true
                isAnimation = false
                expandIv?.rotation = 0f;
                getLuckDrawWallCallback()?.onHomeLuckDrawWallHeightChange()
                return
            }
            val rotation1 = ObjectAnimator.ofFloat(
                expandIv,
                AnimationUtil.ANMATOR_PROPERTY_ROTATION,
                180f,
                0f,
            )
            rotation1.duration = 200
            rotation1.start()
            AnimationUtil.buildTranslationYObjectAnimator(
                rootView,
                it.measuredHeight * 1.0f,0f
            ).setDuration(200).start()
            val animator: ObjectAnimator =
                AnimationUtil.buildAlphaObjectAnimator(roomRecyclerView, 0.0f, 1.0f).setDuration(200)
            animator.addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator?) {

                }

                override fun onAnimationEnd(animation: Animator?) {
                    isExpand = true
                    isAnimation = false
                    expandIv?.rotation = 0f;
                    getLuckDrawWallCallback()?.onHomeLuckDrawWallHeightChange()
                }

                override fun onAnimationCancel(animation: Animator?) {
                }

                override fun onAnimationRepeat(animation: Animator?) {
                }

            })
            animator.start()
        }
    }

    override fun contract(animation:Boolean) {
        if(adapter.itemCount <=0){
            return
        }
        if(rootView == null || rootView?.translationY!! > 0){
            getLuckDrawWallCallback()?.onHomeLuckDrawWallHeightChange()
            return
        }
        if(isAnimation){
            return
        }
        Logger.d("qmc_test12", "unfold")
        isAnimation = true
        roomRecyclerView?.let {
            if(!animation){
                rootView?.translationY =  it.measuredHeight * 1.0f
                isAnimation = false
                isExpand = false
                rootView?.alpha = 1.0f
                expandIv?.rotation = 180f;
                getLuckDrawWallCallback()?.onHomeLuckDrawWallHeightChange()
                return
            }

            AnimationUtil.buildTranslationYObjectAnimator(
                rootView,
                0f,
                it.measuredHeight * 1.0f
            ).setDuration(200).start()

            val rotation1 = ObjectAnimator.ofFloat(
                expandIv,
                AnimationUtil.ANMATOR_PROPERTY_ROTATION,
                0.0f,
                180f,
            )
            rotation1.duration = 200
            rotation1.start()

            val animator: ObjectAnimator =
                AnimationUtil.buildAlphaObjectAnimator(roomRecyclerView, 1.0f, 0.0f).setDuration(200)
            animator.addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator?) {

                }

                override fun onAnimationEnd(animation: Animator?) {
                    isAnimation = false
                    isExpand = false
                    rootView?.alpha = 1.0f
                    expandIv?.rotation = 180f;
                    getLuckDrawWallCallback()?.onHomeLuckDrawWallHeightChange()
                }

                override fun onAnimationCancel(animation: Animator?) {
                }

                override fun onAnimationRepeat(animation: Animator?) {
                }

            })
            animator.start()
        }
    }

    fun getLuckDrawWallCallback():IHomeLuckDrawWallCallback?{
        return fragment as? IHomeLuckDrawWallCallback
    }
}