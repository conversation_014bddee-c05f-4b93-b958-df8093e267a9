package com.ximalaya.ting.android.live.home.components.attention

import com.ximalaya.ting.android.live.host.adapter.ICommonLiveDataCallback

/**
 * 新版直播频道页：关注主播组件。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18321019958
 * @since 2025/2/6
 */
interface IHomeAttentionComponent {
    fun setIsDisable(disable: Boolean)
    fun changeStyle(black: <PERSON><PERSON>an)
    fun loadAttentionData(callback: Runnable? = null)
    fun hasAttentionData(): Boolean
    fun isLoadingData(): Boolean
    fun getAttentionCardHeight(callback: ICommonLiveDataCallback<Int>)
    fun clearData()
    fun goneComponent()
    fun showComponent()
}