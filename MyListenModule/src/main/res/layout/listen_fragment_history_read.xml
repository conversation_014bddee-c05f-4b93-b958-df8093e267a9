<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/host_color_white_darkPageColor">

    <FrameLayout
        android:id="@+id/listen_container_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/listen_tv_select_all"
        style="@style/host_style_text_n1"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/host_default_side_margin"
        android:contentDescription="全选，未选中按钮"
        android:drawableStart="@drawable/listen_ic_checkbox"
        android:drawablePadding="12dp"
        android:gravity="center_vertical"
        android:text="全选"
        android:textColor="@color/host_color_textColor"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/listen_container_layout"
        tools:visibility="visible" />

    <com.handmark.pulltorefresh.library.PullToRefreshRecyclerView
        android:id="@+id/listen_rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_goneMarginBottom="@dimen/host_bottom_bar_height"
        app:layout_constraintBottom_toTopOf="@+id/listen_rl_bottom_control_layout"
        app:layout_constraintTop_toBottomOf="@+id/listen_tv_select_all" />

    <FrameLayout
        android:id="@+id/listen_rl_bottom_control_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/host_color_ffffff_2a2a2a"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/listen_tv_cancel_select"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="取消"
                android:textColor="@color/host_delete_text_color_selector"
                android:textSize="14sp" />

            <View
                android:layout_width="1dp"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:background="@color/host_color_eeeeee_353535" />

            <TextView
                android:id="@+id/listen_batch_delete_track"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="删除"
                android:enabled="false"
                android:textColor="@color/host_download_delete_text_color_selector"
                android:textSize="14sp" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_gravity="top"
            android:background="@color/host_color_dddddd_353535" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>