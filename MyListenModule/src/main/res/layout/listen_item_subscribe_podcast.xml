<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:descendantFocusability="blocksDescendants"
    android:paddingStart="16dp"
    android:paddingEnd="11dp"
    android:paddingBottom="10dp">

    <!--更多按钮-->
    <ImageView
        android:id="@+id/listen_iv_select"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:contentDescription="选中"
        android:layout_marginTop="10dp"
        android:src="@drawable/host_checkbox_download"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <!--封面-->
    <com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
        android:id="@+id/listen_cover_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        app:layout_goneMarginStart="0dp"
        android:layout_marginTop="10dp"
        app:albumCoverSize="60dp"
        app:layout_constraintStart_toEndOf="@+id/listen_iv_select"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/listen_red_point_guide"
        app:layout_constraintStart_toStartOf="@+id/listen_cover_group"
        app:layout_constraintTop_toTopOf="@+id/listen_cover_group"
        android:layout_marginStart="60dp"
        android:layout_width="1px"
        android:layout_height="1px" />

    <TextView
        android:id="@+id/listen_iv_red_point"
        android:visibility="gone"
        tools:visibility="visible"
        tools:text="99+"
        android:textSize="9sp"
        android:paddingEnd="5dp"
        android:paddingStart="5dp"
        android:gravity="center"
        android:textColor="@color/host_color_ffffff"
        android:background="@drawable/listen_bg_small_red_point"
        app:layout_constraintStart_toStartOf="@+id/listen_red_point_guide"
        app:layout_constraintEnd_toEndOf="@+id/listen_red_point_guide"
        app:layout_constraintTop_toTopOf="@+id/listen_red_point_guide"
        app:layout_constraintBottom_toBottomOf="@+id/listen_red_point_guide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <!--主标题-->
    <TextView
        android:id="@+id/listen_tv_album_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="20dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/host_color_2c2c3c_e6ffffff"
        android:textSize="14sp"
        android:textStyle="bold"
        android:fontFamily="sans-serif-light"
        app:layout_constraintBottom_toTopOf="@+id/listen_tv_subTitle"
        app:layout_constraintLeft_toRightOf="@id/listen_cover_group"
        app:layout_constraintRight_toLeftOf="@id/listen_iv_more"
        app:layout_constraintTop_toTopOf="@id/listen_cover_group"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="这是一个标题" />

    <!--主标题-->
    <TextView
        android:id="@+id/listen_tv_subTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:layout_marginTop="1dp"
        android:textColor="@color/host_color_662c2c3c_66ffffff"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@+id/listen_layout_tag"
        app:layout_constraintEnd_toEndOf="@+id/listen_tv_album_title"
        app:layout_constraintStart_toStartOf="@+id/listen_tv_album_title"
        app:layout_constraintTop_toBottomOf="@id/listen_tv_album_title"
        tools:text="这是一个标题" />

    <LinearLayout
        android:id="@+id/listen_layout_tag"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="@id/listen_cover_group"
        app:layout_constraintStart_toStartOf="@+id/listen_tv_album_title"
        app:layout_constraintTop_toBottomOf="@id/listen_tv_subTitle"
        app:layout_constraintEnd_toStartOf="@+id/listen_iv_more"
        app:layout_constraintHorizontal_bias="0"
        android:layout_marginTop="6dp"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/listen_iv_set_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:src="@drawable/listen_ic_subscribe_set_top"
            android:visibility="gone"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/listen_iv_label"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:src="@drawable/listen_ic_podcast_dujia_label"
            android:visibility="gone"
            android:scaleType="fitXY"
            android:layout_marginEnd="8dp"
            app:layout_constraintBottom_toBottomOf="@+id/listen_tv_update"
            app:layout_constraintStart_toEndOf="@+id/listen_iv_set_top"
            app:layout_constraintTop_toTopOf="@+id/listen_tv_update"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/listen_iv_reserve"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:layout_marginEnd="8dp"
            android:scaleType="fitXY"
            android:src="@drawable/listen_ic_subscribe_label_reserve"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/listen_tv_update"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="@color/host_color_4d2c2c3c_4dffffff"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/listen_cover_group"
            app:layout_constraintStart_toEndOf="@+id/listen_iv_label"
            app:layout_constraintTop_toBottomOf="@id/listen_tv_subTitle"
            tools:text="28天前更新" />

    </LinearLayout>

    <!--更多按钮-->
    <ImageView
        android:id="@+id/listen_iv_more"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:padding="5dp"
        android:contentDescription="更多"
        android:src="@drawable/listen_ic_more_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="UseAppTint" />

</androidx.constraintlayout.widget.ConstraintLayout>