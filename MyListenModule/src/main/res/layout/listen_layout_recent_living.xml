<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingTop="4dp"
    android:paddingBottom="10dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/listen_bg_color_14a7a7a7_08ffffff_radius_6"
        android:layout_width="match_parent"
        android:layout_height="38dp">

        <AdapterViewFlipper
            android:id="@+id/listen_live_viewFlipper"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:animateFirstView="false"
            android:flipInterval="4000"
            android:inAnimation="@anim/listen_recent_live_anim_in"
            android:loopViews="true"
            android:outAnimation="@anim/listen_recent_live_anim_out"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/listen_live_right_arrow"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/listen_live_right_arrow"
            android:src="@drawable/listen_ic_live_right_arrow"
            app:layout_constraintTop_toTopOf="parent"
            android:paddingStart="70dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:tint="@color/listen_color_732c2c3c_66ffffff"
            android:layout_marginEnd="10dp"
            android:layout_width="wrap_content"
            android:layout_height="match_parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>