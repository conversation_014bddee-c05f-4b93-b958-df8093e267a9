<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape>
            <solid android:color="@color/host_color_f6f7f8_dcdcdc" />
            <corners android:radius="15dp" />
            <padding android:bottom="3dp" android:left="16dp" android:right="16dp" android:top="3dp" />
        </shape>
    </item>
    <item android:state_checked="true">
        <shape>
            <solid android:color="@color/host_color_f6f7f8_dcdcdc" />
            <corners android:radius="15dp" />
            <padding android:bottom="3dp" android:left="16dp" android:right="16dp" android:top="3dp" />
        </shape>
    </item>
    <item>
        <shape>
            <solid android:color="@color/host_color_ffffff_131313" />
            <corners android:radius="15dp" />
            <padding android:bottom="3dp" android:left="16dp" android:right="16dp" android:top="3dp" />
        </shape>
    </item>
</selector>
