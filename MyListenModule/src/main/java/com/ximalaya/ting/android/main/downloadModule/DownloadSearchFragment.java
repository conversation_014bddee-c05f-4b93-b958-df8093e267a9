package com.ximalaya.ting.android.main.downloadModule;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.downloadservice.DownLoadedAlbum;
import com.ximalaya.ting.android.downloadservice.base.IDownloadService;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.AlbumSubscript;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.main.adapter.album.item.DownloadAlbumAdapterNew;
import com.ximalaya.ting.android.main.downloadModule.adapter.DownloadedTrackAdapterNew;
import com.ximalaya.ting.android.main.util.ListenExposure;
import com.ximalaya.ting.android.mylisten.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

/**
 * Created by zhouli on 2021/12/29.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class DownloadSearchFragment extends BaseFragment2 implements View.OnClickListener, AdapterView.OnItemClickListener {

    private int mType = AppConstants.KEY_SEARCH_DOWNLOAD_ALBUM;
    private List<Album> mAlbumData;
    private List<Track> mTrackData;

    private EditText mSearchEt;
    private TextView mBackTV;
    private ImageView mClearIv;
    private ListView mListView;
    private String mKeyWord;
    private DownloadAlbumAdapterNew mAlbumAdapter;
    private DownloadedTrackAdapterNew mTrackAdapter;
    private boolean isRefresh = false;
    private boolean mIsLoadDataFinish = false;
    private TextView mAlbumTabTv;
    private TextView mTrackTabTv;
    private int mPlaySource = ConstantsOpenSdk.PLAY_FROM_DOWNLOAD_SOUND;
    private boolean mIsFirstLoad = true;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 我的-下载-搜索页  页面展示
        new XMTraceApi.Trace()
                .pageView(40677, "myDownloadSearch")
                .put("currPage", "myDownloadSearch")
                .createTrace();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // 我的-下载-搜索页  页面离开
        new XMTraceApi.Trace()
                .pageExit2(40678)
                .createTrace();
    }

    private TextWatcher mTextWatcher = new TextWatcher(){

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            if (TextUtils.isEmpty(s.toString())) {
                mKeyWord = null;
                mClearIv.setVisibility(View.GONE);
                mAlbumAdapter.clear();
                mTrackAdapter.clear();
                loadData();
            } else {
                mKeyWord = s.toString();
                if (mType == AppConstants.KEY_SEARCH_DOWNLOAD_ALBUM) {
                    loadSearchDataForAlbum(mKeyWord, false);
                } else if (mType == AppConstants.KEY_SEARCH_DOWNLOAD_TRACK) {
                    loadSearchDataForTrack(mKeyWord, false);
                }
            }
        }
    };

    private void setTabItem(int item) {
        mAlbumTabTv.setSelected(item == AppConstants.KEY_SEARCH_DOWNLOAD_ALBUM);
        mTrackTabTv.setSelected(item == AppConstants.KEY_SEARCH_DOWNLOAD_TRACK);
        ViewStatusUtil.setTypeFace(mAlbumTabTv, mTrackTabTv);
    }

    private void loadSearchDataForAlbum(String keyword, Boolean isShowNoContent) {
        if (!TextUtils.isEmpty(keyword)) {
            mClearIv.setVisibility(View.VISIBLE);
            List<Album> searchAlbums = searchAlbumByKeyword(mAlbumData, mKeyWord);
            if (mAlbumAdapter instanceof DownloadAlbumAdapterNew) {
                mAlbumAdapter.clear();
                ((DownloadAlbumAdapterNew)mAlbumAdapter).setListData(searchAlbums);
                mListView.setAdapter(mAlbumAdapter);
                ((DownloadAlbumAdapterNew)mAlbumAdapter).notifyDataSetChanged();
            }
            if (mListView != null) {
                mListView.smoothScrollToPosition(0);
            }
            if (searchAlbums.size() == 0 && isShowNoContent) {
                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
            } else {
                onPageLoadingCompleted(LoadCompleteType.OK);
            }
            if (searchAlbums.size() == 0 && isShowNoContent) {
                hideSoftInput();
            }
            postExposure();
        }
    }

    private List<Album> searchAlbumByKeyword(List<Album> albums, String keyword){
        List<Album> hits = new ArrayList();
        if (albums != null && albums.size() != 0 && !TextUtils.isEmpty(keyword)) {
            for (Album album:albums) {
                if (isHitAlbumTitle(album, keyword) || isHitAlbumTrackTitle(album, keyword)) {
                    if (album instanceof AlbumM && !TextUtils.isEmpty(((AlbumM) album).getTimeTag())) {
                        ((AlbumM) album).setTimeTag("");
                    }
                    hits.add(album);
                }
            }
        }
        return hits;
    }

    private boolean isHitAlbumTitle(Album album, String keyword) {
        return album != null && !TextUtils.isEmpty(album.getAlbumTitle()) && album.getAlbumTitle().toLowerCase(Locale.getDefault()).contains(keyword.toLowerCase(Locale.getDefault()));
    }

    private boolean isHitAlbumTrackTitle(Album album, String keyword) {
        AlbumM albumM = (AlbumM)album;
        String albumTitle = albumM.getAlbumTitle();
        return album != null && !TextUtils.isEmpty(albumTitle) && albumTitle.toLowerCase(Locale.getDefault()).contains(keyword.toLowerCase(Locale.getDefault()));
    }

    private void loadSearchDataForTrack(String keyword, Boolean isShowNoContent) {
        if (!TextUtils.isEmpty(keyword)) {
            mClearIv.setVisibility(View.VISIBLE);
            List<Track> searchEbooks = searchEbookByKeyword(mTrackData, mKeyWord);
            if (mTrackAdapter instanceof DownloadedTrackAdapterNew) {
                mTrackAdapter.clear();
                mTrackAdapter.addListData(searchEbooks);
                mListView.setAdapter(mTrackAdapter);
                mTrackAdapter.notifyDataSetChanged();
            }
            if (mListView != null) {
                mListView.smoothScrollToPosition(0);
            }
            if (searchEbooks.size() == 0 && isShowNoContent) {
                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
            } else {
                onPageLoadingCompleted(LoadCompleteType.OK);
            }
            if (searchEbooks.size() == 0 && isShowNoContent) {
                hideSoftInput();
            }
            postExposure();
        }
    }

    private List<Track> searchEbookByKeyword(List<Track> tracks, String keyword) {
        List<Track> hits = new ArrayList();
        if (tracks != null && tracks.size() != 0 && !TextUtils.isEmpty(keyword)) {
            for (Track track:tracks) {
                if (isHitTrackTitle(track, keyword)) {
                    hits.add(track);
                }
            }
        }
        return hits;
    }

    private boolean isHitTrackTitle( Track track, String keyword) {
        return track != null && !TextUtils.isEmpty(track.getTrackTitle()) && track.getTrackTitle().toLowerCase(Locale.getDefault()).contains(keyword.toLowerCase(Locale.getDefault()));
    }

    private TextView.OnEditorActionListener mEditorActionListener = new TextView.OnEditorActionListener() {
        @Override
        public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                String content = (mSearchEt != null && mSearchEt.getText() != null) ? mSearchEt.getText().toString() : null;
                if (TextUtils.isEmpty(content)) {
                    loadData();
                } else {
                    if (mType == AppConstants.KEY_SEARCH_DOWNLOAD_ALBUM) {
                        loadSearchDataForAlbum(content, false);
                    } else if (mType == AppConstants.KEY_SEARCH_DOWNLOAD_TRACK) {
                        loadSearchDataForTrack(content, false);
                    }
                }
                hideSoftInput();
            }
            return false;
        }
    };


    public DownloadSearchFragment() {
        super(true, null);
    }

    public static DownloadSearchFragment newInstance(int type) {
        Bundle args = new Bundle();
        args.putInt(AppConstants.KEY_SEARCH_TYPE, type);
        DownloadSearchFragment fragment = new DownloadSearchFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected String getPageLogicName() {
        return "DownloadSearchFragment";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {

        if (getArguments() != null) {
            mType = getArguments().getInt(AppConstants.KEY_SEARCH_TYPE, AppConstants.KEY_SEARCH_DOWNLOAD_ALBUM);
        }

        mAlbumAdapter = new DownloadAlbumAdapterNew((MainActivity) mActivity, new ArrayList<Album>());
        mAlbumAdapter.setMoreUnVisiable();
        mTrackAdapter = new DownloadedTrackAdapterNew(mActivity, null);
        mTrackAdapter.setMoreUnVisiable();

        mBackTV = findViewById(R.id.listen_download_search_cancel);
        mBackTV.setOnClickListener(this);

        mAlbumTabTv = findViewById(R.id.listen_tab_download_album_tv);
        mTrackTabTv = findViewById(R.id.listen_tab_download_track_tv);
        setTabItem(mType);
        mListView = findViewById(R.id.listen_download_items);
        mListView.setDivider(null);
        mSearchEt = findViewById(R.id.listen_search_et);
        mBackTV = findViewById(R.id.listen_download_search_cancel);
        mClearIv = findViewById(R.id.listen_clear_search_text);
        mClearIv.setOnClickListener(this);

        View view = findViewById(R.id.listen_search_download);

        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            ViewGroup.LayoutParams titleBarLayoutParams = view.getLayoutParams();
            if (null != titleBarLayoutParams) {
                titleBarLayoutParams.height = titleBarLayoutParams.height + BaseUtil.getStatusBarHeight(mContext);
                view.setLayoutParams(titleBarLayoutParams);
                view.setPadding(0, BaseUtil.getStatusBarHeight(mContext), 0, 0);
            }
        }

        mSearchEt.addTextChangedListener(mTextWatcher);
        mSearchEt.setOnEditorActionListener(mEditorActionListener);
        mClearIv.setOnClickListener(this);
        mSearchEt.setOnClickListener(this);
        mAlbumTabTv.setOnClickListener(this);
        mTrackTabTv.setOnClickListener(this);

        if (mType == AppConstants.KEY_SEARCH_DOWNLOAD_ALBUM) {
            mListView.setAdapter(mAlbumAdapter);
        } else {
            mListView.setAdapter(mTrackAdapter);
        }
        mListView.setOnItemClickListener(this);
        mListView.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                if (scrollState == ListView.OnScrollListener.SCROLL_STATE_IDLE) {
                    ListenExposure.exposureViews(mListView);
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {

            }
        });
    }

    private void hideSoftInput() {
        View focusView = mActivity.getCurrentFocus() != null ? mActivity.getCurrentFocus() : mSearchEt;
        if (focusView != null) {
            SystemServiceManager.hideSoftInputFromWindow(mActivity, focusView.getWindowToken(), 0);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            onRealResume();
        }
    }

    private void onRealResume() {
        if (!mIsFirstLoad) {
            postExposure();
        }
        if (mTrackAdapter != null) {
            mTrackAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        onRealResume();
    }

    private void postExposure() {
        HandlerManager.removeCallbacks(runnable);
        HandlerManager.postOnUIThreadDelay(runnable, 100);
    }

    private Runnable runnable = new Runnable() {
        @Override
        public void run() {
            ListenExposure.exposureViews(mListView);
        }
    };

    @Override
    protected void loadData() {
        if (mType == AppConstants.KEY_SEARCH_DOWNLOAD_ALBUM) {
            if (isRefresh) {
                return;
            }
            mIsLoadDataFinish = false;
            if (canUpdateUi() && mAlbumAdapter != null && mAlbumAdapter.getCount() == 0) {
                onPageLoadingCompleted(LoadCompleteType.LOADING);
            }

            isRefresh = true;
            new LoadTask(this).myexec();
        } else {
            if (isRefresh) {
                return;
            }
            mIsLoadDataFinish = false;
            isRefresh = true;
            if (canUpdateUi() && mTrackAdapter != null && mTrackAdapter.getCount() == 0) {
                onPageLoadingCompleted(LoadCompleteType.LOADING);
            }

            loadDataFromDB();
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        if (mType == AppConstants.KEY_SEARCH_DOWNLOAD_TRACK){
            int index = position
                    - mListView.getHeaderViewsCount();
            if (mTrackAdapter == null || index < 0 || mTrackAdapter.getCount() <= index)
                return;

            Track track = (Track) mTrackAdapter.getItem(index);

            if (track != null) {
                if (TextUtils.isEmpty(track.getDownloadedSaveFilePath())) {
                    fileDeleted(track);
                    return;
                }
            }
            if (track != null) {
                File file = new File(track.getDownloadedSaveFilePath());
                if (!file.exists()) {
                    fileDeleted(track);
                    return;
                }
            }

            for (Track track2 : mTrackAdapter.getListData()) {
                track2.setPlaySource(mPlaySource);
            }
            if (getActivity() != null) {


                int totalCount = mTrackAdapter.getListData().size();
                if (totalCount > 200) {
                    int startPosition;
                    int endPosition;

                    int indexInNewList;

                    if (index + 100 > totalCount) {
                        endPosition = totalCount;
                    } else {
                        endPosition = index + 100;
                    }

                    if (index - 100 < 0) {
                        startPosition = 0;
                        indexInNewList = index;
                    } else {
                        startPosition = index - 100;
                        indexInNewList = 100;
                    }

                    PlayTools.playListWithoutWifi(getActivity(), mTrackAdapter.getListData().subList(startPosition, endPosition),
                            indexInNewList, true, view);
                } else {
                    PlayTools.playListWithoutWifi(getActivity(), mTrackAdapter.getListData(),
                            index, true, view);
                }
            }
            if (track != null) {
//            new UserTracking().setSrcPage("下载听").setSrcModule("声音")
//                    .setSrcPosition(index).setItem("track").setItemId(track.getDataId())
//                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_VIEW);
                // 我的-下载-声音条  点击事件
                if (track.getAlbum() != null) {
                    // 我的-下载-搜索结果页-声音条  点击事件
                    new XMTraceApi.Trace()
                            .click(40734)
                            .put("currPage", "myDownloadSearch")
                            .put("tabName", "声音")
                            .put("trackId", track.getDataId() + "")
                            .put("position", position + 1 + "")
                            .createTrace();
                }
            }
        } else {
            if (OneClickHelper.getInstance().onClick(view)) {
                int index = position - mListView.getHeaderViewsCount();
                Object o = mAlbumAdapter.getItem(index);
                if (o instanceof AlbumM) {
                    DownLoadedAlbum album = ((AlbumM) o).getDownLoadedAlbum();
                    if (album == null)
                        return;
                    startFragment(DownloadedAlbumDetailFragment
                            .newInstance(album.getAlbum(), album.isPaid(), album.getAnnouncerNiceName()));
//                        new UserTracking().setSrcPage("下载听").setSrcModule("专辑")
//                                .setSrcPosition(index).setItem("album").setItemId(album.getAlbum()
//                                .getAlbumId()).statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_VIEW);
                    // 我的-下载-专辑条  点击事件
                    if (album.getAlbum() != null) {
                        // 我的-下载-搜索页-专辑条  点击事件
                        new XMTraceApi.Trace()
                                .click(40736)
                                .put("currPage", "myDownloadSearch")
                                .put("tabName", "专辑")
                                .put("albumId", album.getAlbum().getAlbumId() + "")
                                .put("position", position + 1 + "")
                                .createTrace();
                    }
                }
            }
        }
    }

    private static class LoadTask extends MyAsyncTask<Void, Void, List<DownLoadedAlbum>> {
        private WeakReference<DownloadSearchFragment> mFragmentRef;

        LoadTask(DownloadSearchFragment fragment) {
            mFragmentRef = new WeakReference<>(fragment);
        }

        @Override
        protected List<DownLoadedAlbum> doInBackground(Void... params) {
            if (mFragmentRef.get() == null) {
                return null;
            }

            return RouteServiceUtil.getDownloadService().getDownLoadedAlbumList();
        }

        @Override
        protected void onPostExecute(final List<DownLoadedAlbum> albums) {
            DownloadSearchFragment fragment = mFragmentRef.get();
            if (fragment == null) {
                return;
            }

            fragment.updateUi(albums);
        }
    }

    private void loadDataFromDB() {
        new MyAsyncTask<Void, Void, List<Track>>() {
            @Override
            protected void onPreExecute() {
            }

            @Override
            protected List<Track> doInBackground(Void... params) {

                List<Track> trackList = RouteServiceUtil.getDownloadService().getSortedDownloadedTrack();
                return trackList;
            }

            protected void onPostExecute(final List<Track> result) {
                isRefresh = false;

                if (!canUpdateUi() || mAlbumAdapter == null)
                    return;

                mTrackData = result;

                setDefaultData(result);
            }
        }.myexec();
    }

    private void setDefaultData(final List<Track> trackList) {
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (RouteServiceUtil.getDownloadService().isHasFinishedInitData()) {
                    onPageLoadingCompleted(LoadCompleteType.OK);
                }
                //ViewStatusUtil.setVisible(trackList == null || trackList.size() <= SearchActionRouter.DEFAULT_SHOW_SEARCH_DOWNLOAD_TRACK_COUNT ? View.GONE : View.VISIBLE, mSearchTv, mDividerLine2);
                if (trackList != null) {
                    if (trackList.size() == 0) {
                        mTrackAdapter.clear();
                        if (RouteServiceUtil.getDownloadService().isHasFinishedInitData()) {
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                        }
                        return;
                    }
                    SharedPreferencesUtil spu = SharedPreferencesUtil
                            .getInstance(getActivity());
                    int orderType = spu.getInt(
                            PreferenceConstantsInHost.TINGMAIN_KEY_DOWNLOAD_ALBUM_SOUNDLIST_ORDER
                                    + 0,
                            AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER);

//                    mTrackAdapter.clear();
//                    mTrackAdapter.getListData().addAll(doGetSortedListByOrder(orderType,
//                            trackList));
                    mTrackAdapter.resetData(trackList);
                    if (mTrackAdapter != null && mTrackAdapter.isEmpty()) {
                        onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                    } else {
                        onPageLoadingCompleted(LoadCompleteType.OK);
                    }
                    mListView.setAdapter(mTrackAdapter);

                    mIsFirstLoad = false;
                    postExposure();

                    //notifyAdapter();
                    mIsLoadDataFinish = true;

                } else {
                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                }
            }
        });
    }

    private void notifyAdapter() {
        if (mTrackAdapter != null) {
            mTrackAdapter.notifyDataSetChanged();

            if (mTrackAdapter != null && mTrackAdapter.isEmpty()) {
//                if (mPlaySource == ConstantsOpenSdk.PLAY_FROM_DOWNLOAD_ALBUM) {
//                    //单个专辑声音列表
//                    finish();
//                } else {
//                    //非单个专辑下载声音列表
//                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
//                }
                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
            } else {
                onPageLoadingCompleted(LoadCompleteType.OK);
            }
        }
    }

    private List<Track> doGetSortedListByOrder(int orderType, List<Track> source) {
        List<Track> result;
        if (orderType == AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER) {
            result = source;
        } else {
            result = new ArrayList<>(source);
            Collections.reverse(result);
        }
        return result;
    }

    private void updateUi(final List<DownLoadedAlbum> albums) {
        isRefresh = false;
        if (!canUpdateUi()) {
            return;
        }
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (RouteServiceUtil.getDownloadService().isHasFinishedInitData()) {
                    onPageLoadingCompleted(LoadCompleteType.OK);
                }
                List<Album> albumList = new ArrayList<>();
                for (DownLoadedAlbum album :
                        albums) {
                    if (album.getAlbum() == null)
                        continue;
                    AlbumM copy = new AlbumM();
                    copy.setId(album.getAlbum().getAlbumId());
                    copy.setIncludeTrackCount(album.getDownloadTrackCount());
                    copy.setAlbumTitle(album.getAlbum().getAlbumTitle());
                    copy.setCoverUrlMiddle(album.getAlbum().getValidCover());
                    copy.setDownLoadedAlbum(album);
                    copy.setIsPaid(album.isPaid());
                    copy.setVipFreeType(album.getVipFreeType());
                    copy.setVipFree(album.isVipFree());
                    copy.setAlbumSubscript(new AlbumSubscript(album.getAlbum().getAlbumSubscript()));
                    albumList.add(copy);
                }

                if (mAlbumAdapter != null) {
                    if (albumList.size() > 0) {
                        mAlbumAdapter.clear();
                        mAlbumAdapter.addListData(albumList);
                        mListView.setAdapter(mAlbumAdapter);
                        //mAlbumAdapter.notifyDataSetChanged();
                        onPageLoadingCompleted(LoadCompleteType.OK);

                    } else {
                        if (RouteServiceUtil.getDownloadService().isHasFinishedInitData()) {
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                        }
                        mAlbumAdapter.clear();
                    }
                }

                mIsFirstLoad = false;
                postExposure();

                mAlbumData = albumList;

//                if (mListView != null) {
//                    mListView.onRefreshComplete(false);
//                    mListView.setHasMoreNoFooterView(false);
//                }
                mIsLoadDataFinish = true;
            }
        });

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.listen_fra_download_search;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.listen_search_et) {
            mSearchEt.setFocusable(true);
            mSearchEt.setFocusableInTouchMode(true);
            mSearchEt.requestFocus();
            mSearchEt.requestFocusFromTouch();
            SystemServiceManager.adjustSoftInput(mSearchEt, true);
        } else if (id == R.id.listen_tab_download_album_tv){
            mType = AppConstants.KEY_SEARCH_DOWNLOAD_ALBUM;
            setTabItem(mType);
            loadData();
        }else if (id == R.id.listen_tab_download_track_tv){
            mType = AppConstants.KEY_SEARCH_DOWNLOAD_TRACK;
            setTabItem(mType);
            loadData();
        } else if (id == R.id.listen_download_search_cancel){
            finishFragment();
        } else if (id == R.id.listen_clear_search_text) {
            if (mSearchEt != null) {
                mSearchEt.setText("");
            }
        }
    }


    private void fileDeleted(final Track track) {

        if (track == null)
            return;

        XDCSCollectUtil.statErrorToXDCS("download","download search "+track.toString());

        new DialogBuilder(mActivity).setMessage("声音已被其它清理软件误删，要避免该问题，请勿清除喜马拉雅应用数据")
                .setOkBtn("重新下载").setOkBtn(new DialogBuilder.DialogCallback() {

            @Override
            public void onExecute() {
                if (NetworkType.getNetWorkType(mActivity) == NetworkType.NetWorkType.NETWORKTYPE_INVALID) {
                    CustomToast.showFailToast("没有网络");
                    return;
                }
                if (RouteServiceUtil.getDownloadService().checkNeedLogin()) {
                    return;
                }
                if (mTrackAdapter != null) {

                    if (TextUtils.isEmpty(track.getDownloadUrl())) {
                        HashMap<String, String> params = new HashMap<>();
                        params.put("trackId", track.getDataId() + "");
                        params.put("startTime", "" + System.currentTimeMillis());
                        params.put("sendDataTime", "" + System.currentTimeMillis());
                        long downloadedSize = track.getDownloadedSize();
                        long downloadSize = track.getDownloadSize();
                        long percent = 0;
                        if (downloadSize != 0) {
                            percent = (downloadedSize * 100) / downloadSize;
                        }

                        params.put("downloadPercent", percent + "");
                        CommonRequestM.getInstanse().getDownloadTrackInfo(params,
                                new IDataCallBack<Track>() {

                                    @Override
                                    public void onSuccess(Track object) {
                                        if (object != null) {
                                            object.setPlayCount(track.getPlayCount());
                                            object.setFavoriteCount(track
                                                    .getFavoriteCount());
                                            object.setCommentCount(track
                                                    .getCommentCount());

                                            object.setCoverUrlLarge(track.getCoverUrlLarge());
                                            object.setCoverUrlMiddle(track.getCoverUrlMiddle());
                                            object.setCoverUrlSmall(track.getCoverUrlSmall());

                                            if (!object.isPaid() && TextUtils.isEmpty(object.getDownloadUrl())) {
                                                XDCSCollectUtil.statErrorToXDCS("download", "resource=DownloadedTrackListFragment1" + ";track={" + object.toString() + "}");
                                            }
                                            if (RouteServiceUtil.getDownloadService().checkNeedLogin()) {
                                                return;
                                            }
                                            boolean isAddTaskList = !object.isPayTrack() || object.isAuthorized();
                                            if (isAddTaskList) {
                                                RouteServiceUtil.getDownloadService().addTask(object, new IDownloadService.IAddTaskCallBack() {
                                                    @Override
                                                    public void onAddTaskSuccess() {
                                                        mTrackAdapter.getListData().remove(track);
                                                        mTrackAdapter.notifyDataSetChanged();
                                                        CustomToast.showSuccessToast("重新加入下载列表");
                                                    }

                                                    @Override
                                                    public void onAddTaskFail() {
                                                    }
                                                });
                                            } else {
                                                CustomToast.showFailToast("重新下载失败");
                                            }
                                        }
                                    }

                                    @Override
                                    public void onError(int code, String message) {
                                        CustomToast.showFailToast("重新下载失败");
                                    }

                                });
                    } else {
                        if (!track.isPaid() && TextUtils.isEmpty(track.getDownloadUrl())) {
                            XDCSCollectUtil.statErrorToXDCS("download", "resource=DownloadedTrackListFragment2" + ";track={" + track.toString() + "}");
                        }
                        if (RouteServiceUtil.getDownloadService().checkNeedLogin()) {
                            return;
                        }
                        RouteServiceUtil.getDownloadService().addTask(track, new IDownloadService.IAddTaskCallBack() {
                            @Override
                            public void onAddTaskSuccess() {
                                mTrackAdapter.getListData().remove(track);
                                mTrackAdapter.notifyDataSetChanged();
                            }

                            @Override
                            public void onAddTaskFail() {
                            }
                        });
                    }
                }
            }
        }).setCancelBtn("删除").setCancelBtn(new DialogBuilder.DialogCallback() {

            @Override
            public void onExecute() {
                if (mTrackAdapter != null) {
                    mTrackAdapter.getListData().remove(track);
                    mTrackAdapter.notifyDataSetChanged();
                    RouteServiceUtil.getDownloadService().deleteDownloadedTasks(track);
                }
            }
        }).setCancelable(false).setLayoutWidth().showConfirm();
    }
}
