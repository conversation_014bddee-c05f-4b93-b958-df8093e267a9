<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/vip_dialog_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/vip_dialog_normal_title_area"
        android:layout_width="match_parent"
        android:paddingTop="8dp"
        android:layout_height="52dp">

        <TextView
            android:id="@+id/vip_purchase_dialog_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:text="开通大师课会员，尊享高品质课程"
            android:textColor="@color/host_color_333333_ffffff"
            android:textSize="16sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/vip_purchase_dialog_close"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_alignParentRight="true"
            android:layout_marginRight="15dp"
            android:contentDescription="关闭"
            android:padding="6dp"
            android:scaleType="fitXY"
            android:src="@drawable/vip_ic_close_gray" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/vip_fra_purchase_dialog_real_content_area"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/vip_purchase_dialog_button_area"
        android:layout_below="@+id/vip_dialog_normal_title_area"
        android:layout_weight="1">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <include layout="@layout/vip_master_purchase_dialog_content" />

        </ScrollView>


    </RelativeLayout>

    <FrameLayout
        android:id="@+id/vip_purchase_dialog_button_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/host_color_ffffff_1e1e1e">

        <ViewStub
            android:id="@+id/vip_default_master_purchase_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout="@layout/vip_master_purchase_button" />

        <ViewStub
            android:id="@+id/vip_anniversary_master_purchase_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout="@layout/vip_master_aniversary_purchase_button" />
    </FrameLayout>


</RelativeLayout>