<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/vip_tv_album_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="6dp"
        android:background="@drawable/vip_bg_rect_082c2c3c_08ffffff_r8"
        android:includeFontPadding="false"
        android:paddingHorizontal="6dp"
        android:paddingVertical="4dp"
        android:textColor="@color/host_color_cc2c2c3c_ccffffff"
        android:textSize="11dp"
        tools:text="AlbumTag" />
</FrameLayout>

