package com.ximalaya.ting.android.main.util;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.IntRange;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.google.gson.Gson;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.search.SearchHotWord;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.StableProvider;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

import org.json.JSONArray;
import org.json.JSONException;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ListIterator;
import java.util.Locale;
import java.util.Map;
import java.util.RandomAccess;

/**
 * Created by ervin.li on 2018/10/30.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class SearchUtils {


    /**
     * 直达类型（1：专辑 2：声音 3：主播 4：活动 5：连接 6：直播间）
     *
     * @param fragment      fragment用于获取Context和跳转
     * @param v             点击的view
     * @param searchHotWord 热词
     * @return 能够是否处理
     */
    public static boolean onSearchHotWordClicked(BaseFragment2 fragment, View v, final SearchHotWord searchHotWord) {
        if (searchHotWord == null || fragment == null || !fragment.canUpdateUi()) {
            return false;
        }
        if (!TextUtils.isEmpty(searchHotWord.getUrl())) {
            fragment.startFragment(NativeHybridFragment.newInstance(searchHotWord.getUrl(), true));
            return true;
        }
        switch (searchHotWord.getThroughType()) {
            case 1:
                AlbumEventManage.startMatchAlbumFragment(searchHotWord.getTgId(), AlbumEventManage.FROM_SEARCH,
                        ConstantsOpenSdk.PLAY_FROM_SEARCH, null, null, -1, fragment.getActivity());
                return true;
            case 2:
                PlayTools.playTrackByCommonList(fragment.getContext(), searchHotWord.getTgId(), ConstantsOpenSdk.PLAY_FROM_SEARCH, v);
                return true;
            case 3:
                fragment.startFragment(StableProvider.getInstance().newAnchorSpaceFragment(searchHotWord.getTgId()));
                return true;
            case 4:
                Bundle bundle = new Bundle();
                String url = UrlConstants.getInstanse().getActivity() + "\\"
                        + searchHotWord.getTgId();
                bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
                bundle.putInt(NativeHybridFragment.WEB_VIEW_TYPE, NativeHybridFragment.PAGE_ACTIVITY);
                fragment.startFragment(NativeHybridFragment.class, bundle);
                return true;
            case 5:
                fragment.startFragment(NativeHybridFragment.newInstance(searchHotWord.getUrl(), true));
                return true;
            case 6:
                StableProvider.getInstance().handleIting(fragment.getActivity(), Uri.parse(searchHotWord.getUrl()));
                return true;
            default:
                return false;
        }
    }


    public static <T> T cast(Object object, Class<?> tClass) {
        if (object != null && tClass.isInstance(object)) {
            return (T) object;
        }
        return null;
    }

    public static <T> T cast(Object object, Class<?> tClass, T defaultValue) {
        if (object != null && tClass.isInstance(object)) {
            return (T) object;
        }
        return defaultValue;
    }


    public static <T> void copy(List<? super T> dest, List<? extends T> src) {
        int srcSize = src.size();
        if (srcSize < 10 ||
                (src instanceof RandomAccess && dest instanceof RandomAccess)) {
            for (int i = 0; i < srcSize; i++)
                dest.set(i, src.get(i));
        } else {
            ListIterator<? super T> di = dest.listIterator();
            ListIterator<? extends T> si = src.listIterator();
            for (int i = 0; i < srcSize; i++) {
                di.next();
                di.set(si.next());
            }
        }
    }

    public static boolean isThisCore(String coreSrc, String coreDest) {
        return TextUtils.isEmpty(coreSrc) && TextUtils.equals(coreDest, coreSrc);
    }


    /**
     * 修改Fragment 加载Loading图等位置，由默认的局中改成期望的位置，一般是外层包了StickLayout时，Fragment显示不全
     * 1。loading图较小，需要距顶100dp
     * 2。noContent,NetworkError图较大，居顶即可
     **/
    public static void changeLoadStateViewLocation(View parent, ViewGroup.LayoutParams lp, BaseFragment.LoadCompleteType type, int loadingMargin, int networkErrorMargin, int noContentMargin) {
        if (lp != null && parent != null) {
            Context context = parent.getContext();
            if (lp instanceof ViewGroup.MarginLayoutParams) {
                if (type == BaseFragment.LoadCompleteType.LOADING) {
                    ((ViewGroup.MarginLayoutParams) lp).bottomMargin = BaseUtil.dp2px(context, loadingMargin);
                } else if (type == BaseFragment.LoadCompleteType.NETWOEKERROR) {
                    ((ViewGroup.MarginLayoutParams) lp).bottomMargin = BaseUtil.dp2px(context, networkErrorMargin);
                } else if (type == BaseFragment.LoadCompleteType.NOCONTENT) {
                    ((ViewGroup.MarginLayoutParams) lp).bottomMargin = BaseUtil.dp2px(context, noContentMargin);
                }
            }
        }
    }

    public static <T> T parse(String json, Class<? extends T> tClass) {
        if (TextUtils.isEmpty(json)) return null;
        try {
            return new Gson().fromJson(json, tClass);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static <T> List<T> parseList(String jsonArray, Class<? extends T> tClass) {
        if (TextUtils.isEmpty(jsonArray)) return null;
        try {
            JSONArray array = new JSONArray(jsonArray);
            List<T> list = new ArrayList<>();
            for (int index = 0; index < array.length(); index++) {
                T t = parse(array.optString(index), tClass);
                if (t != null) {
                    list.add(t);
                }
            }
            return list;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static <T> List<T> parseList(String jsonArray, IParse<T> parse) {
        if (TextUtils.isEmpty(jsonArray) || parse == null) return null;
        try {
            JSONArray array = new JSONArray(jsonArray);
            List<T> list = new ArrayList<>();
            for (int index = 0; index < array.length(); ++index) {
                T t = parse.parse(array.optString(index));
                if (t != null) {
                    list.add(t);
                }
            }
            return list;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static int getSize(List list) {
        return list == null ? 0 : list.size();
    }

    public static <T> T getItem(List<T> items, int position) {
        if (items != null && items.size() > position && position > -1) {
            return items.get(position);
        }
        return null;
    }

    public static RecyclerView.ItemDecoration createItemDecoration(final int leftInit, final int left, final int right, final int top, final int bottom) {
        return new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                if (parent == null) return;
                Context context = parent.getContext();
                int initLeft = BaseUtil.dp2px(context, leftInit);
                int l = BaseUtil.dp2px(context, left);
                int r = BaseUtil.dp2px(context, right);
                int t = BaseUtil.dp2px(context, top);
                int b = BaseUtil.dp2px(context, bottom);
                int position = parent.getChildAdapterPosition(view);
                outRect.left = position == 0 ? initLeft : l;
                outRect.right = r;
                outRect.top = t;
                outRect.bottom = b;
            }

        };
    }


    public static void setImageRes(ImageView imageView, int resId) {
        if(imageView!=null){
            imageView.setImageResource(resId);
        }
    }


    public static void setDrawable(TextView textView, @IntRange(from = 0, to = 3) int position, int resId) {
        if (textView != null && resId != -1) {
            Drawable[] drawables = new Drawable[4];
            Drawable drawable = textView.getResources().getDrawable(resId);
            drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
            drawables[position] = drawable;
            textView.setCompoundDrawables(drawables[0], drawables[1], drawables[2], drawables[3]);
        }
    }

    public static void setDrawable(TextView textView, @IntRange(from = 0, to = 3) int position, Drawable drawable) {
        if (textView != null) {
            Drawable[] drawables = textView.getCompoundDrawables();
            if (drawable != null) {
                drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
            }
            drawables[position] = drawable;
            textView.setCompoundDrawables(drawables[0], drawables[1], drawables[2], drawables[3]);
        }
    }

    public static void setTag(View view, Object tag) {
        if (view != null && tag != null) {
            view.setTag(tag);
        }
    }

    public static void setTag(View view, int id, Object tag) {
        if (view != null && tag != null) {
            view.setTag(id, tag);
        }
    }


    public static <T> T getTag(View view, int res, Class<? extends T> cls) {
        if (view != null && cls != null) {
            Object tag = view.getTag(res);
            if (tag != null && cls.isInstance(tag)) {
                return (T) tag;
            }
        }
        return null;
    }

    public static void setTextColor(TextView textView, int color) {
        if (textView != null) {
            textView.setTextColor(color);
        }
    }

    public static <T> T getSafe(List list, int index, Class<?> cls) {
        Object o = getItem(list, index);
        return cls.isInstance(o)? (T) o :null;
    }

    public interface IParse<T> {
        T parse(String string);
    }


    /*
     * 将时间转换为时间戳
     */
    public static long dateToStamp(String s) throws ParseException {
        if (TextUtils.isEmpty(s)) return 0;
        String[] patterns = {"yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM-dd HH", "yyyy-MM-dd"};
        String pattern = null;
        for (int index = 0; index < patterns.length; ++index) {
            if (patterns[index].length() == s.length()) {
                pattern = patterns[index];
                break;
            }
        }
        if (TextUtils.isEmpty(pattern)) return 0;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern, Locale.CHINA);
        Date date = simpleDateFormat.parse(s);
        return date != null ? date.getTime() : 0;
    }

    public static boolean isLiveSearchHotWord(SearchHotWord searchHotWord) {
        return searchHotWord != null && searchHotWord.getDisplayType() == 3;
    }


    @SafeVarargs
    public static void tracePageClick(String srcPage, String srcModule, String item, String itemId, long id, Map.Entry<String, String>... otherParams) {
        UserTracking tracking = new UserTracking().setSrcModule(srcModule).setItem(item).setItemId(itemId).setSrcPage(srcPage).setId(id);
        if (otherParams != null) {
            for (Map.Entry<String, String> p : otherParams) {
                tracking.putParam(p.getKey(), p.getValue());
            }
        }
        tracking.statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
    }

}
