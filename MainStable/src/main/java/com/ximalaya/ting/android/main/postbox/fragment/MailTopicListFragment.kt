package com.ximalaya.ting.android.main.postbox.fragment

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.handmark.pulltorefresh.library.PullToRefreshBase
import com.handmark.pulltorefresh.library.PullToRefreshRecyclerView
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.model.postbox.MailBoxInfo
import com.ximalaya.ting.android.host.model.postbox.MailTopicList
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.view.TitleBar
import com.ximalaya.ting.android.host.view.CustomPullToRefreshRecyclerView
import com.ximalaya.ting.android.main.adapter.recyclerview.BaseRecyclerAdapter
import com.ximalaya.ting.android.main.postbox.adapter.MailTopicListAdapter
import com.ximalaya.ting.android.main.request.MainStableUrlConstants
import com.ximalaya.ting.android.main.stable.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack

class MailTopicListFragment(val type: Long, val needLimit: Boolean = false) :
    BaseFragment2(AppConstants.isPageCanSlide, null) {

    private lateinit var mPullToRefreshRecyclerView: CustomPullToRefreshRecyclerView
    private lateinit var mAdapter: MailTopicListAdapter
    private val mListData = mutableListOf<MailBoxInfo>()
    private var mLoadedPageCount = 0
    private val mLoadPageSize = 20

    override fun getPageLogicName(): String {
        return "话题"
    }

    override fun initUi(savedInstanceState: Bundle?) {
        setTitle("话题")
        mPullToRefreshRecyclerView = findViewById(R.id.main_recycler_view)
        mPullToRefreshRecyclerView.let {
            it.mode = PullToRefreshBase.Mode.DISABLED
            it.setFooterBackgroundColor(
                ContextCompat.getColor(
                    mContext,
                    R.color.main_transparent
                )
            )
            mAdapter = MailTopicListAdapter(this, mListData)
            mAdapter.setOnItemClickListener(object :
                BaseRecyclerAdapter.OnItemClickListener<MailBoxInfo> {
                override fun onItemClick(view: View?, model: MailBoxInfo?, position: Int) {
                    setFinishCallBackData(model)
                    finishFragment()
                }

                override fun onItemLongClick(view: View?, model: MailBoxInfo?, position: Int) {
                }
            })
            it.setAdapter(mAdapter)
            it.setOnRefreshLoadMoreListener(object :
                PullToRefreshRecyclerView.IRefreshLoadMoreListener {
                override fun onRefresh() {
                    requestMailTopicList(1)
                }

                override fun onMore() {
                    requestMailTopicList(mLoadedPageCount + 1)
                }
            })
        }
    }

    override fun loadData() {
        requestMailTopicList(1)
    }

    override fun onMyResume() {
        super.onMyResume()
    }

    override fun onPause() {
        super.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_fra_mail_detail
    }

    override fun isShowPlayButton(): Boolean {
        return false
    }

    override fun darkStatusBar(): Boolean {
        return false
    }

    override fun isPageBgDark(): Boolean {
        return true
    }

    override fun getTitleBarResourceId(): Int {
        return R.id.main_title_bar
    }

    override fun setTitleBar(titleBar: TitleBar) {
        if (titleBar.titleBar != null) {
            titleBar.titleBar.setBackgroundResource(0)
        }

        (titleBar.title as TextView).setTextColor(Color.WHITE)
        (titleBar.back as ImageView).setColorFilter(Color.WHITE)
        titleBar.back.setOnClickListener { finishFragment() }
    }

    private fun requestMailTopicList(pageId: Int) {
        val params = mutableMapOf<String, String>()
        params["type"] = type.toString()
        params["pageId"] = pageId.toString()
        params["pageSize"] = mLoadPageSize.toString()
        val url = MainStableUrlConstants.getInstanse().mailTopicListUrl
        if (pageId == 1) {
            onPageLoadingCompleted(LoadCompleteType.LOADING)
        }
        CommonRequestM.getData(
            url,
            params,
            MailTopicList::class.java,
            object : IDataCallBack<MailTopicList> {
                override fun onSuccess(data: MailTopicList?) {
                    if (!canUpdateUi()) return
                    onPageLoadingCompleted(LoadCompleteType.OK)
                    val resultList = data?.list ?: mutableListOf()
                    if (pageId == 1) {
                        mListData.clear()
                        doAfterAnimation {
                            if (resultList.isNullOrEmpty()) {
                                mLoadedPageCount = 0
                                HandlerManager.postOnUIThread {
                                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT)
                                }
                            } else {
                                mLoadedPageCount = 1
                                if (needLimit && resultList.size >= 10) {
                                    mListData.addAll(resultList.subList(0, 10))
                                    mPullToRefreshRecyclerView.isHasMore = false
                                } else {
                                    mListData.addAll(resultList)
                                    val maxPageId = data?.maxPageId ?: 0
                                    mPullToRefreshRecyclerView.isHasMore = maxPageId > 1
                                }
                                mAdapter.data = mListData
                            }
                        }
                    } else {
                        mLoadedPageCount = pageId
                        if (resultList.isNotEmpty()) {
                            if (needLimit) {
                                if (mAdapter.data.size < 10) {
                                    if (mAdapter.data.size + resultList.size >= 10) {
                                        mPullToRefreshRecyclerView.isHasMore = false
                                        val tempList =
                                            resultList.subList(0, 10 - mAdapter.data.size)
                                        mListData.addAll(tempList)
                                        mAdapter.addData(tempList)
                                    } else {
                                        val maxPageId = data?.maxPageId ?: 0
                                        mPullToRefreshRecyclerView.isHasMore = pageId < maxPageId
                                        mListData.addAll(resultList)
                                        mAdapter.addData(resultList)
                                    }
                                } else {
                                    mPullToRefreshRecyclerView.isHasMore = false
                                }
                            } else {
                                mListData.addAll(resultList)
                                mAdapter.addData(resultList)
                                val maxPageId = data?.maxPageId ?: 0
                                mPullToRefreshRecyclerView.isHasMore = pageId < maxPageId
                            }
                        } else {
                            val maxPageId = data?.maxPageId ?: 0
                            mPullToRefreshRecyclerView.isHasMore = pageId < maxPageId
                        }
                    }
                }

                override fun onError(code: Int, message: String?) {
                    if (!canUpdateUi()) return
                    CustomToast.showFailToast(message)
                    if (pageId == 1) {
                        onPageLoadingCompleted(LoadCompleteType.OK)
                        if (mListData.isEmpty()) {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR)
                        }
                    } else {
                        mPullToRefreshRecyclerView.isHasMore = true
                    }
                }
            })
    }

    override fun getNoContentView(): View {
        return LayoutInflater.from(mContext).inflate(R.layout.main_layout_mailbox_no_content, null).also {
           it.findViewById<TextView>(R.id.main_tv_tips).text = "这里什么都没有，要不咱们看看别的"
        }
    }
}