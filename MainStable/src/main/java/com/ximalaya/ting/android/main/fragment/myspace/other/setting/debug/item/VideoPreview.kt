package com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item

import android.text.TextUtils
import android.view.View
import android.widget.CheckBox
import android.widget.CompoundButton
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.model.base.ListModeBase
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.model.play.VideoBaseInfo
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.DebugType
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.NetworkType
import org.json.JSONObject
import java.util.*

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2023/1/4
 */
class VideoPreview : BaseDebugItem() {

    override fun getName(): String {
        return "刷新首页推荐预览"
    }

    override fun getCategory(): DebugType {
        return DebugType.CATEGORY_VIDEO
    }

    override fun getIconResId(): Int {
        return 0
    }

    override fun showToggle(): Boolean {
        return true
    }

    override fun bindToggle(toggle: CheckBox?) {
        toggle?.isChecked = MMKVUtil.getInstance().getBoolean(KEY_SWITCH, false)
    }

    override fun onCheckedChanged(buttonView: CompoundButton?, isChecked: Boolean) {
        MMKVUtil.getInstance().saveBoolean(KEY_SWITCH, isChecked)
        if (isChecked) {
            loadProgramVideoDataList()
        }
    }

    override fun showArrow(): Boolean {
        return false
    }

    override fun onClick(v: View?) {
        if (enable()) {
            loadProgramVideoDataList()
        }
    }

    private fun saveFirstVideo(trackInfo: PlayingSoundInfo.TrackInfo) {
        val originJson = JSONObject(templates)
        originJson.put("picForCustomColor", trackInfo.validCover)

        originJson.optJSONObject("item")?.apply {
            put("uid", trackInfo.uid)
            put("refId", trackInfo.trackId)
            put("title", trackInfo.title)

            val subElement = optJSONArray("subElements")?.optJSONObject(0)
            subElement?.put("cover", trackInfo.validCover)
            subElement?.put("refId", trackInfo.albumId)
            put("uid", trackInfo.uid)
            put("title", trackInfo.albumTitle)
        }


        val updateStr = originJson.toString()
        save(updateStr)
    }

    private fun saveAsAlbum(trackInfo: PlayingSoundInfo.TrackInfo) {
        val originJson = JSONObject(albumTemplate)
        originJson.put("picForCustomColor", trackInfo.validCover)

        originJson.optJSONObject("item")?.apply {
            put("uid", trackInfo.uid)
            put("title", trackInfo.albumTitle)
            put("albumId", trackInfo.albumId)

            put("coverLarge", trackInfo.coverLarge)
            put("coverMiddle", trackInfo.coverMiddle)
            put("coverSmall", trackInfo.coverSmall)
            put("coverPath", trackInfo.validCover)

            put("nickname", UserInfoMannage.getInstance().user?.nickname)
            put("logo", UserInfoMannage.getInstance().user?.getMobileSmallLogo())


            val reason = optJSONObject("recInfo")
            reason?.put("recReason", trackInfo.title)

        }


        val updateStr = originJson.toString()
        save(updateStr)
    }



    private fun getVideoPlayUrl(trackId: Long) {
        CommonRequestM.getVideoBaseInfoNew(trackId, object : IDataCallBack<VideoBaseInfo> {
            override fun onSuccess(data: VideoBaseInfo?) {
                if (data == null) return

                val videoUrl = data.playInfo?.decodeUrl
            }

            override fun onError(code: Int, message: String) {
                if (!TextUtils.isEmpty(message)) {
                    CustomToast.showFailToast(message)
                } else {
                    CustomToast.showFailToast("获取视频数据异常")
                }
            }
        })
    }

    private fun loadProgramVideoDataList() {
        val params: MutableMap<String, String> = HashMap()
        params[HttpParamsConstants.PARAM_PAGE_ID] = "1"
        params[HttpParamsConstants.PARAM_PAGE_SIZE] = "10"
        params["queryMode"] = "0"
        val trackType = "videos"
        val callBack = object: IDataCallBack<ListModeBase<PlayingSoundInfo.TrackInfo>?> {
            override fun onSuccess(list: ListModeBase<PlayingSoundInfo.TrackInfo>?) {
                val firstVideo = list?.list?.firstOrNull()
                if (firstVideo != null) {
                    try {
                        saveAsAlbum(firstVideo)
                        CustomToast.showFailToast("刷新成功，请回到首页刷新查看")
                    } catch (e: Throwable) {
                        CustomToast.showFailToast("刷新失败")
                    }
                } else {
                    CustomToast.showFailToast("刷新失败")
                }
            }

            override fun onError(code: Int, message: String) {
                CustomToast.showFailToast(if (TextUtils.isEmpty(message)) "获取专辑数据失败" else message)
            }
        }

        CommonRequestM.baseGetRequest<ListModeBase<PlayingSoundInfo.TrackInfo>?>(
            String.format(
                "%s/%s/%d",
                UrlConstants.getInstanse().myTracksInfoNew,
                trackType,
                System.currentTimeMillis()
            ), params, callBack,
            CommonRequestM.IRequestCallBack<ListModeBase<PlayingSoundInfo.TrackInfo>?> { content ->
                if (TextUtils.isEmpty(content)) return@IRequestCallBack null
                val jsonObject = JSONObject(content)
                var records: ListModeBase<PlayingSoundInfo.TrackInfo>? = null
                if (jsonObject.has("data")) {
                    records = ListModeBase<PlayingSoundInfo.TrackInfo>(jsonObject.optJSONObject("data").toString(), PlayingSoundInfo.TrackInfo::class.java, "list")
                }
                records
            })
    }

    companion object {
        private val templates = "{\"displayClass\":\"single\",\"item\":{\"auditStatus\":\"PASS\",\"bizType\":\"IndexFeedVideoNew\",\"contentType\":\"IndexFeedVideoNew\",\"count\":{},\"cover\":\"http://imagev2.test.ximalaya.com/storages/923d-audiotest/8C/91/CAoVXrsEOhCgAAIPmQAAM--_.jpeg\",\"deleteStatus\":\"ACTIVE\",\"digInfo\":{\"albumId\":1279653},\"digStatus\":\"ALL\",\"dislikeReasonNewV1\":[{\"negative\":{\"key\":{\"codeType\":\"DEFAULT\",\"name\":\"不喜欢该内容\",\"setCodeType\":true,\"setName\":true},\"setKey\":true,\"setSubReasons\":false,\"subReasons\":[],\"subReasonsOpt\":{\"defined\":true}},\"reasonRowType\":0},{\"negative\":{\"key\":{\"codeType\":\"REDUCE_SIMILAR\",\"name\":\"减少相似内容\",\"setCodeType\":true,\"setName\":true},\"setKey\":true,\"setSubReasons\":false,\"subReasons\":[],\"subReasonsOpt\":{\"defined\":true}},\"reasonRowType\":1},{\"negative\":{\"key\":{\"codeType\":\"DEFAULT\",\"name\":\"不喜欢主播\",\"setCodeType\":true,\"setName\":true},\"setKey\":true,\"setSubReasons\":false,\"subReasons\":[],\"subReasonsOpt\":{\"defined\":true}},\"reasonRowType\":2},{\"negative\":{\"key\":{\"codeType\":\"DEFAULT\",\"name\":\"反馈问题\",\"setCodeType\":true,\"setName\":true},\"setKey\":true,\"setSubReasons\":true,\"subReasons\":[{\"codeType\":\"DEFAULT\",\"name\":\"涉及色情暴力内容\",\"setCodeType\":true,\"setName\":true},{\"codeType\":\"DEFAULT\",\"name\":\"标题或内容夸张、诱导点击\",\"setCodeType\":true,\"setName\":true},{\"codeType\":\"DEFAULT\",\"name\":\"涉及侵权\",\"setCodeType\":true,\"setName\":true},{\"codeType\":\"DEFAULT\",\"name\":\"内容粗糙不美观\",\"setCodeType\":true,\"setName\":true},{\"codeType\":\"DEFAULT\",\"name\":\"其他\",\"setCodeType\":true,\"setName\":true}],\"subReasonsOpt\":{\"defined\":true}},\"reasonRowType\":3}],\"id\":6144333,\"preview\":\"https://aod.cos.tx.xmcdn.com/storages/5055-audiofreehighqps/C1/D6/GMCoOScHXNefABAAAAHYZ8yk.mp4\",\"recInfo\":{\"recReasonType\":\"RECSYS\",\"recSrc\":\"COMMON_TYPE.BS.P270635.SHELL649.diversify|ContentDegradeV3Fusion|FixInsertByCTFusion.wrapper|pool_ids.F3|\",\"recTrack\":\"128.792_1306-493_697-491_695-731_1162-793_1307.4042\",\"recReason\":\"22理由推荐专属简介似懂非懂沙发是对方是对方，是对方看是对方说了就是逻辑是对方三氯甲烷了人家的饭\"},\"refId\":1704928,\"searchStatus\":\"ALL\",\"source\":\"MainApp\",\"subElements\":[{\"auditStatus\":\"PASS\",\"bizType\":\"Album\",\"contentType\":\"Album/0\",\"count\":{},\"cover\":\"http://imagev2.test.ximalaya.com/storages/e7b5-audiotest/57/08/GKwaDD0GpZzSAAEUzAAAa3t_.jpeg\",\"deleteStatus\":\"ACTIVE\",\"digStatus\":\"ALL\",\"id\":500002,\"interact\":{\"subscribed\":0},\"wrap\":{\"recReason\":\"22理由推荐专属简介\"},\"quality\":{\"score\":7.5},\"refId\":1279653,\"searchStatus\":\"ALL\",\"source\":\"MainApp\",\"subElements\":[],\"summary\":\"专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专辑 简介专...\",\"title\":\"视频播放不同声音类型，是对方舒服的沙发是对方是对方，圣诞节为哦日是对方是独领风骚看的饭了诉讼法\",\"uid\":12854}],\"title\":\"【视频】史上最强配音：胥渡吧鸿篇巨献：《高考来袭》\",\"uid\":12854},\"itemType\":\"IndexFeedVideoNew\",\"itemTypeEnum\":\"IndexFeedVideoNew\",\"layoutId\":-1,\"mtlId\":6144333,\"mtlPosition\":0,\"mtlPriority\":0,\"mtlProvider\":\"recsys\",\"mtlRankType\":\"BASE\",\"mtlType\":\"ELEMENT\",\"p\":3,\"picForCustomColor\":\"http://imagev2.test.ximalaya.com/storages/5606-audiotest/F6/DA/GKwaLfoHXQ9IAAIHGgAAlG1g.jpeg\",\"ubtTraceId\":\"-3768356171669949498088\"}"

        private val albumTemplate = "{\"displayClass\":\"single\",\"item\":{\"albumId\":45446415,\"categoryId\":9,\"discountedPrice\":0,\"dislikeReasonNew\":{\"default\":[{\"codeType\":\"DEFAULT\",\"name\":\"不感兴趣\"},{\"codeType\":\"DEFAULT\",\"name\":\"听过了\"},{\"codeType\":\"DEFAULT\",\"name\":\"内容质量差\"},{\"codeType\":\"DEFAULT\",\"name\":\"不喜欢主播\"}],\"traits\":[{\"codeType\":\"CATEGORY\",\"name\":\"历史\"},{\"codeType\":\"FLATCATEGORY\",\"name\":\"中国史\"}]},\"dislikeReasonNewV1\":[{\"negative\":{\"key\":{\"codeType\":\"DEFAULT\",\"name\":\"不喜欢该内容\",\"setCodeType\":true,\"setName\":true},\"setKey\":true,\"setSubReasons\":false,\"subReasons\":[],\"subReasonsOpt\":{\"defined\":true}},\"reasonRowType\":0},{\"negative\":{\"key\":{\"codeType\":\"REDUCE_SIMILAR\",\"name\":\"减少相似内容推荐\",\"setCodeType\":true,\"setName\":true},\"setKey\":true,\"setSubReasons\":false,\"subReasons\":[],\"subReasonsOpt\":{\"defined\":true}},\"reasonRowType\":1},{\"negative\":{\"key\":{\"codeType\":\"DEFAULT\",\"name\":\"不喜欢该主播\",\"setCodeType\":true,\"setName\":true},\"setKey\":true,\"setSubReasons\":false,\"subReasons\":[],\"subReasonsOpt\":{\"defined\":true}},\"reasonRowType\":2},{\"negative\":{\"key\":{\"codeType\":\"DEFAULT\",\"name\":\"反馈问题\",\"setCodeType\":true,\"setName\":true},\"setKey\":true,\"setSubReasons\":true,\"subReasons\":[{\"codeType\":\"DEFAULT\",\"name\":\"涉及色情暴力内容\",\"setCodeType\":true,\"setName\":true},{\"codeType\":\"DEFAULT\",\"name\":\"标题或内容夸张、诱导点击\",\"setCodeType\":true,\"setName\":true},{\"codeType\":\"DEFAULT\",\"name\":\"涉及侵权\",\"setCodeType\":true,\"setName\":true},{\"codeType\":\"DEFAULT\",\"name\":\"内容粗糙不美观\",\"setCodeType\":true,\"setName\":true},{\"codeType\":\"DEFAULT\",\"name\":\"其他\",\"setCodeType\":true,\"setName\":true}],\"subReasonsOpt\":{\"defined\":true}},\"reasonRowType\":3}],\"feedbackAction\":\"dislike\",\"isActivity\":\"false\",\"isSampleAlbumTimeLimited\":false,\"landingPage\":\"jump\",\"price\":0,\"priceShowType\":0,\"recInfo\":{\"recSrc\":\"TAG2I-V2.DSSMPrerankerNewuser.CMTMMRalbum2.ctr-rescore|true.diversify|FlowDivByAlbumFusionV2|AlbumTraitsDivFusion.wrapper\",\"recTrack\":\"104.4486_10030-4488_10032-9660_21961-9706_22080-8123_18239-8197_18444-5701_12662-9883_22492-7589_17002-9868_22456-4485_10029-9934_22605-9266_21062-8849_20019-7592_17006-6191_13753.4042\"},\"secFields\":{},\"sourceType\":1,\"type\":0,\"uid\":126439854},\"itemType\":\"ALBUM\",\"itemTypeEnum\":\"ALBUM\",\"layoutId\":-1,\"mtlId\":45446415,\"mtlRankType\":\"BASE\",\"p\":2,\"ubtTraceId\":\"-8386140091700482902780\"}"

        private val KEY = "debug_video_upload_preview_json"
        private val KEY_SWITCH = "debug_video_upload_preview_switch"

        private fun save(videoStr: String) {
            MMKVUtil.getInstance().saveString(KEY, videoStr)
        }

        @JvmStatic
        fun getPreviewInfoStr(): String? {
            return MMKVUtil.getInstance().getString(KEY)
        }

        @JvmStatic
        fun enable(): Boolean {
            return MMKVUtil.getInstance().getBoolean(KEY_SWITCH, false)
        }

        @JvmStatic
        fun getPreviewInfo(): JSONObject? {
            val str = getPreviewInfoStr()
            return kotlin.runCatching { JSONObject(str) }.getOrNull()
        }
    }
}