package com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item;

import android.widget.CheckBox;
import android.widget.CompoundButton;

import com.ximalaya.cookiecontroller.CookieHelper;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.DebugType;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;

/**
 * Created by ervin.li on 2020-04-13.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class CookieHttp2OptimizeToggle extends BaseDebugItem {

    @Override
    public String getName() {
        return "Cookie切割";
    }

    @Override
    public DebugType getCategory() {
        return DebugType.CATEGORY_OTHER;
    }

    @Override
    boolean showToggle() {
        return true;
    }

    @Override
    boolean showArrow() {
        return false;
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        super.onCheckedChanged(buttonView, isChecked);
        CookieHelper.getInstance().setOpenCookieCrumble(isChecked);
        SharedPreferencesUtil.getInstance(mContext)
                .saveBoolean(PreferenceConstantsInHost.KEY_OPEN_HTTP2_COOKIE_OPTIMIZE, isChecked);
    }

    @Override
    protected void bindToggle(CheckBox toggle) {
        toggle.setChecked(SharedPreferencesUtil.getInstance(mContext)
                .getBoolean(PreferenceConstantsInHost.KEY_OPEN_HTTP2_COOKIE_OPTIMIZE, false));
    }

    @Override
    int getIconResId() {
        return 0;
    }

}
