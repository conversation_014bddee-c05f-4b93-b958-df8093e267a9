package com.ximalaya.ting.android.main.kachamodule.produce.component

import android.content.Context
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl

/**
 * Created by z<PERSON><PERSON><PERSON> on 11/24/20.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15721173906
 */
class KachaXMPlayerComponent(
        var track: Track,
        var startTime: Int,
        private val initSeekTime: Int,
        private val context: Context?,
        private val listener: IXmPlayerStatusListener) : KachaPlayerComponent() {

    private val player: XmPlayerManager by lazy(LazyThreadSafetyMode.NONE) {
        XmPlayerManager.getInstance(context)
    }

    init {
        player.addPlayerStatusListener(listener)
        // 替换播放列表中的数据 防止用户操作其他地方改变播放器逻辑
        player.setPlayList(listOf(track), 0)
        // 设置声音为单曲循环
        player.playMode = XmPlayListControl.PlayMode.PLAY_MODEL_SINGLE_LOOP
        player.tempo = 1.0f
    }

    override fun play() {
        super.play()
        player.play()
    }

    override fun pause() {
        super.pause()
        player.pause(PauseReason.Business.Kacha)
    }

    fun setVolume(volume: Float) {
        player.setVolume(volume, volume)
    }

    override fun seekTo(timeMs: Int) {
        player.seekTo(timeMs)
    }

    fun loop() {
        seekToStart()
        play()
    }

    override fun seekToStart() {
        seekTo(startTime)
    }

    fun changeTempo(value: Float) {
        player.tempo = value
    }

    fun currentPlayPosition(): Int {
        return player.playCurrPositon
    }

    fun firstStart() {
        seekTo(initSeekTime)
        play()
    }

    override fun release() {
        player.removePlayerStatusListener(listener)
    }
}