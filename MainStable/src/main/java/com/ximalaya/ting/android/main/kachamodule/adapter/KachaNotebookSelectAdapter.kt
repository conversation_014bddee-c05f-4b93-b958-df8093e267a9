package com.ximalaya.ting.android.main.kachamodule.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.main.kachamodule.model.KachaNoteBook
import com.ximalaya.ting.android.main.stable.R

/**
 * Created by zhangkaikai on 2021/10/9.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15721173906
 */
class KachaNotebookSelectAdapter(
        val data: List<KachaNoteBook?>,
        val itemClickAction: (notebook: Kacha<PERSON><PERSON>Book, position: Int) -> Unit
) : RecyclerView.Adapter<KachaNotebookSelectAdapter.TextHolder>() {

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): TextHolder {
        return TextHolder(LayoutInflater.from(viewGroup.context).inflate(
                if (viewType == KachaNoteBook.TYPE_CUSTOM)
                    R.layout.main_item_kacha_notebook_custom
                else
                    R.layout.main_item_kacha_notebook_select, null, false))
    }

    override fun onBindViewHolder(holder: TextHolder, position: Int) {
        position.takeIf { it in data.indices }?.let {
            data[it]?.let { book ->
                if (book.type == KachaNoteBook.TYPE_NORMAL) {
                    holder.itemView.isSelected = book.isSelected
                    (holder.itemView as? TextView)?.text = book.title
                }
                holder.itemView.setOnClickListener { itemClickAction(book, position) }
            }
        }
    }

    override fun getItemCount() = data.size

    override fun getItemViewType(position: Int): Int {
        return data[position]?.type ?: KachaNoteBook.TYPE_NORMAL
    }

    inner class TextHolder(view: View) : RecyclerView.ViewHolder(view)
}