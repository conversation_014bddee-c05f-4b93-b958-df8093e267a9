package com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug;

import android.content.Context;
import androidx.annotation.IdRes;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

/**
 * 简单封装的适用于RecyclerView的ViewHolder
 *
 * <AUTHOR>
 * @since 16/1/5
 */
public abstract class AbsViewBinder<T> extends RecyclerView.ViewHolder {
    private View mView;

    public AbsViewBinder(final View view) {
        super(view);
        mView = view;
        getViews();
    }

    protected final View getView() {
        return mView;
    }

    protected abstract void getViews();

    public final <V extends View> V getView(@IdRes int id) {
        return (V) mView.findViewById(id);
    }

    public abstract void bind(T t, int position);

    protected final Context getContext() {
        return mView.getContext();
    }
}
