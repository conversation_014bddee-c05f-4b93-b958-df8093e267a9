package com.ximalaya.ting.android.main.playlet.request;

import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;

/**
 * Created by zhangqianghua on 2021/12/22
 *
 * @phoneNumber 17621920801
 * @Description 这是描述
 * @wiki Wiki网址放在这里
 */
public class UrlConstantsForPlaylet extends UrlConstants {

    private static volatile UrlConstantsForPlaylet mSingleton;

    public static UrlConstantsForPlaylet getSingleton() {
        if (mSingleton == null) {
            synchronized (UrlConstantsForPlaylet.class) {
                if (mSingleton == null) {
                    mSingleton = new UrlConstantsForPlaylet();
                }
            }
        }
        return mSingleton;
    }

    @Override
    public String getVideoInfo(long trackId) {
        return getVideoPlayUrl() + "video/" + trackId + "/ts-" + System.currentTimeMillis();
    }

    /**
     * 听友圈消息列表
     */
    public String getMessageListUrl() {
        return getServerNetAddressHost() + "chaos-notice-web/v1/tyq/";
    }

    /**
     * 评论消息
     */
    public String getCommentMessageListUrl() {
        return getMessageListUrl() + "comment/list/" + System.currentTimeMillis();
    }

    /**
     * 提问消息
     */
    public String getQuestionMessageListUrl() {
        return getMessageListUrl() + "question/list/" + System.currentTimeMillis();
    }

    /**
     * 通知消息
     */
    public String getNoticeMessageListUrl() {
        return getMessageListUrl() + "notice/list/" + System.currentTimeMillis();
    }

    /**
     * 发现页推荐列表
     */
    public String getFindRecommendListUrl() {
        return getServerNetAddressHost() + "nexus/v4/stream/pull/" + System.currentTimeMillis();
    }

    /**
     * 发现页视频列表
     */
    public String getFindVideoListUrl() {
        return getServerNetAddressHost() + "ugc-web/stream/video/v1/recommend/" + System.currentTimeMillis();
    }

    @Override
    public String getDynamicTopicRecentTrackUrl() {
        return getHybridHost() + "dub-web/theme/getRecentTrack";
    }

    @Override
    public String getDynamicTopicTrackRankingUrl() {
        return getHybridHost() + "dub-web/theme/queryTemplate";
    }

    @Override
    public String getDynamicMyTopicWorksUrl() {
        return getServerNetAddressHost() + "mobile-dub-track/dubTrack/query/myworks";
    }

    public String getDynamicDetail(long feedId) {
        return getServerNetAddressHost() + "chaos-discovery-web/v1/" + feedId + "/feed/" + System.currentTimeMillis();
    }

    /**
     * 查询动态的评论列表(第一页)
     */
    public String dynamicRequestFirstPageCommentUrl() {
        return getServerNetAddressHost() + "chaos/v3/comment/first/list";
    }

    public String getQueryUserInfoInCommunityUrl() {

        return getMNetAddressHost() + "community/v1/user/profile";
    }

    public String getBaseUrlForFeedNew() {
        return getServerNetAddressHostForTopic() + "nexus/";
    }

    public String getCommunityBaseUrlV1() {
        return getMNetAddressHost() + "community/v1/";
    }

    public String getBaseUrlForFeedTopic() {
        return getBaseUrlForFeedNew() + "v1/topic/";
    }

    public String getRecommendTopicUrl() {
        return getBaseUrlForFeedTopic() + "recommendItems";
    }

    public String getHotTopicList() {
        return getBaseUrlForFeedTopic() + "hotList/" + System.currentTimeMillis();
    }

    //查询话题推荐专辑
    public String getRecommendAlbum(long communityId) {
        return getMNetAddressHost() + "community/v1/communities/" + communityId + "/topic/album/recommend";
    }

    //删除话题
    public String deleteTopic(long communityId, long topicId) {
        return getMNetAddressHost() + "community/v1/communities/" + communityId + "/topics/" + topicId + "/del";
    }

    /**
     * 沉浸式播放页跳转的背景音聚合页进入沉浸式播放页下拉更多视频
     */
    public String getAnchorMoreVideoList(long bgmId) {
        return getServerNetAddressHost() + "chaos-discovery-web/v1/bgm/" + bgmId + "/feed/playlist/ts-"
                + System.currentTimeMillis();
    }

    /**
     * 沉浸式播放页跳转的背景音聚合进入拍摄页前要查询素材
     */
    public String getBgmVideoMaterial() {
        return getServerNetAddressHost() + "vtool-web/v1/materials/video/records";
    }


    //订阅专辑
    public String subscribeAlbum() {
        return getServerNetAddressHost() + "subscribe/v1/subscribe/create";
    }

    /**
     * 获取发现页发布按钮的选项
     */
    public String getFeedHomeTabList() {
        return getServerNetAddressHost() + "nexus-web/option/publish/feed_following";
    }

    /**
     * 拍同款视频缩略地址
     */
    public String queryAnchorSmallVideoPathUrl() {
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            return "http://mpay.ximalaya.com/social-video-web/v1/video/download";
        } else if (BaseConstants.ENVIRONMENT_UAT == BaseConstants.environmentId) {
            return "http://mpay.uat.ximalaya.com/social-video-web/v1/video/download";
        } else {
            return "http://mpay.dev.test.ximalaya.com/social-video-web/v1/video/download";
        }
    }

    /**
     * 提问详情
     */
    public String getQuestionDetailUrl(long id) {
        return getServerNetAddressHost() + "social-question-web/v1/questions/" + id;
    }

    /**
     * 提问的回答列表
     */
    public String getQuestionAnswerUrl(long id) {
        return getServerNetAddressHost() + "social-question-web/v1/questions/" + id + "/answers";
    }

    public String getKachaIdUrl() {
        return getServerNetAddressHost() + "shortcontent-web/shortcontent/queryid/byfeed/" + System.currentTimeMillis();
    }


    public String getPlayletAllLikeUrl() {
        return getServerNetAddressHost() + "mobile-album/playlet/chasing/" + System.currentTimeMillis();
    }

    public String getPlayletAllRecommendUrl() {
        return getServerNetAddressHost() + "mobile-album/playlet/playList/" + System.currentTimeMillis();
    }

    public String getPlayletListUrl() {
        return getServerNetAddressHost() + "mobile-album/playlet/auto/" + System.currentTimeMillis();
    }

    public String getPlayletCollectUrl() {
        return getServerNetAddressHost() + "general-follow-service/follow/create/" + System.currentTimeMillis();
    }

    public String getPlayletCancelCollectUrl() {
        return getServerNetAddressHost() + "general-follow-service/follow/cancel/" + System.currentTimeMillis();
    }

    public String getPlayletPraiseUrl() {
        return getServerNetAddressHost() + "shortcontent-like/like/do/" + System.currentTimeMillis();
    }

    public String getPlayletCancelPraiseUrl() {
        return getServerNetAddressHost() + "shortcontent-like/like/cancel/" + System.currentTimeMillis();
    }
}
