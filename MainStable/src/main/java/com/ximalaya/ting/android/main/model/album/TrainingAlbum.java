package com.ximalaya.ting.android.main.model.album;

import android.text.TextUtils;

import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.TrainingCampInfo;
import com.ximalaya.ting.android.host.model.album.TrainingPageData;
import com.ximalaya.ting.android.host.model.album.TrainingPunchReward;
import com.ximalaya.ting.android.main.StableProvider;

import org.json.JSONObject;

import java.io.Serializable;
import java.util.List;

/**
 * Created by ervin.li on 2019-10-16.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class TrainingAlbum extends AlbumM implements Serializable {
    public static final long ONE_DAY = 24 * 60 * 60 * 1000; // 一天的毫秒数

    private long saleEnd;//训练营结束
    private long openStart;//训练营开始
    private String buttonText;//训练营购买文案
    private boolean canBuy;//能否购买
    private String personalDescription;


    public boolean isShowScroller;
    public boolean isShowSales;
    public boolean isShowCountdown;
    public int saleAmount;

    public List<String> saleList;

    public long remainMilliseconds;

    public String getPersonalDescription() {
        return personalDescription;
    }

    public void setPersonalDescription(String personalDescription) {
        this.personalDescription = personalDescription;
    }

    public boolean isCanBuy() {
        return canBuy;
    }

    public void setCanBuy(boolean canBuy) {
        this.canBuy = canBuy;
    }

    public long getSaleEnd() {
        return saleEnd;
    }

    public void setSaleEnd(long saleEnd) {
        this.saleEnd = saleEnd;
    }

    public long getOpenStart() {
        return openStart;
    }

    public void setOpenStart(long openStart) {
        this.openStart = openStart;
    }

    public String getButtonText() {
        return buttonText;
    }

    public void setButtonText(String buttonText) {
        this.buttonText = buttonText;
    }

    public static TrainingAlbum parse(JSONObject dataContentJson) throws Exception {
        if (dataContentJson == null) return null;
        String albumJson = dataContentJson.optString("album");
        TrainingAlbum trainingAlbum = new TrainingAlbum();

        trainingAlbum.setFavorite(dataContentJson.optBoolean("subscribe"));
        trainingAlbum.setCpsProductCommission(dataContentJson.optDouble("cpsProductCommission"));
        trainingAlbum.setCpsPromotionRate(dataContentJson.optDouble("cpsPromotionRate"));
        trainingAlbum.setCpsProductExist(dataContentJson.optBoolean("isCpsProductExist"));
        trainingAlbum.setCpsTitle(dataContentJson.optString("cpsTitle"));
        trainingAlbum.setCpsContent(dataContentJson.optString("cpsContent"));
        trainingAlbum.setCpsRuleDesc(dataContentJson.optString("cpsRuleDesc"));
        trainingAlbum.setCpsRuleLink(dataContentJson.optString("cpsRuleLink"));

        if (!TextUtils.isEmpty(albumJson)) {
            JSONObject jsonData = new JSONObject(albumJson);
            trainingAlbum.setId(jsonData.optLong("albumId"));
            trainingAlbum.setAlbumTitle(jsonData.optString("title"));
            trainingAlbum.setSubTitle(jsonData.optString("customSubTitle"));
            trainingAlbum.setStatus(jsonData.optInt("status"));
            trainingAlbum.setIntroRich(jsonData.optString("richIntro"));
            trainingAlbum.setBuyNotes(jsonData.optString("buyNotes"));
            trainingAlbum.setCreatedAt(jsonData.optLong("createdAt"));
            trainingAlbum.setUpdatedAt(jsonData.optLong("updatedAt"));
            trainingAlbum.setIsFinished(jsonData.optInt("isFinish"));
            trainingAlbum.setLastUptrackAt(Long.toString(jsonData.optLong("lastUptrackAt")));
            trainingAlbum.setCoverPathHighClear(jsonData.optString("coverPathHighClear"));
            trainingAlbum.setButtonText(jsonData.optString("buttonText"));
            trainingAlbum.setPrice(jsonData.optDouble("price"));
            trainingAlbum.setCouponPrice(jsonData.optDouble("couponPrice"));
            trainingAlbum.setAllowancePrice(jsonData.optDouble("allowancePrice"));
            trainingAlbum.setAfterAllowancePrice(jsonData.optDouble("afterAllowancePrice"));
            trainingAlbum.setPriceUnit(jsonData.optString("priceUnit"));
            trainingAlbum.setCanBuy(jsonData.optBoolean("canBuy"));
            trainingAlbum.setItemId(jsonData.optLong("itemId"));
            trainingAlbum.setCommentsCounts(jsonData.optInt("commentNum"));
            trainingAlbum.setPublic(jsonData.optBoolean("isPublic"));
            trainingAlbum.setShareSupportType(jsonData.optInt("shareSupportType"));
            trainingAlbum.setOutline(jsonData.optString("outline"));
            trainingAlbum.setTotalTrackCount(jsonData.optInt("totalTrackCount"));

            trainingAlbum.personalDescription = jsonData.optString("personalDescription");
            trainingAlbum.isShowScroller = jsonData.optBoolean("isShowScroller");
            trainingAlbum.remainMilliseconds = jsonData.optLong("remainMilliseconds");
            trainingAlbum.isShowSales = jsonData.optBoolean("isShowSales");
            trainingAlbum.isShowCountdown = jsonData.optBoolean("isShowCountdown");
            trainingAlbum.saleAmount = jsonData.optInt("saleAmount");
            trainingAlbum.saleList = StableProvider.getInstance().searchUtilsParseList(jsonData.optString("saleList"));

            TrainingPageData trainingPageData = new TrainingPageData();
            TrainingCampInfo campInfo = new TrainingCampInfo();
            campInfo.openStart = jsonData.optLong("openStart");
            campInfo.saleEnd = jsonData.optLong("saleEnd");
            campInfo.openEnd = jsonData.optLong("openEnd");
            if (campInfo.openStart > 0) {
                campInfo.tryDay = campInfo.openStart - ONE_DAY;
            }
            campInfo.trainingId = jsonData.optLong("trainId");
            campInfo.phraseInfoId = jsonData.optInt("periodId");
            trainingPageData.campInfo = campInfo;
            trainingAlbum.setTrainingPageData(trainingPageData);

            trainingAlbum.saleEnd = campInfo.saleEnd;
            trainingAlbum.openStart = campInfo.openStart;

            //为本地订阅提供训练营标示
            trainingAlbum.setType(3);
            trainingAlbum.setGoToAlbumPresalePageVersion(2);
        }

        if (dataContentJson.has("trainingPunchReward")) {
            trainingAlbum.setTrainingPunchReward(TrainingPunchReward.parse(dataContentJson.optString("trainingPunchReward", null)));
        }
        return trainingAlbum;
    }
}
