package com.ximalaya.ting.android.main.adapter.mulitviewtype;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

/**
 * @param <T>
 * @param <M>
 * <AUTHOR>
 */
public interface IMulitViewTypeViewAndDataStaggered<T extends RecyclerView.ViewHolder, M> {
//    void bindViewDatas(T holder, ItemModel<Model> t, View convertView, int position);
//
//    View getView(LayoutInflater layoutInflater, int position, ViewGroup parent);

    T createViewHolder(View convertView);

    void bindViewHolder(T holder, int position, M data, View convertView);

    default int getLayoutId() {
        return 0;
    }

    default View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return null;
    }
}