<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/main_transparent">

    <com.ximalaya.ting.android.main.view.text.StaticLayoutView
        android:id="@+id/main_training_item_content_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/main_color_333333_cfcfcf"
        android:textSize="14dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="贴大大鱼讲论语增值贴大鱼讲论语增值大讲论语增值大讲论语增值大贴大大鱼讲论语增值贴大鱼讲论语增值大讲论语增值大讲论语增值大贴大大鱼讲论语增值贴大鱼讲论语增值大讲论语增值大讲论语增值大贴大大鱼讲论语增值贴大鱼讲论语增值大讲论语增值大讲论语增值大贴大大鱼讲论语增值贴大鱼讲论语增值大讲论语增值大讲论语增值大贴大大鱼讲论语增值贴大鱼讲论语增值大讲论语增值大讲论语增值大。" />

    <ImageView
        android:id="@+id/main_training_item_content_audio"
        android:layout_width="100dp"
        android:layout_height="30dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/main_audio_bubble"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/main_training_item_content_text"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/main_iv_voice_icon"
        android:tag=""
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_marginLeft="10dp"
        android:src="@drawable/main_anim_voice"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/main_training_item_content_audio"
        app:layout_constraintStart_toStartOf="@id/main_training_item_content_audio"
        app:layout_constraintTop_toTopOf="@id/main_training_item_content_audio"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/main_tv_voice_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:textColor="@color/main_color_white"
        android:textSize="10sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/main_training_item_content_audio"
        app:layout_constraintStart_toEndOf="@id/main_iv_voice_icon"
        app:layout_constraintTop_toTopOf="@id/main_training_item_content_audio"
        tools:text="12'12'"
        tools:visibility="visible" />

    <GridLayout
        android:id="@+id/main_training_item_content_images"
        android:layout_marginTop="10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/main_transparent"
        app:layout_constraintTop_toBottomOf="@id/main_training_item_content_audio"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:background="@color/main_transparent"
            android:layout_width="100dp"
            android:layout_height="75dp"
            android:layout_row="0"
            android:layout_column="0"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:corner_radius="4dp"/>
        <TextView
            android:background="@color/main_transparent"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_row="0"
            android:layout_column="1"
            android:visibility="gone"/>
        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:background="@color/main_transparent"
            android:layout_width="100dp"
            android:layout_height="75dp"
            android:layout_row="0"
            android:layout_column="2"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:corner_radius="4dp"/>
        <TextView
            android:background="@color/main_transparent"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_row="0"
            android:layout_column="3"
            android:visibility="gone"/>
        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:background="@color/main_transparent"
            android:layout_width="100dp"
            android:layout_height="75dp"
            android:layout_row="0"
            android:layout_column="4"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:corner_radius="4dp"/>

        <TextView
            android:background="@color/main_transparent"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_row="1"
            android:layout_column="0"
            android:visibility="gone"/>

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:background="@color/main_transparent"
            android:layout_width="100dp"
            android:layout_height="75dp"
            android:layout_row="2"
            android:layout_column="0"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:corner_radius="4dp"/>
        <TextView
            android:background="@color/main_transparent"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_row="2"
            android:layout_column="1"
            android:visibility="gone"/>
        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:background="@color/main_transparent"
            android:layout_width="100dp"
            android:layout_height="75dp"
            android:layout_row="2"
            android:layout_column="2"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:corner_radius="4dp"/>
        <TextView
            android:background="@color/main_transparent"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_row="2"
            android:layout_column="3"
            android:visibility="gone"/>
        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:background="@color/main_transparent"
            android:layout_width="100dp"
            android:layout_height="75dp"
            android:layout_row="2"
            android:layout_column="4"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:corner_radius="4dp"/>

        <TextView
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_row="3"
            android:layout_column="0"
            android:visibility="gone"/>

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:background="@color/main_transparent"
            android:layout_width="100dp"
            android:layout_height="75dp"
            android:layout_row="4"
            android:layout_column="0"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:corner_radius="4dp"/>
        <TextView
            android:background="@color/main_transparent"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_row="4"
            android:layout_column="1"
            android:visibility="gone"/>
        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:background="@color/main_transparent"
            android:layout_width="100dp"
            android:layout_height="75dp"
            android:layout_row="4"
            android:layout_column="2"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:corner_radius="4dp"/>
        <TextView
            android:background="@color/main_transparent"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_row="4"
            android:layout_column="3"
            android:visibility="gone"/>
        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:background="@color/main_transparent"
            android:layout_width="100dp"
            android:layout_height="75dp"
            android:layout_row="4"
            android:layout_column="4"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:corner_radius="4dp"/>


    </GridLayout>

</androidx.constraintlayout.widget.ConstraintLayout>