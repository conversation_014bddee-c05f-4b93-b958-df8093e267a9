<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/main_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@drawable/main_gray_underline_white_bg" />

    <RelativeLayout
        android:id="@+id/main_bundle_use_info_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@drawable/main_bg_ffffff_1e1e1e_coner_4">

        <TextView
            android:id="@+id/main_debug_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="10dp"
            android:text="插件使用信息"
            android:textSize="18sp" />

        <View
            android:id="@+id/main_debug_view_1"
            android:layout_width="match_parent"
            android:layout_height="0.8dp"
            android:layout_below="@+id/main_debug_title"
            android:background="@color/host_color_f3f4f5" />


        <androidx.core.widget.NestedScrollView
            android:id="@+id/main_bundle_use_info_wrap"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/main_menue"
            android:layout_below="@id/main_debug_view_1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <HorizontalScrollView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/main_bundle_use_info"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="0dp"
                            android:layout_marginRight="0dp"
                            android:layout_marginBottom="2dp"
                            android:paddingLeft="2dp" />
                    </LinearLayout>
                </HorizontalScrollView>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>


        <View
            android:id="@+id/main_debug_view_2"
            android:layout_width="match_parent"
            android:layout_height="0.8dp"
            android:layout_below="@+id/main_bundle_use_info_wrap"
            android:background="@color/host_color_f3f4f5" />

        <LinearLayout
            android:id="@+id/main_menue"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_alignParentBottom="true"
            android:visibility="gone">

            <TextView
                android:id="@+id/main_bundle_use_info_cancel"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="取消" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/host_color_f3f4f5" />

            <TextView
                android:id="@+id/main_bundle_use_info_share"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="分享" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/host_color_f3f4f5" />

            <TextView
                android:id="@+id/main_bundle_use_info_copy"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="复制" />
        </LinearLayout>
    </RelativeLayout>

    <ProgressBar
        android:id="@+id/main_debug_pb_loading"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_gravity="center"
        android:visibility="gone" />

</LinearLayout>
