<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_root_limit_exit"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_centerInParent="true">

    <LinearLayout
        android:paddingHorizontal="16dp"
        android:id="@+id/main_ll_limit_exit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginHorizontal="50dp"
        android:background="@drawable/main_bg_rect_ffecea_radius_10"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/main_tv_title_limit_exit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:text="亲,现在就要离开吗?"
            android:textColor="#660000"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/main_tv_subtitle_limit_exit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="您还有任务未完成"
            android:textColor="#99660000"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/main_tv_dsp_limit_exit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="完成即可获得惊喜礼盒哦"
            android:textColor="#99660000"
            android:textSize="14sp" />

        <ImageView
            android:id="@+id/main_iv_limit_exit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="14dp"
            tools:src="@drawable/main_icon_limit_gift_bg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="24dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/main_tv_limit_later_exit"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="40dp"
                android:background="@drawable/main_bg_stroke_ff4444_corner_20"
                android:gravity="center"
                android:text="稍后收听"
                android:textColor="#ff4444"
                android:textSize="14sp"
                android:textStyle="bold"
                />

            <TextView
                android:id="@+id/main_tv_limit_confirm_exit"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="40dp"
                android:layout_marginLeft="10dp"
                android:background="@drawable/main_bg_ff4444_20"
                android:gravity="center"
                android:text="立即收听"
                android:textColor="#ffffff"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/main_iv_limit_close_exit"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_alignTop="@id/main_ll_limit_exit"
        android:layout_alignRight="@id/main_ll_limit_exit"
        android:layout_marginTop="2dp"
        android:layout_marginRight="2dp"
        android:padding="10dp"
        android:src="@drawable/main_icon_limit_gift_close" />

</RelativeLayout>