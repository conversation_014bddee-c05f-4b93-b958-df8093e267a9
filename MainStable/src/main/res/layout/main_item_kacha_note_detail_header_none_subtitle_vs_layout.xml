<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/main_kacha_product_author_write_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="32dp"
        android:layout_marginTop="28dp"
        android:layout_marginRight="32dp"
        android:layout_marginBottom="24dp"
        android:textColor="@color/main_color_111111_cfcfcf"
        android:textSize="17sp"
        tools:text="这是我制作的咔嚓，邀请你来听，文案占位文案占位文案占位文案" />

    <TextView
        android:id="@+id/main_kacha_product_author_write_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginRight="32dp"
        android:textColor="@color/main_color_999999"
        android:textSize="12sp"
        tools:text="发布于 2021-03-09 09:22 下午" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/host_common_margin_16"
        android:layout_marginTop="@dimen/host_common_margin_16"
        android:layout_marginRight="@dimen/host_common_margin_16"
        android:background="@drawable/main_bg_ffffff_radius_8"
        android:paddingBottom="24dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_kacha_product_cover_bg"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:layout_marginStart="32dp"
            android:layout_marginTop="22dp"
            android:scaleType="centerCrop"
            android:alpha="0.1"
            app:corner_radius="@dimen/host_common_margin_8"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_kacha_product_cover"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:layout_marginStart="24dp"
            android:layout_marginTop="30dp"
            android:scaleType="centerCrop"
            android:src="@drawable/host_default_album_large"
            app:corner_radius="@dimen/host_common_margin_8"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:layout_width="105dp"
            android:layout_height="80dp"
            android:scaleType="centerCrop"
            android:src="@drawable/main_ic_kacha_blank_product_corner"
            app:corner_radius="@dimen/host_common_margin_8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/main_kacha_product_track_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="50dp"
            android:maxLines="2"
            android:textColor="@color/main_color_333333"
            android:textSize="17sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/main_kacha_product_cover"
            app:layout_constraintTop_toBottomOf="@+id/main_kacha_product_cover"
            app:layout_constraintVertical_chainStyle="spread_inside"
            tools:text="Ep.46 林安：自由与面包不可兼得" />

        <TextView
            android:id="@+id/main_kacha_product_album_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/host_common_margin_8"
            android:maxLines="1"
            android:textColor="@color/host_color_aaaaaa"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="@+id/main_kacha_product_track_title"
            app:layout_constraintStart_toStartOf="@+id/main_kacha_product_track_title"
            app:layout_constraintTop_toBottomOf="@+id/main_kacha_product_track_title"
            tools:text="非正常旅行" />

        <com.ximalaya.ting.android.host.view.PlayRichSeekBar
            android:id="@+id/main_kacha_product_seek_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="@dimen/host_common_margin_16"
            android:layout_weight="1"
            android:contentDescription="播放进度条"
            android:max="100"
            android:maxHeight="3dp"
            android:minHeight="3dp"
            android:paddingStart="0dp"
            android:paddingTop="5dp"
            android:paddingEnd="0dp"
            android:paddingBottom="5dp"
            android:progress="0"
            android:progressDrawable="@drawable/main_kacha_product_seekbar"
            android:thumb="@drawable/host_seek_bar_thumb_transparent"
            android:thumbOffset="0dp"
            app:host_keyPointColor="@color/main_transparent"
            app:host_leftPointColor="@color/main_transparent"
            app:host_rightPointColor="@color/main_transparent"
            app:host_thumbLayout="@layout/main_layout_kacha_product_seek_bar_thumb"
            app:layout_constraintEnd_toStartOf="@+id/main_kacha_product_play_pause_control"
            app:layout_constraintStart_toStartOf="@+id/main_kacha_product_cover"
            app:layout_constraintTop_toBottomOf="@+id/main_kacha_product_album_name" />

        <ImageView
            android:id="@+id/main_kacha_product_play_pause_control"
            android:layout_width="27dp"
            android:layout_height="27dp"
            android:layout_marginEnd="24dp"
            android:src="@drawable/main_ic_product_play"
            app:layout_constraintBottom_toBottomOf="@+id/main_kacha_product_seek_bar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main_kacha_product_seek_bar"
            app:layout_constraintTop_toTopOf="@+id/main_kacha_product_seek_bar" />

        <View
            android:id="@+id/main_kacha_product_listen_all"
            android:layout_width="260dp"
            android:layout_height="44dp"
            android:layout_marginTop="40dp"
            android:background="@drawable/main_bg_kacha_next_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_kacha_product_seek_bar" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableRight="@drawable/main_ic_kacha_note_detail_go"
            android:drawablePadding="6dp"
            android:text="听整集"
            android:textColor="@color/host_color_ffffff"
            app:layout_constraintBottom_toBottomOf="@+id/main_kacha_product_listen_all"
            app:layout_constraintEnd_toEndOf="@+id/main_kacha_product_listen_all"
            app:layout_constraintStart_toStartOf="@+id/main_kacha_product_listen_all"
            app:layout_constraintTop_toTopOf="@+id/main_kacha_product_listen_all" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>