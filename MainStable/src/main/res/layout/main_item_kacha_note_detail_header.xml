<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/main_kacha_product_author_write_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="32dp"
        android:layout_marginTop="28dp"
        android:layout_marginRight="32dp"
        android:layout_marginBottom="24dp"
        android:textColor="@color/main_color_111111_cfcfcf"
        android:textSize="17sp"
        tools:text="这是我制作的咔嚓，邀请你来听，文案占位文案占位文案占位文案" />

    <TextView
        android:id="@+id/main_kacha_product_author_write_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginRight="32dp"
        android:textColor="@color/main_color_999999"
        android:textSize="12sp"
        tools:text="发布于 2021-03-09 09:22 下午" />

    <ViewStub
        android:id="@+id/main_kacha_note_detail_header_stub"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/host_common_margin_16"
        android:layout_marginTop="@dimen/host_common_margin_16"
        android:layout_marginRight="@dimen/host_common_margin_16"
        android:inflatedId="@+id/main_kacha_note_detail_header_inside"
        android:layout="@layout/main_layout_kacha_note_detail_header" />

    <ViewStub
        android:id="@+id/main_kacha_note_detail_header_none_subtitle_stub"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/host_common_margin_16"
        android:layout_marginTop="@dimen/host_common_margin_16"
        android:layout_marginRight="@dimen/host_common_margin_16"
        android:inflatedId="@+id/main_kacha_note_detail_header_none_subtitle_inside"
        android:layout="@layout/main_layout_kacha_note_detail_none_subtitle_header" />
</LinearLayout>