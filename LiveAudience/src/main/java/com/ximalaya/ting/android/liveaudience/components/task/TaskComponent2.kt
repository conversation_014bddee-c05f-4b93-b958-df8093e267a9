package com.ximalaya.ting.android.liveaudience.components.task

import android.content.Context
import android.graphics.Rect
import android.os.Bundle
import android.view.View
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.live.R
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType
import com.ximalaya.ting.android.live.common.lib.base.constants.RoomProgressiveLoadingConstants
import com.ximalaya.ting.android.live.common.lib.base.util.LiveCommonITingUtil.handleITing
import com.ximalaya.ting.android.live.common.lib.entity.LiveAnchorTaskMessage
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager
import com.ximalaya.ting.android.live.common.lib.utils.XmLiveRequestIdHelper
import com.ximalaya.ting.android.live.common.lib.utils.XmLiveRequestIdHelper.getNewXmQuestId
import com.ximalaya.ting.android.live.host.data.AnchorTask
import com.ximalaya.ting.android.live.host.data.AnchorTaskWrapper
import com.ximalaya.ting.android.live.host.data.RoomTaskEntranceModel
import com.ximalaya.ting.android.live.host.fragment.debug.DebugItem
import com.ximalaya.ting.android.live.host.fragment.debug.RoomDebugConfigure
import com.ximalaya.ting.android.live.host.fragment.debug.handler.DebugHandlerManager
import com.ximalaya.ting.android.live.host.fragment.debug.handler.IRoomDebugEventHandler
import com.ximalaya.ting.android.live.lib.chatroom.entity.ent.CommonNavigationInfo
import com.ximalaya.ting.android.liveaudience.components.base.LamiaComponentNew
import com.ximalaya.ting.android.liveaudience.data.request.CommonRequestForLive
import com.ximalaya.ting.android.liveaudience.util.LamiaHelper
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger
import java.lang.ref.WeakReference
import java.lang.reflect.Field

/**
 * 观众端只展示主播的任务星级
 * 观众端和主播端样式不一样
 *
 * <AUTHOR>
 */
class TaskComponent2 : LamiaComponentNew(), ITaskComponent,
    TaskComponentAdapter.IOnPageChangeListener2, TaskComponentAdapter.IOnPageClickListener2 {
    companion object {
        const val TAG = "TaskComponentLOG"
    }

    private var viewPager2: ViewPager2? = null

    private val modelList: MutableList<RoomTaskEntranceModel> = mutableListOf()

    private val taskComponentAdapter: TaskComponentAdapter by lazy {
        TaskComponentAdapter(isFromHostFragment, modelList).apply {
            onPageChangeListener = this@TaskComponent2
            onPageClickListener = this@TaskComponent2
        }
    }


    private var currentBannerView: TaskVerticalView? = null

    private var currentHttpData: AnchorTaskWrapper? = null

    override fun onCreate() {
        super.onCreate()
        if (ConstantsOpenSdk.isDebug) {
            DebugHandlerManager.addHandler(
                "发送一个领航消息",
                "audie",
                object : IRoomDebugEventHandler {
                    override fun onClick(item: DebugItem): Boolean {
                        val m = LiveAnchorTaskMessage().apply {
                            type = LiveAnchorTaskMessage.TYPE_ANCHOR_PILOT
                            navigationVo = CommonNavigationInfo().apply {
                                this.icon =
                                    "https://imagev2.xmcdn.com/storages/dea9-audiofreehighqps/1A/58/GAqhntAL5K-nAAAb3QOh3ql-.png"
                                this.level = 48
                            }
                        }
                        onReceivedAnchorTaskMessage(m)
                        return true
                    }
                })

            DebugHandlerManager.addHandler(
                "发送一个主播任务完成消息",
                "audie",
                object : IRoomDebugEventHandler {
                    override fun onClick(item: DebugItem): Boolean {
                        val m = LiveAnchorTaskMessage().apply {
                            type = LiveAnchorTaskMessage.TYPE_ANCHOR_TASK
                            complete = true
                            anchorTaskVos.add(LiveAnchorTaskMessage.AnchorTaskOrWish().apply {
                                icon = ""
                                level = 10
                            })
                        }
                        onReceivedAnchorTaskMessage(m)
                        return true
                    }
                })

            DebugHandlerManager.addHandler(
                "发送一个0主播任务消息",
                "audie",
                object : IRoomDebugEventHandler {
                    override fun onClick(item: DebugItem): Boolean {
                        val m = LiveAnchorTaskMessage().apply {
                            type = LiveAnchorTaskMessage.TYPE_ANCHOR_TASK
                            complete = true
                        }
                        onReceivedAnchorTaskMessage(m)
                        return true
                    }
                })
        }
    }


    private val autoHorizonScrollTask = Runnable {
        viewPager2?.let {
            val currentItem = it.currentItem
            Logger.d(TAG, "autoHorizonScrollTask，currentItem $currentItem")
            if (modelList.size <= 1) {
                return@Runnable
            }
            if (currentItem >= modelList.size - 1) {
                Logger.d(TAG, "autoHorizonScrollTask，setCurrentItem to 0")
                it.setCurrentItem(0, true)
            } else {
                Logger.d(TAG, "autoHorizonScrollTask，setCurrentItem to ${currentItem + 1}}")
                it.setCurrentItem(currentItem + 1, true)
            }
        }
    }

    private val viewPage2Change: ViewPager2.OnPageChangeCallback =
        object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                Logger.d(TAG, "viewPage2Change onPageSelected，position $position")
                val recyclerView = viewPager2?.getChildAt(0) as RecyclerView
                viewPager2?.post {
                    Logger.d(
                        TAG,
                        "viewPage2Change onPageSelected childCount ${recyclerView.childCount}"
                    )
                    val selectModel = modelList.getOrNull(position)
                    for (i in 0 until recyclerView.childCount) {
                        val child = recyclerView.getChildAt(i)
                        Logger.d(TAG, "viewPage2Change onPageSelected child ${child}")
                        (child as? TaskVerticalView)?.let {
                            Logger.d(
                                TAG,
                                "viewPage2Change onPageSelected it.dataPosition ${it.mode}  ${child.mode}"
                            )
                            if (it.mode == selectModel) {
                                startVerticalLoopOrHorizonLoopAfterHorizonSelect(it)
                                return@post
                            }
                        }
                    }
                }
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                if (state == ViewPager2.SCROLL_STATE_IDLE) {
                    val recyclerView = viewPager2?.getChildAt(0) as RecyclerView
                    viewPager2?.post {
                        Logger.d(
                            TAG,
                            "viewPage2Change onPageSelected childCount ${recyclerView.childCount}"
                        )
                        val selectModel = modelList.getOrNull(viewPager2?.currentItem ?: 0)
                        for (i in 0 until recyclerView.childCount) {
                            val child = recyclerView.getChildAt(i)
                            Logger.d(TAG, "viewPage2Change onPageSelected child ${child}")
                            (child as? TaskVerticalView)?.let {
                                Logger.d(
                                    TAG,
                                    "viewPage2Change onPageSelected it.selectModel ${selectModel}  ${child.mode}"
                                )
                                if (it.mode == selectModel) {
                                    startVerticalLoopOrHorizonLoopAfterHorizonSelect(it)
                                    return@post
                                }
                            }
                        }
                    }
                }
            }
        }

    private fun startVerticalLoopOrHorizonLoopAfterHorizonSelect(bannerView: TaskVerticalView) {
        val data: RoomTaskEntranceModel =
            bannerView.getTag(com.ximalaya.ting.android.framework.R.id.framework_view_holder_data) as? RoomTaskEntranceModel
                ?: return
        handHorizonItemShow(data)
        currentBannerView = bannerView
        Logger.d(TAG, "toVerticalLoopOrHorizonLoop ${data.tasks.size}")
        if (data.tasks.size > 1) {
            Logger.d(TAG, "有多个任务，竖向开始移动")
            bannerView.resetToFirstItem()
            bannerView.startLoop()
        } else {
            Logger.d(TAG, "只有1个任务，开始横向轮播")
            HandlerManager.removeCallbacks(autoHorizonScrollTask)
            HandlerManager.postOnUIThreadDelay(autoHorizonScrollTask, 5000)
        }
    }

    override fun onPageClick(position: Int, parentPosition: Int, mode: RoomTaskEntranceModel?) {
        handClickAndGoH5(mode)
    }

    private fun handClickAndGoH5(mode: RoomTaskEntranceModel?) {
        mode?.url ?: return
        if (TextUtils.isEmpty(mode.url)) {
            CustomToast.showDebugFailToast("click ${mode.localName} url empty")
            return
        }
        CustomToast.showDebugFailToast("click ${mode.localName}")
        when (mode.type) {
            LiveAnchorTaskMessage.TYPE_ANCHOR_PILOT,
            LiveAnchorTaskMessage.TYPE_ANCHOR_TASK,
            LiveAnchorTaskMessage.TYPE_ANCHOR_TASK_EMPTY -> {
                val item =
                    if (mode.type == LiveAnchorTaskMessage.TYPE_ANCHOR_PILOT) "领航任务" else "主播任务"
                // 直播间-主播任务  点击事件
                XMTraceApi.Trace()
                    .click(56869) // 用户点击时上报
                    .put("currPage", "liveRoom")
                    .put(LiveRecordInfoManager.getInstance().baseProps)
                    .put("Item", item)
                    .createTrace()
            }

            LiveAnchorTaskMessage.TYPE_ANCHOR_WISH -> {
                XMTraceApi.Trace()
                    .click(33377)
                    .put("item", "2")
                    .put("currPage", "liveRoom")
                    .put(LiveRecordInfoManager.getInstance().baseProps)
                    .createTrace()
            }
        }
        if (NativeHybridFragment.isItingScheme(mode.url)) {
            //通过iTing打开
            handleITing(activity, mode.url)
        }
    }

    private fun handHorizonItemShow(mode: RoomTaskEntranceModel?) {
        mode ?: return
        if (mode.uploadTrace) {
            return
        }
        when (mode.type) {
            LiveAnchorTaskMessage.TYPE_ANCHOR_PILOT,
            LiveAnchorTaskMessage.TYPE_ANCHOR_TASK,
            LiveAnchorTaskMessage.TYPE_ANCHOR_TASK_EMPTY -> {
                val item =
                    if (mode.type == LiveAnchorTaskMessage.TYPE_ANCHOR_PILOT) "领航任务" else "主播任务"
                // 直播间-主播任务  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(56870)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "liveRoom")
                    .put(XmLiveRequestIdHelper.IS_DUPLICATE_VIEW, "2")
                    .put(LiveRecordInfoManager.getInstance().baseProps)
                    .put("Item", item)
                    .createTrace()
                mode.uploadTrace = true
            }

            LiveAnchorTaskMessage.TYPE_ANCHOR_WISH -> {
                XMTraceApi.Trace()
                    .setMetaId(33378)
                    .setServiceId("slipPage")
                    .put("currPage", "liveRoom")
                    .put("item", "2")
                    .put(XmLiveRequestIdHelper.IS_DUPLICATE_VIEW, "2")
                    .put(LiveRecordInfoManager.getInstance().baseProps)
                    .createTrace()
                mode.uploadTrace = true
            }
        }
    }

    override fun onPageSelected(
        position: Int,
        parentPosition: Int,
        mode: RoomTaskEntranceModel?,
        bannerView: TaskVerticalView
    ) {
        Logger.d(TAG, "vertical onPageSelected $position  $parentPosition $mode")
        mode ?: return
    }

    override fun onPageVerticalLastItemSelected(
        position: Int,
        parentPosition: Int,
        mode: RoomTaskEntranceModel?,
        bannerView: TaskVerticalView
    ) {
        mode ?: return
        if (position >= mode.tasks.size - 1) {
            bannerView.stopLoop()
            HandlerManager.removeCallbacks(autoHorizonScrollTask)
            HandlerManager.postOnUIThreadDelay(autoHorizonScrollTask, 500)
        }
    }


    override fun initComponentViewAfterInflated(view: View) {
        super.initComponentViewAfterInflated(view)
        findView()
    }

    private fun initViewPagerScrollProxy() {
        viewPager2 ?: return
        try {
            //获取到viewpager2里的RecyclerView
            val recyclerView: RecyclerView = viewPager2?.getChildAt(0) as RecyclerView
            //获取到RecyclerView里mLayoutManager
            val layoutManagerField: Field =
                ViewPager2::class.java.getDeclaredField("mLayoutManager")
            layoutManagerField.isAccessible = true
            val linearLayoutManager: LinearLayoutManager =
                layoutManagerField.get(viewPager2) as LinearLayoutManager
            val proxyLayoutManger = ProxyLayoutManger(context, linearLayoutManager)
            recyclerView.layoutManager = proxyLayoutManger
            val mRecyclerViewField =
                RecyclerView.LayoutManager::class.java.getDeclaredField("mRecyclerView")
            mRecyclerViewField.isAccessible = true
            mRecyclerViewField.set(linearLayoutManager, recyclerView)
            //反射更换mViewPager里的LinearLayoutManager
            layoutManagerField.set(viewPager2, proxyLayoutManger)
            //反射更换mPageTransformerAdapter里的LinearLayoutManager
            val pageTransformerAdapterField: Field =
                ViewPager2::class.java.getDeclaredField("mPageTransformerAdapter")
            pageTransformerAdapterField.isAccessible = true
            val mPageTransformerAdapter: Any? = pageTransformerAdapterField.get(viewPager2)
            if (mPageTransformerAdapter != null) {
                val aClass: Class<*> = mPageTransformerAdapter.javaClass
                val layoutManager: Field = aClass.getDeclaredField("mLayoutManager")
                layoutManager.isAccessible = true
                layoutManager.set(mPageTransformerAdapter, proxyLayoutManger)
            }
            //反射更换mScrollEventAdapter里的LinearLayoutManager
            val scrollEventAdapterField: Field =
                ViewPager2::class.java.getDeclaredField("mScrollEventAdapter")
            scrollEventAdapterField.isAccessible = true
            val mScrollEventAdapter: Any? = scrollEventAdapterField.get(viewPager2)
            if (mScrollEventAdapter != null) {
                val aClass: Class<*> = mScrollEventAdapter.javaClass
                val layoutManager: Field = aClass.getDeclaredField("mLayoutManager")
                layoutManager.isAccessible = true
                layoutManager.set(mScrollEventAdapter, proxyLayoutManger)
            }
        } catch (e: NoSuchFieldException) {
            e.printStackTrace()
        } catch (e: IllegalAccessException) {
            e.printStackTrace()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    inner class ProxyLayoutManger internal constructor(
        context: Context?, //这个是ViewPager2中的LinearLayoutManagerImpl对象
        private val linearLayoutManager: RecyclerView.LayoutManager
    ) : LinearLayoutManager(context) {

        override fun performAccessibilityAction(
            recycler: RecyclerView.Recycler,
            state: RecyclerView.State, action: Int, args: Bundle?
        ): Boolean {
            return linearLayoutManager.performAccessibilityAction(recycler, state, action, args)
        }

        override fun onInitializeAccessibilityNodeInfo(
            recycler: RecyclerView.Recycler,
            state: RecyclerView.State, info: AccessibilityNodeInfoCompat
        ) {
            linearLayoutManager.onInitializeAccessibilityNodeInfo(recycler, state, info)
        }

        override fun requestChildRectangleOnScreen(
            parent: RecyclerView,
            child: View, rect: Rect, immediate: Boolean,
            focusedChildVisible: Boolean
        ): Boolean {
            return linearLayoutManager.requestChildRectangleOnScreen(parent, child, rect, immediate)
        }

        //核心处理页面切换速度的方法
        override fun smoothScrollToPosition(
            recyclerView: RecyclerView,
            state: RecyclerView.State,
            position: Int
        ) {
            val linearSmoothScroller: LinearSmoothScroller =
                object : LinearSmoothScroller(recyclerView.context) {
                    override fun calculateTimeForDeceleration(dx: Int): Int {
                        return ((500 * (1 - 0.3356)).toInt())
                    }
                }
            linearSmoothScroller.targetPosition = position
            startSmoothScroll(linearSmoothScroller)
        }

        override fun calculateExtraLayoutSpace(
            state: RecyclerView.State,
            extraLayoutSpace: IntArray
        ) {

            val pageLimit: Int =
                viewPager2?.offscreenPageLimit ?: ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT
            if (pageLimit == ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT) {
                super.calculateExtraLayoutSpace(state, extraLayoutSpace)
                return
            }
            val offscreenSpace = pageSize * pageLimit
            extraLayoutSpace[0] = offscreenSpace
            extraLayoutSpace[1] = offscreenSpace
        }

        private val pageSize: Int
            get() {
                val rv = viewPager2?.getChildAt(0) as RecyclerView
                return if (orientation == RecyclerView.HORIZONTAL) rv.width - rv.paddingLeft - rv.paddingRight else rv.height - rv.paddingTop - rv.paddingBottom
            }

    }

    private class VideoRoomDelayLoadWishDataTask(private val taskComponentWeakReference: WeakReference<TaskComponent2?>?) :
        Runnable {
        override fun run() {
            if (taskComponentWeakReference?.get() == null || taskComponentWeakReference.get()
                    ?.canUpdateUi() != true
            ) {
                return
            }

            taskComponentWeakReference.get()?.queryHttp()
        }
    }

    private var mRoomDelayLoadWishDataTask: VideoRoomDelayLoadWishDataTask? = null
    override fun bindData(detail: PersonLiveDetail) {
        super.bindData(detail)
        if (liveMediaType == LiveMediaType.TYPE_VIDEO) {
            HandlerManager.removeCallbacks(mRoomDelayLoadWishDataTask)
            mRoomDelayLoadWishDataTask = VideoRoomDelayLoadWishDataTask(WeakReference(this))
            HandlerManager.postOnUIThreadDelay(
                mRoomDelayLoadWishDataTask,
                RoomProgressiveLoadingConstants.LOADING_HEADER_AREA_SECTION_THREE_DELAY_TIME_MS
            )
        } else {
            queryHttp()
        }
    }

    override fun onNetworkChanged(networkAvailable: Boolean, isWifi: Boolean) {
        super.onNetworkChanged(networkAvailable, isWifi)
        if (networkAvailable) queryHttp()
    }

    override fun onPause() {
        super.onPause()
        pauseAllLoopTask()
    }

    override fun onResume() {
        super.onResume()
        resumeAllLoopTask()
    }

    private fun queryHttp() {
        //观众传1，主播传2
        val userType = if (isFromHostFragment) 2 else 1
        CommonRequestForLive.requestAnchorTask(
            roomBizType,
            userType,
            hostUid,
            object : IDataCallBack<AnchorTaskWrapper?> {
                override fun onSuccess(`object`: AnchorTaskWrapper?) {
                    if (!canUpdateUi()) {
                        return
                    }
                    if (`object` != null) {
                        `object`.xmRequestId = getNewXmQuestId()
                    }
                    updateHttpData(`object`)
                }

                override fun onError(code: Int, message: String) {
                    if (!canUpdateUi()) {
                        return
                    }
                    Logger.d(TAG, "queryHttp error $code $message")
                    ViewStatusUtil.setVisible(View.GONE, viewPager2)
                    CustomToast.showDebugFailToast("queryHttp error $code $message")
                }
            })
    }

    override fun onReceivedAnchorTaskMessage(message: LiveAnchorTaskMessage?) {
        LamiaHelper.Log.i("live-test-长链76-AnchorTaskMessage", message.toString())
        if (message?.type == LiveAnchorTaskMessage.TYPE_ANCHOR_TASK) {
            handleAnchorTaskMsg(message)
        } else if (message?.type == LiveAnchorTaskMessage.TYPE_ANCHOR_PILOT) {
            handlePilotMsg(message)
        }
    }

    override fun onReceiveWishOrGiftWallProgressMsg(message: LiveAnchorTaskMessage?) {
        LamiaHelper.Log.i("live-test-长链96-WishOrGiftWallProgress", message.toString())
        message ?: return
        if (message.type == LiveAnchorTaskMessage.TYPE_ANCHOR_TASK
            || message.type == LiveAnchorTaskMessage.TYPE_ANCHOR_PILOT
        ) {
            //96消息 ，只处理心愿或者礼物墙
            return
        }
        handleWishMsg(message)
    }

    private fun handleAnchorTaskMsg(message: LiveAnchorTaskMessage) {
        Logger.e(TAG, "handleAnchorTaskMsg $message")
        if (isOfficialLive) {
            ViewStatusUtil.setVisible(View.GONE, viewPager2)
            return
        }
        ViewStatusUtil.setVisible(View.VISIBLE, viewPager2)
        val wrapper = AnchorTaskWrapper()
        wrapper.xmRequestId = getNewXmQuestId()

        if (message.anchorTaskVos.isEmpty()) {
            val anchorTask = findAnchorTaskModel()
            if (anchorTask != null) {
                val index = modelList.indexOf(anchorTask)
                modelList.remove(anchorTask)
                taskComponentAdapter.notifyItemRemoved(index)
            }
            val empty = findAnchorEmptyModel()
            if (empty != null) {
                val index = modelList.indexOf(empty)
                modelList.remove(empty)
                taskComponentAdapter.notifyItemRemoved(index)
            }
            return
        }

        val list = ArrayList<AnchorTask>()
        if (message.anchorTaskVos.size > 0) {
            for (tw in message.anchorTaskVos) {
                if (tw.progress < tw.goal) {
                    val task = AnchorTask()
                    task.goal = tw.goal
                    task.progress = tw.progress
                    task.icon = tw.icon
                    task.name = tw.name
                    task.unit = tw.unit
                    task.taskLevel = message.level
                    task.isShowLevel = tw.isShowLevel
                    task.shortName = tw.shortName
                    list.add(task)
                }
            }
        }
        wrapper.tasks = list
        wrapper.complete = message.complete
        wrapper.taskLevel = message.level
        wrapper.receivedTaskMessage = true
        message.url?.let { url ->
            wrapper.taskUrl = url
        }
        var anchorTask: RoomTaskEntranceModel? = findAnchorTaskModel()

        if (wrapper.tasks.isEmpty() || message.complete) {
            Logger.d(TAG, "handleAnchorTaskMsg complete")
            if (anchorTask != null) {
                modelList.indexOf(anchorTask).let {
                    modelList.removeAt(it)
                    taskComponentAdapter.notifyItemRemoved(it)
                }
            }
            var emptyTask = findAnchorEmptyModel()
            if (emptyTask == null) {
                emptyTask = RoomTaskEntranceModel().apply {
                    xmRequestId = currentHttpData?.xmRequestId ?: ""
                    type = LiveAnchorTaskMessage.TYPE_ANCHOR_TASK_EMPTY
                    localName = LiveAnchorTaskMessage.TYPE_ANCHOR_TASK_EMPTY_NAME
                    url = message.url ?: ""
                    tasks.add(AnchorTask().apply {

                    })
                }
                if (findPilotModel() == null) {
                    modelList.add(0, emptyTask)
                    taskComponentAdapter.notifyItemInserted(0)
                } else {
                    modelList.indexOf(findPilotModel()).let { index ->
                        modelList.add(index + 1, emptyTask)
                        taskComponentAdapter.notifyItemInserted(index + 1)
                    }
                }
            } else {
                emptyTask.url = message.url ?: ""
                taskComponentAdapter.notifyItemChanged(modelList.indexOf(emptyTask))
            }
        } else {
            val emptyTask = findAnchorEmptyModel()
            if (emptyTask != null) {
                modelList.indexOf(emptyTask).let {
                    modelList.removeAt(it)
                    taskComponentAdapter.notifyItemRemoved(it)
                }
            }
            if (anchorTask == null) {
                if (!isFromHostFragment) {
                    wrapper.tasks.clear()
                }
                val task = AnchorTask()
                task.taskLevel = message.level
                task.isShowLevel = true
                wrapper.tasks.add(task)

                anchorTask = RoomTaskEntranceModel()
                anchorTask.localName = LiveAnchorTaskMessage.TYPE_ANCHOR_TASK_NAME
                anchorTask.type = LiveAnchorTaskMessage.TYPE_ANCHOR_TASK
                anchorTask.tasks.addAll(wrapper.tasks)
                anchorTask.level = wrapper.taskLevel
                anchorTask.url = wrapper.taskUrl
                val pilot = findPilotModel()
                if (pilot == null) {
                    modelList.add(0, anchorTask)
                    taskComponentAdapter.notifyItemInserted(0)
                    Logger.d(TAG, "handleAnchorTaskMsg notifyItemInserted 0 ")
                } else {
                    val index = modelList.indexOf(pilot) + 1
                    modelList.add(index, anchorTask)
                    taskComponentAdapter.notifyItemChanged(index)
                    Logger.d(TAG, "handleAnchorTaskMsg notifyItemChanged index ")
                }
            } else {
                anchorTask.tasks.clear()
                if (!isFromHostFragment) {
                    wrapper.tasks.clear()
                }
                val task = AnchorTask()
                task.taskLevel = message.level
                task.isShowLevel = true
                wrapper.tasks.add(task)
                anchorTask.tasks.addAll(wrapper.tasks)
                anchorTask.level = wrapper.taskLevel
                anchorTask.url = wrapper.taskUrl
                taskComponentAdapter.notifyItemChanged(modelList.indexOf(anchorTask))
                Logger.d(TAG, "handleAnchorTaskMsg notifyItemChanged  ")
            }
        }

    }

    private fun findPilotModel(): RoomTaskEntranceModel? {
        return modelList.find { it.type == LiveAnchorTaskMessage.TYPE_ANCHOR_PILOT }
    }

    private fun findAnchorTaskModel(): RoomTaskEntranceModel? {
        return modelList.find { it.type == LiveAnchorTaskMessage.TYPE_ANCHOR_TASK }
    }

    private fun findWishModel(): RoomTaskEntranceModel? {
        return modelList.find { it.type == LiveAnchorTaskMessage.TYPE_ANCHOR_WISH }
    }

    private fun findAnchorEmptyModel(): RoomTaskEntranceModel? {
        return modelList.find { it.type == LiveAnchorTaskMessage.TYPE_ANCHOR_TASK_EMPTY }
    }

    private fun handlePilotMsg(message: LiveAnchorTaskMessage) {
        Logger.d(TAG, "handlePilotMsg $message")
        if (isOfficialLive) {
            ViewStatusUtil.setVisible(View.GONE, viewPager2)
            return
        }
        ViewStatusUtil.setVisible(View.VISIBLE, viewPager2)
        var pilot = findPilotModel()
        if (pilot == null) {
            pilot = RoomTaskEntranceModel()
            pilot.localName = "领航任务"
            pilot.url = currentHttpData?.navigationUrl ?: ""
            pilot.type = LiveAnchorTaskMessage.TYPE_ANCHOR_PILOT
            val task = AnchorTask().apply {
                taskLevel = message.navigationVo?.level ?: 0
                icon = message.navigationVo?.icon ?: ""
            }
            pilot.tasks.add(task)
            modelList.add(0, pilot)
            taskComponentAdapter.notifyItemInserted(0)
        } else {
            val index = modelList.indexOf(pilot)
            var oldTask = pilot.tasks.getOrNull(0)
            if (oldTask == null) {
                oldTask = AnchorTask().apply {
                    taskLevel = message.navigationVo?.level ?: 0
                    icon = message.navigationVo?.icon ?: ""
                }
                pilot.tasks.add(oldTask)
            } else {
                oldTask.taskLevel = message.navigationVo?.level ?: 0
                oldTask.icon = message.navigationVo?.icon ?: ""
            }
            taskComponentAdapter.notifyItemChanged(index)
        }
    }


    private fun handleWishMsg(message: LiveAnchorTaskMessage) {
        Logger.e(TAG, "handleWishMsg $message")
        if (isOfficialLive) {
            ViewStatusUtil.setVisible(View.GONE, viewPager2)
            return
        }
        ViewStatusUtil.setVisible(View.VISIBLE, viewPager2)
        if (message.anchorTaskVos.isEmpty()) {
            Logger.d(TAG, "handleWishMsg wish empty remove old")
            findWishModel()?.let {
                modelList.indexOf(it).let { index ->
                    Logger.d(TAG, "handleWishMsg wish empty remove index $index")
                    modelList.removeAt(index)
                    taskComponentAdapter.notifyItemRemoved(index)
                }
            }
            return
        }

        val wrapper = AnchorTaskWrapper()
        wrapper.xmRequestId = getNewXmQuestId()
        val list = ArrayList<AnchorTask>()
        if (message.anchorTaskVos.size > 0) {
            for (tw in message.anchorTaskVos) {
                val task = AnchorTask()
                task.goal = tw.goal
                task.progress = tw.progress
                task.icon = tw.icon
                task.name = tw.name
                task.unit = tw.unit
                task.taskLevel = message.level
                task.isShowLevel = tw.isShowLevel
                task.shortName = tw.shortName
                list.add(task)
            }
        }
        wrapper.receivedWishMessage = true
        if (!TextUtils.isEmpty(message.wishUrl)) {
            wrapper.wishUrl = message.wishUrl
        }
        if (!TextUtils.isEmpty(message.wallUrl)) {
            wrapper.wallUrl = message.wallUrl
        }

        var wishModel: RoomTaskEntranceModel? = findWishModel()
        if (wishModel == null) {
            wishModel = RoomTaskEntranceModel()
            wishModel.type = LiveAnchorTaskMessage.TYPE_ANCHOR_WISH
            wishModel.localName = LiveAnchorTaskMessage.TYPE_ANCHOR_WISH_NAME
            wishModel.tasks.addAll(list)
            wishModel.level = wrapper.taskLevel
            wishModel.url = wrapper.taskUrl
            modelList.add(wishModel)
            taskComponentAdapter.notifyItemInserted(modelList.size - 1)
        } else {
            wishModel.tasks.clear()
            wishModel.tasks.addAll(list)
            wishModel.level = wrapper.taskLevel
            wishModel.url = wrapper.wishUrl
            taskComponentAdapter.notifyItemChanged(modelList.indexOf(wishModel))
        }
    }

    private fun updateHttpData(httpData: AnchorTaskWrapper?) {
        Logger.d(TAG, "updateHttpData $httpData")
        httpData ?: return
        if (isOfficialLive) {
            ViewStatusUtil.setVisible(View.GONE, viewPager2)
            return
        }
        ViewStatusUtil.setVisible(View.VISIBLE, viewPager2)
        val level = httpData.navigationVo?.level ?: -1
        if (level >= 0) {
            Logger.e(TAG, "有领航任务 ${httpData.wishes}")
            val pilotMode = findPilotModel()
            if (pilotMode == null) {
                val pilot = RoomTaskEntranceModel()
                pilot.xmRequestId = httpData.xmRequestId
                pilot.type = LiveAnchorTaskMessage.TYPE_ANCHOR_PILOT
                pilot.localName = LiveAnchorTaskMessage.TYPE_ANCHOR_PILOT_NAME
                pilot.url = httpData.navigationUrl ?: ""
                val anchorTask = AnchorTask()
                anchorTask.taskLevel = httpData.navigationVo?.level ?: 0
                anchorTask.icon = httpData.navigationVo?.icon ?: ""
                pilot.tasks.add(anchorTask)
                modelList.add(pilot)
                taskComponentAdapter.notifyItemInserted(modelList.size - 1)
            } else {
                pilotMode.tasks.clear()
                val anchorTask = AnchorTask()
                anchorTask.taskLevel = httpData.navigationVo?.level ?: 0
                anchorTask.icon = httpData.navigationVo?.icon ?: ""
                pilotMode.tasks.add(anchorTask)
                taskComponentAdapter.notifyItemChanged(modelList.indexOf(pilotMode))
            }
        }else{
            val model = findPilotModel()
            if(model != null ){
                val index = modelList.indexOf(model)
                modelList.remove(model)
                taskComponentAdapter.notifyItemRemoved(index)
            }
        }
        if (httpData.tasks.isNotEmpty()) {
            Logger.e(TAG, "有主播任务 ${httpData.tasks}")
            val newUndoTaskList: MutableList<AnchorTask> = mutableListOf()
            val tasks: List<AnchorTask> = httpData.tasks
            for (task in tasks) {
                if (task.goal > task.progress) {
                    //取出未完成任务
                    newUndoTaskList.add(task)
                }
            }
            if (RoomDebugConfigure.getBoolFromGroup("主播任务完成", "audie")) {
                httpData.complete = true
            }
            Logger.d(TAG, "未完成的主播任务 $newUndoTaskList")
            if (newUndoTaskList.isEmpty() || httpData.complete) {
                val anchorTask = findAnchorTaskModel()
                if (anchorTask != null) {
                    val index = modelList.indexOf(anchorTask)
                    modelList.remove(anchorTask)
                    taskComponentAdapter.notifyItemRemoved(index)
                }
                var empty = findAnchorEmptyModel()
                if (empty == null) {
                    empty = RoomTaskEntranceModel().apply {
                        xmRequestId = httpData.xmRequestId
                        type = LiveAnchorTaskMessage.TYPE_ANCHOR_TASK_EMPTY
                        localName = LiveAnchorTaskMessage.TYPE_ANCHOR_TASK_EMPTY_NAME
                        url = httpData.taskUrl
                        this.tasks.add(AnchorTask().apply {

                        })
                    }
                    val indexPilot = modelList.indexOf(findPilotModel())
                    if (indexPilot < 0) {
                        modelList.add(0, empty)
                        taskComponentAdapter.notifyItemInserted(0)
                    } else {
                        modelList.add(indexPilot + 1, empty)
                        taskComponentAdapter.notifyItemChanged(indexPilot + 1)
                    }
                }
            } else {
                if (!isFromHostFragment) {
                    //观众端把任务清掉，只展示等级。
                    //主播端全都展示
                    newUndoTaskList.clear()
                }
                //创建一个等级bean
                val task = AnchorTask()
                task.taskLevel = httpData.taskLevel
                task.isShowLevel = true
                newUndoTaskList.add(task)
                var anchorTask = findAnchorTaskModel()
                if (anchorTask == null) {
                    findAnchorEmptyModel()?.let {
                        modelList.indexOf(it).let { index ->
                            modelList.remove(it)
                            taskComponentAdapter.notifyItemRemoved(index)
                        }
                    }
                    anchorTask = RoomTaskEntranceModel()
                    anchorTask.xmRequestId = httpData.xmRequestId
                    anchorTask.localName = LiveAnchorTaskMessage.TYPE_ANCHOR_TASK_NAME
                    anchorTask.type = LiveAnchorTaskMessage.TYPE_ANCHOR_TASK
                    anchorTask.url = httpData.taskUrl
                    anchorTask.tasks.addAll(newUndoTaskList)
                    val indexPilot = modelList.indexOf(findPilotModel())
                    if (indexPilot < 0) {
                        modelList.add(0, anchorTask)
                        taskComponentAdapter.notifyItemInserted(0)
                    } else {
                        modelList.add(indexPilot + 1, anchorTask)
                        taskComponentAdapter.notifyItemChanged(indexPilot + 1)
                    }
                } else {
                    anchorTask.tasks.clear()
                    anchorTask.tasks.addAll(newUndoTaskList)
                    taskComponentAdapter.notifyItemChanged(modelList.indexOf(anchorTask))
                }
            }
        } else {
            val anchorTask = findAnchorTaskModel()
            if (anchorTask != null) {
                val index = modelList.indexOf(anchorTask)
                modelList.remove(anchorTask)
                taskComponentAdapter.notifyItemRemoved(index)
            }
            val empty = findAnchorEmptyModel()
            if (empty != null) {
                val index = modelList.indexOf(empty)
                modelList.remove(empty)
                taskComponentAdapter.notifyItemRemoved(index)
            }
        }
        if (httpData.wishes.isEmpty()) {
            Logger.d(TAG, "无有心愿单 ${httpData.wishes}")
            findWishModel()?.let {
                modelList.indexOf(it).let { index ->
                    modelList.remove(it)
                    taskComponentAdapter.notifyItemRemoved(index)
                }
            }
        } else {
            Logger.e(TAG, "有心愿单 ${httpData.wishes}")
            var wish = findWishModel()
            if (wish == null) {
                wish = RoomTaskEntranceModel()
                wish.xmRequestId = httpData.xmRequestId
                wish.localName = LiveAnchorTaskMessage.TYPE_ANCHOR_WISH_NAME
                wish.type = LiveAnchorTaskMessage.TYPE_ANCHOR_WISH
                wish.tasks.addAll(httpData.wishes)
                wish.url = httpData.wishUrl
                modelList.add(wish)
                taskComponentAdapter.notifyItemInserted(modelList.size - 1)
            } else {
                wish.tasks.clear()
                wish.tasks.addAll(httpData.wishes)
                taskComponentAdapter.notifyItemChanged(modelList.indexOf(wish))
            }
        }
        this.currentHttpData = httpData
    }

    override fun onDestroy() {
        super.onDestroy()
        pauseAllLoopTask()
        viewPager2?.unregisterOnPageChangeCallback(viewPage2Change)
    }

    private fun findView() {
        viewPager2 = findViewById(R.id.live_banner_vp2)
        viewPager2?.offscreenPageLimit = 3
        viewPager2?.registerOnPageChangeCallback(viewPage2Change)
        initViewPagerScrollProxy()
        viewPager2?.orientation = ViewPager2.ORIENTATION_HORIZONTAL
        viewPager2?.adapter = taskComponentAdapter
    }

    override fun officialLiveStart() {
        super.officialLiveStart()
        ViewStatusUtil.setVisible(View.INVISIBLE, viewPager2)
        pauseAllLoopTask()
    }

    override fun onOrientationChange(orientation: Int, isSameOrientation: Boolean) {
        super.onOrientationChange(orientation, isSameOrientation)
        ViewStatusUtil.setVisible(if (isFull) View.GONE else View.VISIBLE, viewPager2)
    }

    /**
     * 官方直播间结束
     */
    override fun officialLiveEnd() {
        super.officialLiveEnd()
        if (modelList.size > 0) {
            ViewStatusUtil.setVisible(View.VISIBLE, viewPager2)
            resumeAllLoopTask()
        }
        queryHttp()
    }

    private fun pauseAllLoopTask() {
        Logger.e(TAG, "pauseAllLoopTask")
        HandlerManager.removeCallbacks(autoHorizonScrollTask)
        val recyclerView = viewPager2?.getChildAt(0) as? RecyclerView ?:return
        for (i in 0 until recyclerView.childCount) {
            val child = recyclerView.getChildAt(i)
            Logger.e(TAG, "pauseAllLoopTask 第 $i 个 $child")
            (child as? TaskVerticalView)?.let {
                Logger.d(
                    TAG,
                    "viewPage2Change onPageSelected it.dataPosition $modelList  ${child.mode}"
                )
                child.stopLoop()
            }
        }
    }

    private fun resumeAllLoopTask() {
        Logger.e(TAG, "resumeAllLoopTask")
        viewPager2 ?: return
        currentBannerView?.mode?.let { mode ->
            viewPager2?.currentItem?.let { currentItem ->
                if (mode == modelList.getOrNull(currentItem)) {
                    currentBannerView?.startLoop()
                }
            }
        }
    }


    override fun onSwitchRoom(newRoomId: Long, newArgs: Bundle) {
        super.onSwitchRoom(newRoomId, newArgs)
        LamiaHelper.Log.i("live-test-主播任务" + "onSwitchRoom：：mTaskList.clear()   mWishOrGiftWallList.clear()")
        pauseAllLoopTask()
        if (modelList.isNotEmpty()) {
            modelList.clear()
            taskComponentAdapter.notifyDataSetChanged()
        }
        currentHttpData = null
    }
}
