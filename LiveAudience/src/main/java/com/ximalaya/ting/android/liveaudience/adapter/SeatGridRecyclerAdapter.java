package com.ximalaya.ting.android.liveaudience.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.lib.avatarcache.ChatUserAvatarCache;
import com.ximalaya.ting.android.live.common.lib.micemotion.SvgGifAnimView;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.common.view.widget.LiveSoundWaveView;
import com.ximalaya.ting.android.live.host.view.seat.SeatGiftView;
import com.ximalaya.ting.android.liveaudience.data.model.friends.SeatStateModel;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveMicUser;
import com.ximalaya.ting.android.liveaudience.friends.LoveModeLogicHelper;
import com.ximalaya.ting.android.liveaudience.manager.love.LoveModeUIManager;

import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 交友模式中的顶部座位 GridView 的 适配器
 *
 * <AUTHOR>
 * @since 26/04/2018.
 */
public class SeatGridRecyclerAdapter extends RecyclerView.Adapter<SeatGridRecyclerAdapter.SeatUsedViewHolder> {
    public static final int TYPE_NOBODY = 1;    //麦上没人
    public static final int TYPE_HAS_PEOPLE = 2; //麦上有人
    private static final float PK_MODE_ITEM_MARGIN = 14;

    //左边战队的座位编号：0 1 4 5
    private final List<Integer> mPkModeLeftPositionList = Arrays.asList(0, 1, 4, 5);
    //左边战队中间的座位号
    private final List<Integer> mPkModeLeftCenterList = Arrays.asList(1, 5);
    //右边战队中间的座位号
    private final List<Integer> mPkModeRightCenterList = Arrays.asList(2, 6);

    static class ViewHolderCache {
        final ArrayMap<Integer, SeatUsedViewHolder> mViewHolderHeap = new ArrayMap<>();
        public int size() {
            return mViewHolderHeap.size();
        }

        public SeatUsedViewHolder get(int position) {
            return mViewHolderHeap.get(position);
        }

        public void put(int position, SeatUsedViewHolder holder) {
            mViewHolderHeap.put(position, holder);
        }
    }

    /**
     * 保存不同模式下，座位的 ViewHolder，方便复用
     */
    SparseArray<ViewHolderCache> mViewHolderCacheForDiffMode = new SparseArray<>();

    private Context mContext;
    private List<SeatStateModel> mData = new ArrayList<>();
    private LayoutInflater mInflater;
    private OnSeatClickListener mOnSeatClickListener;
    private NumberFormat mNumberFormat;
    private Drawable mCharmValuePositiveDrawable, mCharmValueNegativeDrawable;

    //当前模式，有：普通交友、团战交友
    private int mMode = -1;

    //显示每个人得到的喜爱值的条件：1.普通模式 2.团战交友模式下，主播端
    private boolean isAnchor;

    public SeatGridRecyclerAdapter(Context context, boolean anchor) {
        mContext = context;
        isAnchor = anchor;
        mInflater = LayoutInflater.from(context);
        mNumberFormat = new DecimalFormat("##0.##");
        mNumberFormat.setRoundingMode(RoundingMode.HALF_UP);
    }

    public SeatGridRecyclerAdapter setData(List<SeatStateModel> data) {
        this.mData = data;

        updateMode(LoveModeUIManager.getInstance().getMode());
        return this;
    }

    private void updateMode(int mode) {
        if (mode == mMode) {
            notifyDataChangedInternal();
            return;
        }

        this.mMode = mode;

        // 切换模式后清除 viewHolder
        mViewHolderCacheForDiffMode.put(mMode, null);

        notifyDataSetChanged();
    }

    public void notifyDataChangedInternal() {
        ViewHolderCache viewHolderCache = getCurrentModeViewHolderCache();

        if (viewHolderCache.size() < 8) {
            notifyDataSetChanged();
        } else {
            notifyDataSetChanged2();
        }
    }

    private ViewHolderCache getCurrentModeViewHolderCache() {
        ViewHolderCache viewHolderCache = mViewHolderCacheForDiffMode.get(mMode);
        if (viewHolderCache == null) {
            viewHolderCache = new ViewHolderCache();
            mViewHolderCacheForDiffMode.put(mMode, viewHolderCache);
        }
        return viewHolderCache;
    }

    private void notifyDataSetChanged2() {

        ViewHolderCache viewHolderCache = getCurrentModeViewHolderCache();

        int position = 0;
        for (SeatStateModel model : mData) {
            SeatUsedViewHolder holder = viewHolderCache.get(position);
            if (holder != null) {
                bindData(holder, position, model);
            }
            position++;
        }
    }

    public SeatGridRecyclerAdapter setOnSeatClickListener(OnSeatClickListener onSeatClickListener) {
        mOnSeatClickListener = onSeatClickListener;
        return this;
    }

    @Override
    public int getItemViewType(int position) {
        return mMode;
    }

    @NonNull
    @Override
    public SeatUsedViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        log("onCreateViewHolder " + mMode);

        parent.setClipChildren(false);
        View view = mInflater.inflate(getLayoutIdByMode(), parent, false);
        return new SeatUsedViewHolder(view, LoveModeLogicHelper.getContextWithCheck(mContext));
    }

    /**
     * 不同模式下，使用不同的 item 布局，里面布局大小、内容有所不同
     *
     * @return
     */
    @LayoutRes
    private int getLayoutIdByMode() {
        log("getLayoutIdByMode: " + mMode);

        if (isPkMode()) {
            return R.layout.liveaudience_item_friends_pk_mode_chair;
        } else if (isMarryMode()){
            return R.layout.liveaudience_item_friends_marry_mode_chair;
        } else {
            return R.layout.liveaudience_item_friends_normal_mode_chair;
        }
    }

    /**
     * 使用 Payload 提高 RecyclerView 渲染效率
     * <p>
     * Read more: http://blog.chengyunfeng.com/?p=1007#ixzz5DlvP3D39
     *
     * @param holder
     * @param position
     * @param payloads
     */
    @Override
    public void onBindViewHolder(SeatUsedViewHolder holder, int position, List<Object> payloads) {
        if (payloads == null || payloads.isEmpty()) {   //更新全部布局
            onBindViewHolder(holder, position);
        } else { //payloads 不为空，只更新部分 view

        }
    }

    @Override
    public void onBindViewHolder(SeatUsedViewHolder holder, final int position) {
        if (holder == null) {
            return;
        }

        ViewHolderCache viewHolderCache = getCurrentModeViewHolderCache();
        if (viewHolderCache.size() >= 8) {
            return;
        }

        if (isPkMode()) {
            changeUIForFriendsPkMode(holder, position);
        }

        if (isMarryMode()) {
            changeUIForMarryMode(holder, position);
        }

        viewHolderCache.put(position, holder);

        SeatStateModel chairStateModel = getItemWithIndexCheck(position);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) return;
                LoveModeLogicHelper.clickLog("onClick: " + position);

                if (mOnSeatClickListener != null) {
                    mOnSeatClickListener.onSeatClick(getItemWithIndexCheck(position));
                }
            }
        });

        holder.itemView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                LoveModeLogicHelper.clickLog("onLongClick: " + position);
                if (mOnSeatClickListener != null) {
                    mOnSeatClickListener.onSeatLongClick(getItemWithIndexCheck(position));
                }
                return true;
            }

        });
        long uid = chairStateModel != null && chairStateModel.mOnlineUser != null ? chairStateModel.mOnlineUser.mUid : 0;
        AutoTraceHelper.bindData(holder.itemView, AutoTraceHelper.MODULE_DEFAULT, uid);
        bindData(holder, position, chairStateModel);
    }

    private void changeUIForFriendsPkMode(SeatUsedViewHolder holder, int position) {
        if (holder == null) {
            return;
        }
        //团战模式下，需要调整中间四个位置的外边距
        changeMarginByPosition(position, holder);
        //团战模式下，需要区分左右战队的 border
        holder.mSeatIv.setBackgroundResource(getBorderBackgroundByTeam(position));
    }

    private void changeMarginByPosition(int position, SeatUsedViewHolder holder) {
        if (holder == null || holder.itemView == null) {
            return;
        }

        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(holder.itemView.getLayoutParams());
        if (mPkModeLeftCenterList.contains(position)) {
            layoutParams.rightMargin = BaseUtil.dp2px(mContext, PK_MODE_ITEM_MARGIN);
            holder.itemView.setLayoutParams(layoutParams);
            return;
        }

        if (mPkModeRightCenterList.contains(position)) {
            layoutParams.leftMargin = BaseUtil.dp2px(mContext, PK_MODE_ITEM_MARGIN);
            holder.itemView.setLayoutParams(layoutParams);
            return;
        }

        layoutParams.rightMargin = 0;
        layoutParams.leftMargin = 0;
        holder.itemView.setLayoutParams(layoutParams);
    }

    private void bindData(SeatUsedViewHolder holder, final int position, SeatStateModel chairStateModel) {
        log("onBindViewHolder ++++++++++++++++++ " + chairStateModel);
        if (chairStateModel == null || chairStateModel.isNobody()) {
            bindNobodyData(holder, position, chairStateModel);
        } else {
            bindMicUserData(holder, position, chairStateModel);
        }
    }

    /**
     * 绑定有人麦位的状态，包括静音、发言、礼物等
     *
     * @param viewHolder
     * @param position
     */
    private void bindMicUserData(SeatUsedViewHolder viewHolder, int position, SeatStateModel seatStateModel) {

        UIStateUtil.showViewsIfTrue(position == 0, viewHolder.mDecorateForMarryGuest);
        if (viewHolder.mDecorateForMarryGuest != null) {
            // 非诚勿扰模式下，中间的嘉宾因为有框，音浪初始大小需要调整，不然看不见
            viewHolder.mSoundWaveView.getDefaultWaveView().setAvatarSize(position == 0 ? 60F : 50F);
        }

        UIStateUtil.showViews(viewHolder.mSVGAView, viewHolder.mMicEmotionAnimView);
        long onlineUserUid = 0;
        if (seatStateModel != null) {
            onlineUserUid = seatStateModel.getOnlineUserUid();
        }
        viewHolder.mSVGAView.setSeat(onlineUserUid);
        if (seatStateModel == null) {
            UIStateUtil.hideViews(viewHolder.mPKMVPView);
            viewHolder.mMicEmotionAnimView.setCurrentUid(-1L);
            return;
        }
        viewHolder.mMicEmotionAnimView.setCurrentUid(seatStateModel.getOnlineUserUid());

        CommonLoveMicUser onlineUser = seatStateModel.mOnlineUser;
        UIStateUtil.showViewsIfTrue(seatStateModel.isMute, viewHolder.mMuteIv);
        UIStateUtil.showViewsIfTrue(!seatStateModel.isMute, viewHolder.mSoundWaveView);

        boolean showSelectLove = isNormalMode() || isLoveMode();

        //普通交友下才显示选择中、选择中的爱心
        UIStateUtil.showViewsIfTrue(
                showSelectLove && onlineUser != null && (onlineUser.isSelecting || seatStateModel.isSelected),
                viewHolder.mBeSelectedLoverIv, viewHolder.mLoveSelectedBorderIv
        );
        UIStateUtil.showViewsIfTrue(showSelectLove && onlineUser != null && onlineUser.isSelecting,
                viewHolder.mSelectingShadowView, viewHolder.mSelectingTv);

        boolean pkMode = isPkMode();
        UIStateUtil.showViewsIfTrue(pkMode && seatStateModel.isPkMVP, viewHolder.mPKMVPView);

        //显示每个人得到的喜爱值的条件：(前置条件，非 Pia 戏模式) 1.普通模式 或者 2.团战交友模式下，主播端
        boolean showCharmValue = (!pkMode || isAnchor) && !isPiaMode();
        UIStateUtil.showViewsIfTrueElseInvisible(showCharmValue, viewHolder.mTotalCharmValueTv);

        if (onlineUser == null) return;

        viewHolder.mBottomDescTv.setText(LoveModeLogicHelper.getFormatName(onlineUser.mNickname));

        // 防止头像闪烁
        if (!viewHolder.isSameUser(seatStateModel)) {
            // 用户有生日、周年纪念等头像 (文字头像) 玩法，防止头像缓存导致不刷新，这里不使用本地缓存，重新请求
            ChatUserAvatarCache.self().displayImage(
                    viewHolder.mSeatIv,
                    onlineUser.mUid,
                    com.ximalaya.ting.android.live.host.R.drawable.live_img_friends_user_no_head,
                    false
            );
        }

        if (showCharmValue) {
            long totalCharmValue = seatStateModel.getTotalCharmValue();
            viewHolder.mTotalCharmValueTv.setText(getFormatValue(totalCharmValue));
            changeCharmDrawable(viewHolder.mTotalCharmValueTv, totalCharmValue);
        }

        updateMicWave(viewHolder, seatStateModel);

        viewHolder.setSeatStateModel(seatStateModel);
    }

    /**
     * 更新音浪
     */
    private void updateMicWave(SeatUsedViewHolder holder, SeatStateModel seatStateModel) {
        // 闭麦情况下视图不可见
        if (seatStateModel.isMute) return;

        boolean isTalking = seatStateModel.isSpeaking;

        // todo(zoeywoohoo): 2022/5/19 本期版本 9.0.39 因涉及面较多，风险评估后决定个播暂时不上麦位波需求，后期可增加模板字段快速接入
        boolean supportSoundWaveFeature = false;
        if (supportSoundWaveFeature && holder.mSoundWaveView != null) {
            // todo(zoeywoohoo): 2022/5/18 获取交友模式麦位波模板资源字段
            int micWaveResId = seatStateModel.mMicNumber;
            holder.mSoundWaveView.setCustomWave(micWaveResId);
        }

        if (isTalking) {
            startWaving(holder);
        } else {
            stopWaving(holder);
        }
    }

    private void startWaving(SeatUsedViewHolder holder) {
        if (holder.mSoundWaveView != null) {
            holder.mSoundWaveView.start();
        }
    }

    private void stopWaving(SeatUsedViewHolder holder) {
        if (holder.mSoundWaveView != null) {
            holder.mSoundWaveView.stop();
        }
    }

    /**
     * 非诚勿扰模式下，第一个位置是嘉宾，头像什么的会大一些
     * 写错了，应该在 getItemType 里做区分的，在这里写不好维护，后面得改一下
     * @param viewHolder
     * @param position
     */
    private void changeUIForMarryMode(SeatUsedViewHolder viewHolder, int position) {
        ViewGroup.LayoutParams layoutParams = viewHolder.itemView.getLayoutParams();
        ViewGroup.LayoutParams avatarParentLp = viewHolder.mAvatarParentView.getLayoutParams();
        RelativeLayout.LayoutParams defaultSoundWaveLp = (RelativeLayout.LayoutParams) viewHolder.mSoundWaveView.getLayoutParams();

        if (position == 0) {
            layoutParams.width = BaseUtil.dp2px(mContext, 90);
            layoutParams.height = BaseUtil.dp2px(mContext, 102);

            avatarParentLp.width = BaseUtil.dp2px(mContext, 70);
            avatarParentLp.height = BaseUtil.dp2px(mContext, 70);
            if (avatarParentLp instanceof ViewGroup.MarginLayoutParams) {
                ((ViewGroup.MarginLayoutParams)avatarParentLp).topMargin = BaseUtil.dp2px(mContext, 14);
            }
            defaultSoundWaveLp.width = layoutParams.width;
            defaultSoundWaveLp.height = layoutParams.width;
        } else {
            layoutParams.width = BaseUtil.dp2px(mContext, 80);
            layoutParams.height = BaseUtil.dp2px(mContext, 92);

            avatarParentLp.width = BaseUtil.dp2px(mContext, 60);
            avatarParentLp.height = BaseUtil.dp2px(mContext, 60);
            if (avatarParentLp instanceof ViewGroup.MarginLayoutParams) {
                ((ViewGroup.MarginLayoutParams)avatarParentLp).topMargin = 0;
            }

            defaultSoundWaveLp.width = avatarParentLp.width;
            defaultSoundWaveLp.height = avatarParentLp.height;
            defaultSoundWaveLp.topMargin = 0;
        }

        viewHolder.itemView.setLayoutParams(layoutParams);
        viewHolder.mAvatarParentView.setLayoutParams(avatarParentLp);
        viewHolder.mSoundWaveView.setLayoutParams(defaultSoundWaveLp);
    }

    private void changeCharmDrawable(TextView textView, long totalCharmValue) {
        if (textView == null || mContext == null) {
            CustomToast.showDebugFailToast("changeCharmDrawable failed!");
            return;
        }
        if (mCharmValuePositiveDrawable == null) {
            mCharmValuePositiveDrawable = mContext.getResources().getDrawable(com.ximalaya.ting.android.live.host.R.drawable.live_img_friends_charm_positive);
            mCharmValuePositiveDrawable.setBounds(0, 0, getCharmBound(), getCharmBound());
        }
        if (mCharmValueNegativeDrawable == null) {
            mCharmValueNegativeDrawable = mContext.getResources().getDrawable(com.ximalaya.ting.android.live.host.R.drawable.live_img_friends_charm_negative);
            mCharmValueNegativeDrawable.setBounds(0, 0, getCharmBound(), getCharmBound());
        }
        Drawable drawable = totalCharmValue >= 0 ? mCharmValuePositiveDrawable : mCharmValueNegativeDrawable;
        textView.setCompoundDrawables(drawable, null, null, null);
    }

    private void log(String s) {
        LiveHelper.Log.logWithClassName(SeatGridRecyclerAdapter.class, s);
    }

    private String getFormatValue(long charmValue) {
        if (charmValue > 9999 || charmValue < -9999) {
            return mNumberFormat.format(charmValue * 1.0 / 10000) + "w";
        }
        return String.valueOf(charmValue);
    }

    private int mCharmValueBound;

    private int getCharmBound() {
        if (mCharmValueBound == 0) {
            mCharmValueBound = BaseUtil.dp2px(mContext, 15);
        }
        return mCharmValueBound;
    }

    /**
     * 绑定无人麦位的状态，主要是空闲和锁定
     *
     * @param holder
     * @param position
     */
    private void bindNobodyData(final SeatUsedViewHolder holder, int position, SeatStateModel seatStateModel) {
        UIStateUtil.showViewsIfTrue(position == 0, holder.mDecorateForMarryGuest);
        UIStateUtil.hideViews(
                holder.mMuteIv, holder.mTotalCharmValueTv, holder.mSoundWaveView,
                holder.mSelectingShadowView, holder.mSelectingTv,
                holder.mBeSelectedLoverIv, holder.mLoveSelectedBorderIv, holder.mSVGAView,
                holder.mPKMVPView, holder.mMicEmotionAnimView
        );

        holder.mSVGAView.setSeat(0);
        holder.mMicEmotionAnimView.setCurrentUid(-1L);

        if (seatStateModel == null || !seatStateModel.isLocked) {
            holder.mSeatIv.setImageResource(com.ximalaya.ting.android.live.host.R.drawable.live_img_seat_empty);
        } else {
            holder.mSeatIv.setImageResource(com.ximalaya.ting.android.live.host.R.drawable.live_img_seat_lock);
        }

        holder.mBottomDescTv.setText(
                LoveModeLogicHelper.getFormatName((seatStateModel != null ? seatStateModel.mMicNumber : position + 1) + "号位")
        );

        holder.setSeatStateModel(seatStateModel);
    }

    private int getBorderBackgroundByTeam(int position) {
        if (isLeftTeam(position)) {
            return com.ximalaya.ting.android.live.common.R.drawable.live_bg_friends_pk_border_left;
        }
        return com.ximalaya.ting.android.live.common.R.drawable.live_bg_friends_pk_border_right;
    }

    //是否为普通交友 模式
    private boolean isNormalMode() {
        return LoveModeUIManager.Mode.isNormalMode(mMode);
    }

    private boolean isLoveMode() {
        return LoveModeUIManager.Mode.isLoveMode(mMode);
    }

    private boolean isPkMode() {
        return LoveModeUIManager.Mode.isPKMode(mMode);
    }

    private boolean isMarryMode() {
        return LoveModeUIManager.Mode.isMarryMode(mMode);
    }

    private boolean isPiaMode() {
        return LoveModeUIManager.Mode.isPia(mMode);
    }

    //是否为左边的队伍
    private boolean isLeftTeam(int position) {
        return mPkModeLeftPositionList.contains(position);
    }

    @Override
    public int getItemCount() {
        return 8;
    }

    public static class SeatUsedViewHolder extends RecyclerView.ViewHolder {
        SeatStateModel mSeatStateModel;

        View mAvatarParentView;
        TextView mBottomDescTv;
        ImageView mSeatIv;
        SeatGiftView mSVGAView;
        SvgGifAnimView mMicEmotionAnimView;

        LiveSoundWaveView mSoundWaveView;

        ImageView mMuteIv;
        ImageView mBeSelectedLoverIv, mLoveSelectedBorderIv;   //心动时刻被选中
        TextView mTotalCharmValueTv;
        View mSelectingShadowView;
        TextView mSelectingTv;

        View mPKMVPView;

        /**
         * 非诚勿扰模式嘉宾的装饰
         */
        View mDecorateForMarryGuest;

        private boolean isSameUser(SeatStateModel model) {
            return model != null && mSeatStateModel != null && model.getOnlineUserUid() == mSeatStateModel.getOnlineUserUid();
        }

        public void setSeatStateModel(SeatStateModel seatStateModel) {
            mSeatStateModel = seatStateModel;
        }

        public SeatUsedViewHolder(View itemView, Context context) {
            super(itemView);

            mAvatarParentView = itemView.findViewById(R.id.live_seat_top);
            mBottomDescTv = itemView.findViewById(R.id.live_seat_name_tv);
            mSeatIv = itemView.findViewById(R.id.live_seat_iv);
            mSVGAView = itemView.findViewById(R.id.live_seat_svg_view);
            mMicEmotionAnimView = itemView.findViewById(R.id.live_seat_emotion_view);
            mSoundWaveView = itemView.findViewById(R.id.live_seat_sound_wave);
            mMuteIv = itemView.findViewById(R.id.live_seat_mute_iv);
            mBeSelectedLoverIv = itemView.findViewById(R.id.live_seat_selected_lover_iv);
            mLoveSelectedBorderIv = itemView.findViewById(R.id.live_seat_selected_border);
            mTotalCharmValueTv = itemView.findViewById(R.id.live_seat_gift_count);
            mSelectingShadowView = itemView.findViewById(R.id.live_seat_selecting_shadow);
            mDecorateForMarryGuest = itemView.findViewById(R.id.live_decorate_for_marry_guest);

            mSelectingTv = itemView.findViewById(R.id.live_seat_selecting_tv);

            mPKMVPView = itemView.findViewById(R.id.live_seat_pk_mvp);
        }
    }

    public interface OnSeatClickListener {
        void onSeatClick(SeatStateModel stateModel);

        void onSeatLongClick(SeatStateModel stateModel);
    }

    //避免 IndexOutOfBoxException
    private SeatStateModel getItemWithIndexCheck(int position) {
        if (mData == null) {
            return null;
        }
        return position >= mData.size() ? null : mData.get(position);
    }

    public int getMode() {
        return mMode;
    }
}
