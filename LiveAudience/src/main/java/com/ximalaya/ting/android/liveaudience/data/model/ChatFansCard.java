package com.ximalaya.ting.android.liveaudience.data.model;

import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatFansCard;

/**
 * 粉丝团.
 *
 * <AUTHOR>
 */

public class ChatFansCard {
    public int level;
    public String name;

    public ChatFansCard(int level, String name) {
        this.level = level;
        this.name = name;
    }

    public ChatFansCard() {
    }

    public static ChatFansCard parse(LiveUserInfo.FansClubVoBean fansClubVo) {
        if (fansClubVo == null) return null;

        return new ChatFansCard(fansClubVo.getFansGrade(), fansClubVo.getClubName());
    }

    public static ChatFansCard parse(CommonChatFansCard fansCard) {
        if (fansCard == null) return null;

        return new ChatFansCard(fansCard.mLevel, fansCard.mName);
    }
}
