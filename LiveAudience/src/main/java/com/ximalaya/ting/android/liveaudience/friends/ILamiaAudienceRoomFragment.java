package com.ximalaya.ting.android.liveaudience.friends;

import com.ximalaya.ting.android.live.common.lib.gift.panel.SendGiftDialog;
import com.ximalaya.ting.android.live.lib.stream.IStreamManager;
import com.ximalaya.ting.android.liveaudience.data.model.friends.SeatStateModel;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatGiftMessage;

import java.util.List;

import javax.annotation.Nullable;

/**
 * 观众侧房间接口类.
 *
 * <AUTHOR>
 */
public interface ILamiaAudienceRoomFragment extends ILamiaRoomFragment {

    SendGiftDialog getGiftDialogByMode(int mode, long micUid);

    @Nullable IStreamManager getStreamManager();

    boolean isAnchorVisitor();

    void hideAllDialog();
    /**
     * 仅关闭交友模式相关的dialog
     */
    void hideMakeFriendsDialog();

    List<SeatStateModel> getSeatStateData();

    void updateMyLoverView(SeatStateModel loverInfo);

    void updateReceiverCharmValue(CommonChatGiftMessage giftMsg);
}
