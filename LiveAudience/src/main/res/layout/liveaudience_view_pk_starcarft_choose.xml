<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/live_bg_pk_starcraft_normal"
    tools:layout_height="220dp"
    tools:layout_width="365dp"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <com.ximalaya.ting.android.liveaudience.view.pk.PkStarCraftBountyView
        android:id="@+id/live_rl_pk_view_bounty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="21dp"
        android:layout_marginTop="21dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_begin="15dp" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:layout_marginTop="15dp"
        android:src="@drawable/live_ic_label_star_craft_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/live_tv_pk_choose_mode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="47dp"
        android:includeFontPadding="false"
        android:text="请主播选择参赛模式"
        android:textColor="#30BAD4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/live_pk_intro"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginRight="10dp"
        android:contentDescription="说明按钮"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_pk_view_choose_mode"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginLeft="21dp"
        android:layout_marginTop="62dp"
        android:layout_marginRight="21dp"
        app:layout_constraintDimensionRatio="333:100"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/live_pk_starcraft_fire"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/live_ic_pk_starcraft_fire_select"
            android:tag="selected"
            app:layout_constraintDimensionRatio="165:100"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/live_pk_starcraft_war"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/live_guide_line_left_margin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.1" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/live_guide_line_top_margin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_percent="0.57" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="不限次数"
                android:textColor="@color/live_color_white_90"
                android:textSize="10sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="@id/live_guide_line_left_margin"
                app:layout_constraintTop_toTopOf="@id/live_guide_line_top_margin" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/live_pk_starcraft_war"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginLeft="3dp"
            android:background="@drawable/live_ic_pk_starcraft_war_normal"
            android:tag="unselected"
            app:layout_constraintDimensionRatio="165:100"
            app:layout_constraintLeft_toRightOf="@id/live_pk_starcraft_fire"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/live_guide_line_vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.12" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/live_guide_line_horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_percent="0.57" />

            <TextView
                android:id="@+id/live_tv_pk_war_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/live_color_white_90"
                android:textSize="10sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="@id/live_guide_line_vertical"
                app:layout_constraintTop_toTopOf="@id/live_guide_line_horizontal"
                tools:text="剩余2次" />

            <TextView
                android:id="@+id/live_tv_pk_war_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="3dp"
                android:layout_marginTop="2dp"
                android:textColor="@color/live_color_white_90"
                android:textSize="8sp"
                app:layout_constraintLeft_toRightOf="@id/live_tv_pk_war_count"
                app:layout_constraintTop_toTopOf="@id/live_tv_pk_war_count"
                tools:text="21:30后清零" />

            <TextView
                android:id="@+id/live_tv_pk_war_entry_wait"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/live_bg_pk_star_craft_war_wait"
                android:paddingLeft="4dp"
                android:paddingTop="2dp"
                android:paddingRight="4dp"
                android:paddingBottom="2dp"
                android:text="敬请期待"
                android:textColor="@color/live_white"
                android:textSize="10sp"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="@id/live_guide_line_vertical"
                app:layout_constraintTop_toTopOf="@id/live_guide_line_horizontal" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/live_tv_pk_starcraft_rule_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="胜 掠夺对方赏金30%，败 损失我方赏金30%"
        android:textColor="@color/live_color_white_60"
        android:textSize="10sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_pk_view_choose_mode" />

</merge>