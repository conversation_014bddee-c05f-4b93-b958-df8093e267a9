<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/live_mine_child_rl"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:gravity="center">

    <ImageView
        android:layout_centerHorizontal="true"
        android:id="@+id/live_mine_child_iv"
        android:layout_width="28dp"
        android:layout_height="28dp" />

    <View
        android:id="@+id/live_mine_child_dot"
        android:layout_width="5dp"
        android:layout_height="5dp"
        android:visibility="gone"
        android:layout_alignTop="@+id/live_mine_child_iv"
        android:layout_alignRight="@+id/live_mine_child_iv"
        android:background="@drawable/live_mine_shape_dot" />

    <TextView
        android:id="@+id/lvie_mine_child_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/live_mine_child_iv"
        android:layout_marginTop="4dp"
        android:gravity="center"
        android:textColor="@color/host_color_333333_999999"
        android:textSize="12sp" />
</RelativeLayout>