<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/record_v_layer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/record_bg_12corner_b_r_ffffff"
        android:padding="12dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/record_tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingEnd="12dp"
            android:textColor="@color/record_color_444444"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="🤗 根据声音标题为您生成金句海报:" />


        <LinearLayout
            android:id="@+id/record_ll_track_poster"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/record_tv_title">

            <include
                android:id="@+id/record_rv_track_poster_left"
                layout="@layout/record_item_ai_track_single_poster_l"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <include
                android:id="@+id/record_rv_track_poster_right"
                layout="@layout/record_item_ai_track_single_poster_r"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_weight="1" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/record_ll_share"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/record_bg_ai_broker_topic_record"
            android:orientation="horizontal"
            android:paddingHorizontal="37dp"
            android:paddingVertical="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/record_ll_track_poster">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:background="@drawable/record_ic_ai_broker_update"
                android:backgroundTint="@color/host_color_ffffff" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="6dp"
                android:text="分享金句海报"
                android:textColor="@color/record_color_ffffff"
                android:textSize="14sp" />
        </LinearLayout>

        <com.ximalaya.ting.android.record.fragment.ai.AiBrokerLikeView
            android:id="@+id/record_alv_like"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/record_ll_share" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>