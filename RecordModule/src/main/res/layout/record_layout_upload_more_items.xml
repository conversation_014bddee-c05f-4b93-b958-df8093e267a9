<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/host_color_e8e8e8">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/record_gl_start"
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="12dp"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/record_gl_end"
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_end="12dp"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:id="@+id/record_v_private"
        android:background="@drawable/record_bg_8corner_ffffff"
        app:layout_constraintTop_toTopOf="@id/record_tv_private"
        app:layout_constraintBottom_toBottomOf="@id/record_tv_private"/>

    <TextView
        android:id="@+id/record_tv_private"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="权限设置"
        android:textColor="@color/host_color_111111_cfcfcf"
        android:paddingTop="23dp"
        android:paddingBottom="23dp"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toBottomOf="@id/record_tv_album_product"
        android:layout_marginTop="10dp"/>

    <TextView
        android:id="@+id/record_tv_private_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawableEnd="@drawable/record_arrow_gray_right"
        android:drawablePadding="4dp"
        android:gravity="center_vertical|end"
        android:paddingStart="100dp"
        android:textColor="@color/host_color_111111_cfcfcf"
        android:textColorHint="@color/host_color_999999_666666"
        android:textSize="14sp"
        tools:text="公开"
        app:layout_constraintBottom_toBottomOf="@id/record_tv_private"
        app:layout_constraintEnd_toEndOf="@id/record_gl_end"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toTopOf="@id/record_tv_private" />

    <androidx.constraintlayout.widget.Group
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/record_g_private"
        app:constraint_referenced_ids="record_tv_private,record_tv_private_value"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/record_cl_schedule_submit"
        android:paddingTop="10dp"
        app:layout_constraintTop_toBottomOf="@id/record_tv_private">

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:id="@+id/record_v_timing_submit"
            android:background="@drawable/record_bg_8corner_ffffff"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/record_tv_timing_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="定时发布"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:paddingTop="23dp"
            android:paddingBottom="23dp"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginStart="12dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>

        <TextView
            android:id="@+id/record_tv_timing_hint"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            app:layout_constraintStart_toEndOf="@id/record_tv_timing_txt"
            android:textColor="@color/host_color_999999_666666"
            android:textSize="14sp"
            android:text="支持设置2小时后的时间点发布"
            app:layout_constraintTop_toTopOf="@id/record_tv_timing_txt"
            app:layout_constraintBottom_toBottomOf="@id/record_tv_timing_txt"/>

        <CheckBox
            android:id="@+id/record_cb_schedule_submit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/record_selector_album_switch"
            app:layout_constraintTop_toTopOf="@id/record_tv_timing_txt"
            app:layout_constraintBottom_toBottomOf="@id/record_tv_timing_txt"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="12dp"/>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:id="@+id/record_v_cb_schedule_submit_mask"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="@id/record_cb_schedule_submit"
            app:layout_constraintEnd_toEndOf="@id/record_cb_schedule_submit"
            app:layout_constraintTop_toTopOf="@id/record_cb_schedule_submit"
            app:layout_constraintBottom_toBottomOf="@id/record_cb_schedule_submit"/>

        <TextView
            android:id="@+id/record_tv_time_going_submit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="发布时间"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:paddingBottom="23dp"
            android:textSize="14sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/record_tv_timing_txt"
            app:layout_constraintTop_toBottomOf="@id/record_tv_timing_txt" />

        <TextView
            android:id="@+id/record_tv_time_going_submit_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/record_arrow_gray_right"
            android:drawablePadding="4dp"
            android:gravity="center_vertical|end"
            android:paddingStart="100dp"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:textColorHint="@color/host_color_999999_666666"
            android:textSize="14sp"
            android:visibility="gone"
            tools:text="2023年11月16日12:00"
            android:layout_marginEnd="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/record_tv_time_going_submit" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/record_v_paid_type"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/record_bg_8corner_ffffff"
        app:layout_constraintBottom_toBottomOf="@id/record_tv_album_product"
        app:layout_constraintTop_toTopOf="@id/record_type_title_tv" />

    <Space
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:id="@+id/record_space_title_tv"
        app:layout_constraintTop_toBottomOf="@id/record_tv_private"/>

    <TextView
        android:id="@+id/record_type_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableRight="@drawable/record_ic_reason"
        android:drawablePadding="3dp"
        android:paddingTop="20dp"
        android:paddingBottom="20dp"
        android:text="付费类型"
        android:textStyle="bold"
        android:textColor="@color/host_color_111111_cfcfcf"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="10dp"/>

    <RadioGroup
        android:id="@+id/record_type_rg"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical|right"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/record_type_title_tv"
        app:layout_constraintEnd_toEndOf="@id/record_gl_end"
        app:layout_constraintStart_toEndOf="@id/record_type_title_tv"
        app:layout_constraintTop_toTopOf="@id/record_type_title_tv">

        <RadioButton
            android:id="@+id/record_type_free"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="30dp"
            android:button="@drawable/record_selector_check_upload_if_picture"
            android:text=" 免费"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:textSize="14sp"
            tools:checked="true" />

        <RadioButton
            android:id="@+id/record_type_paid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="30dp"
            android:button="@drawable/record_selector_check_upload_if_picture"
            android:text=" 付费"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <RadioButton
            android:id="@+id/record_type_advance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/record_selector_check_upload_if_picture"
            android:text=" 超前听"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />
    </RadioGroup>

    <LinearLayout
        android:id="@+id/record_ll_advance"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@id/record_gl_end"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toBottomOf="@id/record_type_title_tv"
        android:gravity="center_vertical"
        android:paddingBottom="20dp"
        tools:visibility="visible"
        android:orientation="horizontal"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="开启试听"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:textSize="14sp"/>

        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/record_tv_early_listen_hint"
            tools:text="102s"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:drawablePadding="8dp"
            android:textColor="@color/host_color_333333_cfcfcf"
            android:textSize="14sp"
            android:drawableEnd="@drawable/record_ic_question"
            android:drawableTint="@color/host_color_999999"/>

        <CheckBox
            android:id="@+id/record_cb_advance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/record_selector_album_switch"/>

    </LinearLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/record_g_pay_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="record_type_title_tv, record_type_rg" />

    <LinearLayout
        android:id="@+id/record_ll_track_price"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/record_bg_4corner_1aea6347"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="12dp"
        android:paddingTop="7dp"
        android:paddingEnd="7dp"
        android:paddingBottom="7dp"
        android:visibility="gone"
        android:weightSum="10"
        app:layout_constraintEnd_toEndOf="@id/record_gl_end"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toBottomOf="@id/record_ll_advance"
        tools:visibility="visible">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="价格"
            android:textStyle="bold"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:textSize="13sp"
            android:includeFontPadding="false"/>

        <TextView
            android:id="@+id/record_tv_track_price_tip"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="6"
            android:gravity="end|center_vertical"
            android:hint="请填写声音的售卖价格哦～"
            android:textColor="@color/host_color_ea6347"
            android:textColorHint="@color/host_color_999999_888888"
            android:textSize="11sp" />

        <EditText
            android:id="@+id/record_price_et"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_marginStart="10dp"
            android:layout_weight="3"
            android:background="@drawable/record_bg_ffffffff_4corner"
            android:gravity="center"
            android:hint="1.99~200"
            android:inputType="numberDecimal"
            android:singleLine="true"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:textColorHint="@color/host_color_bbbbbb_707070"
            android:textSize="14sp" />

    </LinearLayout>

    <Space
        android:id="@+id/record_space_price"
        android:layout_width="match_parent"
        android:layout_height="14dp"
        app:layout_constraintTop_toBottomOf="@id/record_ll_track_price" />

    <View
        android:id="@+id/record_v_track_type"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        android:background="@color/host_color_f3f4f5_353535"
        app:layout_constraintTop_toBottomOf="@id/record_space_price" />

    <androidx.constraintlayout.widget.Group
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/record_g_track_price"
        android:visibility="gone"
        tools:visibility="visible"
        app:constraint_referenced_ids="record_ll_track_price, record_space_price"/>

    <TextView
        android:id="@+id/record_tv_album_product"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="电商带货"
        android:textColor="@color/host_color_111111_cfcfcf"
        android:paddingTop="23dp"
        android:paddingBottom="23dp"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toBottomOf="@id/record_ll_track_price" />

    <TextView
        android:id="@+id/record_tv_album_product_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawableEnd="@drawable/record_arrow_gray_right"
        android:drawablePadding="4dp"
        android:gravity="center_vertical|end"
        android:paddingStart="100dp"
        android:textColor="@color/host_color_111111_cfcfcf"
        android:textColorHint="@color/host_color_999999_666666"
        android:textSize="14sp"
        android:hint="关联带商品"
        app:layout_constraintBottom_toBottomOf="@id/record_tv_album_product"
        app:layout_constraintEnd_toEndOf="@id/record_gl_end"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toTopOf="@id/record_tv_album_product" />

    <androidx.constraintlayout.widget.Group
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/record_g_produce"
        app:constraint_referenced_ids="record_v_track_type, record_tv_album_product, record_tv_album_product_value"/>

    <View
        android:id="@+id/record_v_share_ting_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/record_bg_8corner_ffffff"
        app:layout_constraintBottom_toBottomOf="@id/record_et_share_content"
        app:layout_constraintTop_toTopOf="@id/record_tv_label_share_ting" />

    <TextView
        android:id="@+id/record_tv_label_share_ting"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingTop="23dp"
        android:paddingBottom="17dp"
        android:text="动态配文"
        android:textStyle="bold"
        android:textColor="@color/host_color_111111_cfcfcf"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="@id/record_gl_end"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toBottomOf="@id/record_cl_schedule_submit" />

    <EditText
        android:id="@+id/record_et_share_content"
        style="@style/host_edit_text_cursor_drawable"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@null"
        android:gravity="left|top"
        android:hint="写20字以上的优质动态配文，将会在动态页信息流中被优先曝光"
        android:minHeight="40dp"
        android:textColor="@color/host_color_111111_cfcfcf"
        android:textColorHint="@color/record_color_cccccc_555555"
        android:textSize="14sp"
        android:paddingBottom="15dp"
        app:layout_constraintEnd_toEndOf="@id/record_gl_end"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toBottomOf="@id/record_tv_label_share_ting" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/record_g_share_ting"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="record_v_share_ting_bg, record_tv_label_share_ting, record_et_share_content" />

</androidx.constraintlayout.widget.ConstraintLayout>