<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/host_transparent">

    <LinearLayout
        android:id="@+id/record_ll_ai_loading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/record_bg_white_8corner"
        android:backgroundTint="@color/record_color_ffffff"
        android:orientation="vertical"
        android:visibility="visible">

        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/record_iv_anim"
            android:layout_width="55dp"
            android:layout_height="55dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="24dp"
            app:lottie_fileName="lottie/edit/loading.json"
            app:lottie_imageAssetsFolder="lottie/cover/images"
            app:lottie_loop="true" />


        <TextView
            android:id="@+id/record_tv_ai_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|center_horizontal"
            android:layout_marginTop="12dp"
            android:text="AI合成中"
            android:textStyle="bold"
            android:textColor="@color/record_color_111111"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/record_tv_loading_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="24dp"
            android:layout_gravity="top|center_horizontal"
            android:gravity="center"
            android:layout_marginTop="8dp"
            android:text="正在快马加鞭合成中，精彩语音即刻呈现\n可去草稿箱查看生成的录音文件"
            android:textColor="@color/record_color_999999"
            android:textSize="13sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="24dp"
            android:background="#EBEBEB" />

        <TextView
            android:id="@+id/record_tv_cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_gravity="center_horizontal"
            android:layout_marginVertical="4dp"
            android:layout_marginHorizontal="10dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:textStyle="bold"
            android:text="取消合成"
            android:textColor="@color/record_color_111111"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/record_ll_ai_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/record_bg_white_8corner"
        android:orientation="vertical"
        android:paddingHorizontal="30dp"
        android:paddingVertical="16dp"
        android:visibility="gone">

        <ImageView
            android:id="@+id/record_iv_ai_status"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="24dp"
            android:src="@drawable/record_icon_tts_ai_success" />

        <TextView
            android:id="@+id/record_tv_ai_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|center_horizontal"
            android:layout_marginTop="12dp"
            android:text="AI合成成功"
            android:textColor="@color/record_color_111111"
            android:textSize="14sp" />
    </LinearLayout>

</FrameLayout>