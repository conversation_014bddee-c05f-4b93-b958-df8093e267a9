<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/record_color_000_131313">

    <include
        android:id="@+id/record_cl_guide"
        layout="@layout/record_item_interactive_guide"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/record_iv_mic_logo"
        android:layout_width="65dp"
        android:layout_height="0dp"
        android:scaleType="fitXY"
        android:src="@drawable/record_ic_interactive_mic"
        android:translationX="8dp"
        android:translationY="-50dp"
        app:layout_constraintDimensionRatio="279:340"
        app:layout_constraintEnd_toEndOf="@id/record_gl_end"
        app:layout_constraintTop_toBottomOf="@id/record_cl_guide" />

    <View
        android:id="@+id/record_v_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:translationY="-1dp"
        android:background="@drawable/record_bg_interactive_start_record_item"
        app:layout_constraintBottom_toBottomOf="@id/record_s_start_record"
        app:layout_constraintTop_toBottomOf="@id/record_cl_guide" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/record_gl_start"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="16dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/record_gl_end"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_end="16dp" />

    <ImageView
        android:id="@+id/record_iv_word"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="14dp"
        android:src="@drawable/record_ic_da_zhao_hu"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toBottomOf="@id/record_cl_guide" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="9dp"
        android:src="@drawable/record_ic_interactive_pc_sound"
        app:layout_constraintEnd_toEndOf="@id/record_gl_end"
        android:layout_marginEnd="1dp"
        app:layout_constraintTop_toBottomOf="@id/record_cl_guide" />

    <TextView
        android:id="@+id/record_tv_txt_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="32dp"
        android:ellipsize="end"
        android:textColor="@color/host_color_131313_dcdcdc"
        android:textSize="16sp"
        app:layout_constraintTop_toBottomOf="@id/record_iv_word"
        tools:text="大家好，欢迎收听xxx的播客节目。我是xx，一个热衷于分享工作只是的人。我们将分享一个的话题：剧荒！推理类好剧推荐。" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/record_v_record"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@color/record_color_8e6fff_a9fa61"
        app:layout_constraintBottom_toBottomOf="@id/record_tv_start_record"
        app:layout_constraintEnd_toEndOf="@id/record_tv_start_record"
        app:layout_constraintStart_toStartOf="@id/record_tv_start_record"
        app:layout_constraintTop_toTopOf="@id/record_tv_start_record"
        app:roundPercent="1" />

    <com.ximalaya.ting.android.main.util.ui.PicCenterTextView
        android:id="@+id/record_tv_start_record"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="16dp"
        android:drawablePadding="18dp"
        android:gravity="center_vertical"
        android:paddingTop="18dp"
        android:paddingBottom="18dp"
        android:textColor="@color/host_color_000000"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/record_gl_end"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toBottomOf="@id/record_tv_txt_content" />

    <ImageView
        android:id="@+id/record_iv_start_record_mic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:src="@drawable/record_ic_interactive_start_record_mic"
        app:layout_constraintBottom_toBottomOf="@id/record_tv_start_record"
        app:layout_constraintEnd_toStartOf="@id/record_iv_start_record_txt"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@id/record_tv_start_record"
        app:layout_constraintTop_toTopOf="@id/record_tv_start_record" />

    <ImageView
        android:id="@+id/record_iv_start_record_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="4dp"
        android:src="@drawable/record_ic_interactive_start_record_txt"
        app:layout_constraintBottom_toBottomOf="@id/record_tv_start_record"
        app:layout_constraintEnd_toEndOf="@id/record_tv_start_record"
        app:layout_constraintStart_toEndOf="@id/record_iv_start_record_mic"
        app:layout_constraintTop_toTopOf="@id/record_tv_start_record" />

    <Space
        android:id="@+id/record_s_start_record"
        android:layout_width="match_parent"
        android:layout_height="28dp"
        app:layout_constraintTop_toBottomOf="@id/record_tv_start_record" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/record_btn_ai_link"
        style="@style/host_material_button_basic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:backgroundTint="@color/record_color_e5e5e5_1f2229"
        android:drawableLeft="@drawable/record_ic_interactive_down"
        android:drawablePadding="4dp"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingTop="8dp"
        android:paddingRight="10dp"
        android:paddingBottom="8dp"
        android:text="全部AI创作指引"
        android:textColor="@color/record_color_000000_805ef1"
        android:textSize="12sp"
        app:cornerRadius="100dp"
        app:elevation="0dp"
        android:stateListAnimator="@null"
        app:layout_constraintStart_toStartOf="@id/record_gl_start"
        app:layout_constraintTop_toBottomOf="@id/record_s_start_record" />


    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="@color/record_color_c7c4cb_4c2e333e"
        app:layout_constraintTop_toTopOf="@id/record_tv_record_done"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/record_tv_record_done"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="10dp"
        android:id="@+id/record_v_done_line" />

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="@color/record_color_c7c4cb_4c2e333e"
        app:layout_constraintTop_toTopOf="@id/record_tv_record_done"
        app:layout_constraintStart_toEndOf="@id/record_tv_record_done"
        android:layout_marginStart="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="12dp"
        android:id="@+id/record_v_done_line2" />

    <TextView
        android:id="@+id/record_tv_record_done"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:drawableStart="@drawable/record_ic_interactive_chat"
        android:drawablePadding="12dp"
        android:paddingBottom="180dp"
        android:text="完成录制啦，期待下次再见！"
        android:textColor="@color/record_color_000000_645496"
        android:background="@color/record_color_000_131313"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/record_s_start_record" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/record_cl_chat_line"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:paddingBottom="20dp"
        app:layout_constraintTop_toBottomOf="@id/record_btn_ai_link">

        <View
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:background="@color/record_color_c7c4cb_4c2e333e"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/record_iv_chat"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:background="@color/record_color_c7c4cb_4c2e333e"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/record_iv_chat"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/record_iv_chat"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:background="@color/record_color_000_131313"
            android:paddingStart="16dp"
            android:paddingTop="3dp"
            android:paddingEnd="16dp"
            android:paddingBottom="3dp"
            android:scaleType="centerInside"
            android:src="@drawable/record_ic_interactive_chat"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:id="@+id/record_iv_submit_now"
        app:layout_constraintTop_toTopOf="@id/record_tv_record_done"
        android:layout_marginTop="56dp"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        app:round="23dp"
        android:background="@color/record_color_8e6fff_7242c8"/>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/record_iv_submit_now_word"
        android:src="@drawable/record_ic_li_ji_fa_bu"
        app:layout_constraintTop_toTopOf="@id/record_iv_submit_now"
        app:layout_constraintBottom_toBottomOf="@id/record_iv_submit_now"
        app:layout_constraintStart_toStartOf="@id/record_iv_submit_now"
        app:layout_constraintEnd_toEndOf="@id/record_iv_submit_now"/>
</androidx.constraintlayout.widget.ConstraintLayout>