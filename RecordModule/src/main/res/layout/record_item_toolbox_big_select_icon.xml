<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/record_iv_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:contentDescription="@string/record_icon"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        tools:src="@drawable/record_ic_sound_effect_laugh" />

    <TextView
        android:id="@+id/record_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="4dp"
        android:textColor="@color/record_393a3e_ff7a80_selector"
        android:textSize="@dimen/record_text_size_12"
        app:layout_constraintEnd_toEndOf="@id/record_iv_icon"
        app:layout_constraintStart_toStartOf="@id/record_iv_icon"
        app:layout_constraintTop_toBottomOf="@id/record_iv_icon"
        tools:text="大笑" />

    <ImageView
        android:id="@+id/record_iv_selected_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:contentDescription="@string/record_selected_tag"
        android:src="@drawable/record_item_selected"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/record_iv_icon"
        app:layout_constraintEnd_toEndOf="@id/record_iv_icon"
        app:layout_constraintStart_toStartOf="@id/record_iv_icon"
        app:layout_constraintTop_toTopOf="@id/record_iv_icon"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/record_iv_playing_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="37dp"
        android:layout_marginRight="-10dp"
        android:contentDescription="@string/record_playing_now"
        android:src="@drawable/record_playing_anim"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="@id/record_iv_icon"
        app:layout_constraintTop_toTopOf="@id/record_iv_icon" />

</androidx.constraintlayout.widget.ConstraintLayout>