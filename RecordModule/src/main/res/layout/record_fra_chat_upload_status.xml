<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/record_cl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/host_white">

    <TextView
        android:id="@+id/record_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="上传音频"
        android:textColor="@color/host_color_131313"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/record_iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:src="@drawable/record_ic_back_black"
        app:layout_constraintBottom_toBottomOf="@id/record_tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/record_tv_title" />


    <LinearLayout
        android:id="@+id/record_upload_tip_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@color/record_color_14ff4b61"
        android:orientation="horizontal"
        android:paddingVertical="8dp"
        android:paddingStart="24dp"
        app:layout_constraintTop_toBottomOf="@id/record_tv_title">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="10dp"
            android:src="@drawable/record_ic_chat_upload_warning" />

        <TextView
            android:id="@+id/record_upload_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="请勿离开当前页面，离开页面或切换app，文件会丢失"
            android:textColor="@color/record_color_ff4b61"
            android:textSize="12sp" />
    </LinearLayout>


    <ImageView
        android:id="@+id/record_iv_uploading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="64dp"
        android:src="@drawable/record_ic_connect_cloud"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/record_upload_tip_container" />

    <TextView
        android:id="@+id/record_tv_uploading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="24dp"
        android:text="正在上传音频文件"
        android:textColor="@color/host_color_111111"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="@id/record_iv_uploading"
        app:layout_constraintStart_toStartOf="@id/record_iv_uploading"
        app:layout_constraintTop_toBottomOf="@id/record_iv_uploading" />

    <TextView
        android:id="@+id/record_tv_mid_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="8dp"
        android:textColor="@color/host_color_cccccc"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/record_tv_uploading" />

    <TextView
        android:id="@+id/record_tv_bottom_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="130dp"
        android:text="音频文件已在草稿箱备份"
        android:textColor="@color/host_color_999999"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/record_btn_give_up"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="14dp"
        android:insetTop="0dp"
        android:insetBottom="0dp"
        android:minHeight="0dp"
        android:paddingStart="14dp"
        android:paddingTop="8dp"
        android:paddingEnd="14dp"
        android:paddingBottom="8dp"
        android:stateListAnimator="@null"
        android:text="放弃上传音频"
        android:textColor="@color/host_color_111111"
        android:textSize="15sp"
        android:theme="@style/Theme.MaterialComponents.NoActionBar"
        app:backgroundTint="@color/host_color_f5f5f5"
        app:cornerRadius="100dp"
        app:elevation="0dp"
        app:layout_constraintBottom_toTopOf="@id/record_tv_bottom_tip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rippleColor="@color/host_transparent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/record_btn_re_upload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="14dp"
        android:insetTop="0dp"
        android:insetBottom="0dp"
        android:letterSpacing="0"
        android:minHeight="0dp"
        android:paddingStart="14dp"
        android:paddingTop="8dp"
        android:paddingEnd="14dp"
        android:paddingBottom="8dp"
        android:stateListAnimator="@null"
        android:text="重新上传"
        android:textColor="@color/host_color_111111"
        android:textSize="15sp"
        android:theme="@style/Theme.MaterialComponents.NoActionBar"
        android:visibility="invisible"
        app:backgroundTint="@color/host_color_f5f5f5"
        app:cornerRadius="100dp"
        app:elevation="0dp"
        app:layout_constraintBottom_toTopOf="@id/record_tv_bottom_tip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rippleColor="@color/host_transparent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/record_rv_users"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@id/record_btn_give_up"
        app:layout_constraintTop_toBottomOf="@id/record_tv_mid_tip"
        tools:listitem="@layout/record_item_user_uploading_status" />

</androidx.constraintlayout.widget.ConstraintLayout>