<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <View
        android:id="@+id/record_tv_record_arrow"
        android:layout_width="8dp"
        android:layout_height="16dp"
        android:background="@drawable/record_icon_broker_arrow_left"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="27dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@+id/record_record_content_layout" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/record_record_content_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/record_bg_12corner_b_r_ffffff"
        app:layout_constraintStart_toEndOf="@+id/record_tv_record_arrow"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/record_tv_record_normal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="12dp"
            android:text="🤗 Hi 你好，我是AI经纪人，Hi "
            android:textColor="@color/record_color_444444"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_like_visible_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            app:constraint_referenced_ids="record_iv_content_line, record_iv_like_container" />

        <View
            android:id="@+id/record_iv_content_line"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="15dp"
            android:background="#F0F0F0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/record_ll_views" />

        <include
            android:id="@+id/record_iv_like_container"
            layout="@layout/record_ai_broker_like_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/record_iv_content_line" />

        <LinearLayout
            android:id="@+id/record_ll_views"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:paddingTop="8dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/record_tv_record_normal" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>