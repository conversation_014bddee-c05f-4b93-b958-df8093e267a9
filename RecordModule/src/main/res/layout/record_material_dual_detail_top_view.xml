<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/record_material_dual_dub_detail_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="20dp"
        android:textColor="#333333"
        android:textSize="18sp"
        android:textStyle="bold"
        tools:text="辛芷蕾飙戏，挑战1人分食盐8觉的撒打算的" />

    <TextView
        android:id="@+id/record_material_dual_dub_detail_play_count_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/host_common_margin_15"
        android:layout_marginRight="@dimen/host_common_margin_15"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#666666"
        android:textSize="13sp"
        tools:text="5412次演绎" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginBottom="20dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="20dp"
        android:background="#F6F6F6" />

    <ImageView
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:layout_marginLeft="@dimen/host_common_margin_15"
        android:layout_marginRight="@dimen/host_common_margin_15"
        android:src="@drawable/record_ic_newest_cooperate" />

    <TextView
        android:id="@+id/record_dub_material_dual_detail_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:layout_marginTop="20dp"
        android:textColor="@color/host_color_999999"
        android:textSize="14sp"
        android:visibility="gone"
        tools:visibility="visible"
        android:drawableTop="@drawable/record_ic_dual_dub_detail_empty"
        android:text="暂时没有人与你合配哦~\n快去邀请好友吧！"/>
</LinearLayout>