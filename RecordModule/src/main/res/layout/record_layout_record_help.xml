<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/record_color_80000000"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_percent="0.82"
        app:layout_constraintDimensionRatio="w,394:311"
        android:id="@+id/record_update_desc"
        android:src="@drawable/record_help_guide"
        android:scaleType="fitXY"
        android:contentDescription="录音更新内容"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/record_start_play"
        app:layout_constraintStart_toStartOf="@id/record_update_desc"
        app:layout_constraintEnd_toEndOf="@id/record_update_desc"
        app:layout_constraintBottom_toBottomOf="@id/record_update_desc"
        android:layout_marginBottom="34dp"
        android:src="@drawable/record_btn_start_play"
        android:contentDescription="开始体验"/>

    <ImageView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_percent="0.87"
        app:layout_constraintDimensionRatio="w,81:327"
        android:scaleType="fitXY"
        android:src="@drawable/record_click_show_help"
        android:id="@+id/record_click_show_help"
        android:contentDescription="点击这里可查看新手操作指南"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="invisible"
        android:layout_marginEnd="20dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>