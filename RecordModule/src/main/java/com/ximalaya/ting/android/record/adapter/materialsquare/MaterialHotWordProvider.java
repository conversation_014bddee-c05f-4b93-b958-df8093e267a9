package com.ximalaya.ting.android.record.adapter.materialsquare;

import android.net.Uri;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.hybrid.provider.account.BaseAccountAction;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.view.layout.FlowLayout;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.record.data.model.square.CategoryTagInfo;
import com.ximalaya.ting.android.record.data.model.square.HotWordBean;
import com.ximalaya.ting.android.record.data.model.square.MaterialSquareItem;
import com.ximalaya.ting.android.record.fragment.dub.square.DubMaterialCommonListFragmentNew;

import java.util.List;

/**
 * Created by zhangkaikai on 2018/10/15.
 *
 * <AUTHOR>
 */
class MaterialHotWordProvider implements
    DubMaterialSquareAdapter.IMultiViewTypeAndData<MaterialHotWordProvider.HotWordHolder, List<HotWordBean>> {

    private MainActivity mActivity;
    private BaseFragment2 fragment;
    private int mPadding13;
    private int mPadding10;
    private int mPadding15;
    private int mDp33;
    private int mDp19;

    MaterialHotWordProvider(BaseFragment2 fragment) {
        this.fragment = fragment;
        mActivity = (MainActivity) fragment.getActivity();
        mPadding13 = BaseUtil.dp2px(mActivity, 13f);
        mPadding10 = BaseUtil.dp2px(mActivity, 10f);
        mPadding15 = BaseUtil.dp2px(mActivity, 15f);
        mDp19 = BaseUtil.dp2px(mActivity, 19);
        mDp33 = BaseUtil.dp2px(mActivity, 33);
    }

    @Override
    public void bindViewDatas(final HotWordHolder holder, DubMaterialSquareAdapter.ItemModel<List<HotWordBean>> t,
        final View convertView, int position, boolean isViewTypeLastItem) {
        if (holder == null || t == null || t.getObject() == null) {
            return;
        }
        String title = "热门素材合集";
        if (t.getTag() instanceof MaterialSquareItem) {
            MaterialSquareItem item = (MaterialSquareItem) t.getTag();
            if (!TextUtils.isEmpty(item.getName())) {
                title = item.getName();
            }
        }
        holder.title.setText(title);
        holder.mLabels.removeAllViews();
        holder.mFlowLayout.removeAllViews();
        List<HotWordBean> beans = t.getObject();
        for (HotWordBean bean : beans) {
            final View child = buildChildView(bean);
            FlowLayout.LayoutParams lp = new FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT,
                FlowLayout.LayoutParams.WRAP_CONTENT);
            lp.rightMargin = mPadding10;
            lp.bottomMargin = mPadding10;
            holder.mFlowLayout.addView(child, lp);
            if (bean.getIsHot() == 1) {
                HandlerManager.postOnUIThread(new Runnable() {
                    @Override
                    public void run() {
                        addLabel(child, buildLabel(false), holder.mLabels);
                    }
                });
            } else if (bean.getIsNew() == 1) {
                HandlerManager.postOnUIThread(new Runnable() {
                    @Override
                    public void run() {
                        addLabel(child, buildLabel(true), holder.mLabels);
                    }
                });
            }
        }
    }

    private void addLabel(View child, View labelView, ViewGroup viewGroup) {
        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT);
        lp.topMargin = (int) child.getY() + mPadding15;
        lp.leftMargin = ((int) child.getX()) + child.getWidth() - mDp33;
        viewGroup.addView(labelView, lp);
    }

    private ImageView buildLabel(boolean isNew) {
        ImageView newLabel = new ImageView(mActivity);
        newLabel.setLayoutParams(new FrameLayout.LayoutParams(mDp33, mDp19));
        newLabel
            .setImageResource(isNew ? R.drawable.record_material_square_new : R.drawable.record_material_square_hot);
        return newLabel;
    }

    private View buildChildView(final HotWordBean bean) {
        TextView item = new TextView(mActivity);
        item.setText(bean.getName());
        item.setTextSize(12);
        item.setEllipsize(TextUtils.TruncateAt.END);
        item.setSingleLine();
        item.setTextColor(ContextCompat.getColor(BaseApplication.getMyApplicationContext(), com.ximalaya.ting.android.host.R.color.host_color_000000_cfcfcf));
        item.setPadding(mPadding13, mPadding10, mPadding13, mPadding10);
        item.setBackgroundResource(R.drawable.record_bg_white_100corner_e7e7e7_border);
        item.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                // configure = 0才跳转配置链接
                if (bean.getConfigure() == 0 && !TextUtils.isEmpty(bean.getConfigureUrl())) {
                    handleConfigureUrl(bean.getConfigureUrl());
                } else {
                    CategoryTagInfo info = new CategoryTagInfo();
                    info.tagId = bean.getId();
                    info.tagName = bean.getName();
                    fragment.startFragment(
                        DubMaterialCommonListFragmentNew.newInstance(info, AppConstants.TYPE_MATERIAL_HOT_WORD));
                }
                new UserTracking("趣配音素材广场页", "tag")
                    .setSrcModule("hotMaterials").setItemId(bean.getId())
                    .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            }
        });
        return item;
    }

    /**
     * 跳转web页面或是iTing跳转
     */
    private void handleConfigureUrl(String url) {
        Uri uri = Uri.parse(url);
        if (url.startsWith("iting://")) {
            if (TextUtils.equals(uri.getQueryParameter("msg_type"), "56") && !UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(mActivity);
                return;
            }
            NativeHybridFragment.start(mActivity, url, false);
            return;
        }
        boolean isRightUrl = !TextUtils.isEmpty(url) && (url.startsWith("http") || url.startsWith("https"));
        if (!isRightUrl || mActivity == null) {
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
        bundle.putBoolean(BundleKeyConstants.KEY_IS_SCANCODE_URL, true);
        fragment.startFragment(NativeHybridFragment.class, bundle);
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.record_material_hot_word, parent, false);
    }

    @Override
    public HotWordHolder buildHolder(View convertView) {
        return new HotWordHolder(convertView);
    }

    public static class HotWordHolder extends HolderAdapter.BaseViewHolder {

        public TextView title;
        FlowLayout mFlowLayout;
        FrameLayout mLabels;

        HotWordHolder(View view) {
            title = view.findViewById(R.id.record_material_title);
            mFlowLayout = (FlowLayout) view.findViewById(R.id.record_fl_hot_word);
            mLabels = (FrameLayout) view.findViewById(R.id.record_fl_labels);
        }
    }
}
