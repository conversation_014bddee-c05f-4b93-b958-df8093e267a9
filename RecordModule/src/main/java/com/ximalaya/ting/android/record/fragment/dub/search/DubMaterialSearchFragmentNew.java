package com.ximalaya.ting.android.record.fragment.dub.search;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.model.play.DubTransferModel;
import com.ximalaya.ting.android.host.util.AndroidBug5497Workaround;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.record.adapter.materialsquare.DubMaterialSearchAdapterNew;
import com.ximalaya.ting.android.record.constants.ConstantValues;
import com.ximalaya.ting.android.record.constants.UrlConstantsForRecord;
import com.ximalaya.ting.android.record.data.model.search.MaterialSearchHotWord;
import com.ximalaya.ting.android.record.data.model.search.MaterialSearchResultItem;
import com.ximalaya.ting.android.record.data.model.square.MaterialFilterItemData;
import com.ximalaya.ting.android.record.fragment.dub.DubMaterialDownloadFragment;
import com.ximalaya.ting.android.record.fragment.dub.ImageDubFragment;
import com.ximalaya.ting.android.record.fragment.dub.search.impl.IDubMaterialSearchContext;
import com.ximalaya.ting.android.record.fragment.dub.square.landing.DubMaterialLandingFragment;
import com.ximalaya.ting.android.record.fragment.dub.wrapper.EditTextWatcherWrapper;
import com.ximalaya.ting.android.record.fragment.dub.wrapper.OnClickListenerWrapper;
import com.ximalaya.ting.android.record.fragment.dub.wrapper.OnEditorActionListenerWrapper;
import com.ximalaya.ting.android.record.fragment.dub.wrapper.OnItemClickListenerWrapper;
import com.ximalaya.ting.android.record.fragment.util.SearchHistoryWordImpl;
import com.ximalaya.ting.android.record.fragment.util.SearchUiUtils;
import com.ximalaya.ting.android.record.manager.request.CommonRequestForRecord;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

/**
 * Created by ervin.li on 2018/4/26.
 *
 * <AUTHOR> 在线图片配音模板搜索
 */
public class DubMaterialSearchFragmentNew extends BaseFragment2 implements AdapterView.OnItemClickListener,
    IRefreshLoadMoreListener, IDubMaterialSearchContext, View.OnClickListener, TextView.OnEditorActionListener,
    TextWatcher, DubMaterialSearchAdapterNew.IAdapterViewClickListener {

    private static final String TAG_SEARCH_RESULT = "search_result";
    private static final String TAG_SEARCH_HISTORY_HOT_WORD = "search_hot_word";
    private static final String KEY_SEARCH_EDIT_TEXT = "key_search_edit_text";
    public String mSearchKeyword;
    private ListView mRefreshListView;
    private EditText mSearchTextEt;
    private TextView mTvEmptyHint;
    private TextView mTvCancelOrSearch;
    private ImageView mClearBtn;
    private String mLastRequestKeyWord;
    private int mCurrentPageNo;
    private DubMaterialSearchAdapterNew mSuggestAdapter;
    private boolean mIsLoadMore;
    private String mSearchHint;
    private int mInputMode;
    private boolean mHotPageContentTop;
    /**
     * 暂时禁止提示词
     */
    private boolean mForbidKeyboardOnce;
    /**
     * 连续连词搜索历史词出现页面重叠问题，所以通过标志过滤掉连续点击
     */
    private boolean mIsDealSearchWordClick = false;
    /**
     * 可变搜索词上报
     */
    private Map<String, String> mData = new HashMap<>();
    private String mInputKey;
    private boolean mForbiddenSuggestWordOnce;
    private List<MaterialFilterItemData> materialFilterItemDataList;
    /**
     * 是否为显示搜索结果页面
     */
    private boolean mIsShowSearchResult = false;
    private AutoTraceHelper.IDataProvider mDataProvider = new AutoTraceHelper.IDataProvider() {
        @Override
        public Object getData() {
            return mData;
        }

        @Override
        public Object getModule() {
            return null;
        }

        @Override
        public String getModuleType() {
            return AutoTraceHelper.MODULE_DEFAULT;
        }
    };

    public DubMaterialSearchFragmentNew() {
        super(AppConstants.isPageCanSlide, null);
    }

    public static DubMaterialSearchFragmentNew newInstance() {
        return new DubMaterialSearchFragmentNew();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCanSlided(true);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        getWindow().setSoftInputMode(mInputMode);
        if (mSearchTextEt != null) {
            mSearchTextEt.clearFocus();
            mSearchTextEt.setOnClickListener(null);
            mSearchTextEt.setCursorVisible(false);
            mSearchTextEt.setOnEditorActionListener(null);
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        initTitleBars();
        uiInit();
        regListener();
        addSearchHotWordFragment();
        openSoftInput(100);
    }

    private void initTitleBars() {
        ViewGroup titleBar = findViewById(R.id.record_title_bar);
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) titleBar.getLayoutParams();
        params.topMargin += BaseUtil.getStatusBarHeight(mContext);
        titleBar.setLayoutParams(params);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        //滑动结束不需要拦截，直接返回
        setOnFinishListener(() -> {
            finish();
            return true;
        });
    }

    @Override
    public void onMyResume() {
        if (getActivity() != null) {
            AndroidBug5497Workaround.releaseActivity(getActivity());
        }
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN
            | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        super.onMyResume();
        if (TextUtils.isEmpty(SearchUiUtils.getEditTextContent(mSearchTextEt)) && !mIsShowSearchResult) {
            openSoftInput(100);
        }
    }

    @Override
    public void onPause() {
        hideSoftInput();
        super.onPause();
    }

    @Override
    public boolean onBackPressed() {
        if (mIsShowSearchResult) {
            removeSearchDataFraShowHistoryFra();
        }
        hideSoftInput();
        return super.onBackPressed();
    }

    @Override
    protected boolean onPrepareNoContentView() {
        setNoContentImageView(com.ximalaya.ting.android.host.R.drawable.host_no_search_result);
        setNoContentTitle("没有找到相关结果?");
        return true;
    }

    private void uiInit() {
        mInputMode = getWindow().getAttributes().softInputMode;
        mRefreshListView = findViewById(R.id.record_lv_online_picture_template_sub);
        mRefreshListView.setDivider(null);
        mSuggestAdapter = new DubMaterialSearchAdapterNew(this, new ArrayList<>(), this);
        mRefreshListView.setAdapter(mSuggestAdapter);
        // 需要先初始化emptyView，否则mRefreshListView不能measure内部PullToRefreshListView的宽高，接口返回数据后就显示不出来.为什么？！ >_<
        initEmptyView();
        mTvCancelOrSearch = findViewById(R.id.record_tv_cancel_search);
        mClearBtn = findViewById(R.id.record_clear_search_text);
        mSearchTextEt = findViewById(R.id.record_et_search_content);
    }

    private void regListener() {
        OnClickListenerWrapper mOnClickListener = new OnClickListenerWrapper(this);
        SearchUiUtils.setOnClickListener(mOnClickListener, mClearBtn, mSearchTextEt, mTvCancelOrSearch, mClearBtn);

        OnEditorActionListenerWrapper onEditorActionListenerWrapper = new OnEditorActionListenerWrapper(this);
        EditTextWatcherWrapper editTextWatcherWrapper = new EditTextWatcherWrapper(this);
        mSearchTextEt.setOnEditorActionListener(onEditorActionListenerWrapper);
        mSearchTextEt.addTextChangedListener(editTextWatcherWrapper);

        OnItemClickListenerWrapper mListViewItemClickListener = new OnItemClickListenerWrapper(this);
        mRefreshListView.setOnItemClickListener(mListViewItemClickListener);

        AutoTraceHelper.bindData(mClearBtn, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(mTvCancelOrSearch, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindDataCallback(mSearchTextEt, mDataProvider);
    }

    @Override
    protected String getPageLogicName() {
        return getClass().getSimpleName();
    }

    public void hideSoftInput() {
        if (mSearchTextEt == null || mActivity == null || !canUpdateUi()) {
            return;
        }
        mSearchTextEt.clearFocus();
        InputMethodManager inputMethodManager = (InputMethodManager) mActivity.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputMethodManager != null && inputMethodManager.isActive()) {
            inputMethodManager.hideSoftInputFromWindow(mSearchTextEt.getWindowToken(), 0);
        }
    }

    private void openSoftInput(final long time) {
        //设置键盘延迟弹出毫秒时间
        if (mSearchTextEt == null || time < 0) {
            return;
        }
        doAfterAnimation(() -> postOnUiThreadDelayed(DubMaterialSearchFragmentNew.this::openSoftInput, time));
    }

    private void openSoftInput() {
        if (mSearchTextEt == null || !canUpdateUi() || mActivity == null) {
            return;
        }
        mSearchTextEt.requestFocus();
        InputMethodManager inputMethodManager = (InputMethodManager) mActivity.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputMethodManager != null) {
            inputMethodManager.showSoftInput(mSearchTextEt, 0);
        }
    }

    @Override
    protected void loadData() {
        doAfterAnimation(this::initLoadData);
    }

    private void initLoadData() {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        CommonRequestForRecord.getMaterialFilterItemData(UrlConstantsForRecord.getInstance().getMaterialFilterItemDataUrl(),
            null, new IDataCallBack<List<MaterialFilterItemData>>() {
                @Override
                public void onSuccess(@Nullable List<MaterialFilterItemData> object) {
                    if (!canUpdateUi()) {
                        return;
                    }
                    materialFilterItemDataList = object;
                    onPageLoadingCompleted(LoadCompleteType.OK);
                }

                @Override
                public void onError(int code, String message) {
                    onPageLoadingCompleted(LoadCompleteType.OK);
                }
            });
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    private void initEmptyView() {
        if (mTvEmptyHint == null) {
            mTvEmptyHint = new TextView(mContext);
            mTvEmptyHint.setTextColor(Color.parseColor("#666666"));
            mTvEmptyHint.setTextSize(14);
            mTvEmptyHint.setGravity(Gravity.CENTER);
            FrameLayout.LayoutParams ps = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.WRAP_CONTENT);
            mTvEmptyHint.setLayoutParams(ps);
            mRefreshListView.setEmptyView(mTvEmptyHint);
        }
    }

    private void loadSearch() {
        String editTextContent = SearchUiUtils.getEditTextContent(mSearchTextEt);
        String kw = null;
        if (!TextUtils.isEmpty(editTextContent)) {
            kw = mSearchKeyword = editTextContent.trim();
        } else if (!TextUtils.isEmpty(mSearchHint)) {
            kw = mSearchKeyword = mSearchHint.trim();
        }
        //没有提示词也没有输入
        if (TextUtils.isEmpty(kw)) {
            CustomToast.showFailToast("请输入搜索关键字!");
            return;
        }
        SearchUiUtils.setSelection(mSearchTextEt);
        doSearch(buildMaterialSearchHotWord(0, kw, 0));
    }

    private MaterialSearchHotWord buildMaterialSearchHotWord(int type, String kw, long templateId) {
        MaterialSearchHotWord materialSearchHotWord = new MaterialSearchHotWord();
        materialSearchHotWord.setType(type);
        materialSearchHotWord.setSearchWord(kw);
        materialSearchHotWord.setTemplateId(templateId);
        return materialSearchHotWord;
    }

    private void doSearch(MaterialSearchHotWord materialSearchHotWord) {
        if (materialSearchHotWord == null || TextUtils.isEmpty(materialSearchHotWord.getSearchWord())) {
            CustomToast.showFailToast("请输入搜索关键字!");
            return;
        }
        mLastRequestKeyWord = materialSearchHotWord.getSearchWord();
        //搜索词并入历史
        SearchHistoryWordImpl.getInstance().buildHistory(materialSearchHotWord);
        SearchHistoryWordImpl.getInstance().saveHistory(getActivity());
        hideSoftInput();
        if (materialSearchHotWord.getType() == 3) {
            startNativeHybridFragment(materialSearchHotWord.getLinkUrl());
        } else if (materialSearchHotWord.getType() == 2) {
            BaseFragment2 fragment = DubMaterialSearchResultFragment.getInstance(materialSearchHotWord.getSearchWord(), materialSearchHotWord.getSearchId(), materialFilterItemDataList);
            replaceSearchResultFragment(fragment);
        } else if (materialSearchHotWord.getType() == 1 && materialSearchHotWord.getTemplateId() > 0) {
            prepareGoToMaterialLandingFragment(materialSearchHotWord.getTemplateId());
        } else {
            // 跳转搜索结果页
            BaseFragment2 fragment = DubMaterialSearchResultFragment.getInstance(mLastRequestKeyWord, materialFilterItemDataList);
            replaceSearchResultFragment(fragment);
        }
        SearchUiUtils.setVisible(View.INVISIBLE, mRefreshListView);
    }

    /**
     * 加载联想词
     */
    protected void loadSuggestWord(final String inputKeyWord, final int pageNo) {
        if (TextUtils.isEmpty(inputKeyWord)) {
            return;
        }
        if (mSuggestAdapter != null) {
            List<MaterialSearchResultItem> result = new ArrayList<>();
            result.add(createDefaultQueryResult(inputKeyWord));
            mSuggestAdapter.setListData(result);
            mSuggestAdapter.notifyDataSetChanged();
        }
        SearchUiUtils.setVisible(View.VISIBLE, mRefreshListView);
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        mLastRequestKeyWord = inputKeyWord;
        String url = UrlConstantsForRecord.getInstance().getFuzzySearchTemplatesByWordUrl();
        HashMap<String, String> params = new HashMap<>();
        params.put("words", inputKeyWord);
        CommonRequestForRecord.getMaterialFuzzySearchResult(url, params, new IDataCallBack<List<MaterialSearchResultItem>>() {
            @Override
            public void onSuccess(@Nullable List<MaterialSearchResultItem> object) {
                boolean noSuggestWord = !canUpdateUi() || !TextUtils.equals(inputKeyWord, mInputKey);
                if (noSuggestWord) {
                    return;
                }
                List<MaterialSearchResultItem> result = new ArrayList<>();
                result.add(createDefaultQueryResult(inputKeyWord));
                if (ToolUtil.isEmptyCollects(object)) {
                    if (mIsLoadMore) {
                        mIsLoadMore = false;
                    } else {
                        mSuggestAdapter.setListData(result);
                        mSuggestAdapter.notifyDataSetChanged();
                    }
                } else {
                    if (mIsLoadMore) {
                        mCurrentPageNo = pageNo;
                    } else {
                        mCurrentPageNo = ConstantValues.PARAM_DEFAULT_PAGE_NO;
                    }
                    mIsLoadMore = false;
                    result.addAll(object);
                    mSuggestAdapter.setListData(result);
                    mSuggestAdapter.notifyDataSetChanged();
                }
                onPageLoadingCompleted(LoadCompleteType.OK);
            }

            @Override
            public void onError(int code, String message) {
                if (mIsLoadMore) {
                    mIsLoadMore = false;
                }
                onPageLoadingCompleted(LoadCompleteType.OK);
            }
        });
    }

    private MaterialSearchResultItem createDefaultQueryResult(String keyword) {
        MaterialSearchResultItem materialSearchResultItem = new MaterialSearchResultItem();
        materialSearchResultItem.setHighlightKeyword(String.format("搜索\"<em>%s</em>\" ", keyword));
        return materialSearchResultItem;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.record_fra_material_search_new;
    }

    @Override
    protected void loadDataError() {
        super.loadDataError();
        mRefreshListView.setVisibility(View.INVISIBLE);
    }

    @Override
    protected void loadDataOk() {
        super.loadDataOk();
        mRefreshListView.setVisibility(View.VISIBLE);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        if (mSuggestAdapter.getItemViewType(position) == DubMaterialSearchAdapterNew.VIEW_TYPE_SUGGEST_ITEM) {
            doSearch(buildMaterialSearchHotWord(0, mLastRequestKeyWord, 0));
            return;
        }
        Object item = parent.getAdapter().getItem(position);
        if (item instanceof MaterialSearchResultItem) {
            MaterialSearchResultItem itemModel = (MaterialSearchResultItem) item;
            prepareGoToMaterialLandingFragment(itemModel.getTemplateId());
            SearchHistoryWordImpl.getInstance().buildHistory(buildMaterialSearchHotWord(1, itemModel.getName(), itemModel.getTemplateId()));
            SearchHistoryWordImpl.getInstance().saveHistory(getActivity());
        }
    }

    @Override
    public void onMore() {
        mIsLoadMore = true;
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        int requestPageNo = mCurrentPageNo + 1;
        loadSuggestWord(mLastRequestKeyWord, requestPageNo);
    }

    @Override
    public void showSoftInput(boolean show) {
        if (show) {
            openSoftInput(0);
        } else {
            hideSoftInput();
        }
    }

    @Override
    public void updateSearchHint(String searchHint) {
        this.mSearchHint = searchHint;
        setSearchSpaceHint(searchHint);
    }

    private void setSearchSpaceHint(String hint) {
        if (mSearchTextEt != null) {
            String searchHint = " " + hint;
            mSearchTextEt.setHint(searchHint);
        }
    }

    @Override
    public void onItemClick(View v, MaterialSearchHotWord word, int type, int pageId, int position) {
        //热词列表点击
        if (word == null || mIsDealSearchWordClick) {
            return;
        }
        if (TextUtils.isEmpty(word.getSearchWord())) {
            CustomToast.showFailToast("请输入搜索关键字!");
            return;
        }
        mIsDealSearchWordClick = true;
        hideSoftInput();
        // 搜索词
        String searchWord = word.getSearchWord();
        // 搜索词展示出来后不去请求打开联想词页面
        if(!TextUtils.isEmpty(searchWord)) {
            mForbiddenSuggestWordOnce = true;
            mSearchTextEt.setText(searchWord);
        }
        mData.put(KEY_SEARCH_EDIT_TEXT, searchWord);
        doSearch(word);
        postOnUiThread(() -> mIsDealSearchWordClick = false);
    }

    @Override
    public void setHotWordList(List<String> hotWordList) {
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int viewId = v.getId();
        if (viewId == R.id.record_et_search_content) {
            boolean hasFocus = mSearchTextEt != null && !mSearchTextEt.isFocused() && mSearchTextEt.getText() != null;
            if (hasFocus) {
                mSearchTextEt.setSelection(mSearchTextEt.getText().length());
            } else if (mSearchTextEt != null && mSearchTextEt.isFocused()) {
                mSearchTextEt.setCursorVisible(true);
                // 在搜索结果页面点击时才回退到历史热词页面
                if (mIsShowSearchResult) {
                    removeSearchDataFraShowHistoryFra();
                }
            }
        } else if (viewId == R.id.record_clear_search_text) {
            if (mSearchTextEt != null) {
                mSearchTextEt.setText("");
            }
        } else if (viewId == R.id.record_tv_cancel_search) {
            hideSoftInput();
            finishFragment();
        }
    }

    @Override
    public void onAdapterViewClick(View view, MaterialSearchResultItem itemModel, int position, HolderAdapter.BaseViewHolder holder) {
        int id = view.getId();
        if (id == R.id.record_dub_now) {
            if (itemModel == null) {
                return;
            }

            if (itemModel.getType() == 0) {
                // 跳转图片录制页
                startFragment(ImageDubFragment.newInstance(itemModel.getTemplateId(), 0, null));
            } else {
                if (itemModel.getTemplateId() <= 0 || ChildProtectManager.checkChildrenModeOpenFromToB(getContext())) {
                    return;
                }
                DubTransferModel dubTransferModel = new DubTransferModel.DubTransferItemBuilder()
                    .seTrackId(itemModel.getTrackId()).setTopicId(0).setTopicName(null)
                    .setTopicUploadType(0).setFromType(1).setUp();
                startFragment(DubMaterialDownloadFragment.newInstance(dubTransferModel));
            }
            SearchHistoryWordImpl.getInstance().buildHistory(buildMaterialSearchHotWord(1, itemModel.getName(), itemModel.getTemplateId()));
            SearchHistoryWordImpl.getInstance().saveHistory(getActivity());
        }
    }

    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        boolean loadSearch = mSearchTextEt != null && v != null && actionId == EditorInfo.IME_ACTION_SEARCH;
        if (loadSearch) {
            String editTextTrimContent = SearchUiUtils.getEditTextContentTrim(mSearchTextEt);
            if (TextUtils.isEmpty(editTextTrimContent) && TextUtils.isEmpty(mSearchHint)) {
                return true;
            }
            loadSearch();
        }
        return true;
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {}

    @Override
    public void afterTextChanged(Editable s) {
        mInputKey = s.toString();
        if (s.length() == 0) {
            SearchUiUtils.setVisible(View.GONE, mClearBtn);
            // 不是搜索结果页面才展示热词历史页面
            if (!TAG_SEARCH_RESULT.equals(getForegroundChildFragmentTag())) {
                removeSearchDataFraShowHistoryFra();
            }
            mSuggestAdapter.clear();
            SearchUiUtils.setVisible(View.INVISIBLE, mRefreshListView);
            //当次键盘弹出禁止
            if (!mForbidKeyboardOnce) {
                openSoftInput(0);
            }
        } else {
            SearchUiUtils.setVisible(View.VISIBLE, mClearBtn);
            if (!mForbiddenSuggestWordOnce) {
                removeSearchDataFraHideHistoryFra();
                loadSuggestWord(s.toString(), 1);
            }
        }
        mForbiddenSuggestWordOnce = false;
        mForbidKeyboardOnce = false;
    }

    private String getForegroundChildFragmentTag() {
        String tag = null;
        FragmentManager fm = getChildFragmentManager();
        int n = fm.getBackStackEntryCount();
        if (n > 0) {
            FragmentManager.BackStackEntry backStackEntry = fm.getBackStackEntryAt(n - 1);
            tag = backStackEntry.getName();
        }
        return tag;
    }

    private void replaceSearchResultFragment(BaseFragment2 fragment) {
        if (!canUpdateUi()) {
            return;
        }
        FragmentTransaction ft = getChildFragmentManager().beginTransaction();
        Fragment searchResultFragment = getChildFragmentManager().findFragmentByTag(TAG_SEARCH_RESULT);
        Fragment searchHistoryHotFragment = getChildFragmentManager().findFragmentByTag(TAG_SEARCH_HISTORY_HOT_WORD);
        if (searchResultFragment != null) {
            ft.replace(R.id.record_fragment_container, fragment, TAG_SEARCH_RESULT);
        } else {
            ft.add(R.id.record_fragment_container, fragment, TAG_SEARCH_RESULT);
        }
        if (searchHistoryHotFragment != null) {
            ft.hide(searchHistoryHotFragment);
        }
        mIsShowSearchResult = true;
        ft.commitAllowingStateLoss();
        setSlideState(false);
    }

    private void startNativeHybridFragment(String linkUrl) {
        startFragment(NativeHybridFragment.newInstance(linkUrl, true));
    }

    private void prepareGoToMaterialLandingFragment(final long templateId) {
        if (ConstantsOpenSdk.isBundleFrameWork &&
            Configure.mainBundleModel.isDl &&
            !Configure.mainBundleModel.hasGenerateBundleFile) {
            Router.getActionByCallback(Configure.BUNDLE_MAIN, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.mainBundleModel.bundleName.equals(bundleModel.bundleName)) {
                        goToMaterialLandingFragment(templateId);
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    CustomToast.showDebugFailToast("main bundle install error");
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });

        } else {
            goToMaterialLandingFragment(templateId);
        }
    }

    private void goToMaterialLandingFragment(long templateId) {
        if (ChildProtectManager.checkChildrenModeOpenFromToB(getContext())) {
            return;
        }
        // 跳转落地页
        startFragment(DubMaterialLandingFragment.getInstance(templateId));
    }

    /**
     * 添加搜索词页面
     */
    private void addSearchHotWordFragment() {
        if (!canUpdateUi()) {
            return;
        }
        FragmentTransaction ft = getChildFragmentManager().beginTransaction();
        Fragment searchHistoryHotFragment = getChildFragmentManager().findFragmentByTag(TAG_SEARCH_HISTORY_HOT_WORD);
        if (searchHistoryHotFragment != null) {
            ft.show(searchHistoryHotFragment);
        } else {
            DubMaterialSearchHistoryHotFragment fragment = getSearchHistoryHotFragment();
            ft.add(R.id.record_fragment_container, fragment, TAG_SEARCH_HISTORY_HOT_WORD);
        }
        mHotPageContentTop = false;
        ft.commitAllowingStateLoss();
    }

    @NonNull
    private DubMaterialSearchHistoryHotFragment getSearchHistoryHotFragment() {
        DubMaterialSearchHistoryHotFragment fragment = DubMaterialSearchHistoryHotFragment.newInstance();
        fragment.setSearchContext(this);
        fragment.setMoveContentToTop(mHotPageContentTop);
        return fragment;
    }

    private void removeSearchDataFraShowHistoryFra() {
        if (!canUpdateUi()) {
            return;
        }
        FragmentManager fm = getChildFragmentManager();
        FragmentTransaction ft = getChildFragmentManager().beginTransaction();
        Fragment searchDataFragment = fm.findFragmentByTag(TAG_SEARCH_RESULT);
        if (ft != null) {
            DubMaterialSearchHistoryHotFragment searchHistoryHotFragment = (DubMaterialSearchHistoryHotFragment) getChildFragmentManager().findFragmentByTag(TAG_SEARCH_HISTORY_HOT_WORD);
            if (searchHistoryHotFragment != null) {
                ft.show(searchHistoryHotFragment);
            } else {
                DubMaterialSearchHistoryHotFragment fragment = getSearchHistoryHotFragment();
                ft.add(R.id.record_fragment_container, fragment, TAG_SEARCH_HISTORY_HOT_WORD);
            }
            if (searchDataFragment != null) {
                ft.remove(searchDataFragment);
            }
            mIsShowSearchResult = false;
            ft.commitAllowingStateLoss();
            setSlideState(true);
        }
    }

    private void removeSearchDataFraHideHistoryFra() {
        if (!canUpdateUi()) {
            return;
        }
        FragmentManager fm = getChildFragmentManager();
        Fragment searchDataFragment = fm.findFragmentByTag(TAG_SEARCH_RESULT);
        Fragment searchHistoryHotFragment = getChildFragmentManager().findFragmentByTag(TAG_SEARCH_HISTORY_HOT_WORD);
        FragmentTransaction ft = getChildFragmentManager().beginTransaction();
        if (ft != null) {
            if (searchDataFragment != null) {
                ft.remove(searchDataFragment);
            }
            if (searchHistoryHotFragment != null) {
                ft.hide(searchHistoryHotFragment);
            }
            mIsShowSearchResult = false;
            ft.commitAllowingStateLoss();
            setSlideState(true);
        }
    }

    private void setSlideState(boolean canSlide) {
        if (getSlideView() != null) {
            getSlideView().setSlide(canSlide);
        }
    }
}
