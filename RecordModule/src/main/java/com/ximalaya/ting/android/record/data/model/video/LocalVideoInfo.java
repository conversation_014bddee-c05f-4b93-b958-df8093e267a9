package com.ximalaya.ting.android.record.data.model.video;

import android.graphics.Bitmap;

import java.io.Serializable;

/**
 * Created by ervin.li on 2018/5/28.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class LocalVideoInfo implements Serializable {

    private static final long serialVersionUID = 4044530847575319607L;

    private String thumbPath;
    private String path;
    private Bitmap thumbBitmap;
    private boolean takeVideo;
    private double duration;

    public String getThumbPath() {
        return thumbPath;
    }

    public void setThumbPath(String thumbPath) {
        this.thumbPath = thumbPath;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public double getDuration() {
        return duration;
    }

    public void setDuration(double duration) {
        this.duration = duration;
    }

    public boolean isTakeVideo() {
        return takeVideo;
    }

    public void setTakeVideo(boolean takeVideo) {
        this.takeVideo = takeVideo;
    }

    public Bitmap getThumbBitmap() {
        return thumbBitmap;
    }

    public void setThumbBitmap(Bitmap bitmap) {
        this.thumbBitmap = bitmap;
    }
}
