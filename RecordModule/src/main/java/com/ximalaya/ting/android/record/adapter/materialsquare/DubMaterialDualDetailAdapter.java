package com.ximalaya.ting.android.record.adapter.materialsquare;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.model.materialsquare.CooperateDubTrackBean;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.util.List;

/**
 * Created by zhangkaikai on 2019-09-14.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15721173906
 */
public class DubMaterialDualDetailAdapter extends HolderAdapter<CooperateDubTrackBean> {

    public DubMaterialDualDetailAdapter(Context context, List<CooperateDubTrackBean> listData) {
        super(context, listData);
    }

    @Override
    public void onClick(View view, CooperateDubTrackBean cooperateDubTrackBean, int position, BaseViewHolder holder) {

    }

    @Override
    public int getConvertViewId() {
        return R.layout.record_item_material_dual_detail_track;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new DualDetailHolder(convertView);
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, CooperateDubTrackBean bean, int position) {
        if (bean == null || !(holder instanceof DualDetailHolder)) {
            return;
        }
        DualDetailHolder detailHolder = (DualDetailHolder) holder;
        ImageManager.from(context).displayImage(detailHolder.mAvatarIv, bean.getLogo(),
            com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88);
        detailHolder.mNameTv.setText(bean.getTitle());
        detailHolder.mCountTv.setText(StringUtil.getFriendlyNumStr(bean.getPlays()));
        AutoTraceHelper.bindData(detailHolder.mRootView, "", new AutoTraceHelper.DataWrap(position, bean));
    }

    private static class DualDetailHolder extends BaseViewHolder {

        private RoundImageView mAvatarIv;
        private TextView mNameTv;
        private TextView mCountTv;
        private View mRootView;

        DualDetailHolder(View convertView) {
            mRootView = convertView;
            mAvatarIv = convertView.findViewById(R.id.record_author_icon_view_small);
            mNameTv = convertView.findViewById(R.id.record_author_name_view);
            mCountTv = convertView.findViewById(R.id.record_author_play_count_view);
        }
    }
}
