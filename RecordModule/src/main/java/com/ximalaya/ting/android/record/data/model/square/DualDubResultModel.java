package com.ximalaya.ting.android.record.data.model.square;

import java.util.List;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import android.text.TextUtils;

/**
 * Created by <PERSON><PERSON>kaikai on 2019/3/14.
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @PhoneNumber 15721173906
 */
public class DualDubResultModel {

    private int pageSize;
    private int pageNo;
    private int totalPage;
    private int totalCount;
    private List<DualDubMaterialBean> result;

    public static DualDubResultModel parseData(String content) {
        if (TextUtils.isEmpty(content)) {
            return null;
        }
        DualDubResultModel model = null;
        Gson gson = new Gson();
        JsonObject jsonObject;
        try {
            jsonObject = new Gson().fromJson(content, JsonObject.class);
            if (jsonObject == null) {
                return null;
            }
            model = gson.fromJson(
                gson.toJson(jsonObject.getAsJsonObject("data")), DualDubResultModel.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return model;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public List<DualDubMaterialBean> getResult() {
        return result;
    }

    public void setResult(List<DualDubMaterialBean> result) {
        this.result = result;
    }
}
