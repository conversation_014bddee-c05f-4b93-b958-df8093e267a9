package com.ximalaya.ting.android.record.util.tasks;

import com.ximalaya.ting.android.opensdk.util.WeakReferenceAsyncTask;
import com.ximalaya.ting.android.record.manager.other.RecCacheDirManager;
import com.ximalaya.ting.android.record.util.RecordBundleUtils;
import com.ximalaya.ting.android.record.util.tasks.CopyCountdownSoundTask.CopyCountdownSoundListener;

/**
 * Created by zhangkaikai on 2019/3/11.
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @PhoneNumber 15721173906
 */
public final class CopyCountdownSoundTask
    extends WeakReferenceAsyncTask<CopyCountdownSoundListener, Void, Integer, Boolean> {

    public CopyCountdownSoundTask(CopyCountdownSoundListener listener) {
        super(listener);
    }

    @Override
    protected Boolean doInBackground(Void... voids) {
        return RecordBundleUtils.copyFileFromAssetsIfNeed("isHadCopyCountDownSound",
            RecCacheDirManager.getInstance().getCountDownSoundPath(), "count_down_sound.mp3");
    }

    @Override
    protected void onPostExecute(Boolean isSuccess) {
        CopyCountdownSoundListener listener = getReferenceObject();
        if (listener == null) {
            return;
        }

        if (isSuccess) {
            listener.onCopySuccess();
        } else {
            listener.onCopyFailed();
        }
    }

    public interface CopyCountdownSoundListener {

        void onCopyFailed();

        void onCopySuccess();
    }
}
