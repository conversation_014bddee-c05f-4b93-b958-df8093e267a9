package com.ximalaya.ting.android.record.fragment.dub.wrapper;

import android.view.View;
import android.widget.AdapterView;

/**
 * Created by ervin.li on 2019/1/25.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class OnItemClickListenerWrapper extends BaseWrapper<AdapterView.OnItemClickListener> implements AdapterView.OnItemClickListener {
    public OnItemClickListenerWrapper(AdapterView.OnItemClickListener onItemClickListener) {
        super(onItemClickListener);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        if(getWrapContent()!=null){
            getWrapContent().onItemClick(parent,view,position,id);
        }
    }
}
