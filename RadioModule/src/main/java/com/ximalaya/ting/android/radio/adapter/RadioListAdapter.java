package com.ximalaya.ting.android.radio.adapter;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.radio.R;
import com.ximalaya.ting.android.radio.data.model.TypeRadio;
import com.ximalaya.ting.android.radio.fragment.RadioContentFragmentNew;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;


/**
 * 电台list的Adapter
 *
 * <AUTHOR>
 */
public class RadioListAdapter extends HolderAdapter<Radio> {

    public final static int TYPE_RANK = 1;
    public final static int TYPE_SEARCH = 2;//搜索条

    private boolean isBuffering;
    private Fragment mFragment;
    private boolean hasRankNum = false;    // 是否显示榜单排序
    private boolean hasSearchArrow = false; //搜索条专用是否将播放图标显示为箭头
    private IXmPlayerStatusListener playListener = new IFragmentFinish.ISimpleOnPlayerStatusUpdateListener() {
        @Override
        public void onPlayStateChange() {
            notifyDataSetChanged();
        }

        @Override
        public boolean onError(XmPlayerException exception) {
            return super.onError(exception);
        }

        @Override
        public void onBufferingStart() {
            super.onBufferingStart();
            isBuffering = true;
            notifyDataSetChanged();
            Logger.i("yjs_","onBufferingStart");
        }

        @Override
        public void onBufferingStop() {
            super.onBufferingStop();
            isBuffering = false;
            notifyDataSetChanged();
            Logger.i("yjs_","onBufferingStop");
        }
    };
    private FragmentActivity mActivity;

    public RadioListAdapter(Context context, List<Radio> listData) {
        super(context, listData);
    }

    public void setFragment(Fragment fragment) {
        mFragment = fragment;
    }

    public void setType(int type) {
        typeToView(type);
    }

    /**
     * 将Type转换为是否显示的东西
     *
     * @param type
     */
    private void typeToView(int type) {
        if (type == TYPE_RANK) {
            hasRankNum = true;
        } else if (type == TYPE_SEARCH) {
            hasSearchArrow = true;
        }
    }

    @Override
    public void onClick(View view, Radio t, int position, BaseViewHolder holder) {
        if (view.getId() == R.id.radio_play_icon) {
            if (mActivity != null) {
                PlayTools.LivePlayBtnToClick(mActivity, t, view);
                trackClickRadioItemPlayBtn(mActivity, t);
                return;
            }
            if (mFragment != null) {
                PlayTools.LivePlayBtnToClick(mFragment.getActivity(), t, view);
                trackClickRadioItemPlayBtn(mFragment.getActivity(), t);
            }
        }
    }

    /**
     * 首页_广播-排行榜、最近听、城市模块点击广播条播放&暂停按钮
     *
     * @param activity
     * @param radio
     */
    private void trackClickRadioItemPlayBtn(Activity activity, Radio radio) {
        if (activity == null || radio == null ) return;
        if (!(radio instanceof TypeRadio)) return;

        TypeRadio typeRadio = (TypeRadio) radio;

        String status = "play";
        boolean isPlayingCurrentRadio = PlayTools.isPlayCurrRadioById(context, radio.getDataId());

        if (isPlayingCurrentRadio) {
            boolean isPlaying = XmPlayerManager.getInstance(activity)
                    .isPlaying();
            status = isPlaying ? "play" : "pause";
        }

        String radioType = typeRadio.getRadioType();

        UserTracking userTracking = new UserTracking()
                .setSrcPage("首页_广播")
                .setSrcModule(radioType)
                .setItem("button")
                .setItemId(status)
                .putParam("radioId", String.valueOf(radio.getDataId()));

        if ("cityRadio".equals(radioType)) {
            userTracking.putParam("cityName", TextUtils.isEmpty(RadioContentFragmentNew.sCurrentCityName) ? "" : RadioContentFragmentNew.sCurrentCityName);
        }

        userTracking.statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);

        Logger.d("radio_ubt", ",radioType: " + radioType + ", 点击广播条播放&暂停按钮, status: " + status
                + ", radioId: " + radio.getDataId() + ", cityName: " + RadioContentFragmentNew.sCurrentCityName);
    }

    @Override
    public int getConvertViewId() {
        return R.layout.radio_item_live_fm_list;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void bindViewDatas(BaseViewHolder viewHolder, Radio model, int position) {
        ViewHolder holder = (ViewHolder) viewHolder;

        if (hasRankNum) {
            refreshRankPosition(holder.rankTxt, position);
        } else {
            holder.rankTxt.setVisibility(View.GONE);
        }

        ImageManager.from(context).displayImage(holder.radioCoverImg,
                TextUtils.isEmpty(model.getCoverUrlLarge()) ? model.getCoverUrlSmall() : model.getCoverUrlLarge(),
                com.ximalaya.ting.android.host.R.drawable.host_default_album);
        holder.radioNameTxt.setText(model.getRadioName());
        if (TextUtils.isEmpty(model.getProgramName()) || model.getProgramName().contains("null")) {
            holder.programNameTxt.setText(AppConstants.LIVE_PROGRAM_DEFAULT_TEXT);
        } else {
            holder.programNameTxt.setText("正在直播： " + model.getProgramName());
        }
        if (model.getRadioPlayCount() == 0) {
            holder.radioPlayCountTxt.setVisibility(View.GONE);
        } else {
            holder.radioPlayCountTxt.setText(StringUtil.getFriendlyNumStr(model.getRadioPlayCount()) + "人");
            holder.radioPlayCountTxt.setVisibility(View.VISIBLE);
        }

        if (!hasSearchArrow) {
            if (PlayTools.isPlayCurrRadioById(context, model.getDataId())) {
                if (isBuffering) {
                    startLoading(holder.playImg);
                } else {
                    AnimationUtil.stopAnimation(holder.playImg);
                    holder.playImg.setImageResource(!XmPlayerManager.getInstance(context).isPlaying() ? R.drawable.radio_fm_play
                            : R.drawable.radio_fm_pause);
                }
            } else {
                AnimationUtil.stopAnimation(holder.playImg);
                holder.playImg.setImageResource(R.drawable.radio_fm_play);
            }
            setClickListener(holder.playImg, model, position, viewHolder);
            AutoTraceHelper.bindData(holder.playImg, AutoTraceHelper.MODULE_DEFAULT, model);
        } else {
            ViewGroup.LayoutParams layoutParams = holder.playImg.getLayoutParams();
            layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT;
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            holder.playImg.setLayoutParams(layoutParams);
            AnimationUtil.stopAnimation(holder.playImg);
            holder.playImg.setImageResource(R.drawable.radio_btn_more_selector);
        }

        if (getListData() == null) {
            holder.divider.setVisibility(View.VISIBLE);
        } else {
            holder.divider.setVisibility((position == getListData().size() - 1) ? View.GONE : View.VISIBLE);
        }

    }

    private void refreshRankPosition(TextView rankNumView, int rankPos) {
        rankNumView.setVisibility(View.VISIBLE);
        rankNumView.setText(++rankPos + "");
        if (rankPos == 1) {
            rankNumView.setTextColor(Color.parseColor("#F86442"));
        } else if (rankPos == 2) {
            rankNumView.setTextColor(Color.parseColor("#f79100"));
        } else if (rankPos == 3) {
            rankNumView.setTextColor(Color.parseColor("#9ebc0c"));
        } else if (rankPos > 3) {
            rankNumView.setTextColor(Color.parseColor("#999999"));
        }
    }

    public void setActivity(FragmentActivity activity) {
        mActivity = activity;
    }

    public IXmPlayerStatusListener getXmPlayerStatuListener() {
        return playListener;
    }

    /**
     * 20191225 TODO 暂时解决跳转播放页返回时播放按钮状态仍然一直转圈（几乎必现）
     *  可能会存在：如果播放状态仍然为buffer，不会恢复这个状态
     * RadioListFragment 和 RadioContentFragment两处引用皆有notifyDataChange
     */
    public void updatePlayStatu(){
        isBuffering = false;
        Logger.i("yjs_","updatePlayStatu");
    }

    public void setXmPlayerStatuListener(IXmPlayerStatusListener listener) {
        this.playListener = null;
    }

    private void startLoading(final ImageView ivPlayFlag) {
        ivPlayFlag.setImageResource(R.drawable.radio_fm_icon_loading);
        AnimationUtil.rotateView(context, ivPlayFlag);
    }

    private void stopLoading(ImageView ivPlayFlag) {
        AnimationUtil.stopAnimation(ivPlayFlag);
        ivPlayFlag.setImageResource(R.drawable.radio_fm_pause);
    }

    public static class ViewHolder extends BaseViewHolder {
        public final TextView rankTxt;
        public final ImageView radioCoverImg;
        public final TextView radioNameTxt;
        public final TextView programNameTxt;
        public final TextView radioPlayCountTxt;
        public final ImageView playImg;
        public final View divider;

        public ViewHolder(View convertView) {
            rankTxt = (TextView) convertView.findViewById(R.id.radio_rank);
            radioCoverImg = (ImageView) convertView.findViewById(R.id.radio_fm_img);
            radioNameTxt = (TextView) convertView.findViewById(R.id.radio_fm_name);
            programNameTxt = (TextView) convertView.findViewById(R.id.radio_program_name);
            radioPlayCountTxt = (TextView) convertView.findViewById(R.id.radio_tv_play_count);
            divider = convertView.findViewById(R.id.radio_list_divider);
            playImg = (ImageView) convertView.findViewById(R.id.radio_play_icon);
        }
    }
}
