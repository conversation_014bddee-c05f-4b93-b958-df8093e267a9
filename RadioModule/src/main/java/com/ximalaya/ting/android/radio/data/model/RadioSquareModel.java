package com.ximalaya.ting.android.radio.data.model;

import java.util.List;

public class RadioSquareModel {
    /**
     * {"type":1,"id":0,"name":"上海台","sort":0},
     * {"type":2,"id":0,"name":"国家台","sort":2},
     * {"type":3,"id":0,"name":"省市台","sort":3},
     * {"type":4,"id":0,"name":"网络台","sort":4}
     *
     * */
    private int type;
    private int id;
    private String name;
    private int sort;
    private int showSort;
    private String cover;
    private List<RadioProvinceModel> provinces;
    private String locationProvince;

    public String getLocationProvince() {
        return locationProvince;
    }

    public void setShowSort(int showSort) {
        this.showSort = showSort;
    }

    public String getValidId() {
        return getName() + " " + getShowSort();
    }

    public int getShowSort() {
        return showSort;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public List<RadioProvinceModel> getProvinces() {
        return provinces;
    }

    public void setProvinces(List<RadioProvinceModel> provinces) {
        this.provinces = provinces;
    }
}
