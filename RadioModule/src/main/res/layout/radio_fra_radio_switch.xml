<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/radio_bg_rect_ffffff_radius_10"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="18dp"
        android:layout_marginBottom="3dp"
        android:text="电台列表"
        android:textColor="@color/host_color_333333_cfcfcf"
        android:textSize="18sp" />

    <com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
        android:id="@+id/radio_switch_listview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:layout_weight="1"
        android:clipToPadding="true"
        android:divider="@null"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:listSelector="@color/radio_color_eeeeee_353535"
        android:overScrollMode="never"
        android:scrollbars="none" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/radio_color_eeeeee_353535" />

    <TextView
        android:id="@+id/radio_switch_close_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="18dp"
        android:paddingBottom="18dp"
        android:text="关闭"
        android:textColor="@color/radio_color_333333_cccccc"
        android:textSize="15sp" />

</LinearLayout>