package com.ximalaya.ting.android.localserver;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.util.Locale;

import static com.ximalaya.ting.android.localserver.ProxyCacheUtils.DEFAULT_BUFFER_SIZE;

/**
 * <AUTHOR>
 * @date 2023/9/6 21:26
 */
public class HttpProxyAssetsSource {
    private static final String TAG = "HttpProxyAssetsSource";

    public void processRequest(String url, Socket socket) throws IOException {
        OutputStream out = new BufferedOutputStream(socket.getOutputStream());
        StringBuilder sb = new StringBuilder();

        InputStream inputStream = BaseApplication.getMyApplicationContext().getAssets().open(url);

        sb.append("HTTP/1.1 200 OK\n");
        sb.append("Accept-Ranges: bytes\n");
        if (url.endsWith(".html") || url.endsWith(".htm")) {
            sb.append(format("Content-Type: %s\n", "text/html"));
        } else if (url.endsWith(".js")) {
            sb.append(format("Content-Type: %s\n", "application/javascript"));
        } else if (url.endsWith(".jpg")) {
            sb.append(format("Content-Type: %s\n", "image/jpeg"));
        } else if (url.endsWith(".ogg")) {
            sb.append(format("Content-Type: %s\n", "audio/ogg"));
        } else if (url.endsWith(".css")) {
            sb.append(format("Content-Type: %s\n", "text/css"));
        } else if (url.endsWith(".ico")) {
            sb.append(format("Content-Type: %s\n", "image/png"));
        }
        int contentLength = inputStream.available();
        sb.append(format("Content-Length: %d\n", contentLength));
        sb.append("\n");
        out.write(sb.toString().getBytes("UTF-8"));
        Logger.i(TAG, "HttpProxyAssetsSource processRequest url=" + url + ",headers=" + sb);

        byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
        int readBytes;
        int offset = 0;
        while ((readBytes = inputStream.read(buffer)) != -1) {
            out.write(buffer, 0, readBytes);
            offset += readBytes;
        }
        out.flush();
    }

    private String format(String pattern, Object... args) {
        return String.format(Locale.US, pattern, args);
    }
}
