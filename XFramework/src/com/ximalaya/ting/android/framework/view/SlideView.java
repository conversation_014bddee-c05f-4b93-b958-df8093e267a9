package com.ximalaya.ting.android.framework.view;

import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Scroller;

import androidx.annotation.ColorRes;
import androidx.annotation.DrawableRes;
import androidx.annotation.IntDef;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.R;
import com.ximalaya.ting.android.framework.util.BaseUtil;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 *
 * ClassName:SlideView<br>
 * Function: 一个继承于{@link FrameLayout}的{@link ViewGroup}，可以侧滑退出。
 *
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2014 2014-3-21 下午4:12:35
 *
 * @see
 */
public class SlideView extends FrameLayout {

	private Context mContext;
	private View mainView;
	private ViewGroup contentView;
	private int screenWidth;

	public static final int TYPE_FRAMELAYOUT = 0;
	public static final int TYPE_RELATIVELAYOUT = 1;
	public static final int TYPE_LINEARLAYOUT = 2;
	public static final int TYPE_LINEARLAYOUT_VERTICAL = 3;

	public static final int TYPE_RELATIVE_SLIDE_VERTICAL = 4;//myclub个人页面使用
	private View leftShadeView;
    private int mContentViewBackgroundColor = R.color.framework_color_ffffff_121212;
    private boolean fullSlideAble = true;
	private int edgeMargin;

	@IntDef({TYPE_FRAMELAYOUT, TYPE_RELATIVELAYOUT, TYPE_LINEARLAYOUT, TYPE_LINEARLAYOUT_VERTICAL})
	@Retention(RetentionPolicy.SOURCE)
	public @interface SlideViewContentViewLayoutType {
	}

	@SlideViewContentViewLayoutType
	private int currentContentViewType = TYPE_FRAMELAYOUT;

	public void setContentViewLayoutType(@SlideViewContentViewLayoutType int layoutType){
		currentContentViewType = layoutType;
	}

	public SlideView(Context context, @SlideViewContentViewLayoutType int contentType) {
        this(context, contentType, R.color.framework_color_ffffff_121212);
    }

	public SlideView(Context context, @SlideViewContentViewLayoutType int contentType, @ColorRes int contentViewBackgroundColor) {
		super(context);
		currentContentViewType = contentType;
		mContentViewBackgroundColor = contentViewBackgroundColor;
		init(context);
	}

	public SlideView(Context context, AttributeSet attrs) {
		super(context, attrs);
		init(context);
	}

	public SlideView(Context context, AttributeSet attrs, int defStyle) {
		super(context, attrs, defStyle);
		init(context);
	}

	public ViewGroup getContentView() {
		return contentView;
	}

	public View getLeftShadeView() {
		return leftShadeView;
	}

	private void init(Context context) {
		mContext = context;
		mTouchSlop = ViewConfiguration.get(mContext).getScaledTouchSlop();
		mScroller = new Scroller(mContext);
		setBackgroundResource(0);
		if (mContext instanceof Activity) {
			screenWidth = ((Activity) mContext).getWindowManager()
					.getDefaultDisplay().getWidth();
		}
		leftShadeView = new View(context);
        leftShadeView.setBackgroundResource(R.drawable.framework_bg_shadow_left);
        int screenWidth = BaseUtil.getScreenWidth(context);
        int shadeWidth = screenWidth<=0?BaseUtil.dp2px(context, 600):screenWidth;
		LayoutParams shadeViewLp = new LayoutParams(shadeWidth, LayoutParams.MATCH_PARENT);
		shadeViewLp.setMargins(-shadeWidth,0,0,0);
		addView(leftShadeView,shadeViewLp);
		if (currentContentViewType == TYPE_RELATIVELAYOUT){
			contentView = new RelativeLayout(context);
		} else if(currentContentViewType == TYPE_LINEARLAYOUT){
			contentView = new LinearLayout(context);
		} else if (currentContentViewType == TYPE_LINEARLAYOUT_VERTICAL) {
			contentView = new LinearLayout(context);
			((LinearLayout) contentView).setOrientation(LinearLayout.VERTICAL);
		} else{
			contentView = new FrameLayout(context);
		}
		contentView.setClickable(true);
		contentView.setBackgroundColor(getResources().getColor(mContentViewBackgroundColor));
		LayoutParams contentViewLp= new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
		addView(contentView,contentViewLp);

		mainView = this;
		edgeMargin = BaseUtil.dp2px(mContext ,20);
	}

	private Scroller mScroller;
	private VelocityTracker mVelocityTracker;
	private int mTouchSlop;
	private float mLastMotionX;
	private float mSlideMoveX;
	private float mLastMotionY;
	private static final int VELOCITY = 50;
	private boolean mIsBeingDragged = false;
	private boolean canSlide = true;

	@Override
	public boolean onInterceptTouchEvent(MotionEvent ev) {
		if (canSlide) {
			final int action = ev.getAction();
			final float x = ev.getX();
			final float y = ev.getY();
			switch (action) {
			case MotionEvent.ACTION_DOWN:
				mLastMotionX = x;
				mLastMotionY = y;
				mIsBeingDragged = false;
				break;
			case MotionEvent.ACTION_MOVE:
				if(!fullSlideAble && mLastMotionX > edgeMargin) {
					return super.onInterceptTouchEvent(ev);
				}
				final float dx = x - mLastMotionX;
				final float xDiff = Math.abs(dx);
				final float yDiff = Math.abs(y - mLastMotionY);
				if (dx < 0 && xDiff > mTouchSlop)
					return false;

				if (xDiff > mTouchSlop) {
					if (xDiff > yDiff) {
						mIsBeingDragged = true;
						isFirst = true;
					}
				}
				break;
			}
			if (mIsBeingDragged)
				return mIsBeingDragged;
			else
				return super.onInterceptTouchEvent(ev);
		} else {
			return super.onInterceptTouchEvent(ev);
		}
	}

	private boolean isMoveing = false;

	@Override
	public boolean onTouchEvent(MotionEvent ev) {
		if (mVelocityTracker == null) {
			mVelocityTracker = VelocityTracker.obtain();
		}
		mVelocityTracker.addMovement(ev);

		final int action = ev.getAction();
		final float x = ev.getX();
		final float y = ev.getY();
		if (isFirst) {
			isFirst = false;
			if (mSlideListener != null)
				mSlideListener.slideStart();
			if (mSlideMoveXChangeListener != null) {
				mSlideMoveXChangeListener.slideStart(x);
			}
		}

		switch (action) {
		case MotionEvent.ACTION_DOWN:   //CODEREVIEW by easoll 这部分不会执行, 可以删除
			isMoveing = true;
			if (!mScroller.isFinished()) {
				mScroller.abortAnimation();
			}
			mLastMotionX = x;
			mLastMotionY = y;
			break;
		case MotionEvent.ACTION_MOVE:
			isMoveing = true;
			if (mainView == null)
				break;

			final float deltaX = mLastMotionX - x;
			mLastMotionX = x;
			float tOldScrollX = mainView.getScrollX();
			float scrollX = tOldScrollX + deltaX;
			if (scrollX > 0) {
				scrollX = 0;
			}
			if (deltaX < 0 && tOldScrollX < 0) { // left view
				final float leftBound = 0;
				final float rightBound = -screenWidth;
				if (scrollX > leftBound) {
					scrollX = leftBound;
				} else if (scrollX < rightBound) {
					scrollX = rightBound;
				}
			}
			mSlideMoveX = scrollX;
			if (mSlideMoveXChangeListener != null) {
				mSlideMoveXChangeListener.slideMove(mSlideMoveX);
			}
			mainView.scrollTo((int) scrollX, mainView.getScrollY());
			changeAlpha();
			break;
		case MotionEvent.ACTION_CANCEL:
		case MotionEvent.ACTION_UP:
			isMoveing = false;
			if (mainView == null)
				break;

			if (mSlideListener != null)
				mSlideListener.slideEnd();

			final VelocityTracker velocityTracker = mVelocityTracker;
			velocityTracker.computeCurrentVelocity(100);
			float xVelocity = velocityTracker.getXVelocity();
			int oldScrollX = mainView.getScrollX();
			int dx = 0;

			if (oldScrollX <= 0) {// left view
				if (xVelocity > VELOCITY) {
					dx = -screenWidth - oldScrollX;
				} else if (xVelocity < -VELOCITY) {
					dx = -oldScrollX;
				} else if (oldScrollX < -screenWidth / 2) {
					dx = -screenWidth - oldScrollX;
				} else if (oldScrollX >= -screenWidth / 2) {
					dx = -oldScrollX;
				}

			}
			if (oldScrollX > 0) {
				dx = -oldScrollX;
			}
//			if (mSlideMoveXChangeListener != null) {
//				mSlideMoveXChangeListener.slideEnd(dx);
//			}
			smoothScrollTo(dx);
			break;
		}
		return true;
	}

	private void smoothScrollTo(int dx) {

		if (mainView == null)
			return;

		int duration = 500;
		int oldScrollX = mainView.getScrollX();
		mScroller.startScroll(oldScrollX, mainView.getScrollY(), dx,
				mainView.getScrollY(), duration);
		invalidate();
	}

	@Override
	public void computeScroll() {

		if (mainView == null)
			return;

		if (!mScroller.isFinished()) {
			if (mScroller.computeScrollOffset()) {
				int oldX = mainView.getScrollX();
				int oldY = mainView.getScrollY();
				int x = mScroller.getCurrX();
				int y = mScroller.getCurrY();
				if (oldX != x || oldY != y) {
					mainView.scrollTo(x, y);
					if (mSlideMoveXChangeListener != null) {
						mSlideMoveXChangeListener.slideEnd(x);
					}
					changeAlpha();
					if (mainView.getScrollX() < -screenWidth + 10) {
						finish();
					}
					if (mainView.getScrollX() == 0 && mSlideListener != null) {//该页面没有finish
						mSlideListener.keepFragment();
						if (mSlideMoveXChangeListener != null) {
							mSlideMoveXChangeListener.keepFragment();
						}
					}
				}
				postInvalidate();
			}
		}else{
			if(!isMoveing) {
				setBackgroundDrawable(null);
			}
		}
	}

	// 某些页面比如配音秀播放页,支持侧滑之后,只是hide了fragment,如果再从肚几眼点进去就会出现界面已经被scoller 之后的状态
	public void resetScrollXState() {
		if(mainView != null) {
			isFinish = false;
			mainView.scrollTo(0 ,mainView.getScrollY());
			postInvalidate();
		}
	}

	public void show() {
		if (mainView != null) {
			mainView.setVisibility(View.VISIBLE);
		}
	}

	private boolean isFinish = false;

	public boolean isFinished() {
		return isFinish;
	}

	private void finish() {

		if (isFinish)
			return;

		isFinish = true;

		if (mContext == null)
			return;
		if (mOnFinishListener != null) {
			boolean handle = mOnFinishListener.onFinish();
			if (handle) {
				return;
			}
		}

		if (mainView != null)
			mainView.setVisibility(View.GONE);

		if (mContext instanceof Activity) {
			if (!((Activity) mContext).isFinishing()) {
				((Activity) mContext).finish();
			}
		}

	}

	private boolean isFirst = true;
	private SlideListener mSlideListener;
	private SlideMoveXChangeListener mSlideMoveXChangeListener;
    private @Nullable
    IOnFinishListener mOnFinishListener;

	public void setSlideMoveXChangeListener(SlideMoveXChangeListener slideMoveXChangeListener) {
		mSlideMoveXChangeListener = slideMoveXChangeListener;
	}

	public void setOnSlideListener(SlideListener l) {
		mSlideListener = l;
	}

	public void setSlide(boolean canSlide) {
		this.canSlide = canSlide;
	}


	/**
	 * 直接使用setSlide
	 * @param forbidSlide
	 */
	@Deprecated
	public void setForbidSlide(boolean forbidSlide){
		this.canSlide = !forbidSlide;
	}

	public void setOnFinishListener(IOnFinishListener l) {
		mOnFinishListener = l;
	}

	public interface SlideListener {
		void slideStart();
		void slideEnd();
		void keepFragment();//还在当前页面
	}

	public interface SlideMoveXChangeListener {
		void slideStart(float slideMoveX);
		void slideMove(float slideMoveX);
		void slideEnd(float slideMoveX);
		void keepFragment();//还在当前页面
	}

	public interface VerticalSlideListener {
		void slideStart();
		void slideEnd();
		void onHeightChange(boolean up, int scrollY);
	}

	public interface SlideMotionEventListener {
		boolean interceptMotionEvent(MotionEvent motionEvent);
	}

	public interface IOnFinishListener {
		/**
		 * onFinish:This method will be call when the view is out of screen
		 *
		 * @param @return 设定文件
		 * @return boolean return true represent the finish event is consume
		 * @throws
		 * @since CodingExample　Ver 1.1
		 */
		boolean onFinish();
	}

	private void changeAlpha() {
		if (leftShadeView == null) {
			return;
		}

		float deltaPha = (screenWidth * 1.0f + mainView.getScrollX()) / screenWidth;
		leftShadeView.setAlpha(deltaPha);
	}

	public void setContentViewBackgroundResource(@DrawableRes int resid) {
		if(contentView!=null){
			contentView.setBackgroundResource(resid);
		}
	}

	public void setFullSlideAble(boolean fullSlideAble) {
		this.fullSlideAble = fullSlideAble;
	}

	//	@Override
//	public void addView(View child, int width, int height) {
//
//		addView(child, width, height);
//
//	}
//
//	@Override
//	public void addView(View child, int index, ViewGroup.LayoutParams params) {
//		if (contentView != null) {
//			contentView.addView(child, index, params);
//		}
//	}
//
//	@Override
//	public void addView(View child, int index) {
//		ViewGroup.LayoutParams params = child.getLayoutParams();
//		if (params == null) {
//			params = generateDefaultLayoutParams();
//			if (params == null) {
//				throw new IllegalArgumentException(
//						"generateDefaultLayoutParams() cannot return null");
//			}
//		}
//		addView(child, index, params);
//
//	}
//
//	@Override
//	public void addView(View child, ViewGroup.LayoutParams params) {
//
//		addView(child, -1, params);
//
//	}
//
//	@Override
//	public void addView(View child) {
//
//		addView(child, -1);
//
//	}

}
