package com.ximalaya.ting.android.framework.startup;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Intent;

import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * <AUTHOR>
 * @date 2021/11/1 19:14
 */
public class StartUpStatisticsManager {
    private static final String TAG = "startUpManager";

    private boolean mHasInit = false;
    private StartUpCallback mStartUpCallback;
    private Application mApplication;
    private XmStartUpActivityLifecycle mStartUpActivityLifecycle;
    private long mLastPausedTime = 0;
    private long mLastStoppedTime = 0;
    private boolean mFirstStart = true;
    private static int sStartUpTimeInterval = 30; // 30s

    private StartUpStatisticsManager() {
    }

    private static class StartUpStatisticsManagerHolder {
        private static final StartUpStatisticsManager INSTANCE = new StartUpStatisticsManager();
    }

    public static StartUpStatisticsManager getInstance() {
        return StartUpStatisticsManagerHolder.INSTANCE;
    }

    public void setStartUpCallback(StartUpCallback startUpCallback) {
        mStartUpCallback = startUpCallback;
    }

    public static int getStartUpTimeInterval() {
        return sStartUpTimeInterval;
    }

    public static void setStartUpTimeInterval(int startUpTimeInterval) {
        sStartUpTimeInterval = startUpTimeInterval;
    }

    public Boolean isAppForeground() {
        if (mStartUpActivityLifecycle != null) {
            return mStartUpActivityLifecycle.isAppForeground();
        }
        return null;
    }

    public void init(Application application, StartUpCallback startUpCallback) {
        if (mHasInit) {
            return;
        }
        mHasInit = true;
        if (startUpCallback == null) {
            startUpCallback = new StartUpCallbackImpl();
        }
        mStartUpCallback = startUpCallback;

        if (application == null) {
            mApplication = getApplicationByReflect();
        } else {
            mApplication = application;
        }
        if (mApplication != null) {
            mStartUpActivityLifecycle = new XmStartUpActivityLifecycle();
            mApplication.registerActivityLifecycleCallbacks(mStartUpActivityLifecycle);
            mStartUpActivityLifecycle.addOnAppStatusChangedListener(mAppStatusChangedListener);
        }

        sStartUpTimeInterval = MmkvCommonUtil.getInstance(mApplication).getInt(
                PreferenceConstantsInOpenSdk.ITEM_START_UP_TIME_INTERVAL, 30);
    }

    private IOnAppStatusChangedListener mAppStatusChangedListener = new IOnAppStatusChangedListener() {
        @Override
        public void onForeground(Intent it) {
            activityOnForeground();
        }

        @Override
        public void onBackground(Intent it, boolean fromPause) {
            activityOnBackground(fromPause);
        }
    };

    private void activityOnForeground() {
        if (!mHasInit) {
            return;
        }
        Logger.d("zimotag", "StartUpStatisticsManager activityForeground: ");
        if (mFirstStart) {
            mFirstStart = false;
            Logger.d("zimotag", "StartUpStatisticsManager activityOnForeground: 第一次启动 不上报数据");
            return;
        }
        if (mStartUpCallback == null) {
            return;
        }
        if (mLastPausedTime <= 0) {
            // 这里小于0说明activity没有经历过pause，只走了resume的过程，这里应该直接return掉
            return;
        }
        long curTime = System.currentTimeMillis();
        if (mLastStoppedTime >= mLastPausedTime && curTime - mLastStoppedTime > sStartUpTimeInterval * 1000L) {
            // pause之后有stop操作 并且stop到resume时间超过设置时间间隔
            mStartUpCallback.activityForeground(false);
        } else if (mLastStoppedTime < mLastPausedTime && curTime - mLastPausedTime > sStartUpTimeInterval * 1000L) {
            // pause之后 无stop操作 并且pause到resume时间超过设置的时间间隔
            mStartUpCallback.activityForeground(true);
        } else {
            Logger.d("zimotag", "StartUpStatisticsManager activityOnForeground: callback not triggered");
        }
    }

    private void activityOnBackground(boolean fromPause) {
        if (!mHasInit) {
            return;
        }
        long time = System.currentTimeMillis();
        if (fromPause) {
            mLastPausedTime = time;
        } else {
            mLastStoppedTime = time;
        }
    }

    public void activityCreate() {
        if (!mHasInit) {
            return;
        }
        if (mStartUpCallback != null) {
            mStartUpCallback.activityCreate();
        }
        Logger.d("zimotag", "StartUpStatisticsManager activityCreate: ");
    }

    public void serviceCreate() {
        if (!mHasInit) {
            return;
        }
        if (mStartUpCallback != null) {
            mStartUpCallback.serviceCreate();
        }
        Logger.d("zimotag", "StartUpStatisticsManager serviceCreate: ");
    }

    private static Application getApplicationByReflect() {
        try {
            @SuppressLint("PrivateApi")
            Class<?> activityThread = Class.forName("android.app.ActivityThread");
            Object thread = activityThread.getMethod("currentActivityThread").invoke(null);
            Object app = activityThread.getMethod("getApplication").invoke(thread);
            if (app == null && ConstantsOpenSdk.isDebug) {
                throw new NullPointerException("u should init first");
            }
            return (Application) app;
        } catch (Exception e) {
            Logger.i(TAG, e.getMessage());
        }
        if (ConstantsOpenSdk.isDebug) {
            throw new NullPointerException("u should init first");
        }
        return null;
    }
}
