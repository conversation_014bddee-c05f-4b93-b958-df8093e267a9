package com.ximalaya.ting.android.framework.util;

import android.annotation.TargetApi;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;

import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * Created by roc on 2016/8/12.
* <AUTHOR>
 */
@TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
public class ActivityManagerDetacher implements Application.ActivityLifecycleCallbacks {

    private int appCount = 0;

    @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
    public int getAppCount() {
        return appCount;
    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        try {
            detachActivityFromActivityManager(activity);
        } catch (NoSuchFieldException e) {
            Logger.log("Samsung activity leak fix has to be removed as ActivityManager field has changed"+e);
        } catch (IllegalAccessException e) {
            Logger.log("Samsung activity leak fix did not work, probably activity has leaked"+e);
        }

        for (Application.ActivityLifecycleCallbacks activityLifeCallBack : mActivityLifeCallBacks) {
            activityLifeCallBack.onActivityDestroyed(activity);
        }
    }

    /**
     * fuck Samsung,On Samsung, Activity reference is stored into static mContext field of ActivityManager
     * resulting in activity leak.
     */
    private void detachActivityFromActivityManager(Activity activity) throws
            NoSuchFieldException, IllegalAccessException {
        ActivityManager activityManager = (ActivityManager) activity.
                getSystemService(Context.ACTIVITY_SERVICE);

        Field contextField = activityManager.getClass().getDeclaredField("mContext");

        int modifiers = contextField.getModifiers();
        if ((modifiers | Modifier.STATIC) == modifiers) {
            // field is static on Samsung devices only
            contextField.setAccessible(true);

            if (contextField.get(null) == activity) {
                contextField.set(null, null);
            }
        }
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        for (Application.ActivityLifecycleCallbacks activityLifeCallBack : mActivityLifeCallBacks) {
            activityLifeCallBack.onActivityCreated(activity ,savedInstanceState);
        }
    }

    @Override
    public void onActivityStarted(Activity activity) {
        if(appCount == 0){  //应用回到前台
            for(AppStatusListener listener : mAppStatusListeners){
                if (listener != null){
                    listener.onAppGoToForeground(activity);
                }
            }

            XmPlayerManager.getInstance(activity).showNotificationOnResume();
        }
        setAppCount(appCount + 1);

        for (Application.ActivityLifecycleCallbacks activityLifeCallBack : mActivityLifeCallBacks) {
            activityLifeCallBack.onActivityStarted(activity);
        }
    }

    @Override
    public void onActivityResumed(Activity activity) {
        for (Application.ActivityLifecycleCallbacks activityLifeCallBack : mActivityLifeCallBacks) {
            activityLifeCallBack.onActivityResumed(activity);
        }
    }

    @Override
    public void onActivityPaused(Activity activity) {
        for (Application.ActivityLifecycleCallbacks activityLifeCallBack : mActivityLifeCallBacks) {
            activityLifeCallBack.onActivityPaused(activity);
        }
    }

    @Override
    public void onActivityStopped(Activity activity) {
        setAppCount(appCount - 1);
        if(appCount == 0){ //应用退至后台
            for(AppStatusListener listener : mAppStatusListeners){
                if (listener != null){
                    listener.onAppGoToBackground(activity);
                }
            }
            MmkvCommonUtil.getInstance(activity).saveLong(PreferenceConstantsInOpenSdk.KEY_APP_LAST_GO_TO_BACKGROUND_TIME, System.currentTimeMillis());
//            MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInOpenSdk.KEY_HOME_SNACK_BAR_OR_FIREWOKR_SHOWING, false);
        }

        for (Application.ActivityLifecycleCallbacks activityLifeCallBack : mActivityLifeCallBacks) {
            activityLifeCallBack.onActivityStopped(activity);
        }
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
        for (Application.ActivityLifecycleCallbacks activityLifeCallBack : mActivityLifeCallBacks) {
            activityLifeCallBack.onActivitySaveInstanceState(activity, outState);
        }
    }

    public interface AppStatusListener{
        void onAppGoToForeground(Activity startedActivity);

        void onAppGoToBackground(Activity stoppedActivity);
    }

    private List<AppStatusListener> mAppStatusListeners = new CopyOnWriteArrayList<>();

    public void addAppStatusListener(AppStatusListener listener){
        if(listener != null && !mAppStatusListeners.contains(listener)){
            mAppStatusListeners.add(listener);
        }
    }

    public void removeAppStatusListener(AppStatusListener listener){
        if(listener != null){
            mAppStatusListeners.remove(listener);
        }
    }

    private Set<Application.ActivityLifecycleCallbacks> mActivityLifeCallBacks = new CopyOnWriteArraySet<>();
    public void addActivityLifeListener(Application.ActivityLifecycleCallbacks listener) {
        if(listener != null) {
            mActivityLifeCallBacks.add(listener);
        }
    }

    public void removeActivityLifeListener(Application.ActivityLifecycleCallbacks listener) {
        if(listener != null) {
            mActivityLifeCallBacks.remove(listener);
        }
    }

    /**
     * appCount值变动都汇聚到这个方法里
     */
    private void setAppCount(int count) {
        appCount = count;
        MMKVUtil.getInstance().saveInt(PreferenceConstantsInOpenSdk.MMKV_KEY_RESUME_ACTIVITY_COUNT, appCount);
    }
}
