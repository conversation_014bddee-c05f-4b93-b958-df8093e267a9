package com.ximalaya.ting.android.commercial.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.RelativeLayout;

import java.lang.ref.WeakReference;

public class InterceptRelativeLayout extends RelativeLayout {
    private WeakReference<Runnable> interceptTouchEventCallback;

    public InterceptRelativeLayout(Context context) {
        super(context);
    }

    public InterceptRelativeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public InterceptRelativeLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public InterceptRelativeLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
//        Logger.e("z_pop", "onInterceptTouchEvent >>> ");
        if (interceptTouchEventCallback != null && interceptTouchEventCallback.get() != null) {
            interceptTouchEventCallback.get().run();
        }
        return false;
    }

    public Runnable getInterceptTouchEventCallback() {
        if (interceptTouchEventCallback == null)
            return null;
        if (interceptTouchEventCallback.get() == null)
            return null;
        return interceptTouchEventCallback.get();
    }

    public void setInterceptTouchEventCallback(WeakReference<Runnable> interceptTouchEventCallback) {
        this.interceptTouchEventCallback = interceptTouchEventCallback;
    }
}
