package com.ximalaya.ting.android.read.bean;

import android.text.TextUtils;

import com.ximalaya.ting.android.read.common.NetworkConstants;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * @CreateDate: 2022/3/3 3:34 下午
 * @Author: ypp
 * @Description:
 */
public class DeepReadBean {

    public int code;
    public List<ItemBean> itemList = new ArrayList<>();

    public boolean isSuccess() {
        return code == NetworkConstants.CODE_SUCCESS && !itemList.isEmpty();
    }

    public void parseSelf(String content) {
        JSONObject json = null;
        try {
            json = new JSONObject(content);
            code = json.optInt("code");
            if (code == NetworkConstants.CODE_SUCCESS) {
                String arrayJson = json.optString("data");
                JSONArray array = new JSONArray(arrayJson);
                if (array != null && array.length() > 0) {
                    for (int i = 0; i < array.length(); i++) {
                        JSONObject item = array.getJSONObject(i);
                        ItemBean itemBean = new ItemBean();
                        if (item != null) {
                            itemBean.pic = item.optString("pic");
                            itemBean.second = item.optInt("second");
                            JSONArray wordArray = item.optJSONArray("word");
                            if (wordArray != null && wordArray.length() > 0) {
                                itemBean.wordList = new ArrayList<>();
                                for (int j = 0; j < wordArray.length(); j++) {
                                    if (!TextUtils.isEmpty(wordArray.getString(j))) {
                                        itemBean.wordList.add(wordArray.getString(j));
                                    }
                                }
                            }
                            itemList.add(itemBean);
                        }
                    }
                }

            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

    }


    public static class ItemBean {
        public List<String> wordList;
        public String pic;
        public int second;
    }


}
