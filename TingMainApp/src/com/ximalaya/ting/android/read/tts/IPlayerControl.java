package com.ximalaya.ting.android.read.tts;

import com.ximalaya.ting.android.read.bean.tts.ListenInfo;
import com.ximalaya.ting.android.read.bean.tts.TimerBean;

/**
 * @CreateDate: 2022/5/25 4:36 下午
 * @Author: ypp
 * @Description:
 */
public interface IPlayerControl {

    void onChangeSpeed(float speed);

    void onChangeTimer(TimerBean bean);

    void onChangeSpeaker(ListenInfo.TtsConfigItem bean);

    void onStop();


}
