package com.ximalaya.ting.android.read.widgets.baserecyclerviewadapter.Footer;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;

import androidx.appcompat.widget.AppCompatImageView;

import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.read.widgets.baserecyclerviewadapter.IBottomView;
import com.ximalaya.ting.android.read.widgets.baserecyclerviewadapter.utils.DensityUtil;


/**
 * Created by lcodecore on 2016/10/3.
 */

public class LoadingView extends AppCompatImageView implements IBottomView {
    public LoadingView(Context context) {
        this(context, null);
    }

    public LoadingView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LoadingView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        int size = DensityUtil.dp2px(context, 34);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(size, size);
        params.gravity = Gravity.CENTER;
        setLayoutParams(params);
        setImageResource(R.drawable.read_anim_loading_view);
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void onPullingUp(float fraction, float maxHeadHeight, float headHeight) {

    }

    @Override
    public void startAnim(float maxHeadHeight, float headHeight) {
        ((AnimationDrawable) getDrawable()).start();
    }

    @Override
    public void onPullReleasing(float fraction, float maxHeadHeight, float headHeight) {

    }

    @Override
    public void onFinish() {
        ((AnimationDrawable) getDrawable()).stop();
    }

    @Override
    public void reset() {

    }
}
