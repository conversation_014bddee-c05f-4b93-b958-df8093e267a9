package com.ximalaya.ting.android.read.widgets.epub.lib.epub.util;

import android.os.Build;

import com.ximalaya.ting.android.read.widgets.epub.lib.commen.configuration.DemonstrationConfiguration;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.constants.Constant_Epub;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.paint.EpubPaint;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.style.StyleModel;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.style.item.Border;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.style.item.Margin;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.tree.EpubTree;


/**
 * Created by 5Greatest on 2020.10.04
 *
 * <AUTHOR>
 * On 2020-10-04
 */
public class SizeUtil {
    public static EpubPaint setPaintTextSize(EpubPaint to, EpubPaint from, float basicFont) {
        if (null == to) {
            return null;
        }
        if (null == from) {
            return to;
        }
        if (0 < from.getAbsSize()) {
            to.setTextSize(from.getAbsSize());
            return to;
        }
        to.setTextSize(from.getSizeRatio() * basicFont);
        return to;
    }

    public static EpubPaint setPaintTextSize(EpubPaint paint, float basicFont) {
        return setPaintTextSize(paint, paint, basicFont);
    }

    public static float getAbsoluteLineHeight(EpubTree treeNode, float basicFont) {
        if (null == treeNode) {
            return 0;
        }
        return getAbsoluteLineHeight(treeNode.getPaint(), basicFont);
    }

    public static float getAbsoluteLineHeight(EpubPaint paint, float basicFont) {
        if (null == paint) {
            return 0;
        }
        if (0 < paint.getAbsSize()) {
            if (0 < paint.getAbsHalfLineSpacing()) {
                return paint.getAbsSize()
                        // 样式中声明的行距
                        + paint.getAbsHalfLineSpacing() * 2f
                        // 全局设置的行距
                        + DemonstrationConfiguration.getLineSpacingRatio() * basicFont;
            } else {
                return paint.getAbsSize()
                        // 样式中声明的行距
                        + paint.getHalfLineSpacingRatio() * basicFont * 2f
                        // 全局设置的行距
                        + DemonstrationConfiguration.getLineSpacingRatio() * basicFont;
            }
        } else {
            if (0 < paint.getAbsHalfLineSpacing()) {
                return paint.getSizeRatio() * basicFont
                        // 样式中声明的行距
                        + paint.getAbsHalfLineSpacing() * 2f
                        // 全局设置的行距
                        + DemonstrationConfiguration.getLineSpacingRatio() * basicFont;
            } else {
                return paint.getSizeRatio() * basicFont
                        // 样式中声明的行距
                        + paint.getHalfLineSpacingRatio() * basicFont * 2f
                        // 全局设置的行距
                        + DemonstrationConfiguration.getLineSpacingRatio() * basicFont;
            }
        }
    }

    public static float getBorderSize(EpubTree treeNode, float basicFont, short location) {
        if (null == treeNode) {
            return 0;
        }
        return getBorderSize(treeNode.getPaint(), basicFont, location);
    }

    public static float getBorderSize(EpubPaint paint, float basicFont, short location) {
        if (null == paint) {
            return 0;
        }
        return getBorderSize(paint.getStyleModel(), basicFont, location);
    }

    public static float getBorderSize(StyleModel styleModel, float basicFont, short location) {
        if (null == styleModel) {
            return 0;
        }
        return getBorderSize(styleModel.getBorder(), basicFont, location);
    }

    public static float getBorderSize(Border border, float basicFont, short location) {
        if (null == border || null == border.getItem(location)) {
            return 0;
        }
        if (0 < border.getItem(location).getAbsBorderSize()) {
            return border.getItem(location).getAbsBorderSize();
        } else {
            // 因为过细的边框可能无法显示，所以要额外加一个辅助值，保证其显示
            return border.getItem(location).getBorderRatio() * basicFont + Constant_Epub.DEFAULT_BORDER_WIDTH;
        }
    }

    public static float getMarginSize(EpubTree treeNode, float basicFont, short location) {
        if (null == treeNode) {
            return 0;
        }
        return getMarginSize(treeNode.getPaint(), basicFont, location);
    }

    public static float getMarginSize(EpubPaint paint, float basicFont, short location) {
        if (null == paint) {
            return 0;
        }
        return getMarginSize(paint.getStyleModel(), basicFont, location);
    }

    public static float getMarginSize(StyleModel styleModel, float basicFont, short location) {
        if (null == styleModel) {
            return 0;
        }
        return getMarginSize(styleModel.getMargin(), basicFont, location);
    }

    public static float getMarginSize(Margin margin, float basicFont, short location) {
        if (null == margin || null == margin.getItem(location)) {
            return 0;
        }
        if (0 < margin.getItem(location).getAbsMargin()) {
            return margin.getItem(location).getAbsMargin();
        } else {
            return margin.getItem(location).getMarginRatio() * basicFont;
        }
    }

    public static float getIntent(EpubTree treeNode, float basicFont) {
        if (null == treeNode) {
            return 0;
        }
        return getIntent(treeNode.getPaint(), basicFont);
    }

    public static float getIntent(EpubPaint paint, float basicFont) {
        if (null == paint) {
            return 0;
        }
        if (0 < paint.getAbsIntent()) {
            return paint.getAbsIntent();
        } else {
            return paint.getIntent() * basicFont;
        }
    }

    public static float getAbsoluteHalfSpacing(EpubTree treeNode, float basicFont) {
        if (null == treeNode) {
            return 0;
        }
        return getAbsoluteHalfSpacing(treeNode.getPaint(), basicFont);
    }

    public static float getAbsoluteHalfSpacing(EpubPaint paint, float basicFont) {
        if (null == paint) {
            return 0;
        }
        if (0 < paint.getAbsHalfLineSpacing()) {
            return paint.getAbsHalfLineSpacing();
        } else {
            return paint.getHalfLineSpacingRatio() * basicFont;
        }
    }

    public static EpubPaint setLetterSpacing(EpubPaint paint) {
        return setLetterSpacing(paint, DemonstrationConfiguration.getLetterSpacingRatio());
    }

    public static EpubPaint setLetterSpacing(EpubPaint paint, float spacing) {
        if (null == paint) {
            return null;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            paint.setLetterSpacing(spacing);
        }
        return paint;
    }
}
