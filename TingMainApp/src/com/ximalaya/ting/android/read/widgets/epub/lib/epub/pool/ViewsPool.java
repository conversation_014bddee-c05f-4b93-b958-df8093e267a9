package com.ximalaya.ting.android.read.widgets.epub.lib.epub.pool;

import android.view.View;
import android.view.ViewGroup;

import com.ximalaya.ting.android.read.widgets.epub.entity.ChapterData;
import com.ximalaya.ting.android.read.widgets.epub.lib.commen.configuration.LibraryConfiguration;
import com.ximalaya.ting.android.read.widgets.epub.lib.commen.error.NoRealPageException;
import com.ximalaya.ting.android.read.widgets.epub.lib.commen.error.NoSizeInfoException;
import com.ximalaya.ting.android.read.widgets.epub.lib.commen.model.info.PaginationInfo;
import com.ximalaya.ting.android.read.widgets.epub.lib.commen.model.info.SizeInfo;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.RealPage;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.view.EpubBookViewGroup;

import java.util.ArrayList;
import java.util.List;


/**
 * Created by 5Greatest on 2020.09.25
 *
 * <AUTHOR>
 * On 2020-09-25
 */
public class ViewsPool {
    private static final int MAX_LIMIT = 3;
    private static final List<EpubBookViewGroup> VIEW_GROUPS = new ArrayList<>(MAX_LIMIT);

    private static int count = 0;

    @Deprecated
    public static EpubBookViewGroup getEpubView(RealPage page, PaginationInfo paginationInfo, ChapterData chapterData) throws NoRealPageException, NoSizeInfoException {
        if (null == page) {
            throw new NoSizeInfoException();
        }
        return getEpubView(page, page.getSizeInfo(), paginationInfo ,chapterData);
    }

    public static EpubBookViewGroup getEpubView(RealPage page, SizeInfo sizeInfo, PaginationInfo paginationInfo, ChapterData chapterData) throws NoSizeInfoException {
        ViewGroup.LayoutParams params = null;
        if (null != sizeInfo) {
            params = new ViewGroup.LayoutParams(sizeInfo.screenWidth, sizeInfo.screenHeight);
        } else {
            throw new NoSizeInfoException();
        }
        EpubBookViewGroup result = null;

        EpubBookViewGroup viewGroup = new EpubBookViewGroup.EpubViewGroupBuilder()
                .setPaginationInfo(paginationInfo)
                .setChapterData(chapterData)
                .build(LibraryConfiguration.getApplicationContext(), sizeInfo);
        viewGroup.setLayoutParams(params);
        // VIEW_GROUPS.add(viewGroup);
        result = (EpubBookViewGroup) viewGroup.forceToStartDemonstrate(page, sizeInfo);

        return result;
    }


    private static class ViewItem {
        public boolean isOccupied;
        public View view;
    }
}
