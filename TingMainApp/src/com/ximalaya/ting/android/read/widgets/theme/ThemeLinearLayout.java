package com.ximalaya.ting.android.read.widgets.theme;

import android.content.Context;
import android.content.res.Resources;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

import com.ximalaya.ting.android.read.widgets.theme.util.ThemeUIInterface;
import com.ximalaya.ting.android.read.widgets.theme.util.ViewAttributeUtil;


/**
 * <AUTHOR>
 * @date 2019/1/2.
 * Description：
 */
public class ThemeLinearLayout extends LinearLayout implements ThemeUIInterface {

    private int attr_background = -1;

    public ThemeLinearLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ThemeLinearLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.attr_background = ViewAttributeUtil.getBackgroundAttribute(attrs);
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void setTheme(Resources.Theme themeId) {
        if (attr_background != -1) {
            ViewAttributeUtil.applyBackgroundDrawable(this, themeId, attr_background);
        }
    }
}
