package com.ximalaya.ting.android.read.manager

import android.content.Context
import android.content.Intent
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.FileProviderUtil
import com.ximalaya.ting.android.host.download.bean.TaskInfo
import com.ximalaya.ting.android.host.download.bean.TaskState
import com.ximalaya.ting.android.host.download.listener.DownloadListener
import com.ximalaya.ting.android.host.download.manager.TaskMgr
import com.ximalaya.ting.android.read.utils.LogUtils
import java.io.File

object DownFileManager {

    const val TAG = "DownFileManager"

    private val mDownloadListener = mutableListOf<DownloadListener>()

    @JvmStatic
    fun addDownListener(listener: DownloadListener?) {
        if (listener == null) {
            return
        }
        if (!mDownloadListener.contains(listener)) {
            mDownloadListener.add(listener)
        }
    }

    @JvmStatic
    fun removeDownListener(listener: DownloadListener?) {
        mDownloadListener.remove(listener)
    }

    @JvmStatic
    fun createTaskInfo(downUrl: String): TaskInfo {
        val context = BaseApplication.getMyApplicationContext()
        val path = context?.externalCacheDir?.absolutePath?.plus("/qiji")
        val fileName =
            downUrl.subSequence(downUrl.lastIndexOf("/") + 1, downUrl.length).toString()

//        LogUtils.d(TAG, "path:$path  fileName:$fileName downUrl:$downUrl")

        return TaskInfo.TaskInfoBuilder()
            .setUrl(downUrl).setDirPath(path).setFileName(fileName).build()
    }

    @JvmStatic
    fun isNeedStartDownload(taskInfo: TaskInfo?): Boolean {
        val state = TaskMgr.get().search(taskInfo)?.state
        return state == null || state.state >= TaskState.STATE_DONE
    }

    @JvmStatic
    fun isNeedInstall(taskInfo: TaskInfo?): Boolean {
        val state = TaskMgr.get().search(taskInfo)?.state
        return state == null || state.state == TaskState.STATE_DONE
    }

    @JvmStatic
    fun downloadApkFile(taskInfo: TaskInfo?) {
        TaskMgr.get().add(taskInfo, object : DownloadListener {
            override fun onTaskStart(task: TaskInfo?) {
                mDownloadListener.forEach {
                    it.onTaskStart(task)
                }
            }

            override fun onTaskSuccess(task: TaskInfo?) {
                task?.run {
                    file = File(dirPath, fileName)
                }

                if (task?.file?.exists() == true) {
                    mDownloadListener.forEach {
                        it.onTaskSuccess(task)
                    }
                    installApp(BaseApplication.getMyApplicationContext(), task.file)
                } else {
                    mDownloadListener.forEach {
                        it.onTaskFailed(task)
                    }
                }
            }

            override fun onTaskFailed(task: TaskInfo?) {
                mDownloadListener.forEach {
                    it.onTaskFailed(task)
                }
            }

            override fun onTaskProgress(task: TaskInfo?, progress: Int) {
                mDownloadListener.forEach {
                    it.onTaskProgress(task, progress)
                }
            }
        })
    }

    fun installApp(context: Context?, appFile: File?): Boolean {
        try {
            if (null != appFile && appFile.exists()) {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                FileProviderUtil.setIntentDataAndType(
                    context, intent,
                    "application/vnd.android.package-archive",
                    appFile, true
                )
                context?.startActivity(intent)
                return true
            } else {
                LogUtils.d(TAG, "触发安装失败,appFile: ${appFile?.exists()}")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            LogUtils.d(TAG, e.message)
        }
        return false
    }
}