package com.ximalaya.ting.android.host.util

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.animation.ValueAnimator
import android.content.ComponentCallbacks
import android.content.Context
import android.content.res.ColorStateList
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.cardview.widget.CardView
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.FragmentActivity
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.easyfloat.utils.DisplayUtils
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.service.DownloadService
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.record.BaseDownloadTask
import com.ximalaya.ting.android.host.manager.record.DownloadManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.common.FoldableScreenCompatUtil
import com.ximalaya.ting.android.host.util.common.IScreenConfigChangedListener
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.read.utils.LogUtils
import com.ximalaya.ting.android.read.utils.checkActivity
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONObject
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.lang.ref.SoftReference
import java.lang.ref.WeakReference

object HomePageQuickListenGuideUtil {

    public const val TAG = "HomePageQuickListenGuideUtil"


    private var curViewRefresh: SoftReference<View>? = null
    private var isShowed = false
    private const val KEY_SHOW_DATE_RECORD = "key_show_quick_listen_guide_record"
    private var mData:JSONObject? = null
    private val FILE_DIR_PATH: String =
        DownloadService.getDiskCachePath(MainApplication.getMyApplicationContext()) + File.separator + "ql_guide"

    //    private const val autoDismissInterval = 5 * 1000L
    private var xmRequestId = ""

    private var fileInputStream:FileInputStream? = null

    private fun requestShowGuideData(callback: IDataCallBack<JSONObject?>) {
        CommonRequestM.baseGetRequest(
            UrlConstants.getInstanse().quickListenGuideUrl, emptyMap(), callback
        ) {
            try {
                JSONObject(it)
            } catch (e: Exception) {
                null
            }
        }
    }
    private var mComponentCallbacksRef: WeakReference<ComponentCallbacks?>? = null

    private fun reportGuideShow(jsonObject: JSONObject?) {
        jsonObject?:return
        val postStr = JSONObject()

        postStr.put("bizType", jsonObject.optString("bizType"))
        postStr.put("uiType", jsonObject.optString("uiType"))
        postStr.put("contentType", jsonObject.optString("contentType"))
        postStr.put("event", "expose")
        CommonRequestM.basePostRequestJsonStr(UrlConstants.getInstanse()
            .reportQuickListenGuideShowUrl(),
            postStr.toString(),
            object : IDataCallBack<String> {
                override fun onSuccess(data: String?) {
                }

                override fun onError(code: Int, message: String?) {
                }

            }) {
            it
        }
    }

    fun checkShowGuide(
        activity: FragmentActivity?,
        itemView: View?
    ) {
        if(ConfigureCenter.getInstance().getInt(CConstants.Group_android.GROUP_NAME, "show_quick_listen_guide_switch", 0) == 2){
            // 控制中心控制关闭展示弹窗
            return
        }
        if (!activity.checkActivity() || itemView == null) {
            Logger.d(TAG, "itemView:$itemView")
            return
        }
        if (ViewUtil.haveDialogIsShowing(activity)) {
            Logger.d(TAG, "haveDialogIsShowing:true")
            return
        }
        val context = BaseApplication.getMyApplicationContext()
        val currentDate = DateTimeUtil.getToday()
        val lastShowDate = MmkvCommonUtil.getInstance(context).getString(KEY_SHOW_DATE_RECORD, "")
        val debugMode = ToolUtil.getDebugSystemProperty("debug.mark.showGuide", "0") == "2"
        if (!debugMode && currentDate == lastShowDate && !lastShowDate.isNullOrEmpty()) {
            Logger.d(TAG, "一个自然日最多展示一次")
            return
        }

        Logger.d(TAG, "requestShowGuideData")
        requestShowGuideData(object : IDataCallBack<JSONObject?> {
            override fun onSuccess(data: JSONObject?) {
                Logger.d(TAG, "requestShowGuideData:$data")
                data?.optJSONObject("data")?.let {
                    if ("newComer" == it.optString("bizType")
                        && "floatLayer" == it.optString("uiType")
                    ) {
                        val url = it.optJSONObject("guideInfo")?.optString("url")
                        if (!url.isNullOrEmpty()) {
                            xmRequestId = XmRequestIdManager.getInstance(context).requestId
                            mData = it
                            preLoadDialogJsonFile(
                                activity, itemView, url,
                                currentDate
                            )
                        }
                    }
                }
            }

            override fun onError(code: Int, message: String?) {
                Logger.d(TAG, "requestShowGuideData:code$code, message$message")
            }

        })
    }
    private fun getCompatScreenWidth(context: Context): Int {
        return if (BaseUtil.isFoldScreen(context) && BaseUtil.dp2px(
                context,
                600f
            ) <= BaseUtil.getScreenWidth(context)
        ) {
            BaseUtil.dp2px(context, 375f)
        } else {
            BaseUtil.getScreenWidth(context)
        }
    }

    private fun checkShowGuideInner(
        activity: FragmentActivity?,
        itemView: View,
        filePath: String,
        currentDate: String
    ): Boolean {
        val context = BaseApplication.getMyApplicationContext()
        val rect = Rect()
        itemView.getGlobalVisibleRect(rect)
        if (rect.isEmpty || rect.top == 0) {
            LogUtils.d(TAG, "未获取到菜单位置 $rect")
            return false
        }
        val jsonFile = File(filePath)
        if (!jsonFile.exists() || !jsonFile.isFile) {
          return false
        }

        if (activity is MainActivity) {
            if (!activity.isManagerEmpty) {
                Logger.d(
                    TAG, "有别的界面 不显示引导:${activity.manageFragment?.currentFragment}"
                )
                return false
            }

            val tabFragmentManager = activity.tabFragmentManager
            if (tabFragmentManager != null) {
                if (tabFragmentManager.currentTab != TabFragmentManager.TAB_HOME_PAGE) {
                    Logger.d(
                        TAG,
                        "不在我页界面 不显示引导:${tabFragmentManager.currFragment}"
                    )
                    return false
                }
            }
        }

        try {
            ViewUtil.setHasDialogShow(true)
            val rootView = activity!!.window.decorView as ViewGroup
            val childView = LayoutInflater.from(context)
                .inflate(R.layout.host_layout_quick_listen_guide, rootView, false)
            curViewRefresh = SoftReference(childView)
            val maskView = childView.findViewById<View>(R.id.host_mask_view)
            val bottomTab = childView.findViewById<ImageFilterView>(R.id.host_iv_bottom_tab)
            val ivArrowTop = childView.findViewById<ImageView>(R.id.host_iv_arrow_top)
            val cardView = childView.findViewById<CardView>(R.id.host_card_lottie)
            val bottomTabLeftSpace = childView.findViewById<View>(R.id.host_view_bottom_left_space)
            val ivBgView = childView.findViewById<XmLottieAnimationView>(R.id.host_iv_lottie)
            val ivCloseView = childView.findViewById<View>(R.id.host_iv_close)
            try {
                fileInputStream = FileInputStream(jsonFile)
                ivBgView?.setAnimation(fileInputStream, "quick_listen_guide")
            } catch (e: IOException) {
                e.printStackTrace()
            }
            val updateLayoutParamsRunnable = Runnable {
                itemView.getGlobalVisibleRect(rect)
                val halfRound = BaseUtil.dp2px(context, 26f)
                val centerY = (rect.bottom + rect.top) / 2
                val centerX = (rect.right + rect.left) / 2
                rect.top = centerY - halfRound
                rect.bottom = centerY + halfRound
                rect.left = centerX - halfRound
                rect.right = centerX + halfRound
                Logger.d(
                    TAG, "itemView.rect:$rect"
                )
                cardView.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    width = getCompatScreenWidth(context) - BaseUtil.dp2px(context, 32f)
                }
                bottomTabLeftSpace.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    if (BaseUtil.isFoldScreen(context) && BaseUtil.dp2px(
                            context,
                            600f
                        ) <= BaseUtil.getScreenWidth(context)
                    ) {
                        width = BaseUtil.dp2px(context, 70f)
                    } else {
                        width = centerX - BaseUtil.dp2px(context, 31f) - BaseUtil.dp2px(
                            context,
                            16f
                        )
                    }
                }
                val bottomTabParams = bottomTab.layoutParams as ConstraintLayout.LayoutParams
                bottomTabParams.leftMargin = centerX - BaseUtil.dp2px(context, 31f)
                Logger.d(TAG, "screenHeight:${BaseUtil.getScreenHeight(context)} ,rect:$rect")
                bottomTabParams.bottomMargin = DisplayUtils.getNavigationBarCurrentHeight(context)
                bottomTab.layoutParams = bottomTabParams
            }
            updateLayoutParamsRunnable.run()
            val callbacks = object : ComponentCallbacks {
                override fun onConfigurationChanged(newConfig: Configuration) {
                    Logger.d(
                        TAG, "onScreenWidthChanged"
                    )
                    childView?.postDelayed(updateLayoutParamsRunnable, 16)
                }

                override fun onLowMemory() {
                    removeGuide()
                }

            }
            mComponentCallbacksRef = WeakReference(callbacks)
            ToolUtil.getCtx()?.registerComponentCallbacks(callbacks)
            maskView.setOnClickListener {
                removeGuide()
            }
            bottomTab.setOnClickListener {
                tracePopClick(xmRequestId)
                removeGuide()
                QuickListenTabAbManager.go2QuickListenMainPage(
                    activity as? MainActivity,
                    "底tab"
                )
            }
            cardView.setOnClickListener {
                tracePopClick(xmRequestId)
                removeGuide()
                QuickListenTabAbManager.go2QuickListenMainPage(
                    activity as? MainActivity,
                    "底tab"
                )

            }
            ivCloseView.setOnClickListener {
                removeGuide()
            }


            rootView.addView(childView)
            ValueAnimator.ofFloat(0f, 1f)?.let {
                it.duration = 250
                it.addUpdateListener { value ->
                    ivArrowTop?.alpha = value.animatedValue as Float
                    cardView?.alpha = value.animatedValue as Float

                }
                it.addListener(object:AnimatorListener{
                    override fun onAnimationStart(animation: Animator?) {
                    }

                    override fun onAnimationEnd(animation: Animator?) {
                        ivBgView.visibility = View.VISIBLE
                        ivBgView.playAnimation()

                    }

                    override fun onAnimationCancel(animation: Animator?) {
                    }

                    override fun onAnimationRepeat(animation: Animator?) {
                    }

                })
                it.start()
            }
            var hasChanged1 = false
            var hasChanged2 = false
            ivBgView?.addAnimatorUpdateListener {
                if( it.animatedValue as Float  >= 0.001 && !hasChanged1){
                    hasChanged1 = true
                    ivArrowTop.imageTintList = ColorStateList.valueOf(Color.parseColor("#C0DFFE"))
                }
                if (it.animatedValue as Float >= 0.2983 && !hasChanged2) {
                    hasChanged2 = true
                    ivArrowTop.imageTintList =
                        ColorStateList.valueOf(Color.parseColor("#F3F4FC"))
                }
            }
            ivBgView?.addAnimatorListener(object : AnimatorListener {
                override fun onAnimationStart(animation: Animator?) {
                }

                override fun onAnimationEnd(animation: Animator?) {
                    removeGuide()
                }

                override fun onAnimationCancel(animation: Animator?) {
                }

                override fun onAnimationRepeat(animation: Animator?) {
                }

            })
            isShowed = true
            MmkvCommonUtil.getInstance(context).saveString(KEY_SHOW_DATE_RECORD, currentDate)
            tracePopShow(xmRequestId)
            reportGuideShow(mData)
            return true
        } catch (e: Exception) {
            ViewUtil.setHasDialogShow(false)
            e.printStackTrace()
            Logger.d(TAG, "显示异常:${e.message}")
        }
        return false
    }

    private fun preLoadDialogJsonFile(
        activity: FragmentActivity?,
        itemView: View,
        url: String,
        currentDate: String
    ): Boolean {
        val lottieUrl = if (url.isNullOrEmpty()) {
            "https://audiotest.cos.tx.xmcdn.com/storages/0b54-audiotest/50/16/GAqStYUMOlVJAAo8NAAB4dK-.json"
        } else {
            url
        }
        val fileName = "${lottieUrl.hashCode()}.json"
        val file = File(FILE_DIR_PATH, fileName);
        Logger.d(TAG, "preLoadDialogJsonFile:url$lottieUrl,file.name:$fileName ,file:${file.exists()}")
        if (file.exists()) {
            checkShowGuideInner(activity, itemView, file.absolutePath, currentDate)
            return true
        }
        val downloadTask = QuickListenGuideDownloadTask(
            lottieUrl,
            FILE_DIR_PATH,
            fileName,
            object : QuickListenGuideDownloadTask.DownloadCallback {
                override fun onSuccess(filePath: String) {
                    checkShowGuideInner(activity, itemView, file.absolutePath, currentDate)
                }

                override fun onError(e: java.lang.Exception?) {
                }

            }
        )
        DownloadManager.getInstance().download(downloadTask, true)
        return false
    }

    fun removeGuide() {
        Logger.d(
            TAG, "removeGuide"
        )
        mComponentCallbacksRef?.get()?.let {
            ToolUtil.getCtx()?.unregisterComponentCallbacks(it)
        }
        ViewUtil.setHasDialogShow(false)
        try {
            fileInputStream?.close();
        } catch (e: Exception) {
            e.printStackTrace()
        }
        val childView = curViewRefresh?.get()
        if (childView?.parent is ViewGroup) {
            try {
                (childView.parent as ViewGroup).removeView(childView)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        curViewRefresh = null
    }

//    private val autoDismissRunnable = Runnable {
//        removeGuide()
//    }

    private fun tracePopShow(xmRequestId: String) {
        // 快听底Tab气泡引导  控件曝光
        XMTraceApi.Trace()
            .setMetaId(69108)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "forAll")
            .put("xmRequestId", xmRequestId)
            .put(
                "isDuplicateView",
                "0"
            ) // 0，1，2 字段文档见：https://alidocs.dingtalk.com/i/nodes/jb9Y4gmKWr7lEp50HZ55dyaZVGXn6lpz?utm_scene=team_space&utm_source=dingdoc_doc&utm_medium=dingdoc_doc_plugin_card
            .put("guideType", "新人引导")
            .put("guideText", "") // 传对应的引导文案
            .createTrace()
    }

    private fun tracePopClick(xmRequestId: String) {
        Logger.d(TAG, "tracePopClick:$xmRequestId")
        // 全局页-底Tab-快听  点击事件
        XMTraceApi.Trace()
            .click(68007) // 用户点击时上报
            .put("currPage", "forAll")
            .put("xmRequestId", xmRequestId)
            .put("guideType", "新人引导")
            .put("guideText", "") // 传对应的引导文案
            .createTrace();
    }

}

class QuickListenGuideDownloadTask(
    private val mDownloadUrl: String,
    private val mDirPath: String,
    private val mFileName: String,
    private val mDownloadCallback: DownloadCallback? = null
) : BaseDownloadTask() {

    override fun getDownloadUrl(): String {
        return mDownloadUrl
    }

    override fun getLocalPath(): String {
        return mDirPath
    }

    override fun getLocalName(): String {
        return mFileName
    }

    override fun isRefresh(): Boolean {
        return false
    }

    override fun handleStartDownload() {
        // 更新为下载中
        Logger.d(HomePageQuickListenGuideUtil.TAG, "开始下载json:$mFileName")
    }

    override fun handleStopDownload() {
        Logger.d(HomePageQuickListenGuideUtil.TAG, "停止下载json:$mFileName")
        HandlerManager.postOnUIThread {
            mDownloadCallback?.onError(null)
        }
    }

    override fun handleUpdateDownload(curr: Long, total: Long) {}

    override fun handleCompleteDownload() {
        Logger.d(HomePageQuickListenGuideUtil.TAG, "json:$mFileName 下载完成")
        HandlerManager.postOnUIThread {
            mDownloadCallback?.onSuccess(mDirPath + File.separator + mFileName)
        }
    }

    override fun handleDownloadError(e: java.lang.Exception, what: Int, extra: Int) {
        Logger.d(HomePageQuickListenGuideUtil.TAG, "json:$mFileName 下载失败 ${e.message} what=${what} extra=${extra}")
        HandlerManager.postOnUIThread {
            mDownloadCallback?.onError(e)
        }
    }

    interface DownloadCallback {
        fun onSuccess( filePath: String)
        fun onError(e:java.lang.Exception?)
    }
}