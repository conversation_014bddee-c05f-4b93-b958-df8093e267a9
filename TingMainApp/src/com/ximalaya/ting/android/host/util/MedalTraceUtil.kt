package com.ximalaya.ting.android.host.util

import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.model.play.CommentUserTag
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2023/10/12
 */
object MedalTraceUtil {
    @JvmOverloads
    fun traceMedalIcon(isClick: Boolean, userTag: CommentUserTag?, currPage: String?, uid: Long,
                       xmRequestId: String? = null, contType: String? = null, contId: Long = 0) {
        if (userTag == null) return
        if (userTag.businessType == CommentUserTag.TYPE_MEDAL) {
            traceMedalIcon(
                isClick,
                currPage ?: "",
                uid,
                userTag.extra?.get("level")?.toIntOrNull(),
                userTag.extra?.get("medalType")?.toIntOrNull(),
                uid == UserInfoMannage.getUid(),
                "",
                0,
                xmRequestId
            )
        } else if (userTag.businessType == CommentUserTag.TYPE_LEVEL) {
            traceLevelIcon(isClick, userTag, currPage, uid, xmRequestId, contType, contId)
        }
    }

    private fun traceLevelIcon(
        isClick: Boolean,
        userTag: CommentUserTag,
        currPage: String?,
        uid: Long,
        xmRequestId: String?, contType: String?, contId: Long
    ) {
        val trace = if (isClick) {
            // 用户等级标识  点击事件
            XMTraceApi.Trace()
                .click(58583) // 用户点击时上报
        } else {
            // 用户等级标识  控件曝光
            XMTraceApi.Trace()
                .setMetaId(58584)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
        }
        trace.put("currPage", currPage)
            .put("status", if (uid == UserInfoMannage.getUid()) "主态" else "客态") // 主态｜客态
            .put("userLevel", userTag.extra?.get("level"))
            .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
            .put(XmRequestIdManager.CONT_TYPE, contType)
            .put(XmRequestIdManager.CONT_ID, contId.toString())
            .createTrace()
    }

    fun traceMedalIcon(
        isClick: Boolean,
        currPage: String = "",
        uid: Long = 0L,
        level: Int? = -1,
        type: Int? = -1,
        isAnchorSpace: Boolean = true,
        moduleName: String = "",
        entranceType: Int = 0,
        xmRequestId: String? = ""
    ) {
        // 勋章入口  控件曝光
        if (isClick) {
            XMTraceApi.Trace()
                .click(57934)
        } else {
            XMTraceApi.Trace()
                .setMetaId(57935)
                .setServiceId("slipPage")
        }.put("currPage", currPage) // 播放页｜专辑页｜评论区｜我的页｜个人页｜关注页｜福利广场｜动态页｜关注列表等
            .put("uid", uid.toString()) // 被点击用户ID
            .put("Medallevel", level.toString()) // 金牌｜银牌｜铜牌
            .put("MedalType", type.toString()) // 收听勋章｜粉丝勋章｜限定勋章
            .put("status", if (isAnchorSpace) "主态" else "客态") // 主态｜客态
            .put("moduleName", moduleName) // 主播介绍，联合出品，专辑页头部等
            .put("type", if (entranceType == 0) "勋章标签" else "勋章大图")
            .put(XmRequestIdManager.CONT_ID, moduleName + "_" + type + "_" + level)
            .put(XmRequestIdManager.CONT_TYPE, "user")
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                xmRequestId
            ).createTrace()
    }

    /**
     * 勋章入口
     * */
    fun traceMedalEntrance(
        isClick: Boolean,
        currPage: String = "",
        uid: Long = 0L,
        level: Int? = -1,
        type: Int? = -1,
        isAnchorSpace: Boolean = true,
        moduleName: String = "",
        entranceType: Int = 0,
        xmRequestId: String = ""
    ) {
        // 勋章入口  控件曝光
        XMTraceApi.Trace()
            .setMetaId(if (isClick) 58031 else 58032)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", currPage) // 播放页｜专辑页｜评论区｜我的页｜个人页｜关注页｜福利广场｜动态页｜关注列表等
            .put("uid", uid.toString()) // 被点击用户ID
            .put("Medallevel", level.toString()) // 金牌｜银牌｜铜牌
            .put("MedalType", type.toString()) // 收听勋章｜粉丝勋章｜限定勋章
            .put("status", if (isAnchorSpace) "主态" else "客态") // 主态｜客态
            .put("moduleName", moduleName) // 主播介绍，联合出品，专辑页头部等
            .put("type", if (entranceType == 0) "勋章标签" else "勋章大图")
            .put(XmRequestIdManager.CONT_ID, uid.toString())
            .put(XmRequestIdManager.CONT_TYPE, "user")
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                xmRequestId
            )
            .createTrace()
    }

    fun traceNewLevel(
        currPage: String,
        isMySelf: Boolean,
        level: Int? = 0,
        xmRequestId: String = ""
    ) {
        // 用户等级标识  点击事件
        XMTraceApi.Trace()
            .click(58583) // 用户点击时上报
            .put("currPage", currPage) // 我的页｜个人页｜评论区｜专辑评论tab页｜专辑评论详情页｜关注页（关注tab页、福利广场tab页）
            .put("status", if (isMySelf) "主态" else "客态") // 主态｜客态
            .put("userLevel", level?.toString() ?: "")
            .put(XmRequestIdManager.CONT_ID, level?.toString())
            .put(XmRequestIdManager.CONT_TYPE, "user")
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                xmRequestId
            )
            .createTrace()
    }

    fun eTraceNewLevel(
        currPage: String,
        isMySelf: Boolean,
        level: Int? = 0,
        xmRequestId: String = ""
    ) {
        // 用户等级标识  控件曝光
        XMTraceApi.Trace()
            .setMetaId(58584)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", currPage) // 我的页｜个人页｜评论区｜专辑评价详情页｜关注页等
            .put("status", if (isMySelf) "主态" else "客态") // 主态｜客态
            .put("userLevel", level?.toString() ?: "")
            .put(XmRequestIdManager.CONT_ID, UserInfoMannage.getUid().toString())
            .put(XmRequestIdManager.CONT_TYPE, "user")
            .put(
                XmRequestIdManager.XM_REQUEST_ID,
                xmRequestId
            )
            .createTrace()
    }
}