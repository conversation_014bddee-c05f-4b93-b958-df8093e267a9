package com.ximalaya.ting.android.host.util.ui;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.ToolUtil;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import androidx.annotation.DrawableRes;

/**
 * Created by WolfXu on 2020-02-28.
 * 专辑标题标签工具类
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class AlbumTitleTagUtil {

    public static final int INVALID_COLOR = 1;

    public static CharSequence getTitleWithTag(AlbumM albumM, int textSize) {
        return getTitleWithTag(albumM, textSize, INVALID_COLOR);
    }

    public static CharSequence getTitleWithTag(AlbumM albumM, int textSize, int filterColor) {
        if (albumM == null) {
            return "";
        }
        Context context = BaseApplication.getOptActivity();
        if (context == null) {
            context = BaseApplication.getMyApplicationContext();
        }
        if (context == null) {
            return albumM.getAlbumTitle();
        }
        @DrawableRes int tagResId = 0;
        if (albumM.getType() == 3) { // 训练营
            tagResId = R.drawable.host_tag_training_camp;
        } else if (albumM.getIsFinished() == 2 || albumM.getSerialState() == 2 || albumM.isCompleted()) {
            tagResId = R.drawable.host_tag_complete;
        } else if (albumM.getIsDraft()) {
            tagResId = R.drawable.host_tag_draft;
        }
        if (tagResId != 0) {
            return ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(context
                    , " " + albumM.getAlbumTitle(), tagResId, textSize, filterColor);
        } else {
            return albumM.getAlbumTitle();
        }
    }


    public static void setBrandImage(String logo, ImageView img){
        Context context = BaseApplication.getOptActivity();
        if (img == null) {
            return;
        }
        if (context == null || logo == null) {
            img.setVisibility(View.GONE);
            return;
        }

        if (!TextUtils.isEmptyOrNull(logo)) {
            img.setVisibility(View.VISIBLE);
            ImageManager.from(context).displayImage(img,logo, null);
        } else {
            img.setVisibility(View.GONE);
        }
    }

    public static void setBrandImage(String logo, ImageView img, ImageManager.DisplayCallback callback){
        Context context = BaseApplication.getOptActivity();
        if (img == null) {
            return;
        }
        if (context == null || logo == null) {
            img.setVisibility(View.GONE);
            return;
        }

        if (!TextUtils.isEmptyOrNull(logo)) {
            img.setVisibility(View.VISIBLE);
            ImageManager.from(context).displayImage(img,logo, null, callback, null);
        } else {
            img.setVisibility(View.GONE);
            if (callback != null) {
                callback.onCompleteDisplay(null, null);
            }
        }
    }
}
