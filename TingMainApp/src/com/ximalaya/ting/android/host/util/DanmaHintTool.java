package com.ximalaya.ting.android.host.util;

import android.text.TextUtils;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.host.util.common.ToolUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * Created by le.xin on 2018/7/6.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17097256298
 */
public class DanmaHintTool {
    private static List<String> mHints;
    private static List<String> mBulletInputTopHints;
    private final static List<String> mBackupHints = new ArrayList<String>() {{
        add("我猜测，此刻你可能有话想说");
        add("说点什么吧，也许TA也正在看");
        add("万千思绪，下笔汇集一句");
        add("爱发言的人运气都不会太差");
    }};
    private final static List<String> mBackupBulletInputTopHints = new ArrayList<String>() {{
        add("幽默、友善的发言会获得更多回应哦～");
        add("有学识的观点，会显得更有范儿～");
        add("点赞，可以让优秀的弹幕保留更久！");
        add("请不要提前剧透哦～，这是基本的“弹幕礼仪”");
        add("与节目无关的表态，会损害大家的收听体验！");
        add("请避免使用低俗的用词");
        add("都是地球人，请不要地域攻击～");
        add("“恶意刷屏”会被举报哦～");
        add("请不要发送带有私人信息的弹幕");
        add("请避免讨论违反法律法规的话题哦！");
    }};
    private static long mTrackId;
    private static int mIndex;

    public static List<String> getRankContent(long id) {
        if (mTrackId == id && !ToolUtil.isEmptyCollects(mHints)) {
            return mHints;
        }
        mTrackId = id;
        String[] hints = null;
        String string = ConfigureCenter.getInstance().getString("toc", "interaction_bulletguide", "");
        if (!TextUtils.isEmpty(string)) {
            hints = string.split("\\|");
        }
        if (hints == null) {
            return mBackupHints;
        }
        mHints = Arrays.asList(hints);
        return mHints;
    }

    public static String getRankRandomContent(long id) {
        Random random = new Random();
        if (mTrackId == id && !ToolUtil.isEmptyCollects(mHints)) {
            return mHints.get(random.nextInt(mHints.size()));
        }
        mTrackId = id;
        String[] hints = null;
        String string = ConfigureCenter.getInstance().getString("toc", "comment_introducer", "");
        if (!TextUtils.isEmpty(string)) {
            hints = string.split("\\|");
        }
        if (hints == null) {
            return mBackupHints.get(random.nextInt(mBackupHints.size()));
        }
        mHints = Arrays.asList(hints);
        return mHints.get(random.nextInt(mHints.size()));
    }

    public static String getBulletInputTopHintRankRandomContent(long id) {
        Random random = new Random();
        if (mTrackId == id && !ToolUtil.isEmptyCollects(mBulletInputTopHints)) {
            return mBulletInputTopHints.get(random.nextInt(mBulletInputTopHints.size()));
        }
        mTrackId = id;
        String[] hints = null;
        String string = ConfigureCenter.getInstance().getString("toc", "interactionhintplaceholder", "");
        if (!TextUtils.isEmpty(string)) {
            hints = string.split("\\|");
        }
        if (hints == null) {
            return mBackupBulletInputTopHints.get(random.nextInt(mBackupBulletInputTopHints.size()));
        }
        mBulletInputTopHints = Arrays.asList(hints);
        return mBulletInputTopHints.get(random.nextInt(mBulletInputTopHints.size()));
    }
}
