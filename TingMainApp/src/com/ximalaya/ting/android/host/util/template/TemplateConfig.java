package com.ximalaya.ting.android.host.util.template;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.support.rastermill.FrameSequenceDrawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

//import androidx.annotation.Nullable;

import com.tmall.wireless.vaf.virtualview.Helper.ImageLoader;
import com.tmall.wireless.vaf.virtualview.Helper.LocalImageProvider;
//import com.tmall.wireless.vaf.virtualview.event.EventManager;
import com.tmall.wireless.vaf.virtualview.view.image.ImageBase;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
//import com.ximalaya.ting.android.configurecenter.base.IConfigureCenter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
//import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
//import com.ximalaya.ting.android.framework.util.FileUtil;
//import com.ximalaya.ting.android.framework.util.SerialInfo;
//import com.ximalaya.ting.android.host.MainApplication;
//import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
//import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
////import com.ximalaya.ting.android.host.util.VirtualViewTemplateManagerUtil;
//import com.ximalaya.ting.android.host.util.common.DeviceUtil;
//import com.ximalaya.ting.android.host.util.constant.UrlConstants;
//import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager;
//import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
//import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
//import com.ximalaya.ting.android.opensdk.httputil.BaseBuilder;
//import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
//import com.ximalaya.ting.android.template.TemplateManager;
//import com.ximalaya.ting.android.template.core.IAbTestProvider;
//import com.ximalaya.ting.android.template.model.TemplateDetail;
//import com.ximalaya.ting.android.template.provider.IConfig;
//import com.ximalaya.ting.android.template.provider.IDataCallback;
//import com.ximalaya.ting.android.template.provider.IHttpRequester;
//import com.ximalaya.ting.android.template.provider.ITemplateEventListener;
//import com.ximalaya.ting.android.xmabtest.ABTest;
//import com.ximalaya.ting.android.xmlog.XmLogger;
//import com.ximalaya.ting.android.xmtrace.XMTraceApi;
//import com.ximalaya.ting.android.xmutil.Logger;
//import com.ximalaya.ting.android.xmutil.NetworkType;
//
//import org.json.JSONArray;
//import org.json.JSONException;
//import org.json.JSONObject;
//
//import java.io.BufferedOutputStream;
//import java.io.File;
//import java.io.FileOutputStream;
//import java.io.OutputStream;
//import java.lang.ref.SoftReference;
//import java.util.HashMap;
//import java.util.Iterator;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.ConcurrentHashMap;
//
//import okhttp3.Request;
//import okhttp3.Response;

/**
 * Description: 模板管理的配置信息
 * <br>
 * 有两个开关：AB 和 配置中心，同时打开才继续使用
 * AB 默认关闭，配置中心默认打开
 *
 * <br> Created by shixinzhang on 2022/5/20.
 */
public class TemplateConfig{
//    public static final int DOWNLOAD_ERROR_CODE_NO_RESPONSE = 1000;
//    public static final int DOWNLOAD_ERROR_CODE_NO_DATA = 1001;
//    public static final int DOWNLOAD_ERROR_CODE_SAVE_FAILED = 1002;
//
//    private static final String TAG = "XTemplate";
//    private static final String SP_NAME_DYNAMIC_SETTING = "XTemplateSetting";
//    //VV 是否使用性能监控的 AB
//    private static final String AB_KEY_TEMPLATE_PERFORMANCE = "is_dynamic_vv_use_perf_monitor";
//
//    //模板整体功能是否打开
//    private static final String KEY_TEMPLATE_SUM = "template_key_sum";
//
//    Context mContext;
//    IHttpRequester mHttpRequester;
//    APMTraceConfig mApmTraceConfig;
//    ITemplateEventListener mEventListener;
//    ImageLoader.IImageLoaderAdapter mImageLoader;
//    IAbTestProvider mAbTestProvider;
//
//    //内存中保存开关的 setting
//    static final Map<String, Boolean> sSettingMap = new ConcurrentHashMap<>();
//
//    static final List<SoftReference<String>> mErrorLogList = new LinkedList<>();
//
//    /**
//     * 用于控制启动阶段是否开关打开，目前是首页打开混排才会去请求
//     * @return
//     * @param context
//     */
//    public static boolean isABOpen(Context context) {
////        Boolean isOpen = sSettingMap.get(KEY_TEMPLATE_SUM);
////        if (isOpen != null) {
////            return isOpen == Boolean.TRUE;
////        }
////        SharedPreferences sp = getSp(context);
////        boolean recommendMixModeSpOpen = false;
////        if (sp != null) {
////            recommendMixModeSpOpen =  sp.getBoolean(CConstants.Group_toc.ITEM_RECOMMEND_FRAGMENT_MODE, false);
////        }
////
////        boolean recommendMixModeABOpen = isRecommendUseDynamic();
//////        Log.d(TAG, "isABOpen: recommendMixModeABOpen: " + recommendMixModeABOpen + ", recommendMixModeSpOpen: " + recommendMixModeSpOpen);
////
////        //1.推荐页是否使用混排
////        boolean open = recommendMixModeSpOpen || recommendMixModeABOpen;
////        sSettingMap.put(KEY_TEMPLATE_SUM, open);
//        return true;
//    }
//
//    /**
//     * 保存 AB 的值到 SP，下次启动使用
//     * （因为目前 AB 初始化的太晚，模板初始化时获取不到值）
//     * @param context
//     */
//    public static void updateAB(Context context) {
//        boolean isDynamicHomePage = isRecommendUseDynamic();
//        String apmTraceConfig = ABTest.getString(AB_KEY_TEMPLATE_PERFORMANCE, "");
//
//        SharedPreferences sp = getSp(context);
//        if (sp != null) {
//            sp.edit()
//                    .putString(AB_KEY_TEMPLATE_PERFORMANCE, apmTraceConfig)
//                    .putBoolean(CConstants.Group_toc.ITEM_RECOMMEND_FRAGMENT_MODE, isDynamicHomePage)
//                    .apply();
//
//            boolean isCurrentTemplateNotInit = sSettingMap.get(KEY_TEMPLATE_SUM) == Boolean.FALSE;
//            if (isDynamicHomePage && isCurrentTemplateNotInit) {
//                //初始化过早，当时没开启，现在拉到配置了，强制更新初始化一下
//                sSettingMap.put(KEY_TEMPLATE_SUM, true);
////                VirtualViewTemplateManagerUtil.initVirtualViewTemplateManager(context);
//            }
//            Set<String> abTestKeys = TemplateManager.getInstance().getABTestKeySet();
//            if (abTestKeys != null && abTestKeys.size() > 0) {
//                SharedPreferences.Editor editor = sp.edit();
//                for (String abKey: abTestKeys) {
//                    Logger.i("cf_test","动态模板abkey:_____写入__" + abKey);
//                    editor.putString(abKey, ABTest.getStringWithoutLog(abKey, ""));
//                }
//                editor.apply();
//            }
//        }
//    }
//
//    /**
//     * 确认首页是否使用动态化：混排模式下才使用
//     * @return
//     */
//    private static boolean isRecommendUseDynamic() {
//        String recommendMode = ABTest.getString(CConstants.Group_toc.ITEM_RECOMMEND_FRAGMENT_MODE, "");
//        return TextUtils.equals(recommendMode, RecommendFragmentAbManager.MODE_MIX);
//    }
//
//    public static List<SoftReference<String>> getErrorLogs() {
//        return mErrorLogList;
//    }
//
//    public TemplateConfig(final Context context) {
//        mContext = context;
//
//        getSettingStateFromSp();
//        registerConfigureUpdateListener();
//    }
//
//    /**
//     * 读取 sp 中的 setting 状态
//     */
//    private void getSettingStateFromSp() {
//        SharedPreferences sp = getSp(mContext);
//        if (sp == null) {
//            return;
//        }
//
//        boolean all = sp.getBoolean(TemplateSetting.ALL, true);
//        boolean request = sp.getBoolean(TemplateSetting.REQUEST_TEMPLATE, true);
//        boolean useAssets = sp.getBoolean(TemplateSetting.USE_ASSETS, true);
//
//        synchronized (sSettingMap) {
//            sSettingMap.put(TemplateSetting.ALL, all);
//            sSettingMap.put(TemplateSetting.REQUEST_TEMPLATE, request);
//            sSettingMap.put(TemplateSetting.USE_ASSETS, useAssets);
//        }
//    }
//
//    /**
//     * 获取到配置，更新缓存，下次启动使用
//     */
//    private void registerConfigureUpdateListener() {
//
//        ConfigureCenter.getInstance().registerConfigFetchCallback(new IConfigureCenter.ConfigFetchCallback() {
//            @Override
//            public void onUpdateSuccess() {
//                boolean dynamicAllOpen = false;
//                boolean dynamicUseAssetsOpen = false;
//                boolean dynamicHttpRequestOpen = false;
//                try {
//                    dynamicAllOpen = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, "dynamicAll");
//                    dynamicUseAssetsOpen = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, "dynamicUseAssets");
//                    dynamicHttpRequestOpen = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, "dynamicHttpRequest");
//
//                    updateSettingData(dynamicAllOpen, dynamicUseAssetsOpen, dynamicHttpRequestOpen);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                debugLog("[ConfigFetched]isSettingOpen, dynamicAll open? " + dynamicAllOpen
//                        + ", dynamicUseAssetsOpen: " + dynamicUseAssetsOpen
//                        + ", dynamicHttpRequestOpen: " + dynamicHttpRequestOpen);
//            }
//
//            @Override
//            public void onRequestError() {
//
//            }
//        });
//    }
//
//    static SharedPreferences getSp(Context context) {
//        if (context == null) {
//            return null;
//        }
//        return context.getSharedPreferences(SP_NAME_DYNAMIC_SETTING, Context.MODE_PRIVATE);
//    }
//
//    /**
//     * 更新内存和 sp 的数据
//     * @param dynamicAllOpen
//     * @param dynamicUseAssetsOpen
//     * @param dynamicHttpRequestOpen
//     */
//    private void updateSettingData(boolean dynamicAllOpen, boolean dynamicUseAssetsOpen, boolean dynamicHttpRequestOpen) {
//
//        synchronized (sSettingMap) {
//            sSettingMap.put(TemplateSetting.ALL, dynamicAllOpen);
//            sSettingMap.put(TemplateSetting.REQUEST_TEMPLATE, dynamicHttpRequestOpen);
//            sSettingMap.put(TemplateSetting.USE_ASSETS, dynamicUseAssetsOpen);
//        }
//
//        SharedPreferences sp = getSp(mContext);
//        if (sp == null) {
//            return;
//        }
//        sp.edit()
//                .putBoolean(TemplateSetting.ALL, dynamicAllOpen)
//                .putBoolean(TemplateSetting.REQUEST_TEMPLATE, dynamicHttpRequestOpen)
//                .putBoolean(TemplateSetting.USE_ASSETS, dynamicUseAssetsOpen)
//                .apply();
//        debugLog("[isSettingOpen] saveSettingToCache : " + dynamicAllOpen + dynamicHttpRequestOpen + ", dynamicUseAssetsOpen: " + dynamicUseAssetsOpen);
//    }
//
//    @Override
//    public Class<? extends TemplateDetail<?>> getTemplateDetailClass() {
//        return XmTemplateDetail.class;
//    }
//
//    @Override
//    public Context getContext() {
//        return mContext;
//    }
//
//    @Override
//    public String getAppVersionFour() {
//        return DeviceUtil.getVersionFour(mContext);
//    }
//
//    @Override
//    public IHttpRequester getHttpRequester() {
//        if (mHttpRequester == null) {
//            mHttpRequester = new HttpRequester();
//        }
//        return mHttpRequester;
//    }
//
//    @Override
//    public APMTraceConfig getAPMTraceConfig() {
//        if (mApmTraceConfig == null) {
//            mApmTraceConfig = new TemplateAPMTraceConfig(getSp(getContext()), AB_KEY_TEMPLATE_PERFORMANCE);
//        }
//        return mApmTraceConfig;
//    }
//
//    @Override
//    public ImageLoader.IImageLoaderAdapter getImageLoaderAdapter() {
//        if (mImageLoader == null) {
//            mImageLoader = new TemplateImageLoader(mContext);
//        }
//        return mImageLoader;
//    }
//
//    @Override
//    public ITemplateEventListener getTemplateEventListener() {
//        if (mEventListener == null) {
//            mEventListener = new EventListener();
//        }
//        return mEventListener;
//    }
//
//    @Override
//    public boolean isDebug() {
//        return ConstantsOpenSdk.isDebug;
////        return false;
//    }
//
//    @Override
//    public boolean isDarkMode() {
//        return BaseFragmentActivity2.sIsDarkMode;
//    }
//
//    /**
//     * 刚启动时，这个值是从 map 取的；配置更新后，会更新这个 map
//     * @param s
//     * @return
//     */
//    @Override
//    public boolean isSettingOpen(final String s) {
//        if (sSettingMap.isEmpty()) {
//            debugLog("isSettingOpen , empty map");
//            return true;
//        }
//        Boolean open = sSettingMap.get(s);
//
//        debugLog("isSettingOpen , " + s + ", open? " + open);
//        return open == Boolean.TRUE;
//    }
//
//    @Override
//    public IAbTestProvider getAbTestProvider() {
//        if (mAbTestProvider == null) {
//            mAbTestProvider = new AbTestProvider();
//        }
//        return mAbTestProvider;
//    }
//
//    private void debugLog(String msg) {
//        if (!ConstantsOpenSdk.isDebug) {
//            return;
//        }
//        TemplateManager.getInstance().log(msg);
//    }
//
//    public class HttpRequester implements IHttpRequester {
//
//        @Override
//        public void post(final String s, final JSONObject params, final IDataCallback<JSONArray> iDataCallback) {
//            String url = UrlConstants.getInstanse().getServerNetMcdAddressHost() + s;
//            String jsonString = params != null ? params.toString() : "";
//
//            if (TemplateManager.getInstance().isDebug()) {
//                debugLog("post " + url + ", jsonString: " + jsonString);
//            }
//
//            CommonRequestM.basePostRequestWithStr(url, jsonString, new IDataCallBack<JSONArray>() {
//                @Override
//                public void onSuccess(@Nullable final JSONArray data) {
//                    iDataCallback.onSuccess(data);
//                }
//
//                @Override
//                public void onError(int code, final String message) {
//
//                    boolean connectToNetwork = mContext != null && NetworkType.isConnectTONetWork(mContext);
//                    if (connectToNetwork) {
//                        code = 666;
//                    }
//                    iDataCallback.onError(code, message);
//                }
//            }, content -> {
//                JSONObject jb = new JSONObject(content);
//                if (jb.optInt("ret") != 0 || !jb.has("data")) {
//                    return null;
//                }
//                JSONObject data = jb.getJSONObject("data");
//                if (data != null) {
//                    return data.getJSONArray("templates");
//                }
//                return null;
//            });
//        }
//
//        @Override
//        public void download(final String url, final String savePath, final IDataCallback<String> iDataCallback) {
//            if (mContext == null || TextUtils.isEmpty(url)) {
//                debugLog("download failed" + mContext + ", " + url );
//                return;
//            }
//            File templateDir = mContext.getDir("template", Context.MODE_PRIVATE);
//            File templateFile = new File(templateDir, savePath);
//            String templateFileAbsolutePath = templateFile.getAbsolutePath();
//
//            if (!templateFile.exists()) {
//                templateFile.mkdirs();
//            }
//
//            Request.Builder builder = null;
//            try {
//                builder = BaseBuilder.urlGet(url, null);
//                builder.header("user-agent", SerialInfo.getUserAgent(mContext));
//                debugLog("download direct s1 >>> " + url);
//                Response response = BaseCall.getInstanse().doSync(builder.build());
//                if (response == null || response.body() == null) {
//                    iDataCallback.onError(DOWNLOAD_ERROR_CODE_NO_RESPONSE, "没有拿到响应体：" + response);
//                    return;
//                }
//                int code = response.code();
//                byte[] data = response.body().bytes();
//                debugLog( "download direct, s2 code: " + code + "bytes: " + data.length );
//
//                if (data != null && data.length > 0) {
//                    saveFile(data, templateFile);
//                    debugLog("download direct s3 saveFile, file length: " + templateFile.length() + ", data length: " + data.length);
//                    iDataCallback.onSuccess(templateFileAbsolutePath);
//                } else {
//                    iDataCallback.onError(DOWNLOAD_ERROR_CODE_NO_DATA, "没有拿到数据, code:" + code );
//                }
//
//            } catch (Exception e) {
//                e.printStackTrace();
//                Log.e(TAG, "download direct failed" + e.getMessage());
//                iDataCallback.onError(-1, e.getMessage());
//            }
//        }
//
//        public void saveFile(byte[] buffer, File file) throws Exception {
//            OutputStream fos = null;
//            BufferedOutputStream bos = null;
//            try {
//                if (file.exists()) {
//                    file.delete();
//                }
//                file.createNewFile();
//                fos = new FileOutputStream(file);
//                bos = new BufferedOutputStream(fos);
//                bos.write(buffer, 0, buffer.length);
//                bos.flush();
//            } catch (Throwable e) {
//                Log.e(TAG, "saveFile failed: " + e.getMessage() );
//                throw e;
//            } finally {
//                FileUtil.close(fos);
//                FileUtil.close(bos);
//            }
//        }
//    }
//
//    public class EventListener implements ITemplateEventListener {
//
//        @Override
//        public void onLog(final String log) {
//            if (!ConstantsOpenSdk.isDebug) {
//                return;
//            }
//            Log.d("XTemplate", "onLog: " + log);
//        }
//
//        @Override
//        public void onEventReport(String s, String s1) {
//            XDCSCollectUtil.statErrorToXDCS(s, s1);
////            Log.e("XTemplate", "onEventReport: " + s + ", " + s1 );
//            mErrorLogList.add(new SoftReference<>(s + " : " + s1 ));
//        }
//
//        @Override
//        public void onAPMTrace(String subType, String msg) {
//            XmLogger.log("apm", subType, msg);
//
//            if (ConstantsOpenSdk.isDebug) {
//                debugLog("onAPMTrace: " + subType + ", \n" + msg);
//            }
//        }
//
//        @Override
//        public void sendTrace(final int type, final String s) {
//            try {
////                Log.e("111", "sendTrace: type: " + type + "  trace: " + s);
//                JSONObject object = new JSONObject(s);
//                int metaId = object.optInt("metaId", -1);
//                if (metaId == -1) {
//                    throw new Exception("metaId error");
//                }
//                String srcModule = object.optString("srcModule", null);
//                if (TextUtils.isEmpty(srcModule) || "null".equals(srcModule)) {
//                    srcModule = null;
//                }
//                HashMap<String, String> props = new HashMap<>();
//                JSONObject propsJson = object.optJSONObject("props");
//                if (propsJson != null) {
//                    Iterator<String> iterator = propsJson.keys();
//                    while (iterator.hasNext()) {
//                        String key = iterator.next();
//                        String  value = propsJson.getString(key);
//                        if (TextUtils.isEmpty(value) || "null".equals(value)) {
//                            continue;
//                        }
//                        props.put(key, value);
//                    }
//                }
//                XMTraceApi.Trace trace = new XMTraceApi.Trace();
//                switch (type) {
//                    case EventManager.TYPE_Click:
//                        trace.click(metaId);
//                        break;
//                    case EventManager.TYPE_Exposure:
//                        trace.slipPage(metaId);
//                        break;
//                    default:
//                        return;
//                }
//                if (srcModule != null) {
//                    trace.srcModule(srcModule);
//                }
//                if (props != null && !props.isEmpty()) {
//                    Iterator<Map.Entry<String, String>> iterator = props.entrySet().iterator();
//                    while (iterator.hasNext()) {
//                        Map.Entry<String, String> item = iterator.next();
//                        trace.put(item.getKey(), item.getValue());
//                    }
//                }
//                if (object.has("isLocalCache")) {
//                    boolean isLocalCache = false;
//                    try {
//                        isLocalCache = object.getBoolean("isLocalCache");
//                    } catch (JSONException e) {
//                        e.printStackTrace();
//                    }
//                    if (isLocalCache) {
//                        trace.isLocalCache();
//                    }
//                }
//                trace.createTrace();
//            } catch (Exception e) {
//
//            }
//        }
//    }

    public static class TemplateImageLoader implements ImageLoader.IImageLoaderAdapter {

        private final Context mContext;

        public TemplateImageLoader(final Context context) {
            mContext = context;
        }

        @Override
        public void bindImage(String uri, String placeHolder, ImageBase imageBase, int reqWidth, int reqHeight, ImageLoader.DisplayCallback callback) {
            if (imageBase == null) {
                return;
            }
            boolean isNotAllowGifLoadAgain = ConfigureCenter.getInstance()
                    .getBool(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.KEY_IS_NOT_ALLOW_GIF_LOAD_AGAIN, false);
            //解决从其他页面返回图片闪烁的问题
            ImageView nativeView = (ImageView) imageBase.getNativeView();
            if (nativeView != null) {
                if (ImageManager.isGifUrl(uri) && isNotAllowGifLoadAgain) {
                    Object showingUrl = nativeView.getTag(com.ximalaya.ting.android.framework.R.id.framework_img_showing_url);
                    if (showingUrl instanceof String && ((String) showingUrl).length() > 0 && nativeView.getDrawable() != null) {
                        Drawable drawable = nativeView.getDrawable();
                        if (drawable != null) {
                            int hash = drawable.hashCode();
                            // 有可能在外面给ImageView设置了新的图，所以通过url加上bitmap的hashcode结合判断，避免外部设置图片后，加载同一个url的图不会更新的情况
                            if (showingUrl.equals(uri + hash)) {
                                return;
                            }
                        }
                    }
                } else {
                    Object showingUrl = nativeView.getTag(com.ximalaya.ting.android.framework.R.id.framework_img_showing_url);
                    if (showingUrl instanceof String && ((String) showingUrl).length() > 0 && nativeView.getDrawable() != null) {
                        Bitmap bitmap = ImageManager.getBitmapFromDrawable(nativeView.getDrawable());
                        if (bitmap != null) {
                            int hash = bitmap.hashCode();
                            // 有可能在外面给ImageView设置了新的图，所以通过url加上bitmap的hashcode结合判断，避免外部设置图片后，加载同一个url的图不会更新的情况
                            if (showingUrl.equals(uri + hash)) {
                                return;
                            }
                        }
                    }
                }
            }

            if (uri != null) {
                Bitmap fromMemCache = ImageManager.from(mContext).getFromMemCache(uri);
                if (fromMemCache != null) {
                    imageBase.setBitmap(fromMemCache);
//                    Log.d("z_test", "bindImage findFromCache!!! " + uri);
                    return;
                }
            }

            if (placeHolder != null) {
                int resId = LocalImageProvider.getLocalImage(placeHolder);
                if (resId > 0) {
                    try {
                        Drawable drawable = mContext.getResources().getDrawable(resId);
                        imageBase.setImageDrawable(drawable, true);
                    } catch (Throwable ignore) {}
                } else {
                    Drawable placeHolderDrawable = ImageLoader.getLocalDrawable(placeHolder, imageBase);
                    imageBase.setImageDrawable(placeHolderDrawable, true);
                }
            }
            if (uri == null) {
                return;
            }

            if (nativeView != null) {
                if (reqWidth == 0 || reqHeight == 0) {
                    //还没加载完全时，宽度-高度可能为 0，会触发 ImageManager 里的 defer request 逻辑，这里改为 -1 避免进入这个逻辑
//                    Log.w("z_test", "bindImage size error " + reqWidth + ", " + reqHeight + ", " + uri);
                    reqWidth = (reqWidth > 0) ? reqWidth : -1;
                    reqHeight = (reqHeight > 0) ? reqHeight : -1;
                }
                //Native ImageView
                ImageManager.from(mContext).displayImageNotIncludeDownloadCache(nativeView, uri, -1, reqWidth, reqHeight, new ImageManager.DisplayCallback() {
                    @Override
                    public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
//                        if (nativeView.getDrawable() instanceof FrameSequenceDrawable) {
//                            nativeView.addOnAttachStateChangeListener(mOnAttachStateChangeListener);
//                        }
                        if (callback != null) {
                            callback.onCompleteDisplay(lastUrl, bitmap);
                        }
                    }
                });
                return;
            }

            ImageManager.Options options = null;
            if (reqHeight >0 || reqWidth > 0) {
                options = new ImageManager.Options();
                if (reqWidth > 0) {
                    options.targetWidth = reqWidth;
                }
                if (reqHeight > 0) {
                    options.targetHeight = reqHeight;
                }
            }
            //Virtual ImageView
            imageBase.setImageUrl(uri);
            ImageManager.from(mContext).downloadBitmap(uri, options, new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(final String lastUrl, final Bitmap bitmap) {
                    if (TextUtils.equals(lastUrl, imageBase.getImageUrl())) {
                        imageBase.setBitmap(bitmap);
                    }
                    if (callback != null) {
                        callback.onCompleteDisplay(lastUrl, bitmap);
                    }
                }
            });
        }

        @Override
        public void getBitmap(String uri, int reqWidth, int reqHeight, final ImageLoader.Listener lis) {
            if (lis == null || uri == null) {
                return;
            }
            ImageManager.Options options = null;
            if (reqHeight >0 || reqWidth > 0) {
                options = new ImageManager.Options();
                if (reqWidth > 0) {
                    options.targetWidth = reqWidth;
                }
                if (reqHeight > 0) {
                    options.targetHeight = reqHeight;
                }
            }
            ImageManager.from(mContext).downloadBitmap(uri, options, new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(final String lastUrl, final Bitmap bitmap) {
                    if (bitmap != null) {
                        lis.onImageLoadSuccess(bitmap);
                    } else {
                        lis.onImageLoadFailed();
                    }
                }
            });
        }
    }

//    private static MyOnAttachStateChangeListener mOnAttachStateChangeListener = new MyOnAttachStateChangeListener();
//
//    public static class MyOnAttachStateChangeListener implements View.OnAttachStateChangeListener {
//        @Override
//        public void onViewAttachedToWindow(View v) {
//            if (v instanceof ImageView) {
//                Drawable drawable = ((ImageView) v).getDrawable();
//                if (drawable instanceof FrameSequenceDrawable) {
//                    ((FrameSequenceDrawable)drawable).start();
//                }
//            }
//        }
//
//        @Override
//        public void onViewDetachedFromWindow(View v) {
//            if (v instanceof ImageView) {
//                Drawable drawable = ((ImageView) v).getDrawable();
//                if (drawable instanceof FrameSequenceDrawable) {
//                    ((FrameSequenceDrawable)drawable).stop();
//                    ((ImageView) v).setImageDrawable(null);
//                }
//            }
//        }
//    }
//
//    public static class AbTestProvider implements IAbTestProvider {
//
//        @Override
//        public String getString(String abKey, String defaultValue) {
//            String abValue = getSp(MainApplication.getMyApplicationContext()).getString(abKey, defaultValue);
//            Logger.i("cf_test","动态模板abkey___读取:___" + abKey +"_____" + abValue);
//            return abValue;
//        }
//    }
}
