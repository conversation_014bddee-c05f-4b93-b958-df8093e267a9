package com.ximalaya.ting.android.host.util.xchat;

import androidx.annotation.NonNull;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @since 2017/11/15
 */

public class XChatThreadPools {

    private static class SingletonHolder {
        private static final XChatThreadPools INSTANCE = new XChatThreadPools();
    }

    public static XChatThreadPools getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private XChatThreadPools() {

    }

    private static ExecutorService mSimpleTaskPool =
            new ThreadPoolExecutor(0,
                    Runtime.getRuntime().availableProcessors() * 2 + 1,
                    0, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(),
                    new XChatThreadFactory("simple-task-"));


    static class XChatThreadFactory implements ThreadFactory {

        private static final String suffix = "xchat-";

        private String mType = "";

        private AtomicInteger index = new AtomicInteger(0);

        public XChatThreadFactory(String type) {

            mType = type;
        }

        @Override
        public Thread newThread(@NonNull Runnable r) {

            return new Thread(r, suffix + mType + index.getAndIncrement());
        }
    }

    public void submitSimpleTask(Runnable r) {

        mSimpleTaskPool.submit(r);
    }
}
