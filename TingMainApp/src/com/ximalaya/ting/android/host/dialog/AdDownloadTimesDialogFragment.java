package com.ximalaya.ting.android.host.dialog;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV3;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.download.DownloadIncentiveAdManager;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 下载增加激励视频拦截弹窗
 */
public class AdDownloadTimesDialogFragment extends BaseDialogFragment {
    private ViewGroup mContainer;
    private View mCloseIV;
    private TextView mTitle;
    private TextView mDownloadButton;
    private TextView mWatchVideoButton1;
    private TextView mWatchVideoButton2;
    private TextView mOpenVipButton;

    private boolean mIsTimeEnough;
    private int mRemainTimes;
    private IActionCallBack mActionCallBack;
    private String mSourceName;

    private int onceRewardDownloadTimes = 20; // 看一次视频增加的下载次数

    private String titleStr;
    private String downloadButtonStr;
    private String watchVideoButtonStr;
    private String openVipButtonStr;
    private String openVipUrlStr;

    public static AdDownloadTimesDialogFragment getInstance(boolean isTimesEnough, int remainTimes, IActionCallBack callback, String sourceName) {
        AdDownloadTimesDialogFragment dialogFragment = new AdDownloadTimesDialogFragment();
        dialogFragment.mIsTimeEnough = isTimesEnough;
        dialogFragment.mRemainTimes = remainTimes;
        dialogFragment.mActionCallBack = callback;
        dialogFragment.mSourceName = sourceName;
        return dialogFragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        initConfig();
        onceRewardDownloadTimes = DownloadIncentiveAdManager.getOnceRewardDownloadTimes();
        Dialog dialog = getDialog();
        if (dialog != null) {
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
            Window window = dialog.getWindow();
            if (window != null) {
                window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                window.setWindowAnimations(-1);
            }
        }

        View contentView = inflater.inflate(R.layout.host_ad_download_times_dialog_layout, container, false);
        mContainer = contentView.findViewById(R.id.main_dialog_layout);
        mContainer.setVisibility(View.INVISIBLE);
        mCloseIV = contentView.findViewById(R.id.main_close);
        mTitle = contentView.findViewById(R.id.main_title);
        mDownloadButton = contentView.findViewById(R.id.main_download);
        mOpenVipButton = contentView.findViewById(R.id.main_open_vip);
        mWatchVideoButton1 = contentView.findViewById(R.id.main_watch_video_1);
        mWatchVideoButton2 = contentView.findViewById(R.id.main_watch_video_2);
        if (mIsTimeEnough) {
            setTitle(String.format(titleStr, mRemainTimes));
            mDownloadButton.setText(downloadButtonStr);
            mDownloadButton.setVisibility(View.VISIBLE);
            mWatchVideoButton1.setText(String.format(watchVideoButtonStr, onceRewardDownloadTimes));
            mWatchVideoButton1.setVisibility(View.VISIBLE);
            mWatchVideoButton2.setVisibility(View.GONE);
            mOpenVipButton.setVisibility(View.GONE);
        } else {
            setTitle(String.format(titleStr, onceRewardDownloadTimes));
            mDownloadButton.setVisibility(View.GONE);
            mWatchVideoButton1.setVisibility(View.GONE);
            mWatchVideoButton2.setText(watchVideoButtonStr);
            mWatchVideoButton2.setVisibility(View.VISIBLE);
            mOpenVipButton.setText(openVipButtonStr);
            mOpenVipButton.setVisibility(View.VISIBLE);
        }
        mDownloadButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new XMTraceApi.Trace()
                        .setMetaId(65377)
                        .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                        .put("currPage", mSourceName)
                        .put("dialogTitle", mIsTimeEnough ? "下载次数充足" : "下载次数不足")
                        .put("Item", mDownloadButton.getText().toString())
                        .createTrace();
                if (mActionCallBack != null) {
                    mActionCallBack.onDownloadClick();
                }
                doHideAnimation();
            }
        });
        mWatchVideoButton1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new XMTraceApi.Trace()
                        .setMetaId(65377)
                        .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                        .put("currPage", mSourceName)
                        .put("dialogTitle", mIsTimeEnough ? "下载次数充足" : "下载次数不足")
                        .put("Item", mWatchVideoButton1.getText().toString())
                        .createTrace();
                watchVideo();
            }
        });
        mWatchVideoButton2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new XMTraceApi.Trace()
                        .setMetaId(65377)
                        .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                        .put("currPage", mSourceName)
                        .put("dialogTitle", mIsTimeEnough ? "下载次数充足" : "下载次数不足")
                        .put("Item", mWatchVideoButton2.getText().toString())
                        .createTrace();
                watchVideo();
            }
        });
        mOpenVipButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 下载激励弹窗-操作  弹框控件点击
                new XMTraceApi.Trace()
                        .setMetaId(65377)
                        .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                        .put("currPage", mSourceName)
                        .put("dialogTitle", mIsTimeEnough ? "下载次数充足" : "下载次数不足")
                        .put("Item", mOpenVipButton.getText().toString())
                        .createTrace();
                doHideAnimation();
                Activity activity = MainApplication.getOptActivity();
                if (activity instanceof MainActivity) {
                    MainActivity mainActivity = (MainActivity) activity;
                    NativeHybridFragment.start(mainActivity, openVipUrlStr, true);
                }
            }
        });

        mCloseIV.setOnClickListener(v -> {
            new XMTraceApi.Trace()
                    .setMetaId(65377)
                    .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                    .put("currPage", mSourceName)
                    .put("dialogTitle", mIsTimeEnough ? "下载次数充足" : "下载次数不足")
                    .put("Item", "关闭")
                    .createTrace();
            onClickClose();
            doHideAnimation();
        });
        contentView.setOnClickListener(v -> {
            onClickContent("");
            doHideAnimation();
        });
        mContainer.setOnClickListener(v -> {
            // 不响应点击
        });
        doShowAnimation();
        // 下载激励弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(65376)
                .setServiceId("dialogView") // 弹窗展示时上报
                .put("currPage", mSourceName)
                .put("dialogTitle", mIsTimeEnough ? "下载次数充足" : "下载次数不足")
                .createTrace();
        return contentView;
    }

    private void setTitle(String titleString) {
        String[] split = titleString.split("\\*");
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder();
        spannableStringBuilder.append(split[0]);
        spannableStringBuilder.setSpan(new ForegroundColorSpan(getContext().getResources().getColor(R.color.host_color_333333_ffffff)),
                0, spannableStringBuilder.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        if (split.length > 1) {
            spannableStringBuilder.append(split[1]);
            spannableStringBuilder.setSpan(new ForegroundColorSpan(Color.parseColor("#ff4444")),
                    spannableStringBuilder.length() - split[1].length(), spannableStringBuilder.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            if (split.length > 2) {
                spannableStringBuilder.append(split[2]);
                spannableStringBuilder.setSpan(new ForegroundColorSpan(getContext().getResources().getColor(R.color.host_color_333333_ffffff)),
                        spannableStringBuilder.length() - split[2].length(), spannableStringBuilder.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        }
        mTitle.setText(spannableStringBuilder);
    }

    private void initConfig() {
        if (mIsTimeEnough) {
            titleStr = "今日剩余*%d次*下载";
            downloadButtonStr = "立即下载";
            watchVideoButtonStr = "看视频获%d次下载";
        } else {
            titleStr = "下载次数不足，看视频可再获*%d次*下载次数";
            watchVideoButtonStr = "去看视频";
            openVipButtonStr = "开通会员无限下载";
            openVipUrlStr = "https://m.ximalaya.com/vip/product";
        }
        JSONObject config = ConfigureCenter.getInstance().getJson(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_DOWNLOAD_INCENTIVE_CONFIG);
        if (config != null) {
            try {
                JSONObject dialogConfig = config.getJSONObject("dialogConfig");
                if (dialogConfig != null) {
                    if (mIsTimeEnough) {
                        JSONObject realConfig = dialogConfig.getJSONObject("timesEnough");
                        if (realConfig != null) {
                            titleStr = realConfig.getString("title");
                            downloadButtonStr = realConfig.getString("downloadButton");
                            watchVideoButtonStr = realConfig.getString("watchVideoButton");
                        }
                    } else {
                        JSONObject realConfig = dialogConfig.getJSONObject("timesNotEnough");
                        if (realConfig != null) {
                            titleStr = realConfig.getString("title");
                            watchVideoButtonStr = realConfig.getString("watchVideoButton");
                            openVipButtonStr = realConfig.getString("openVipButton");
                            openVipUrlStr = realConfig.getString("openVipUrl");
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    private void watchVideo() {
        doHideAnimation();
        final RewardExtraParams rewardExtraParams = new RewardExtraParams();
        rewardExtraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_DOWNLOAD);
        rewardExtraParams.setCanCloseTime(0);
        rewardExtraParams.setPositionName(AppConstants.AD_POSITION_NAME_DOWNLOAD_REWARD_VIDEO);
        AdUnLockTimeManagerV3.getInstance().unlockTrack(PlayerConstants.PLAY_METHOD_MANUAL_PLAY, mIsTimeEnough ? "downloadDialogMore" : "downloadDialogLess", new AdUnLockTimeManagerV3.IAdUnLockStatusCallBack() {
            @Override
            public void onRewardSuccess(Advertis advertis, boolean realFinishTask) {
                // 增加下载次数
                DownloadIncentiveAdManager.addDownloadTimes(0, onceRewardDownloadTimes, advertis != null ? advertis.getAdid() : 0,
                        advertis != null ? advertis.getResponseId() : 0,
                        new DownloadIncentiveAdManager.IDownLoadTimesCallBack() {
                    @Override
                    public void onSuccess(int remainTimes, String toast) {
                        if (!TextUtils.isEmpty(toast)) {
                            CustomToast.showToast(toast);
                        } else {
                            CustomToast.showToast("下载次数领取成功");
                        }
                        if (mActionCallBack != null) {
                            mActionCallBack.onAddTimesSuccess(remainTimes);
                        }
                    }

                    @Override
                    public void onFail(String toast) {
                        if (!TextUtils.isEmpty(toast)) {
                            CustomToast.showToast(toast);
                        } else {
                            CustomToast.showToast("下载次数领取失败");
                        }
                        if (mActionCallBack != null) {
                            mActionCallBack.onAddTimesFail();
                        }
                    }
                }, mSourceName);
            }

            @Override
            public void onRewardFail(String message) {
                if (mActionCallBack != null) {
                    mActionCallBack.onAddTimesFail();
                }
                CustomToast.showFailToast(message);
            }

            @Override
            public void onPlayStart() {

            }
        }, rewardExtraParams);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        Dialog dialog = getDialog();
        if (dialog != null) {
            Window window = dialog.getWindow();
            if (window != null) {
                window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            }
        }
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        super.show(manager, tag);
    }

    private void doShowAnimation() {
        ObjectAnimator tranYStart = ObjectAnimator.ofFloat(mContainer, "translationY", BaseUtil.dp2px(getContext(), 60), 0f);
        ObjectAnimator alphaStart = ObjectAnimator.ofFloat(mContainer, "alpha", 0f, 1.0f);
        AnimatorSet showAnimatorSet = new AnimatorSet();
        showAnimatorSet.setDuration(300);
        showAnimatorSet.playTogether(tranYStart, alphaStart);
        showAnimatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                mContainer.setVisibility(View.VISIBLE);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
            }
        });
        showAnimatorSet.start();
    }

    private void doHideAnimation() {
        ObjectAnimator tranYStart = ObjectAnimator.ofFloat(mContainer, "translationY", 0, BaseUtil.dp2px(getContext(), 60));
        ObjectAnimator alphaStart = ObjectAnimator.ofFloat(mContainer, "alpha", 1.0f, 0f);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.setDuration(300);
        animatorSet.playTogether(tranYStart, alphaStart);
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                dismiss();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                super.onAnimationCancel(animation);
                dismiss();
            }
        });
        animatorSet.start();
    }


    @Override
    public String getDialogSource() {
        return "download_reward_video_dialog";
    }

    @Override
    public String getBusinessId() {
        return "AdDownloadTimesDialogFragment";
    }

    public interface IActionCallBack {
        void onDownloadClick();
        void onAddTimesSuccess(int remainTimes);
        void onAddTimesFail();
    }
}