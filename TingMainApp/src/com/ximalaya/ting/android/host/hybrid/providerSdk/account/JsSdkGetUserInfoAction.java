package com.ximalaya.ting.android.host.hybrid.providerSdk.account;

import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.component.Component;

import org.json.JSONObject;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/18.
 * <AUTHOR>
 */

public class JsSdkGetUserInfoAction extends BaseJsSdkAccountAction {
    @Override
    public boolean needStatRunloop() {
        return false;
    }

    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args,
                         AsyncCallback callback, Component comp, String scheme) {
        super.doAction(hybridContainer, args, callback, comp, scheme);
        getAccountCallBackParams(hybridContainer, callback, true);
    }
}
