package com.ximalaya.ting.android.host.hybrid.providerSdk.busi;

import android.content.Context;
import android.os.Build;

import com.ximalaya.ting.android.host.manager.ShareResultManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.util.ShareUtils;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.component.Component;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;


/**
 * Created by chengyun.wu on 2018/11/14.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
public class ShareForCouponAction extends BaseJsSdkShareAction {

    private static final String TAG = "ShareForCouponAction";

    ShareResultManager.ShareListener mShareListener;

    @Override
    public boolean needStatRunloop() {
        return false;
    }

    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args, AsyncCallback callback, Component comp, String scheme) {
        super.doAction(hybridContainer, args, callback, comp, scheme);

        String type = args.optString("type");
        JSONObject jsonObject = args.optJSONObject("params");
        if (type == null || jsonObject == null) {
            callback.callback(NativeResponse.fail(-1, "参数错误"));
        }

        long id = jsonObject.optLong("albumId", -1);
        if (id == -1) {
            id = args.optLong("activityId");
        }
        long couponId = jsonObject.optLong("couponId");

        ShareUtils.shareCoupon(hybridContainer.getActivityContext(), type, id, couponId, ICustomShareContentType.SHARE_TYPE_COUPON);
        mShareListener = new ShareResultManager.ShareListener() {
            @Override
            public void onShareSuccess(String thirdName) {
                JSONObject result = createShareResult(hybridContainer.getActivityContext(), 0, thirdName);
                if (result != null) {
                    callback.callback(NativeResponse.success(result));
                }
                ShareResultManager.getInstance().clearShareFinishListener();
                unregisterCancelReceiver(hybridContainer);
            }

            @Override
            public void onShareFail(String thirdName) {
                Logger.d(TAG, "onShareFail IN");

                JSONObject result = createShareResult(hybridContainer.getActivityContext(), -1, thirdName);
                if (result != null) {
                    callback.callback(NativeResponse.fail(-1, "onShareFail", result));
                }
                ShareResultManager.getInstance().clearShareFinishListener();
                unregisterCancelReceiver(hybridContainer);
            }
        };

        ShareResultManager.getInstance().setShareFinishListener(mShareListener);
    }

    @Override
    public void reset(IHybridContainer container) {
        ShareResultManager.getInstance().clearShareFinishListener();
        mShareListener = null;
        super.reset(container);
    }

    @Override
    public void onDestroy(IHybridContainer container) {
        ShareResultManager.getInstance().clearShareFinishListener();
        mShareListener = null;
        super.onDestroy(container);
    }

    private JSONObject createShareResult(Context context, int ret, String channel) {
        Logger.d(TAG, "createMultiShareResult IN");
        try {
            JSONObject result = new JSONObject();
            result.put("ret", ret);
            if (ret == 0) {
                result.put("msg", "分享成功");
            } else {
                result.put("msg", "分享失败");
            }
            JSONObject dataJson = new JSONObject();
            dataJson.put("device", "android");
            dataJson.put("deviceId", DeviceUtil.getDeviceToken(context));
            dataJson.put("appVersion", CommonRequestM.getInstanse().getVersionName());
            dataJson.put("osVersion", String.valueOf(Build.VERSION.SDK_INT));
            dataJson.put("channel", channel);

            result.put("data", dataJson);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
