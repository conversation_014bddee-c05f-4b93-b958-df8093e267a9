package com.ximalaya.ting.android.host.hybrid.providerSdk.busi;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ximalaya.ting.android.host.manager.share.ShareConstants;
import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.component.Component;
import com.ximalaya.ting.android.hybridview.provider.BaseAction;

import org.json.JSONObject;

/**
 * Created by chengyun.wu on 2018/11/20.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
public abstract class BaseJsSdkShareAction extends BaseAction {

    BroadcastReceiver broadcastReceiver;

    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args,
                         AsyncCallback callback, Component comp, String scheme) {
        super.doAction(hybridContainer, args, callback, comp, scheme);

        if(broadcastReceiver != null){
            LocalBroadcastManager.getInstance(hybridContainer.getActivityContext()).unregisterReceiver(broadcastReceiver);
        }

        broadcastReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                callback.callback(NativeResponse.fail(-1, "分享取消"));
                LocalBroadcastManager.getInstance(hybridContainer.getActivityContext()).unregisterReceiver(this);
            }
        };
        LocalBroadcastManager.getInstance(hybridContainer.getActivityContext()).registerReceiver(broadcastReceiver,
                new IntentFilter(ShareConstants.ACTION_CANCEL_SHARE_DIALOG));
    }

    @Override
    public void reset(IHybridContainer container) {
        super.reset(container);
        if(broadcastReceiver != null){
            LocalBroadcastManager.getInstance(container.getActivityContext()).unregisterReceiver(broadcastReceiver);
        }
    }

    @Override
    public void onDestroy(IHybridContainer container) {
        super.onDestroy(container);
        if(broadcastReceiver != null){
            LocalBroadcastManager.getInstance(container.getActivityContext()).unregisterReceiver(broadcastReceiver);
        }
    }

    protected void unregisterCancelReceiver(IHybridContainer container){
        if(broadcastReceiver != null){
            LocalBroadcastManager.getInstance(container.getActivityContext()).unregisterReceiver(broadcastReceiver);
        }
    }
}
