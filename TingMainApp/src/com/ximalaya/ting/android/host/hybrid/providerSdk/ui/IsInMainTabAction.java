package com.ximalaya.ting.android.host.hybrid.providerSdk.ui;

import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.host.fragment.MainBottomTabProvider;
import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.IJsSdkContainer;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.provider.BaseAction;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2023/12/14
 * Description：是否在首页等一级页面
 */
public class IsInMainTabAction extends BaseAction {
    @Override
    protected boolean needStatRunloop() {
        return false;
    }

    @Override
    public void doAction(IJsSdkContainer hybridContainer, JSONObject args, AsyncCallback callback, String url) {
        super.doAction(hybridContainer, args, callback, url);
        Fragment fragment = hybridContainer.getAttachFragment();
        JSONObject jsonObject = new JSONObject();
        if (fragment != null && (fragment instanceof MainBottomTabProvider || fragment.getParentFragment() instanceof MainBottomTabProvider)){
            try {
                jsonObject.put("isInMainTab", true);
                callback.callback(NativeResponse.success(jsonObject));
            } catch (JSONException e) {
                e.printStackTrace();
                callback.callback(NativeResponse.fail(-1, e.getMessage()));
            }
        } else {
            try {
                jsonObject.put("isInMainTab", false);
                callback.callback(NativeResponse.success(jsonObject));
            } catch (JSONException e) {
                e.printStackTrace();
                callback.callback(NativeResponse.fail(-1, e.getMessage()));
            }
        }
    }

    @Override
    public void reset(IHybridContainer container) {
        super.reset(container);
    }
}
