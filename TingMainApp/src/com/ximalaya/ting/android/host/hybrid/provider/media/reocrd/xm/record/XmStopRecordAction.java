package com.ximalaya.ting.android.host.hybrid.provider.media.reocrd.xm.record;


import com.ximalaya.ting.android.host.hybrid.provider.media.reocrd.xm.XmBaseMediaAction;
import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.component.Component;

import org.json.JSONObject;

/**
 * 停止录音
 * <AUTHOR>
 */

public class XmStopRecordAction extends XmBaseMediaAction {


    @Override
    public boolean needStatRunloop() {
        return false;
    }

    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args, AsyncCallback callback,
                         Component comp, String scheme) {
        super.doAction(hybridContainer, args, callback, comp, scheme);
        stopRecord(hybridContainer, callback);
    }
}
