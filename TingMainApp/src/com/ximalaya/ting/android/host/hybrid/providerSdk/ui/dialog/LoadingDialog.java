package com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.R;

/**
 * Created by chengyun.wu on 2018/1/23.
 *
 * <AUTHOR>
 */

public class LoadingDialog extends XmBaseDialog {

    private String mTitle;
    private boolean showIcon;
    private boolean showBg;
    private TextView mTvTitle;
    private ProgressBar mProgressbar;

    public LoadingDialog(@NonNull Context context) {
        super(context, R.style.host_share_dialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        initUI();
        initView();
        apply();
    }

    private void apply() {
        if (!TextUtils.isEmpty(mTitle)) {
            mTvTitle.setText(mTitle);
            mTvTitle.setVisibility(View.VISIBLE);
            mProgressbar.setVisibility(showIcon ? View.VISIBLE : View.GONE);
        }else {
            mProgressbar.setVisibility(View.VISIBLE);
        }
    }


    public void setTitle(String title) {
        this.mTitle = title;
    }

    public void showIcon(boolean show) {
        showIcon = show;
    }

    public void showBg(boolean show) {
        showBg = show;
    }

    private void initView() {
        mTvTitle = (TextView) findViewById(R.id.tv_progress);
        mProgressbar = (ProgressBar) findViewById(R.id.pb_progress);
    }

    private void initUI() {
        setContentView(R.layout.hybrid_dialog_loading);
        Window dialogWindow = getWindow();
        if (!showBg) {
            dialogWindow.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        }
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = WindowManager.LayoutParams.WRAP_CONTENT;
        dialogWindow.setGravity(Gravity.CENTER);
        dialogWindow.setAttributes(lp);
        setCanceledOnTouchOutside(false);
    }
}
