package com.ximalaya.ting.android.host.model.play;

import android.os.SystemClock;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.typeadapter.annotation.GenerateGsonTypeAdapter;

import java.io.Serializable;

/**
 * Created by 5Greatest on 2021.03.20
 *
 * <AUTHOR>
 * On 2021/3/20
 */
@GenerateGsonTypeAdapter
public class PlayPromotionGuideInfo implements Serializable {
    public static final int BUTTON_TYPE_URL = 2;
    public static final int BUTTON_TYPE_PURCHASE = 1;

    public static final int TYPE_VIP = 1;                          // 会员
    public static final int TYPE_TIME_LIMIT_FREE = 2;              // 限时免费听
    public static final int TYPE_LIMIT_FREE_NEW_GOODS = 3;         // 新品限免

    @SerializedName("countDownText")
    public String countDownText;
    @SerializedName("type")
    public int type;                        // 转化类型
    @SerializedName("showPrice")
    public boolean showPrice;               // 是否显示价格
    @SerializedName("guidance")
    public String guidance;                 // 引导文案
    @SerializedName("explainText")
    public String explainText;              // 说明文案
    @SerializedName("buttonText")
    public String buttonText;               // 按钮文案
    @SerializedName("buttonActionId")
    public int buttonActionId;              // 购买行为id： 1购买专辑 2跳转链接
    @SerializedName("buttonActionUrl")
    public String buttonActionUrl;          // 按钮链接
    @SerializedName("playFinishedVoiceUrl")
    public String playFinishedVoiceUrl;
    @SerializedName("expireTime")
    public long expireTime;                 // 权限过期时间
    @SerializedName("countdownText")
    public String countdownText;            // 倒计时类型文案
    @SerializedName("paoPaoTipsInfo")
    public FloatingTipInfo paoPaoTipsInfo;
    @SerializedName("dataAnalysis")
    public String dataAnalysis;             // 埋点数据
    @SerializedName("businessLogo")
    public PlayBusinessLogo businessLogo;   // 业务logo
    @SerializedName("newGuidanceText")
    public String newGuidanceText;          // 新版播放页小黄条引导文案，格式为“%s后限免结束”，其中%s为占位符


    public long localStartTimeOnDataGot = System.currentTimeMillis();
    public long localTimeStampOnDataGot = SystemClock.elapsedRealtime();

    /**
     * 倒计时类型描述文案
     */
    public static String getDescription(PlayPromotionGuideInfo guideInfo) {
        if (null == guideInfo) {
            return null;
        }
        if (!StringUtil.isEmpty(guideInfo.countdownText)) {
            return guideInfo.countdownText;
        }
        return "剩余时间";
    }

    @GenerateGsonTypeAdapter
    public static class FloatingTipInfo implements Serializable {
        @SerializedName("trackTipsText")
        public String trackTipsText;        // 播放页线上
        @SerializedName("icon")
        public String icon;                 // 权益icon 链接 会员权限下会返回
    }
}
