package com.ximalaya.ting.android.host.model.postbox

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Created by WolfXu on 2022/9/27.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
@Parcelize
data class MailBoxInfo(
    val recordId: Long = 0L,
    val title: String? = null,
    val subTitle: String? = null,
    var likeIcon: String? = null,
    var likeBeforeText: String? = null,
    var likeAfterText: String? = null,
    val type: Long = 0
) : Parcelable
