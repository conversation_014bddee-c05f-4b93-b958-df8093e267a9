package com.ximalaya.ting.android.host.model.imchat;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/5/3.
 * <AUTHOR>
 */

public class ImChatReportInfo implements Parcelable {

    public long     mMyUid;
    public long     mToUid;
    public long     mMsgId;
    public String   mMsgContent;
    public long     mCreateTime;

    public ImChatReportInfo(long mMyUid, long mToUid, long mMsgId, String mMsgContent, long mCreateTime) {
        this.mMyUid = mMyUid;
        this.mToUid = mToUid;
        this.mMsgId = mMsgId;
        this.mMsgContent = mMsgContent;
        this.mCreateTime = mCreateTime;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.mMyUid);
        dest.writeLong(this.mToUid);
        dest.writeLong(this.mMsgId);
        dest.writeString(this.mMsgContent);
        dest.writeLong(this.mCreateTime);
    }

    protected ImChatReportInfo(Parcel in) {
        this.mMyUid = in.readLong();
        this.mToUid = in.readLong();
        this.mMsgId = in.readLong();
        this.mMsgContent = in.readString();
        this.mCreateTime = in.readLong();
    }

    public static final Creator<ImChatReportInfo> CREATOR = new Creator<ImChatReportInfo>() {
        @Override
        public ImChatReportInfo createFromParcel(Parcel source) {
            return new ImChatReportInfo(source);
        }

        @Override
        public ImChatReportInfo[] newArray(int size) {
            return new ImChatReportInfo[size];
        }
    };
}
