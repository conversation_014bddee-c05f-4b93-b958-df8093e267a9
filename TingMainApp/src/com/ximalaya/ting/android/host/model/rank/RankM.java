package com.ximalaya.ting.android.host.model.rank;

import com.ximalaya.ting.android.opensdk.model.ranks.Rank;
import com.ximalaya.ting.android.opensdk.model.ranks.RankItem;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

 /**
  * <AUTHOR>
  */
public class RankM extends Rank {

	// kind String 固定值"rank"
	// rank_title String 榜单标题
	// rank_type Int 榜单类型，1-节目榜单
	// rank_sub_title String 榜单副标题
	// rank_period Int 榜单计算周期，单位为天
	// rank_period_type String 榜单计算周期类型，比如“日榜”、“周榜”等
	// rank_item_num Int 该榜单内条目总数，比如100
	// rank_order_num Int 该榜单相对其他榜单的排序值，值越小越靠前
	// cover_url String 榜单封面图URL
	// category_id Long 榜单所属分类ID
	// rank_content_type String 榜单内容类型，album-专辑，track-声音
	// rank_first_item_id Long 榜单内排名第一的条目的ID
	// rank_first_item_title String 榜单内排名第一的条目的标题
	// index_rank_items Array 榜单首页显示的条目列表，包含下列字段：
	//  id：条目ID，比如声音或专辑的ID（Long类型）
	//  title：条目标题，比如声音或专辑的ID（String类型）
	//       content_type：条目内容类型，album -专辑，track-声音（String类型）

	private String calcPeroid;
	private String rankingRule;
	private String coverPath;
	private String subtitle;
	private int rankingListId;
    public long rankClusterId;

	public RankM(String json) {
		try {
			JSONObject job = new JSONObject(json);
			setCategoryId(job.optLong("categoryId"));
			calcPeroid = job.optString("calcPeroid");
			setRankContentType(job.optString("contentType"));
			setCoverUrl(job.optString("coverPath"));
			setRankFirstItemId(job.optLong("firstId"));
			setRankFirstItemTitle(job.optString("firstTitle"));
			setRankKey(job.optString("key"));
			setRankOrderNum(job.optInt("orderNum"));
			setRankPeriod(job.optInt("period"));
			rankingRule = job.optString("rankingRule");
			setRankTitle(job.optString("title"));
			coverPath = job.optString("coverPath");
			subtitle = job.optString("subtitle");
			rankingListId = job.optInt("rankingListId");
            rankClusterId = job.optLong("rankClusterId");
			List<RankItem> rankItemMs = new ArrayList<RankItem>();
			JSONArray jsonArray = job.optJSONArray("firstKResults");
			if (jsonArray != null) {
				for (int i = 0; i < jsonArray.length(); i++) {
					rankItemMs.add(new RankItemM(jsonArray.optString(i)));
				}
			}

			setRankItemList(rankItemMs);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public String getCalcPeroid() {
		return calcPeroid;
	}

	public void setCalcPeroid(String calcPeroid) {
		this.calcPeroid = calcPeroid;
	}

	public String getRankingRule() {
		return rankingRule;
	}

	public void setRankingRule(String rankingRule) {
		this.rankingRule = rankingRule;
	}

	public String getCoverPath() {
		return coverPath;
	}

	public void setCoverPath(String coverPath) {
		this.coverPath = coverPath;
	}

	public String getSubtitle() {
		return subtitle;
	}

	public void setSubtitle(String subtitle) {
		this.subtitle = subtitle;
	}

	public int getRankingListId() {
		return rankingListId;
	}

	public void setRankingListId(int rankingListId) {
		this.rankingListId = rankingListId;
	}
}
