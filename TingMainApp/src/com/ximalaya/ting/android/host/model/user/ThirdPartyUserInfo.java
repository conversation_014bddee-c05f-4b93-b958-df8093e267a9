package com.ximalaya.ting.android.host.model.user;

import com.google.gson.Gson;

import java.io.Serializable;

 /**
  * <AUTHOR>
  */
public class ThirdPartyUserInfo implements Serializable {
	public static final int THIRDPARTY_ID_SINA = 1;
	public static final int THIRDPARTY_ID_QQ = 2;
	public static final int THIRDPARTY_ID_WEIXIN = 4;

	private static final long serialVersionUID = 1L;
	private String thirdpartyId; // 在第三方的id
	private String nickname; // 第三方的昵称
	private String header; // 第三方的头像
	private String thirdpartyName; // 第三方名称
	private boolean isExpired;// 绑定是否过期
	private String homePage;// 在第三方的个人主页
	private String thirdpartyUid;// 第三方用户信息的id，数据库主键
	private boolean isInvite = false;
	private String name;// 账号名，用于@
	private String smallLogo;// ’:’’, //用户小头像
	private boolean isRealData = true;
	private boolean mobileAll; // 手机端分享设置（?
	private boolean webComment;// web端评论
	private boolean webTrack;// web端上传声音、分享?
	private boolean webAlbum;// web端上传专辑
	private boolean relay; // 转发 是否分享
	private boolean webFavorite; // 喜欢 是否分享
	private String thirdpartyNickname;
	private String identity;
	private String phoneHash;

	public ThirdPartyUserInfo() {
		super();
	}
	public ThirdPartyUserInfo(String json) {
		super();
		try {
			ThirdPartyUserInfo userInfo = new Gson().fromJson(json,
					ThirdPartyUserInfo.class);
			this.thirdpartyId = userInfo.thirdpartyId;
			this.nickname = userInfo.nickname;
			this.header = userInfo.header;
			this.thirdpartyName = userInfo.thirdpartyName;
			this.isExpired = userInfo.isExpired;
			this.homePage = userInfo.homePage;
			this.thirdpartyUid = userInfo.thirdpartyUid;
			this.isInvite = userInfo.isInvite;
			this.name = userInfo.name;
			this.smallLogo = userInfo.smallLogo;
			this.isRealData = userInfo.isRealData;
			this.mobileAll = userInfo.mobileAll;
			this.webComment = userInfo.webComment;
			this.webTrack = userInfo.webTrack;
			this.webAlbum = userInfo.webAlbum;
			this.relay = userInfo.relay;
			this.webFavorite = userInfo.webFavorite;
			this.thirdpartyNickname = userInfo.thirdpartyNickname;
			this.identity = userInfo.identity;
		}catch (Exception e){
			e.printStackTrace();
		}
	}
	public String getIdentity() {
		return identity;
	}

	public void setIdentity(String identity) {
		this.identity = identity;
	}


	public String getThirdpartyId() {
		return thirdpartyId;
	}

	public void setThirdpartyId(String thirdpartyId) {
		this.thirdpartyId = thirdpartyId;
	}

	public String getNickname() {
		return nickname;
	}

	public void setNickname(String nickname) {
		this.nickname = nickname;
	}

	public String getHeader() {
		return header;
	}

	public void setHeader(String header) {
		this.header = header;
	}

	public String getThirdpartyName() {
		return thirdpartyName;
	}

	public void setThirdpartyName(String thirdpartyName) {
		this.thirdpartyName = thirdpartyName;
	}

	public boolean isExpired() {
		return isExpired;
	}

	public void setExpired(boolean isExpired) {
		this.isExpired = isExpired;
	}

	public String getHomePage() {
		return homePage;
	}

	public void setHomePage(String homePage) {
		this.homePage = homePage;
	}

	public String getThirdpartyUid() {
		return thirdpartyUid;
	}

	public void setThirdpartyUid(String thirdpartyUid) {
		this.thirdpartyUid = thirdpartyUid;
	}

	public boolean isInvite() {
		return isInvite;
	}

	public void setInvite(boolean isInvite) {
		this.isInvite = isInvite;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSmallLogo() {
		return smallLogo;
	}

	public void setSmallLogo(String smallLogo) {
		this.smallLogo = smallLogo;
	}

	public boolean isRealData() {
		return isRealData;
	}

	public void setRealData(boolean isRealData) {
		this.isRealData = isRealData;
	}

	public boolean isMobileAll() {
		return mobileAll;
	}

	public void setMobileAll(boolean mobileAll) {
		this.mobileAll = mobileAll;
	}

	public boolean isWebComment() {
		return webComment;
	}

	public void setWebComment(boolean webComment) {
		this.webComment = webComment;
	}

	public boolean isWebTrack() {
		return webTrack;
	}

	public void setWebTrack(boolean webTrack) {
		this.webTrack = webTrack;
	}

	public boolean isWebAlbum() {
		return webAlbum;
	}

	public void setWebAlbum(boolean webAlbum) {
		this.webAlbum = webAlbum;
	}

	public boolean isRelay() {
		return relay;
	}

	public void setRelay(boolean relay) {
		this.relay = relay;
	}

	public boolean isWebFavorite() {
		return webFavorite;
	}

	public void setWebFavorite(boolean webFavorite) {
		this.webFavorite = webFavorite;
	}

	public String getThirdpartyNickname() {
		return thirdpartyNickname;
	}

	public void setThirdpartyNickname(String thirdpartyNickname) {
		this.thirdpartyNickname = thirdpartyNickname;
	}

	 public String getPhoneHash() {
		 return phoneHash;
	 }

	 public void setPhoneHash(String phoneHash) {
		 this.phoneHash = phoneHash;
	 }
 }
