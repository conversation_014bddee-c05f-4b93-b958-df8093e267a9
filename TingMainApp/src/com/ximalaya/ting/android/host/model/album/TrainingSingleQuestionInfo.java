package com.ximalaya.ting.android.host.model.album;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by 5Greatest on 2019.12.19
 *
 * <AUTHOR>
 * On 2019-12-19
 */
public class TrainingSingleQuestionInfo implements Parcelable {
    public TrainingSingleQuestion question;

    public TrainingSingleQuestionInfo(Parcel parcel) {
        question = parcel.readParcelable(TrainingSingleQuestion.class.getClassLoader());
    }

    public static final Creator<TrainingSingleQuestionInfo> CREATOR = new Creator<TrainingSingleQuestionInfo>() {
        @Override
        public TrainingSingleQuestionInfo createFromParcel(Parcel source) {
            return new TrainingSingleQuestionInfo(source);
        }

        @Override
        public TrainingSingleQuestionInfo[] newArray(int size) {
            return new TrainingSingleQuestionInfo[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeParcelable(question, flags);
    }
}
