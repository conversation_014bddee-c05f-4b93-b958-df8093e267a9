package com.ximalaya.ting.android.host.model.account

import androidx.annotation.Keep


/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2024/10/14
 */

@Keep
data class UserMemberCardInfo(
    val strategyType: String = "",
    val text: String = "",
    val url: String = ""
)

@Keep
data class VipCardEntrance(
    val cardInfo: List<UserMemberCardInfo> = listOf(),
    val vipStatus: Int,
    val vipTraitType: String,
    val textAfterVipLogo: String,
    val vipType: Int,
    val vipTypeDesc: String
)