package com.ximalaya.ting.android.host.model.play

import com.google.gson.annotations.SerializedName

/**
 * Create by {jian.kang} on 8/24/22
 * <AUTHOR>
 */
data class TopicModel(
        @SerializedName("metadataValueType")
        var metadataValueType: Int,
        @SerializedName("tagName")
        var tagName: String,
        @SerializedName("tagId")
        var tagId: Int,
        @SerializedName("iting")
        var iting: String,
        @SerializedName("metadataValueList")
        var metadataValueList: List<MetaDataValueList>,
        @SerializedName("topicPlayCount")
        var topicPlayCount : String?,
        @SerializedName("topicTrackCount")
        var topicTrackCount : String?
) {
        companion object {
                const val TYPE_CONTENT = 1
                const val TYPE_TOPIC = 2
                const val TYPE_SUPER_TOPIC = 3
        }
}

data class MetaDataValueList(
        @SerializedName("metadataId")
        var metadataId: Int,
        @SerializedName("metadataValueId")
        var metadataValueId: Int
)
