package com.ximalaya.ting.android.host.model.album;

import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by WolfXu on 2018/5/29.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class TingReaderAlbumM extends AlbumM {

    private int lineCollectionCount;

    public TingReaderAlbumM() {

    }

    public TingReaderAlbumM(String json) {
        super(json);
        try {
            JSONObject jsonObject = new JSONObject(json);
            lineCollectionCount = jsonObject.optInt("lineCollectionCount");
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void parseAlbum(JSONObject album, Gson gson) throws JSONException {
        if (gson == null) {
            gson = getGson();
        }
        super.parseAlbum(album, gson);
    }

    public int getLineCollectionCount() {
        return lineCollectionCount;
    }

    public void setLineCollectionCount(int lineCollectionCount) {
        this.lineCollectionCount = lineCollectionCount;
    }
}
