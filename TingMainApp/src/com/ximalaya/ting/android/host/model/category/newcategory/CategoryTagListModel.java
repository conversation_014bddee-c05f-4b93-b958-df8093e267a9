package com.ximalaya.ting.android.host.model.category.newcategory;

import java.util.List;

public class CategoryTagListModel {
    private long id;

    private long refId;

    private String title;

    private String summary;

    private List<CategoryTagModel> subElements;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getRefId() {
        return refId;
    }

    public void setRefId(long refId) {
        this.refId = refId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public List<CategoryTagModel> getSubElements() {
        return subElements;
    }

    public void setSubElements(List<CategoryTagModel> subElements) {
        this.subElements = subElements;
    }
}
