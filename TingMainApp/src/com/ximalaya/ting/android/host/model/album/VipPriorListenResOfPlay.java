package com.ximalaya.ting.android.host.model.album;

import com.google.gson.annotations.SerializedName;

public class VipPriorListenResOfPlay {
    @SerializedName("top")
    public String usingPermission;
    @SerializedName("hint")
    public String noPermission;
    @SerializedName("download_hint")
    public VipPriorListenDownloadRes downloadRes;


    public static class VipPriorListenDownloadRes {
        @SerializedName("title")
        public String dialogTitle;
        @SerializedName("detail")
        public String dialogContent;
    }

    public static class VipPriorListenBtnRes {
        @SerializedName("text")
        public String text;
        @SerializedName("url")
        public String url;
    }
}




