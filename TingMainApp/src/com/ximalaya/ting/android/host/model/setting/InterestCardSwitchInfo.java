package com.ximalaya.ting.android.host.model.setting;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;

/**
 * Created by qinhuifeng on 2019/2/22.
 *
 * <AUTHOR>
 * <p>
 * 兴趣卡片相关开关
 */
public class InterestCardSwitchInfo implements Parcelable {
    // 兴趣卡片相关
    public boolean fullScreenStyle; // 兴趣卡片弹窗是否全屏

    // 礼包相关
    public boolean showUserGift;    // 是否展示新人礼包
    public String saveButtonIting;  // 保存按钮兴趣卡片链接，废弃
    public String saveButtonText;   // 保存按钮兴趣卡片文案,废弃,现在写死返回"保存"，防止客户端使用
    public String userGiftPic; //礼包图片地址
    public String userGiftLink; //礼包点击跳转地址
    @SerializedName("showLoginPage")
    public boolean mShowLoginPage;  // 是否先跳转到登录页面，展示礼包弹窗之后判断
    @SerializedName("deeplinkChannel")
    public boolean mDeeplinkChannel;    // deeplink等渠道进来的新用户，不展示礼包弹窗/兴趣卡片时使用

    public boolean playCardPage; // 在进入新的兴趣卡片页进行选择后是否跳转到推荐专辑卡片页
    public boolean halfScreenGenderAge; //点击跳过是否展示半屏性别年龄卡片（只有新版本需要判断）
    public int uiType; //0:全不出，1：只出性别年龄，2：只出兴趣卡片，3：全出
    public String userRedPic; //强引导登录弹窗的背景图
    public boolean showStrongBootLogin; //是否展示强引导登录
    public String clientAb; //客户端自行查的ab
    public String sceneType; //场景类型定：reCallUser-召回用户，newUser-新用户
    public String userType; //new-新用户
    public boolean skipShowPlayCard; //ab：兴趣卡片点击跳过是否跳转到播放卡片
    public String playCardPageStyle;    //ab播放卡片是否用新版 "new"是新版
    public PopInfo popInfo; // 弹窗信息

    public boolean submitJump; // 提交兴趣卡片后是否跳转，true｜是，false｜否
    public String submitJumpPage;  //提交兴趣卡片后的跳转链接

    public InterestCardSwitchInfo() {

    }

    protected InterestCardSwitchInfo(Parcel in) {
        fullScreenStyle = in.readByte() != 0;
        showUserGift = in.readByte() != 0;
        saveButtonIting = in.readString();
        saveButtonText = in.readString();
        userGiftPic = in.readString();
        userGiftLink = in.readString();
        mShowLoginPage = in.readByte() != 0;
        mDeeplinkChannel = in.readByte() != 0;
        playCardPage = in.readByte() != 0;
        halfScreenGenderAge = in.readByte() != 0;
        uiType = in.readInt();
        userRedPic = in.readString();
        showStrongBootLogin = in.readByte() != 0;
        clientAb = in.readString();
        sceneType = in.readString();
        skipShowPlayCard = in.readByte() != 0;
        playCardPageStyle = in.readString();
        popInfo = in.readParcelable(PopInfo.class.getClassLoader());
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeByte((byte) (fullScreenStyle ? 1 : 0));
        dest.writeByte((byte) (showUserGift ? 1 : 0));
        dest.writeString(saveButtonIting);
        dest.writeString(saveButtonText);
        dest.writeString(userGiftPic);
        dest.writeString(userGiftLink);
        dest.writeByte((byte) (mShowLoginPage ? 1 : 0));
        dest.writeByte((byte) (mDeeplinkChannel ? 1 : 0));
        dest.writeByte((byte) (playCardPage ? 1 : 0));
        dest.writeByte((byte) (halfScreenGenderAge ? 1 : 0));
        dest.writeInt(uiType);
        dest.writeString(userRedPic);
        dest.writeByte((byte) (showStrongBootLogin ? 1 : 0));
        dest.writeString(clientAb);
        dest.writeString(sceneType);
        dest.writeByte((byte) (skipShowPlayCard ? 1 : 0));
        dest.writeString(playCardPageStyle);
        dest.writeParcelable(popInfo, flags);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<InterestCardSwitchInfo> CREATOR = new Creator<InterestCardSwitchInfo>() {
        @Override
        public InterestCardSwitchInfo createFromParcel(Parcel in) {
            return new InterestCardSwitchInfo(in);
        }

        @Override
        public InterestCardSwitchInfo[] newArray(int size) {
            return new InterestCardSwitchInfo[size];
        }
    };

    public static class PopInfo implements Parcelable {
        @Nullable public String coverPath;
        public boolean needLogin;
        @Nullable public String giftActName;
        @Nullable public String id;
        @Nullable public String link;
        @Nullable public String sceneType;
        @Nullable public PopInfoRenderData renderData;

        public transient long countDownEndTime;

        protected PopInfo(Parcel in) {
            coverPath = in.readString();
            needLogin = in.readByte() != 0;
            giftActName = in.readString();
            id = in.readString();
            link = in.readString();
            sceneType = in.readString();
            renderData = in.readParcelable(PopInfoRenderData.class.getClassLoader());
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(coverPath);
            dest.writeByte((byte) (needLogin ? 1 : 0));
            dest.writeString(giftActName);
            dest.writeString(id);
            dest.writeString(link);
            dest.writeString(sceneType);
            dest.writeParcelable(renderData, flags);
        }

        @Override
        public int describeContents() {
            return 0;
        }

        public static final Creator<PopInfo> CREATOR = new Creator<PopInfo>() {
            @Override
            public PopInfo createFromParcel(Parcel in) {
                return new PopInfo(in);
            }

            @Override
            public PopInfo[] newArray(int size) {
                return new PopInfo[size];
            }
        };

        public boolean isValid() {
            return !TextUtils.isEmpty(coverPath);
        }
    }

    public static class PopInfoRenderData implements Parcelable {
        public long needSec; // 倒计时，秒
        @Nullable public String timeOverCoverPath;

        protected PopInfoRenderData(Parcel in) {
            needSec = in.readLong();
            timeOverCoverPath = in.readString();
        }

        public static final Creator<PopInfoRenderData> CREATOR = new Creator<PopInfoRenderData>() {
            @Override
            public PopInfoRenderData createFromParcel(Parcel in) {
                return new PopInfoRenderData(in);
            }

            @Override
            public PopInfoRenderData[] newArray(int size) {
                return new PopInfoRenderData[size];
            }
        };

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeLong(needSec);
            dest.writeString(timeOverCoverPath);
        }
    }
}
