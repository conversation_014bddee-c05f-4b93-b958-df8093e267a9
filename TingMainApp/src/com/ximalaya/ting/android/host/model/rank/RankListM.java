package com.ximalaya.ting.android.host.model.rank;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

 /**
  * <AUTHOR>
  */
public class RankListM {
    private int ret;
    private int count;
    private String title;
    private List<RankM> list;

    public RankListM(String json) {
        try {
            JSONObject job = new JSONObject(json);
            ret = job.optInt("ret", -1);
            count = job.optInt("count", 0);
            title = job.optString("title");
            JSONArray jsonArray = job.optJSONArray("list");
            if (jsonArray != null) {
                list = new ArrayList<>();
                for (int i = 0; i < jsonArray.length(); i++) {
                    list.add(new RankM(jsonArray.getString(i)));
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<RankM> getList() {
        return list;
    }

    public void setList(List<RankM> list) {
        this.list = list;
    }

}
