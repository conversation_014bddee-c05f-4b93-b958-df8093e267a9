package com.ximalaya.ting.android.host.model.album;


import android.text.TextUtils;

import org.json.JSONObject;


public class AlbumFansMedalModel {
    public String id;   // 有值：表明开启了粉丝勋章。不返回或者为空：无
    public String url;
    /**
     * 主播是否开启了勋章
     * */
    public boolean hasOpenMedal() {
        return !TextUtils.isEmpty(id);
    }

    public static AlbumFansMedalModel parse(JSONObject content) {
        if (null == content) {
            return null;
        }
        AlbumFansMedalModel result = new AlbumFansMedalModel();
        try {
            if (content.has("id")) {
                result.id = content.optString("id");
            }
            if (content.has("url")) {
                result.url = content.optString("url");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}