package com.ximalaya.ting.android.host.model.rank;

import com.google.gson.Gson;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.model.recommend.FocusImageList;
import com.ximalaya.ting.android.host.model.share.SimpleShareData;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

 /**
  * <AUTHOR>
  */
public class BaseListRankModel<T> extends ListModeBase<T> {
    private String subtitle;
    private String coverPath;
    private String shareUrl;
    private FocusImageList focusImageList;
    private List<RankCategoryKeys> categories;
    private SimpleShareData shareData;

    public BaseListRankModel(String json, Class<T> classType, String listTypem) throws JSONException {
        super(json, classType, listTypem, true);

        JSONObject jsonObject = new JSONObject(json);
        setTitle(jsonObject.optString("title"));
        subtitle = jsonObject.optString("subtitle");
        coverPath = jsonObject.optString("coverPath");
        if (jsonObject.has("shareContent"))
            try {
                shareData = new Gson().fromJson(jsonObject.optString("shareContent"), SimpleShareData.class);
            } catch (Exception e) {
                e.printStackTrace();
            }

        if (jsonObject.has("images"))
            focusImageList = new FocusImageList(jsonObject.optString("images"));
        if (jsonObject.has("categories")) {
            try {
                JSONArray jsonArray = jsonObject.optJSONArray("categories");
                if (jsonArray != null && jsonArray.length() > 0) {
                    categories = new ArrayList<>();
                    for (int i = 0; i < jsonArray.length(); i++) {
                        categories.add(new RankCategoryKeys(jsonArray.optString(i)));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getCoverPath() {
        return coverPath;
    }

    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }

    public FocusImageList getFocusImageList() {
        return focusImageList;
    }

    public void setFocusImageList(FocusImageList focusImageList) {
        this.focusImageList = focusImageList;
    }

    public List<RankCategoryKeys> getCategories() {
        return categories;
    }

    public void setCategories(List<RankCategoryKeys> categories) {
        this.categories = categories;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    public SimpleShareData getShareData() {
        return shareData;
    }

    public void setShareData(SimpleShareData shareData) {
        this.shareData = shareData;
    }
}
