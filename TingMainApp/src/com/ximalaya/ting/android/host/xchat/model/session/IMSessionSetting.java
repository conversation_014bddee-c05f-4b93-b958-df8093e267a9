package com.ximalaya.ting.android.host.xchat.model.session;

/**
 * IM会话的显示设置信息
 *
 * <AUTHOR>
 * @desc 文件描述
 * @email <EMAIL>
 * @wiki 说明文档的链接地址
 * @server 服务端开发人员放在这里
 * @since 2019-12-02 18:09
 */
public class IMSessionSetting {

    //私信会话的用户设置信息
    public int isMyFollow = -1;//是否是自己关注的人
    public int isMyFan = -1;//是否是关注自己的粉丝

    public int isOfficial = -1;//是否官方账户

    public int isNoReadNum = -1;//是否设置为免打扰
    public int isInBlacklist = -1;//是否加入黑名单

    public int anchorGrade = -1;//主播等级
    public int isVerified = -1;//是否通过认证
    public int verifyType = -1;//认证类型 1 个人认证，2 企业认证

    public int vlogoType = -1;//加V等级 -1无微标，1橙v，2蓝v，3红金v，4蓝金v


}
