package com.ximalaya.ting.android.host.manager.ad;

import com.ximalaya.ting.android.adsdk.base.util.AdUtil;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.AdSDKAdapterModel;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.AdSDKAdapterWebVideoModel;
import com.ximalaya.ting.android.adsdk.external.templatead.TemplateUIModel;
import com.ximalaya.ting.android.adsdk.model.AdModel;
import com.ximalaya.ting.android.adsdk.model.submodel.AdAppPermission;
import com.ximalaya.ting.android.adsdk.model.submodel.AdBusinessExtraInfo;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.manager.ad.util.ADABTestUtil;
import com.ximalaya.ting.android.host.manager.ad.videoad.TrackVideoAdCacheHelper;
import com.ximalaya.ting.android.host.model.ad.AnchorAlbumAd;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.model.advertis.AdShareDataForOpenSDK;
import com.ximalaya.ting.android.opensdk.model.advertis.AdWebVideoModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AppPermission;
import com.ximalaya.ting.android.opensdk.model.advertis.BrandData;
import com.ximalaya.ting.android.opensdk.model.advertis.BusinessExtraInfo;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by le.xin on 2021/11/10.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class AdConversionUtil {
    public static final AdSDKAdapterModel conversionModel(Advertis adModel, String positionName) {
        if (adModel == null) {
            return null;
        }

        JSONObject shareDataJsonObject = null;
        try {
            AdShareDataForOpenSDK shareData = adModel.getShareData();
            if(shareData != null) {
                shareDataJsonObject = new JSONObject();
                shareDataJsonObject.put("linkUrl", shareData.getLinkUrl());
                shareDataJsonObject.put("linkTitle", shareData.getLinkTitle());
                shareDataJsonObject.put("linkCoverPath", shareData.getLinkCoverPath());
                shareDataJsonObject.put("linkContent", shareData.getLinkContent());
                shareDataJsonObject.put("isExternalUrl", shareData.isExternalUrl());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        AdSDKAdapterWebVideoModel adapterWebVideoModel = null;
        AdWebVideoModel webVideoModel = adModel.getWebVideoModel();
        if (AdManager.canGotoAdHybridFragment(adModel) && webVideoModel != null) {
            adapterWebVideoModel = new AdSDKAdapterWebVideoModel();
            adapterWebVideoModel.setLastVideoPlayPosition(webVideoModel.getLastVideoPlayPosition());
            adapterWebVideoModel.setPlayMute(webVideoModel.isPlayMute());
            adapterWebVideoModel.setWebVideoUrl(webVideoModel.getWebVideoUrl());
            adapterWebVideoModel.setTitle(webVideoModel.getTitle());
            adapterWebVideoModel.setImageUrl(webVideoModel.getImageUrl());
            adapterWebVideoModel.setDesc(webVideoModel.getDesc());
            adapterWebVideoModel.setButtonText(webVideoModel.getButtonText());
        }
        AdSDKAdapterModel.BusinessExtraSDKInfo businessExtraSDKInfo = null;
        BusinessExtraInfo businessExtraInfo = adModel.getBusinessExtraInfo();
        if (businessExtraInfo != null) {
            businessExtraSDKInfo = new AdSDKAdapterModel.BusinessExtraSDKInfo();
            businessExtraSDKInfo.setPopReminderText(businessExtraInfo.getPopReminderText());
            businessExtraSDKInfo.setPopReminderStyle(businessExtraInfo.getPopReminderStyle());
            businessExtraSDKInfo.setJumpTrackId(businessExtraInfo.getJumpTrackId());
            businessExtraSDKInfo.setAutoJumpTime(businessExtraInfo.getAutoJumpTime());
            businessExtraSDKInfo.setSoundAggType(businessExtraInfo.getSoundAggType());
            businessExtraSDKInfo.setBrandAppManageData(businessExtraInfo.getBrandAppManageData());
            businessExtraSDKInfo.setDownloadPopupStyle(businessExtraInfo.getDownloadPopupStyle());
            businessExtraSDKInfo.setDpAutoJumpSecond(businessExtraInfo.getDpAutoJumpSecond());
            businessExtraSDKInfo.setDpAutoJumpShowSecond(businessExtraInfo.getDpAutoJumpShowSecond());
            businessExtraSDKInfo.setScrollClickReport(businessExtraInfo.isScrollClickReport());
            businessExtraSDKInfo.setEnableShowAppInfo(businessExtraInfo.isEnableShowAppInfo());
            businessExtraSDKInfo.setAppPermissionUrl(businessExtraInfo.getAppPermissionUrl());
            businessExtraSDKInfo.setAutoPopUpSecond(businessExtraInfo.getAutoPopUpSecond());
            businessExtraSDKInfo.setCommonReportMap(businessExtraInfo.getCommonReportMap());
            businessExtraSDKInfo.setAppIntroductionUrl(businessExtraInfo.getAppIntroductionUrl());
            businessExtraSDKInfo.setAppIntroductionText(businessExtraInfo.getAppIntroductionText());
            businessExtraSDKInfo.setUbtReportMap(businessExtraInfo.getUbtReportMap());
            businessExtraSDKInfo.setClickPostMap(businessExtraInfo.getClickPostMap());
            businessExtraSDKInfo.setClickRequestMethod(businessExtraInfo.getClickRequestMethod());

        }

        List<AdSDKAdapterModel.AdSDKAppPermission> adSDKAppPermissions = null;
        List<AppPermission> appPermissions = adModel.getAppPermissions();
        if (!ToolUtil.isEmptyCollects(appPermissions)) {
            adSDKAppPermissions = new ArrayList<>(appPermissions.size());
            for (AppPermission permission : appPermissions) {
                AdSDKAdapterModel.AdSDKAppPermission sdkAppPermission =
                        new AdSDKAdapterModel.AdSDKAppPermission();
                sdkAppPermission.setPermissionDesc(permission.getPermissionDesc());
                sdkAppPermission.setPermissionName(permission.getPermissionName());
                adSDKAppPermissions.add(sdkAppPermission);
            }
        }

        long promoteTrackId = 0;
        if (adModel instanceof AnchorAlbumAd) {
            promoteTrackId = ((AnchorAlbumAd) adModel).getPromoteTrackId();
        }

        String positionIdNoDefault =
                AdPositionIdManager.getPositionIdByPositionNameNoDefault(positionName);
        AdSDKAdapterModel adapterModel = new AdSDKAdapterModel(
                adModel.getResponseId(),
                adModel.getAdid(),
                adModel.getAdPositionId(),
                adModel.getAdtype(),
                adModel.getCommonReportMap(),
                adModel.getClickUrls(),
                adModel.getThirdShowStatUrls(),
                adModel.getThirdClickStatUrls(),
                adModel.getRealLink(),
                adModel.getLinkUrl(),
                adModel.getDownloadMonitorMoment(),
                adModel.getDpRealLink(),
                adModel.getLinkType(),
                adModel.getClickType(),
                adModel.getDownloadAppLogo(),
                adModel.getDownloadAppName(),
                adModel.getDownloadAppDesc(),
                adModel.getDownloadProgressBarClickType(),
                adModel.getClientIp(),
                adModel.getShowTokens(),
                adModel.getClickTokens(),
                adModel.isShowTokenEnable(),
                adModel.isClickTokenEnable(),
                adModel.getCloseStyle(),
                adModel.getColumnSequence(),
                adModel.getHomeRank(),
                adModel.getDisplayAnimation(),
                adModel.getAnimationType(),
                adModel.getOpenlinkType(),
                adModel.getIsInternal(),
                adModel.isShareFlag(),
                shareDataJsonObject,
                adModel.getAppPackageName(),
                positionName,
                adModel.getName(),
                adModel.getThirdDpArouseUrl(),
                adModel.getThirdDpArouseFailUrl(),
                adModel.isEnableDownloadPopUp(),
                adModel.getDownloadPopupStyle(),
                adModel.getDownloadPopUpClickArea(),
                adModel.getProviderName(),
                adModel.isLandScape(),
                adapterWebVideoModel,
                adModel.getAppVersion(),
                adModel.getAppSize(),
                adModel.getAppDeveloper(),
                adModel.getAppPrivacyPolicy(),
                adSDKAppPermissions,
                TextUtils.isEmpty(adModel.getAdPositionId()) ? positionIdNoDefault : adModel.getAdPositionId(),
                adModel.getWxMiniProgramId(),
                adModel.getLandingPageResId(),
                adModel.getStartAt(),
                adModel.getEndAt(),
                adModel.isPreviewAd(),
                adModel.isTrueExposure(),
                adModel.getShowUrls(),
                adModel.getThirdStatUrl(),
                /*adModel.getSlotShowReportExt()*/ null,
                /*adModel.getUserShowReportExt()*/ null,
                adModel.getAdpr(),
                adModel.getRecSrc(),
                adModel.getRecTrack(),
                adModel.getBucketIds(),
                adModel.getAdBucketIds(),
                adModel.getDspPositionId(),
                adModel.getStrongType(),
                adModel.getLogoUrl(),
                adModel.getClickTitle(),
                adModel.getButtonText(),
                adModel.isEnableShowProcessButton(),
                adModel.isClickReportFlag(),
                promoteTrackId,
                adModel.getClickJumpType(),
                adModel.getDpRetrySecond(),
                adModel.getShowstyle()
        );
        adapterModel.setJumpTrackId(adModel.getJumpTrackId());
        adapterModel.setAutoJumpTime(adModel.getAutoJumpTime());
        adapterModel.setBusinessExtraInfo(businessExtraSDKInfo);
        adapterModel.setOpenBounce(adModel.getOpenBounce());
        adapterModel.setBounceDelay(adModel.getBounceDelay());
        adapterModel.setGestureCode(adModel.getGestureCode());
        adapterModel.setEnableContinuePlay(adModel.isEnableContinuePlay());
        adapterModel.setAdScene(adModel.getAdScene());
        adapterModel.setCloseButtonText1(adModel.getCloseButtonText1());
        adapterModel.setCloseButtonText2(adModel.getCloseButtonText2());
        adapterModel.setWaitCloseExpire(adModel.getWaitCloseExpire());
        adapterModel.setNoAdButtonStyle(adModel.getNoAdButtonStyle());
        adapterModel.setNoAdText(adModel.getNoAdText());
        if (adapterModel != null &&
                (AppConstants.AD_POSITION_NAME_SOUND_PATCH.equals(adapterModel.getPositionName())
                        || IAdConstants.IAdPositionId.TRACK_SOUND_PATCH.equals(adapterModel.getAdPositionId()))) {
            adapterModel.setScatterMutexPlayControl(ADABTestUtil.isScatterMutexPlayControlAbTest());
        }
        adapterModel.setSoundType(adModel.getSoundType());
        adapterModel.setVideoDurationTime(adModel.getVideoDurationTime());
        adapterModel.setBackupCover(adModel.getBackupCover());
        String lastListenAdId = TrackVideoAdCacheHelper.INSTANCE.getListenAdIdFromRecord();
        if (!TextUtils.isEmpty(lastListenAdId)) {
            adapterModel.setLastListenAdId(lastListenAdId);
        }
        adapterModel.setDurationPicStyle(adModel.getDurationPicStyle());
        adapterModel.setHideInfoForWebUa(adModel.isHideInfoForWebUa());
        adapterModel.setMobileRtb(adModel.isMobileRtb());
        adapterModel.setCurrentDspRtbPrice(adModel.getCurrentDspRtbPrice());
        adapterModel.setJumpOpti(adModel.getJumpOpti());
        adapterModel.setDriveMode(adModel.getDriveMode());
        adapterModel.setAutoShowWebTime(adModel.getAutoShowWebTime());
        adapterModel.setUbtReportMap(adModel.getUbtReportMap());
        adapterModel.setClickPostMap(adModel.getClickPostMap());
        adapterModel.setClickRequestMethod(adModel.getClickRequestMethod());
        adapterModel.setOneClickLeaveCash(adModel.isOneClickLeaveCash());
        adapterModel.setDpRecordEnable(adModel.isDpRecordEnable());
        adapterModel.setNeedDedupClick(adModel.getNeedDedupClick());
        adapterModel.setLastClickTime(adModel.getLastClickTime());
        adapterModel.setPreLoadH5Status(adModel.getPreLoadH5Status());
        adapterModel.setAutoCloseWhenInstall(adModel.isAutoCloseWhenInstall());
        adapterModel.setCover(adModel.getImageUrl());
        if (!TextUtils.isEmpty(adModel.getVideoCover())) {
            adapterModel.setVideoCover(adModel.getVideoCover());
        } else {
            // 贴片的视频地址为dynamicCover
            adapterModel.setVideoCover(adModel.getDynamicImage());
        }
        adapterModel.setLandVideoUrl(adModel.getLandVideoUrl());
        adapterModel.setNeedToastDownloading(adModel.isNeedToastDownloading());
        adapterModel.setDescription(adModel.getDescription());
        adapterModel.setProviderAvatar(adModel.getProviderAvatar());
        adapterModel.setNeedDownloadProgressBar(adModel.isNeedDownloadProgressBar());
        adapterModel.setUnlockType(adModel.getUnlockType());
        adapterModel.setAdUserType(adModel.getAdUserType());
        return adapterModel;
    }

    /**
     * 将 sdkmodel 转成 advertis 使用
     * @param adapterModel
     * @return
     */
    public static final Advertis translateSDKModelToAdvertis(AdSDKAdapterModel adapterModel) {

        if (adapterModel == null) {
            return null;
        }
        List<AppPermission> adPermissions = null;
        List<AdSDKAdapterModel.AdSDKAppPermission> appPermissions = adapterModel.getAppPermissions();
        if (!ToolUtil.isEmptyCollects(appPermissions)) {
            adPermissions = new ArrayList<>(appPermissions.size());
            for (AdSDKAdapterModel.AdSDKAppPermission permission : appPermissions) {
                AppPermission adPer = new AppPermission();
                adPer.setPermissionDesc(permission.getPermissionDesc());
                adPer.setPermissionName(permission.getPermissionName());
                adPermissions.add(adPer);
            }
        }

        BusinessExtraInfo businessExtraInfo = null;
        AdSDKAdapterModel.BusinessExtraSDKInfo businessExtraSDKInfo = adapterModel.getBusinessExtraInfo();
        if (businessExtraSDKInfo != null) {
            businessExtraInfo = new BusinessExtraInfo();
            businessExtraInfo.setPopReminderText(businessExtraSDKInfo.getPopReminderText());
            businessExtraInfo.setPopReminderStyle(businessExtraSDKInfo.getPopReminderStyle());
            businessExtraInfo.setJumpTrackId(businessExtraSDKInfo.getJumpTrackId());
            businessExtraInfo.setAutoJumpTime(businessExtraSDKInfo.getAutoJumpTime());
            businessExtraInfo.setSoundAggType(businessExtraSDKInfo.getSoundAggType());
            businessExtraInfo.setBrandAppManageData(businessExtraSDKInfo.getBrandAppManageData());
            businessExtraInfo.setDownloadPopupStyle(businessExtraSDKInfo.getDownloadPopupStyle());
            businessExtraInfo.setDpAutoJumpSecond(businessExtraSDKInfo.getDpAutoJumpSecond());
            businessExtraInfo.setDpAutoJumpShowSecond(businessExtraSDKInfo.getDpAutoJumpShowSecond());
            businessExtraInfo.setScrollClickReport(businessExtraSDKInfo.isScrollClickReport());
            businessExtraInfo.setEnableShowAppInfo(businessExtraSDKInfo.isEnableShowAppInfo());
            businessExtraInfo.setAppPermissionUrl(businessExtraSDKInfo.getAppPermissionUrl());
            businessExtraInfo.setAppIntroductionText(businessExtraSDKInfo.getAppIntroductionText());
            businessExtraInfo.setAppIntroductionUrl(businessExtraSDKInfo.getAppIntroductionUrl());

            businessExtraInfo.setUbtReportMap(businessExtraSDKInfo.getUbtReportMap());
            businessExtraInfo.setClickPostMap(businessExtraSDKInfo.getClickPostMap());
            businessExtraInfo.setClickRequestMethod(businessExtraSDKInfo.getClickRequestMethod());
        }

        Advertis advertis = new Advertis();
        advertis.setAdid(adapterModel.getAdid());
        advertis.setResponseId(adapterModel.getResponseId());
        advertis.setPositionName(adapterModel.getPositionName());
        try {
            advertis.setPositionId(Integer.parseInt(adapterModel.getAdPositionId()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        advertis.setAdtype(adapterModel.getAdtype());
        advertis.setCommonReportMap(adapterModel.getCommonReportMap());
        advertis.setClickUrls(adapterModel.getClickUrls());
        advertis.setThirdShowStatUrls(adapterModel.getThirdShowStatUrls());
        advertis.setThirdClickStatUrls(adapterModel.getThirdClickStatUrls());
        advertis.setLinkType(adapterModel.getLinkType()); // link类型
        advertis.setLinkUrl(adapterModel.getLinkUrl());
        advertis.setDpRealLink(adapterModel.getDpRealLink()); // dp 链接
        advertis.setClickType(adapterModel.getClickType()); // 点击类型
        advertis.setRealLink(adapterModel.getRealLink()); // 链接
        advertis.setDpRetrySecond(adapterModel.getDpRetrySecond());
        advertis.setName(adapterModel.getName()); // 广告标题
        advertis.setJumpTrackId(adapterModel.getJumpTrackId()); // 内容推广落地页，声音ID
        advertis.setAutoJumpTime(adapterModel.getAutoJumpTime()); // 内容推广落地页， 自动跳转时长
        advertis.setBusinessExtraInfo(businessExtraInfo); // 透传字段
        advertis.setClientIp(adapterModel.getClientIp());
        advertis.setShowTokens(adapterModel.getShowTokens());
        advertis.setShowTokenEnable(adapterModel.isShowTokenEnable());
        advertis.setClickTokens(adapterModel.getClickTokens());
        advertis.setClickTokenEnable(adapterModel.isClickTokenEnable());
        advertis.setCloseStyle(adapterModel.getCloseStyle());
        advertis.setColumnSequence(adapterModel.getColumnSequence());
        advertis.setHomeRank(adapterModel.getHomeRank());
        advertis.setDisplayAnimation(adapterModel.getDisplayAnimation());
        advertis.setAnimationType(adapterModel.getAnimationType());
        advertis.setOpenlinkType(adapterModel.getOpenlinkType());
        advertis.setIsInternal(adapterModel.getIsInternal());
        advertis.setName(adapterModel.getName());
        advertis.setThirdDpArouseUrl(adapterModel.getThirdDpArouseUrl());
        advertis.setThirdDpArouseFailUrl(adapterModel.getThirdDpArouseFailUrl());
        advertis.setProviderName(adapterModel.getProviderName());
        advertis.setLandScape(adapterModel.isLandScape());
        advertis.setDspPositionId(adapterModel.getDspPositionId());
        advertis.setWxMiniProgramId(adapterModel.getWxMiniProgramId());
        advertis.setLandingPageResId(adapterModel.getLandingPageResId());
        advertis.setStartAt(adapterModel.getStartAt());
        advertis.setEndAt(adapterModel.getEndAt());
        advertis.setPreviewAd(adapterModel.isPreviewAd());
        advertis.setTrueExposure(adapterModel.isTrueExposure());
        advertis.setShowUrls(adapterModel.getShowUrls());
        advertis.setThirdStatUrl(adapterModel.getThirdStatUrl());
        advertis.setAdpr(adapterModel.getAdpr());
        advertis.setRecSrc(adapterModel.getRecSrc());
        advertis.setRecTrack(adapterModel.getRecTrack());
        advertis.setBucketIds(adapterModel.getBucketIds());
        advertis.setStrongType(adapterModel.getStrongType());
        advertis.setLogoUrl(adapterModel.getLogo());
        advertis.setClickTitle(adapterModel.getClickTitle());
        advertis.setButtonText(adapterModel.getButtonText());
        advertis.setEnableShowProcessButton(adapterModel.isEnableShowProcessButton());
        advertis.setClickReportFlag(adapterModel.isClickReportFlag());
        advertis.setClickJumpType(adapterModel.getClickJumpType());
        advertis.setShowstyle(adapterModel.getShowStyle());
        // 下载业务的，
        advertis.setDownloadAppLogo(adapterModel.getDownloadAppLogo()); // 下载logo
        advertis.setDownloadAppName(adapterModel.getDownloadAppName()); // 下载app name
        advertis.setDownloadPopupStyle(adapterModel.getDownloadPopupStyle()); // 下载弹窗样式类型
        advertis.setDownloadAppDesc(adapterModel.getDownloadAppDesc());  // 应用描述
        advertis.setDownloadProgressBarClickType(adapterModel.getDownloadProgressBarClickType()); // 通知栏下载器点击行为配置：1）触发暂停/继续；2）前往下载中心（不可暂停/继续，没有暂停按钮），默认是2
        advertis.setEnableDownloadPopUp(adapterModel.isEnableDownloadPopUp()); // 下载物料是否出现弹窗（false无，true有）
        advertis.setAppPackageName(adapterModel.getAppPackageName()); // apk包名
        advertis.setDownloadPopUpClickArea(adapterModel.getDownloadPopUpClickArea()); // 下载弹窗可点击位置配置（0：下载按钮，1：按钮+logo+app name+推广应用描述，2：非关闭按钮）
        advertis.setAppVersion(adapterModel.getAppVersion()); // 合规弹窗样式4： app信息- 版本号
        advertis.setAppSize(adapterModel.getAppSize()); // 合规弹窗样式4： app信息- apk大小
        advertis.setAppDeveloper(adapterModel.getAppDeveloper()); // 合规弹窗样式4： app信息- 开发者
        advertis.setAppPrivacyPolicy(adapterModel.getAppPrivacyPolicy()); // 合规弹窗样式4： app信息- 隐私协议链接
        advertis.setAppPermissions(adPermissions); // 合规弹窗样式4： app信息- 权限列表
        advertis.setOpenBounce(adapterModel.getOpenBounce());
        advertis.setBounceDelay(adapterModel.getBounceDelay());
        advertis.setEnableContinuePlay(adapterModel.isEnableContinuePlay());
        advertis.setAdScene(adapterModel.getAdScene());
        advertis.setCloseButtonText1(adapterModel.getCloseButtonText1());
        advertis.setCloseButtonText2(adapterModel.getCloseButtonText2());
        advertis.setWaitCloseExpire(adapterModel.getWaitCloseExpire());
        advertis.setNoAdButtonStyle(adapterModel.getNoAdButtonStyle());
        advertis.setNoAdText(adapterModel.getNoAdText());
        advertis.setSoundType(adapterModel.getSoundType());
        advertis.setVideoDurationTime(adapterModel.getVideoDurationTime());
        advertis.setBackupCover(adapterModel.getBackupCover());
        advertis.setDurationPicStyle(adapterModel.getDurationPicStyle());
        advertis.setHideInfoForWebUa(adapterModel.isHideInfoForWebUa());
        advertis.setDriveMode(adapterModel.getDriveMode());
        advertis.setAutoShowWebTime(adapterModel.getAutoShowWebTime());
        advertis.setUbtReportMap(adapterModel.getUbtReportMap());
        advertis.setClickPostMap(adapterModel.getClickPostMap());
        advertis.setClickRequestMethod(adapterModel.getClickRequestMethod());
        advertis.setOneClickLeaveCash(adapterModel.isOneClickLeaveCash());
        advertis.setNeedDedupClick(adapterModel.getNeedDedupClick());
        advertis.setLastClickTime(adapterModel.getLastClickTime());
        advertis.setLandVideoUrl(adapterModel.getLandVideoUrl());
        advertis.setProviderAvatar(adapterModel.getProviderAvatar());
        advertis.setDescription(adapterModel.getDescription());

        AdWebVideoModel adWebVideoModel = null;
        AdSDKAdapterWebVideoModel adapterWebVideoModel = adapterModel.getWebVideoModel();
        if ( adapterWebVideoModel != null) {
            adWebVideoModel = new AdWebVideoModel();
            adWebVideoModel.setLastVideoPlayPosition(adapterWebVideoModel.getLastVideoPlayPosition());
            adWebVideoModel.setPlayMute(adapterWebVideoModel.isPlayMute());
            adWebVideoModel.setWebVideoUrl(adapterWebVideoModel.getWebVideoUrl());
            adWebVideoModel.setTitle(adapterWebVideoModel.getTitle());
            adWebVideoModel.setImageUrl(adapterWebVideoModel.getImageUrl());
            adWebVideoModel.setDesc(adapterWebVideoModel.getDesc());
            adWebVideoModel.setButtonText(adapterWebVideoModel.getButtonText());
        }
        advertis.setWebVideoModel(adWebVideoModel);
        advertis.setNeedToastDownloading(adapterModel.isNeedToastDownloading());
        advertis.setNeedDownloadProgressBar(adapterModel.isNeedDownloadProgressBar());
        advertis.setUnlockType(adapterModel.getUnlockType());
        return advertis;
    }



    /**
     * 将 adModel 转成 advertis 使用
     * @param adapterModel
     * @return
     */
    public static final Advertis translateSDKAdModelToAdvertis(AdSDKAdapterModel adapterModel) {
        if (adapterModel == null) {
            return null;
        }

        List<AppPermission> adPermissions = null;
        List<AdSDKAdapterModel.AdSDKAppPermission> appPermissions = adapterModel.getAppPermissions();
        if (!ToolUtil.isEmptyCollects(appPermissions)) {
            adPermissions = new ArrayList<>(appPermissions.size());
            for (AdSDKAdapterModel.AdSDKAppPermission permission : appPermissions) {
                AppPermission adPer = new AppPermission();
                adPer.setPermissionDesc(permission.getPermissionDesc());
                adPer.setPermissionName(permission.getPermissionName());
                adPermissions.add(adPer);
            }
        }

        BusinessExtraInfo businessExtraInfo = null;
        AdSDKAdapterModel.BusinessExtraSDKInfo sdkBusinessExtraInfo = adapterModel.getBusinessExtraInfo();
        if (sdkBusinessExtraInfo != null) {
            businessExtraInfo = new BusinessExtraInfo();
            businessExtraInfo.setPopReminderText(sdkBusinessExtraInfo.getPopReminderText());
            businessExtraInfo.setPopReminderStyle(sdkBusinessExtraInfo.getPopReminderStyle());
            businessExtraInfo.setJumpTrackId(sdkBusinessExtraInfo.getJumpTrackId());
            businessExtraInfo.setAutoJumpTime(sdkBusinessExtraInfo.getAutoJumpTime());
            businessExtraInfo.setSoundAggType(sdkBusinessExtraInfo.getSoundAggType());
            businessExtraInfo.setBrandAppManageData(sdkBusinessExtraInfo.getBrandAppManageData());
            businessExtraInfo.setDownloadPopupStyle(sdkBusinessExtraInfo.getDownloadPopupStyle());
            businessExtraInfo.setAutoScroll(sdkBusinessExtraInfo.isAutoScroll());
            businessExtraInfo.setDpAutoJumpSecond(sdkBusinessExtraInfo.getDpAutoJumpSecond());
            businessExtraInfo.setDpAutoJumpShowSecond(sdkBusinessExtraInfo.getDpAutoJumpShowSecond());
            businessExtraInfo.setScrollClickReport(sdkBusinessExtraInfo.isScrollClickReport());
            businessExtraInfo.setEnableShowAppInfo(sdkBusinessExtraInfo.isEnableShowAppInfo());
            businessExtraInfo.setAppPermissionUrl(sdkBusinessExtraInfo.getAppPermissionUrl());
            businessExtraInfo.setAppIntroductionText(sdkBusinessExtraInfo.getAppIntroductionText());
            businessExtraInfo.setAppIntroductionUrl(sdkBusinessExtraInfo.getAppIntroductionUrl());

            businessExtraInfo.setUbtReportMap(sdkBusinessExtraInfo.getUbtReportMap());
            businessExtraInfo.setClickPostMap(sdkBusinessExtraInfo.getClickPostMap());
            businessExtraInfo.setClickRequestMethod(sdkBusinessExtraInfo.getClickRequestMethod());
        }

        Advertis advertis = new Advertis();
        advertis.setAdid(adapterModel.getAdid());
        advertis.setResponseId(adapterModel.getResponseId());
        advertis.setPositionName(adapterModel.getPositionName());
        try {
            advertis.setPositionId(Integer.parseInt(adapterModel.getAdPositionId()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        advertis.setAdtype(adapterModel.getAdtype());
        advertis.setCommonReportMap(adapterModel.getCommonReportMap());
        advertis.setClickUrls(adapterModel.getClickUrls());
        advertis.setThirdShowStatUrls(adapterModel.getThirdShowStatUrls());
        advertis.setThirdClickStatUrls(adapterModel.getThirdClickStatUrls());
        advertis.setLinkType(adapterModel.getLinkType()); // link类型
        advertis.setLinkUrl(adapterModel.getLinkUrl());
        advertis.setDpRealLink(adapterModel.getDpRealLink()); // dp 链接
        advertis.setClickType(adapterModel.getClickType()); // 点击类型
        advertis.setRealLink(adapterModel.getRealLink()); // 链接
        advertis.setDpRetrySecond(adapterModel.getDpRetrySecond());
        advertis.setName(adapterModel.getName()); // 广告标题
        advertis.setJumpTrackId(adapterModel.getJumpTrackId()); // 内容推广落地页，声音ID
        advertis.setAutoJumpTime(adapterModel.getAutoJumpTime()); // 内容推广落地页， 自动跳转时长
        advertis.setBusinessExtraInfo(businessExtraInfo); // 透传字段
        advertis.setClientIp(adapterModel.getClientIp());
        advertis.setShowTokens(adapterModel.getShowTokens());
        advertis.setShowTokenEnable(adapterModel.isShowTokenEnable());
        advertis.setClickTokens(adapterModel.getClickTokens());
        advertis.setClickTokenEnable(adapterModel.isClickTokenEnable());
        advertis.setCloseStyle(adapterModel.getCloseStyle());
        advertis.setColumnSequence(adapterModel.getColumnSequence());
        advertis.setHomeRank(adapterModel.getHomeRank());
        advertis.setDisplayAnimation(adapterModel.getDisplayAnimation());
        advertis.setAnimationType(adapterModel.getAnimationType());
        advertis.setOpenlinkType(adapterModel.getOpenlinkType());
        advertis.setIsInternal(adapterModel.getIsInternal());
        advertis.setName(adapterModel.getName());
        advertis.setThirdDpArouseUrl(adapterModel.getThirdDpArouseUrl());
        advertis.setThirdDpArouseFailUrl(adapterModel.getThirdDpArouseFailUrl());
        advertis.setProviderName(adapterModel.getProviderName());
        advertis.setLandScape(adapterModel.isLandScape());
        advertis.setDspPositionId(adapterModel.getDspPositionId());
        advertis.setWxMiniProgramId(adapterModel.getWxMiniProgramId());
        advertis.setLandingPageResId(adapterModel.getLandingPageResId());
        advertis.setStartAt(adapterModel.getStartAt());
        advertis.setEndAt(adapterModel.getEndAt());
        advertis.setPreviewAd(adapterModel.isPreviewAd());
        advertis.setTrueExposure(adapterModel.isTrueExposure());
        advertis.setShowUrls(adapterModel.getShowUrls());
        advertis.setThirdStatUrl(adapterModel.getThirdStatUrl());
        advertis.setAdpr(adapterModel.getAdpr());
        advertis.setRecSrc(adapterModel.getRecSrc());
        advertis.setRecTrack(adapterModel.getRecTrack());
        advertis.setBucketIds(adapterModel.getBucketIds());
        advertis.setStrongType(adapterModel.getStrongType());
        advertis.setLogoUrl(adapterModel.getLogo());
        advertis.setClickTitle(adapterModel.getClickTitle());
        advertis.setButtonText(adapterModel.getButtonText());
        advertis.setEnableShowProcessButton(adapterModel.isEnableShowProcessButton());
        advertis.setClickReportFlag(adapterModel.isClickReportFlag());
        advertis.setClickJumpType(adapterModel.getClickJumpType());

        // 下载业务的，
        advertis.setDownloadAppLogo(adapterModel.getDownloadAppLogo()); // 下载logo
        advertis.setDownloadAppName(adapterModel.getDownloadAppName()); // 下载app name
        advertis.setDownloadPopupStyle(adapterModel.getDownloadPopupStyle()); // 下载弹窗样式类型
        advertis.setDownloadAppDesc(adapterModel.getDownloadAppDesc());  // 应用描述
        advertis.setDownloadProgressBarClickType(adapterModel.getDownloadProgressBarClickType()); // 通知栏下载器点击行为配置：1）触发暂停/继续；2）前往下载中心（不可暂停/继续，没有暂停按钮），默认是2
        advertis.setEnableDownloadPopUp(adapterModel.isEnableDownloadPopUp()); // 下载物料是否出现弹窗（false无，true有）
        advertis.setAppPackageName(adapterModel.getAppPackageName()); // apk包名
        advertis.setDownloadPopUpClickArea(adapterModel.getDownloadPopUpClickArea()); // 下载弹窗可点击位置配置（0：下载按钮，1：按钮+logo+app name+推广应用描述，2：非关闭按钮）
        advertis.setAppVersion(adapterModel.getAppVersion()); // 合规弹窗样式4： app信息- 版本号
        advertis.setAppSize(adapterModel.getAppSize()); // 合规弹窗样式4： app信息- apk大小
        advertis.setAppDeveloper(adapterModel.getAppDeveloper()); // 合规弹窗样式4： app信息- 开发者
        advertis.setAppPrivacyPolicy(adapterModel.getAppPrivacyPolicy()); // 合规弹窗样式4： app信息- 隐私协议链接
        advertis.setAppPermissions(adPermissions); // 合规弹窗样式4： app信息- 权限列表
        advertis.setOpenBounce(adapterModel.getOpenBounce());
        advertis.setBounceDelay(adapterModel.getBounceDelay());
        advertis.setEnableContinuePlay(adapterModel.isEnableContinuePlay());
        advertis.setCloseButtonText1(adapterModel.getCloseButtonText1());
        advertis.setCloseButtonText2(adapterModel.getCloseButtonText2());
        advertis.setWaitCloseExpire(adapterModel.getWaitCloseExpire());
        advertis.setNoAdButtonStyle(adapterModel.getNoAdButtonStyle());
        advertis.setNoAdText(adapterModel.getNoAdText());
        advertis.setSoundType(adapterModel.getSoundType());
        advertis.setVideoDurationTime(adapterModel.getVideoDurationTime());
        advertis.setBackupCover(adapterModel.getBackupCover());
        advertis.setDurationPicStyle(adapterModel.getDurationPicStyle());
        advertis.setHideInfoForWebUa(adapterModel.isHideInfoForWebUa());
        advertis.setDriveMode(adapterModel.getDriveMode());
        advertis.setUbtReportMap(adapterModel.getUbtReportMap());
        advertis.setClickPostMap(adapterModel.getClickPostMap());
        advertis.setClickRequestMethod(adapterModel.getClickRequestMethod());
        advertis.setNeedDedupClick(adapterModel.getNeedDedupClick());
        advertis.setLastClickTime(adapterModel.getLastClickTime());
        return advertis;
    }

    /**
     * 将 adModel 转成 advertis 使用
     * @param adapterModel
     * @return
     */
    public static final Advertis translateSDKAdModelToAdvertis(AdModel adapterModel) {
        if (adapterModel == null) {
            return null;
        }

        List<AppPermission> adPermissions = null;
        List<AdAppPermission> appPermissions = adapterModel.getAppPermissions();
        if (!ToolUtil.isEmptyCollects(appPermissions)) {
            adPermissions = new ArrayList<>(appPermissions.size());
            for (AdAppPermission permission : appPermissions) {
                AppPermission adPer = new AppPermission();
                adPer.setPermissionDesc(permission.getPermissionDesc());
                adPer.setPermissionName(permission.getPermissionName());
                adPermissions.add(adPer);
            }
        }

        BusinessExtraInfo businessExtraInfo = null;
        AdBusinessExtraInfo sdkBusinessExtraInfo = adapterModel.getBusinessExtraInfo();
        if (sdkBusinessExtraInfo != null) {
            businessExtraInfo = new BusinessExtraInfo();
            businessExtraInfo.setPopReminderText(sdkBusinessExtraInfo.getPopReminderText());
            businessExtraInfo.setPopReminderStyle(sdkBusinessExtraInfo.getPopReminderStyle());
            businessExtraInfo.setJumpTrackId(sdkBusinessExtraInfo.getJumpTrackId());
            businessExtraInfo.setAutoJumpTime(sdkBusinessExtraInfo.getAutoJumpTime());
            businessExtraInfo.setSoundAggType(sdkBusinessExtraInfo.getSoundAggType());
            businessExtraInfo.setBrandAppManageData(sdkBusinessExtraInfo.getBrandAppManageData());
            businessExtraInfo.setDownloadPopupStyle(sdkBusinessExtraInfo.getDownloadPopupStyle());
            businessExtraInfo.setAutoScroll(sdkBusinessExtraInfo.isAutoScroll());
            businessExtraInfo.setDpAutoJumpSecond(sdkBusinessExtraInfo.getDpAutoJumpSecond());
            businessExtraInfo.setDpAutoJumpShowSecond(sdkBusinessExtraInfo.getDpAutoJumpShowSecond());
            businessExtraInfo.setScrollClickReport(sdkBusinessExtraInfo.isScrollClickReport());
            businessExtraInfo.setEnableShowAppInfo(sdkBusinessExtraInfo.isEnableShowAppInfo());
            businessExtraInfo.setAppPermissionUrl(sdkBusinessExtraInfo.getAppPermissionUrl());
            businessExtraInfo.setAppIntroductionText(sdkBusinessExtraInfo.getAppIntroductionText());
            businessExtraInfo.setAppIntroductionUrl(sdkBusinessExtraInfo.getAppIntroductionUrl());

            businessExtraInfo.setUbtReportMap(sdkBusinessExtraInfo.getUbtReportMap());
            businessExtraInfo.setClickPostMap(sdkBusinessExtraInfo.getClickPostMap());
            businessExtraInfo.setClickRequestMethod(sdkBusinessExtraInfo.getClickRequestMethod());
        }

        Advertis advertis = new Advertis();
        advertis.setBusinessExtraInfo(businessExtraInfo);
        advertis.setAppPermissions(adPermissions);
        advertis.setAdid(adapterModel.getAdid());
        advertis.setResponseId(adapterModel.getResponseId());
        advertis.setPositionName(adapterModel.getPositionName());
        try {
            advertis.setPositionId(Integer.parseInt(adapterModel.getAdPositionId()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        advertis.setAdtype(adapterModel.getAdtype());
        advertis.setCommonReportMap(adapterModel.getCommonReportMap());
        advertis.setClickUrls(adapterModel.getClickUrls());
        advertis.setThirdShowStatUrls(adapterModel.getThirdShowStatUrls());
        advertis.setThirdClickStatUrls(adapterModel.getThirdClickStatUrls());
        advertis.setLinkType(adapterModel.getLinkType()); // link类型
        advertis.setLinkUrl(adapterModel.getLinkUrl());
        advertis.setDpRealLink(adapterModel.getDpRealLink()); // dp 链接
        advertis.setClickType(adapterModel.getClickType()); // 点击类型
        advertis.setRealLink(adapterModel.getRealLink()); // 链接
        advertis.setDpRetrySecond(adapterModel.getDpRetrySecond());
        advertis.setName(adapterModel.getName()); // 广告标题
        advertis.setJumpTrackId(adapterModel.getJumpTrackId()); // 内容推广落地页，声音ID
        advertis.setAutoJumpTime(adapterModel.getAutoJumpTime()); // 内容推广落地页， 自动跳转时长
        advertis.setBusinessExtraInfo(businessExtraInfo); // 透传字段
        advertis.setClientIp(adapterModel.getClientIp());
        advertis.setShowTokens(adapterModel.getShowTokens());
        advertis.setShowTokenEnable(adapterModel.isShowTokenEnable());
        advertis.setClickTokens(adapterModel.getClickTokens());
        advertis.setClickTokenEnable(adapterModel.isClickTokenEnable());
        advertis.setCloseStyle(adapterModel.getCloseStyle());
        advertis.setColumnSequence(adapterModel.getColumnSequence());
        advertis.setHomeRank(adapterModel.getHomeRank());
        advertis.setDisplayAnimation(adapterModel.getDisplayAnimation());
        advertis.setAnimationType(adapterModel.getAnimationType());
        advertis.setOpenlinkType(adapterModel.getOpenlinkType());
        advertis.setIsInternal(adapterModel.getIsInternal());
        advertis.setName(adapterModel.getName());
        advertis.setThirdDpArouseUrl(adapterModel.getThirdDpArouseUrl());
        advertis.setThirdDpArouseFailUrl(adapterModel.getThirdDpArouseFailUrl());
        advertis.setProviderName(adapterModel.getProviderName());
        advertis.setLandScape(adapterModel.isLandScape());
        advertis.setDspPositionId(adapterModel.getDspPositionId());
        advertis.setWxMiniProgramId(adapterModel.getWxMiniProgramId());
        advertis.setLandingPageResId(adapterModel.getLandingPageResId());
        advertis.setStartAt(adapterModel.getStartAt());
        advertis.setEndAt(adapterModel.getEndAt());
        advertis.setPreviewAd(adapterModel.isPreviewAd());
        advertis.setTrueExposure(adapterModel.isTrueExposure());
        advertis.setShowUrls(adapterModel.getShowUrls());
        advertis.setThirdStatUrl(adapterModel.getThirdStatUrl());
        advertis.setAdpr(adapterModel.getAdpr());
        advertis.setRecSrc(adapterModel.getRecSrc());
        advertis.setRecTrack(adapterModel.getRecTrack());
        advertis.setBucketIds(adapterModel.getBucketIds());
        advertis.setStrongType(adapterModel.getStrongType());
        advertis.setLogoUrl(adapterModel.getLogo());
        advertis.setClickTitle(adapterModel.getClickTitle());
        advertis.setButtonText(adapterModel.getButtonText());
        advertis.setEnableShowProcessButton(adapterModel.isEnableShowProcessButton());
        advertis.setClickReportFlag(adapterModel.isClickReportFlag());
        advertis.setClickJumpType(adapterModel.getClickJumpType());

        // 下载业务的，
        advertis.setDownloadAppLogo(adapterModel.getDownloadAppLogo()); // 下载logo
        advertis.setDownloadAppName(adapterModel.getDownloadAppName()); // 下载app name
        advertis.setDownloadPopupStyle(adapterModel.getDownloadPopupStyle()); // 下载弹窗样式类型
        advertis.setDownloadAppDesc(adapterModel.getDownloadAppDesc());  // 应用描述
        advertis.setDownloadProgressBarClickType(adapterModel.getDownloadProgressBarClickType()); // 通知栏下载器点击行为配置：1）触发暂停/继续；2）前往下载中心（不可暂停/继续，没有暂停按钮），默认是2
        advertis.setEnableDownloadPopUp(adapterModel.isEnableDownloadPopUp()); // 下载物料是否出现弹窗（false无，true有）
        advertis.setAppPackageName(adapterModel.getAppPackageName()); // apk包名
        advertis.setDownloadPopUpClickArea(adapterModel.getDownloadPopUpClickArea()); // 下载弹窗可点击位置配置（0：下载按钮，1：按钮+logo+app name+推广应用描述，2：非关闭按钮）
        advertis.setAppVersion(adapterModel.getAppVersion()); // 合规弹窗样式4： app信息- 版本号
        advertis.setAppSize(adapterModel.getAppSize()); // 合规弹窗样式4： app信息- apk大小
        advertis.setAppDeveloper(adapterModel.getAppDeveloper()); // 合规弹窗样式4： app信息- 开发者
        advertis.setAppPrivacyPolicy(adapterModel.getAppPrivacyPolicy()); // 合规弹窗样式4： app信息- 隐私协议链接
        advertis.setAppPermissions(adPermissions); // 合规弹窗样式4： app信息- 权限列表
        advertis.setOpenBounce(adapterModel.getOpenBounce());
        advertis.setBounceDelay(adapterModel.getBounceDelay());
        advertis.setEnableContinuePlay(adapterModel.isEnableContinuePlay());
        advertis.setCloseButtonText1(adapterModel.getCloseButtonText1());
        advertis.setCloseButtonText2(adapterModel.getCloseButtonText2());
        advertis.setWaitCloseExpire(adapterModel.getWaitCloseExpire());
        advertis.setNoAdButtonStyle(adapterModel.getNoAdButtonStyle());
        advertis.setNoAdText(adapterModel.getNoAdText());
        advertis.setSoundType(adapterModel.getSoundType());
        advertis.setVideoDurationTime(adapterModel.getVideoDurationTime());
        advertis.setBackupCover(adapterModel.getBackupCover());
        advertis.setHideInfoForWebUa(adapterModel.isHideInfoForWebUa());
        advertis.setDriveMode(adapterModel.getDriveMode());
        advertis.setAutoShowWebTime(adapterModel.getAutoShowWebTime());
        advertis.setUbtReportMap(adapterModel.getUbtReportMap());
        advertis.setClickPostMap(adapterModel.getClickPostMap());
        advertis.setClickRequestMethod(adapterModel.getClickRequestMethod());
        advertis.setNeedDedupClick(adapterModel.getNeedDedupClick());
        advertis.setLastClickTime(adapterModel.getLastClickTime());
        return advertis;
    }

    public static TemplateUIModel getUiModelFromAdvertis(Advertis adModel) {
        TemplateUIModel templateUIModel = new TemplateUIModel();
        List<TemplateUIModel.BrandData> brandDatas = new ArrayList<>();
        List<com.ximalaya.ting.android.opensdk.model.advertis.BrandData> adModelBrandDatas = adModel.getComposite();
        if (!AdUtil.isEmptyCollects(adModelBrandDatas)) {
            for (BrandData adModelBrandData: adModelBrandDatas) {
                TemplateUIModel.BrandData uiBrandData = new TemplateUIModel.BrandData();
                if (TextUtils.isEmpty(adModelBrandData.getTitle())) {
                    uiBrandData.setTitle(adModel.getName());
                } else {
                    uiBrandData.setTitle(adModelBrandData.getTitle());
                }
                uiBrandData.setPicUrl(adModelBrandData.getPicUrl());
                uiBrandData.setVideoUrl(adModelBrandData.getVideoUrl());
                uiBrandData.setRealLink(adModelBrandData.getRealLink());
                uiBrandData.setDpLink(adModelBrandData.getDpLink());
                brandDatas.add(uiBrandData);
            }
        }
        templateUIModel.setComposite(brandDatas);
        templateUIModel.setShowStyle(adModel.getShowstyle());
        templateUIModel.setCover(adModel.getImageUrl());
        templateUIModel.setTitle(adModel.getName());
        templateUIModel.setSubTitle(adModel.getDescription());
        templateUIModel.setAdMark(adModel.getAdMark());
        if (!TextUtils.isEmpty(adModel.getClickTitle())) {
            templateUIModel.setButtonText(adModel.getClickTitle());
        } else  {
            templateUIModel.setButtonText(adModel.getButtonText());
        }
        templateUIModel.setButtonIconUrl(adModel.getButtonIconUrl());
        templateUIModel.setAdUserType(adModel.getAdUserType());
        templateUIModel.setPositionId(adModel.getPositionId());
        return templateUIModel;
    }

}
