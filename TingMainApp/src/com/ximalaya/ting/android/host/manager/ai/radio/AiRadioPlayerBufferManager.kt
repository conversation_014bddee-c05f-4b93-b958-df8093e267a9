package com.ximalaya.ting.android.host.manager.ai.radio

import com.ximalaya.aiagentconnect.sdk.logger.XYLogger
import java.io.File
import java.io.RandomAccessFile
import java.nio.ByteBuffer
import java.nio.channels.FileLock

/**
 * 存储单元messageId+partId
 * 缓存文件路径：cachePath/aiRadio/messageId/partId
 * */
class AiRadioPlayerBufferManager(cachePath: String) : IAiRadioMediaDataCallback {
    companion object {
        private const val TAG = "AiRadioPlayerBufferManager"
        private const val BUFFER_SIZE = 1024 * 8 // 8KB 缓冲区大小
    }

    private var isLoadingEnded = false
    private var mIsExecuteStart = false
    private val mObj = Any()

    // 文件缓存相关属性
    private var curPart: RadioPart? = null
    private var raf: RandomAccessFile? = null
    private var writePosition: Long = 0  // 写入位置指针
    private var writeLock: FileLock? = null  // 写入文件锁
    private var writeBuffer: ByteBuffer = ByteBuffer.allocate(BUFFER_SIZE) // 写入缓冲区

//    // 读
//    private var readPart: RadioPart? = null
//    private var readFile: RandomAccessFile? = null
//    private var readPosition: Long = 0   // 读取位置指针
//    private var readLock: FileLock? = null  // 读取文件锁

    private var rootPath = cachePath

    init {
        clear()
    }

    fun onStart(radioPart: RadioPart) {
        synchronized(this) {
            dlog("onStart >>> $radioPart")
            curPart = radioPart
            mIsExecuteStart = true
            isLoadingEnded = false
            initCacheFile(radioPart)
        }
    }

    fun onEnd(partId: String) {
        synchronized(this) {
            dlog("onLoadEnd >>> partId=$partId length=${raf?.length()}")
            mIsExecuteStart = false
            flush()
        }
    }

    private fun flush() {
        // 确保缓冲区中的数据被写入
        if (writeBuffer.position() > 0) {
            try {
                writeBuffer.flip()
                raf?.seek(writePosition)
                raf?.write(writeBuffer.array(), 0, writeBuffer.limit())
                writePosition += writeBuffer.limit()
            } catch (e: Exception) {
                dlog("Failed to flush buffer: ${e.message}")
            }
        }
    }

    fun writeData(data: ByteArray, offset: Int, length: Int): Int {
        synchronized(this) {
            return try {
                // 在写入前获取锁
                if (writeLock == null) {
//                    dlog("Attempting to acquire write lock")
                    writeLock = raf?.channel?.lock()
                    if (writeLock == null) {
//                        dlog("Failed to acquire write lock")
                        return -1
                    }
//                    dlog("Successfully acquired write lock")
                }

                var remaining = length
                var currentOffset = offset

                while (remaining > 0) {
                    val bytesToWrite = minOf(remaining, writeBuffer.remaining())

                    if (bytesToWrite > 0) {
                        writeBuffer.put(data, currentOffset, bytesToWrite)
                        currentOffset += bytesToWrite
                        remaining -= bytesToWrite
                    }

                    if (!writeBuffer.hasRemaining() || remaining == 0) {
                        writeBuffer.flip()
                        raf?.seek(writePosition)
                        raf?.write(writeBuffer.array(), 0, writeBuffer.limit())
                        writePosition += writeBuffer.limit()
                        writeBuffer.clear()
                    }
                }

//                dlog("Write success: new position=$writePosition, bytes=$length ${raf?.length()}")
                length
            } catch (e: Exception) {
                dlog("Write failed: ${e.message}")
                e.printStackTrace()
                releaseWriteLock()
                -1
            }
        }
    }

    override fun readData(
        radioPart: RadioPart,
        data: ByteArray,
        offset: Int,
        length: Int
    ): Int {
        return -1
//        synchronized(this) {
//            try {
//                if (isLoadingEnded) {
//                    dlog("read end >>>")
//                    return 0
//                }
//                if (readPart != radioPart || readFile == null) {
//                    readPart = radioPart
//                    readPosition = 0
//                    readFile = RandomAccessFile(radioPart2CacheFile(radioPart), "r")
//                }
//                if (readLock == null) {
//                    readLock = readFile?.channel?.tryLock()
//                    if (readLock == null) {
//                        dlog("Failed to acquire read lock")
//                        return 0
//                    }
//                }
//                readFile?.seek(readPosition)
//                val actualRead = readFile?.read(data, offset, length) ?: 0
//                readPosition += actualRead
//                dlog("Read success: $actualRead bytes")
//                return actualRead
//            } catch (e: Exception) {
//                dlog("Read error: ${e.message}")
//                releaseReadLock()
//                return -1
//            }
//        }
    }

    private fun releaseWriteLock() {
        try {
            writeLock?.release()
            writeLock = null
//            dlog("Write lock released successfully")
        } catch (e: Exception) {
//            dlog("Failed to release write lock: ${e.message}")
            e.printStackTrace()
        }
    }

//    private fun releaseReadLock() {
//        try {
//            readLock?.release()
//            readLock = null
//        } catch (e: Exception) {
//            dlog("Failed to release read lock: ${e.message}")
//        }
//    }

    private fun radioPart2CacheFile(radioPart: RadioPart): File {
        return File(radioPart.getFilePath(rootPath))
    }

    private fun initCacheFile(radioPart: RadioPart) {
        val cacheFile = radioPart2CacheFile(radioPart)
        if (cacheFile.exists()) {
            return
        }
        try {
            releaseWriteLock()
            raf?.close()
            cacheFile.parentFile?.mkdirs()
            cacheFile.createNewFile()
            raf = RandomAccessFile(cacheFile, "rw")
            writePosition = 0
            dlog("Created cache file: ${cacheFile.absolutePath}")
        } catch (e: Exception) {
            dlog("Init cache failed: ${cacheFile.absolutePath} ${e.message}")
            raf?.close()
            raf = null
        }
    }

    fun stop() {
        synchronized(this) {
            dlog("Stopping buffer manager")
            mIsExecuteStart = false
            flush()
            releaseWriteLock()
            writeBuffer.clear()
            try {
                raf?.close()
                raf = null
//                dlog("File closed successfully")
            } catch (e: Exception) {
//                dlog("Failed to close file: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    fun clear() {
        synchronized(this) {
            dlog("Clearing buffer manager")
            mIsExecuteStart = false
            releaseWriteLock()
            writeBuffer.clear()
            try {
                raf?.close()
                raf = null
//                dlog("File closed successfully")
            } catch (e: Exception) {
//                dlog("Failed to close file: ${e.message}")
                e.printStackTrace()
            }
            File(rootPath).deleteRecursively()
//            dlog("Cache directory cleared")
        }
    }

    /**
     * 网络断开，视同end
     */
    fun onDisconnected() {
        synchronized(this) {
            dlog("断开连接，停掉TTS")
            this.isLoadingEnded = true
            this.mIsExecuteStart = false
        }
    }

    private fun dlog(message: String) {
        XYLogger.log(TAG, message)
    }
}