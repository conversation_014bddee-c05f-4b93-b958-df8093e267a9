package com.ximalaya.ting.android.host.manager.bundleframework.route.router;

import com.ximalaya.ting.android.host.manager.bundleframework.listener.IActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.base.IAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNActivityRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFragmentRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter;

import java.util.HashMap;

import static com.ximalaya.ting.android.host.manager.bundleframework.route.RouterConstant.ACTIVITY_ACTION;
import static com.ximalaya.ting.android.host.manager.bundleframework.route.RouterConstant.FRAGMENT_ACTION;
import static com.ximalaya.ting.android.host.manager.bundleframework.route.RouterConstant.FUNCTION_ACTION;

/**
 * Created by <PERSON> on 2018/6/5 下午4:15.
 *
 * <AUTHOR>
 */
public class RNActionRouter implements IActionRouter {

    public HashMap<String, IAction> actionMap = new HashMap<>();


    @Override
    public IRNFragmentRouter getFragmentAction() {
        return (IRNFragmentRouter) actionMap.get(FRAGMENT_ACTION);
    }

    @Override
    public IRNFunctionRouter getFunctionAction() {
        return (IRNFunctionRouter) actionMap.get(FUNCTION_ACTION);
    }

    @Override
    public IRNActivityRouter getActivityAction() {
        return (IRNActivityRouter) actionMap.get(ACTIVITY_ACTION);
    }

    public void addAction(String actionName, IAction action) {
        actionMap.put(actionName, action);
    }
}
