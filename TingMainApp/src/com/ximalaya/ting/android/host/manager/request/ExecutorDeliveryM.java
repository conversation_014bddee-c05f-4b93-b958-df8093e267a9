/**
 * ExecutorDeliveryM.java
 * com.ximalaya.ting.android.data.request
 *
 * Function： TODO 
 *
 *   ver     date      		author
 * ──────────────────────────────────
 *   		 2015-11-12 		jack.qin
 *
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.host.manager.request;

import android.os.Handler;

import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallbackWithExpandErrorInfo;
import com.ximalaya.ting.android.xmbootmonitor.BootMonitor;

import java.util.concurrent.Executor;

import androidx.annotation.Nullable;
import okhttp3.Headers;


/**
 * ClassName:ExecutorDeliveryM Function: TODO ADD FUNCTION Reason: TODO ADD
 * REASON
 * 
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-11-12 下午8:41:17
 * 
 * @see
 */
public class ExecutorDeliveryM {
	private final Executor mResponsePoster;

	public ExecutorDeliveryM(final Handler handler) {
		// Make an Executor that just wraps the handler.
		mResponsePoster = new Executor() {
			@Override
			public void execute(Runnable command) {
				handler.post(command);
			}
		};
	}

	public <T> void postSuccess(IDataCallBack<T> callback, T t, Headers headers) {
		mResponsePoster.execute(new ResponseDeliveryRunnable<>(0, t, callback,
				headers));
	}

	public <T> void postError(int code, String message,
			IDataCallBack<T> callback) {
		mResponsePoster.execute(new ResponseDeliveryRunnable<>(1, code,
				message, null, callback));
	}

	public <T> void postError(int code, String message, Object expandErrorInfo,
							  IDataCallbackWithExpandErrorInfo<T> callback) {
		mResponsePoster.execute(new ResponseDeliveryRunnable<>(1, code,
				message, null, callback).setExpandErrorInfo(expandErrorInfo));
	}

	private class ResponseDeliveryRunnable<T> implements Runnable {
		private int code;
		private String message;
		private IDataCallBack<T> callback;
		private T t;
		private int postCode;
		private Headers headers;
		@Nullable private Object expandErrorInfo;

		public ResponseDeliveryRunnable(int postCode, int code, String message,
				T t, IDataCallBack<T> callback) {
			this.postCode = postCode;
			this.code = code;
			this.message = message;
			this.callback = callback;
			this.t = t;
		}

		public ResponseDeliveryRunnable(int postCode, T t,
										IDataCallBack<T> callback, Headers headers) {
			this.postCode = postCode;
			this.callback = callback;
			this.t = t;
			this.headers = headers;
		}

		public ResponseDeliveryRunnable<T> setExpandErrorInfo(Object expandErrorInfo) {
			this.expandErrorInfo = expandErrorInfo;
			return this;
		}

		@Override
		public void run() {
			if(callback == null) {
				return;
			}

			if (postCode == 0) {
				BootMonitor.getInstance().getProcessDuration(headers, true);
				callback.onSuccess(t);
				BootMonitor.getInstance().getProcessDuration(headers, false);
			} else if (postCode == 1) {
				if (callback instanceof IDataCallbackWithExpandErrorInfo) {
					((IDataCallbackWithExpandErrorInfo<T>) callback).onError(code, message, expandErrorInfo);
				} else {
					callback.onError(code, message);
				}
			}
		}

	}
}
