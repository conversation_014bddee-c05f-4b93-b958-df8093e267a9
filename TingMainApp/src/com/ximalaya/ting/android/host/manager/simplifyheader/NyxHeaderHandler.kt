package com.ximalaya.ting.android.host.manager.simplifyheader

import com.ximalaya.ting.android.host.manager.configurecenter.CConstants.Group_android.ITEM_REQUEST_SIMPLIFY_HEADER_KEYS_NYX
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants.Group_android.ITEM_REQUEST_SIMPLIFY_URL_PATH_NYX
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.RequestBody
import org.json.JSONObject

/**
 * Created by xiaolei on 2022/9/7.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13701664636
 */
class NyxHeaderHandler : BaseHeaderHandler() {

    private object InstanceHolder {
        var sInstance: IHeaderHandler = NyxHeaderHandler()
    }

    companion object {
        @JvmStatic
        fun getInstance(): IHeaderHandler {
            return InstanceHolder.sInstance
        }
    }

    override fun getFilterConfig(): String {
        return ITEM_REQUEST_SIMPLIFY_HEADER_KEYS_NYX
    }

    override fun getPathConfig(): String {
        return ITEM_REQUEST_SIMPLIFY_URL_PATH_NYX
    }

    override fun handleRequest(request: Request): Request {
        val newBuilder = request.newBuilder()
        val oriBody = getBodyString(request)
        if (oriBody.isNullOrEmpty()) {
            return request
        }
        return try {
            val newBody = JSONObject()
            val jsonHeader = JSONObject()
            val filterKeys = getFilterHeaderKeys()
            newBody.put("headers", jsonHeader)
            newBody.put("body", oriBody)
            updateHeader(filterKeys, request, jsonHeader, newBuilder)
            zipBody(newBody, newBuilder)
            newBuilder.build()
        } catch (e: Exception) {
            request
        }
    }

    private fun zipBody(newBody: JSONObject, newBuilder: Request.Builder) {
        zipRequestBody(newBody.toString())?.let {
            newBuilder.post(
                RequestBody.create(
                    MediaType.parse("application/x-www-form-urlencoded"),
                    it
                )
            )
                .header("Content-Encoding", "gzip")
                .header("Transfer-Encoding", "chunked")
        }
    }
}