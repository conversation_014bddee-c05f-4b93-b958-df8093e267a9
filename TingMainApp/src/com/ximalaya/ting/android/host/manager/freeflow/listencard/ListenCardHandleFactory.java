package com.ximalaya.ting.android.host.manager.freeflow.listencard;

import android.content.Context;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.xmutil.NetworkType;

/**
 * Created by nali on 2018/4/25.
 *
 * <AUTHOR>
 */
public class ListenCardHandleFactory {

    @Nullable
    public BaseListenerCard getListenerCard(Context context) {
        if(context == null) {
            return null;
        }
        int operator = NetworkType.getOperator(context);
        /*if(operator == NetworkType.OPERATOR_TELECOM) {
            return new TelecomListenCard();
        } else*/ if(operator == NetworkType.OPERATOR_UNICOME){
            return new UnicomListenCard();
        }
        return null;
    }
}
