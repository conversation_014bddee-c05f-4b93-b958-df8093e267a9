package com.ximalaya.ting.android.host.manager.pay;

import android.content.Intent;
import android.util.Pair;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenConstant;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenHostManager;
import com.ximalaya.ting.android.host.manager.freeunlock.PlayAutoUnlockTrackActionManager;
import com.ximalaya.ting.android.host.manager.play.PlayAutoBuyTrackActionManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.util.commercial.AlbumTypeUtil;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.ActionConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.model.track.UnlockModeInfo;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by 5Greatest on 2020.07.28
 *
 * <AUTHOR>
 * On 2020-07-28
 */
public class PayResultManager {
    public static final String TAG = PayResultManager.class.getSimpleName();

    public static final String RELOAD_WITH_UPDATE = "reloadWithUpdate";
    public static final String RELOAD = "reload";
    public static final String FINISH = "finish";


    public static final String ACTION_TRACK_AUTO_UNLOCK = PlayAutoUnlockTrackActionManager.ACTION_UNLOCK_RESULT;
    public static final int TYPE_TRACK_AUTO_UNLOCK = 99;

    public static final String ACTION_TRACK_AUTO_BUY = PlayAutoBuyTrackActionManager.ACTION_BUY_RESULT;
    public static final int TYPE_TRACK_AUTO_BUY = 100;

    public static final String ACTION_BUY_VIP = ActionConstants.ACTION_BOUGHT_VIP_SUCCESS;
    public static final int TYPE_BUY_VIP = 101;
    public static final int TYPE_BUY_VERTICAL_VIP = 102;
    public static final int TYPE_BUY_VERTICAL_VIP_MASTER_CLASS = 103;

    public static final String ACTION_AUTO_RENEW_VIP = ActionConstants.ACTION_AUTO_RENEW_VIP_SUCCESS;
    public static final int TYPE_AUTO_RENEW_VIP = 130;

    public static final String ACTION_TRAINING_CAMP_BUY = ActionConstants.ACTION_WHOLE_ALBUM_SUCCESS;
    public static final int TYPE_TRAINING_CAMP_BUY = 140;

    public static final String ACTION_OLD_PAY_MANAGER = PayManager.ACTION;
    public static final int TYPE_OLD_PAY_MANAGER = 200;

    public static final String ACTION_BUY_SINGLE_TRACK = ActionConstants.LOCAL_ACTION_BOUGHT_SINGLE_TRACK_SUCCESS;
    public static final int TYPE_BUY_SINGLE_TRACK  = 300;

    public static final String ACTION_BUY_BUNCH_TRACK = ActionConstants.LOCAL_ACTION_BOUGHT_BUNCH_TRACK_SUCCESS;
    public static final int TYPE_BUY_BUNCH_TRACK  = 310;

    public static final String ACTION_BUY_LEFT_ALL_TRACK = ActionConstants.LOCAL_ACTION_BOUGHT_TRACK_LEFT_ALL_SUCCESS;
    public static final int TYPE_BUY_LEFT_ALL_TRACK  = 320;

    public static final String ACTION_BUY_UNDEFINE_NUM_TRACK = ActionConstants.LOCAL_ACTION_BOUGHT_TRACK_UNDEFINE_NUM_SUCCESS;
    public static final int TYPE_BUY_UNDEFINE_NUM_TRACK = 330;

    public static final String ACTION_BUY_WHOLE_ALBUM = ActionConstants.LOCAL_ACTION_WHOLE_ALBUM_BOUGHT_SUCCESS;
    public static final int TYPE_BUY_WHOLE_ALBUM = 400;

    public static final String ACTION_FREE_LISTEN_UNLOCK_WITH_TIME = ActionConstants.LOCAL_ACTION_FREE_LISTEN_UNLOCK_WITH_TIME_SUCCESS;
    public static final int TYPE_FREE_LISTEN_UNLOCK_WITH_TIME = 500;

    public static final String ACTION_FREE_LISTEN_UNLOCK_WITH_TIME_END = ActionConstants.LOCAL_ACTION_FREE_LISTEN_UNLOCK_WITH_TIME_END;
    public static final int TYPE_FREE_LISTEN_UNLOCK_WITH_TIME_END = 510;

    public static final String ACTION_FREE_LISTEN_UNLOCK_QUIT = ActionConstants.LOCAL_ACTION_FREE_LISTEN_QUIT;
    public static final int TYPE_FREE_LISTEN_UNLOCK_QUIT = 550;

    public static final String ACTION_BUY_VIP_SOUND = ActionConstants.ACTION_BOUGHT_VIP_SOUND_SUCCESS;
    public static final int TYPE_BUY_VIP_SOUND = 600;

    public static final String ACTION_UNLOCK_TRACK_SUCCESS_NOTIFY = ActionConstants.ACTION_UNLOCK_TRACK_SUCCESS_NOTIFY;
    public static final int TYPE_ACTION_UNLOCK_TRACK_SUCCESS = 650;

    public static final String ACTION_PLATINUM_VIP_COUPON_CONSUME_SUCCESS_NOTIFY = ActionConstants.ACTION_PLATINUM_VIP_COUPON_CONSUME_SUCCESS_NOTIFY;
    public static final int TYPE_PLATINUM_VIP_COUPON_CONSUME_SUCCESS = 700;

    public static void checkPayResult(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent || null == data) {
            return;
        }
        String action = intent.getAction();
        switch (action) {
            case ACTION_BUY_WHOLE_ALBUM:
                if (ResultData.FRAGMENT_TYPE_PLAY == data.getType()) {
                    checkBuyWholeAlbumResultInPlayFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_WHOLE_ALBUM == data.getType()) {
                    checkBuyWholeAlbumResultInWholeAlbumFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkBuyWholeAlbumResultInAlbumFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_TRAINING_CAMP_BUY:
                if (ResultData.FRAGMENT_TYPE_WHOLE_ALBUM == data.getType()) {
                    checkBuyWholeAlbumResultInTrainingCampFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_AD_UNLOCK == data.getType()) {
                    checkBuyWholeAlbumResultInAdUnlockFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_TRACK_AUTO_BUY:
                if (ResultData.FRAGMENT_TYPE_PLAY == data.getType()) {
                    checkTrackAutoBuyResultInPlayFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkTrackAutoBuyResultInAlbumFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_TRACK_AUTO_UNLOCK:
                onSuccess.operate(TYPE_TRACK_AUTO_UNLOCK, "");
                break;
            case ACTION_BUY_VIP:
                if (ResultData.FRAGMENT_TYPE_PLAY == data.getType()) {
                    checkBuyVipResultInPlayFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_WHOLE_ALBUM == data.getType()) {
                    checkBuyVipResultInWholeAlbumFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkBuyVipResultInAlbumFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_AD_UNLOCK == data.getType()) {
                    checkBuyVipResultInAdUnlockFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_OLD_PAY_MANAGER:
                if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkOldPayManagerBroadCastInAlbumFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_BUY_SINGLE_TRACK:
                if (ResultData.FRAGMENT_TYPE_PLAY == data.getType()) {
                    checkBuySingleTrackBroadCastInPlayFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkBuySingleTrackBroadCastInAlbumFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_AD_UNLOCK == data.getType()) {
                    checkBuySingleTrackBroadCastInAdUnlockFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_BUY_BUNCH_TRACK:
                if (ResultData.FRAGMENT_TYPE_PLAY == data.getType()) {
                    checkBuyBunchTrackBroadCastInPlayFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkBuyBunchTrackBroadCastInAlbumFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_AD_UNLOCK == data.getType()) {
                    checkBuyBunchTrackBroadCastInAdUnlockFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_BUY_UNDEFINE_NUM_TRACK:
               if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkBuyUndefineNumTrackBroadCastInAlbumFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_BUY_LEFT_ALL_TRACK:
                if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkBuyLeftAllTrackBroadCastInAlbumFragment(intent, data, onSuccess, onError);
                }
            case ACTION_FREE_LISTEN_UNLOCK_WITH_TIME:
                if (ResultData.FRAGMENT_TYPE_PLAY == data.getType()) {
                    checkFreeListenUnlockBroadCastInPlayFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkFreeListenUnlockBroadCastInAlbumFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_FREE_LISTEN_UNLOCK_WITH_TIME_END:
                if (ResultData.FRAGMENT_TYPE_PLAY == data.getType()) {
                    checkFreeListenUnlockEndBroadCastInPlayFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkFreeListenUnlockEndBroadCastInAlbumFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_FREE_LISTEN_UNLOCK_QUIT:
                if (ResultData.FRAGMENT_TYPE_PLAY == data.getType()) {
                    checkFreeListenUnlockQuitBroadCastInPlayFragment(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkFreeListenUnlockQuitBroadCastInAlbumFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_BUY_VIP_SOUND:
                if (ResultData.FRAGMENT_TYPE_PLAY == data.getType()) {
                    checkBuyVipSoundResultInPlayFragment(intent, data, onSuccess, onError);
                }
                break;
            case ACTION_UNLOCK_TRACK_SUCCESS_NOTIFY:
                onSuccess.operate(TYPE_ACTION_UNLOCK_TRACK_SUCCESS, "");
                break;
            case ACTION_PLATINUM_VIP_COUPON_CONSUME_SUCCESS_NOTIFY:
                if (ResultData.FRAGMENT_TYPE_PLAY == data.getType()) {
                    checkPlatinumVipCouponConsumeResultInPlay(intent, data, onSuccess, onError);
                } else if (ResultData.FRAGMENT_TYPE_ALBUM_FRAGMENT == data.getType()) {
                    checkPlatinumVipCouponConsumeResultInAlbum(intent, data, onSuccess, onError);
                }
                break;
            default:
                break;
        }
    }

    private static void checkBuyWholeAlbumResultInPlayFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data.playingSoundInfo
                || null == data.playingSoundInfo.trackInfo) {
            return;
        }

        long albumId = intent.getLongExtra(BundleKeyConstants.KEY_ALBUM_ID, -1);
        if (albumId == data.playingSoundInfo.trackInfo.albumId) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_BUY_WHOLE_ALBUM, null);
            }
        } else {
            if (null != onError) {
                onError.operate(TYPE_BUY_WHOLE_ALBUM, null);
            }
        }
    }

    private static void checkBuyWholeAlbumResultInWholeAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data.album) {
            return;
        }

        long albumId = intent.getLongExtra(BundleKeyConstants.KEY_ALBUM_ID, -1);
        if (albumId == data.album.getId()) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_BUY_WHOLE_ALBUM, null);
            }
        } else {
            if (null != onError) {
                onError.operate(TYPE_BUY_WHOLE_ALBUM, null);
            }
        }
    }

    private static void checkBuyWholeAlbumResultInAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data.album) {
            return;
        }

        long albumId = intent.getLongExtra(BundleKeyConstants.KEY_ALBUM_ID, -1);
        if (albumId == data.album.getId()) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_BUY_WHOLE_ALBUM, null);
            }
        } else {
            if (null != onError) {
                onError.operate(TYPE_BUY_WHOLE_ALBUM, null);
            }
        }
    }

    private static void checkBuyWholeAlbumResultInTrainingCampFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data.album) {
            return;
        }
        if (null != onSuccess) {
            onSuccess.operate(TYPE_TRAINING_CAMP_BUY, null);
        }
    }

    private static void checkPlatinumVipCouponConsumeResultInAlbum(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data.album) {
            return;
        }

        long albumId = intent.getLongExtra(BundleKeyConstants.KEY_ALBUM_ID, -1);
        Logger.d("mark112211","Coupon consume success in album。albumId="+albumId+",target="+data.album.getId());
        if (albumId == data.album.getId()) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_PLATINUM_VIP_COUPON_CONSUME_SUCCESS, null);
            }
        } else {
            if (null != onError) {
                onError.operate(TYPE_PLATINUM_VIP_COUPON_CONSUME_SUCCESS, null);
            }
        }
    }

    private static void checkPlatinumVipCouponConsumeResultInPlay(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data.playingSoundInfo) {
            return;
        }

        long albumId = intent.getLongExtra(BundleKeyConstants.KEY_ALBUM_ID, -1);
        long targetAlbumId = data.playingSoundInfo.trackInfo != null ? data.playingSoundInfo.trackInfo.albumId : (data.playingSoundInfo.albumInfo != null ? data.playingSoundInfo.albumInfo.albumId : -1);
        Logger.d("mark112211","Coupon consume success in Play。albumId="+albumId+",target="+targetAlbumId);
        if (albumId == targetAlbumId) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_PLATINUM_VIP_COUPON_CONSUME_SUCCESS, null);
            }
        } else {
            if (null != onError) {
                onError.operate(TYPE_PLATINUM_VIP_COUPON_CONSUME_SUCCESS, null);
            }
        }
    }

    private static void checkBuyWholeAlbumResultInAdUnlockFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data.track) {
            return;
        }

        /*long
        if (null != onSuccess) {
            onSuccess.operate(TYPE_TRAINING_CAMP_BUY, null);
        }*/
        /// 未完成 todo
    }

    private static void checkTrackAutoBuyResultInPlayFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data
                || null == data.playingSoundInfo) {
            return;
        }

        PlayingSoundInfo soundInfoInFragment = data.playingSoundInfo;

        long trackId = intent.getLongExtra(PlayAutoBuyTrackActionManager.ARGS_TRACK_ID, 0);
        if (soundInfoInFragment == null
                || soundInfoInFragment.trackInfo == null
                || soundInfoInFragment.trackInfo.trackId != trackId) {
            return;
        }
        int resultType = intent.getIntExtra(PlayAutoBuyTrackActionManager.ARGS_BUY_RESULT, 0);
        if (PlayAutoBuyTrackActionManager.BUY_RESULT_SUCCESS == resultType) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_TRACK_AUTO_BUY, null);
            }
        } else if (PlayAutoBuyTrackActionManager.BUY_RESULT_ERROR == resultType) {
            if (null != onError) {
                onError.operate(TYPE_TRACK_AUTO_BUY, null);
            }
        }
    }

    private static void checkTrackAutoBuyResultInAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent) {
            return;
        }

        long trackId = intent.getLongExtra(PlayAutoBuyTrackActionManager.ARGS_TRACK_ID, 0);
        int resultType = intent.getIntExtra(PlayAutoBuyTrackActionManager.ARGS_BUY_RESULT, 0);
        if (PlayAutoBuyTrackActionManager.BUY_RESULT_SUCCESS == resultType) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_TRACK_AUTO_BUY, "" + trackId);
            }
        } /*else if (PlayAutoBuyTrackActionManager.BUY_RESULT_ERROR == resultType) {
            if (null != onError) {
                onError.operate(TYPE_TRACK_AUTO_BUY, null);
            }
        }*/
    }

    private static void checkBuyVipResultInPlayFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        PlayingSoundInfo soundInfoInFragment;
        if (null == intent
                || null == data
                || null == (soundInfoInFragment = data.playingSoundInfo)) {
            return;
        }

        String vipType = intent.getStringExtra("vipType");
        if (StringUtil.isEmpty(vipType)) {
            if (null != soundInfoInFragment.albumInfo
                    && (1 == soundInfoInFragment.albumInfo.getVipFreeType()
                    || soundInfoInFragment.albumInfo.isVipFree
                    || soundInfoInFragment.albumInfo.supportFreeListen())) {
                // vip声音才刷新
                if (null != onSuccess) {
                    onSuccess.operate(TYPE_BUY_VIP, null);
                }
            }
        } else {
            if ("masterclass_vip".equals(vipType)
                    && null != soundInfoInFragment.albumInfo
                    && AlbumTypeUtil.VIP_CODE_MASTER_CLASS == soundInfoInFragment.albumInfo.vipCode) {
                // 大师课垂类会员
                if (null != onSuccess) {
                    onSuccess.operate(TYPE_BUY_VERTICAL_VIP_MASTER_CLASS, null);
                }
            } else {
                // 垂类会员
                if (null != onSuccess) {
                    onSuccess.operate(TYPE_BUY_VERTICAL_VIP, null);
                }
            }
        }
    }

    private static void checkBuyVipSoundResultInPlayFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        PlayingSoundInfo soundInfoInFragment;
        if (null == intent
                || null == data
                || null == (soundInfoInFragment = data.playingSoundInfo)) {
            return;
        }
        if (null != onSuccess) {
            onSuccess.operate(TYPE_BUY_VIP_SOUND, null);
        }
    }

    private static void checkBuyVipResultInWholeAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data.album) {
            return;
        }
        String vipType = intent.getStringExtra("vipType");
        if (StringUtil.isEmpty(vipType)) {
            if (data.album.isVipFree() || 1 == data.album.getVipFreeType() || data.album.isFreeListenAlbum()) {
                if (null != onSuccess) {
                    onSuccess.operate(TYPE_BUY_VIP, null);
                }
            }
        } else {
            // 垂类会员
            checkAlbumAccessibleViaNet(data.album.getId(), TYPE_BUY_VERTICAL_VIP, onSuccess, onError, null, null);
        }
    }

    private static void checkBuyVipResultInAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        AlbumM album;
        if (null == intent
                || null == (album = data.album)) {
            return;
        }
        String vipType = intent.getStringExtra("vipType");
        if ("masterclass_vip".equals(vipType) && AlbumTypeUtil.VIP_CODE_MASTER_CLASS == album.getVipCode()) {
            // 大师课垂类会员
            if (null != onSuccess) {
                onSuccess.operate(TYPE_BUY_VERTICAL_VIP_MASTER_CLASS, null);
            }
        } else {
            // 大会员
            if (data.album.isVipFree() || 1 == data.album.getVipFreeType() || 1 == data.album.vipPriorListenStatus || data.album.isFreeListenAlbum()) {
                if (null != onSuccess) {
                    if (data.album.getPriceTypeEnum() == PayManager.PAY_ALBUM_MEMBER_PART
                            || PayManager.PAY_ALBUM_PART == data.album.getPriceTypeEnum()
                            || 1 == data.album.vipPriorListenStatus) {
                        onSuccess.operate(TYPE_BUY_VIP, FINISH);
                    } else {
                        onSuccess.operate(TYPE_BUY_VIP, null);
                    }
                }
            }
        }
    }

    private static void checkBuyVipResultInAdUnlockFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data.track) {
            return;
        }
        if (data.track.isVipFree() || 1 == data.track.getVipFreeType() || 1 == data.track.vipPriorListenStatus) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_BUY_VIP, null);
            }
        }
    }

    private static void checkOldPayManagerBroadCastInAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data.album
                || null == onSuccess) {
            return;
        }

        long albumId = intent.getLongExtra(
                BundleKeyConstants.KEY_ALBUM_ID, -1);
        if (albumId == data.album.getId()) {
            onSuccess.operate(TYPE_OLD_PAY_MANAGER, RELOAD_WITH_UPDATE);
            return;
        }

        int buyType = intent.getIntExtra(BundleKeyConstants.KEY_FLAG, 0);
        if (PayManager.TYPE_ALBUM_WHOLE_MEMBER == buyType) {
            if (intent.getSerializableExtra(BundleKeyConstants.KEY_ALBUM_ID_LIST) != null) {
                List<Long> memberIdList = (List<Long>) intent.getSerializableExtra(BundleKeyConstants.KEY_ALBUM_ID_LIST);
                if (memberIdList != null) {
                    for (Long i : memberIdList) {
                        if (i != null && i == data.album.getId()) {
                            onSuccess.operate(TYPE_OLD_PAY_MANAGER, RELOAD_WITH_UPDATE);
                            return;
                        }
                    }
                }
            }
            onSuccess.operate(TYPE_OLD_PAY_MANAGER, RELOAD);
        }
    }

    private static void checkBuySingleTrackBroadCastInAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data
                || null == data.album) {
            return;
        }

        long albumId = intent.getLongExtra(
                BundleKeyConstants.KEY_ALBUM_ID, -1);
        long trackId = intent.getLongExtra(
                BundleKeyConstants.KEY_TRACK_ID, -1);
        if (albumId == data.album.getId()) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_BUY_SINGLE_TRACK, "");
                onSuccess.operate(TYPE_BUY_SINGLE_TRACK, "", trackId);
            }
        }
    }

    private static void checkBuySingleTrackBroadCastInAdUnlockFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data
                || null == data.track) {
            return;
        }

        long albumId = intent.getLongExtra(
                BundleKeyConstants.KEY_ALBUM_ID, -1);
        long trackId = intent.getLongExtra(
                BundleKeyConstants.KEY_TRACK_ID, -1);
        if (trackId == data.track.getDataId()) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_BUY_SINGLE_TRACK, "");
                onSuccess.operate(TYPE_BUY_SINGLE_TRACK, "", trackId);
            }
        }
    }

    private static void checkBuySingleTrackBroadCastInPlayFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data
                || null == data.playingSoundInfo) {
            return;
        }

        long trackId = intent.getLongExtra(
                BundleKeyConstants.KEY_TRACK_ID, -1);
        if (trackId == (null == data.playingSoundInfo.trackInfo ? 0 : data.playingSoundInfo.trackInfo.trackId)) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_BUY_SINGLE_TRACK, "");
                onSuccess.operate(TYPE_BUY_SINGLE_TRACK, "", trackId);
            }
        }
    }

    private static void checkBuyBunchTrackBroadCastInAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data
                || null == data.album) {
            return;
        }

        long albumId = intent.getLongExtra(
                BundleKeyConstants.KEY_ALBUM_ID, -1);
        long[] trackIds = intent.getLongArrayExtra(
                BundleKeyConstants.KEY_ALBUM_ID_LIST);
        if (null != trackIds && albumId == data.album.getId()) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_BUY_BUNCH_TRACK, "");
                onSuccess.operate(TYPE_BUY_BUNCH_TRACK, "", trackIds);
            }
        }
    }

    private static void checkBuyBunchTrackBroadCastInPlayFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data
                || null == data.playingSoundInfo) {
            return;
        }

        long[] trackIds = intent.getLongArrayExtra(
                BundleKeyConstants.KEY_ALBUM_ID_LIST);
        long thisTrackId = null == data.playingSoundInfo.trackInfo ? 0 : data.playingSoundInfo.trackInfo.trackId;
        if (null != trackIds) {
            boolean contains = false;
            for (long temp : trackIds) {
                if (temp == thisTrackId) {
                    contains = true;
                    break;
                }
            }
            if (contains) {
                if (null != onSuccess) {
                    onSuccess.operate(TYPE_BUY_BUNCH_TRACK, "");
                    onSuccess.operate(TYPE_BUY_BUNCH_TRACK, "", trackIds);
                }
            } else {
                if (null != onError) {
                    onError.operate(TYPE_BUY_BUNCH_TRACK, "");
                }
            }
        }
    }

    private static void checkBuyUndefineNumTrackBroadCastInAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data
                || null == data.album) {
            return;
        }

        long albumId = intent.getLongExtra(
                BundleKeyConstants.KEY_ALBUM_ID, -1);
        long[] trackIds = intent.getLongArrayExtra(
                BundleKeyConstants.KEY_ALBUM_ID_LIST);
        if (albumId == data.album.getId()) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_BUY_UNDEFINE_NUM_TRACK, "");
            }
        }
    }

    private static void checkBuyBunchTrackBroadCastInAdUnlockFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data
                || null == data.track) {
            return;
        }

        long[] trackIds = intent.getLongArrayExtra(
                BundleKeyConstants.KEY_ALBUM_ID_LIST);
        long thisTrackId = data.track.getDataId();
        if (null != trackIds) {
            boolean contains = false;
            for (long temp : trackIds) {
                if (temp == thisTrackId) {
                    contains = true;
                    break;
                }
            }
            if (contains) {
                if (null != onSuccess) {
                    onSuccess.operate(TYPE_BUY_BUNCH_TRACK, "");
                    onSuccess.operate(TYPE_BUY_BUNCH_TRACK, "", trackIds);
                }
            } else {
                if (null != onError) {
                    onError.operate(TYPE_BUY_BUNCH_TRACK, "");
                }
            }
        }
    }

    private static void checkBuyLeftAllTrackBroadCastInAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (null == intent
                || null == data
                || null == data.album) {
            return;
        }

        long albumId = intent.getLongExtra(
                BundleKeyConstants.KEY_ALBUM_ID, -1);

        if (albumId == data.album.getId()) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_BUY_LEFT_ALL_TRACK, "");
            }
        }
    }

    public static void checkFreeListenUnlockBroadCastInPlayFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (!FreeListenHostManager.getInstance().canUnlockWithTime()) {
            return;
        }
        PlayingSoundInfo playingSoundInfo;
        if (null == intent
                || null == data
                || null == (playingSoundInfo = data.playingSoundInfo)) {
            return;
        }

        long newPermissionExpireSecond = intent.getLongExtra(FreeListenConstant.BROAD_CAST_PARAM_NAME_UNLOCK_PERMISSION_EXPIRE_SECOND, 0L);
        long newLocalBaseTimeStamp = intent.getLongExtra(BundleKeyConstants.KEY_LOCAL_BASE_TIME_STAMP, 0);
        /*PlayableModel playableModel = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getCurrSound();
        if (null == playableModel || playableModel.getDataId() != (null == playingSoundInfo.trackInfo ? 0 : playingSoundInfo.trackInfo.trackId)) {
            return;
        }
        if (playableModel instanceof Track) {
            Track track = (Track) playableModel;
            List<UnlockModeInfo> unlockModeInfo = track.getUnlockModeInfos();
            if (!ToolUtil.isEmptyCollects(unlockModeInfo)) {
                Pair<Long, Long> unlockTimeInfo = null;
                if (0 < newPermissionExpireSecond && 0 < newLocalBaseTimeStamp) {
                    unlockTimeInfo = new Pair<>(newPermissionExpireSecond, newLocalBaseTimeStamp);
                }
                if (null != onSuccess) {
                    // 在回调中任选一个使用，避免成功信息被执行两次
                    onSuccess.operate(TYPE_FREE_LISTEN_UNLOCK_WITH_TIME, null);
                    onSuccess.operate(TYPE_FREE_LISTEN_UNLOCK_WITH_TIME, null, unlockTimeInfo);
                }
            }
        }*/
        Track track = playingSoundInfo.trackInfo2TrackM();
        if (null == track) {
            return;
        }
        if (track.isPaid() && track.isAuthorized() && !UnlockModeInfo.isMatchUnlockWithTimePermissionSource(track)) {
            return;
        }
        Pair<Long, Long> unlockTimeInfo = null;
        if (0 < newPermissionExpireSecond && 0 < newLocalBaseTimeStamp) {
            unlockTimeInfo = new Pair<>(newPermissionExpireSecond, newLocalBaseTimeStamp);
        }
        if (null != onSuccess) {
            // 在回调中任选一个使用，避免成功信息被执行两次
            onSuccess.operate(TYPE_FREE_LISTEN_UNLOCK_WITH_TIME, null);
            onSuccess.operate(TYPE_FREE_LISTEN_UNLOCK_WITH_TIME, null, unlockTimeInfo);
        }
    }

    public static void checkFreeListenUnlockBroadCastInAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (!FreeListenHostManager.getInstance().canUnlockWithTime()) {
            return;
        }
        if (null == intent
                || null == data
                || null == data.album) {
            return;
        }

        long newPermissionExpireSecond = intent.getLongExtra(FreeListenConstant.BROAD_CAST_PARAM_NAME_UNLOCK_PERMISSION_EXPIRE_SECOND, 0L);
        long newLocalBaseTimeStamp = intent.getLongExtra(BundleKeyConstants.KEY_LOCAL_BASE_TIME_STAMP, 0);
        AlbumM album = data.album;
        List<UnlockModeInfo> unlockModeInfo = album.getUnlockModeInfos();
        if (!ToolUtil.isEmptyCollects(unlockModeInfo)) {
            Pair<Long, Long> unlockTimeInfo = null;
            if (0 < newPermissionExpireSecond && 0 < newLocalBaseTimeStamp) {
                unlockTimeInfo = new Pair<>(newPermissionExpireSecond, newLocalBaseTimeStamp);
            }
            if (null != onSuccess) {
                // 在回调中任选一个使用，避免成功信息被执行两次
                onSuccess.operate(TYPE_FREE_LISTEN_UNLOCK_WITH_TIME, null);
                onSuccess.operate(TYPE_FREE_LISTEN_UNLOCK_WITH_TIME, null, unlockTimeInfo);
            }
        }
    }

    public static void checkFreeListenUnlockEndBroadCastInPlayFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (!FreeListenHostManager.getInstance().canUnlockWithTime()) {
            return;
        }
        if (null == intent
                || null == data
                || null == data.playingSoundInfo) {
            return;
        }

        PlayingSoundInfo playingSoundInfo = data.playingSoundInfo;
        PlayableModel playableModel = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getCurrSound();
        if (null == playableModel || playableModel.getDataId() != (null == playingSoundInfo.trackInfo ? 0 : playingSoundInfo.trackInfo.trackId)) {
            return;
        }
        if (playableModel instanceof Track) {
            Track track = (Track) playableModel;
            List<UnlockModeInfo> unlockModeInfo = track.getUnlockModeInfos();
            if (!ToolUtil.isEmptyCollects(unlockModeInfo) && UnlockModeInfo.isMatchUnlockWithTimePermissionSource(track)) {
                if (null != onSuccess) {
                    onSuccess.operate(TYPE_FREE_LISTEN_UNLOCK_WITH_TIME_END, null);
                }
            }
        }
    }

    public static void checkFreeListenUnlockEndBroadCastInAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (!FreeListenHostManager.getInstance().canUnlockWithTime()) {
            return;
        }
        if (null == intent
                || null == data
                || null == data.album) {
            return;
        }

        AlbumM album = data.album;
        List<UnlockModeInfo> unlockModeInfo = album.getUnlockModeInfos();
        if (!ToolUtil.isEmptyCollects(unlockModeInfo) || album.isShowVideoAdverts()) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_FREE_LISTEN_UNLOCK_WITH_TIME_END, null);
            }
        }
    }

    public static void checkFreeListenUnlockQuitBroadCastInPlayFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (!FreeListenHostManager.getInstance().canUnlockWithTime()) {
            return;
        }
        PlayingSoundInfo playingSoundInfo;
        if (null == intent
                || null == data
                || null == (playingSoundInfo = data.playingSoundInfo)) {
            return;
        }

        /*Track track = playingSoundInfo.trackInfo2TrackM();
        if (null == track) {
            return;
        }
        if (track.isPaid() && track.isAuthorized()) {
            return;
        }*/
        if (null != onSuccess) {
            onSuccess.operate(TYPE_FREE_LISTEN_UNLOCK_QUIT, null);
        }
    }

    public static void checkFreeListenUnlockQuitBroadCastInAlbumFragment(Intent intent, ResultData data, IOperation onSuccess, IOperation onError) {
        if (!FreeListenHostManager.getInstance().canUnlockWithTime()) {
            return;
        }
        if (null == intent
                || null == data
                || null == data.album) {
            return;
        }

        AlbumM album = data.album;
        List<UnlockModeInfo> unlockModeInfo = album.getUnlockModeInfos();
        if (!ToolUtil.isEmptyCollects(unlockModeInfo) || album.isShowVideoAdverts()) {
            if (null != onSuccess) {
                onSuccess.operate(TYPE_FREE_LISTEN_UNLOCK_QUIT, null);
            }
        }
    }

    /**
     * 通过请求接口判断专辑是否有权限
     */
    private static void checkAlbumAccessibleViaNet(long albumId, final int type, final IOperation onSuccess, final IOperation onError, final String successMsg, final String errorMsg) {
        Map<String, String> simpleParams = new HashMap<>();
        simpleParams.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId + "");
        CommonRequestM.getAlbumSimpleInfo(simpleParams, new IDataCallBack<AlbumM>() {
            @Override
            public void onSuccess(@Nullable AlbumM object) {
                if (null != object && object.isAuthorized()) {
                    if (null != onSuccess) {
                        onSuccess.operate(type, successMsg);
                    }
                } else {
                    if (null != onError) {
                        onError.operate(type, errorMsg);
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                if (null != onError) {
                    onError.operate(type, errorMsg);
                }
            }
        });
    }


    public static class ResultData {
        public static final int FRAGMENT_TYPE_PLAY = 1;
        public static final int FRAGMENT_TYPE_WHOLE_ALBUM = 2;
        public static final int FRAGMENT_TYPE_ALBUM_FRAGMENT = 3;
        public static final int FRAGMENT_TYPE_AD_UNLOCK = 4;

        private int mType;

        //// 需要的数据
        // 播放页
        public PlayingSoundInfo playingSoundInfo;
        // 售前、售后页
        public AlbumM album;
        // 广告
        public Track track;

        public ResultData(int type) {
            this.mType = type;
        }

        public int getType() {
            return mType;
        }
    }

    public interface IOperation {
        void operate(int type, String massage);

        default void operate(int type, String massage, Object dataInfo) {
        }
    }
}
