package com.ximalaya.ting.android.host.manager.ad;

import com.ximalaya.ting.android.adsdk.proxy.AdSdkConfigProxy;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

public class SoundAdConfigInitManager {

    public static void initTrackVentConfig(){
        final String CONFIG_TRACK_VENT_CONFIG = "trackVentConfig";
        final String CONFIG_START_INTERVAL = "startInterval";
        final String CONFIG_INTERVAL = "interval";
        final String config = AdSdkConfigProxy.getInstance().getString(CONFIG_TRACK_VENT_CONFIG, "");
        try {
            JSONObject jsonObject = new JSONObject(config);
            int startInterval = jsonObject.optInt(CONFIG_START_INTERVAL);
            int interval = jsonObject.optInt(CONFIG_INTERVAL);
            MMKVUtil.getInstance().saveInt(CONFIG_START_INTERVAL, startInterval);
            MMKVUtil.getInstance().saveInt(CONFIG_INTERVAL, interval);
            Logger.d("initTrackVentConfig", "startInterval = "+ startInterval +", interval" + interval);
        } catch (Exception e){
            e.printStackTrace();
            Logger.d("initTrackVentConfig", e.getMessage());
        }
    }
}
