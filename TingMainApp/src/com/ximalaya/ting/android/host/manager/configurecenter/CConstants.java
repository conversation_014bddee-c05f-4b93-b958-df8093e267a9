package com.ximalaya.ting.android.host.manager.configurecenter;

import com.ximalaya.ting.android.framework.util.AutoSizeUtil;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationPendingIntentCreateUtils;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.manager.CrossProcessTransferValueManager;
import com.ximalaya.ting.android.opensdk.push.PushNotificationFilterManager;
import com.ximalaya.ting.android.opensdk.util.ToListenUtil;

import java.util.Map;

/**
 * Created by luh<PERSON> on 2018/11/9.
 *
 * <AUTHOR>
 * Email: <EMAIL>
 * Tel:15918812121
 * <p>
 * 配置中心常量使用规范说明：
 * 1、原则上，配置中心常量都集中到这里管理，但各个模块（bundle） 可单独管理自己的常量，避免太多而不易阅读查找
 * 2、个模块单独管理时，组必须在这里申请接口，并集成这里的接口进行扩展（便于查看组所有的item）
 * 3、命名规范：
 * 组接口命名规则：Group+ 组名 + [模块名称（如果需要再自己的模块中实现的话，需继承此接口）]
 * 接口中的组名称：GROUP_NAME
 * item命名："ITEM_" + item名称 (全大写)，参见一些列子
 * 4、item 填写后，最好写上注释
 * </p>
 */
public interface CConstants {
    String[] ALL_GROUPS = new String[]{"fufei", "sys", "toc", "account", "live", "tob", "android", "mermaid", "ad", "community", "qiji", "apm", "local_ting", "XiaoyaOS", "myclub", "adSplashShake", "car", "kid"};

    /**
     * 付费组常量
     * 接口命名规则：Group+ 组名 + [模块名称（如果需要再自己的模块中实现的话，需继承此接口）]
     * 接口中的组名称：GROUP_NAME
     * 接口总item名称的命名规则：item + item名称 （大写）
     */
    interface Group_fufei {
        String GROUP_NAME = "fufei";
        String ITEM_IFSHOWWECHATACCOUNT = "IfShowWechatAccount";
        String ITEM_SUBSCRIBE_PIC = "Subscribe_Pic";
        String ITEM_BUYGIFTSTEXT = "BuyGiftsText";
        String ITEM_VIPTITLEICON = "VipTitleIcon";
        String ITEM_MY_123 = "my_123";
        String ITEM_VIPBUTTONVALUE = "vipbuttonvalue";
        String ITEM_TRANINGCAMPFINISHPAGE = "TraningCampFinishPage";
        String ITEM_GROUP_SUBSCRIBE = "group-subscribe";
        String ITEM_FOLLOWTHEWECHATACCOUNT = "FollowTheWechatAccount";
        String ITEM_AWARD = "award";
        String ITEM_BUYGIFTSURL = "BuyGiftsUrl";
        String ITEM_DISPLAY_PAYMENT_TEXT = "display_payment_text";
        String ITEM_VIPBUTTONTAGVALUE = "vipbuttontagvalue";
        String ITEM_ANDROID_BANNER_URL = "Android_banner_url";
        String ITEM_ISVIPBUTTON = "isvipbutton";
        String ITEM_ALLOWANCE = "allowance";
        String ITEM_NOADTAG = "noadtag";
        String ITEM_SHARETEXTOFCHECKIN = "ShareTextOfCheckIn";
        String ITEM_ANDROID_BANNER = "Android_banner";
        String ITEM_LISTENGUIDEENABLE = "listenGuideEnable";
        String ITEM_VOICESALETEXT = "voicesaletext";
        String ITEM_ISSHOWPRICE = "isShowPrice";
        String ITEM_GROUP_SUBSCRIBE_TOAST = "group-subscribe-toast";
        String ITEM_7DAYSREFUNDABLE_DISPLAY = "7DaysRefundable_display";
        String ITEM_ACCOUNTVIPBAR = "AccountVipBar";
        String ITEM_VIP_LIFECYCLE_TIPS = "vip-lifecycle-tips";
        String ITEM_ANDROID_BANNER_DISPLAY = "Android_banner_display";
        String ITEM_TRANINGCAMPTEXT = "TraningCampText";
        String ITEM_DKACTIVITYTIPS = "DKactivitytips";
        String ITEM_ACCOUNT_VIP_DEFAULT_RESOURCE = "accountVipDeafultResource"; // 账号页VIP条兜底资源
        String ITEM_AD_UNLOCK_TIP_ASSEMBLE = "ad_unlock_tips_assemble"; // 激励视频的引导内容物料
        String ITEM_FOLLOW_AD_FUNCTION_PARAM = "follow_ad_function_param";
        String ITEM_PLAY_PAGE_AD_ANCHOR_WINDOW_SWITCH = "play_page_ad_anchor_windoe_switch";
        String ITEM_VIP_RELATIVE_IMAGE_MATERIAL = "vip_relative_image_material";
        String ITEM_MASTER_VIP_PAYMENT_AGREEMENT_POP_MATERIAL = "payment_master_statement_pop_message";//大师课会员协议弹窗物料
    }

    interface Group_sys {
        String GROUP_NAME = "sys";
        String ITEM_SWITCH_OLD_YA_JS_INJECT = "switch_old_ya_js_inject";
        String ITEM_IGNORE_FQ_CTRL = "ignore_fq_ctrl";// 弹屏全屏视频是否受到凭空的开关
        String ITEM_DUB_SHOW_VIDEO_UPLOAD_DOMAIN = "dubshowVideoUploadDomain";// 趣配音上传domain
        String ITEM_VOICEANALYZERUPLOADDOMAIN = "voiceAnalyzerUploadDomain";
        String ITEM_CPUPLOADDOMAIN = "cpUploadDomain";
        String ITEM_PUSH_MESSAGE = "push_message";
        String ITEM_LOG_SWITCH = "log_switch";
        String ITEM_USE_NEW_DOG_PORTAL_SERVER = "use_new_dog_portal_server";
        String ITEM_DUBSHOWVIDEOUPLOADDOMAIN = "dubshowVideoUploadDomain";
        String ITEM_ALLOW_BLOCK_UPLOAD = "allow_block_upload";
        String ITEM_TRACKSIGNATUREUPLOADDOMAIN = "trackSignatureUploadDomain";
        String ITEM_HYBRID_INTERNAL_DOMAIN = "hybrid_internal_domain";
        String ITEM_FEED_LIST_FOLLOW_ANIMATION_TIME = "feed_follow_showtime";
        String ITEM_FIREWORK_SOUND_OFF = "firework_sound_off";// 弹屏静音时段控制开关
        String ITEM_FIREWORK_CHECK_LOG = "firework_check_log";// 广告弹屏日志校验开关
        String ITEM_FEED_VIDEO_TIME = "feed_video_time";// 发现页视频时长要求
        String ITEM_CREATE_DYNAMIC_VIDEO_TIME = "create_dynamic_video_time";// 发现页创件动态同步专辑对视频时长要求

        String ITEM_XMFLEXBOX_VERSION = "item_xmflexbox_version_control";// xmflexbox 打开版本
        String ITEM_HYBRID_XDCS_UPLOAD_LOG_FLAG = "item_hybrid_xdcs_upload_log_flag";// hybrid上报日志开关
        String ITEM_HYBRID_XDCS_FILE_KEEP_LOG_FLAG = "item_hybrid_xdcs_file_keep_log_flag";// hybrid本地写入开关
        String ITEM_XMFLEXBOX_FETCH_INTERVAL = "item_xmflexbox_fetch_interval";
        String ITEM_XMFLEXBOX_UPLOAD_ERROR_TOGGLE = "item_xmflexbox_upload_error_toggle";
        String ITEM_PRIVACY_RISK_COLLECT_TOGGLE = "item_privacy_risk_collect_toggle";
        String ITEM_COOKIE_CONTROLLER_TOGGLE = "item_cookie_controller_toggle";
        String ITEM_VOICE_SIGNATURE_HOST = "voice_signature_host"; // 声音签名上传host
        String ITEM_OPEN_HYBRID_PRE_REQUEST = "open_hybrid_pre_request"; // 與請求開關
        String ITEM_INNER_DEBUG_WHITE_LIST = "inner_debug_white_list"; // 内部调试白名单
        String ITEM_IOS_SAME_RECORD_OUTPUT_FORMAT = "ios_same_record_output_format"; // 内部调试白名单
        String ITEM_PLAY_REALTIME_STATISTICS = "playRealtimeStatistics"; // 播放进度实时统计
        String ITEM_PLAY_AND_PAUSE_STATISTICS = "playAndPauseStatistics"; // 播放开始暂停埋点，用于统计使用时长
        String ITEM_EXPOSE_PERCENT = "ubtExposePercent"; // 新版曝光方案曝光面积比例
        String ITEM_DUMP_STATE_TRACE = "dumpStateTrace"; // 日志回捞数据跟踪
        String ITEM_DUMP_BLOCK_SIZE = "dumpBlockSize"; // 日志回捞数据跟踪
        String ITEM_ENABLE_SYNC_DELAY_ON_FAIL = "enableSyncDelayOnFail"; //实时埋点请求失败延时累加开关

        String ITEM_IS_SEND_CLIENT_AB = "isSendClientAb";
        String ITEM_CLOSE_RECALL_ADVERTISING_FREE_FEATURE = "close_recall_advertising_free_feature";    // 是否关闭召回用户免广告功能,true表示关闭
    }

    interface Group_toc {
        int DEFAULT_0_PLAY_DIALOG_TIME = 120;

        String GROUP_NAME = "toc";

        String ITEM_HOME_RANK_CARD_MIN_COUNT = "home_rank_card_min_count";
        String ITEM_HOME_SCENE_CARD_MIN_COUNT = "home_scene_card_min_count";
        String ITEM_HOME_LIVE_CARD_MIN_COUNT = "home_live_card_min_count";
        String ITEM_HOME_818_KOL_LIVE_CARD_MIN_COUNT = "home_818_kol_live_card_min_count";
        String ITEM_HOME_VIDEO_LIVE_CARD_MIN_COUNT = "home_video_live_card_min_count";

        String ITEM_SETTING_BEIAN = "setting_beian";
        String ITEM_COMMON_DISLIKE_REASON_NEW = "common_dislike_reason_json_string";
        String ITEM_COMMON_FEED_BACK_REASON_JSON_STRING = "common_feed_back_reason_json_string";
        String ITEM_IMMERSIVE_PLAYPAGE_VIDEO_SCROLLABLE = "playpage_video_scrollable";//进入视频可滑动
        String ITEM_PLAY_AV_IMMERSIVE = "playpage_audio_video_immersive";
        String ITEM_PLAY_TIMBRE_FEEDBACK = "aivoice_questionnaire";//音色反馈卡片选项
        String ITEM_KEY_IMMERSION_ANIMATION = "immersionCovers_animation";//沉浸式背景图片是否有动画
        String ITEM_SETTING_NOTIFICATION = "Notice_copy";
        String ITEM_LOCK_PLAY = "Lock_screen_play";
        String ITEM_LOCK_SCREEN_GUIDE_IMG = "Lock_screen";
        String ITEM_SWITCH_PORTRAIT_PHOTO = "switch_portrait_photo";
        String ITEM_HOMEPAGE_FM_NAME = "homepage_fm_name";
        String ITEM_ONEKEY_DESKTOP_ICON = "fm_channel_logo_android"; // 私人FM 桌面图标
        String ITEM_SWITCH_PHOTO = "switch_photo";
        String ITEM_ALBUM_COMMENT_CONDITION = "album_comment_condition"; // 免费专辑评分条件是否保留，以后会去掉
        String ITEM_SEARCH_DISPLAY = "searchdisplay"; // 首页搜索框中是否显示搜索按钮
        String ITEM_0_PLAY_USER_GUIDE_MSG = "new_no_play";  // 0播放用户引导文案
        String ITEM_0_PLAY_DIALOG_TIME = "new_no_play_popup_time";  // 0播放弹窗出现时间
        String ITEM_KACHA_SUBTITLES_SWITCH = "kacha_subtitles_switch";  // 咔嚓入口开关
        String ITEM_KACHA_WATERMARK = "kacha_watermark";  // 咔嚓保存本地添加水印开关
        String ITEM_KACHA_TIME_LIMIT = "kacha_time_limit";  // 咔嚓截取声音时长
        String ITEM_SLEEP_AUTO_TIME_OFF_BACKGROUND = "baby_sleep_background"; // 哄睡播放页背景视频
        String ITEM_ALBUM_RATE_ACTIVITY_DATA = "comment_banner";    // 专辑评分活动位
        String ITEM_ZHUMIAN_ICON_SWITCH = "zmthlkg";// 助眠模式糖葫芦icon是否随内容变化
        String ITEM_BOTTOM_BULLET_SWITCH = "barrage_bottom";    // 底部弹幕开关
        String ITEM_BOTTOM_BARRAGE_COMMIT = "barrage_commit"; // 播放页弹幕打开时，底部评论区输入是否为弹幕，true为弹幕
        String ITEM_SWITCH_KACHA_VIDEO = "kacha_video_switch"; // 是否能打开视频裁剪
        String ITEM_OPEN_SOUND_NOTE_TAG = "kacha_default_page";
        String ITEM_ALBUM_OFF_SHELVES = "album_off_shelves";
        String ITEM_DACALLFXKG = "dacallfxkg";//打call分享开关
        String ITEM_DACALLGGKG = "dacallggkg";//打call广告开关
        String ITEM_DACALLTGGGKG = "dacalltgggkg";//打call视频跳过开关
        String ITEM_DACALLHLKG = "dacallhlkg";//打call瓜分好礼开关
        String ITEM_DACALLJFDZ = "dacalljfdz";//打call瓜分好礼跳转链接
        ////
        String ITEM_COMMENT_HEARLIST_SWITCH = "comment_hearlist_switch";
        String ITEM_GUESSYOULIKE_REFRESH_SWITCH = "guessyoulike_refresh_switch";
        String ITEM_SUBSCRIBE_CART_423 = "subscribe_cart_123";
        String ITEM_RECOMMENDPLAY = "recommendplay";
        String ITEM_NEW_NO_PLAY_POPUP_TIME = "new_no_play_popup_time";
        String ITEM_RANK_RULE_VALID_H5_URL = "item_rank_rule_valid_h5_url";
        String ITEM_SOUND_BALANCE = "sound-balance";
        String ITEM_BREAKCATEGORY = "breakcategory";
        String ITEM_NEW_GIFT_BAG_LIANJIE = "New-gift-bag-lianjie";
        String ITEM_ZHENGSHIDINGYUEGAIGUANZHUQUDAO = "zhengshidingyuegaiguanzhuqudao";
        String ITEM_HOME_ICON_TOUTIAO = "home_icon_toutiao";
        String ITEM_NEW_GIFT_BAG_GUANDIAO = "New-gift-bag-guandiao";
        String ITEM_LOOKFORUPDATE_START = "lookforupdate_start";
        String ITEM_BAOGUANGSWITCH = "baoguangswitch";
        String ITEM_SHAREICONMONEY = "shareIconMoney";
        String ITEM_TINGDANNUM = "tingdannum";
        String ITEM_RANKING_PIC = "ranking_pic";
        String ITEM_SOUNDBARCLICK = "soundbarclick";
        String ITEM_NEW_NO_PLAY = "new_no_play";
        String ITEM_DISCOVERY_DATING_UNREAD_COUNT = "discovery_dating_unread_count";
        String ITEM_COIN_ENTRANCE = "coin_entrance";
        String ITEM_RANKING_NEW = "ranking_new";
        String ITEM_KINGCARD = "kingcard";
        String ITEM_SHAREWORD = "shareWord";
        String ITEM_POP_ABLUM_VALUE = "pop_ablum_value";
        String ITEM_FM_NEWS_LIST = "fm_news_list"; // 私人FM是否显示新闻列表
        String ITEM_PLAYTASKENTRANCE = "playTaskEntrance";
        String ITEM_ZHENGSHIDINGYUEGAIGUANZHU = "zhengshidingyuegaiguanzhu";
        String ITEM_NEWS_DIY = "News_diy";
        String ITEM_POP_LIULIANG = "pop_liuliang";
        String ITEM_AD_SLEEP = "AD_sleep";
        String ITEM_SHAREBOARDLINK = "shareBoardLink";
        String ITEM_LISTENRED_SWITCH = "listenred_switch";
        String ITEM_SLEEP = "sleep";
        String ITEM_DEFAULTTDTYPE = "defaulttdtype";
        String ITEM_ADDPLAYLISTUI_INDISCOVERY = "addplaylistUI_indiscovery";
        String ITEM_CHANNALS_SUPPORT_REYUN_SDK = "channals_support_reyun_sdk";
        String ITEM_INVITE_URL = "invite_url";
        String ITEM_NEWUSERHOMEPAGE = "newUserHomepage";
        String ITEM_HOMEPAGE_NEW_UI = "homepage_new_ui";
        String ITEM_SWITCH_DISABLED_PEOPLE = "switch_disabled_people";
        String ITEM_TASKREWARD = "taskReward";
        String ITEM_SWITCH_ONE_CLICK_LOGIN_NEW = "switch_one_click_login_new";
        String ITEM_NO_SUBSCRIPTION_PAI = "No-subscription-pai";
        String ITEM_LISTEN_POSITION_ABC = "listen_position_abc";
        String ITEM_LOOKFORUPDATE_NUM = "lookforupdate_num";
        String ITEM_POPUP_CYCLE = "popup-cycle";
        String ITEM_SHARETIMES = "shareTimes";
        String ITEM_LOGIN_WAIT_TIME = "login-wait-time";
        String ITEM_POP_ABLUM_EVALUATE = "pop_ablum_evaluate";
        String ITEM_MEMBER_SWITCH = "member_switch";
        String ITEM_RANKING = "ranking";
        String ITEM_ALBUM_SIMILARTAB_NAMETEST = "album_similartab_nametest";
        String ITEM_ALBUM_POPUP_TIMES = "Album-popup-times";
        String ITEM_SUPERSCRIPT = "superscript";
        String ITEM_HEAR_REVISION = "hear_revision";
        String ITEM_NEW_GIFT_BAG = "New-gift-bag";
        String ITEM_TITLE_TIP = "title_tip";
        String ITEM_QIUHAOPINGHA = "qiuhaopingha";
        String ITEM_POINT_SLOGAN_02 = "point_slogan-02";
        String ITEM_POINT_SLOGAN_01 = "point_slogan-01";
        String ITEM_LASTTAB_TOP_SWITCH = "lasttab_top_switch";
        String ITEM_QIUHAOPINGHEIHA = "qiuhaopingheiha";
        String ITEM_POPUP_STYLE = "popup-style";
        String ITEM_XIAOYA_SPEAKER_COPYWRITING = "Xiaoya_speaker_copywriting";
        String ITEM_NEW_GIFT_BAG_TANCHUANG = "New-gift-bag-tanchuang";
        String ITEM_BARRAGE_BUTTON_TWINKLE = "barrage_button_twinkle";
        String ITEM_HARDWARE_MALL_URL = "hardware_mall_url";
        String ITEM_GZJPWA = "gzjpwa";
        String ITEM_FEED_WELFARE_ACTIVITY_TAB = "feed.welfare.activityContest.tab";
        String ITEM_ZMSQRK = "zmsqrk";
        String ITEM_NEWHOME_OPERATE_RECORD = "newhome_operation_record_name2";
        String ITEM_COIN_ENTRANCE_URL = "coin_entrance_url";
        String ITEM_MY_SLQJ_SWITCH = "my-slqj-switch";
        String ITEM_POINT_ICON = "point_icon";
        String ITEM_COPYRIGHT_PROTECTION = "copyright_protection";
        String ITEM_15DAYS_LIBAO = "15days_libao";
        String ITEM_TRACKUI_INDISCOVERY = "trackUI_indiscovery";
        String ITEM_SHARE_POSITION = "share-position";
        String ITEM_NEW_DINGTAB = "new_dingtab";
        String ITEM_SEARCH_ASR = "search_asr";
        String ITEM_LASTTAB_BUTTON_SWITCH = "lasttab_button_switch";
        String ITEM_ALBUM_ICON = "Album_Icon";
        String ITEM_AUTO_PLAY = "auto_play";
        String ITEM_INVITE_ICON_NEW2 = "invite_icon_new2";
        String ITEM_SUBTITLE_BUTTON_TWINKLE = "subtitle_button_twinkle";
        String ITEM_INVITE_ICON_NEW1 = "invite_icon_new1";
        String ITEM_SWITCH_FORCE_LOGIN = "switch_force_login";
        String ITEM_BENZHOUREPINGTOUTU = "benzhourepingtoutu";
        String ITEM_ZMTHLKG = "zmthlkg";
        String ITEM_SWITCH_PERMISSION = "switch_permission";
        String ITEM_ZHUANJIRED_SWITCH = "zhuanjired_switch";
        String ITEM_SWITCH_ONE_CLICK_LOGIN_CHUANG = "switch_one_click_login_chuang";
        String ITEM_TINGDANLIANJIE = "tingdanlianjie";
        String ITEM_XIAOYA_SPEAKER_BUTTON = "Xiaoya_speaker_button";
        String ITEM_BARRAGE_BOTTOM = "barrage_bottom";
        String ITEM_XIAOYA_SPEAKER_PICTURE = "Xiaoya_speaker_picture";
        String ITEM_KACHA_FONT_DEFAULT = "kacha_font_default";
        String ITEM_NEW_GIFT_BAG_YOUXIAJIAO = "New-gift-bag-youxiajiao";
        String ITEM_SHAREBANNERURL = "shareBannerUrl";
        String ITEM_SHORTCUTLISTENTIPS = "shortcutListenTips";
        String ITEM_ALBUM_SIMILAR_PHOTO = "album_similar_photo";
        String ITEM_SWITCH_ONE_CLICK_LOGIN = "switch_one_click_login";
        String ITEM_EXIT_APP_POPUP_SET = "exit_app_popup_set";
        String ITEM_15DAYS_LIANJIE = "15days_lianjie";
        String ITEM_SHARECANJUMP = "shareCanJump";
        String ITEM_TASKCENTERCONFIG = "taskCenterConfig";
        String ITEM_ALBUMTASKENTRANCE = "albumTaskEntrance";
        String ITEM_POPUP = "popup";
        String ITEM_SHAREBOARDPIC = "shareBoardPic";
        String ITEM_SEARCHDISPLAY = "searchdisplay";
        String ITEM_MY_ENTRANCE = "my-entrance";
        String ITEM_CLOCK_TITLE = "clock_title";
        String ITEM_DOWNLOAD_VIDEO = "download_video";
        String ITEM_SDK_LIMIT_SWITCH = "sdk_limit_switch";
        String ITEM_SHAREBOARDWECHAT = "shareBoardWechat";
        String ITEM_HISTORY_BANNER = "history_banner";
        String ITEM_TOUTIAOSEZHI = "toutiaosezhi";
        String ITEM_INVITE_SWITCH2 = "invite_switch2";
        String ITEM_NO_SUBSCRIPTIONS = "No-subscriptions";
        String ITEM_DESCRIPTION_TIP = "description_tip";
        String ITEM_BARRAGE_COMMIT = "barrage_commit";
        String ITEM_ERROR_TIPS = "error-tips";
        String ITEM_LHLWA = "lhlwa";
        String ITEM_CHANNALS_SUPPORT_FORCE_LOGIN = "channals_support_force_login";
        String ITEM_SHARESUCCESSTOAST = "shareSuccessToast";
        String ITEM_KACHA_SWITCH_48 = "kacha_switch_48";
        String ITEM_SEARCH_SHARE = "search_share";
        String ITEM_FORWARDUNRECOGNIZEDSELECTOR = "forwardUnrecognizedSelector";
        String ITEM_SLEEP_MODE = "sleep_mode";
        String ITEM_SEARCH_PAGE = "search_page";
        String ITEM_SEARCH_TIPS = "search-tips";
        String ITEM_SEARCH_VOICE = "search_voice";
        String ITEM_USE_NEWCONNECTION = "use_NewConnection";
        String ITEM_SHAREBANNER_SWITCH = "sharebanner_switch";
        String ITEM_SEARCH_SELECT_RANK = "search_select_rank";

        String ITEM_FREE_LISTEN_TAB_DISLIKE_CONFIG = "item_free_listen_tab_dislike_config";
        String ITEM_COMMENT_BANNER = "comment_banner";
        String ITEM_BREAKAGE = "breakage";
        String ITEM_RECOMMENDPLAY2 = "recommendplay2";
        String ITEM_SHAREBOARDWORD = "shareBoardWord";
        String ITEM_GIFT_DISABLED_PEOPLE = "gift_disabled_people";
        String ITEM_SOUNDS_FEED_TIPS = "sounds_feed_tips";
        String ITEM_SWITCH_LOGIN_NEW_USER_GIFT = "switch_login_new_user_gift";
        String ITEM_GUESS_U_LIKE_TITLE = "guess_u_like_title";
        String ITEM_BABY_SLEEP_BACKGROUND = "baby_sleep_background";
        String ITEM_NICKNAME_MODIFY = "nickname_modify";
        String ITEM_NEWHOME_OPERATE = "newhome_operate";
        String ITEM_MOBILE_TRAFFIC_URL = "mobile_traffic_url";
        String ITEM_SWITCHXIAOYA = "SwitchXiaoya";
        String ITEM_INVITE_URL_NEW1 = "invite_url_new1";
        String ITEM_COINSWITCH2 = "coinSwitch2";
        String ITEM_SHARE_ICON_URL = "share_icon_url";
        String ITEM_COINSWITCH1 = "coinSwitch1";
        String ITEM_CREDITS_LABEL_SWITCH = "credits_label_switch";
        String ITEM_FEEDALBUMPLAY = "feedalbumplay";
        String ITEM_CREDITS_LABEL_SLOGAN = "credits_label_slogan";
        String ITEM_CPM_PASSWORD_SWITCH = "cpm_password_switch";
        String ITEM_COINLINK = "coinLink";
        String ITEM_DAILYLISTEN_PLAY_ORDER = "dailylisten_play_order";
        String ITEM_INVITE_URL_NEW2 = "invite_url_new2";
        String ITEM_POPUP_SUBTITLE = "popup-Subtitle";
        String ITEM_SHAREICONGIFT = "shareIconGift";
        String ITEM_HOMERESTART = "homerestart";
        String ITEM_MINE_JINGCAISHENGHUO = "mine_jingcaishenghuo";
        String ITEM_LHLWAXS = "lhlwaxs";
        String ITEM_SEMAPK_TAN1 = "Semapk_tan1";
        String ITEM_PAIDTAG = "paidtag";
        String ITEM_SHARE_ICON_SWITCH = "share_icon_switch";
        String ITEM_SEMAPK_TAN2 = "Semapk_tan2";
        String ITEM_NEWUSER_0PLAY_TIME = "newUser_0play_time";
        String ITEM_TINGDANHUODON = "tingdanhuodon";
        String ITEM_CAR_CHANNELID = "car_channelid";
        String ITEM_COMMENT_PUSH = "comment_push";
        String ITEM_COMMENT_MESSAGE = "comment_message";
        String ITEM_SHAREBOARDMONEY = "shareBoardMoney";
        String ITEM_TASKLASTENTRANCE = "taskLastEntrance";
        String ITEM_LOOKFORUPDATE_END = "lookforupdate_end";
        String ITEM_SHAREBOARDPOSTER = "shareBoardPoster";
        String ITEM_15DAYS_TINGH5 = "15days_tingh5";
        String ITEM_TAPRETURN = "TapReturn";
        String ITEM_SUBNEWUSER = "subnewuser";
        String ITEM_GUESSYOULIKEYINDAO = "guessyoulikeyindao";
        String ITEM_KACHA_NEW_YEAR2019 = "kacha_new_year2019";
        String ITEM_NO_SUBSCRIPTION_PAIHANGBANG = "No-subscription-paihangbang";
        String ITEM_SHARE_ICON = "share_icon";
        String ITEM_LOCATION_SWITCH = "location_switch";
        String ITEM_SHAREICONALBUM = "shareIconAlbum";
        String ITEM_FORCE_LOGIN_SUBSCRIBE = "force_login_subscribe";
        String ITEM_INVITE_ICON = "invite_icon";
        String ITEM_RECOMAPP_SWITCH = "recomApp-switch";
        String ITEM_SHARESUCCESSWORD = "shareSuccessWord";
        String ITEM_JUMPRANK = "jumpRank";
        String ITEM_HEAR_SOUNDS_FEED = "hear_sounds_feed";
        String ITEM_SLOGAN = "slogan";
        String ITEM_MINE_VIP_NOLOGIN = "mine-vip-nologin";
        String ITEM_SHAREJUMPURL = "shareJumpUrl";
        String ITEM_JUMPALBUMFIRSTTRACK = "jumpAlbumFirstTrack";
        String ITEM_BUSINESS_PROMOTION = "business_promotion";
        String ITEM_BUSINESS_PROMOTION_NEW = "business_promotion_new"; // 账号页运营位新版配置（8.0后支持5个）
        String ITEM_BUSINESS_TITLE = "business_title";//账号页活动模块标题
        String ITEM_COMMENTSUCCESSTOAST = "commentSuccessToast";
        String ITEM_PRIVACYTIPS = "PrivacyTips";
        String ITEM_HOMEPAGE_AB = "homepage_ab";
        String ITEM_NEWTASKCENTER = "newTaskCenter";
        String ITEM_INVITE_SWITCH = "invite_switch";
        String ITEM_HOMETASKENTRANCE = "homeTaskEntrance";
        String ITEM_ALBUM_TAB_READBOOK = "album_tab_readBook";
        String ITEM_XIAOYA_SPEAKER_VOLUME = "xiaoya_speaker_volume";
        String ITEM_SHAREBANNER_URL = "sharebanner_url";
        String ITEM_DANGEROUS_COMMENT_SWITCH = "dangerous-comment-switch";
        String ITEM_SHARESHOWWECHAT = "shareShowWechat";
        String ITEM_SEARCH_SELECT = "search_select";
        String ITEM_LOGIN_NEW_USER_GIFT = "login_new_user_gift";
        String ITEM_COMMENT_INTRODUCER = "comment_introducer";
        String ITEM_MINETASKENTRANCE = "mineTaskEntrance";
        String ITEM_GOODTAG = "goodtag";
        String ITEM_QR_CODE_URL = "QR_code_url";
        String ITEM_SHAREBOARDMOMENT = "shareBoardMoment";
        String ITEM_KUAIBAOTIAOICON = "kuaibaotiaoicon";
        String ITEM_SWITCH_NONE_ABLUM_EVALUATE = "switch_none_ablum_evaluate";
        String ITEM_BANKING_HIDE = "banking_hide";
        String ITEM_SHAREBANNERDARK = "shareBannerDark";
        String ITEM_SEARCH_XIAOYA = "search_xiaoya";
        String ITEM_ALBUM_POPUP = "Album-popup";
        String ITEM_BENZHOUREPING = "benzhoureping";
        String ITEM_SHAREICONTOP = "shareIconTop";
        String ITEM_SUBTITLE_AD_DISPLAY = "subtitle_ad_display";
        String ITEM_SHAREREFRESH = "shareRefresh";
        String ITEM_SHAREFIRSTTIME = "shareFirstTime";
        String ITEM_SET_TQ_XIAOYA = "set_tq_xiaoya";
        String ITEM_PLAY_PAGE_8_0 = "play_page_80";  // 播放页8.0版本ab
        String ITEM_NEW_LISTEN_NOTE_PAGE_AB = "tingdannew";
        String ITEM_NEW_LISTEN_NOTE_PAGE_TOP_BG_URL = "listen-background";
        String ITEM_KACHA_AUDIO_LIMIT = "kacha_audio_limit";
        String ITEM_KACHA_VIDEO_SHOT = "kacha_video_shot";
        String ITEM_ASR_UPLOAD = "asr_upload";
        String ITEM_READER_SWITCH = "reader_switch";
        String ITEM_LOGIN_GUIDING = "login_guiding";
        String ITEM_SHARE_CATEGORY = "shareCategory";//分类id，分享声音到微信，在该分类列表里，以小程序形式分享
        String LOGIN_GUIDING_HOMEPAGE = "login_guiding_homepage";   // 首页底部登录引导条，true表示展示，false表示不展示，默认false
        String ITEM_IF_PICTURE = "if_picture";
        String ITEM_ACCOUNT_CS = "account_cs";
        String ITEM_SHARE_HONOR = "shareHonor";//专辑页是否显示成就入口
        String ITEM_SHARE_HONOR_WORD = "shareHonorWord";//成就分享按钮文案
        String ITEM_SHARE_PIC = "sharePic";//儿童品类分享背景url
        String ITEM_TASK_SIGN_WORD = "taskSignWord";//首页签到入口文案
        String ITEM_FM_CHAT_NAME = "fm_chat_name"; // 私人FM设置-交流圈文案
        String ITEM_FM_CHAT_ITING = "fm_chat_iting"; // 私人FM设置-交流圈跳转url
        String ITEM_SHARE_SUCCESS = "shareSuccess";//分享成功lottie文案
        String ITEM_XIAOYA_HOME = "Xiaoya_Home";//是否使用新的小雅等级系统
        String ITEM_XIAOYA_NEW_LEVEL = "xiaoyaNewLevel";//小雅等级页面url
        String ITEM_SHARE_HOME_PAGE = "shareHomePage";//分享用户面板标题
        String ITEM_HIGHLIGHTS_SHARE = "highlights_share";//精彩片段分享
        String ITEM_HIGHLIGHTS_PRODUCE_HINT = "highlights_guidance";//精彩片段制作提示
        String ITEM_HIGHLIGHTS_PRODUCE_GUIDE_URL = "highlights_guidance_url";//精彩片段规则与教程链接
        String ITEM_COPY_RIGHT_RELATION_LABEL = "copyright_relation_label";
        String ITEM_SHARE_PLAY_HONOR = "sharePlayHonor";//播放页是否请求专辑收听时长
        String ITEM_PLAYPAGE_LIVE_SWITCH = "playpage_live_switch"; // 播放页右上角直播入口开关
        String ITEM_TASK_HOMEPAGE = "taskHomepage"; //推荐页签到入口开关
        String ITEM_SHARE_WEIXINQUN = "share_weixinqun";//新版分享面板微信群渠道开关
        String ITEM_SHARE_INTERFACE_TIME = "shareInterfaceTime";//播放页分享数据请求间隔
        String ITEM_SHARE_HIGHLIGHTS_DESCRIPTION = "share_highlights_description";//分享卡片精彩片段文案
        String ITEM_SWITCH_SHARE_PANEL = "switch_share_panel";//播放页新旧分享面板配置开关
        String ITEM_SPIC_AD_PAUSE_DISPLAY = "spic_ad_pause_display";  // 播放页暂停时资源位自动展开的时间间隔， 0不展开，大于0的整数，表示允许展开的最小时间间隔，单位秒
        String ITEM_TAG_JUMP = "tagjump"; // 专辑页和播放页的标签是跳标签频道落地页，还是跳分类全部页
        String ITEM_ACCOUNT_CHANGE = "account_change"; //模式切换各个模式的介绍
        String ITEM_PLAY_PAGE_AD_BAR_NP = "play_page_ad_bar_np"; // 播放页缩略小图广告等待时间_无大图情况
        String ITEM_PLAY_PAGE_AD_BAR_HP = "play_page_ad_bar_hp"; // 播放页缩略小图广告等待时间_有大图情况
        String ITEM_YQT_EMPTY_BUTTON = "yqt_empty_button"; // 播放页一起听入口按钮文案，没有房间
        String ITEM_YQT_ANCHOR_BUTTON = "yqt_anchor_button"; // 播放页一起听入口按钮文案，主播在房间内
        String ITEM_YQT_LISTENING_BUTTON = "yqt_listening_button"; // 播放页一起听入口按钮文案，有房间
        String ITEM_TASK_ENTRANCE_AB = "taskEntranceAB"; //账号页积分入口背景AB
        String ITEM_HOMETODAYREDIAN = "hometodayredian"; // 首页今日推荐热点
        String ITEM_HOMETODAYBITING = "hometodaybiting"; // 首页今日推荐必听
        String ITEM_HOMETODAYDIANTAI = "hometodaydiantai"; // 首页今日推荐电台
        String ITEM_HOMETODAYXINPIN = "hometodayxinpin"; // 首页今日推荐新品
        String ITEM_TJHYTCPC = "tjhytcpc"; // 推荐朋友弹窗频次
        String ITEM_TJHYTCJGN = "tjhytcjgn"; // 推荐好友弹窗间隔天数
        String ITEM_SYQMTP = "syqmtp"; // 声音签名录制页背景图
        String ITEM_GZHYTC = "gzhytc";//是否展示推荐好友弹窗
        String ITEM_LISTEN_EVERYDAY_UPDATE_SET = "my_listen_update_list_set";
        String ITEM_NEWS_NOTE = "newsNote";
        String ITEM_LOCKSCREEN_BG_PICS = "lockscreen_bg_pics";
        String ITEM_SUBSCRIBE_REC_FREQUENCY = "subscribe_rec_frequency";
        String ITEM_ACCOUNT_TAB_ANCHOR_LEVEL = "account_tab_anchor_level";//账号页面默认跳转创作顶tab状态所需主播等级
        String ITEM_CREATION_PAGE_DEFAULT_PICTURE = "Creation_Page_Default_Picture";//创作页面未登录状态缺省图
        String ITEM_MESSAGE_PAGE_DEFAULT_PICTURE = "Message_Page_default_Picture";//消息页面未登录状态缺省图
        String ITEM_TASK_MINE_SIGN = "taskMineSign";//签到弹窗是否展示激励视频入口
        String ITEM_TASK_AID = "taskAid"; // 我的页面任务列表aid
        String ITEM_TASK_MINE_VIDEO = "taskMineVideo"; // 我的页面是否展示看视频任务
        String ITEM_TASK_VIDEO_SLOT_ID = "taskVideoSlotID"; // 我的页面激励视频slotID
        String ITEM_TASK_CENTER_URL = "taskCenterUrl"; // 任务中心跳转链接
        String ITEM_TASK_CENTER_LISTEN = "taskCenterlisten"; // 任务中心跳转链接
        String ITEM_MEDAL_RULE = "medal_rule"; // 收听成就勋章规则页
        String ITEM_SHARE_FAMILY_VIP = "shareFamilyVip"; // 赠送Vip配置
        String ITEM_RAISE_SHOW_CHANNEL_TIME = "qhtqdsj"; // 求好评渠道时间
        String ITEM_AD_UNLOCK_VIP_TIPS_AUDITION = "ad_unlock_vip_tips_audition"; // 有试听的短视频开通会员声音贴片
        String ITEM_AD_UNLOCK_VIP_TIPS = "ad_unlock_vip_tips"; // 无试听的短视频开通会员声音贴片
        String ITEM_LIVE_TIPS_SHOWTIME = "live_tips_showtime"; // 播放页直播入口tips展示时长，单位秒
        String ITEM_CLOSE_LIVE_TIME = "close_live_time"; // 播放页直播入口关闭后屏蔽时长，单位分钟
        String ITEM_MCGRYGQKG = "mcgrygqkg"; // 个人页MyClub标识
        String ITEM_GIVE_FRIEND_VIP = "giveFriendsVip"; // 赠送VIP海报链接
        String ITEM_SHARE_TYPE = "shareType"; // 播放页分享面板类型
        String ITEM_SIGN_AID = "signAid"; // 账号页0积分弹窗签到活动id
        String ITEM_SHARE_PLC_GUIDE = "guideShareBtnDesc"; // p拉c分享提示
        String ITEM_GIVE_FRIEND_VIP_TIPS = "giveFriendVipTips"; // 赠送VIPTips是否展示
        String ITEM_NICK_WINDOW_FREQUENCY = "nick_window_frequency"; // 昵称完善弹窗频次
        String ITEM_ACCOUNT_FEED_FULI = "account-feed-fuli"; // 账号页积分福利标题
        String ITEM_NEW_COMMENT = "nplys"; // 评论的 ab test
        String ITEM_AI_KACHA_PIC = "ai_kacha_pic"; // Ai文稿分享文字视频封面
        String ITEM_NEW_INSTALL_FETCH_APPLIST = "new_install_fetch_applist"; // 新安装用户是否可以获取应用列表
        String ITEM_ACCOUNT_FEED_TITLE = "account_feed_title"; // 账号页浏览积分任务标题
        String ITEM_FRONTPAGE_IMAGE = "news_frontpage_background_pic"; // 首页热点模块
        String ITEM_COMMENT_LINK_LIMIT = "comment_link_limit"; // 评论的最大链接数
        String ITEM_SHOW_LIVE_LISTEN_USER_INFO = "yqt_zlk"; // 是否校验一起听资料卡
        String ITEM_GRYTTSBMD = "gryttsbmd"; // tts白名单
        String ITEM_FREE_FLOW_DIALOG = "freeFlowDialog";    // 免流弹窗配置
        String ITEM_HOME_PAGE_HAS_MORE = "9_mt_ab_more"; // 首页右上角更多按钮
        String ITEM_USE_NEW_BOTTOM_TAB = "9_mt_ab_vipdown"; // 使用新版底tab
        String ITEM_SHOW_9_MT_HOME_GUIDE = "9_mt_ab_homeguide"; // 9.0首页引导+我页引导
        String ITEM_SHOW_9_MT_VIP_GUIDE = "9_mt_ab_vipguide"; // 9.0vip页引导
        String ITEM_KACHA_NOTE_TIME_LIMIT = "kacha_note_time_limit";  // 咔嚓笔记截取声音时长
        String ITEM_KACHA_AI_SWITCH = "ai_kacha_switch";  // 咔嚓笔记截取声音时长
        String ITEM_SPRING_SCREEN_TIME = "springscreentime2"; // 青少年模式弹窗弹完之后，当前页面一定时间内不弹统一弹屏
        String ITEM_WALLET_NEW_FRAGMENT = "my_walletpage_v1.1"; //钱包页AB
        String ITEM_RECOMMEND_PAGE_AA = "ab_first_page_aa"; // 首页-推荐aa实验
        String ITEM_DISCOVERY_PAGE_AA = "ab_discovery_page_aa"; // 发现页aa实验
        String ITEM_HOME_MORE_TAG = "homemoretag"; // 首页加号气泡
        String ITEM_MY_MORE_TAG = "mymoretag"; // 我的页全部服务气泡
        String ITEM_NEW_SHARE_ICON = "newShareIcon"; // 声音页分享入口动画
        String ITEM_SHARE_PANNEL = "SharePannel"; //海报分享面版ab
        String ITEM_TRACK_SHARE_PANNEL = "voice_share_ab"; // 声音页全屏分享面板AB
        String ITEM_ALBUM_SHARE_PANNEL = "album_share_ab"; //专辑分享ab
        String ITEM_SOUND_PLAY_PAGE_SHARE = "sound_play_page_share";// 声音分享新面版ab
        String ITEM_SHARE_VIP = "shareVip"; // 播放页分享vip通知配置
        String ITEM_READING_MILESTONE = "readingMilestone"; // 收听里程碑
        String ITEM_MILE_STONE_APP_NOTICE = "mileStoneAppNotice"; // 收听里程碑通知配置
        String ITEM_USE_NEW_RECOMMEND_FRAGMENT = "home_biserial_feeds_correcting"; // 使用新双列首页
        String ITEM_PLAYPAGE_DATA_PRASE_USE_TYPE_ADAPTER = "playpage_data_prase_use_type_adapter"; // 播放页数据解析使用typeadapter
        String ITEM_PLAY_PAGE_PRE_INIT_AUDIO_TAB = "play_page_pre_init_audio_tab"; // 播放页提前初始化声音tab
        String ITEM_SWTICH_SETTING_MYDETAIL = "swtich_setting_mydetail";//设置个人信息收集
        String ITEM_TEENAGER_LAUNCH_AB = "teenager_launch_ab"; // 青少年弹窗出现时间ab
        String ITEM_DEEPLINK_RETRY_MAX_WAIT_TIME_SECOND = "deeplink_retry_max_wait_time_second"; // deeplink没有匹配重发最大等待的时间间隔
        String ITEM_PLAY_ANCHOR_AND_AD_ENTRA_CONFIG = "playAnchorAndAdEntraConfig"; // 播放页主播带货和广告入口配置
        String ITEM_KACHA_DEFAULT_CLIP_LEFT = "kacha_default_clip_left"; // 咔嚓向前剪辑时间
        String HOME_PAGE_H5_DELAY_INIT = "home_page_h5_delay_init"; // 首页h5tab页是否要延迟初始化
        String MINE_BG_IMG_LIST = "grymrtt"; // 个人页背景图弹窗默认图
        String ITEM_RECOMMEND_FRAGMENT_MODE = "home_biserial_feeds_correcting_v4"; // 首页模式
        String ITEM_PLAYPAGE_SUBSCRIBE_SWITCH = "playpage_subscribe_switch"; // 播放页面展示订阅开关。配置项为专辑分类。配置中的专辑分类在新声音播放页先展示订阅，订阅后再展示关注主播。
        String ITEM_SCREEN_TIME_CONTROLLER = "screen_time_controller"; // 播放页开屏时长控制
        String ITEM_RECOMMEND_REALTIME_REQUEST_LIMIT = "RealtimeUpperLimit"; // 单个专辑实时推荐请求触发次数
        String ITEM_RECOMMEND_REALTIME_REQUEST_TRIGGER_TIME = "RealtimeThreshold"; // 实时推荐请求触发时长
        String ITEM_RECOMMEND_REALTIME_GROUP_NAME = "RtGroupName"; // 实时推荐实验 old new
        String ITEM_TALENT_LOGO_JUMP_URL = "skip_super_listener_url"; // 超级听友跳转地址
        String ITEM_RECOMMEND_STAGGERED_SPECIAL_NEW_KEY = "home_feeds_stream_playlist_ab"; // 混排听单样式新ab
        String ITEM_ALBUM_REALTIME_RECOMMEND = "album_realtime_recommend"; // 实时推荐条数
        String ITEM_KID_VIP_RESOURCE = "kidVipResource"; // 混排听单ab
        String ITEM_MIX_FOCUS_HEAD_LINE_STYLE = "news_front_focus_more_button"; // 双列焦点图，头条样式
        String ITEM_INDEX_HOTSPOT_TITLE_B = "index_hotspot_title_b"; // 热点ui相关文案配置
        String ITEM_INDEX_HOTSPOT_TITLE_C = "index_hotspot_title_c"; // 热点ui相关文案配置
        String ITEM_FULL_SCREEN_TEXT_SWITCH = "full_screen_text_switch"; // AI文稿全屏开关配置
        String ITEM_ANI_NEWS_ICON = "ani_news_icon";  // 热点的icon
        String ITEM_DYNAMIC_HOT_TAB_INTERVAL_TIME = "DynamicHotTabIntervalTime"; // 动态小红点最小间隔时间-单位s
        String ITEM_NEWS_INTERFACE_CHANGE_CONTROLLER = "news_interface_change_controller";
        String ITEM_COMMUNITY_LOTTERY_AGREEMENT = "community_lottery_agreement";   // 动态抽奖法务问题
        String ITEM_PLAYPAGE_AI_TAG_DESCRIBE = "playpage_ai_tag_describe"; // 播放页ai文稿提示弹窗文案
        String ITEM_INTERACTION_WELCOME = "interaction_welcome"; // 互动开屏弹幕欢迎语

        // 双排控制同一时间x行出y个
        String ITEM_DOUBLE_SQUARE_LABEL_LINES_TIMES = "double_square_label_lines_times";
        // 单排糖葫芦情况出y个
        String ITEM_SINGLE_SQUARE_LABEL_TIMES = "single_square_label_times";
        String ITEM_SQUARE_LABEL_CD_TIME = "square_label_cd";
        String ITEM_SQUARE_FREQUENCY_DAILY = "square_frequency_daily";
        String ITEM_HOME_GUID_SUBSCRIBE = "home_guid_subscribe";   // 首页引导订阅
        String ITEM_HOME_PAGE_OTHER_TAB_DELAY_CREATE = "home_page_other_tab_delay_create"; // 首页其他tab延迟创建
        String ITEM_PLAY_PAGE_OTHER_TAB_DELAY_CREATE = "play_page_other_tab_delay_create"; // 播放页其他tab延迟创建
        String ITEM_DISABLE_LIVE_HOME_PAGE_TRACE = "disable_live_home_page_trace"; // 禁止直播首页上报页面加载耗时

        String ITEM_BARRAGE_PAGE_SIZE = "barragePageSize";  // 互动播放页弹幕每页加载的个数
        String ITEM_RECOMMEND_FRA_DARK_MODE = "nightmode"; //老版首页暗黑模式ab
        String ITEM_PLAYLET_LIST = "playlet_list"; // 默认进视频播放页专辑配置
        String ITEM_URL_COOKIE_VERIFY = "item_url_cookie_verify_level"; // url审核模式
        String ITEM_COMMENT_HATE_AB = "comment_hate_ab";    //评论带不带踩功能，值为"old"不带，"new"带
        String ITEM_COMMENT_HOTTEST_SORT = "comment_hottest_sort";  //声音评论按照最热/最新排序，还是推荐/最新排序，前者为new，后者为old
        String ITEM_PLAY_PAGE_ANIMATION_NEED_MANAGE = "play_page_animation_need_manage"; // 播放页动效排序开关，做个保底，有问题及关掉
        String ITEM_DIRECTIONAL_CHANNEL_ID = "directional_channel_id"; // 定投渠道id
        String ITEM_DIRECTIONAL_CHANNEL_NAME = "directional_channel_name"; // 定投渠道名称
        String ITEM_HISTORY_PLAY_PODCAST_WAIT_TIME_IN_MILLIS = "item_history_play_podcast_wait_time_in_millis"; // 历史页播客类型查询兜底时间

        String ITEM_AIGC_SOUND_PATCH_CONFIG = "aigc_soundPatch_config";

        String ITEM_HOME_AUTO_SCROLL_MAX_NUMBER = "home_auto_scroll_max_number";
        String ITEM_XPNS_PAGE_BLACK_LIST = "item_xpns_page_black_list";
        String ITEM_TO_LISTEN_QUESTIONNAIRE_URL = "to_listen_questionnaire_url"; // 待播问卷地址
        String ITEM_OPEN_PLAY_WHILE_PAUSE = "item_open_play_while_pause"; // 新版肚脐眼播放状态下点击暂停按钮是否进播放页
        String ITEM_REPORT_ALBUM_TYPE_CONFIG = "item_report_album_type_config"; // 专辑举报原因

        String ITEM_ANDROID_HYBRID_INVISIBLE_THRESHOLD = "item_android_hybrid_invisible_threshold"; // h5白屏监控懒加载相关阈值开关
        String ITEM_INNER_PUSH_OPERATION_RULES_CONFIG = "item_inner_push_operation_rules_config"; // 内push收听及操作频控拦截规则
        String ITEM_ALBUM_LABEL_PAGE_SIZE_CONFIG = "album_label_page_size_config";

        String ITEM_TO_LISTEN_DAILY_NEWS_OPEN_STATUS = "to_listen_daily_news_open_status";
        String ITEM_ALBUM_SCORE = "albumscore"; //专辑评分结构化
        String ITEM_FOLLOW_PAGE_USER_NEW = "follow_page_user_new"; // 动态关注流是否使用新的页面
        String ITEM_LIKE_ANIMATION_GUIDE = "playingPraiseAnimationGuideText"; // 连击点赞动效引导配置文案
        String ITEM_TEXT_NEGATIVE_FEEDBACK_SWITCH = "ai_text_negative_feedback_switch";
        String ITEM_TEXT_NEGATIVE_FEEDBACK_TIME = "ai_text_negative_feedback_time";
        String ITEM_TEXT_NEGATIVE_FEEDBACK_DISAPPEAR = "ai_text_negative_feedback_disappear";
        String ITEM_TEXT_NEGATIVE_FEEDBACK_CLOSE = "ai_text_negative_feedback_close";
        String ITEM_TEXT_NEGATIVE_RANDOM_APPEAR = "ai_text_negative_feedback_random_appear";
        String ITEM_TRACK_HOT_COMMENTS_GUIDE_TEXTS = "excellent_comment_on_new_palypage";
        String ITEM_TRACK_HOT_COMMENTS_EMPTY_GUIDE_TEXT = "anchor_guide_comments";
        String ITEM_TRACK_HOT_COMMENTS_OPERATION_GUIDE_TEXT = "operation_guide_comments";
        String ITEM_TRACK_HOT_COMMENTS_VIEW_MORE_TEXT_WHEN_COUNT_NOT_ZERO = "view_more_nonnull";
        String ITEM_TRACK_HOT_COMMENTS_VIEW_MORE_TEXT_WHEN_COUNT_IS_ZERO = "view_more_zero";
        String KEY_AUTO_SIZE_CONFIG_OPEN = AutoSizeUtil.KEY_AUTO_SIZE_CONFIG_OPEN;
        String ITEM_PLAYING_VOTE_NUM_MIN_VALUE = "playing_vote_number_min_value";
        String ITEM_MAX_INTERACTIVE_CARD_SHOW_NUM = "max_interactive_card_show_num";    // 互动卡片最大的展示次数
        String ITEM_PLAY_PAGE_SHOW_TIPS = "play_page_show_tips";
        String ITEM_MONTHLY_TICKET_TOP_BANNER = "monthlyticket_topbanner";
        String ITEM_CALENDAR_OPERATION_OPEN = "calendar_operation_open";
        String ITEM_INTERACTIVE_BOX_AUTO_HIDE_TIME = "interactive_box_auto_hide_time";  // 互动box自动隐藏时间-秒
        String ITEM_FREE_UNLOCK_SUCCESS_SOUND_PATCH = "free_auto_unlock_success_sound_patch";
        String ITEM_LIKE_GUIDE_BOX_SWITCH = "like_guide_box_switch";
        String ITEM_YELLOW_BAR_AD_OPEN = "player_ad_banner_switch";// 播放页小黄条广告开关
        String ITEM_BAOHUO_PLAYER_UPDATE_1 = "baohuo_player_update_1";   // 保活播放器_更新频率_未转化时长
        String ITEM_BAOHUO_PLAYER_UPDATE_2 = "baohuo_player_update_2";   // 保活播放器_更新频率_推荐内容
        String ITEM_BAOHUO_PLAYER_RECOMMEND_CONTENT = "baohuo_player_recommend_content";   // 保活播放器_推荐内容开关
        String ITEM_BAOHUO_PLAYER_RECOMMEND_CONTENT_HARMONYOS = "baohuo_player_recommend_content_HarmonyOS";   // 保活播放器_推荐内容开关_鸿蒙系统
        String ITEM_ALBUM_NEW_COMMENT_DAY = "album_new_comment_day";
        String ITEM_HOME_SIGN_RED_DOT_FREQUENCY = "home_sign_badges"; // x天显示一次
        String ITEM_PUSH_START_PLAY_SEC = "push_start_play_sec";
        String ITEM_HALF_PLAYER_LYRIC = "half_player_lyric";
        String ITEM_PLAY_MORE_AD_FREE = "player_panel_ad_free";
        String ITEM_SUBSCRIBE_FOLLOW_PRIORITY = "playpagev3FavoriteCollectControl";
        String ITEM_DELAY_CHECK_SPLASH_TIME_MS = "delay_check_splash_time_ms";
        String KEY_APP_COMMENT_NEW_CONFIG = "key_app_comment_new_config"; // 应用市场求好评弹窗配置
        String PLAYING_CARD_CLOSE_INTERVAL = "playing_card_close_interval";
        String ITEM_RECOMMEND_SCENE_PLAYLIST = "recommend_scene_playlist";
        String KEY_MAX_ALBUM_GUIDE_INTERVAL = "max_album_guide_interval";   // 专辑引导订阅弹窗最长间隔多长时间不再出了-单位分钟
        String KEY_MAX_ALBUM_SUBSCRIBE_DIALOG_SHOW_INTERVAL = "max_album_subscribe_dialog_show_interval";   // 订阅引导弹窗最小间隔时间-单位小时
        String ITEM_PLAYER_READ_FEEDBACK = "player_read_feedback";

        String KEY_P_X_CARD_VALIDATED_DURATION = "p_x_card_validated_duration"; // 有效曝光时长配置中心
        String KEY_P_X_CARD_BLACK_COUNT = "p_x_card_black_count"; // 小黑屋次数

        String KEY_AD_FREE_ALBUM = "pre_ad_free_all_situ_in_album"; // 广告白名单

        String KEY_POLICY_CHANGE_NOTICE_CONTENT = "policy_change_notice_content"; // 隐私协议变更弹窗内容
        String KEY_POLICY_CHANGE_NOTICE_VERSION = "policy_change_notice_display_version"; // 隐私协议变更版本
        String KEY_IS_POLICY_CHANGE_NOTICE_ENABLE = "is_policy_change_notice_enable"; // 隐私协议版本变更弹窗是否允许弹出
        String KEY_POLICY_CHANGE_NOTICE_URL = "policy_change_notice_url"; // 隐私协议变更弹窗内容超链接
        String KEY_LOGIN_VERFIY_UID_MOST = "login_verfiy_uid_most"; //登录校验uid需要大于某个值
        String KEY_CHASE_TAB_RED_INTERVAL_TIME = "chase_tab_red_interval_time_s"; // 追更tab红点展示间隔时间 单位s
        String ITEM_HALFSCREEN_PLAYVC_RIGHTS_EXPIRE_REMINDER_TIME = "halfscreen_playvc_rights_expire_reminder_time";
        String ITEM_HALFSCREEN_PLAYVC_AD_PRIVILEGE_FLOAT_MAX_DISPLAYTIMES = "halfscreen_playvc_ad_privilege_float_max_displaytimes";

        String KEY_IMMERSIVE_SKIN_AD_FULLSCREEN = "immersive_skin_ad_force_fullscreen"; //沉浸式广告强制全屏
        String KEY_APP_WIDGET_COFIG_JSON_DATA = "key_app_widget_cofig_json_data"; // 小组件配置
        String KEY_APP_RECOMMEND_TO_LISTEN_JSON_DATA = "key_app_recommend_to_listen_json_data"; // 新首页习惯听待播卡片配置
        String KEY_TIMBRE_FEEDBACK = "timbre_feedback"; // 音色反馈
        String KEY_HIMALAYA_DOWNLOAD_URL = "himalaya_download_url"; // himalaya下载链接
        String KEY_HIMALAYA_DOWNLOAD_INTERVAL = "himalaya_download_interval";
        String KEY_OUTER_ITING_PLAY_PAGE_LAUNCH_CATEGORIES = "outer_iting_play_page_launch_categories";

        String KEY_AI_DOC_PRODUCT_COPYRIGHT = "ai_doc_product_copyright";
        String KEY_VIRTUAL_PERSON_BOX_ON_PLAYPAGE = "virtual_person_box_on_playpage"; // 虚拟人box在播放页
        String ITEM_NO_DOC_CLICK_TIPS_SWITCH = "item_no_doc_click_tips_switch";
        String KEY_IM_SDK_SYNC_DONE_SWITCH = "key_im_sdk_sync_done_switch";
        String SIGNIN_NEED_LOGIN = "signIn_need_login"; // 签到入口是否要检验登录

        String KEY_TING_KAN_COPY_FUNCTION_SWITCH = "tingkan_copy_function_switch";

        String KEY_DRIVE_MODE_FIQ = "drive_mode_fiq";

        String RECALL_ADFREE_LISTEN_DURATION = "recall_adFree__listen_duration"; // 召回用户每天免广的收听时长，单位秒

        String ITEM_XPLAY_PLAYLIST_HALF_ON_VIDEO = "playpage_video_playlist_half_hangin_mode";
        String ITEM_RESOURCE_POSITION_AD_FREE_REQUEST_RETRY_COUNT = "resource_position_ad_free_request_retry_count";

        String ITEM_HOME_VIDEO_TAB = "home_video_tab";
        String ITEM_TO_LISTEN_CATEGORY_LIST = "to_listen_category_list";
        String ITEM_TO_LISTEN_TRACK_PKGS_CONFIG = "item_to_listen_track_pkgs_config";
        String ITEM_PLAY_SEEK_BAR_INTERACTION_POINT = "play_seek_bar_interaction_point";
        String ITEM_FREE_LISTEN_V2_SWITCH = "free_listen_v2_switch";
        String ITEM_CHASING_PRODUCT = "duqiyan_effect_Aug_exp"; // 正在追实验
        String ITEM_MASTER_NOTE_AND_COMMENT_CONFIG = "master_note_and_comment_config";
        String ITEM_SHUHUANG_TOPIC_GUIDANCE = "shuhuang_topic_guidance"; // 话题默认文案
        String ITEM_SHORT_VIDEO_ALBUM_CARD_SHOW_PROGRESS = "short_video_album_card_show_progress";  // 短视频展示进度
        String ITEM_FULLSCREEN_VIDEO_FEEDBACK_CONFIG = "fullscreen_video_feedback_config";  // 全屏视频反馈
        String ALBUM_VIDEO_TAB_HAS_WIDTH_HIGHT = "album_video_tab_has_width_hight";  // 视频有宽高
        String ITEM_INTERACTIVE_CARD_CLOSE_BY_ALBUM_INTERVAL = "interactive_card_close_by_album_interval";  // 关闭互动卡片在专辑维度不再展示的时间间隔单位s
        String ITEM_ACCESSIBILITY_MODE_ENTRY_START_VERSION = "accessibility_mode_entry_start_version"; // 无障碍模式入口开关
        String ITEM_HOME_RED_DOT_MAX_COUNT = "home_red_dot_max_count"; // 主页红点最大数量
        String ITEM_HOME_RED_DOT_PRIORITY = "home_red_dot_priority"; // 主页红点优先级
        String ITEM_FREE_UNLOCK_TIPS_ASSEMBLE_V6 = "free_unlock_tips_assemble_v6"; // 畅听配置V6
        String ITEM_FREE_UNLOCK_TIPS_ASSEMBLE_V7 = "free_unlock_tips_assemble_v7_0515"; // 畅听配置V7

        String ITEM_SHORT_PLAY_FREE_UNLOCK_TIPS_ASSEMBLE = "short_play_free_unlock_tips_assemble_1226"; // 短剧畅听配置
        String ITEM_SHORT_PLAY_FREE_UNLOCK_TIPS_ASSEMBLE_0111 = "short_play_free_unlock_tips_assemble_0111"; //2024.01.11 新版本的短剧畅听配置

        String NEXT_VOICE_LENGTH = "next_voice_length"; // 下一首声音播报-最小声音长度
        String NEXT_VOICE_GAP = "next_voice_gap"; // 下一首声音播报-间隔时间

        String ITEM_SKETCH_ALBUM_NO_AUDIO = "sketch_album_no_audio_tab"; //短剧声音 tab 开关
        String ITEM_LOGIN_OVERSEA_VERFIY_SWITCH = "login_oversea_verfiy_switch"; // 海外登录校验开关

        String ITEM_BARRIER_FREE_ACTIVITY = "barrierFreeActivity"; // 无障碍模式运营位

        String ITEM_PLAY_PAGE_AND_TING_TEXT_MODE_SWITCH = "key_paly_page_and_ting_text_mode_switch";
        String ITEM_CHATXMLY_SCAN_BLUETOOTH = "chatxmly_scan_bluetooth";
        String ITEM_CHATXMLY_WAKEUP_SETTING = "chatxmly_wakeup_setting";
        String ITEM_WAKE_UP = "wake_up"; // 语音助手语音唤醒功能ab
        String ITEM_TRACK_TAG_CONFIG = "track_tag_config"; // 声音条标签配置
        String ITEM_HOMEPAGE_FREE_TIME = "homepage_free_time"; // 首页免费时间
        String ITEM_HOME_FIRST_SCREEN_MIN_COUNT = "home_first_screen_min_count"; // 推荐页首屏白屏兜底
        String ITEM_CHATXMLY_DEFAULT_SPEAKER = "chatxmly_default_speaker"; // Chatxmly默认speaker
        String ITEM_ELDERLY_MODE_VOICE_SEARCH = "elderly_mode_voice_search"; // 老年模式语音搜索
        String ITEM_ACCESSIBILITY_MODE_VOICE_SEARCH = "accessibility_mode_voice_search"; // 无障碍模式语音搜索
        String ITEM_FLOW_LAYOUT_BUG_FIX = "flow_layout_bug_fix"; // FlowLayout 修复尺寸改变后显示异常的问题，为了怕出问题，加个开关
        String ITEM_ELDER_READ_TAB_URL = "elder_read_tab_url"; // 老年模式阅读tab url

        String ITEM_END_PLAY_DEFAULT_URL = "endplay_default_url";
        String ITEM_PLAYPAGE_LIVE_ANIMATION_CD = "playpage_live_animation_cd"; // 播放页右上角直播入口动画CD
        String ITEM_LIVE_PAUSE_ANIMATION_EFFECT = "live_sound_pause_animation_effect"; // 播放页声音暂停时右上角直播入口动画CD
        String AB_LIVE_SOUND_PAUSE = "live_sound_pause";
        String HOME_RN_USE_CLIENT_AB = "home_rn_use_client_ab";
    }

    interface Group_account {
        String GROUP_NAME = "account";
        String ITEM_ANNOUNCEMENTMESSAGE = "announcementMessage";
    }

    interface Group_live {
        String GROUP_NAME = "live";
        String ITEM_FEED_STATISTICS_UPLOAD_THRESHOLD = "feed_statistics_upload_threshold";
        String ITEM_STUDIO_PIC = "Studio_Pic";//图片发言开关
        String ITEM_STUDIO_TEXT = "Studio_Text";//文本发言开关
        String ITEM_LIVEROOM_CMT = "liveroom_cmt";//评论框默认文字
        String ITEM_MOBILE_MAKEFRIENDS_BATTLE = "Mobile_MakeFriends_Battle";//交友模式-团战模式
        String ITEM_BARRAGE = "Barrage";//普通弹幕
        String ITEM_RED_ENVELOPESYYF = "Red_Envelopesyyf";//语音房红包
        String ITEM_VIP_OPEN_ENTRANCE = "Vip_Open_Entrance";//贵族开通入口
        String LIVE_MIC_DELAYED_CHAIN_TIME = "live_mic_delayed_chaining_time";//连麦降频延迟时间
        String ITEM_LIVE_SCROLL_BOTTOM_TIP = "live_scroll_bottom_tip";//底部动画文案
        String ITEM_LIVE_SCROLL_BOTTOM_SWITCH = "live_scroll_bottom_switch";//滑动有惊喜功能开关
        String ITEM_LIVE_SELL_GOODS_CONFIG = "live_sell_goods_config";//客户端卖货功能配置组
        String ITEM_LIVE_CELEBRATION_SWITCH = "Celeb-entry";//主播庆会入口开关
        String ITEM_LIVE_MINIMIZE_ENTRY = "live_minimize_entry";//视频，课程最小化入口
        String SP_LIVE_TOPS = "SP_LIVE_TOPS";//是否打开视频上头条
        String LIVE_INVITE_SWITCH_MIC = "chat_invite_switch";//是否打开主播连麦开关
        String LIVE_ANIM_CHANNEL_INTERVAL = "live_home_channel_avatar_animation_interval";//动画间隔
        String LIVE_ANIM_CHANNEL_REPEAT = "live_home_channel_avatar_animation_repeat_times";//动画次数
        String ITEM_LIVE_SALE_TIP = "sale_tip";//开播页卖货图上的标签
        String HOT_WORD_SHOW_TIME = "HOT_WORD_SHOW_TIME";//热词时间
        String ITEM_AMUSE_CONSTELLATION_LOTTERY = "amuse_Constellation_Lottery";//语音房抽奖
        String ITEM_XIDIANSWITCH = "xidianswitch";//礼物面板喜点入口
        String ITEM_AMUSEIM = "amuseIM";//语音房私信
        String ITEM_GIFT_ENTRANCE = "Gift_Entrance";//送礼入口
        String ITEM_MOBILE_MAKEFRIENDS_ENTRANCE = "Mobile_MakeFriends_Entrance";// 音频直播 - 交友模式入口
        String ITEM_MOBILE_PIA_ENTRANCE = "Mobile_Pia_Entrance"; // 音频直播 - Pia 戏模式入口
        String ITEM_PK = "pk";//PK模式
        String ITEM_AMUSE_TREASURE = "amusetreasure"; //语音房夺宝
        String ITEM_FOLLOW_REMINDER_TIME = "followremind_time";
        String ITEM_FOLLOW_REMINDER_TIME2 = "followremind_time2";
        String ITEM_NEW_USER_RADIO_NOTICE = "new_User_Radio_Notice";
        String ITEM_NEW_USER_CHAT = "new_User_Chat";
        String ITEM_MY_LIVE = "MyLive";//我的直播跳转
        String ITEM_FAST_MIC_ENT = "amuse_Lineup";// 快速上麦显示开关
        String ITEM_PODCAST_GUIDE_URL = "podcast_owner_guide_url";//播客房间，房主第一次关闭跳转的引导页ru
        String ITEM_LIVE_PK_APPOINT = "live_pk_appoint";//指定PK开关
        String ITEM_LIVE_PK_POP_OVER = "pk_popover";
        String ITEM_LIVE_PK_ANIM = "pk_ani";
        String ITEM_LIVE_MEM_CONFIG = "live_mem_switch";//直播内存上报
        String ITEM_QUIT_LIVEROOM_NO_POPUP_ENTRANCE = "quit_liveroom_no_popup_entrance_v2";//退出无直播间推荐卡片弹窗的配置（黑名单）
        String ITEM_QUIT_LIVEROOM_TARGET_MINUTE = "quit_liveroom_target_minute";//用户在房间的停留时间
        String ITEM_FANS_JOIN_ANIM_DURATION = "fans_group";//加入粉丝团引导动画时间频率
        String ITEM_RESOURCE_FULL_DOWNLOAD_FLAG = "resource_full_download_flag";//是否全量下载动效资源，默认仅下载核心资源
        String ITEM_LIVE_HOMEPAGE_AB_CARD_THREE = "live_homepage_three_card";//9.5.60版本，采用新的AB实验
        String ITEM_GREET_EMOTICONS_INFO = "liveroom_greet_emoticons_info";//直播间打招呼表情配置
        String ITEM_SATISFACTION_TIME_SECOND = "satisfaction_time";
        String ITEM_1ST_COMMENT = "liveroom_1st_comment";//评论区输入框文案可配置
        String ITEM_LIVE_VERIFY_SWITCH = "realname_auth";//实名认证是否打开
        String ITEM_DEVICE_BLACKLIST_FOR_H265 = "device_blacklist_for_h265";//明确不支持H265的设备黑名单
        String ITEM_LIVE_HOME_TAB_AUTO_LOAD = "live_home_tab_auto_load";//直播tab自动加载数据
        String ITEM_LIVE_PK_PREDICTION = "PKprediction";
        /**
         * 匿名模式是否放开给用户
         */
        String ITEM_LIVE_ROOM_ANONYMITY_MODE = "live_room_anonymity_entry";
        String ITEM_LIVE_ROOM_ANONYMITY_WHITELIST = "live_room_whitelist_anonymous";
        String ITEM_KEY_LIVE_PK_PANEL_BACKGROUND = "PK.background";
        String ITEM_KEY_LIVE_OFF_EXCHANGE_ROOM = "off_exchange_room";
        /**
         * 更多直播黑名单
         */
        String ITEM_KEY_LIVE_MORE_LIVE_BLACK_LIST = "more_live_blacklist";
        String ITEM_KEY_LIVE_HOME_AUTO_ROOM = "feedpage_auto_enter_sec";
        /**
         * Android 视频直播渲染 View 类型
         * 0：Textureview 1：SurfaceView
         */
        String ITEM_KEY_LIVE_ANDROID_SURFACE_TYPE = "android_surface_type";
        /**
         * 鸿蒙 4.0 及以上系统视频直播渲染 View 类型
         * 0：Textureview 1：SurfaceView
         */
        String ITEM_KEY_LIVE_HARMONY_SURFACE_TYPE = "harmony_surface_type";
        /**
         * 预加载视频直播间时，延迟创建房间页面的时间
         */
        String ITEM_KEY_LIVE_CREAT_FRAGMENT_DELAY_TIME = "creat_fragment_delay_time";
        /**
         * 视频直播专用的部分设置
         */
        String ABTEST_KEY_VIDEO_REBUF_PLAN = "live_video_rebuffer_plan_index";
        String LIVE_VIDEO_REBUF_PLAN_INDEX = "exo_live_video_rebuffer_plan_index";
        String COUNT_REBUF_STEP_COUNT = "exo_live_video_rebuffer_step_count";
        String MAX_REBUF_NETSPEED_LIMIT = "exo_live_video_rebuffer_speed_max";
        String MIN_REBUF_NETSPEED_LIMIT = "exo_live_video_rebuffer_speed_min";
        String MAX_LIVE_VIDEO_REBUF_TIME = "exo_live_video_rebuffer_max";
        String MIN_LIVE_VIDEO_REBUF_TIME = "exo_live_video_rebuffer_min";
        String SWITCH_LIVE_REBUF_AUTO_CLEAR = "exo_live_video_rebuffer_auto_clear_open";
        String LIVE_VIDEO_REBUF_AUTO_CLEAR_INTERVAL = "exo_live_video_rebuffer_auto_clear_interval_s";
        String LIVE_M3U8_STATIC_FILE_ENABLE_TEMPO = "exo_live_m3u8_static_file_enable_tempo";
        /**
         * 个播是否支持批量礼物连击
         */
        String ITEM_LIVE_SUPPORT_BATCH_CONSECUTIVE_GIFT = "live_batch_consecutive_gift";
        String ITEM_COMPONENT_FIRST_GAP_TIME = "component_first_gap_time";
        String ITEM_COMPONENT_FIRST_TO_SECOND_GAP_TIME = "component_first_to_second_gap_time";
        String ITEM_COMPONENT_SECOND_GAP_TIME = "component_second_gap_time";
        /**
         * 短剧频道页「推荐」Tab 强制刷新的时间间隔配置，单位分钟
         */
        String MINI_DRAMA_CHANNEL_REFRESH_DURATION = "minidrama_channel_refresh_duration";

        /**
         * 短剧剧集自动弹出激励视频的冷却时长配置，单位分钟
         */
        String MINI_DRAMA_EPISODE_REWARD_AD_GAP = "minidrama_episode_reward_ad_gap";
        /**
         * 直播单列信息流 AB 实验
         */
        String AB_LIVE_HOME_PAGE_STYLE = "exp_name";
        /**
         * 是否需要检测此次打开是抖音等广告渠道激活后首次打开App
         */
        String ITEM_CHECK_NEW_USER_FOR_AD = "check_new_user_for_ad";

        String GUIDE_NEW_MSG_CONFIG = "live_anchor_guide_config";

        /**
         * quic协议ab实验
         */
        String AB_LIVE_PLAY_QUIC_CONFIG = "live_play_quic_config_new";

        /**
         * 配置中心quic开关
         */
        String ITEM_LIVE_PLAY_QUIC_ENABLE = "live_play_quic_enable_new";
    }

    interface Group_kid {
        String GROUP_NAME = "kid";

        String ITEM_KID_HYBIRD_INFO = "kid_hybrid_info";

        String ITEM_KID_ACCOMPANY_INFO = "accompany_info";

        String ITEM_KID_COMMON_CHANNEL_CONFIG = "kid_common_channel_config";

        String KID_STORY_CHANNEL_ALERT_URL = "kid_story_channel_alert_url";

        String KID_TEEN_MODE_CONFIG = "kid_teen_mode_config";

        String KID_COAS_SLEEP_MODE_TIME = "kidCoasSleepModeTime";
    }

    interface Group_tob {
        boolean AUTH_SWITCH_DEFAULT = false;

        String GROUP_NAME = "tob";
        String ITEM_AUTH_SWITCH = "authEntireSwitch";   // 实名认证整体开关
        String ITEM_TRACK_ALBUM_AI_TEXT = "track_album_ai_text"; // AI内容声明更改为配置项
        String ITEM_AUTH_MSG_IN_RECORD_HOME = "authSwitchInRecord"; // 录音页实名认证提示文案
        String ITEM_AUTH_MSG_IN_SELECT_ALBUM = "authSwitchInSelectAlbum";   // 录音-专辑选择页实名认证提示文案
        String ITEM_RECORD_BUTTON_DESCRIPTION = "recordButtonDescription";   // 录音-专辑选择页实名认证提示文案
        String ITEM_DUB_TIPS = "dubTips"; //下载时的提示
        String ITEM_RECORD_BACK_DIALOG_CONTENT = "luyintanchuang"; //录音返回弹窗内容
        String ITEM_RECORD_SKILL_URL = "luyinjiqiao_url"; //录音返回弹窗技巧链接
        String ITEM_JIANGZAO = "jiangzao"; //降噪开头
        String ITEM_DUB_SCORE_EVALUATION = "dubScoreEvaluation"; //趣配音评分文案
        String ITEM_QUPEIYIN_UPLOAD_SHARE = "qupeiyin_upload_share";//趣配音分享文案
        String ITEM_DUB_MARK_CONTROLAB = "dubMarkControlAB"; //趣配音评分开关
        String ITEM_DUB_SCORE = "qpy_score2"; //趣配音规避崩溃
        String ITEM_GOODS_PLATFORM = "goods_platform"; //带货？
        String ITEM_PACKING_STRATEGY = "packing_strategy";
        String ITEM_READING_NO_VERIFY = "reading_no_verify"; //实名认证
        String ITEM_EXAMPLE_CUSTOMTITLE = "example_customtitle";
        String ITEM_EXAMPLE_TITLE = "example_title";
        String ITEM_ALBUM_TITLE_STRATEGY = "title_strategy";
        String ITEM_ALBUM_COVER_STRATEGY = "cover_strategy";
        String ITEM_ALBUM_SELLING_POINT_STRATEGY = "selling_point_strategy";
        String ITEM_ALBUM_INTO_STRATEGY = "into_strategy";
        String ITEM_ALBUM_COMMENT = "album_comment";
        String ITEM_PAY_MANAGEMENT = "pay_management";
        String ITEM_NICKNAME_TIMES = "nickname_times";
        String ITEM_BEAUTIFY_ID_ANDROID = "beautify_id_android";
        String ITEM_LIVE_MANUAL = "live_manual";
        String ITEM_MYDATA = "mydata";
        String ITEM_ALBUM_REFUND = "album_refund";
        String ITEM_QUPEIYIN_FAXIAN_TAB = "qupeiyin_faxian_tab";
        String ITEM_EFFECT_ID_ANDROID = "effect_id_android";
        String ITEM_QUPEIYIN = "qupeiyin";
        String ITEM_EFFECT_PIC = "effect_pic";
        String ITEM_COINSWITCH = "coinSwitch";
        String ITEM_TORT_ANCHOR = "tort_anchor";
        String ITEM_REVIEW_NAME = "review_name";
        String ITEM_EFFECT_TEXT = "effect_text";
        String ITEM_ILLEGAL_RECORD = "illegal_record";
        String ITEM_TORT_TEXT = "tort_text";
        String ITEM_CIRCLE_MES = "circle_mes";
        String ITEM_COMMODITY_ENTRANCE_AB = "commodity_entrance_AB";
        String ITEM_TUPIANPEIYIN = "tupianpeiyin";
        String ITEM_BEAUTIFY_ID = "beautify_id";
        String ITEM_DATA_CENTER = "data_center";
        String ITEM_MY_COMMUNITY = "my_ community";
        String ITEM_TALENT_ANCHOR = "talent_anchor";
        String ITEM_TRACK_COMMENT = "track_comment";
        String ITEM_QUPEIYIN_PLAYBILL_CONTENT = "qupeiyin_playbill_content";  //趣配音海报分享内容
        String ITEM_BEAUTIFY_PIC = "beautify_pic";
        String ITEM_COMMODITY_ENTRANCE = "commodity_entrance";
        String ITEM_AUDIO = "Audio+";
        String ITEM_SUBDIVISION_AREA = "subdivision_area";
        String ITEM_TORT = "tort";
        String ITEM_MANUAL = "manual";
        String ITEM_INCOME_MANUAL = "income_manual";
        String ITEM_AUTHSWITCHINUPLOADSUCCESS = "authSwitchInUploadSuccess";
        String ITEM_MY_APPLICATION = "my_application";
        String ITEM_INCOME_CENTER = "income_center";
        String ITEM_BEAUTIFY_TEXT = "beautify_text";
        String ITEM_TORT_URL = "tort_url";
        String ITEM_MY_ORDER = "my_order";
        String ITEM_DUBDEFAULTSENBYSENCONTROL = "dubDefaultSenBySenControl";
        String ITEM_SLQJ_DUBBING_COMPETITION_SHARE = "slqj_dubbing_competition_share";
        String ITEM_ORIGINAL = "original";
        String ITEM_DUBSQUARECODUB = "dubSquareCoDub";
        String ITEM_TORT_ANCHOR_URL = "tort_anchor_url";
        String ITEM_MY_QUESTION = "my_question";
        String ITEM_E_COMMERCE_TOOL = "e_commerce_tool";
        String ITEM_SPREAD = "spread";
        String ITEM_MY_ACHIEVEMENT = "my_achievement";
        String ITEM_PROMOTION_BALANCE = "Promotion_balance";
        String ITEM_SUBDIVISION_AREA_URL = "subdivision_area_url";
        String ITEM_ALBUM_CERTIFICATION = "album_certification";
        String ITEM_DUBMARKSWITCHCONTROL = "dubMarkSwitchControl";
        String ITEM_RECORDING_MANUAL = "recording_manual";
        String ITEM_SHARE_DATA_SWITCH = "share_data_switch";
        String ITEM_MY_INCOME = "my_income";
        String ITEM_REQUEST_COUNT_THRESHOLD_LIST = "request_count_threshold_list";
        String ITEM_E_COMMERCE_TOOL_URL = "e_commerce_tool_url";
        String ITEM_GOODS_CARD_TIME = "goods_card_time";
        String ITEM_FRESHMAN_MANUAL = "freshman_manual";
        String ITEM_EFFECT_ID = "effect_id";
        String ITEM_ANCHOR_DESK1 = "anchor-desk1";
        String ITEM_UNCHECK_HTTPS = "item_user_check_https";
        String ITEM_PROGRAM_FEEDBACK_URL = "MyWorks_service";
        String ITEM_ALBUM_DATA_URL = "MyWorks_Data";
        String ITEM_ALBUM_DATA_NEW_URL = "MyWorks_Data2";
        String ITEM_RECORD_IF_PPT_ON = "if-ppt-on";
        String ITEM_SOUND_SHARE_BOARD = "sound_shareBoard";
        String ITEM_SOUND_SHARE_BOARD_NEW_AB_FLAG = "sound_shareBoard_new_ab_flag";
        String ITEM_EDIT_ALBUM_BRIEF = "Edit_albumBrief";
        String ITEM_P_LA_C_SHARE_SWITCH = "p_la_c_share_switch";
        String ITEM_P_LA_C_SHARE_LINK = "p_la_c_share_link";
        String ITEM_REWARD_RULE = "Reward_rule";
        String ITEM_INTERNAL_STORAGE = "recordstoragepath";
        String ITEM_MUSIC_PICK_LOCAL = "musicleadingtype";
        String ITEM_CREATIVE_GRADE = "creative_grade";
        String ITEM_GAO_DING_SDK_OPEN = "app_gaoding_switch";
        String ITEM_USE_ONE_CLICK_COMPOSITION = "use_one_click_composition_panel";
        String ITEM_UPLOAD_INTERACTIVE_CARD_OPEN = "hudongzujian";
        String ITEM_RECORD_INTERACTIVE_ANIM_CONFIG = "fenduanlu_tip_txt";
        String ITEM_RECORD_INTERACTIVE_END_TIP_CONFIG = "fenduanlu_notice_to_end";
        String ITEM_RECORD_AI_BROKER_POST_TIMEOUT = "aiTimeoutSec";
        String ITEM_RECORD_NEW_RECORD_OPEN = "record_new_entrance";
        String ITEM_EDIT_TX = "edit_tx";
        String ITEM_AUDIO_UPLOAD_MONITOR_TIME_CONTROL = "tob_upload_monitor_time_control";
    }

    interface Group_android {
        String GROUP_NAME = "android";
        String ITEM_LISTEN_TIME_FOR_SAFE = "ls_iten_ime_or_afe"; // 用来确定是否有免广告安全问题播放时间阈值
        String ITEM_MAIN_ACT_SHOW_TIMES_FOR_SAFE = "man_at_shw_tim_for_se"; // 用来确定mainactivity展示次数预制
        String ITEM_LAUNCH_XIMA_LOGO_SWITCH = "launch_xima_logo_switch";
        String ITEM_LAUNCH_XIMA_LOGO_CAN_SHOW = "launch_xima_logo_can_show";
        String ITEM_LAUNCH_XIMA_LOGO_SHOW_TIME = "launch_xima_logo_show_time";
        String ITEM_VIDEO_IMMERSIVE_OPEN = "x_video_immersive_open";
        String ITEM_ANDROID_FIX_LEAKCANARY_PLUMBER = "item_android_fix_leakcanary_plumber"; // 是否开启系统泄露修复
        String ITEM_FIREWORK = "firework"; // firework 弹屏开关
        String ITEM_NATIVEDLGCNTR = "nativeDlgCntr"; // 本地弹屏开关
        String ITEM_FIREWORK_SAVE_DETAIL_LOG = "firework_save_detail_log"; // 弹屏是否保存本地日志开关
        String ITEM_FIREWORK_SAVE_DETAIL_FREQ_LOG = "firework_save_detail_freq_log"; // 弹屏是否保存本地频繁执行的日志开关
        String ITEM_FIREWORK_SAVE_SHOW_INFO_ADD = "firework_save_show_info_add"; // 弹屏是否另外保存 firework 的展示数据
        String ITEM_FIREWORK_CLEAR_JUMP_AT = "firework_clear_jump_at_20240426"; // 弹屏是否清除点击后跳转的时间
        String ITEM_FIREWORK_DIALOG_MONITOR = "firework_dialog_monitor_20240426"; // 弹窗监控是否开启
        String ITEM_FIREWORK_DIALOG_MONITOR_LOOP_INTERVAL = "firework_dialog_monitor_loop_interval_20240426"; // 弹窗轮训检查间隔
        String ITEM_CHECKSCHEMEINTERNAL = "checkSchemeInternal";
        String ITEM_NATIVEDIALOG = "nativeDialog";
        String ITEM_XIDOPEN = "xidopen";
        String ITEM_ISOPENHTTPS = "isOpenHttps";
        String ITEM_MERMAID_PUSH_SIZE = "mermaid:push:size";
        String ITEM_MERMAID_NEXTDELAY = "mermaid:nextDelay";
        String ITEM_FLUSHCACHEINTERVAL = "flushCacheInterval";
        String ITEM_USE_IMAGE_KEY = "use_image_key";
        String ITEM_RNPAYWEIKE = "rnpayweike";
        String ITEM_MERMAID_ENABLE = "mermaid:enable";
        String ITEM_XMLOG = "xmlog";
        String ITEM_USEMERGEDREQUEST = "useMergedRequest";
        String ITEM_MERMAID_RN_ENABLE = "mermaid:rn:enable";
        String ITEM_MERMAID_UPNUM = "mermaid:upNum";
        String ITEM_XMLOGSYNC = "xmlogsync";
        String ITEM_ISOPENRECORDDEBUG = "isOpenRecordDebug";
        String ITEM_RNPAYCOMMON = "rnpaycommon";
        String ITEM_NEWPLUGINSERVER = "newPluginServer";
        String ITEM_MERMAID_SAMPLING = "mermaid:sampling";
        String ITEM_SPEECHCONTROLVERIFY = "speechControlVerify";
        String ITEM_ISCHECKRESOURCE = "isCheckResource";
        String ITEM_NEW_CAR_PLUGIN_SERVER = "new_car_plugin_server";
        String ITEM_MERMAID_RN_SAMPLING = "mermaid:rn:sampling";
        String ITEM_USE_MIIT_MDID_SDK = "use_miit_mdid_sdk";
        String ITEM_OPEN_CHECK_DEX = "open_ck_dx"; // 签名校验开关
        String ITEM_SAVE_PROGRESS_INTERVAL = "saveProgressInterval";    // 保存进度间隔-单位秒
        String ITEM_USE_X5_WEBVIEW = "use_x5_webview";  // 是否使用X5内核
        String ITEM_IS_FORCE_VIVO_USE_X5 = "is_force_vivo_use_x5";  //是否强制vivo 5.0和5.1机型使用X5内核
        String ITEM_USE_WAKE_LOCK = "useWakeLock";    // 使用wakeLock保持唤醒
        String ITEM_IS_OPEN_AUTO_STORAGE_APT = "is_open_auto_storage_apt"; //是否打开自动优化磁盘空间开关
        String ITEM_IS_UPLOAD_APP_EXIT_INFO = "is_upload_app_exit_info"; //是否打开自动优化磁盘空间开关
        String ITEM_IS_WRITE_LOG_CACHE = "is_write_log_cache"; //是否打开默认缓存写入
        String ITEM_IS_HOOK_FINALIZER_WATCH_DAEMON = "is_hook_finalizer_watch_daemon"; //是否hook FinalizerWatchdogDaemon开关
        String ITEM_AUTO_STORAGE_APT_INTERVALS_DAY = "auto_storage_apt_intervals_day";
        String ITEM_DOWNLOAD_LABEL = "download_label";// 下载播放资源标记 type=download
        String ITEM_IS_FIT_NO_WEBP_IMAGE = "is_fit_no_webp_image";
        String ITEM_IF_HOST_VERIFIER = "ifHostVerifier";
        String ITEM_AUTO_LOAD_PLUGIN = "auto_load_plugin"; // 自动加载插件开关
        String ITEM_USE_AIDL_OPTIMIZATION = "use_aidl_optimization"; // aidl耗时优化
        String ITEM_USE_EXO_PLAYER = "use_exo_player";  // 使用exo_player
        String ITEM_USE_FLAC_SOFT_DECODER = "use_flac_soft_decoder";  // 使用flac软解
        String ITEM_USE_CDN_RETRY = "use_cdn_retry";// 是否使用tx和ali cdn 重试
        String ITEM_RETRY_PLAY_AFTER_FAIL = "retry_play_after_play_fail"; // 播放失败后是否重试
        String ITEM_DELETE_FILE_AFTER_DECODE_FAIL = "delete_file_after_decode_error"; // 播放失败后是否删除所有文件
        String ITEM_PLAY_ERROR_TOTAL_COUNT = "exo_max_total_play_failed_count";
        String ITEM_PLAY_ERROR_SINGLE_URL_MAX_COUNT = "exo_max_single_url_failed_count";
        String ITEM_EXO_LOADABLE_MAX_COUNT = "exo_loadable_max_count";
        String ITEM_EXO_CACHE_SIZE_SOLUTION = "item_exo_cache_size_solution";
        String ITEM_MAX_RETRYCOUNT_FOR_BEHIND_LIVE_EXCEPTION = "exo_max_retry_count_for_behind_live_exception";
        String ITEM_OPEN_TOGGLE_FOR_BehindLiveWindowException = "exo_open_toggle_for_behind_live_exception";
        String ITEM_CHECK_LOCK_THREAD_FOR_EXO = "exo_check_lock_thread";
        String ITEM_USE_NEW_CACHE_EVICTOR_FOR_EXO = "exo_use_new_cache_evictor";

        String TO_LISTEN_ANDROID_NEW_CLOSE = ToListenUtil.TO_LISTEN_ANDROID_NEW_CLOSE; // android待播功能是否关闭
        String OPEN_SOUND_EFFECT_CONFIG = "open_sound_effect_config_exo";
        String ITEM_KILL_PROCESS_FOR_UNLIMITED_LOADING = "exo_kill_process_for_unlimited_loading";
        String ITEM_OPEN_PLAYER_DUMP_INFO = "open_player_dumpinfo_new";
        String ITEM_IF_DEX2OAT = "if_dex_2_oat"; //是否使用编译动态加速
        String ITEM_IF_DEX2OAT_NO_PATCH = "if_dex_2_oat_no_patch";
        String ITEM_IF_DEX2OAT_PATCH = "if_dex_2_oat_patch";
        String ITEM_IF_USE_LINEAR_ALLOC = "if_use_linear_alloc";
        String ITEM_IF_USE_BITMAP_CHECK_H5 = "if_use_bitmap_check_h5";
        String ITEM_PAGE_MONITOR_POSITION_TYPE = "item_page_monitor_position_type";
        String ITEM_PAGE_MONITOR_BITMAP_SCALE = "item_page_monitor_bitmap_scale";
        String ITEM_PAGE_CHECK_PIXEL_THRESHOLD = "item_page_check_pixel_threshold";
        String ITEM_PAGE_CHECK_OLD_UPLOAD = "item_page_check_old_upload";
        String ITEM_IF_USE_TRACEID = "if_use_trace_id";
        String ITEM_IF_USE_CACHE_IN_NET_MONITOR = "if_use_cache_int_net_monitor";
        String ITEM_NOTIFY_USE_THREAD = "notify_use_thread"; // 是否使用线程notify通知栏
        String ITEM_COOKIE_OPTIMIZE_OPEN = "item_cookie_optimize_open"; // 是否使用cookie标准化控制
        String ITEM_COOKIE_CRUMBLE_OPEN = "item_cookie_crumble_open"; // h2 open开关
        String ITEM_COOKIE_CONTROL_CONFIG = "item_cookie_control_config"; // 是否cookie配置
        String ITEM_HYBRID_LOAD_TRACE_OPEN = "item_hybrid_load_record_open"; // 是否启用hybrid 启动追踪
        String ITEM_JS_SDK_TRACE_LOG_OPEN = "item_js_sdk_trace_log_open"; // 是否启用jssdk 启动日志追踪
        String ITEM_FILTER_OPPO_NO_PLAY_AD = "item_filter_oppo_no_play_ad"; // 是否启用jssdk 启动日志追踪
        String ITEM_COMPLETE_OFFSET = "completeOffset"; // 声音剩余可判断已播完的时长
        String ITEM_PLAY_ERROR_RETRY = "playErrorRetry"; // 播放错误是否进行重试
        String ITEM_NOT_SUPPORT_WEBGL_OPEN_X5 = "item_not_support_webgl_open_x5"; // 是否打开x5如果不支持webgl
        String ITEM_USE_FFMPEG_EXTENSION_DECODER = "useFfmpegExtensionDecoder"; // 是否使用ffmpeg软解码进行播放
        String ITEM_PRE_BUFFER_URL_ENABLE = "preBufferUrlEnable";   // 是否可以预加载播放地址
        String ITEM_USE_FFMEGEXTENSION_STRATEGY = "useFfmegExtensionStrategy";  // 是否使用ffmeg解码的策略
        String ITEM_IF_OPEN_PLAY_ERROR_TARCE = "item_open_play_error_trace"; // 是否开启打印播放错误堆栈开关
        String ITEM_MAX_TRACE_LEN = "max_trace_len_for_play"; // 堆栈最大深度
        String ITEM_OPEN_EXO_CACHE = "item_open_exo_cache"; // 是否打开播放器缓存
        String ITEM_EXO_MIN_LAG_TIME = "item_exo_min_lag_time"; // 播放器卡顿最小值
        String ITEM_ANCHOR_CLEAN_PLAY_CACHE = "anchor_play_clean_cache"; // 主播清理自己作品播放缓存
        String ITEM_CATCH_WEBVIEW_RENDER_PROCESS = "catch_webview_render_process";  // 是否catchwebview崩溃
        String ITEM_HYBRID_CLOSE_PDF_VIEW = "hybrid_close_pdf_view";  // 是否关闭webview pdf预览
        String ITEM_HOTFIX_LOAD_PATCH_USE_DLC = "hotfix_load_patch_use_dlc";  // 热修复加载 patch 是否使用 dlc
        String ITEM_START_UP_TIME_INTERVAL = "startup_time_interval";  // 启动时间间隔
        String ITEM_HYBRID_RELOCATION = "item_hybrid_relocation";   // 是否支持http/https重定向拦截
        String ITEM_SAFE_JS_CALL_OPEN = "item_safe_js_call_open";   // jscall安全检查开关
        String ITEM_RE_REQUEST_PLUGIN = "re_request_plugin";// 是否重试请求插件信息
        String ITEM_SPLASH_CLICK_FILTER = "splashClickFilter";    // 开屏点击过滤快速点击
        String ITEM_OPEN_USER_FEEDBACK_LOG = "user_feedback_log";    // 用户反馈日志
        String ITEM_USE_OLD_FREE_FLOW = "item_use_old_free_flow";    // 使用老的free_flow
        String ITEM_FREE_FLOW_UNICOM_APPKEY = "item_free_flow_unicom_appkey";    // 联通免流的appkey
        String ITEM_FREE_FLOW_UNICOM_APPSERCET = "item_free_flow_unicom_appsercet";    // 联通免流的appsercet
        String ITEM_ENABLE_FIX_SINGLE_INSTANCE_LANCH_MODE = "enableFixSingleInstanceBug";    // 是否修复lanchmode为singleInstance的bug
        String ITEM_START_SUSTAINED_LISTEN_SERVICE = "is_start_sustained_listen_service";    // 用户反馈日志
        String ITEM_PICASSO_USE_NEW_LOAD = "picassoUseNewLoad"; // picasso使用新的图片解析器进行加载
        String ITEM_IF_USE_NEW_XMPLAY_CONTROLLER = "use_new_xmplaycontroller";  // 使用新版的playcontrol
        String ITEM_OPEN_LOG_TO_FILE = "item_open_log_to_file";
        String ITEM_OPEN_ENV_LOG = PreferenceConstantsInOpenSdk.ITEM_OPEN_ENV_LOG;
        String ITEM_LOG_TO_FILE_STRATEGY = "item_log_to_file_strategy";
        String ITEM_IF_USE_NEW_XM_EXTRACTORS_FACTORY = "use_new_xm_extractors_factory";
        String ITEM_IF_USE_REAL_CALL_HOOK = "use_real_call_hook";//是否使用reallcallhook
        String ITEM_IF_USE_JH_SDK_PAY = "if_use_jh_sdk"; //是否使用建行二三类sdk支付
        String ITEM_IF_SHOW_CHILD_PROTECT_BANNER = "child_protect_banner_show";
        String ITEM_IF_SHOW_TO_LISTEN_TAB_WITH_MINE = "if_show_to_listen_tab_with_mine";
        String ITEM_IF_SHOW_TO_LISTEN_TAB_WITH_PLAY = "if_show_to_listen_tab_with_play";
        String ITEM_HYBRID_CAMERA_PERMISSION_REQUEST_ENABLE = "item_hybrid_camera_permission_request_enable";
        String ITEM_HYBRID_FORBID_CAMERA_PERMISSION_REQUEST_SDK_VERSION = "key_not_support_camera_permission_request";
        String ITEM_PRE_INIT_NOTIFICATION_SWITCH = "pre_init_notification_switch";
        String ITEM_HYBRID_DEVICE_INFO_CONFIG = "hybrid_device_info_action_controll";
        String ITEM_IF_OPEN_START_FOREGROUND_SERVICE_MONITOR = "if_open_start_foreground_service_monitor";
        String ITEM_IF_OPEN_FOREGROUND_CRASH_COLLECT = "if_open_foreground_crash_collect";
        String ITEM_USE_START_SERVICE_INSTEAD_FOREGROUND = "if_use_start_service_instead_foreground";
        String ITEM_MEMORY_LEAK_FIX_BY_KJ_SWITCH = "key_memory_leak_fix_by_kj_switch";
        String ITEM_IF_MONITOR_LARGE_MEMORY = "monitor_large_memory";
        String ITEM_IF_MONITOR_MEMORY_LEAK = "monitor_memory_leak";
        String ITEM_IF_USER_UIDS_MEMORY_UPLOAD_HPROF = "user_uids_memory_upload_hprof"; // 指定uid用户上传hprof文件
        String ITEM_MEMORY_BIG_OBJECT_THRESHOLD = "item_memory_big_object_threshold"; // 内存大对象阈值 单位kb
        String ANDROID_MEMORY_LEAK_THREAD_PROBLEM_UPLPAD_FILE = "android_memory_leak_thread_problem_uplpad_file";
        String ITEM_USE_THREAD_INIT_OAID = "use_thread_init_oaid";  // 使用线程初始化oaid
        String ITEM_USE_FIX_SHARE_ANR = "item_use_fix_share_anr";//是否启用fix shareprefrence anr
        String ITEM_ENABLE_INIT_QB_SDK = "item_enable_init_qb_sdk"; // 是否初始化QuestMobile
        String ITEM_CLOSE_JPUSH_SCENARIOS = "item_close_jpush_scenarios"; // 关闭极光地理围栏
        String ITEM_USE_OPEN_HTTPDNS_IP_TEST = "item_use_open_httpdns_test";//是否启用httpdns ip 测速
        String ITEM_LISTEN_TASK_SAVE_INTERVAL_TIME = "item_listen_task_save_interval_time"; // 收听task保存的时间间隔
        String ITEM_LISTEN_TASK_INTERVAL_TIME_NEW = "item_listen_task_interval_time_new"; // 收听task保存的时间间隔
        String ITEM_NO_AUTHORIZED_TO_CALL_ERROR = CrossProcessTransferValueManager.ITEM_NO_AUTHORIZED_TO_CALL_ERROR; // 没有权限的时候是否回调error
        String ITEM_KEY_FILTER_CAR_BLUETOOTH = PlayerConstants.TINGMAIN_KEY_FILTER_CAR_BLUETOOTH;   // 是否过滤车载蓝牙的设备
        String ITEM_KEY_CAR_BLUETOOTH_CONTROL_PLAY_TIME = PlayerConstants.TINGMAIN_KEY_CAR_BLUETOOTH_CONTROL_PLAY_TIME;   // 距离上次控制的间隔时间,小于此时间不进行播放
        String ITEM_KEY_OPEN_FIRST_INSTALL_PLIST_XLOG_UPLOAD = "item_key_open_first_install_plist_xdcs_upload";
        String ITEM_KEY_IS_OPEN_UPLOAD_PLIST = "item_key_is_open_upload_plist"; // 是否打开安装列表上报
        String ITEM_KEY_UPLOAD_PLIST_INTERVAL_TIME_S = "item_key_upload_plist_interval_time_s"; // 安装列表上报时间间隔
        String ITEM_KEY_IS_OPEN_DELAY_TRACE_VIEW = "is_open_delay_trace_view"; // 是否打开延迟上报埋点。
        String ITEM_KEY_TRACE_VIEW_DELAY_TIME = "trace_view_delay_time"; // 埋点延迟上报时间

        String ITEM_IS_BG_PLAY_OPT_OPEN = "is_bg_play_opt_open";//后台播放优化策略开启
        String ITEM_IS_UPLOAD_LOG_FAST = "isUploadLogFast"; // 是否上传日志快速
        String ITEM_IS_OPEN_REQUEST_TOO_MUCH_MONITOR = "is_open_request_too_much_monitor"; // 是否开启请求过多监控
        String ITEM_RECOMMEND_HORIZONTAL_SCROLL_OPT = "recommend_horizontal_scroll_opt"; // 是否开启推荐横向滑动优化
        String ITEM_MAIN_PROCESS_DELAY_EXIT_TIME = "main_process_delay_exit_time";//主进程延迟退出的时间
        String ITEM_REMOVE_GDT_VIDEO_KEEP_SCREEN_ON = "item_remove_gdt_video_keep_screen_on";   // 是否移除广点通视频keepScreenOn的策略
        String ITEM_OPEN_DATABASE_BACKUP = "open_database_backup";
        String KEY_FREE_FLOW_CAN_USE_HTTPS = PreferenceConstantsInOpenSdk.KEY_FREE_FLOW_CAN_USE_HTTPS;  // 免流情况下是否使用https

        String ITEM_HOOK_SOCKET = PreferenceConstantsInOpenSdk.ITEM_HOOK_SOCKET;

        String ITEM_HOOK_HTTP_URL_CONNECTION = PreferenceConstantsInOpenSdk.ITEM_HOOK_HTTP_URL_CONNECTION;

        String ITEM_APM_NET_CAPTURE = PreferenceConstantsInOpenSdk.ITEM_APM_NET_CAPTURE;

        String ITEM_LISTEN_SYS_SETTING = "listen_sys_setting"; // 听系统权限设置开关
        String ITEM_BG_PLAY_OPT_SETTING_ENABLE = "bg_play_opt_setting_enable"; // 通知栏优化后台运行是否弹窗提醒去设置
        String ITEM_DNS_MONITOR = "dns_monitor"; // 自动加载插件开关
        String ITEM_RN_DEGRADE = "rn_backup_map"; //rn降级相关配置
        String ITEM_USE_NEW_APM_UPLOAD = "use_new_apm_upload"; //useNewApmUpload
        String ITEM_OPEN_TRACE_UBTSOURCE = "item_open_trace_for_ubtsource"; // 资源位为空时方法调用栈上报开关
        String ITEM_AUTO_SET_WEBVIEW_CLIENT = "webview_auto_set_client"; // 自动设置webviewclient
        String ITEM_FILTER_HOOK_INFLATE_VIEWS = "filter_hook_inflate_views"; // 过滤inflate views
        String ITEM_NOTIFY_SHOW_LIKE = PreferenceConstantsInOpenSdk.ITEM_NOTIFY_SHOW_LIKE;  // 通知栏显示喜欢
        String ITEM_XDCS_UPLOAD_START_UP = "item_xdcs_upload_start_up";   // 通过xdcs上报启动速度
        String ITEM_PUSH_GUARD_OPEN_PLAYER_PROCESS_FOR_HARMONY_GUARD = "item_push_guard_open_player_process_for_harmony_guard";   // 地理围栏拉活是否拉起player
        String ITEM_PUSH_GUARD_OPEN_PLAYER_PROCESS = "item_push_guard_open_player_process";   // push拉活是否拉起player
        String ITEM_PUSH_OPEN_XIMA_PIPE = "item_push_open_xima_pipe";   // 是否开启喜马push自建通道
        String ITEM_OPEN_NOTIFICATION_RETRY = PushNotificationFilterManager.ITEM_OPEN_NOTIFICATION_RETRY;
        String ANDROID_NOTIFICATION_OPPO_COLOR = XmNotificationCreater.ANDROID_NOTIFICATION_OPPO_COLOR;
        String ANDROID_NOTIFICATION_OPPO_PRIORITY = XmNotificationCreater.ANDROID_NOTIFICATION_OPPO_PRIORITY;
        String ITEM_PUSH_XIMA_PIPE_DURATION = "item_push_xima_pipe_duration";   // 喜马push自建通道轮询时长
        String ITEM_PLAYER_REMOVE_NOTIFY_DURATION = PushNotificationFilterManager.ITEM_PLAYER_REMOVE_NOTIFY_DURATION;
        String ITEM_PLAYER_HARMONY_OPEN_NOTIFY_DURATION = PushNotificationFilterManager.ITEM_PLAYER_HARMONY_OPEN_NOTIFY_DURATION;
        String ITEM_PLAYER_OPEN_TRANS_ACTIVITY = "item_player_open_trans_activity";   // 播放进程起一个透明的activity
        String ITEM_NOTIFICATION_PROGRESS_FIX = PreferenceConstantsInOpenSdk.ITEM_NOTIFICATION_PROGRESS_FIX;
        String ITEM_NOTIFICATION_FLAG_IMMUTABLE_FIX = XmNotificationPendingIntentCreateUtils.ITEM_NOTIFICATION_FLAG_IMMUTABLE_FIX;
        String ITEM_NOTIFICATION_CLICK_AUTO_PLAY = "item_notification_click_auto_play";   // 点击通知栏封面是否自动开播
        String ITEM_GETUI_GUARD_SLEEP_TIMEMILLS = "item_getui_guard_sleep_timemills";   // push拉活页面sleep毫秒数
        String ITEM_PAGE_CHECK_RTT_PING = "item_page_check_rtt_ping";
        String ITEM_WEAK_NET_TEST = "item_weak_net_test";
        String ITEM_COMPRESS_UPLOAD_PHOTO = "item_compress_upload_photo";
        String ITEM_SETTINS_FOR_SYNC_UPLOAD = "settings_for_sync_upload"; //实时埋点请求失败延时累加开关
        String ITEM_IS_USE_OLD_HISTORY_SYNC = "is_use_old_history_sync";
        String ITEM_IS_REQUEST_HEADER_TRIM = "is_request_header_trim";
        String KEY_USE_NEW_RTB_INFO = PreferenceConstantsInHost.KEY_USE_NEW_RTB_INFO;
        String KEY_PLAY_RECORD_PERFORMANCE_BY_FRIST = PreferenceConstantsInOpenSdk.KEY_PLAY_RECORD_PERFORMANCE_BY_FRIST;  // 通知栏显示喜欢
        String ITEM_XUID_OPEN = "xuid_android_open";
        String ITEM_SMSDK_OPEN = "smsdk_android_open_v2";
        String ITEM_SMSDK_UPDATE_OPEN = "smsdk_android_update_open";
        String ITEM_CLEAN_PLAY_SDK_OPEN = "clean_play_sdk_open";
        String ITEM_XUID_OKHTTP_CLIENT_OPEN = "xuid_okhttp_client_open"; // 是否使用主app OkHttpClient
        String ITEM_IS_OPEN_PACKAGE_MANAGER_HOOK = "is_open_package_manager_hook_new";
        String ITEM_IS_OPEN_PACKAGE_MANAGER_HOOK_FOR_VIVO = "is_open_package_manager_hook_for_vivo";

        String ITEM_WEB_VIEW_PROVIDER_POOL_ENABLED = "web_view_provider_pool_enabled_android";

        String ITEM_ADD_SPAN_BACKGROUND_IMPORTANT = "item_add_span_background_important"; // 售后页是否添加标签背景

        String ITEM_ANDROID_HYBRID_FIX_MONITOR = "item_android_hybrid_fix_monitor"; // h5白屏监控tab场景优化开关

        String ITEM_ALBUM_PAGE_SIZE = "album_track_page_size_for_android";

        String ITEM_OPEN_QQ_GAME = PreferenceConstantsInOpenSdk.ITEM_OPEN_QQ_GAME;

        String ITEM_IS_ALLOW_GET_APP_LIST = "is_allow_get_app_list";
        String ITEM_GET_APP_LIST_INTERVAL_TIME_S = "get_app_list_interval_time_s";

        String ITEM_OPEN_SP_DATA_CHECK = "sp_data_check_open";
        String ITEM_OFFLINE_ANDROID_NEW_CONFIG_DATA = "item_offline_android_new_config_data";
        String ITEM_HYBRID_FIX_UNRESPONSE_BUG_NEW = "item_hybrid_fix_unresponse_bug_new";
        String KEY_BASE_INFO_BLOCK_PLAY = PreferenceConstantsInOpenSdk.KEY_BASE_INFO_BLOCK_PLAY;
        String KET_OPEN_VOLUME_FOR_BASE_INFO = PreferenceConstantsInOpenSdk.KET_OPEN_VOLUME_FOR_BASE_INFO;

        String UPLOAD_BAIDU_URL_TRACE = PreferenceConstantsInOpenSdk.UPLOAD_BAIDU_URL_TRACE;

        String KEY_USING_PCDN_IN_CACHE = PreferenceConstantsInOpenSdk.KEY_USING_PCDN_IN_CACHE;

        String KEY_USE_VOLUME_GAIN_FOR_MINI_PLAYER = PreferenceConstantsInOpenSdk.KEY_USE_VOLUME_GAIN_FOR_MINI_PLAYER;

        String KEY_RESET_TO_IDLE_PLAY = PreferenceConstantsInOpenSdk.KEY_RESET_TO_IDLE_PLAY;

//        String KEY_USE_BAIDU_PCDN = PreferenceConstantsInOpenSdk.KEY_USE_BAIDU_PCDN;
//        String KEY_USE_SDK_PCDN_OUT_CN = PreferenceConstantsInOpenSdk.KEY_USE_SDK_PCDN_OUT_CN;
//        String ITEM_SDK_PCDN_WHITE_DOMAIN_LIST = PreferenceConstantsInOpenSdk.ITEM_SDK_PCDN_WHITE_DOMAIN_LIST;
//
//        String KEY_WHITE_RESPONSE_CODE_LIST_SDK = PreferenceConstantsInOpenSdk.KEY_WHITE_RESPONSE_CODE_LIST_SDK;
        String KEY_WHITE_RESPONSE_CODE_LIST = PreferenceConstantsInOpenSdk.KEY_WHITE_RESPONSE_CODE_LIST;

//        String KEY_USE_SDK_PCDN_FOR_DOWNLOAD = PreferenceConstantsInOpenSdk.KEY_USE_SDK_PCDN_FOR_DOWNLOAD;
//        String ITEM_DOWNLOAD_SDK_PCDN_WHITE_DOMAIN_LIST = PreferenceConstantsInOpenSdk.ITEM_DOWNLOAD_SDK_PCDN_WHITE_DOMAIN_LIST;

        // 压缩请求头配置
        String ITEM_REQUEST_SIMPLIFY_HEADER_KEYS = "xdcs_simplify_header_keys";
        String ITEM_REQUEST_SIMPLIFY_URL_PATH = "xdcs_simplify_url_path";
        // 压缩请求头配置(nyx)
        String ITEM_REQUEST_SIMPLIFY_HEADER_KEYS_NYX = "nyx_simplify_header_keys";
        String ITEM_REQUEST_SIMPLIFY_URL_PATH_NYX = "nyx_simplify_url_path";
        // 压缩请求头配置json,移除无用header（key:path, value:header）
        String ITEM_REQUEST_SIMPLIFY_REMOVE_HEADER = "simplify_request_remove_header";
        // Nonce缓存开关
        String ITEM_NONCE_MULTI_OPEN = "nonce_multi_open";

        String ITEM_PCDN_FAIL_COUNT_IN_WINDOW = PreferenceConstantsInOpenSdk.ITEM_PCDN_FAIL_COUNT_IN_WINDOW;
        String ITEM_PCDN_FAIL_TIME_IN_WINDOW = PreferenceConstantsInOpenSdk.ITEM_PCDN_FAIL_TIME_IN_WINDOW;

        String ITEM_PCDN_WHITE_DOMAIN_LIST = PreferenceConstantsInOpenSdk.ITEM_PCDN_WHITE_DOMAIN_LIST;
        String ITEM_PCDN_LAG_COUNT_IN_WINDOW = PreferenceConstantsInOpenSdk.ITEM_PCDN_LAG_COUNT_IN_WINDOW;
        String ITEM_PCDN_LAG_TIME_IN_WINDOW = PreferenceConstantsInOpenSdk.ITEM_PCDN_LAG_TIME_IN_WINDOW;

        String KEY_COLLECT_USE_DELAY_CREATE = "key_collect_use_delay_create";

        String KEY_USE_SMART_QUALITY = PreferenceConstantsInOpenSdk.KEY_USE_SMART_QUALITY;

        String KEY_GET_URL_FROM_SERVER = PreferenceConstantsInOpenSdk.KEY_GET_URL_FROM_SERVER;
        String KEY_GET_URL_FROM_INFO_LIST = PreferenceConstantsInOpenSdk.KEY_GET_URL_FROM_INFO_LIST;

        String KEY_VIVID_ALBUM_WHITE_LIST = "key_vivid_album_white_list";
        String KEY_OPEN_CREATE_NEW_DEVICE = "key_open_create_new_device";
        String KEY_NOT_PRE_LOAD_PLUGIN_LIST = "not_pre_load_plugin_list";   // 禁止预加载插件列表
        String KEY_IS_NEED_CHECK_RATE_LIMITS = "is_need_check_rate_limits"; // 插件预加载是否要检查带宽
        String KEY_ENABLE_X_XMLY_RESOURCE = "enable_x_xmly_resource"; // xdcs请求是否添加x_xmly_resource对应cookie

        String KEY_OKHTTP_MAX_IDLE_CONNECTIONS = PreferenceConstantsInOpenSdk.KEY_OKHTTP_MAX_IDLE_CONNECTIONS;

        String KEY_NO_FIX_TIMEOUT_EXCEPTION_FOR_OKHTTP = PreferenceConstantsInOpenSdk.KEY_NO_FIX_TIMEOUT_EXCEPTION_FOR_OKHTTP;

        String KEY_USE_OLD_H5_TIP_ERROR_VIEW = "use_old_h5_tip_error_view";

        String KEY_HEART_BEAT_INTERVAL_TIME = PreferenceConstantsInOpenSdk.KEY_HEART_BEAT_INTERVAL_TIME;

        String KEY_SOUND_PATCH_VOLUME_BALANCE = PreferenceConstantsInOpenSdk.KEY_SOUND_PATCH_VOLUME_BALANCE;

        String KEY_EQUAL_TO_XDCS_FOR_PLAY_DURATION = PreferenceConstantsInOpenSdk.KEY_EQUAL_TO_XDCS_FOR_PLAY_DURATION;

        String KEY_HEART_BEAT_CHECK_PLAYING = PreferenceConstantsInOpenSdk.KEY_HEART_BEAT_CHECK_PLAYING;

        String KEY_MODE_SLEEP_CLEAR_DURATION = PreferenceConstantsInOpenSdk.KEY_MODE_SLEEP_CLEAR_DURATION;

        String KEY_PLAY_TRACE_SOUND_SWITCH_UPLOAD = PreferenceConstantsInOpenSdk.KEY_PLAY_TRACE_SOUND_SWITCH_UPLOAD;

        String KEY_VIDEO_PLAY_TRACE_SOUND_SWITCH_UPLOAD = PreferenceConstantsInOpenSdk.KEY_VIDEO_PLAY_TRACE_SOUND_SWITCH_UPLOAD;

        String KEY_UPLOAD_COLD_STARTUP_TRACE_FOR_PUSH = PreferenceConstantsInOpenSdk.KEY_UPLOAD_COLD_STARTUP_TRACE_FOR_PUSH;

        Map<String, Boolean> KEY_BOOL_ITEMS = PreferenceConstantsInOpenSdk.KEY_CONFIG_CENTER_BOOL_ITEMS;

        Map<String, String> KEY_STRING_ITEMS = PreferenceConstantsInOpenSdk.KEY_CONFIG_CENTER_STRING_ITEMS;

        Map<String, Integer> KEY_INT_ITEMS = PreferenceConstantsInOpenSdk.KEY_CONFIG_CENTER_INT_ITEMS;

        String KEY_USE_GAIAX_FOR_RECOMMEND_PAGE = PreferenceConstantsInOpenSdk.KEY_USE_GAIAX_FOR_RECOMMEND_PAGE;
        String KEY_HIGH_PROPERTY_FIREWORK_DELAY = "high_property_firework_delay";
        String KEY_NEW_PLAY_STATISTICS_UPLOAD = PreferenceConstantsInOpenSdk.KEY_NEW_PLAY_STATISTICS_UPLOAD;

        String KEY_PLAY_COUNT_UPLOAD_TICKET = PreferenceConstantsInOpenSdk.KEY_PLAY_COUNT_UPLOAD_TICKET;

        String KEY_PLAY_STATISTICS_UPLOAD_TICKET = PreferenceConstantsInOpenSdk.KEY_PLAY_STATISTICS_UPLOAD_TICKET;

        String KEY_SHORT_PLAY_FREE_LISTEN_ENABLED = PreferenceConstantsInOpenSdk.KEY_SHORT_PLAY_FREE_LISTEN_ENABLED;

        String KEY_NEED_CHECK_UPLOAD_XLOG_STATISTICS = PreferenceConstantsInOpenSdk.KEY_NEED_CHECK_UPLOAD_XLOG_STATISTICS;

        String KEY_SHOW_RECOMMEND_PLAY_LIST = PreferenceConstantsInOpenSdk.KEY_NEW_PLAY_STATISTICS_UPLOAD;
        String KEY_FEED_HOME_RECORD = "feed_home_record"; // flutter是否预加载页面
        String KEY_VIDEO_PICK_LAZY = "video_pick_lazy"; // video_pick_lazy

        String KEY_LAUNCH_DELAY = "launch_delay"; // video_pick_lazy

        //新用户兴趣卡片引导顺序
        String NEW_USER_INTEREST_CARD_LOAD_STRATEGY = "new_user_interest_card_load_strategy";
        String NEW_USER_INTEREST_CARD_LOAD_STRATEGY_NEW = "interest_card_first";

        //渠道分包归因
        String NEW_USER_CHANNEL_CUSTOMIZATION_INTEREST_CARD = "new_user_channel_customization_interest_card";

        // 是否同步获取是否支持vivid的能力
        String CHECK_CAN_SUPPORT_VIVID_ASYNC = "check_can_support_vivid_async";

        // 动态处理size变化
        String DYNC_HANDLE_SIZE_CHANGE = "dync_handle_size_change";

        // 测评分享是否使用rn
        String VOTE_SHARE_USE_RN = "vote_share_use_rn";

        // 初始化mob 初始化sdk
        String INIT_MOB_LOGIN_SDK = "init_mob_login_sdk_new";

        // 创建没有代理的okhttp是否是重新创建的
        String ITEM_OKHTTP_NOT_PROXY_USE_OLD = "item_okhttp_not_proxy_use_old";

        String ITEM_KEEP_ALIVE_ON_BACKGROUND = "item_keep_alive_on_background";
        String ITEM_HOTFIX_OPEN_REPORT_LOG = "item_hotfix_open_report_log_202306";
        String ITEM_HOTFIX_CLOSE_RETRY_COMPILE = "item_hotfix_close_retry_compile_202306";
        // 是否延时上报互动卡片
        String ITEM_DELAY_INTERACTION_SHOW_TRACE = "item_delay_interaction_show_trace";
        String ITEM_USE_JAVA_DEVICE_TOKEN = "item_use_java_device_token";
        String ITEM_USE_JAVA_ANDROID_ID = "item_use_java_android_id";
        String ITEM_USE_JAVA_TO_LOWER_TYPES = "item_use_java_to_lower_types";
        String ITEM_IS_USE_V3_IN_SCREEN_READER = "is_use_v3_in_screen_reader"; // 读屏模式切换到v3推荐页
        String ITEM_HTTP_COVERT_WHITE_LIST = "http_covert_white_list"; // webview加载http转https白名单
        String ITEM_IS_NEED_CONVERT_HTTPS = "is_need_convert_https"; // 是否需要convert https
        String ITEM_PLAY_PAGE_SHAKE_SPEED = "playPageShakeSpeedAndroid"; // 贴片摇一摇检测速度
        String ITEM_IS_HOST_USE_HTTPS = "is_host_use_https"; // 普通请求强制切换https
        String ITEM_IS_UNKNOWN_HOST_FIX_OPEN = "is_unknown_host_fix_open"; // unknow host 修复开关
        String ITEM_TO_LISTEN_GUIDE_CLOSE = "item_to_listen_guide_close"; // 是否关闭播放页待播引导

        String ITEM_ROBUST_SETTINGS = "item_robust_settings";
        String ITEM_RN_ONLY_ONE_PAGE = "item_rn_only_one_page"; // RN 打开多个页面仅保留一个页面
        String ITEM_NEW_USER_ADFREE_CLOSE = "item_new_user_adfree_close"; // 新用户免广告功能是否关闭,true为关闭
        String ITEM_CLOSE_POSITON_AND_SDK_ADVERTISING_FREE = "close_positon_and_sdk_advertising_free"; // 新用户免广告功能是否关闭,true为关闭

        String OPEN_ANDROID_PLAY_PAGE_V3 = "android_playpage_v3";//v3播放页开关
        String ITEM_PLAY_PAGE_HALF_REQ = "playPageHalfScreenReq";
        String ITEM_PLAY_PAGE_HALF_REQ_VALID_VERSIONS = "playPageHalfScreenReqValidVersions";
        String ITEM_USE_IMAGE_MANAGER_FIX_BLACK_REMOTE = "item_use_image_manager_fix_black_remote";

        String ITEM_REALTIME_STATISTICS_INVERVAL = "TrackRealTimeStatisticsInterval";
        String KEY_DARK_MODE_INIT_SOURCE = "key_dark_mode_init_source"; // 暗黑模式初始暗黑值数据源
        String ITEM_PLAY_WIDGET_GUIDE_INTERVAL_DAY = "item_play_widget_guide_interval_day"; // 播放页桌面小组件引导入口出现间隔时间(小时) 线上关闭不开
        String ITEM_PLAY_WIDGET_GUIDE_INTERVAL_NEW = "item_play_widget_guide_interval_new"; // 播放页桌面小组件引导入口出现间隔时间(小时) 新

        String ITEM_FIX_TTS_TIMBRE_SWITCH_ERROR = "fix_tts_timbre_switch_error";
        String ITEM_WIDGET_SUBSCRIBE_GUIDE_SHOW = "item_widget_subscribe_guide_show";

        String ANDROID_ITEM_RANK_USE_NEW_RANK3 = "android_item_rank_use_new_rank3";
        String ANDROID_WIDGET_VIVO_ONEKEY_ADDA = "android_widget_vivo_onekey_adda";
        String ANDROID_WIDGET_VIVO_ONEKEY_VERSION = "android_widget_vivo_onekey_version";
        String ITEM_WIDGET_SUBSCRIBE_GUIDE_CLOSE_INTERVAL = "item_widget_subscribe_guide_close_interval"; // 小组件引导关闭后出现的时间间隔小时

        String ITEM_SPLASH_AD_TOTAL_TIMEOUT = "splash_ad_total_timeout";
        String ITEM_LAUNCH_AD_TIMEOUT_ENABLE = "launch_ad_timeout_enable";
        String ITEM_SPLASH_AD_FORCE_JUMP_ENABLE = "splash_ad_force_jump_enable";
        String ITEM_SPLASH_AD_TOTAL_TIMEOUT_AB = "splash_ad_total_timeout_ab";
        String ITEM_REMOVE_ALL_PAGE_ON_TO_TOP_PAGE = "item_remove_all_page_on_to_top_page";
        String KEY_URL_COOKIE_VERIFY_MODE = "url_cookie_verify_mode"; // 金融信息审核级别
        String KEY_IS_NOT_USE_NEW_INTERNAL_DOMAIN_CHECK = "isNotUseNewInternalDomainCheck"; // 是否禁止使用新的内部域名校验
        String ITEM_XLOG_IS_USE_HTTPS = "is_xlog_use_https"; // xlog上报是否采用https
        String KEY_IS_NOT_ALLOW_GIF_LOAD_AGAIN = "is_not_allow_gif_load_again"; // 禁止git多次加载
        String KEY_LOAD_TAB_INTERVAL_TIME = "load_tab_interval_time"; // 加载首页tab时间间隔
        String ITEM_EXTRA_WEB_CLIENT_INTERCEPT_ENABLE = "extra_web_client_intercept_enable";// NativeHybridFragment customWebClient urlOverriding real intercept enable
        String ITEM_MAX_PAGE_ARRIVE_DURATION = "max_page_arrive_duration";  // 首页达到最大的时间
        String ITEM_MEDIA_SESSION_USE_NEW_THREAD = XmNotificationCreater.ITEM_MEDIA_SESSION_USE_NEW_THREAD;
        String ITEM_FORCE_COMPAT_ANDROID13_PICK_VIDEO = "force_compat_android13_pick_video";//强制适配Android13视频选择
        String ITEM_FORCE_COMPAT_ANDROID13_PICK_PHOTO = "force_compat_android13_pick_image";//强制适配Android13图片选择
        String ITEM_ELDER_CARE_CONFIG_IS_OPEN = "elder_care_config_is_open_v2";  // 华为老年模式是否打开
        String ITEM_BIND_DATA_SET_USEINFO = "item_bind_data_set_useinfo"; // 是否在bindData 的时候设置setUser
        String ITEM_HIDE_PLAY_TO_SHOW_PLAYBAR = "item_hide_play_to_show_playbar"; // 播放或者暂停的时候是否展示播放bar
        String ITEM_HIGH_ENERGY_SHOW_MODE = "item_high_energy_show_mode"; // 高能时刻展示的样式
        String ZONE_EXIT_COMMUNITY_FLITER_VIP = "zone_exit_community_fliter_vip"; // vip是否关闭圈子
        String ITEM_REMOVE_DYNA_FOLLOW_ANCHOR = "item_remove_dyna_follow_anchor";   // 是否动态展示关注列表
        String ITEM_CHILD_PROTECT_DIALOG_DISMISS_TIME = "child_protect_dialog_dismiss_time"; // 儿童保护弹窗消失时间
        String ITEM_LOGIN_ADD_TICKER_TOKEN = "item_login_add_ticker_token";   // 登录添加ticker
        String ITEM_RECORD_DRAFT_GSON_LIMIT = "item_record_draft_gson_limit";   // 草稿箱gson解析限制
        String ITEM_ACTIVITY_DIALOG_IN_COMMERCIAL_DIALOG_USE_FIREWORK = "activity_dialog_in_commercial_album_use_firework"; // 自制页活动弹窗是否使用统一弹屏管理
        String ITEM_ACTIVITY_DIALOG_IN_COMMON_DIALOG_USE_FIREWORK = "activity_dialog_in_common_album_use_firework"; // 专辑页活动弹窗是否使用统一弹屏管理
        String UPLOAD_P_LIST_CHECK_64 = "upload_p_list_check_64"; // 是否上传pList校验64位
        String ITEM_CUSTOM_ALBUM_VIDEO_LIST_PAGE_SIZE_FIX_SWITCH = "custom_album_video_list_page_size_switch";
        String ITEM_CUSTOM_ALBUM_VIDEO_LIST_FORCE_LOAD_NET_DATA = "custom_album_video_list_force_load_net_data";

        String ITEM_MIN_KEYBOARD_HEIGHT_DP = "min_keyboard_height_dp"; // 软键盘状态监听，最小软键盘高度，经验值
        String ITEM_IGNORE_COMMERCIAL_BEHAVIOR_SOURCE = "ignore_commercial_behaivior_source";
        String ITEM_LOGIN_OUT_DELAY = "item_login_out_delay";
        String ITEM_RECORD_ATTEMPT_ON = "item_record_attempt_on";
        String ITEM_FIX_VIDEO_TRANSCODE_ERR = "item_fix_video_transcode_err";
        String ITEM_FIX_VIDEO_CIRCLE_TRANSCODE_ERR = "item_fix_video_circle_transcode_err_new";//圈子剪辑失败开关


        String ITEM_HW_INTENT_DONATE_CONFIG = "item_hw_intent_donate_habit_config";
        String ITEM_HW_SID_UPLOAD = "hw_sid_upload";
        String ITEM_USE_GESS_SDK = "item_use_gess_sdk"; // 是否使用极验SDK
        String ITEM_LOAD_HISTORY_WATING_PLAY_PROCESS = "item_load_history_wating_play_process";
        String USE_OLD_OAID = "use_old_oaid"; // 是否使用加密的oaid
        String ITEM_PRELOAD_ONE_KEY_LOGIN = "item_preload_one_key_login";
        String ITEM_HOOK_WEBSETTING_USER_AGENT = "hook_websetting_user_agent";
        String ITEM_GET_WEB_UA_USE_THREAD = "get_web_ua_use_thread";
        String APP_ARRIVE_RECORD_AD_SHOW = "app_arrive_record_ad_show"; // 首页到达是否记录广告正在展示

        String ITEM_IF_OPEN_DOC_OPT = "key_if_open_doc_opt";

        String ITEM_PLAYBAR_MIX_FIX = "item_playbar_mix_sleep_mode_fix";
        String ITEM_CHANGE_5G_TO_4G = "item_change_5g_to_4g";
        String ITEM_NEED_TO_KNOW_FILES = "key_need_to_use_key_size";
        String ITEM_NEED_TO_KNOW_SOS = "key_need_to_use_key_size1";

        String ITEM_USE_MNN = "use_mnn_v2";
        String ITEM_FIX_FREE_FLOW_TYPE_ERROR = "item_fix_free_flow_type_error";
        String FORCE_CHANGE_QUICK_LOGIN_TYPE = "force_change_quick_login_type";

        String ITEM_ITEM_MINE_PAGE_ANCHOR_FIRST_LOAD_CACHE = "item_mine_page_anchor_first_load_cache";

        String ITEM_RECENT_READ_RN_ITING = "recent_read_rn_iting_1";
        String ITEM_CENTER_BIG_PIC_AD_MAX_DOFRAME_COUNT = "item_center_big_pic_ad_max_doframe_count";
        String ITEM_TURN_OVER_NAME = "turn_over_name";

        String ITEM_RTC_SDK_SWITCH = "rtc_sdk_switch_football";
        String ITEM_MAX_QUICK_COLLECT_REQUEST_TIME = "max_quick_collect_request_time";
        String ITEM_REMOVE_QUICK_LISTEN_FRAGMENT_ONPLAYER_MANAGER = "remove_quick_listen_fragment_onplayer_manager";
    }

    interface Group_mermaid {
        String GROUP_NAME = "mermaid";
        String ITEM_XLOG_ANDROID_SEND = "xlog:android:send"; // 是否发送xlog 的开关
        String ITEM_XLOG_ANDROID_WRITE = "xlog:android:write";
    }

    interface Group_ad {
        String GROUP_NAME = "ad";
        String ITEM_INCENTIVE_SHAKE_SPEED = "incentiveShakeSpeed"; // 激励视频摇一摇检测速度
        String ITEM_PLAY_SKIN_CLICK_HOT_ZONE = "SkinClickHotZone";//互动皮肤点击热区
        String ITEM_PLAY_SKIN_CLICK_HOT_ZONE_NEW = "SkinClickHotZoneRedesign";//互动皮肤点击热区
        String ITEM_SOUND_PATCH_TIME_OUTMS = "soundPatchTimeoutMs";        //声音贴片超时时间
        String ITEM_COOPERATIONADVERTISEMENT = "cooperationAdvertisement";  // 我的页面广告合作配置
        String ITEM_CLOSEDPOSITIONNAMES = "closedPositionNames";    // 关闭的广告位
        String ITEM_PRELOADCACHECAPACITY = "preloadCacheCapacity";  // 预缓存容量限制
        String ITEM_GDTREQUESTTIMEOUTMS = "gdtRequestTimeoutMs";    // 广点通超时时间配置
        String ITEM_ENABLEADMD5 = "enableAdMd5";        // 广告数据的MD5校验开关
        String ITEM_COMMENTADCONFIG = "commentAdConfig";    // 可出评论广告的最小评论数，开始插入位置，间隔参数
        String ITEM_FOLLOW_HEART_CONFIG = "followHeartConfig";    // 随心听样式配置
        String ITEM_CLICKED_AFTER_SHOWAD = "clickedAfterShowAd";  // 当真正展示了广告才可以点击
        String ITEM_FORWARD_VIDEO_CONFIG = "forwardVideoConfig";  // 前插视频广告配置
        String ITEM_TASKCENTER_VIDEO_CONFIG = "TaskCenterVideoConfig";  // 积分中心激励视频广告配置
        String ITEM_UN_LOCK_PAID_VIDEO_CONFIG = "unLockPaidVideoConfig";  // 解锁视频广告配置
        String ITEM_MAX_THIRD_SDK_VIDEO_LOAD_TIME = "maxThirdSDKVideoLoadTime";  // 第三方视频广告最大的加载时间
        String ITEM_NEED_SHOW_APPDOWNLOAD_REMIND_DIALOG = "needShowAppDownloadRemindDialog";  // 是否显示广告下载app提示弹窗
        String ITEM_REMOVE_AD_DIALOG_CONFIG = "removeAdDialogConfig";   // 提示移除广告弹窗
        String ITEM_HOME_FEED_SDK_REQUEST_TIME = "HomeFeedSDKRequestTime";   // 首页信息流sdk请求超时时间单位ms
        String ITEM_PLAY_LARGE_SDK_REQUEST_TIME = "PlayLargeSDKRequestTime";   // 播放页大图sdk请求超时时间单位ms
        String ITEM_PRE_REQUEST_SDK_ENABLE = "preRequestSDKEnable"; // 三方广告预缓存
        String ITEM_LAUNCH_AD_SKIP_BUTTON_TOUCH_PADDING_TIME = "launchAdSkipButtonTouchPadding";   // 开屏广告跳过按钮点击区域相对于按钮的边距
        String ITEM_SHOW_FREE_AD_ENTRANCE_INTERVALTIME = "showFreeAdEntranceIntervalTime";  // 展示免广告入口的间隔时间
        String ITEM_MAX_SHOW_FREE_AD_ENTRANCE_COUNT = "maxShowFreeAdEntranceCount"; // 最多展示免广告入口的次数
        String ITEM_FORWARD_VIDEO_ENDCARD_TIME = "ForwardvideoEndcardtime"; // 免广告配置视频播放倒计时结束之后停留多少秒
        String ITEM_COMMENT_SDK_REQUEST_TIME = "CommentSDKRequestTime"; // 评论页SDK请求超时时间
        String ITEM_SOUND_PATCH_VIDEO_MAX_DURATION_TIME = "soundPatchVideoMaxDuration"; // 视频贴片最大的视频长度
        String ITEM_CAN_RECORD_SDK_RES = "adResRecord"; // 是否可以收集sdk res
        String ITEM_CHECK_SDK_RECORD = "checkSDKRecord";  // 检查sdk是否展示上报的时间
        String ITEM_NOREALSHOW_DEFER_AD = "noRealShowDeferAd";  // 规定时间内未真实曝光时是否顺延展示广告

        String ITEM_SOUND_AD_PLAYING_FORBID_PLAY_BUTTON_ENABLE_V2 = "forbidPlayButtonV2";  // 有声贴片广告播放时是否需要禁止播放按钮v2
        String ITEM_PLAY_GAME_POPUP = "PlayGamePopup";  // 边听边玩弹层入口开关

        String ITEM_GYROSCOPE_ENABLE = "gyroscopeEnable";  // 开屏h5广告是否可以根据陀螺仪的旋转暂停倒计时
        String ITEM_CAN_REQUEST_COMMENT_AD = "canRequestComment";  // 播放页是否请求评论广告

        String ITEM_CAN_REQUEST_NEW_COMMENT_AD = "canRequestNewCommentAd";  // 新播放评论页能否请求广告(预留)
        String ITEM_COMMENT_AD_PAUSE_RESET_ENABLE = "commentAdPauseResetEnable";  // 新播放评论页广告在页面暂停时 执行reset的开关(预留)

        String ITEM_AD_WIDTH_HEIGHT_RATIO = "AdWidthHeightRatio";  // 广告素材自渲染-宽高比
        String ITEM_AD_SECOND_FLOOR_AD_TIME_MS = "SecondFloorAdTimeMs"; // 下拉二楼广告停留时间
        String ITEM_UNLOCK_SOUNDPATCH = "unlockSoundPatch";     // 解锁提示声音
        String ITEM_ENABLE_UNLOCK_SOUNDPATCH = "enableUnLockSoundPatch";// 开启解锁声音提示
        String ITEM_AD_GAME_ENTRY_BLACK_CATEGORY = "GameEntryBlackCategory"; // 边听边玩入口频道黑名单
        String ITEM_MAX_APK_SIZE_TO_SHOW_DIALOG = "maxApkSizeToShowDialog"; // 超过此大小下载app的时候需要弹窗提示-单位M
        String ITEM_AD_SHOWING_NEED_REQUEST = "adShowingNeedRequest"; // 广告展示了是否需要请求
        String ITEM_AD_SOUND_PATCH_FRAGMENT_SHOW_JUDGE = "soundPatchFragmentShowJudge"; // 贴片暂停和二曝请求是否需要判断播放页状态

        String ITEM_AD_CHECK_PLAY_STATE_ENABLE = "adCheckPlayStateEnable"; // 是否检查视频播放状态，当命中频控优化时
        String ITEM_AD_RESET_HIDEANMATION_ENABLE = "adResetHideAnmationEnable"; // 是否重置animation

        String ITEM_AD_HIDE_REMOVE_AD_ENABLE = "adHideRemoveAdEnable"; // hide时是否重置删除广告resource
        String ITEM_AD_PREPARED_NEED_REQUEST = "adPreparedNeedRequest"; // 广告视频准备中是否需要请求
        String ITEM_ALBUM_AD_USE_NEW_STYLE = "albumAdUseNewStyle"; // 专辑通知广告是否用新的类型
        String ITEM_PLAY_AD_SKIP_BUTTON_PADDING_TIME = "playAdSkipButtonPadding";   // 声音贴片跳过按钮点击区域相对于按钮的边距
        String ITEM_PLAY_AD_SKIP_BUTTON_PADDING_NO_COUNTDOWN_TIME = "playAdSkipButtonPaddingNoCountDown";   // 声音贴片跳过按钮点击区域相对于按钮的边距(没有倒计时)
        String ITEM_SOUNDPATCH_SHOW_COUNTDOWN_TIME = "soundPatchShowCountDown";   // 有声音贴片是否显示倒计时
        String ITEM_NO_SOUNDPATCH_SHOW_COUNTDOWN_TIME = "noSoundPatchAdShowCountDown";   // 无声贴片是否显示倒计时
        String ITEM_FLOWER_SOUND_PATCH_CLOSE_CONFIG = "flowerSoundPatchCloseConfig"; // 通栏撒花贴片关闭配置,包括关闭优先级及关闭热区
        String ITEM_SOUNDPATCH_WAIT_CLOSE_EXPIRE = "soundPatchWaitCloseExpire"; // 有声视频贴片x秒可关闭

        String ITEM_AD_FREE_DIALOG_CONFIG = "AdFreeDialogConfig";   // 免广告弹窗配置
        String ITEM_USE_NEW_PLAYER_ANIM = "UseNewPlayerAnim";   // 播放页泡泡条新版动画是否使用
        String ITEM_VIDEO_CACHE_ENABLE = "videoCacheEnable";    // 广告视频是否添加缓存
        String ITEM_DOWNLOAD_APK_INSTALL_WAIT_TIME = "downloadApkInstallWaitTime";    // 下载apk后， 亮屏拉起安装的间隔时间

        String ITEM_INIT_SDK_CONFIG = "initSdkConfig";    // 广告SDK初始化配置
        String ITEM_INIT_DSPSDK_CONFIG = "androidDspConfig";    // dspsdk是否允许初始化配置


        String ITEM_CAN_PRELOAD_WEBVIEW = "canPreloadWebView";    // 是否可以预加载webView
        String ITEM_CLOSE_PRECIS_EYE = "closePrecisEyeNew";    // 是否关闭天眼
        String ITEM_PRECIS_EYE_RECORD_FULL = "PrecisEyeRecordFull";    // 天眼是否需要全量上报
        String ITEM_START_COUNT_DOWN_DURATION = "startCountDownDuration";    // 是否可以预加载webView
        String ITEM_RECORD_NO_SHOW_LOG = "recordNoShowLog";    // 记录没有展示的log
        String ITEM_RECORD_NO_SET_CLICK_LOG = "recordNoSetClick";    // 记录没有点击的log

        String ITEM_NEED_SHOW_ANCHOR_AD_TAG = "AdNeedConfigTag"; // 是否需要显示声播广告标签
        String ITEM_ANCHOR_AD_TAG_STYLE = "AdConfigTagImages"; // 声播广告标签列表

        String ITEM_AD_CHASE_RECOMMEND_CONFIG = "chaseRecommendConfig"; // "更多推荐"浮标频控功能 N次/M天，M天内至多出N次该提醒
        String ITEM_DAZZLING_EXCLUSIVE = "DazzlingExclusiveConfig";    // 炫屏和touch是否需要排重
        String ITEM_DAZZLING_MAX_SHOW_COUNT = "DazzlingMaxShowCount";    // 炫屏最大的展示次数
        String ITEM_DEFAULT_MARKET_PACKAGE_NAME = "defaultMarketPackageName";    // 默认的应用市场的包名
        String ITEM_AD_CSJ_REQUEST_SPLASH_USE_THREAD = "csjRequestSplashUseThread"; // 穿山甲请求使用线程
        String ITEM_AD_CSJ_SET_USE_CACHE = "csjSetUseCache"; // 设置穿山甲是否使用cache
        String ITEM_PLAY_DAZZLING_MAX_SHOW_COUNT = "PlayFragmentDazzlingMaxShowCount"; // 播放页炫屏最大的展示次数
        String ITEM_BIG_SCREEN_DAZZLING_MAX_SHOW_COUNT = "BigScreenFragmentDazzlingMaxShowCount"; // 巨幕广告炫屏最大的展示次数

        String ITEM_AD_GAMECENTER_ENTRY_URL_CONFIG = "GameCenterEntryUrlConfig"; // 播放页弹层小游戏入口,游戏中心跳转链接配置
        String ITEM_AD_GDT_PRELOAD_CONFIG = "gdtPreLoadConfig"; // 广点通预加载配置

        /**
         * 全站畅听相关配置
         */
        String ITEM_AD_UNLOCK_VIP_TRACK_DIALOG = "unlockVipTrackDialog"; // 全站畅听弹窗
        String ITME_AD_UNLOCK_VIP_VIDEO_TOP_CONTENT = "unLockVipVideoTopContent"; // 全站畅听广告顶部文案
        String ITME_AD_FORWARD_VIDEO_CONFIG_NEW = "forwardVideoConfigNew"; // 前插视频顶部黑条配置
        String ITEM_AD_UNLOCK_VIP_VIDEO_AUTO_CLOSE_TIME = "unlockVipAdAutoCloseTime"; //全站畅听竖屏解锁广告自动关闭时间
        String ITEM_CLOSE_NEED_ALERT = "listenStationsCloseNeedAlert"; // 全站畅听提前点击关闭按钮时是否展示挽留弹窗
        String ITEM_HORIZONTAL_AD_FULL_CLICK = "rewardVideoFullClick";// 横版激励视频广告是否支持全屏点击（视频区域不可点击）
        String ITEM_REWARD_VIDEO_SUPPORT_VIP = "rewardVideoSupportVip";// 激励视频是否支持开会员入口
        String ITEM_REWARD_VIDEO_AUTO_PULL_DIALOG_CONFIG = "rewardVideoAutoPullDialogConfig";// 畅听三激励视频页面自动拉起浮层配置
        String ITEM_REWARD_VIDEO_TIME_UNLOCK_TOP_CONTENT = "rewardVideoTimeUnlockTopContent";// 畅听四激励视频页面广告顶部文案
        String ITEM_REWARD_VIDEO_TIME_UNLOCK_TOP_CONTENT_6 = "rewardVideoTimeUnlockTopContent6";// 畅听六激励视频页面广告顶部文案
        String ITEM_REWARD_VIDEO_POP_DIALOG_CONFIG = "rewardVideoPopDialogConfig";// 畅听六激励视频首页弹窗配置
        String ITEM_REWARD_VIDEO_SNACK_BAR_CONFIG = "rewardVideoSnackBarConfig";// 畅听六激励视频首页二次弹窗配置
        String ITEM_NEW_HOME_SNACK_BAR_CONFIG = "newHomeSnackBarConfig";// 畅听九首页snackBar配置
        String ITEM_REWARD_VIDEO_TWICE_CONFIG = "rewardVideoTwiceConfig";// 畅听八-支持二次解锁配置
        String ITEM_REWARD_VIDEO_TWICE_CONFIG_V2 = "rewardVideoTwiceConfigV2";// 时长模式是否支持二次解锁配置
        String ITEM_REWARD_VIDEO_CLICK_CONFIG = "rewardVideoClickConfig";// 时长模式点击领取配置
        String ITEM_REWARD_VIDEO_DURATION_AND_CLICK_CONFIG = "rewardVideoDurationClickConfig";// 时长模式时长加点击领取配置
        String ITEM_IS_NEED_CONFLICT_TOUCH_AD = "isNeedConflictTouchAd";// 畅听首页相关提示是否要互斥touch广告
        String ITEM_REWARD_VIDEO_POP_DIALOG_JUMP_URL = "rewardVideoPopDialogJumpUrl";// 畅听首页弹窗领取奖励后跳转url
        String ITEM_GDT_ONLY_CLCIK_REWARD = "gdtOnlyClickReward"; // 贴片暂停和二曝请求是否需要判断播放页状态
        String ITEM_REWARD_AGAIN_DIALOG_CONFIG = "rewardAgainDialogConfig";// 畅听再看一个弹窗配置

        String ITEM_HOME_INSPIRE_DIALOG_CONFIG = "homeInspireDialogConfig";// 畅听首页唤端弹窗按钮文案配置
        String ITEM_COIN_INSPIRE_DIALOG_CONFIG = "coinInspireDialogConfig";// 金币唤端弹窗按钮文案配置
        String ITEM_AD_SPLASH_SENSOR_VALUE = "splashSensorValue"; // 开屏广告互动类型旋转的多少度数之后去跳转
        String ITEM_AD_SPLASH_HAS_VIBRATION = "splashHasVibration"; // 开屏广告互动类型旋转后是否震动
        String ITEM_AD_SPLASH_SENSOR_DELAY = "splashSensorDelay"; // 开屏广告互动类型获取sensor值延时的时间
        String ITEM_AD_SPLASH_SENSOR_DIALOG_SHOW_RECORD_CLICK = "splashSensorDialogShowRecordClick"; // 开屏广告互动类型弹窗出现时是否记录点击
        String ITEM_SPLASH_REQUEST_USE_ASYNC = "splashRequestUseAsync"; // 开屏广告请求是否使用同步方法
        String ITEM_SPLASH_SHOW_BANG = "splashShowBang"; // 开屏广告是否适配刘海

        String ITEM_RECOMMEND_DELAY_CHECK_TIME = "recomend_delay_check_time"; // 首页延时检测是否可视的监测时间
        String ITEM_SOUND_PATCH_DELAY_SHOW_TIME = "item_sound_patch_delay_show_time"; // 声音贴片延时展示时间

        String ITEM_SPLASH_SHOW_STRATEGY = "splashShowStrategy";    // 开屏间隔展示时间策略 adLoadingIntervalTime-间隔时间 minTime-单次间隔最短时间  strategy-1. 单次计算  2. 累积计算

        String ITEM_AD_SHOW_SPLASH_AD_LOGO = "showSplashAdLogo"; // 是否展示开屏广告左上角喜马logo
        String ITEM_AD_SHOW_OVER_PERCENT = "showOverPercent"; // 超过多少的时候开始算展示成功了
        String ITEM_AD_SPLASH_ERASER_STYLE_PARAM = "splashEraserStyleParam"; //开机擦除样式相关参数
        String ITEM_AD_FEED_AD_USE_SDK = "feedAdUseSDK"; // 首页信息流是否使用sdk的广告
        String ITEM_AD_SPLASH_FLIP_DISTANCE = "splashFlipDistance"; // 开机滑动跳转样式滑动距离
        String ITEM_AD_SPLASH_FLIP_AREA = "splashFlipArea"; // 开机滑动跳转样式可滑动高度比列
        String ITEM_AD_SPLASH_FLIP_ONLY_UP = "splashFlipOnlyUp"; // 开机滑动跳转样式只能向上滑动
        String ITEM_AD_SPLASH_SHAKE_SPEED = "splashShakeSpeed"; // 开机摇一摇检测速度
        String ITEM_AD_SPLASH_SHAKE_TIP = "splashShakeTip"; // 开机摇一摇提示语
        String ITEM_AD_SPLASH_MAX_SCALE_DISTANCE = "splashMaxScaleDistance"; // 开机静态图片比例和可展示区域比例差值如果小于此值就用之前的加载逻辑,如果大于就使用背景模糊效果
        String ITEM_AD_SPLASH_MAX_SCALE_DISTANCE_NEGATIVE = "splashMaxScaleDistanceNegative"; // 开机静态图片比例和可展示区域比例差值如果小于此值就用之前的加载逻辑,如果大于就使用背景模糊效果
        String ITEM_AD_SPLASH_MAX_SCALE_DISTANCE_ENABLE = "splashMaxScaleDistanceEnable"; // 开机静态图片比例和可展示区域比例差值是否调整

        String ITEM_NEED_SHOW_ICON_AD = "needShowIconId"; //是否展示泡泡条广告

        String ITEM_NEED_COLLECT_BEHAVIOR_WHEN_BACKGROUD_SOUNDS = "needCollectWhenBackgroudSounds"; //是否在闭屏声音时收集用户行为
        String ITEM_CLOSE_SOUND_AD_WHEN_CLOSE_PATCH = "closeSoundAdWhenClosePatch";  // 关闭有声贴片时是否关闭声音
        String ITEM_AD_OTHER_INSTALL_APK_LIST = "AdOtherInstallApkList"; // app启动时，外部监听app安装，apk包名列表
        String ITEM_SPLASH_USE_THREAD_CALLBACK = "splashUseThreadCallBack"; // 开屏广告使用线程进行callback
        String ITEM_CHECK_APK_INSTALL_STATE = "checkApkInstallState";   // 检查apk的安装情况

        String ITEM_IS_AUTO_OPEN_ISNTALL_APP = "isAutoOpenInstallAPP";   // app安装完成后_是否自动拉起已安装app

        String ITEM_READ_CHEATING_PARAMS = "readCheatingParams";   // 读取反作弊参数

        // 负反馈相关
        String ITEM_CLOSE_AD_PADDING = "closeAdPadding"; // 广告关闭按钮热区，区分广告位配置
        String ITEM_CLOSE_AD_NEED_DIALOG = "closeAdNeedDialog"; // 广告关闭时是否需要展示负反馈弹窗
        String ITEM_CLOSE_AD_NEED_DIALOG_BY_POSITION_ID = "closeAdNeedDialogByPositionId";  // 广告关闭时是否需要展示负反馈弹窗， 区分广告位配置
        String ITEM_AD_COMPLAIN_REASONS = "closeComplainReasons"; // 广告投诉二级菜单
        String ITEM_AD_COMPLAIN_REASONSV2 = "closeComplainReasonsV2"; // 广告投诉二级菜单V2
        String ITEM_IMMSER_SKIN_CLOSE_PADDING = "immerSkinClosePadding";   // 沉浸式皮肤广告关闭按钮热区

        String ITEM_USE_NEW_SPLASH = "useNewSplashV3"; // 是否使用新开屏
        String ITEM_SOUND_PATCH_CONTAIN_PLAY_AD = "soundPatchContainPlayAdNew";    // 声音贴片继续播放广告
        String KEY_INTERVAL_SOUND_REQUEST_TIME = PreferenceConstantsInOpenSdk.KEY_INTERVAL_SOUND_PATCH_REQUEST_TIME; // 两次贴片请求间隔时间
        String KEY_INTERVAL_CONTINUE_SOUND_PATCH_REQUEST_TIME = PreferenceConstantsInOpenSdk.KEY_INTERVAL_CONTINUE_SOUND_PATCH_REQUEST_TIME; // 两次贴片请求距离上次continePlay间隔时间
        String ITEM_VIDEO_COMPLETE_TO_PLAY = "videoCompleteToPlay";   // 视频播放完了开始播放

        String ITEM_VI_CLICK_MIN_CLOSE_TIME = "viClickMinCloseTime";   // vi点击的时候最小延迟多长时间关闭
        String ITEM_VI_CLICK_MAX_CLOSE_TIME = "viClickMaxCloseTime";   // vi点击的时候最大延迟多长时间关闭
        String ITEM_VI_CLICK_INTERVAL_TIME = "viClickIntervalTime";   // vi点击触发最小间隔时间
        String ITEM_VI_RECORD_CONFIG = "viRecordConfig";   // vi展示上报和点击上报的配置

        String ITEM_ANDBIDDINCONFIG = "AndbiddinConfig"; // 实时竞价football配置

        String MAX_COOKIE_SIZE_FOR_AD = "maxCookieSizeForAd";   // 广告cookie最大的大小
        String MAX_AD_PLAYER_USE_EXO = PreferenceConstantsInOpenSdk.KEY_AD_PLAYER_USE_EXO;   // 广告播放器使用exo
        String KEY_AD_PLAYER_VOLUME_GRADIENT = PreferenceConstantsInOpenSdk.KEY_AD_PLAYER_VOLUME_GRADIENT;   // 广告播放器播放时音量渐强
        String ITEM_ON_VIDEO_AUDIO_PLAY_TO_RECORD = "videoAudioPlayToRecord";   // 视频声音播放的时候就进行上报
        String SHOW_RECORD_USE_SDK = "showRecordUseSDK"; // 展示上报使用sdk
        String CLICK_RECORD_USE_SDK = "clickRecordUseSDK"; // 点击上报使用sdk
        String ITEM_FEED_AD_SHOW_RECORD_REPEAT = "feedAdShowRecordRepeat"; // 老版单排首页是否重复曝光上报

        /**
         * 是否开启广告激励视频顶部自定义倒计时控件,默认false
         */
        String OPEN_VIDEO_AD_TOP_COUNTDOWN_VIEW = "openVideoAdTopCountdownView";

        /**
         * webview加载时需要带上cookie信息的域名白名单
         */
        String ITEM_WEBVIEW_DOMAIN_WITH_COOKIE = "webviewDomainWithcookie";

        /**
         * openIvRecord 是否打开ivRecord
         */
        String ITEM_OPEN_IV_RECORD = "openIvRecord";
        String ITEM_AD_VOLUME_GAIN_MAX_VALUE = "adVolumeGainMaxValue"; // 广告音量增益最大值
        String KEY_AD_VOLUME_DECREASE_VALUE = PreferenceConstantsInOpenSdk.KEY_AD_VOLUME_DECREASE_VALUE; // 广告音量增益减去的值

        /**
         * 内容推广落地页, 二次点击是否展示下载弹窗， 默认false
         */
        String ITEM_PLAY_WEB_CLICK_SHOW_DIALOG = "playWebClickShowDialog";

        String ITEM_CSJ_SPLASH_SHAKE_SLOTS = "csjSplashShakeSlots"; // 穿山甲摇一摇代码位

        /**
         * G网下是否可以自动播放视频类广告， 默认true（自动播放）
         */
        String ITEM_AD_VIDEO_CAN_AUTO_PLAY_BY_GNET = "adAlwaysPlayVideo";
        String ITEM_WEB_SIMULATE_TOUCH_TIME = "webSimulateTouchTime"; // 落地页模拟跳转时间配置

        String ITEM_LOAD_LRC_AD_ON_HALF = "loadLrcAd"; // 半屏字幕文稿下，要不要展示字幕广告
        String ITEM_SOUND_PATCH_DRAFT_INTERVAL = "soundPatchDraftInterval"; // 贴片关闭时间小于该时间时，半屏切换全屏不请求字幕广告
        String ITEM_FIRST_BANNER_SHOW_CONFIG = "firstBannerShowConfig"; // 焦点图首帧展示时长配置
        String ITEM_SHOW_POP_AD_AFTER_SOUND_PATCH = "showPopAdAfterSoundPatch"; // 是否在贴片广告关闭之后再展示主播气泡广告
        String ITEM_POP_AD_STYLE = "popAdStyle"; // 是否在贴片广告关闭之后再展示主播气泡广告
        String ITEM_REQUEST_POP_AD_ON_RESUME_OR_PAUSE = "requestPopAdOnResumeOrPause";

        String ITEM_CONTENT_AD_AUTO_JUMP_TIME = "contentAdAutoJumpTime"; // 内容推广声音进入播放页到自动拉起落地页的时间间隔（自动拉起需配置时间n>=0，进入播放页后ns后自动拉起落地页；不自动拉起，需配置n<0）

        /**
         * 播放页彩蛋，是否自动点击，全局控制
         */
        String ITEM_PLAY_PAGE_CART_AUTO_CLICK = "playPageCartAutoClick";
        /**
         * 播放页彩蛋，自动点击时间间隔
         */
        String ITEM_PLAY_PAGE_CART_AUTO_CLICK_INTERVAL = "playPageCartAutoClickInterval";
        /**
         * 播放页彩蛋，自动点击，千分比，取值范围1-1000
         */
        String ITEM_PLAY_PAGE_CART_AUTO_CLICK_PER_THOUSAND = "playPageCartAutoClickPerThousand";

        String ITEM_SCREEN_NOSHOW_ADFLAG = "screen_noshow_adflag";   // 巨幕广告是否显示广告标签
        String REQUEST_SOUND_PATCH_FULL_DOC_SECOND_EXPOSURE = "draftFullScreenShowSecondExposeSoundPatch";// 全屏字幕状态，二次曝光场景是否请求贴片广告
        String DOWNLOAD_CLICK_REQUEST_ON_RESUME = "downloadClickRequestOnResume"; // 悬浮展示下载信息时，点击下载按钮返回是否需要正常请求二次曝光
        String ITEM_AD_POP_SHOW_DURATION = "adPopShowDuration"; // 橱窗追投小弹窗广告展示持续时间
        String ITEM_AD_POP_BACK_FOREGROUND_TIME = "adPopBackForegroundTime"; // 橱窗追投小弹窗后台切前台时间间隔，在这个间隔内需要展示小弹窗
        String ITEM_SOUND_AD_REQUEST_DELAY_TIME = "soundAdRequestDelayTime"; // 橱窗追投广告延迟请求时间
        String ITEM_END_PLAY_IF_WINDOW_CLOSE = "endPlayIfWindowClose"; // 追投广告关闭跳过广告声音开关
        String ITEM_INSERT_CENTER_SOUND_AD = "insert_center_sound_ad"; // 中插声音流广告开关
        String KEY_INSERT_CENTER_SOUND_AD_RN_PAGE = "insert_center_sound_ad_rn_page"; // 中插声音流广告开关,rn播放页
        String ITEM_PLAY_FRONT_INSERT_AD = "playFrontInsertAd3"; // 片头冠名广告开关
        String ITEM_PLAY_TAIL_INSERT_AD = "playRearInsertAd"; // 片尾彩蛋广告开关
        String ITEM_COLUMN_LARGE_AD_REALTIME = "columnLargeAdRealtimeRequest";// 播放页大图广告实时请求开关
        String ITEM_REAL_TIME_FEED_AD = "homeRecNewFeedAdAndroid";// 是否开启首页信息流实时化
        String ITEM_REAL_TIME_ANCHOR_FEED_AD = "realTimeAnchorFeedAd";// 是否开启首页猜你喜欢实时化
        String ITEM_REQUEST_RADIO_SOUND_PATCH = "requestRadioSoundPatch";// 是否请求新版广播贴片广告
        String ITEM_AD_USE_HTTPS = "adUseHttps";// 是否使用https协议
        String ITEM_AUTHOR_VENT_FILTER_CONFIG = "authorVentFilterConfig";// 中插音贴主播自主打点气口配置
        String ITEM_FILTER_VENT = "filterVent";// 中插音贴主播自主打点气口配置，开关
        String ITEM_FILTER_INTERVAL = "filterInterval";// 中插音贴主播自主打点气口配置，冷却时间
        String ITEM_WEB_TOP_TOAST_SHOW_TIME = "webTopToastShowTime";// 落地页顶部弹窗提示展示时长
        String ITEM_CATEGORY_FEED_AD_POSITION = "categoryFeedAdPosition";// 频道信息流广告位置
        String ITEM_NEED_INSERT_DAILY_FEED_AD = "insertDailyNewsFeedAd";// 是否开启今日热点频道广告

        String ITEM_ENABLE_INIT_RECORD_TO_OTHER_PROCESS_V2 = "enableInitInstallRecordToOtherProcessV2"; // 是否允许注册监听开关
        String ITEM_NEW_RECOMMEND_FOCUS = "focussmall";// 首页是否使用新版焦点图样式（三张图片重叠样式）
        String ITEM_FOCUS_NEW_HANDLER = "isUseFocusNewHandler";// 焦点图处理流程不走广告sdk

        String ITEM_SCATTER_MUTEX_PLAY_CONTROL = "scatterMutexPlayControl"; // 撒花屏蔽播控热区互斥ab开关
        String ITEM_FIND_NATIVE_AD_UI_STYLE = "findNativeAdShowStyle"; // 首页信息流大图广告样式实验
        String ITEM_SOUND_PATCH_CLOSE_OPTIMIZE = "soundpatch_close_optimize"; // 大图声音贴片关闭优化
        String ITEM_SOUND_PATCH_SHAKE_NAME = "soundPatchShakeName"; // 贴片摇一摇提示文案
        String ITEM_SOUND_PATCH_TIP_SHAKE_SHOW_TIME = "soundPatchTipShakeShowTime"; // 贴片摇一摇展示时长
        String ITEM_AD_REQUEST_TIME_RECORD_SWTCH = "adRequestTimeRecordSwtch"; // 广告请求耗时上报
        String ITEM_AD_SCENE_INTERVAL_TIME = "ad_scene_interval_time"; // 文稿页请求soundpatch频控时长
        String ITEM_AD_SCENE_INTERVAL_ENABLE = "ad_scene_interval_enable"; // 文稿页请求soundpatch频控实验
        String ITEM_AD_LISTEN_CACHE_ENABLE = "ad_listen_cache_enable"; // 缓存闭屏声音广告开关
        String ITEM_AD_FRONT_INSERT_AD_REPORT_ADTRACE = "frontInsertAdReportAdTrace"; // 片头冠名优化是否上报adTrace

        String ITEM_AD_EXPOSURE_INTERVAL_TIME = "ad_exposure_interval_time_v2"; // 贴片短时曝光过滤时长，单位s

        String ITEM_AD_ADX_AD_INTERVAL_TIME_MS = "item_ad_adx_ad_interval_time_ms"; // adx下发的频控时间/间隔时间，单位 ms ---- 贴片频控实验 -----------

        String ITEM_AD_SPLASH_ASYNC_ADX_DSP = "splash_async_adx_dsp_v1"; // 开机并行请求adx和dsp开关
        String ITEM_INTERACTIVE_SOUND_CONFIG = "InteractiveSoundAdConfig";// 声音互动广告相关配置

        String ITEM_AD_EXPOSURE_CLEARED_ENABLE = "ad_exposure_cleared_enable"; // 广告在（上下首、二曝、暂停）被清除的开关

        String ITEM_AD_CLIENT_FREQUENCY_CONFIG = "clientFrequency"; // 闭屏声音频控
        String ITEM_AD_REMOVE_SOUND_PATCH_ENABLE = "ad_remove_sound_patch_enable"; // 刷新组件，请求显示广告，如果主站不显示，是否删除贴片，开关
        String ITEM_AD_VIDEO_FEEDBACK_CLOSE_SWITCH = "ad_video_feedback_close_switch";   // 视频广告负反馈关闭按钮
        String ITEM_AD_SHOW_END_VIEW_WHEN_ERROR_DISABLE = "item_ad_show_end_view_when_error_disable";   // 视频广告播放异常时展示定帧画面
        String ITEM_VIDEO_AD_CLOSE_UNEXPECTED_NOTIFY_ENABLE = "video_ad_close_unexpected_notify_enable";   // 视频广告异常关闭通知播放进程
        String ITEM_AD_SOUNDPATCH_SKIN_GUIDE_TIME = "soundPatchSkinGuideTime";   // S级贴片引导出现时间

        String ITEM_AD_X_AUTO_PLAY_REWARD_VIDEO = "XAutoPlayRewardVideo"; // 播放页自动播放激励视频开关
        String ITEM_AD_MAIN_FEED_SHOW_EXPOSE_RADIO = "adNewExposeRadio";   // 首页信息流广告曝光比配置
        String ITEM_HOME_AD_EXPOSE_PERCENT = "homeAdExposurePercent";   // 首页听单&榜单广告曝光比配置

        String ITEM_AD_CENTER_BIG_PIC_AD_ENABLE = "CenterBigPicAdEnable";   // 新版首页中插大图开关
        String ITEM_AD_SOUNDPATCH_GET_ROOT_LAYOUT_ENABLE = "soundpatch_get_root_layout_enable";   // 贴片获取根布局新方法
        String ITEM_AD_NEW_SOUND_PATCH_STYLE = "playing_xsoundpatch_style";// 新版播放页贴片场景差异化测试开关
        String ITEM_AD_SOUND_PATCH_LARGE_TO_SMALL_TIME = "soundPatchLargeToSmallDuration"; // 新版播放页贴片场景差异化,放大贴片收起时间

        String ITEM_AD_UVE_SWITCH_CONFIG = "UveSwitchConfig"; // uve请求开关

        String ITEM_AD_SOUND_PATCH_JAD_BID_ENABLE = "soundPatchJadBidEnable"; // 京东贴片广告客户端竞价开关
        String ITEM_AD_MINE_AD_OPEN = "minePageShowAd"; // 是否展示我的页广告
        String ITEM_AD_MINE_AD_OPTIMIZE = "mineAdUseNewRefresh"; // 我的页广告请求优化

        String ITEM_AD_SOUND_PATCH_CACHE_ENABLE = "adSoundPatchCacheEnable"; // adx 是否缓存/使用的总开关

        String ITEM_AD_SOUND_PATCH_CLOSE_ENLARGE_ENABLE = "adSoundPatchCloseEnlargeEnable"; // 贴片广告关闭区域增大ab实验开关
        String ITEM_AD_SOUND_PATCH_INTERRUPT_REPORT_ENABLE = "adSoundPatchInterruptReportEnable"; // 贴片广告请求中断上报开关
        String ITEM_AD_SOUND_PATCH_UPDATE_CUR_COMPONENT_ENABLE = "adSoundPatchUpdateCurComponentEnable"; // 贴片广告即时更新当前组件开关
        String ITEM_AD_SOUND_WEB_VIEW_PRE_LOAD_CONFIG = "client_preload_frequency_config_ab"; // webview预加载配置
        String ITEM_AD_CACHE_MAX_SIZE = "adxCacheMaxSize"; // adx 缓存最大数
        String ITEM_SOUND_PATCH_ADXREQUESTTIMEOUTMS = "adxRequestTimeoutConfig";    // adx贴片超时时间配置
        String ITEM_SOUND_PATCH_TRACE_IGNORE_CLOSED_SCREEN = "soundPatchTraceIgnoreClosedScreen"; // 是否展示下载页广告
        String ITEM_FREE_LISTEN_SOUND_PATCH_INTERVAL = "freeListenSoundPatchIntervalV3"; // 畅听时长模式贴片时间间隔
        String ITEM_FREE_UNLOCK_SOUND_PATCH = "free_unlock_sound_patch"; // 畅听时长模式提示音
        String ITEM_UNLOCK_TIME_FIREWORK_PLAN_TYPE_MAP = "unlockTimeFireworkPlanTypeMap"; // 时长解锁计划id和弹屏类型映射表
        String ITEM_UNLOCK_TIME_FIREWORK_SCENE_TYPE_MAP = "unlockTimeFireworkSceneTypeMap"; // 时长解锁场景id和弹屏类型映射表
        String ITEM_LISTEN_TIME_FLOAT_SHOW_RULE = "listenTimeFloatViewShowRule";
        String ITEM_FREE_LISTEN_V2_AB_SWITCH = "free_listen_v2_ab_open_android";
        String ITEM_DSP_SDK_INIT_CONFIG = "androidDspInitConfig"; // 三方sdk是否初始化配置
        String ITEM_CLOSE_APP_ENABLED_FOR_ANDROID = "closeAppEnabledForAndroid";
        String ITEM_FREE_LISTEN_DIALOG_TRACE_ENABLE = "FreeListenDialogTraceEnable";
        String ITEM_SOUND_PATCH_RTB_LIST_OPTIMIZE_ENABLE = "soundPatchRtbListOptimizeEnable";
        String ITEM_AD_COMMON_SHAKE_TIPS = "ad_common_shake_tips";
        String ITEM_FREE_LISTEN_VIEW_CONFIG = "freeListenV2ViewConfig";// 激励视频二级页面相关配置

        String ITEM_FREE_LISTEN_TAB_CONFIG = "freeListenTabConfig";// 畅听二级页面tab相关配置
        String ITEM_FREE_LISTEN_OLD_USER_DIALOG = "free_listen_old_user_dialog";// 激励视频二级页面相关配置
        String ITEM_FREE_LISTEN_SCENE_TIPS_CONFIG = "unlockTimeLayerTimeTips"; // 激励视频场景提示
        String ITEM_SOUND_PATCH_CLOSE_AD_PADDING = "soundPatchCloseAdPadding"; // 贴片广告关闭热区
        String ITEM_SOUND_PATCH_ALWAYS_MUTE = "soundpatchAlwaysMute"; // 贴片广告关闭热区
        String ITEM_FREE_UNLOCK_SOUND_PATCH_TIP_LEFT_TIME = "free_unlock_sound_patch_tip_left_time"; // 畅听时长模式提示剩余时长
        String ITEM_UNLOCK_TIME_FIREWORK_SHOW_INTERVAL = "unlockTimeFireworkShowInterval"; // 时长模式本地弹屏天数间隔配置
        String ITEM_REWARD_VIDEO_TIME_UNLOCK_TOP_PLAYLET_CONTENT = "rewardVideoTimeUnlockTopPlayletContent"; // 短剧解锁激励视频顶部文案
        String ITEM_FREE_LISTEN_FIREWORK_DIALOG_ENABLE = "freeListenFireworkDialogEnable"; // 畅听统一弹屏是否可用
        String ITEM_NEED_INTERCEPT_INVALID_CLICK_FOR_TOUCH = "needInterceptNoRealClickJump"; // 是否需要拦截touch广告位非法点击
        String ITEM_INTERCEPT_INVALID_CLICK = "interceptInvalidClick" ;// 拦截非法点击
        String ITEM_VIP_MIDDLE_AD_TIME_OUT = "vipMiddleAdTimeOut" ;// Vip专辑列表页中插条广告超时时长c
        String ITEM_PROMOTION_CHANGE_HOME_BOTTOM = "prmotionChangeHomeBottom"; // 1拖2是否改变首页底部高度
        String ITEM_UVE_AD_OPTIMIZE = "uveAdOptimize"; // UVE下挂广告占位符问题优化
        String ITEM_UNLOCK_TIME_DIALOG_SUPPLEMENT_OPTIMIZE = "unlockTimeDialogSupplementOptimize"; // 畅听首页弹窗补弹逻辑优化
        String ITEM_FREE_UNLOCK_POINTVIEW_SOUNDURL = "free_unlock_pointview_soundurl"; // 畅听获取积分音效
        String ITEM_REWARD_VIDEO_MAX_LOAD_TIME = "rewardVideoMaxLoadTime" ;// 激励视频最大加载时长，单位ms
        String ITEM_USER_USE_INFO_ALLOW = "useUserInfoAllow"; // 是否允许使用用户信息
        String ITEM_AD_FEED_BACK_REASON = "ad_feed_back_reason_json_string"; // 负反馈原因
        String ITEM_AD_FEED_BACK_REASON_NEW = "ad_feed_back_reason_json_string_new"; // 负反馈原因
        String ITEM_AD_FEED_BACK_REASON_NATURE = "nature_content_feed_back_reason_json_string"; // 负反馈原因
        String ITEM_AD_FEED_BACK_REASON_SOUND_PATCH = "soundpatch_feed_back_reason_json_string"; // 贴片负反馈原因
        String ITEM_BIG_SCREEN_AD_RESUME_SHOW = "big_scrren_ad_resume_show"; // 巨幕广告二曝是否请求展示
        String ITEM_CENTER_BIG_PIC_AD_SHOW_INTERVAL = "centerBigPicAdShowInterval"; // 中插大图展示间隔
        String ITEM_NEED_CLEAR_LISTEN_TIME = "clearListenTime"; // 是否在特定场景清除畅听时长
        String ITEM_IS_NEED_FORCE_PRELOAD_RECOMMEND_SERVER = "is_need_force_preload_recommend_server"; // 是否需要强制预加载首页
        String ITEM_IS_INIT_ON_ACTIVITY_CREATE = "is_init_on_activity_create"; // 是否在activity创建时初始化
        String ITEM_FIX_CLOSE_COVER_AD_ENABLE = "fix_close_cover_ad_enable"; // 贴片关闭问题fix开关

        String ITEM_ADD_SOUND_PATCH_TICKET_ENABLE = "add_sound_patch_ticket_enable"; // 增加贴片风控参数开关
        String ITEM_CENTER_BIG_AD_MUTEX = "homeBannerAdMutex";// 中插大图广告和中插信息流广告是否需要互斥展示
        String ITEM_REWARD_AD_BTN_BG_AB = "rewardAdBtnBgAb";// 激励贴片按钮背景AB
        String ITEM_CENTER_BIG_AD_TIMEOUT = "centerBigAdTimeoutAndroid"; // 中插大图优化加载超时时长
        String ITEM_CENTER_BIG_AD_REFRESH_FOLD = "centerBigAdRefreshFoldAndroid"; // 中插大图下拉刷新场景折叠优化开关
        String ITEM_VIDEO_AD_COMPLETED_ON_FINISH_ENABLE = "videoAdCompletedOnFinishEnable"; // 视频广告完播是否通知专辑起播
        String ITEM_HOME_FREE_ICON_WATCH_VIDEO_DAILY_COUNT = "homeFreeIconWatchVideoDailyCount"; // 首页免字直接观看视频单日次数限制
        String ITEM_GIANT_SCREEN_DAILY_MAX_SHOW_COUNT = "giantScreenDailyMaxShowCount"; // 巨幕广告单日最大展示次数
        String ITEM_AIGC_CACHE_DEAL_CONFIG = "aigcCacheDealConfig";// aigc冷却时间和每天最大展示次数
        String ITEM_AIGC_CACHE_FREQUENCY_ENABLE = "checkAIgcMiddleAdFrequencyEnable";// aigc缓存优化功能开关
        String ITEM_FAST_APP_URL_TRACE_ENABLE = "fastAppInterceptUrlTraceEnable"; // 快应用拦截url是否上报
        String ITEM_CENTER_BIG_AD_RTB_ENABLE = "centerBigAdRtbEnable"; // 中插大图rtb开关
        String ITEM_CHECK_AUDIO_FOCUS_LESS_ENABLE = "checkAudioFocusLess"; // 是否检查音频焦点丢失标记开关
        String ITEM_FREE_LISTEN_HOME_DIALOG_AUTO_DISMISS_TIME = "freeListenHomeDialogDismissTime" ;// 畅听首页弹窗自动关闭时间,单位s
        String ITEM_DOWNLOAD_INCENTIVE_CONFIG = "downloadIncentiveConfig"; // 下载激励视频相关配置
        String ITEM_FREE_LISTEN_SPEED_DEC_MAP = "freeListenSpeedDecMap"; // 时长倍速扣减映射
        String ITEM_FREE_LISTEN_COUNT_DOWN_OPT = "freeListenCountDownOpt"; // 畅听扣减时机优化
        String ITEM_ADPI_CARRY_IN_BODY_ENABLE = "adPICarryInBodyEnable"; // apk列表安装信息放在请求body里
        String ITEM_POINT_CENTER_DP_WATCH_MAX_TIME = "point_center_dp_watch_max_time"; // 积分中心dp发奖优化配置
        String ITEM_XM_PUSH_NO_REWARD_CONFIG = "xmPushNoRewardConfig"; // 拉活冷启是否不请求rewardDuration配置
        String ITEM_AD_FULL_VIDEO_MUTE_INIT = "adFullVideoMuteInit"; // 全屏视频是否静音
        String ITEM_AD_FULL_VIDEO_CONTINUE_PLAY = "adFullVideoContinuePlay"; // 全屏视频是否续播
        String ITEM_AD_FULL_VIDEO_NOT_AUTO_DOWNLOAD_LIST = "adFullVideoNotAutoDownloadList"; // 全屏视频自动下载黑名单
        String ITEM_CENTER_BIG_PIC_AD_RELOAD_INTERVAL = "centerBigPicAdReloadInterval"; // 中插大图二曝请求频控
        String ITEM_CENTER_BIG_PIC_AD_RELOAD_INTERVAL_OPTIMIZE = "centerBigPicAdReloadIntervalString"; // 中插大图二曝请求频控优化
        String ITEM_GET_AD_PI_FROM_LOCAL_ENABLE = "getAdPIFromLocalEnable"; // 是否可以从缓存获取adPI
        String ITEM_PLAY_OPTIMIZE_ENABLE = "playOptimizeEnable"; // 是否开启播放优化
        String ITEM_FREE_LISTEN_REWARD_DIALOG_CLOSE_OPT= "rewardDialogCloseOpt"; // 畅听奖励弹窗关闭优化
        String ITEM_SEARCH_BRAND_FILTER_COLOR_ENABLE= "searchBrandFilterColorEnable"; // 搜索品专筛选按钮颜色问题开关
        String ITEM_AD_FEEDBACK= "ad_feedback"; // 负反馈面板修改
    }

    interface Group_community {
        String GROUP_NAME = "community";
        String ITEM_DISCOVERY_PAGE_BANNER = "discovery-page-banner";
        String ITEM_FORBID_ARTICLE_EDIT = "forbid.article.edit";
        String TRACK_DOWNLOAD_BTN_DISPLAY = "track.download.btn.display";
        String ITEM_COMMUNITY_PROFILE_EDIT = "community.profile.edit";
        String ITEM_FORBID_COMMUNITY_PROFILE_EDIT = "forbid.community.profile.edit";
        String ITEM_VTOOL_FACEBEAUTY = "vtool.facebeauty";
        String ITEM_COMMUNITY_ARTICLE_EDIT = "community.article.edit";
        String ITEM_VIP_SHARE_HIDE = "vip.share.hide";
        String ITEM_HIDE_VIP_MEMBER = "hide.vip.member";
        String ITEM_DISPLAY_JOIN = "community.joined.message";
        String ITEM_COMMENT_DISCUSS_SHOW = "playpage.comment.discussion.show";
        String ITEM_AUDIO_PLAY_DISCUSS_SHOW = "playPageDiscussionArea";
        String ITEM_COMMENT_INPUT_TIPS = "comment.input.tips";
        String ITEM_COMMUNITY_ENABLE_SHOW_REC = "feed.realtime.rec";
        String ITEM_SOCIAL_TRACK_COMMENT_PAGESIZE = "social.track.comment.pagesize"; // 播放页评论一页加载条数
        String ITEM_FEED_WELFARE_SQUARE_TAB = "feed.welfare.square.tab"; // 福利广场tab红点配置
        String ITEM_FEED_HOROSCOPE_TAB = "feed.welfare.horoscope.tab"; // 星座tab红点配置
        String ITEM_CREATE_POSTER_HELP_URL ="community.poster.prompt.url";
    }

    /**
     * abtest相关的东西放这里，如果是从配置中心创建的ab 则不需要
     */
    interface ABTest {
        String ABTEST = "abtest";
        String AB_ITEM_FCLS_BTN = "fcls_btn"; // 弹屏新旧样式ab 测试
        String AB_PERFORMANCE_MEMORY = "memory";
        String AB_PERFORMANCE_FPS = "fps";

        String AB_KEY_SCREEN_PUSH = "screenPush"; // 内Push亮屏客户端实验
        String AB_IS_NEED_KILL_PROCESS_ONCE = "is_need_kill_process_once"; // 是否需要杀死一次进程
        String AB_IS_PRE_LOAD_LOCAL_DATA = "is_pre_load_local_data"; // 是否预加载本地数据
        String AB_IS_NEED_FORCE_PRELOAD_RECOMMEND = "is_need_force_preload_recommend"; // 是否强制在广告页后面预加载推荐页
        String AB_NEWHOMEPAGE_AD_ZHONGCHA = "newhomepage_ad_zhongcha";// 新首页中插广告调优实验
        String AB_ITEM_CLEAN_SDK_REPORT_OPEN = "clean_sdk_report_open"; // 清场SDK上报开关
        String AB_ITEM_IS_PUSH_OPEN_DIALOG_OPTION = "is_push_open_dialog_option_v2"; // push通知开关打开优化
    }

    interface Group_MyClub {
        String GROUP_NAME = "myclub";

        //        String ITEM_SMS_INVITE = "sms_invite_template";
//        String ITEM_FEEDBACK__KNOWLEDGE = "feedback_knowledge";
//        String ITEM_TTF_gilroy_extra_bold = "gilroy_extra_bold.ttf";
//        String ITEM_TTF_sf_pro_display_bold = "sf_pro_display_bold.ttf";
//        String ITEM_TTF_sf_pro_display_semi_bold = "sf_pro_display_semi_bold.ttf";
//        String ITEM_TTF_sf_pro_display_regular = "sf_pro_display_regular.ttf";
//        String ITEM_TTF_sf_pro_display_bold_italic = "sf_pro_display_bold_italic.ttf";
//        String ITEM_TTF_sf_pro_display_semi_bold_italic = "sf_pro_display_semi_bold_italic.ttf";
//        String ITEM_TTF_serifpro_regular = "serifpro_regular.ttf";
//        String ITEM_TTF_sf_pro_display_regular_italic = "sf_pro_display_regular_italic.ttf";
//        // 字魂176号-创粗圆
//        String ITEM_TTF_zihun_176_bold_circle = "zihun_176_bold_circle.ttf";
//
//        String ITEM_ROOM_FOLLOW_TIPS_INTERVAL = "room_follow_tips_interval";
//        String ITEM_PRIVACY_AGREEMENT_VERSION = "privacy_agreement_version";
        String NICK_MODIFY_RANDOM = "myclub_nickname_enable_generate_random";
        String H5C_CHANNEL = "h5_channel";

        String ITEM_HOME_POSTER_REFRESH_INTERVAL = "home_poster_refresh_interval"; // 首页焦点图刷新时间
        String PRAISE_COUNT_ANIM_CONFIG = "praise_count_anim_config";   //鼓掌数变化动效参数配置
        String ITEM_PAYMENT_POLICY = "payment_policy"; // 购买须知
        String ITEM_XIMI_PAYMENT_POLICY = "ximi_payment_policy"; // ximi购买须知
        String ITEM_XIMI_PAYMENT_SPECIFICITY = "exclusive_room_policy"; //mc专享房间配置
        String ITEM_MC_APP_DEF_DOWNLOAD_GUIDE_PAGE_URL = "mc_app_def_download_guide_page_url"; //mc app默认引导下载h5页面
        String ITEM_MC_APP_DANMU_DISSMISS_TIME = "message_danmu_dissmiss_delaytime";
        String ITEM_MC_APP_HONOR_USER_LIST = "mc_live_honor_user_list";
        String ITEM_MC_ROOM_RETENTION_MIN_INTERVAL = "mc_room_present_retention_view_min_interval";
        String ITEM_TIP_ENABLE_AGREEMENT_URL = "mc_room_tip_enable_agreement_url";
        String ITEM_PAYMENT_SERVICE_AGREEMENT_URL = "mc_payment_service_agreement_url";
    }

    interface GroupUbt {
        String useXlogOrApi = "ubt_use_xlog_config_open";
        String useBlurMatch = "config_blur_match_switch";
        String isUploadNoTrack = "switch_no_track_upload";
        String XmtraceKey = "xmtrace_key";
        String useCombineUpload = "switch_trace_exposure_combine_upload";
        String useExposureNewWay = "androidTrackerExposureUseNewWay";
        String useMarkView = "switch_trace_use_mark";
        String androidTrackerMatchUploadPath = "androidTrackerMatchUploadPath";
        String dauPageInterval = "dauPageInterval";

        String firstExposureSwitch = "trace_first_exposure_switch";
        String firstExposurePeriod = "trace_first_exposure_period";

        String otherExposureSwitch = "trace_other_exposure_period";
        String pageLeaveForceExposure = "trace_page_leave_force_exposure";

        String dataUploadRealTime = "trace_data_upload_realtime";
        String parseAudioflowSwitch = "ubt_parse_audioflow_switch";

        String ubtTraceNewExplore = "key_for_trace_new_explore";
    }

    interface Client {
        String CLIENT = "client";
        String ITEM_SEARCH_RANK = "search_rank"; //搜索热搜榜方案
        String ITEM_LABEL_UI_AB = "label_UI_ab"; //兴趣标签选择页ab测试
        String AB_ITEM_FIND_RECOMMEND = "findRecommendedStreamStyle"; //发现页推荐卡片AB
        String AB_PLAY_PERFORMANCE = "play_performance";
        String AB_PLAY_ERROR_STATISTIC = "play_error";
        String AB_IF_USE_EXO_PLAYER = "if_use_exo_player";
        String AB_IF_USE_EXO_PLAYER_NEW = "if_use_exo_player_new";
        String AB_IF_STATISTIC_PAGE = "if_statistic_page";
        String AB_IF_USE_IM_NEW_PROTOCOL = "liveroom_use_im_new_protocol";//长连接IM协议升级
        String AB_IF_USE_IM_NEW_PROTOCOL_V2 = "livechat_switchto_new_im_protocol";//长连接IM协议升级
        String AB_IF_SOLOADER_SWITCH_OPEN = "if_soloader_switch_open";//soloder加载测试
        String GROWTH_BRINGUP_AB_V2 = "growth_bringup_ab_v2"; // 增长吊起ab
        String AB_H5_XMCACHE_AB = "xmoffline_ab";//离线资源ab
        String ITEM_TEENAGER_LAUNCH_AB = "teenager_launch_ab"; // 青少年弹窗出现时间ab
        String ADSDK_MONITOR_UPLOAD = "item_adsdk_monitor_upload"; // 广告sdk是否将检测的monitor 数据上报
        String AB_IF_LIVE_USE_NEW_VIDEOPLAYER = "live_use_new_video_player";//直播是否使用新版视频播放器
        String AB_IF_LIVE_USE_CHASE_FRAME = "live_chase_frame";//直播是否使用追帧策略
        String AB_LIVE_ANCHOR_FOLLOW_POP = "live-follow-popup";//直播，多人主播连线关注弹窗

        String AB_NEGATIVE_FEEDBACK = "Negative_feedback";// 负反馈样式大小

        String AB_SP_DATA_CHECK_KEY_VAL_MAX_LENGTH = "ab_sp_data_check_key_val_max_len";
        String AB_SP_DATA_CHECK_MAX_KEY_SIZE_SINGLE_FILE = "ab_sp_data_check_max_key_size_signle_file";
        String AB_SP_DATA_CHECK_MAX_TOTAL_FILE_SIZE = "ab_sp_data_check_max_total_file_size";
        String AB_SP_DATA_CHECK_MAX_TOTAL_SP_SIZE = "ab_sp_data_check_max_total_sp_size";

        String AB_PCDN_SWITCH_OPEN = "ab_pcdn_switch_open";
        String MC_MIC_VOLUME = "mc_mic_volume";
        String AB_IF_LIVE_USE_CDN_REQUEST = "live_req_cdnstyle_switch";//是否采用cdn拉取文件方式访问资源列表

    }

    interface Group_podcast {
        String GROUP_NAME = "podcast_channel";
        String AB_USE_NEW_PODCAST_PAGE = "podcast_channel";
    }

    interface GroupStartup {
        String startupBadTotaltime = "startup_bad_totaltime";
    }

    interface GroupLocalTing {
        String GROUP_NAME = "local_ting";
        String AB_LOCAL_LISTEN_VERSION = "local_listen_version";
    }

    /**
     * 社区相关
     */
    interface GroupBbsFeed {
        String NEW_FIND_PAGE_STYLE = "newFindPageStyle";
        String NEW_FIND_PAGE_MODULE = "moudle";
    }

    interface XiaoyaOs {
        String GROUP_NAME = "XiaoyaOS";
    }

    interface car {
        String GROUP_NAME = "car";
        String SHOWRADIO = "tab_show_radio";
    }

    interface NewAbTest {
        String NEW_AI_TEXT = "new_ai_text";  // 播放页文稿ab，true的话在声音tab封面区显示文稿
        String IMMERSIVE_VIDEO = "immersive_video"; // 播放页视频改版
        String CARD_SHOW = "card_show"; // 播放页互动卡片展示
        String FULL = "full"; // 新用户登录样式实验
        String RECALL_SKIP_FIRST_PAGE20230811 = "recall_skip_first_page20230811"; // iting跳过首页直接到达目标页面
        String RECALL_SOUND_1HOUR_NOAD20230811 = "recall_sound_1hour_noad20230811"; // 召回2-28天内每天收听1小时内免音贴广告
        String VOICE_ASSISANT_TYPE3 = "voiceAssisantType3"; // 语音助手ab
        String RESULT_UI = "result_decision"; // 一致性：搜索结果页UI调整
    }

    interface AdSplashShake {
        String GROUP_NAME = "adSplashShake";
        String SHAKE_SPEED_ANDROID = "shakeSpeedAndroid";
    }

    interface Group_Qiji {
        String GROUP_NAME = "qiji";
        String ITEM_ANDROID_AIDRAFT_CONFIG = "android_AIdraft_config";
        String ITEM_TING_READER_LOOP_UPLOAD_TIME_INTERVAL = "ting_reader_loop_upload_time_interval";
        String ITEM_TING_READER_DOC_PRE_NEXT_CACHE_FUN_ENABLE = "ting_reader_doc_pre_next_cache_fun_enable";
        String TING_READER_DOC_PLAYBAR_INIT_CACHE_FUN_ENABLE = "ting_reader_doc_playbar_init_cache_fun_enable";
        String ITEM_TING_READER_ABTEST_FUNCTION_SHOW_CONFIG = "ting_reader_abtest_function_show_config";
        String ITEM_TING_READER_ABTEST_ADSHOW_CONFIG = "ting_reader_abtest_ADshow_config";
        String ITEM_TING_READER_DEFAULT_SWITCH = "ting_reader_default_switch";
        String ITEM_TING_READER_PLAYPAGE_TIPS = "ting_reader_playpage_tips_2";
    }

}
