package com.ximalaya.ting.android.host.manager.bundleframework.BundleManager;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.tencent.bugly.crashreport.CrashReport;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.configurecenter.base.IConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.hotfixtrace.HotFixMonitor;
import com.ximalaya.ting.android.framework.hotfixtrace.HotFixTrace;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.ActivityManagerDetacher;
import com.ximalaya.ting.android.host.BuildConfig;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.manager.application.ApplicationManager;
import com.ximalaya.ting.android.host.manager.bundleframework.BundleFileManager;
import com.ximalaya.ting.android.host.manager.bundleframework.BundleVersionUtil;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.Util;
import com.ximalaya.ting.android.host.manager.bundleframework.classloader.ClassLoaderManager;
import com.ximalaya.ting.android.host.manager.bundleframework.constant.SpConstants;
import com.ximalaya.ting.android.host.manager.bundleframework.download.BaseBundleDownloadTask;
import com.ximalaya.ting.android.host.manager.bundleframework.download.BundleDownloadManager;
import com.ximalaya.ting.android.host.manager.bundleframework.download.PatchDownloadTask;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.resource.DelegateResources;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.configurecenter.ConfigureCenterUtil;
import com.ximalaya.ting.android.host.manager.read.ReadPreLoadModuleManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.plugin.PluginInfoModel;
import com.ximalaya.ting.android.host.util.EncryptProxy;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.ProcessUtil;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import static com.ximalaya.ting.android.host.manager.bundleframework.Configure.BUNDLE_READ;
import static com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router.CALL_GET_ACTION;


/**
 * <AUTHOR>
 */
public class BundleInfoManager implements ActivityManagerDetacher.AppStatusListener, Handler.Callback {

    private Context mContext;
    public static final String ACTIVITY_BUNDLE_TAG = "bundle_activity_tag";
    public static final String TAG = "BundleInfoManager";

    public static final int INSTALL_CALLBACK_CODE_SUCCESS = 1;
    public static final int INSTALL_CALLBACK_CODE_LOCAL_FAIL = 2;
    public static final int INSTALL_CALLBACK_CODE_REMOTE_FAIL = 3;


    private volatile boolean mHasRegisterAppStatus = false;
    @Nullable
    private Handler mHandler; //目前只在主进程初始化
    private static final int MESSAGE_CHECK_EXIT_ADD = 1;

    private final NotBuildInBundleManager mNotBuildInBundleManager;

    private final ExecutorDelivery executorDelivery;

    private final InstallAndInitThreadPool installAndInitThreadPool;


    public static long startTime = 0;
    public static boolean isRefreshBundle = false;

    /**
     * 对于异步下载安装，安装完后需要回调的，将查找这里的记录并回调，这个记录只有启动异步下载时才添加；
     */
    private final ConcurrentHashMap<String, CopyOnWriteArrayList<BridgeRemoteInstallCallback>> tmpRemoteInstallCallbacks;

    private BundleInfoManager() {
        if (BaseUtil.isMainProcess(mContext)) {
            NetworkStateReceiver.register(mContext);
            mHandler = new Handler(Looper.getMainLooper(), this);
        }
        mNotBuildInBundleManager = new NotBuildInBundleManager(mContext);
        tmpRemoteInstallCallbacks = new ConcurrentHashMap<>(3);
        executorDelivery = new ExecutorDelivery(new Handler(Looper.getMainLooper()));
        installAndInitThreadPool = new InstallAndInitThreadPool("install_bundle_thread");
    }

    @Deprecated
    private NotBuildInBundleManager getNotBuildInBundleManager() {
        return mNotBuildInBundleManager;
    }

    private static class InnerClass {
        private final static BundleInfoManager instance = new BundleInfoManager();
    }

    public static BundleInfoManager getInstance() {
        return InnerClass.instance;
    }

    public void init(Context context) {
        mContext = context;
    }

    public interface BridgeInstallCallback {
        void onInstallSuccess(BundleModel bundleModel);

        void onInstallError(Throwable e, BundleModel bundleModel);
    }

    public abstract static class BridgeRemoteInstallCallback implements BridgeInstallCallback {
        public Router.RealRouter realRouter;

        public BridgeRemoteInstallCallback(Router.RealRouter realRouter) {
            this.realRouter = realRouter;
        }

        abstract public void onRemoteInstallError(Throwable e, BundleModel bundleModel);
    }

    public void addARealRouterRecord(@NonNull BridgeRemoteInstallCallback remoteInstallCallback) {
        CopyOnWriteArrayList<BridgeRemoteInstallCallback> remoteInstallCallbacks = tmpRemoteInstallCallbacks.get(remoteInstallCallback.realRouter.getBundleModel().bundleName);
        if (remoteInstallCallbacks == null) {
            remoteInstallCallbacks = new CopyOnWriteArrayList<>();
        }
        remoteInstallCallbacks.add(remoteInstallCallback);
        tmpRemoteInstallCallbacks.put(remoteInstallCallback.realRouter.getBundleModel().bundleName, remoteInstallCallbacks);
    }

    public void onRemoteInstallCallback(BundleModel bundleModel, boolean success, boolean remoteInstall, Throwable e) {
        if (bundleModel == null) {
            return;
        }
        synchronized (bundleModel) {// bundle 内部同步
            List<BridgeRemoteInstallCallback> remoteInstallCallbacks = tmpRemoteInstallCallbacks.get(bundleModel.bundleName);
            if (remoteInstallCallbacks == null) {
                return;
            }
            if (success || remoteInstall) {// 如果成功 or 远程回来，则移除，本地安装失败是不会移除所有回调
                tmpRemoteInstallCallbacks.remove(bundleModel.bundleName);
            }

            CopyOnWriteArrayList<BridgeRemoteInstallCallback> tmp = new CopyOnWriteArrayList<>();
            for (int i = 0; i < remoteInstallCallbacks.size(); i++) {
                BridgeRemoteInstallCallback remoteInstallCallback = remoteInstallCallbacks.get(i);
                if (!remoteInstallCallback.realRouter.isNeedDownload()) {// 如果没有设置远程下载安装，这移除回调
                    tmp.add(remoteInstallCallback);
                }
                if (success) {
                    executorDelivery.postBundleInstallSuccess(remoteInstallCallback);
                } else {
                    executorDelivery.postBundleInstallError(remoteInstallCallback, e, remoteInstall ? INSTALL_CALLBACK_CODE_REMOTE_FAIL : INSTALL_CALLBACK_CODE_LOCAL_FAIL);
                }
            }

            if (tmp.size() > 0) {
                remoteInstallCallbacks.removeAll(tmp);
            }
        }
    }

    private void clearDirIfAppVersionUpdate(Context context, BundleModel bm) {

        //只对buildIn的插件执行此操作
        if (bm == null || !bm.isBuildIn()) {

            return;
        }

        SharedPreferences sp = context.getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE, Context.MODE_MULTI_PROCESS);

        String version = sp.getString(SpConstants.KEY_BUNDLE_APPLY_VERSION_FOUR(bm), "");

        String newVersion = DeviceUtil.getVersionFour(context);

        if (!TextUtils.isEmpty(version)
                && !checkVersionFourUpdate(version, newVersion)) {
            return;
        }

        //clear dirs
        boolean revertPatchSuccess = doRevertPatchIfExists(context, bm);
        boolean revertPluginSuccess = doRevertPlugin(context, bm);

        //clear origin dir
        boolean deleteOriginDirSuccess = false;

        File dexDir = BundleFileManager.getInstance(context).getInternalDexPath();
        File originDir = new File(dexDir, bm.bundleName + File.separator + "origin");

        try {

            deleteOriginDirSuccess = Util.deleteFolderKeepRootDir(originDir.getAbsolutePath());
        } catch (Exception e) {

            e.printStackTrace();
        }

        if (revertPatchSuccess && revertPluginSuccess && deleteOriginDirSuccess) {

            sp.edit().putString(SpConstants.KEY_BUNDLE_APPLY_VERSION_FOUR(bm), newVersion).apply();

            Logger.i(TAG, "app version update : from " + version + " to " + newVersion + ",has cleared bundle dir for " + bm.bundleName + " bundle");
        } else {

            Logger.i(TAG, "bundleName : " + bm.bundleName
                    + " revertPatchSuccess : " + revertPatchSuccess
                    + " revertPluginSuccess : " + revertPluginSuccess
                    + " deleteOriginDirSuccess : " + deleteOriginDirSuccess);
        }

    }

    /**
     * The different from deRevertPluginPath is that this method will return true if patch not exists.
     *
     * @param context
     * @param bundleModel
     * @return
     */
    private boolean doRevertPatchIfExists(Context context, BundleModel bundleModel) {

        if (context == null || bundleModel == null) {
            return false;
        }

        SharedPreferences pluginSp = context.getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE, Context.MODE_MULTI_PROCESS);
        String bundleStr = pluginSp.getString(SpConstants.KEY_DOWNLOAD_BUNDLE_MODEL(bundleModel), "");

        if (TextUtils.isEmpty(bundleStr)) {
            //Not exists plugin patch.
            return true;
        }
        return doRevertPluginPatch(context, bundleModel);

    }

    private boolean checkVersionFourUpdate(String version, String newVersion) {

        if (!checkVersionFour(version)) {
            //old version invalid, need update.
            return true;
        }
        if (!checkVersionFour(newVersion)) {

            //new version invalid
            return false;
        }

        //both valid
        String[] versions = version.split("\\.");
        String[] newVersions = newVersion.split("\\.");

        try {

            return Integer.parseInt(versions[0]) < Integer.parseInt(newVersions[0]) ||
                    Integer.parseInt(versions[1]) < Integer.parseInt(newVersions[1]) ||
                    Integer.parseInt(versions[2]) < Integer.parseInt(newVersions[2]) ||
                    Integer.parseInt(versions[3]) < Integer.parseInt(newVersions[3]);

        } catch (Exception e) {

            e.printStackTrace();
        }

        return false;
    }

    private boolean checkVersionFour(String version) {

        if (TextUtils.isEmpty(version)) {

            return false;
        }

        String[] versions = version.split("\\.");

        if (versions == null || versions.length != 4) {

            return false;
        }

        try {

            Integer.parseInt(versions[0]);
            Integer.parseInt(versions[1]);
            Integer.parseInt(versions[2]);
            Integer.parseInt(versions[3]);

            return true;

        } catch (Exception e) {

            e.printStackTrace();
        }

        return false;
    }

    /**
     * 异步初始化一个bundle
     *
     * @param bundleModel
     * @param installCallback
     */
    public void asyncInitBundle(final BundleModel bundleModel, BridgeInstallCallback installCallback) {
        BundleApplicationInit.getInstance().initBundleApplication(bundleModel, new BundleApplicationInit.IBundleInitCallback() {
            @Override
            public void onInitSuccess(BundleModel bundleModel) {
                if (installCallback != null) {
                    installCallback.onInstallSuccess(bundleModel);
                }
            }

            @Override
            public void onInitError(Throwable t, BundleModel bundleModel) {
                if (installCallback != null) {
                    installCallback.onInstallError(t, bundleModel);
                }
            }
        });
    }

    /**
     * 异步安装bundle
     * 这里可能会触发远程下载
     *
     * @param realRouter
     * @param remoteInstallCallback
     */
    public void asyncInstallBundle(Router.RealRouter realRouter, BridgeRemoteInstallCallback remoteInstallCallback) {
        BundleModel bundleModel = realRouter.getBundleModel();
        if (realRouter.getCallType() != CALL_GET_ACTION || realRouter.getInstallCallback() != null) {
            addARealRouterRecord(remoteInstallCallback);
        }
        if (bundleModel.hasGenerateBundleFile) {
            if (!bundleModel.isBuildIn()) {
                checkBundleUpdate(bundleModel);
            }
            onRemoteInstallCallback(bundleModel, true, false, null);
            return;
        }
        if (bundleModel.inGenerateBundleFile) {
            return;
        }
        synchronized (bundleModel) {
            if (bundleModel.hasGenerateBundleFile) {
                onRemoteInstallCallback(bundleModel, true, false, null);
                return;
            }
            if (bundleModel.inGenerateBundleFile) {
                return;
            }
            bundleModel.inGenerateBundleFile = true;
            installAndInitThreadPool.executor(new Runnable() {
                @Override
                public void run() {
                    android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_BACKGROUND);
                    realInstallBundle(realRouter);
                }
            });
        }
    }

    public void syncInstallBundleByName(String bundleName) {
        BundleModel bundleModel = Configure.getBundleByName(bundleName);
        syncInstallBundle(bundleModel);
    }

    /**
     * 同步安装bundle
     *
     * @param bundleModel
     */
    public void syncInstallBundle(BundleModel bundleModel) {
        if (bundleModel == null) {
            return;
        }
        synchronized (bundleModel) {
            realInstallBundle(Router.buildRealRouter(bundleModel));
        }
    }

    private void realInstallBundle(Router.RealRouter realRouter) {
        BundleModel bundleModel = realRouter.getBundleModel();
        Logger.i(TAG, "asyncInstallBundle: " + bundleModel.bundleName);

        long time = System.currentTimeMillis();
        startTime = time;
        RandomAccessFile randomAccessFile = null;
        FileChannel fileChannel = null;
        FileLock fileLock = null;
        try {
            String lockFilePath = BundleFileManager.
                    getInstance(mContext).
                    getLocalBundleDir().getAbsolutePath() + File.separator + bundleModel.bundleName + "lock";
            File lockFile = new File(lockFilePath);
            randomAccessFile = new RandomAccessFile(lockFile, "rw");
            fileChannel = randomAccessFile.getChannel();
            fileLock = fileChannel.tryLock();
            if (fileLock == null) {
                throw new Exception("file use by other process");
            }

            //clear all file if app version update.
            clearDirIfAppVersionUpdate(mContext, bundleModel);

            BundleVersionUtil.initBundleLocalVersionNotCopyApk(bundleModel);
            BundleFileManager.getInstance(mContext).initPathForBundle(bundleModel);

            if (Configure.mainBundleModel.bundleName.equals(bundleModel.bundleName)) {
                Logger.i("BundleInstallTime", "BundleInstallTime1 time= " + (System.currentTimeMillis() - time));
            }

            // debug 模式： 检测sd 卡中是否有so 插件
            boolean isSdcardDevBundleExist = false;
            if ((ConstantsOpenSdk.isBundleDevelopMode || BuildConfig.isDevMode) && ConstantsOpenSdk.isDebug) {
                File sdcardSoFile = Util.isSdcardBundleExist(bundleModel);
                if (sdcardSoFile != null && sdcardSoFile.exists()) {
                    if (Util.getApkFromSdcardSuccess(bundleModel, sdcardSoFile)) {
                        isSdcardDevBundleExist = true;
                    } else {
                        Logger.e("BundleInfoManager", "sd 卡上的文件cpu 类型和运行环境可能不一致");
                    }
                }
            }

            if (!isSdcardDevBundleExist) {
                if (bundleModel.isBuildIn()) {
                    if (ConstantsOpenSdk.isDebug) {
                        Util.getApkFromLibs(bundleModel);
                        if (Configure.mainBundleModel.bundleName.equals(bundleModel.bundleName)) {
                            Logger.i("BundleInstallTime", "BundleInstallTime copy from asset time= " + (System.currentTimeMillis() - time));
                        }
                    }
                } else {
                    boolean isMainProcess = BaseUtil.isMainProcess(mContext);
                    BundleModel savedDexBundleInfo = Util.getSavedBundleInfo(bundleModel);
                    boolean dexFileExists = new File(bundleModel.dexFilePath).exists();  //检测origin 目录下文件的有效性
                    boolean dexFileValid = true;
                    // 之前存的cpuType非空才做检查
                    if (savedDexBundleInfo != null && !TextUtils.isEmpty(savedDexBundleInfo.dexFileCpuType) &&
                            !Util.isRightCpuType(savedDexBundleInfo.dexFileCpuType)) {
                        dexFileValid = false;
                    }
                    // 直接用全部buildin打，这个逻辑先去掉
                    // 当前走测试环境，且之前的插件是正式环境的，则重新下载插件
                    // 当前流水线默认配置中，测试包的插件版本都小于正式包，所以只处理测试包覆盖正式包的情况，减少影响面
//                    boolean pluginEnvironmentChanged = false;
//                    if (BundleRequestCache.getInstance().isUseTestPlugin()) {
//                        // 已经安装过，且安装的插件不是测试环境的，则删了重新下载安装
//                        if (savedDexBundleInfo != null) {
//                            if (!savedDexBundleInfo.isTestPlugin) {
//                                pluginEnvironmentChanged = true;
//                                try {
//                                    //noinspection ResultOfMethodCallIgnored
//                                    new File(bundleModel.dexFilePath).delete();
//                                } catch (Exception e) {
//                                    Logger.e(e);
//                                }
//                            }
//                        } else if (MmkvCommonUtil.getInstance(mContext).getBoolean(
//                                PreferenceConstantsInHost.KEY_HAS_EVER_USE_ONLINE_ENVIROMENT_PLUGIN, false)) {
//                            // 没安装过，但曾经在正式环境，则需要需要本地可能已经下载的插件，重新下载插件
//                            pluginEnvironmentChanged = true;
//                        }
//                    }
                    boolean resourceFileExists = new File(bundleModel.resourceFilePath).exists(); //检测origin 目录下文件的有效性
                    if (savedDexBundleInfo == null ||
                            (Util.checkNeedUpdate(savedDexBundleInfo.getLocalVersion(), bundleModel.version) != Util.NOT_UPDATE) ||
                            !dexFileExists || !dexFileValid ||
                            !resourceFileExists/* || pluginEnvironmentChanged*/) {
                        //dex file invalid
                        boolean remoteBundleIsValid = Util.getBundleInfo(bundleModel);
                        if (remoteBundleIsValid/* && !pluginEnvironmentChanged*/) {
                            Util.getApkFromRemote(bundleModel);
                        } else {
                            //本地文件不正确 必须下载
                            if (isMainProcess && realRouter.isNeedDownload()) {
                                download(realRouter);
                            }
                            bundleModel.hasGenerateBundleFile = false;
                            throw new LocalInstallException("local install fail in asyncInstall: " + bundleModel.bundleName);
                        }
                    } else {

                        boolean remoteBundleIsValid = Util.getBundleInfo(bundleModel);
                        if (remoteBundleIsValid) {
                            int update = Util.checkNeedUpdate(savedDexBundleInfo.getLocalVersion(), bundleModel.getLocalVersion());
                            if (update != Util.NOT_UPDATE) {
                                Util.getApkFromRemote(bundleModel);
                            }
                        }
                        if (isMainProcess) {
                            checkBundleUpdate(bundleModel, true);
                        }
                    }
                }
            }

            if (Configure.mainBundleModel.bundleName.equals(bundleModel.bundleName)) {
                Logger.i("BundleInstallTime", "BundleInstallTime readBundleFile time= " + (System.currentTimeMillis() - time));
            }
            //  恢复path 信息，如果一有的话
            initPatchInfo(bundleModel);
            if ("main".equals(bundleModel.bundleName)) {
                Logger.i("BundleInstallTime", "BundleInstallTime read patch time= " + (System.currentTimeMillis() - time));
            }
            ClassLoaderManager.getInstance().installBundleApk(bundleModel);

            if ("main".equals(bundleModel.bundleName)) {
                Logger.i("BundleInstallTime", "BundleInstallTime install apk= " + (System.currentTimeMillis() - time));
            }

            DelegateResources.addBundleResources(bundleModel.resourceFilePath, bundleModel.resourceFilePath);
            Activity activity = BaseApplication.getTopActivity();
            if (activity != null && activity instanceof BaseFragmentActivity2) {
                ((BaseFragmentActivity2) activity).installResourceForHostActivityFor(bundleModel);
            }

            if ("main".equals(bundleModel.bundleName)) {
                Logger.i("BundleInstallTime", "BundleInstallTime install resource= " + (System.currentTimeMillis() - time));
            }
            // 发送插件安装成功信息
            sendPluginInstallSuccess(bundleModel);

            if ("main".equals(bundleModel.bundleName)) {
                Logger.i("BundleInstallTime", "BundleInstallTime send statics= " + (System.currentTimeMillis() - time));
            }
            bundleModel.hasGenerateBundleFile = true;
            onRemoteInstallCallback(bundleModel, true, false, null);
            if ("main".equals(bundleModel.bundleName)) {
                Logger.i("BundleInstallTime", "BundleInstallTime install success= " + (System.currentTimeMillis() - time));
            }
            Logger.logToFile(TAG + bundleModel.bundleName + " install success");
        } catch (Throwable e) {
            bundleModel.hasGenerateBundleFile = false;
            onRemoteInstallCallback(bundleModel, false, false, e);
            try {
                if (!(e instanceof LocalInstallException) && e.getMessage() != null && !"force update".equals(e.getMessage())) {
                    XDCSCollectUtil.statErrorToXDCS("BundleFramework", bundleModel.bundleName + " bundle install error " + e);
                    Exception uploadException = new Exception("BundleFramework" + bundleModel.bundleName + " bundle install error " + e);
                    uploadException.setStackTrace(e.getStackTrace());
                    CrashReport.postCatchedException(uploadException);
                }
            } catch (Throwable ttt) {
                ttt.printStackTrace();
            }
            Logger.logToFile(TAG + bundleModel.bundleName + " install error : " + e);
        } finally {
            bundleModel.inGenerateBundleFile = false;
            if (fileLock != null) {
                try {
                    fileLock.release();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fileChannel != null) {
                try {
                    fileChannel.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (randomAccessFile != null) {
                try {
                    randomAccessFile.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 启动远程下载，下载完后会调到这里安装
     * 运行于子线程
     *
     * @param realRouter
     */
    private void download(Router.RealRouter realRouter) {
        Logger.i("cf_test", "download(Router.RealRouter realRouter)————" + realRouter.getBundleModel().bundleName);
        if (realRouter.getBundleModel().downloadNeedTipsUser(realRouter.getDownloadType())) {
            mNotBuildInBundleManager.gotoNotBuildInBundleUpdate(realRouter.getBundleModel());
        } else {
            mNotBuildInBundleManager.checkAndDownloadNotBuildInBundle(realRouter.getBundleModel());
        }
    }

    /**
     * APP启动首页渲染完成后下载
     */
    private void downloadForAppStart(BundleModel bundleModel) {
        mNotBuildInBundleManager.checkAndDldPluginForStart(bundleModel);
    }


    /**
     * 远程下载并安装 调用此方法
     *
     * @param bundleModel
     * @throws Exception
     */
    public void installForDownloadedBundle(BundleModel bundleModel) {
        if (bundleModel == null || bundleModel.hasGenerateBundleFile || bundleModel.inGenerateBundleFile) {
            return;
        }
        synchronized (bundleModel) {
            if (bundleModel.hasGenerateBundleFile) {
                return;
            }
            Logger.i(TAG, bundleModel.bundleName + " start install bundle");
            bundleModel.inGenerateBundleFile = true;
            try {
                BundleFileManager.getInstance(mContext).initPathForBundle(bundleModel);
                boolean localBundleIsValid = Util.getBundleInfo(bundleModel);
                if (bundleModel.isBuildIn()) {
                    File soFile = new File(bundleModel.soFilePath);
                    Util.getApkFromLibs(bundleModel);
                } else {
                    if (!localBundleIsValid) {
                        bundleModel.hasGenerateBundleFile = false;
                        throw new ForceUpdateException("force update download Install");
                    } else {
                        Util.getApkFromRemote(bundleModel);
                    }
                }

                ClassLoaderManager.getInstance().installBundleApk(bundleModel);

                DelegateResources.addBundleResources(bundleModel.resourceFilePath, bundleModel.resourceFilePath);
                Activity activity = BaseApplication.getTopActivity();
                if (activity != null && activity instanceof BaseFragmentActivity2) {
                    ((BaseFragmentActivity2) activity).installResourceForHostActivityFor(bundleModel);
                }

                // 发送插件安装成功信息

                bundleModel.hasGenerateBundleFile = true;
                // todo 安装成功回调
                onRemoteInstallCallback(bundleModel, true, true, null);
                Util.sendInstallResult(bundleModel.bundleName, Util.CODE_1, null);
            } catch (Throwable e) {
                bundleModel.hasGenerateBundleFile = false;
                onRemoteInstallCallback(bundleModel, false, true, e);
                try {
                    if (!(e instanceof ForceUpdateException)) {
                        Util.sendInstallResult(bundleModel.bundleName, Util.CODE_2, e.getMessage());
                        XDCSCollectUtil.statErrorToXDCS("BundleFramework", bundleModel.bundleName + " bundle install error " + e);
                        Exception uploadException = new Exception("BundleFramework: " + bundleModel.bundleName + " bundle install error " + e);
                        uploadException.setStackTrace(e.getStackTrace());
                        CrashReport.postCatchedException(uploadException);
                    } else {
                        Util.sendInstallResult(bundleModel.bundleName, Util.CODE_3, "force: " + e.getMessage());
                    }
                } catch (Throwable ttt) {
                    ttt.printStackTrace();
                }
                Logger.logToFile(TAG + bundleModel.bundleName + " install error : " + e);
            } finally {
                bundleModel.inGenerateBundleFile = false;
            }
        }
    }

    /**
     * 从保存的历史信息中护肤path 信息
     *
     * @param bundleModel
     */
    private void initPatchInfo(BundleModel bundleModel) {
        try {
            //send plugin install success collect
            SharedPreferences sp = mContext.getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE, Context.MODE_MULTI_PROCESS);
            String pluginModelInfoStr = sp.getString(SpConstants.KEY_DOWNLOAD_BUNDLE_MODEL(bundleModel), "");
            if (TextUtils.isEmpty(pluginModelInfoStr)) {
                return;
            }

            Gson gson = new Gson();
            BundleModel saveBundleModel = gson.fromJson(pluginModelInfoStr, BundleModel.class);
            if (saveBundleModel == null || saveBundleModel.pluginInfoModel == null) {
                return;
            }
            if (!saveBundleModel.pluginInfoModel.getFileVersion().equals(bundleModel.getLocalVersion())) {
                return;
            }
            bundleModel.pluginInfoModel = saveBundleModel.pluginInfoModel;
            if (saveBundleModel.patchPluginInfoModel == null) {
                return;
            }
            if (saveBundleModel.patchPluginInfoModel.getRefPatchId() != saveBundleModel.pluginInfoModel.getId()) {
                return;
            }
            bundleModel.patchPluginInfoModel = saveBundleModel.patchPluginInfoModel;

            File patchSoFile = new File(bundleModel.patchSoFilePath);
            // TODO: 2020/11/12 检查patch md5
            if (patchSoFile.exists()) {
                if (Util.getMd5ByFile(patchSoFile).equals(bundleModel.patchPluginInfoModel.getFileMd5())) {
                    Util.copyFile(bundleModel.patchSoFilePath, bundleModel.patchDexFilePath);
                    bundleModel.hasPatch = true;
                }
            }

        } catch (Exception e) {
            Logger.i(TAG, "get bundle sharepreferences or copy patch file error  " + e.toString());
            e.printStackTrace();
        }
    }

    @SuppressLint("StaticFieldLeak")
    private void sendPluginInstallSuccess(BundleModel bundleModel) {
        SharedPreferences sp = mContext.getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE, Context.MODE_MULTI_PROCESS);
        String statisticInstallVersion = sp.getString(SpConstants.KEY_PLUGIN_INSTALL_STATISTIC_VERSION(bundleModel), "");

        if (bundleModel.pluginInfoModel == null
                || TextUtils.equals(statisticInstallVersion, bundleModel.pluginInfoModel.getFileVersion())) {
            return;

        }
        new MyAsyncTask<Void, Void, Void>() {
            @Override
            protected Void doInBackground(Void... ss) {
                try {
                    //send plugin install success collect

                    if (bundleModel.pluginInfoModel != null && bundleModel.pluginInfoModel.getId() != 0) {
                        Map<String, String> params = new HashMap<>();
                        params.put("installs", bundleModel.pluginInfoModel.getId() + "");
                        EncryptProxy.getGiftSignature(params);
                        CommonRequestM.getInstanse().downloadPluginStatistics(params, null);

                        SharedPreferences.Editor editor = sp.edit();
                        editor.putString(SpConstants.KEY_PLUGIN_INSTALL_STATISTIC_VERSION(bundleModel), bundleModel.pluginInfoModel.getFileVersion());
                        editor.commit();
                    }
                } catch (Exception e) {
                    Logger.i(TAG, "send bundle install message error " + e.toString());
                    e.printStackTrace();
                }
                return null;
            }
        }.myexec();

    }


    public void gotoBackGroundUpdate(final BundleModel bundleModel) {
        if (bundleModel == null) {
            return;
        }
        Logger.i(TAG, "gotoBackGroundUpdate for: " + bundleModel.bundleName);

        if (!mHasRegisterAppStatus) {
            MainApplication.getInstance().addAppStatusListener(this);
            mHasRegisterAppStatus = true;
        }

        final String packageName = bundleModel.packageNameList.get(0);

        if (TextUtils.isEmpty(packageName)) {
            return;
        }
        long startRequestTime = System.currentTimeMillis();
        HotFixMonitor.getInstance().traceHotFix(HotFixTrace.TYPE_START_REQUEST);
        BundleRequestCache.getInstance().getPluginInfo(false, packageName, new IDataCallBack<PluginInfoModel>() {
            @Override
            public void onSuccess(final PluginInfoModel remotePluginModel) {
                // 这里只用于host插件操作
                if (Configure.dispatchBundleModel.bundleName.equals(bundleModel.bundleName)) {
                    if (remotePluginModel == null) {
                        HotFixMonitor.getInstance().traceHotFix(HotFixTrace.TYPE_FINISH_REQUEST, -1, "当前应用版本，无远程补丁");
                        PatchLoadInfoManager.getSingleInstance().receivePatchInfo("当前应用版本，无远程补丁", -1);
                        return;
                    }
                    if (!Util.checkVersion(remotePluginModel.getFileVersion())) {
                        HotFixMonitor.getInstance().traceHotFix(HotFixTrace.TYPE_FINISH_REQUEST, -1, "验证远程补丁版本信息失败");
                        PatchLoadInfoManager.getSingleInstance().receivePatchInfo("验证远程补丁版本信息失败 " + remotePluginModel.getFileVersion(), -1);
                        return;
                    }
                    // 远程插件和发包时的基准版本号一致 说明 插件没有做过升级
                    if (Util.versionEqual(bundleModel.version, remotePluginModel.getFileVersion())) { // 插件版本相同
                        PatchLoadInfoManager.getSingleInstance().receivePatchInfo("远程补丁版本验证成功 " + remotePluginModel.getFileVersion(), 0);
                        bundleModel.pluginInfoModel = remotePluginModel;
                        // 备注 OPPO 手机  PBAM00 这个型号 android 8.0 的机器 热修复不会在dex所处位置生成产物
                        //导致产物不能复用
                        //加载时间过长 因此过滤掉这个机型
                        boolean ignore = "OPPO".equals(Build.MANUFACTURER)
                                && "PBAM00".equals(Build.MODEL)
                                && (Build.VERSION.SDK_INT == Build.VERSION_CODES.O_MR1 || Build.VERSION.SDK_INT == Build.VERSION_CODES.O);

                        try {
                            boolean openHotfix = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost.KEY_OPEN_APP_HOTFIX, true);
                            Log.d(TAG, "runPatchService, openHotfix: " + openHotfix);
                            //在 debug 页面关闭了热修复加载
                            if (!openHotfix) {
                                XDCSCollectUtil.statErrorToXDCS("load_patch_detail",  "runPatchService skipped, because openHotfix is false!");
                                ignore = true;
                            }
                        } catch (Throwable throwable) {
                            throwable.printStackTrace();
                        }

                        if (Build.VERSION.SDK_INT >= 21 && !ignore) {
                            HotFixMonitor.getInstance().traceHotFix(HotFixTrace.TYPE_FINISH_REQUEST, 0,
                                    "远程补丁版本验证成功",
                                    System.currentTimeMillis() - startRequestTime, remotePluginModel.getFileVersion());
                            checkDispatchBundlePatch(bundleModel); //check 是否有补丁
                        } else {
                            if (Build.VERSION.SDK_INT < 21) {
                                HotFixMonitor.getInstance().traceHotFix(HotFixTrace.TYPE_FINISH_REQUEST, -1, "当前系统版本不支持此应用补丁 " + Build.VERSION.SDK_INT);
                                PatchLoadInfoManager.getSingleInstance().receivePatchInfo("当前系统版本不支持此应用补丁 " + Build.VERSION.SDK_INT, -1);
                            }
                            if (ignore) {
                                HotFixMonitor.getInstance().traceHotFix(HotFixTrace.TYPE_FINISH_REQUEST, -1, "当前设备不支持此应用补丁" + Build.MANUFACTURER + " " + Build.MODEL);
                                PatchLoadInfoManager.getSingleInstance().receivePatchInfo("当前设备不支持此应用补丁 " + Build.MANUFACTURER + " " + Build.MODEL, -1);
                            }
                        }
                    } else {
                        HotFixMonitor.getInstance().traceHotFix(HotFixTrace.TYPE_FINISH_REQUEST, -1, "本地应用版本 " + bundleModel.version + "  不同于远程应用补丁要求版本 " + remotePluginModel.getFileVersion());
                        PatchLoadInfoManager.getSingleInstance().receivePatchInfo("本地应用版本 " + bundleModel.version + "  不同于远程应用补丁要求版本 " + remotePluginModel.getFileVersion(), -1);
                    }

//                    bundleModel.downloadPath = bundleModel.downloadDirectory + File.separator + Util.hashKeyForDisk(remotePluginModel.getFileUrl());
//                    bundleModel.pluginInfoModel = remotePluginModel;
//
//                    // needUpdate 表示插件是否需要更新
//                    int needUpdate = Util.checkNeedUpdate(bundleModel.getLocalVersion(), remotePluginModel.getFileVersion());
//                    Logger.d("d-patch", "needUpdate " + needUpdate);
//                    Logger.d("d-patch", "remotePluginModel status " + remotePluginModel.getStatus());
//                    if (needUpdate != Util.NOT_UPDATE) { // 这里是插件需要升级
//                        if (remotePluginModel.getStatus() != PluginInfoModel.STATUS_PLUGIN_DISCARD) { // 这里表示插件不需要废弃
//                            mNotBuildInBundleManager.checkAndDownloadNotBuildInBundle(bundleModel);
//                        }
//                    } else {  // 本地插件版本号和服务端插件版本号相同时要检测插件是否弃用，没弃用才去检测是否有patch
//                        if (remotePluginModel.getStatus() == PluginInfoModel.STATUS_PLUGIN_DISCARD) {
//                            scheduleRevertPlugin(mContext, bundleModel);
//                        } else {
//                            checkPluginPatch(bundleModel); //check 是否有补丁
//                        }
//                    }
                }
            }


            @Override
            public void onError(int code, String message) {
                PatchLoadInfoManager.getSingleInstance().receivePatchInfo("获取应用补丁错误, 定位:786 日志:" + message, -1);
            }
        });
    }


    /**
     * 删除bundleModel的patch
     *
     * @return true, revert成功，false revert失败
     */
    public boolean doRevertPluginPatch(Context context, BundleModel bundleModel) {
        if (context == null || bundleModel == null) {
            return false;
        }
        Logger.i(TAG, "doRevertPluginPatch for: " + bundleModel.bundleName);
        SharedPreferences pluginSp = context.getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE, Context.MODE_MULTI_PROCESS);
        String bundleStr = pluginSp.getString(SpConstants.KEY_DOWNLOAD_BUNDLE_MODEL(bundleModel), "");

        if (TextUtils.isEmpty(bundleStr)) {
            return false;
        }
        BundleModel downloadBundle = null;
        try {
            downloadBundle = new Gson().fromJson(bundleStr, BundleModel.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (downloadBundle != null && downloadBundle.patchPluginInfoModel != null) {
            downloadBundle.patchPluginInfoModel = null;

            bundleStr = new Gson().toJson(downloadBundle);

            pluginSp.edit().putString(SpConstants.KEY_DOWNLOAD_BUNDLE_MODEL(bundleModel), bundleStr).apply();

            File bundleDirA = new File(context.getDir("dex", Context.MODE_PRIVATE).getAbsolutePath() + File.separator + bundleModel.bundleName + File.separator + "a");
            File bundleDirB = new File(context.getDir("dex", Context.MODE_PRIVATE).getAbsolutePath() + File.separator + bundleModel.bundleName + File.separator + "b");
            File patchSoFile = new File(context.getDir("bundle_dir", Context.MODE_PRIVATE).getAbsolutePath() + File.separator + bundleModel.patchBundleName);

            boolean aDirDeleteSuccess = false;
            boolean bDirDeleteSuccess = false;
            boolean patchSoFileDeleteSuccess = false;

            if (!bundleDirA.exists()) {
                aDirDeleteSuccess = true;
            } else {
                try {
                    aDirDeleteSuccess = Util.deleteFolderKeepRootDir(bundleDirA.getAbsolutePath());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (!bundleDirB.exists()) {
                bDirDeleteSuccess = true;
            } else {
                try {
                    bDirDeleteSuccess = Util.deleteFolderKeepRootDir(bundleDirB.getAbsolutePath());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            patchSoFileDeleteSuccess = !patchSoFile.exists() || patchSoFile.delete();

            return aDirDeleteSuccess && bDirDeleteSuccess && patchSoFileDeleteSuccess;
        }
        return false;
    }


    /**
     * 删除bundleModel下载的插件，下次应用启动时使用build in的插件
     *
     * @return true, revert成功，false revert失败
     */
    public boolean doRevertPlugin(Context context, BundleModel bundleModel) {
        if (context == null || bundleModel == null) {
            return false;
        }
        SharedPreferences pluginSp = context.getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE, Context.MODE_MULTI_PROCESS);

        pluginSp.edit().putString(SpConstants.KEY_SAVE_BUNDLE_MODEL(bundleModel), "").apply();
        pluginSp.edit().putString(SpConstants.KEY_DOWNLOAD_BUNDLE_MODEL(bundleModel), "").apply();

        File soFile = new File(mContext.getDir("bundle_dir", Context.MODE_PRIVATE).getAbsolutePath() + File.separator + bundleModel.soFileName);

        return !soFile.exists() || soFile.delete();
    }

    public void scheduleRevertPlugin(Context context, BundleModel bundleModel) {
        if (context == null || bundleModel == null) {
            return;
        }

        SharedPreferences preferences = context.getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE, Context.MODE_MULTI_PROCESS);

        preferences.edit().putBoolean(SpConstants.NEED_REVERT_PLUGIN(bundleModel), true).apply();
    }

    public void scheduleRevertPluginPatch(Context context, BundleModel bundleModel) {
        if (context == null || bundleModel == null) {
            return;
        }

        SharedPreferences preferences = context.getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE, Context.MODE_MULTI_PROCESS);

        preferences.edit().putBoolean(SpConstants.NEED_REVERT_PLUGIN_PATCH(bundleModel), true).apply();
    }

    private void checkDispatchBundlePatch(final BundleModel dispatchBundle) {
        if (dispatchBundle == null || ToolUtil.isEmptyCollects(dispatchBundle.packageNameList)) {
            return;
        }
        String packageName = dispatchBundle.packageNameList.get(0);
        if (TextUtils.isEmpty(packageName)) {
            return;
        }
        PatchLoadInfoManager.getSingleInstance().receivePatchInfo("请求远程补丁详细信息 ", 0);
        BundleRequestCache.getInstance().getPatchInfo(packageName, new com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack<List<PluginInfoModel>>() {
            @Override
            public void onSuccess(@Nullable List<PluginInfoModel> object) {
                PluginInfoModel patchModel = null;
                if (ToolUtil.isEmptyCollects(object) || dispatchBundle.pluginInfoModel == null) {
                    PatchLoadInfoManager.getSingleInstance().receivePatchInfo("获取远程补丁,无远程补丁信息", -1);
                    return;
                }
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("dispatchBundleModel.pluginInfoModel.getId()");
                stringBuilder.append(dispatchBundle.pluginInfoModel.getId());

                for (PluginInfoModel pluginInfoModel : object) {
                    stringBuilder.append(pluginInfoModel.getBundleName());
                    stringBuilder.append(pluginInfoModel.getRefPatchId());
                    if (pluginInfoModel.getRefPatchId() == dispatchBundle.pluginInfoModel.getId()) {
                        patchModel = pluginInfoModel;
                        break;
                    }
                }
                Logger.d("d-patch", "checkDispatchBundlePatch 1 patchModel == null " + (patchModel == null));
                if (patchModel == null) {
                    PatchLoadInfoManager.getSingleInstance().receivePatchInfo("获取远程补丁信息失败 " + stringBuilder.toString(), -1);
                    return;
                }
                Logger.d("d-patch", "checkDispatchBundlePatch 2 " + patchModel.getFileVersion());
                dispatchBundle.patchPluginInfoModel = patchModel;
                if (TextUtils.isEmpty(patchModel.getFileVersion())) {
                    PatchLoadInfoManager.getSingleInstance().receivePatchInfo("服务端返回补丁版本号为空 ", -1);
                    return;
                }

                if (!Util.checkVersion(patchModel.getFileVersion())) {
                    PatchLoadInfoManager.getSingleInstance().receivePatchInfo("服务端返回补丁版本号不合法 " + patchModel.getFileVersion(), -1);
                    return;
                }

                if (TextUtils.isEmpty(dispatchBundle.patchSoFilePath)) {
                    PatchLoadInfoManager.log(-1, "本地patch路径不存在 " + dispatchBundle.toString());
                    return;
                }

                // 之前老逻辑
                // 1. 版本未升级，且远程版本不是回滚版本 则返回
                // 2. 本地patch不存在且远程版本不是回滚版本，则开始下载
                // 3. 本地patch存在且本地patch md5和远程patch md5不一致 且远程版本不是回滚版本 则开始下载
                // 4. 本地patch存在且本地patch md5和远程patch md5一致 远程版本不是回滚版本 则开始patch合并
                // 5. 本地patch存在且本地patch md5和远程patch md5一致 远程版本是回滚版本 则开始回滚

                int needUpdate = Util.checkNeedUpdate(dispatchBundle.hostCurrentUsePatchVersion, patchModel.getFileVersion());
                Logger.d("d-patch", "checkDispatchBundlePatch 3 needUpdate: " + needUpdate + ", " + dispatchBundle.hostCurrentUsePatchVersion + ", patchModel version " + patchModel.getFileVersion());
                File patchSoFile = new File(dispatchBundle.patchSoFilePath);
                String md5 = Util.getMd5ByFile(patchSoFile);
                Logger.d("d-patch", "patchSoFile exists " + patchSoFile.exists() + ", patchFile md5 " + md5 + ", patchModel md5 " + patchModel.getFileMd5());

                // 如果当前patch版本在回滚版本历史里 则需要revert
                if (patchModel.discardComposContain(dispatchBundle.hostCurrentUsePatchVersion)) {
                    Logger.d("d-patch", "discardComposContain " + dispatchBundle.hostCurrentUsePatchVersion);
                    if (patchSoFile.exists() && patchModel.getFileMd5() != null && patchModel.getFileMd5().equals(md5)) {
                        Logger.d("d-patch", "discardComposContain scheduleRevertPluginPatch");
                        PatchLoadInfoManager.log(-1, "当前patch在历史回滚patch中 执行回滚操作");
                        scheduleRevertPluginPatch(mContext, dispatchBundle);
                        return;
                    }
                }
                // 如果服务端下发回滚则不进行后续操作 如果当前版本一致 则需要revert
                Logger.d("d-patch", "patchModel status " + patchModel.getStatus() + ", needUpdate " + needUpdate);
                if (patchModel.getStatus() == PluginInfoModel.STATUS_DISCARD) {
                    if (needUpdate == Util.NOT_UPDATE) {
                        if (patchSoFile.exists() && patchModel.getFileMd5() != null && patchModel.getFileMd5().equals(md5)) {
                            PatchLoadInfoManager.log(-1, "服务端下发回滚操作 开始回滚 " + dispatchBundle.hostCurrentUsePatchVersion + ", patchModel version " + patchModel.getFileVersion());
                            scheduleRevertPluginPatch(mContext, dispatchBundle);
                        }
                    }
                    return;
                }

                if (needUpdate == Util.NOT_UPDATE && patchModel.getStatus() != PluginInfoModel.STATUS_DISCARD) {
                    String info = "服务端返回补丁版本号 小于 当前加载的补丁版本号 " + patchModel.getFileVersion() + " 当前补丁版本号 " + dispatchBundle.hostCurrentUsePatchVersion
                            + ", patch status = " + patchModel.getStatus();
                    PatchLoadInfoManager.getSingleInstance().receivePatchInfo(info, -1);
                    return;
                }

                Logger.d("d-patch", "checkDispatchBundlePatch 4 patchSoFilePath: " + dispatchBundle.patchSoFilePath);
                try {
                    Logger.d("d-patch", "checkDispatchBundlePatch 5 patchSoFile exists: " + patchSoFile.exists());
                    if (patchSoFile.exists()) {
                        PatchLoadInfoManager.getSingleInstance().receivePatchInfo("补丁已下载", 0);
                        dispatchBundle.patchPluginInfoModel = patchModel;
                        Logger.d("d-patch", "checkDispatchBundlePatch 6 patchModel.getFileMd5(): " + patchModel.getFileMd5() + ", patchSoFile md5: " + md5);
                        Logger.d("d-patch", "checkDispatchBundlePatch 7 patchModel.getStatus(): " + patchModel.getStatus());
                        if (!(patchModel.getFileMd5().equals(md5))) {
                            PatchLoadInfoManager.getSingleInstance().receivePatchInfo("签名验证失败 ", 0);
                            PatchLoadInfoManager.getSingleInstance().receivePatchInfo("远程补丁签名 " + patchModel.getFileMd5(), 0);
                            PatchLoadInfoManager.getSingleInstance().receivePatchInfo("本地补丁签名 " + md5, 0);
                            if (patchModel.getStatus() != PluginInfoModel.STATUS_DISCARD) {
                                PatchLoadInfoManager.getSingleInstance().receivePatchInfo("补丁状态 " + patchModel.getStatus(), 0);
                                PatchDownloadTask pluginDownloadTask = new PatchDownloadTask(dispatchBundle, BundleDownloadManager.getInstance());
                                pluginDownloadTask.canDownloadInMobile = true;
                                BaseBundleDownloadTask baseBundleDownloadTask = BundleDownloadManager.getInstance().getDownloadBundle(PatchDownloadTask.DOWNLOAD_TYPE_FOR_PATCH, dispatchBundle.bundleName);
                                if (baseBundleDownloadTask == null) {
                                    PatchLoadInfoManager.getSingleInstance().receivePatchInfo("开始下载远程补丁 " + patchModel.getStatus(), 0);
                                    BundleDownloadManager.getInstance().startDownloadBundle(pluginDownloadTask);
                                } else {
                                    PatchLoadInfoManager.getSingleInstance().receivePatchInfo("远程补丁正在下载中 ", 0);
                                }
                            }
                        } else {
                            PatchLoadInfoManager.getSingleInstance().receivePatchInfo("签名校验成功", 0);
                            PatchLoadInfoManager.getSingleInstance().receivePatchInfo("补丁状态 " + patchModel.getStatus(), 0);
                            if (patchModel.getStatus() != PluginInfoModel.STATUS_DISCARD) {
                                PatchLoadInfoManager.getSingleInstance().receivePatchInfo("执行补丁合并流程 定位:971 ", 0);
                                XMPatchService.runPatchService(mContext,
                                        dispatchBundle.patchSoFilePath,
                                        dispatchBundle.originApkPath,
                                        dispatchBundle.bundleName,
                                        dispatchBundle.patchPluginInfoModel.getFileVersion(),
                                        "",
                                        dispatchBundle.getLocalVersion(),
                                        dispatchBundle.usePatchDir);

                                //上报host patch安装信息，同一版本的patch只上报一次
                                SharedPreferences preferences = mContext.getSharedPreferences(SpConstants.FILE_HOST_SHARE_FILE, Context.MODE_MULTI_PROCESS);
                                boolean patchSuccess = preferences.getBoolean(SpConstants.KEY_HOST_PATCH_LOAD_SUCCESS, false);
                                if (patchSuccess) {
                                    String usingPatchVersion = preferences.getString(SpConstants.KEY_HOST_PATCH_VERSION, "");
                                    if (!TextUtils.isEmpty(usingPatchVersion)) {
                                        String stateBundleVersion = preferences.getString(SpConstants.KEY_HOST_STATE_BUNDLE_VERSION, "");
                                        String statePatchVersion = preferences.getString(SpConstants.KEY_HOST_STATE_PATCH_VERSION, "");

                                        if (!dispatchBundle.getLocalVersion().equals(stateBundleVersion) ||
                                                !usingPatchVersion.equals(statePatchVersion)) {
                                            new MyAsyncTask<Void, Void, Void>() {
                                                @Override
                                                protected Void doInBackground(Void... ss) {
                                                    try {
                                                        if (dispatchBundle.patchPluginInfoModel != null && dispatchBundle.patchPluginInfoModel.getId() != 0) {
                                                            Map<String, String> params = new HashMap<>();
                                                            params.put("installs", dispatchBundle.patchPluginInfoModel.getId() + "");
                                                            EncryptProxy.getGiftSignature(params);
                                                            CommonRequestM.getInstanse().downloadPluginStatistics(params, null);

                                                            SharedPreferences.Editor editor = preferences.edit();
                                                            editor.putString(SpConstants.KEY_HOST_STATE_BUNDLE_VERSION, dispatchBundle.getLocalVersion());
                                                            editor.putString(SpConstants.KEY_HOST_STATE_PATCH_VERSION, usingPatchVersion);
                                                            editor.apply();

                                                            Logger.i(TAG, "send host patch install message success ");

                                                        }
                                                    } catch (Exception e) {
                                                        Logger.i(TAG, "send host patch install message error " + e.toString());
                                                        e.printStackTrace();
                                                    }
                                                    return null;
                                                }
                                            }.myexec();
                                        }
                                    }
                                }
                            } else {
                                PatchLoadInfoManager.getSingleInstance().receivePatchInfo("补丁不可用，执行补丁回滚 ", 0);//todo
                                scheduleRevertPluginPatch(mContext, dispatchBundle);
                            }
                        }
                    } else {
                        Logger.d("d-patch", "checkDispatchBundlePatch 8 patchModel.getStatus(): " + patchModel.getStatus());
                        PatchLoadInfoManager.getSingleInstance().receivePatchInfo("开始下载远程补丁 ", 0);
                        if (patchModel.getStatus() != PluginInfoModel.STATUS_DISCARD) {
                            PatchDownloadTask pluginDownloadTask = new PatchDownloadTask(dispatchBundle, BundleDownloadManager.getInstance());
                            pluginDownloadTask.canDownloadInMobile = true;
                            BaseBundleDownloadTask baseBundleDownloadTask = BundleDownloadManager.getInstance().getDownloadBundle(PatchDownloadTask.DOWNLOAD_TYPE_FOR_PATCH, dispatchBundle.bundleName);
                            if (baseBundleDownloadTask == null) {
                                BundleDownloadManager.getInstance().startDownloadBundle(pluginDownloadTask);
                            } else {
                                PatchLoadInfoManager.getSingleInstance().receivePatchInfo("远程补丁正在下载中 ", 0);
                            }
                        }
                    }
                } catch (Throwable t) {
                    PatchLoadInfoManager.getSingleInstance().receivePatchInfo("获取补丁异常，定位:1037 日志:" + t.getMessage(), 0);
                    t.printStackTrace();
                }
            }

            @Override
            public void onError(int code, String message) {
                Logger.i(TAG, "check if has patch error");
                PatchLoadInfoManager.getSingleInstance().receivePatchInfo("请求补丁异常 errorCode:" + code + " message:" + message, -1);
            }
        });

    }

    /**
     * 检测bundle 正在使用的插件是否存在 patch
     *
     * @param bundleModel bundle
     */
    private void checkPluginPatch(BundleModel bundleModel) {

        String packageName = bundleModel.packageNameList.get(0);

        if (TextUtils.isEmpty(packageName)) {
            return;
        }
        Logger.d("d-patch", "checkPluginPatch packageName " + packageName);

        BundleRequestCache.getInstance().getPatchInfo(packageName, new IDataCallBack<List<PluginInfoModel>>() {
            @Override
            public void onSuccess(@Nullable List<PluginInfoModel> object) {
                if (ToolUtil.isEmptyCollects(object)) {
                    return;
                }
                MyAsyncTask.execute(() -> {
                    PluginInfoModel patchModel = null;
                    if (object.size() <= 0
                            || bundleModel.pluginInfoModel == null) {
                        return;
                    }

                    for (PluginInfoModel pluginInfoModel : object) {
                        if (pluginInfoModel.getRefPatchId() == bundleModel.pluginInfoModel.getId()) {
                            patchModel = pluginInfoModel;
                            break;
                        }
                    }
                    if (patchModel == null) {
                        return;
                    }
                    bundleModel.patchPluginInfoModel = patchModel;

                    if (TextUtils.isEmpty(bundleModel.patchSoFilePath)) {
                        return;
                    }
                    try {
                        File patchSoFile = new File(bundleModel.patchSoFilePath);
                        Logger.d("d-patch", "patchSoFile exists " + patchSoFile.exists());
                        if (patchSoFile.exists()) {
                            bundleModel.patchPluginInfoModel = patchModel;
                            String patchSoFileMd5 = Util.getMd5ByFile(patchSoFile);
                            Logger.d("d-patch", "patchModel md5 " + patchModel.getFileMd5() + ", status " + patchModel.getStatus());
                            Logger.d("d-patch", "patchSoFile md5 " + patchSoFileMd5);
                            if (!(patchModel.getFileMd5().equals(patchSoFileMd5))) {
                                if (patchModel.getStatus() != PluginInfoModel.STATUS_DISCARD) {
                                    PatchDownloadTask pluginDownloadTask = new PatchDownloadTask(bundleModel, BundleDownloadManager.getInstance());
                                    BaseBundleDownloadTask baseBundleDownloadTask = BundleDownloadManager.getInstance().getDownloadBundle(PatchDownloadTask.DOWNLOAD_TYPE_FOR_PATCH, bundleModel.bundleName);
                                    if (baseBundleDownloadTask == null) {
                                        BundleDownloadManager.getInstance().startDownloadBundle(pluginDownloadTask);
                                    }
                                }
                            } else {
                                if (patchModel.getStatus() == PluginInfoModel.STATUS_DISCARD) { //当前正在使用的patch 在服务端已经被标记为弃用
                                    scheduleRevertPluginPatch(mContext, bundleModel);
                                } else {
                                    XMPatchService.runPatchService(mContext,
                                            bundleModel.patchSoFilePath,
                                            bundleModel.originApkPath,
                                            bundleModel.bundleName,
                                            bundleModel.patchPluginInfoModel.getFileVersion(),
                                            bundleModel.dexFileName,
                                            bundleModel.getLocalVersion(),
                                            bundleModel.usePatchDir,
                                            true,
                                            false);
                                }
                            }
                        } else {
                            Logger.d("d-patch", "patchModel status " + patchModel.getStatus());
                            if (patchModel.getStatus() != PluginInfoModel.STATUS_DISCARD) {
                                PatchDownloadTask pluginDownloadTask = new PatchDownloadTask(bundleModel, BundleDownloadManager.getInstance());
                                BaseBundleDownloadTask baseBundleDownloadTask = BundleDownloadManager.getInstance().getDownloadBundle(PatchDownloadTask.DOWNLOAD_TYPE_FOR_PATCH, bundleModel.bundleName);
                                if (baseBundleDownloadTask == null) {
                                    BundleDownloadManager.getInstance().startDownloadBundle(pluginDownloadTask);
                                }
                            }
                        }
                    } catch (Throwable t) {
                        t.printStackTrace();
                    }
                });
            }

            @Override
            public void onError(int code, String message) {

            }
        });

    }

    @Override
    public void onAppGoToForeground(Activity startedActivity) {
        if (mHandler != null) {
            Logger.i(TAG, "remove MESSAGE_CHECK_EXIT_ADD");
            mHandler.removeMessages(MESSAGE_CHECK_EXIT_ADD);
        }
    }

    @Override
    public void onAppGoToBackground(@Nullable Activity stoppedActivity) {
        if (mHandler != null) {
            Logger.i(TAG, "send MESSAGE_CHECK_EXIT_ADD");
            mHandler.sendEmptyMessageDelayed(MESSAGE_CHECK_EXIT_ADD, 1000 * 60);
        }
    }

    private void checkExitApp() {
        Logger.i(TAG, "checkExitApp invoked");

        if (XmPlayerManager.getInstance(mContext).isPlaying()) {
            return;
        }

        ApplicationManager.checkExitApp(mContext);
    }

    public void checkBundleUpdate(BundleModel bundleModel) {
        checkBundleUpdate(bundleModel, false);
    }

    public void checkBundleUpdateForAppDebugCenter(BundleModel bundleModel) {
        if (!ProcessUtil.isMainProcess(mContext)) {
            return;
        }
        bundleModel.lastUpdateTime = System.currentTimeMillis();
        gotoBackGroundUpdate(bundleModel);
    }

    public void checkBundleUpdate(BundleModel bundleModel, boolean forceUpdate) {

        //build-in bundle 不需要安装和检查更新
        if (bundleModel == null || bundleModel.isBuildIn()) {
            return;
        }

        if (ConstantsOpenSdk.isDebug && BundleRequestCache.getInstance().isSimulateReleaseEv()) {
            return;
        }

        if (!forceUpdate
                && BundleRequestCache.isUseOldRequest()
                && (System.currentTimeMillis() - bundleModel.lastUpdateTime > CHECK_PLUGIN_TIME_INTERVAL)) {

            return;
        }

        if (!ProcessUtil.isMainProcess(mContext)) {// 主进程才允许更新，避免子进程用了更新版本的插件
            return;
        }

        bundleModel.lastUpdateTime = System.currentTimeMillis();
        gotoBackGroundUpdate(bundleModel);
    }


    /**
     * 启动首页绘制完成后，检查更新远程插件
     */
    public void checkAndUpdateAllBundle() {
        if (BundleRequestCache.getInstance().isSimulateReleaseEv()) {
            return;
        }
        Logger.i("cf_test", "启动检测更新插件----------checkAndUpdateAllBundle-------");
        Util.uploadXdcsPluginLog("启动检测更新插件----------checkAndUpdateAllBundle-------");
        ConfigureCenter.getInstance().registerConfigureCallback(new IConfigureCenter.ConfigureCallback() {
            @Override
            public void onResult(boolean success) {
                ConfigureCenter.getInstance().unRegisterConfigureCallback(this);
                if (!success) {
                    preLoadPluginList(null);
                    return;
                }
                String notPreLoadPluginListString = ConfigureCenter.getInstance().getString(
                        CConstants.Group_android.GROUP_NAME,
                        CConstants.Group_android.KEY_NOT_PRE_LOAD_PLUGIN_LIST, "");
                if (!TextUtils.isEmpty(notPreLoadPluginListString)) {
                    String[] notPreLoadPlugins = notPreLoadPluginListString.split(",");
                    List<String> notPreLoadPluginList = Arrays.asList(notPreLoadPlugins);
                    Logger.i("cf_test", "启动检测更新插件-----------------");
                    Util.uploadXdcsPluginLog("启动检测更新插件-----------------");
                    preLoadPluginList(notPreLoadPluginList);
                }
            }
        });
    }

    private void preLoadPluginList(List<String> notPreLoadPluginList) {
        Logger.i("cf_test", "预加载插件：——————preLoadPluginList");
        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                List<BundleModel> tmpBundleModels = new ArrayList<>(Configure.bundleList.size() + 1);

                tmpBundleModels.addAll(Configure.bundleList);

                Collections.sort(tmpBundleModels, new Comparator<BundleModel>() {
                    @Override
                    public int compare(BundleModel o1, BundleModel o2) {
                        if (o1.getDownloadPriority() > o2.getDownloadPriority()) {
                            return -1;
                        } else if (o1.getDownloadPriority() == o2.getDownloadPriority()) {
                            return 0;
                        } else {
                            return 1;
                        }
                    }
                });

                for (BundleModel bundleModel : tmpBundleModels) {
                    Logger.i("cf_test", "预加载插件1：——————" + bundleModel.bundleName);
                    synchronized (bundleModel) {
                        if (bundleModel.hasGenerateBundleFile) {
                            Logger.i("cf_test", "预加载插件2：——————" + bundleModel.bundleName);
                            continue;
                        }
                        if (bundleModel.inGenerateBundleFile) {
                            Logger.i("cf_test", "预加载插件3：——————" + bundleModel.bundleName);
                            continue;
                        }

                        if (bundleModel.isBuildIn()) {
                            Logger.i("cf_test", "预加载插件4：——————" + bundleModel.bundleName);
                            continue;
                        }

                        if (Configure.BUNDLE_DISPATCH.equals(bundleModel.bundleName)) {// dispatch bundle 跳过
                            continue;
                        }
                        Logger.i("cf_test", "预加载插件5：——————" + bundleModel.bundleName);
                        if (notPreLoadPluginList != null && notPreLoadPluginList.size() >0
                                && notPreLoadPluginList.contains(bundleModel.bundleName)) {
                            if (!(bundleModel.bundleName.equals(BUNDLE_READ) && ReadPreLoadModuleManager.isNeedPreInstallReadModule())) {
                                // 在黑名单中，且不是readbundle，且没有命中必须加载
                                Logger.i("cf_test", "命中黑名单不预加载插件：——————" + bundleModel.bundleName);
                                continue;
                            }
                        }

                        try {
                            BundleVersionUtil.initBundleLocalVersionNotCopyApk(bundleModel);
                            //initPathForBundle 的调用需要在 initBundleLocalVersionNotCopyApk之后
                            BundleFileManager.getInstance(mContext).initPathForBundle(bundleModel);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        Logger.i("cf_test", "预加载插件：——————" + bundleModel.bundleName);
                        downloadForAppStart(bundleModel);
                    }
                }

            }
        });
    }

    public static long CHECK_PLUGIN_TIME_INTERVAL = TimeUnit.HOURS.toMillis(2); //1个小时 check一次

    /**
     * 检测是否需要revert插件或插件的patch
     */
    public void checkRevert(Context context) {
        if (ToolUtil.isFirstInstallApp(context)) {
            return;
        }

        if (!BaseUtil.isMainProcess(context)) {
            return;
        }

        SharedPreferences preferences = context.getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE, Context.MODE_MULTI_PROCESS);
        boolean needKillOtherProcess = false;

        for (BundleModel bundleModel : Configure.bundleList) {
            if (Configure.dispatchBundleModel.bundleName.equals(bundleModel.bundleName)) {
                continue;
            }
            //NOTICE：不能在该方法中为bundle初始化各个路径的值
            boolean needRevertPlugin = preferences.getBoolean(SpConstants.NEED_REVERT_PLUGIN(bundleModel), false);
            if (needRevertPlugin) {
                boolean success = doRevertPlugin(context, bundleModel);
                if (success) {
                    preferences.edit().putBoolean(SpConstants.NEED_REVERT_PLUGIN(bundleModel), false).apply();

                    needKillOtherProcess = true;
                    continue;
                }
            }

            boolean needRevertPluginPatch = preferences.getBoolean(SpConstants.NEED_REVERT_PLUGIN_PATCH(bundleModel), false);

            if (needRevertPluginPatch) {
                boolean success = doRevertPluginPatch(context, bundleModel);
                if (success) {
                    preferences.edit().putBoolean(SpConstants.NEED_REVERT_PLUGIN_PATCH(bundleModel), false).apply();

                    needKillOtherProcess = true;
                }
            }
        }

        if (needKillOtherProcess) {
            Util.killAllOtherProcess(context);
        }
    }

    @SuppressLint("StaticFieldLeak")
    private void sendPatchInstallInfo(BundleModel dispatchBundleModel) {
        SharedPreferences preferences = mContext.getSharedPreferences(SpConstants.FILE_HOST_SHARE_FILE, Context.MODE_MULTI_PROCESS);
        boolean patchSuccess = preferences.getBoolean(SpConstants.KEY_HOST_PATCH_LOAD_SUCCESS, false);
        if (!patchSuccess) {
            return;
        }
        String usingPatchVersion = preferences.getString(SpConstants.KEY_HOST_PATCH_VERSION, "");
        if (TextUtils.isEmpty(usingPatchVersion)) {
            return;
        }
        String stateBundleVersion = preferences.getString(SpConstants.KEY_HOST_STATE_BUNDLE_VERSION, "");
        String statePatchVersion = preferences.getString(SpConstants.KEY_HOST_STATE_PATCH_VERSION, "");

        if (dispatchBundleModel.getLocalVersion().equals(stateBundleVersion) &&
                usingPatchVersion.equals(statePatchVersion)) {// 如果已经上报过，就不用再上报
            return;
        }
        new MyAsyncTask<Void, Void, Void>() {
            @Override
            protected Void doInBackground(Void... ss) {
                try {
                    if (dispatchBundleModel.patchPluginInfoModel == null || dispatchBundleModel.patchPluginInfoModel.getId() == 0) {
                        return null;
                    }

                    Map<String, String> params = new HashMap<>();
                    params.put("installs", dispatchBundleModel.patchPluginInfoModel.getId() + "");
                    EncryptProxy.getGiftSignature(params);
                    CommonRequestM.getInstanse().downloadPluginStatistics(params, null);

                    SharedPreferences.Editor editor = preferences.edit();
                    editor.putString(SpConstants.KEY_HOST_STATE_BUNDLE_VERSION, dispatchBundleModel.getLocalVersion());
                    editor.putString(SpConstants.KEY_HOST_STATE_PATCH_VERSION, usingPatchVersion);
                    editor.apply();

                    Logger.i(TAG, "send host patch install message success ");
                } catch (Exception e) {
                    Logger.i(TAG, "send host patch install message error " + e.toString());
                    e.printStackTrace();
                }
                return null;
            }
        }.myexec();

    }

    void downloadPluginFail(BundleModel bundleModel, String reason) {
        if (bundleModel == null) {
            return;
        }
        Logger.e(TAG, "downloadPluginFail " + bundleModel.bundleName + "  原因： " + reason);

        onRemoteInstallCallback(bundleModel, false, true, new Exception("下载插件失败 :" + reason));
    }

    void downloadSuccNotInstall(BundleModel bundleModel) {
        Logger.e(TAG, "downloadPluginSucc " + bundleModel + "  原因： 未安装");
        if (bundleModel == null) {
            return;
        }

        onRemoteInstallCallback(bundleModel, false, true, new Exception("下载插件成功，但是未安装~~"));

    }

    @Override
    public boolean handleMessage(Message msg) {
        switch (msg.what) {
            case MESSAGE_CHECK_EXIT_ADD:
                checkExitApp();
                return true;
        }
        return false;
    }
}
