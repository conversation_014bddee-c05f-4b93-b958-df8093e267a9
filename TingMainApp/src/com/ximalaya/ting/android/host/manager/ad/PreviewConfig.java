package com.ximalaya.ting.android.host.manager.ad;

/**
 * Created by xmly on 2019-08-26.
 *
 * <AUTHOR>
 */
public class PreviewConfig {

    private String itingUrl;

    private boolean isDialog;

    private boolean isBanner;

    private String requestUrl;

    private String positionName;

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    public String getItingUrl() {
        return itingUrl;
    }

    public void setItingUrl(String itingUrl) {
        this.itingUrl = itingUrl;
    }

    public boolean isDialog() {
        return isDialog;
    }

    public void setDialog(boolean dialog) {
        isDialog = dialog;
    }

    public boolean isBanner() {
        return isBanner;
    }

    public void setBanner(boolean banner) {
        isBanner = banner;
    }


    public static final class PreviewConfigBuilder {
        private String itingUrl;
        private boolean isDialog;
        private boolean isBanner;
        private String requestUrl;
        private String positionName;

        public PreviewConfigBuilder itingUrl(String itingUrl) {
            this.itingUrl = itingUrl;
            return this;
        }

        public PreviewConfigBuilder isDialog(boolean isDialog) {
            this.isDialog = isDialog;
            return this;
        }

        public PreviewConfigBuilder isBanner(boolean isLiveBanner) {
            this.isBanner = isLiveBanner;
            return this;
        }

        public PreviewConfigBuilder requestUrl(String requestUrl) {
            this.requestUrl = requestUrl;
            return this;
        }

        public PreviewConfigBuilder positionName(String positionNae) {
            this.positionName = positionNae;
            return this;
        }

        public PreviewConfig build() {
            PreviewConfig previewConfig = new PreviewConfig();
            previewConfig.setItingUrl(itingUrl);
            previewConfig.setRequestUrl(requestUrl);
            previewConfig.isDialog = this.isDialog;
            previewConfig.isBanner = this.isBanner;
            previewConfig.positionName = this.positionName;
            return previewConfig;
        }
    }
}
