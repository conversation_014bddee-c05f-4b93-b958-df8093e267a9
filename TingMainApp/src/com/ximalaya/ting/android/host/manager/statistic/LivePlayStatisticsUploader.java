package com.ximalaya.ting.android.host.manager.statistic;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothProfile;
import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;

import androidx.collection.ArrayMap;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.constants.PreferenceConstantsLib;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.xdcs.model.UserTrackCookie;
import com.ximalaya.ting.android.opensdk.constants.HttpParamsConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.TicketConstantsKt;
import com.ximalaya.ting.android.opensdk.model.history.XmPlayRecord;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 2018/4/27.
 */

public class LivePlayStatisticsUploader extends BasePlayStatisticsUploaderInMain {
    LivePlayStatisticsUploader(@NonNull Looper looper, Track track) {
        super(looper);

        mXmPlayRecord.setId(track.getDataId());
        mXmPlayRecord.setLiveRoomId(track.getLiveRoomId());
        if (track.getAnnouncer() != null) {
            mXmPlayRecord.setAnchorId(track.getAnnouncer().getAnnouncerId());
        }
    }

    LivePlayStatisticsUploader(@NonNull Looper looper, XmPlayRecord record) {
        super(looper);

        mXmPlayRecord = record;
    }

    @Override
    protected boolean isStatisticsUploader() {
        return true;
    }

    @Override
    protected void onPreDoPost() {
        UserTrackCookie.getInstance().setXmPlayResource(mXmPlayRecord.getXmUploadPlayResource());
        UserTrackCookie.getInstance().setXmTid(mXmPlayRecord.getTid());
    }

    @Override
    public Map<String, String> getHeader() {
        if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_PLAY_STATISTICS_UPLOAD_TICKET, true)) {
            Map<String, String> header = new ArrayMap<>();
            String ticket = CommonRequestM.getTicket(TicketConstantsKt.TYPE_PLAY_STATISTICS);
            header.put("x-tk", String.valueOf(ticket));
            return header;
        }
        return super.getHeader();
    }

    @NonNull
    @Override
    public Map<String, String> getParams() {
        if (mXmPlayRecord.getConnect_type() == 1) {
            try {
                BluetoothAdapter mBluetoothAdapter = BluetoothAdapter
                        .getDefaultAdapter();
                if (mBluetoothAdapter.isEnabled()
                        && mBluetoothAdapter
                        .getProfileConnectionState(BluetoothProfile.A2DP) == BluetoothProfile.STATE_CONNECTED) {
                    //重构的时候这地方就没有代码
                } else {
                    mXmPlayRecord.setConnectType(0);
                    mXmPlayRecord.setConnectDevice(0);
                    mXmPlayRecord.setConnectDeviceName(null);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        Map<String, String> param = new HashMap<>();
        param.put(HttpParamsConstants.PARAM_LIVE_ID, String.valueOf(mXmPlayRecord.getId()));
        param.put(HttpParamsConstants.PARAM_ROOM_ID, String.valueOf(mXmPlayRecord.getLiveRoomId()));
        param.put(HttpParamsConstants.PARAM_ANCHOR_ID, String.valueOf(mXmPlayRecord.getAnchorId()));

        param.put(HttpParamsConstants.PARAM_STARTED_AT, String.valueOf(mXmPlayRecord.getStartTime()));
        if (mXmPlayRecord.getEndTime() == 0) {
            param.put(HttpParamsConstants.PARAM_ENDED_AT, String.valueOf(System.currentTimeMillis()));
        } else {
            param.put(HttpParamsConstants.PARAM_ENDED_AT, String.valueOf(mXmPlayRecord.getEndTime()));
        }

        param.put(HttpParamsConstants.PARAM_DURATION, String.valueOf(mXmPlayRecord.getDuration()));
        param.put(HttpParamsConstants.PARAM_BREAK_SECOND, String.valueOf(mXmPlayRecord.getStartedPosition() / 1000));
        param.put(HttpParamsConstants.PARAM_PLAY_URL, mXmPlayRecord.getTrack_url());
        param.put(HttpParamsConstants.PARAM_SEND_DATA_TIME, String.valueOf(System.currentTimeMillis()));
        param.put(HttpParamsConstants.PARAM_BLOCK_COUNT, String.valueOf(mXmPlayRecord.getBlockCount()));
        param.put(HttpParamsConstants.PARAM_BLOCK_DURATION, String.valueOf(mXmPlayRecord.getBlockDuration()));
        param.put(HttpParamsConstants.PARAM_PLAY_SOURCE, String.valueOf(mXmPlayRecord.getPlaySource()));

        String mDownloadedSize = null;

        if (null != BaseApplication.getMyApplicationContext()) {
            mDownloadedSize = SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).
                    getString(PreferenceConstantsLib.XFramework_KEY_DOWNLOAD_SEZE);
        }

        try {
            if (mDownloadedSize != null && Long.valueOf(mDownloadedSize) != null && Long.valueOf(mDownloadedSize) == 0 && mXmPlayRecord.getDuration() > 0) {

                String curPlayUrl = null;
                if (XmPlayerService.getPlayerSrvice() != null) {
                    curPlayUrl = XmPlayerService.getPlayerSrvice().getCurPlayUrl();
                }
                if (curPlayUrl != null) {
                    if (curPlayUrl.contains("m3u8")) {
                        int bitIndex = curPlayUrl.indexOf("m3u8");
                        String bit = curPlayUrl.substring(bitIndex - 3, bitIndex - 1);
                        if ("24".equals(bit)) {
                            param.put(HttpParamsConstants.PARAM_TRAFFIC,
                                    "" + mXmPlayRecord.getDuration() * 2600);
                        }
                        if ("64".equals(bit)) {
                            param.put(HttpParamsConstants.PARAM_TRAFFIC,
                                    "" + mXmPlayRecord.getDuration() * 7800);
                        }
                    } else {
                        param.put(HttpParamsConstants.PARAM_TRAFFIC,
                                "" + mXmPlayRecord.getDuration() * 2600);
                    }
                } else {
                    param.put(HttpParamsConstants.PARAM_TRAFFIC,
                            "" + mXmPlayRecord.getDuration() * 2600);
                }
            } else {
                if (TextUtils.isEmpty(mDownloadedSize)) {
                    mDownloadedSize = String.valueOf(0);
                }
                param.put(HttpParamsConstants.PARAM_TRAFFIC, "" + mDownloadedSize);
            }
        } catch (NumberFormatException e) {
            if (TextUtils.isEmpty(mDownloadedSize)) {
                mDownloadedSize = String.valueOf(0);
            }
            param.put(HttpParamsConstants.PARAM_TRAFFIC, mDownloadedSize);
        }

        if(useV2Url()) {
            param.put(HttpParamsConstantsInOpenSdk.PARAM_NONCE, mNonceQueue.poll());
        }

        return param;
    }

    @NonNull
    @Override
    protected String getPostUrl() {
        return UrlConstants.getInstanse().getLiveStatisticsUrl();
    }

    @Nullable
    @Override
    protected String getPostUrlV2() {
        return UrlConstants.getInstanse().getLiveStatisticsUrlV2();
    }

    @Override
    public void onError(int code, String message) {
        if (NetworkType.isConnectTONetWork(BaseApplication.getMyApplicationContext())) {
            XDCSCollectUtil.statErrorToXDCS("playstatisRequest", code + message);

        }
    }

}
