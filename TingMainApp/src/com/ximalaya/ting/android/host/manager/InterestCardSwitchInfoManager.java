package com.ximalaya.ting.android.host.manager;

import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.setting.InterestCardSwitchInfo;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import androidx.annotation.Nullable;

/**
 * Created by WolfXu on 2020-01-07.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class InterestCardSwitchInfoManager {

    private InterestCardSwitchInfo mData;
    private boolean mHasChangeFreeListenTab = false;

    public static final String chooseLikeFragmentV5 = "v22";

    private InterestCardSwitchInfoManager() {

    }

    private static class InterestCardSwitchInfoManagerHolder {
        static InterestCardSwitchInfoManager sInstance = new InterestCardSwitchInfoManager();
    }

    public static InterestCardSwitchInfoManager getInstance() {
        return InterestCardSwitchInfoManagerHolder.sInstance;
    }

    public boolean isNewUserForNovelTab() {
        return mData != null && "new".equals(mData.userType);
    }

    public void clearData() {
        mData = null;
    }

    public void loadData(IDataCallBack<InterestCardSwitchInfo> callback) {
        //默认当设置页处理
        loadData(false,1, callback);
    }

    /**
     *
     * @param from 0 首页，1-设置, 2-播放卡片
     */
    public void loadData(boolean hasIting, int from, IDataCallBack<InterestCardSwitchInfo> callback) {
        if (mData != null) {
            if (callback != null) {
                callback.onSuccess(mData);
            }
        } else {
            CommonRequestM.requestIsInterestCardOfHomepage(hasIting, from, new IDataCallBack<InterestCardSwitchInfo>() {
                @Override
                public void onSuccess(@Nullable InterestCardSwitchInfo object) {
                    if (object != null) {
                        mData = object;
                        if (!mHasChangeFreeListenTab && !NovelTabAbManager.INSTANCE.getHasClickVipTab()
                                && !NovelTabAbManager.INSTANCE.showNovelTab() && !NovelTabAbManager.showCategoryV2Tab()
                                && isNewUserForNovelTab()) {
                            mHasChangeFreeListenTab = true;
                            NovelTabAbManager.INSTANCE.changeVipTabToNovel(true);
                        }
                        NewUserRightsDialogManager.INSTANCE.setPopInfo(object.popInfo);
                    }
                    if (callback != null) {
                        callback.onSuccess(object);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (callback != null) {
                        callback.onError(code, message);
                    }
                }
            });
        }
    }
}
