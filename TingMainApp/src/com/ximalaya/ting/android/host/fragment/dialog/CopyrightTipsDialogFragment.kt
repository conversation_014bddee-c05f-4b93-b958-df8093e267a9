package com.ximalaya.ting.android.host.fragment.dialog

import android.Manifest
import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.location.LocationManager
import android.os.Bundle
import android.provider.Settings
import android.text.Spannable
import android.text.SpannableString
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.StyleSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.manager.account.XmLocationManager
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction.IPermissionListener
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.play.CopyrightExtendInfo
import com.ximalaya.ting.android.host.util.extension.dp
import java.util.*

/**
 * Created by WolfXu on 7/20/21.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber ***********
 */
class CopyrightTipsDialogFragment : BaseDialogFragment<CopyrightTipsDialogFragment>() {
    companion object {
        private const val LOCATION_START_TAG = "<location>"
        private const val LOCATION_END_TAG = "</location>"
        private const val BOLD_START_TAG = "<b>"
        private const val BOLD_END_TAG = "</b>"
        private const val BUNDLE_KEY_COPYRIGHT_INFO = "copyright_info"
        private const val TOP_IMG_URL = "https://imagev2.xmcdn.com/storages/503b-audiofreehighqps/E4/7E/CMCoOSYEzFynAAFE-gDK5jsy.webp"

        fun newInstance(copyrightExtendInfo: CopyrightExtendInfo): CopyrightTipsDialogFragment {
            val args = Bundle()
            args.putParcelable(BUNDLE_KEY_COPYRIGHT_INFO, copyrightExtendInfo)
            val fragment = CopyrightTipsDialogFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private var mCopyrightExtendInfo: CopyrightExtendInfo? = null
    private var mVCancelBtn: View? = null
    private var mTvActionBtn: TextView? = null
    private var mShowHimalayaBtn = true

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        configureDialogStyle()
        return inflater.inflate(R.layout.host_dialog_fra_copyright_tips, container, false)
    }

    private fun configureDialogStyle() {
        val dialog = dialog ?: return
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val window = dialog.window
        if (window != null) {
            window.setWindowAnimations(R.style.host_dialog_window_animation_fade)
            window.setBackgroundDrawableResource(R.color.host_transparent)
            window.setGravity(Gravity.CENTER)
        }
    }

    override fun onStart() {
        super.onStart()
        val width = BaseUtil.getScreenWidth(context) - 50.dp * 2
        dialog?.window?.setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        init()
    }

    private fun init() {
        mCopyrightExtendInfo = arguments?.getParcelable(BUNDLE_KEY_COPYRIGHT_INFO)
        val copyrightExtendInfo = mCopyrightExtendInfo
        if (copyrightExtendInfo == null) {
            dismiss()
            return
        }
        mShowHimalayaBtn = copyrightExtendInfo.isDownHimalayaApp

        val tvContent: TextView? = findViewById(R.id.host_tv_content) as? TextView
        tvContent?.let {
            it.movementMethod = LinkMovementMethod.getInstance()
            it.text = processContent(copyrightExtendInfo)
        }

        mTvActionBtn = findViewById(R.id.host_tv_action_btn) as? TextView

        val ivTopImg: ImageView? = findViewById(R.id.host_iv_top_img) as? ImageView
        if (mShowHimalayaBtn) {
            ImageManager.from(context).displayImage(ivTopImg, TOP_IMG_URL, -1)
        } else {
            ivTopImg?.visibility = View.GONE
            val tvTitle: TextView? = findViewById(R.id.host_tv_title) as? TextView
            tvTitle?.text = getStringSafe(R.string.host_copyright_limit_tips)
            mTvActionBtn?.text = getStringSafe(R.string.host_refetch_location)
        }

        mVCancelBtn = findViewById(R.id.host_tv_cancel_btn)
        mVCancelBtn?.setOnClickListener(mOnClickListener)
        mTvActionBtn?.setOnClickListener(mOnClickListener)
    }

    private fun processContent(copyrightExtendInfo: CopyrightExtendInfo): SpannableString {
        val stringBuilder = StringBuilder(copyrightExtendInfo.msg)
        var locationStartIndex = stringBuilder.indexOf(LOCATION_START_TAG)
        var locationEndIndex = stringBuilder.indexOf(LOCATION_END_TAG)
        var boldStartIndex = stringBuilder.indexOf(BOLD_START_TAG)
        var boldEndIndex = stringBuilder.indexOf(BOLD_END_TAG)
        val deleteLocationTagTask: () -> Int = {
            if (locationStartIndex in 0 until locationEndIndex) {
                stringBuilder.delete(locationStartIndex, locationStartIndex + LOCATION_START_TAG.length)
                locationEndIndex -= LOCATION_START_TAG.length
                stringBuilder.delete(locationEndIndex, locationEndIndex + LOCATION_END_TAG.length)
                LOCATION_START_TAG.length + LOCATION_END_TAG.length
            } else {
                0
            }
        }
        val deleteBoldTagTask: () -> Int = {
            if (boldStartIndex in 0 until boldEndIndex) {
                stringBuilder.delete(boldStartIndex, boldStartIndex + BOLD_START_TAG.length)
                boldEndIndex -= BOLD_START_TAG.length
                stringBuilder.delete(boldEndIndex, boldEndIndex + BOLD_END_TAG.length)
                BOLD_START_TAG.length + BOLD_END_TAG.length
            } else {
                0
            }
        }
        if (locationEndIndex < boldStartIndex) {
            val deleteCount = deleteLocationTagTask()
            boldStartIndex -= deleteCount
            boldEndIndex -= deleteCount
            deleteBoldTagTask()
        } else if (boldEndIndex < locationStartIndex) {
            val deleteCount = deleteBoldTagTask()
            locationStartIndex -= deleteCount
            locationEndIndex -= deleteCount
            deleteLocationTagTask()
        }

        val spannableString = SpannableString(stringBuilder)
        if (boldStartIndex >= 0 && boldEndIndex <= spannableString.length && boldStartIndex < boldEndIndex) {
            spannableString.setSpan(StyleSpan(Typeface.BOLD), boldStartIndex, boldEndIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        }
        if (locationStartIndex >= 0 && locationEndIndex <= spannableString.length && locationStartIndex < locationEndIndex) {
            val clickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    if (OneClickHelper.getInstance().onClick(widget)) {
                        fetchLocation()
                        dismiss()
                    }
                }
            }
            spannableString.setSpan(clickableSpan, locationStartIndex, locationEndIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        }
        return spannableString
    }

    private fun fetchLocation() {
        checkPermission(object : HashMap<String?, Int?>() {
            init {
                put(Manifest.permission.ACCESS_COARSE_LOCATION, null)
            }
        }, object : IPermissionListener {
            override fun havedPermissionOrUseAgree() {
                val context = BaseApplication.getMyApplicationContext() ?: return
                val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as? LocationManager
                if (locationManager?.isProviderEnabled(LocationManager.NETWORK_PROVIDER) == false) {
                    val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                    if (intent.resolveActivity(context.packageManager) != null) {
                        val activity = BaseApplication.getOptActivity()
                        activity?.startActivity(intent)
                        HandlerManager.postOnUIThreadDelay({
                            XmLocationManager.getInstance().requestLocationInfo(BaseApplication.getMyApplicationContext())
                        }, 5000)
                    }
                }
                XmLocationManager.getInstance().requestLocationInfo(BaseApplication.getMyApplicationContext())
            }

            override fun userReject(noRejectPermiss: Map<String, Int>) {}
        })
    }

    private val mOnClickListener = View.OnClickListener {
        if (OneClickHelper.getInstance().onClick(it)) {
            when (it) {
                mVCancelBtn -> dismiss()
                mTvActionBtn -> {
                    if (mShowHimalayaBtn) {
                        val url = mCopyrightExtendInfo?.downHimalayaAppUrl
                        val activity = activity
                        if (url != null && activity is MainActivity) {
                            NativeHybridFragment.start(activity, url, true)
                            dismiss()
                        }
                    } else {
                        fetchLocation()
                        dismiss()
                    }
                }
            }
        }
    }
}