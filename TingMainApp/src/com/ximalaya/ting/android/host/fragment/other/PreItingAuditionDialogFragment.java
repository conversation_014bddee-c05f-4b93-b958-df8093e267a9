package com.ximalaya.ting.android.host.fragment.other;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

/**
 * Iting跳转前置对话框
 *
 * <AUTHOR>
 */
public class PreItingAuditionDialogFragment extends BaseDialogFragment {

    private String mPicUrl;
    private String mUrl;
    private String mShowTitle;
    private ImageView mIvCover;
    private TextView mTvTitle;
    private View mView;
    private long mVideoId;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable
            Bundle savedInstanceState) {
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        Window window = getDialog().getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            mView = inflater.inflate(R.layout.host_dialog_pre_iting_audition,  ((ViewGroup) window.findViewById(android.R.id.content)), false);
            window.setLayout(BaseUtil.dp2px(getContext(), 312), ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        if (mView != null) {
            mIvCover = mView.findViewById(R.id.host_iv_cover);
            mTvTitle = mView.findViewById(R.id.host_tv_title);
            View vClose = mView.findViewById(R.id.host_iv_close);
            View vContinue = mView.findViewById(R.id.host_tv_continue);
            vClose.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                }
            });
            vContinue.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    toIting();
                }
            });
            AutoTraceHelper.bindData(vContinue, "");
            AutoTraceHelper.bindData(vClose, "");
            loadData();
        }
        new UserTracking().setDubMaterialId(mVideoId)
                .setSrcPage("首页_推荐")
                .setModuleType("声临其境海选录制弹窗")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
        return mView;
    }

    private void loadData() {
        try {
//                loadTrack();
            mTvTitle.setText(mShowTitle);
            ImageManager.from(getContext()).displayImage(mIvCover, mPicUrl, R.drawable.host_default_album);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void toIting() {
        dismiss();
        new UserTracking().setDubMaterialId(mVideoId)
                .setItemId("前往录制")
                .setItem("button")
                .setSrcPage("首页_推荐")
                .setSrcModule("声临其境海选录制弹窗")
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
        NativeHybridFragment.start((MainActivity) BaseApplication.getMainActivity(), mUrl, false);
    }

    public void show(FragmentManager manager, String tag, String url, String picUrl, String showTitle, String videoId) {
        super.show(manager, tag);
        mUrl = url;
        mPicUrl = picUrl;
        mShowTitle = showTitle;
        if(!TextUtils.isEmpty(videoId)) {
            try {
                mVideoId =Long.valueOf(videoId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        MainActivity.isPreItingShowing = false;
    }
}
