package com.ximalaya.ting.android.host.fragment.dialog

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentManager
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.xmtrace.XMTraceApi




/**
 * <AUTHOR> lvpengcheng
 * time   : 2023/7/31
 * desc   : 爸爸妈妈讲故事会员引导弹窗
 */
class KidAiSoundVipGuideDialog : BaseDialogFragment<KidAiSoundVipGuideDialog>() {

    private var onBtnClick: () -> Unit = {}

    fun setOnBtnClickListener(onBtnClick: () -> Unit) {
        this.onBtnClick = onBtnClick
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        configureDialogStyle()
        return inflater.inflate(R.layout.host_kid_dialog_ai_sound_vip_guide, container, false)
    }

    private fun configureDialogStyle() {
        val dialog: Dialog = dialog ?: return
        dialog.setCanceledOnTouchOutside(false)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val win = dialog.window ?: return
        win.setDimAmount(0.5f)
        val lp: WindowManager.LayoutParams = win.attributes
        lp.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
        lp.width = WindowManager.LayoutParams.MATCH_PARENT
        win.attributes = lp
        win.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
    }

    override fun onResume() {
        super.onResume()
        setDialogWidthToMatchScreen()
    }

    private fun setDialogWidthToMatchScreen() {
        dialog?.window?.let { window ->
            val params = window.attributes
            params.width = WindowManager.LayoutParams.MATCH_PARENT
            window.attributes = params
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUi()
    }

    fun initUi() {
        arguments ?: return
        findViewById(R.id.kid_iv_close).setOnClickListener {
            dismiss()
        }

        val experience = findViewById(R.id.kid_experience_btn) as TextView
        experience.setOnClickListener {
            dismiss()
            onBtnClick.invoke()
        }

        val bgImage = arguments?.getString(ARG_IMAGE_URL)
        val imageView = findViewById(R.id.kid_image_dialog_main) as ImageView
        ImageManager.from(context)
            .displayImage(imageView, bgImage, R.drawable.host_kid_ai_sound_vip_dialog_bg)

        val btnText = arguments?.getString(ARG_BUTTON_TEXT)
        if (!btnText.isNullOrBlank()) {
            experience.text = btnText
        }
    }

    companion object {

        private const val ARG_IMAGE_URL = "arg_image_url"
        private const val ARG_BUTTON_TEXT = "arg_button_text"

        fun show(
            fragmentManager: FragmentManager?,
            bgImage: String?,
            btnText: String?
        ): KidAiSoundVipGuideDialog {
            val dialog = KidAiSoundVipGuideDialog().apply {
                arguments = Bundle().apply {
                    putString(ARG_IMAGE_URL, bgImage)
                    putString(ARG_BUTTON_TEXT, btnText)
                }
            }
            fragmentManager?.let {
                dialog.show(fragmentManager, "KidAiSoundVipGuideDialog")
            }
            return dialog
        }
    }
}