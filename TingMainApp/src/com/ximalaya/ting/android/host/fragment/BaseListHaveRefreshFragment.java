package com.ximalaya.ting.android.host.fragment;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView.OnItemClickListener;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.framework.view.SlideView.IOnFinishListener;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.listener.IGotoTop;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.lang.reflect.Constructor;
import java.util.List;

/**
 * 含有RefreshListView的fragment可以继承此类 简化fragment的代码
 * <AUTHOR>
 * @param <T> 此页面请求的回来的 Model 此Model必须是通过ListModeBase包含的
 * @param <K> fragment 的Adapter 此Adapter必须继承HolderAdapter 并且实现了 XXXXX(Context
 *            context, List<T> listData); 如果有其他的参数需要传递则用public方法传递 (可以看
 *            AnchorAdapter的 setFragment() 方法)
 *
 */
public abstract class BaseListHaveRefreshFragment<T, K extends HolderAdapter<T>>
        extends BaseFragment2 implements OnItemClickListener,
        IRefreshLoadMoreListener, IDataCallBack<ListModeBase<T>> {

    protected boolean canRefresh = true;
    protected boolean canAddMore = true;

    protected int mPageId = 1;
    protected int mPrePageId = 0;
    protected boolean mIsLoading = false;
    protected boolean mNoDataShowContent = true; // 没有数据时是否显示提示

    protected RefreshLoadMoreListView mListView;
    protected K mAdapter;

    protected ListModeBase<T> mListModeBase;
    protected IDataCallBack<ListModeBase<T>> callBackM;
    protected IDataCallBack<ListModeBase<T>> extraPartsLoadCallback;
    private AbsListView.OnScrollListener mCustomSrcollListener;

    //	protected int mPlaySource;
    public BaseListHaveRefreshFragment(boolean canSiled, IOnFinishListener onFinishListener) {
        super(canSiled, SlideView.TYPE_FRAMELAYOUT, onFinishListener);
    }

    public BaseListHaveRefreshFragment(boolean canSiled, @SlideView.SlideViewContentViewLayoutType int slideViewContentViewLayoutType,
                                       IOnFinishListener onFinishListener) {

        super(canSiled, slideViewContentViewLayoutType, onFinishListener);
    }

    protected abstract Class<K> getHolderAdapterClass();

    protected abstract void myInitUi();

    protected abstract int mListViewId();

    protected int getPlaySource() {
        return -1;
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void initUi(Bundle savedInstanceState) {

        mListView = (RefreshLoadMoreListView) findViewById(mListViewId() > 0 ? mListViewId() : com.ximalaya.ting.android.framework.R.id.listview);

        mListView.getRefreshableView()
                .setPadding(
                        0,
                        getResourcesSafe().getDimensionPixelOffset(R.dimen.host_title_bar_height) + (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR ? BaseUtil.getStatusBarHeight(mContext) : 0),
                        0,
                        getResourcesSafe().getDimensionPixelSize(
                                R.dimen.host_bottom_bar_height));
        mListView.getRefreshableView().setClipToPadding(false);
        mListView.setOnItemClickListener(this);
        mPageId = 1;
        mAdapter = (K) createInstance(getHolderAdapterClass(), getActivity());
        myInitUi();

        mListView.setAdapter(mAdapter);

        if (!canRefresh) {
            mListView.setMode(Mode.DISABLED);
        }
        if (canAddMore) {
            mListView.setOnRefreshLoadMoreListener(this);
        } else {
            mListView.onRefreshComplete(false);
            mListView.setHasMoreNoFooterView(false);
        }

        mListView.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                if (mCustomSrcollListener != null) {
                    mCustomSrcollListener.onScrollStateChanged(view, scrollState);
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (mCustomSrcollListener != null) {
                    mCustomSrcollListener.onScroll(view, firstVisibleItem, visibleItemCount, totalItemCount);
                }

                if (getiGotoTop() != null && showGotoTop()) {
                    getiGotoTop().setState(firstVisibleItem >= 12);
                }
            }
        });
    }

    public boolean showGotoTop() {
        return true;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        if (getiGotoTop() != null) {
            getiGotoTop().addOnClickListener(gotoTop);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            if (getiGotoTop() != null) {
                getiGotoTop().addOnClickListener(gotoTop);
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();

        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(gotoTop);
        }
    }

    protected IGotoTop.IGotoTopBtnClickListener gotoTop = new IGotoTop.IGotoTopBtnClickListener() {
        @Override
        public void onClick(View v) {
            mListView.getRefreshableView().setSelection(0);
        }
    };

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mListView != null) {
            mListView.setOnRefreshLoadMoreListener(null);
            mListView.setOnItemClickListener(null);
            mListView.setAdapter(null);
        }
    }

    private HolderAdapter<T> createInstance(Class<K> holderAdapterClass,
                                            Context context) {

        K obj = null;
        try {
            try {
                Constructor<K> constructor = holderAdapterClass
                        .getDeclaredConstructor(Context.class,
                                List.class);
                obj = constructor.newInstance(context, null);
            } catch (NoSuchMethodException e) {
                try {
                    Constructor<K> constructor = holderAdapterClass
                            .getDeclaredConstructor(Context.class,
                                    List.class, BaseFragment.class);
                    obj = constructor.newInstance(context, null, this);
                } catch (NoSuchMethodException e1) {
                    Constructor<K> constructor = holderAdapterClass
                            .getDeclaredConstructor(MainActivity.class,
                                    List.class);
                    obj = constructor.newInstance(context, null);
                }
            }
        } catch (Exception e) {
            obj = null;
        }

        return obj;
    }

    @Override
    public void onRefresh() {
        mPageId = 1;
        if (mListView != null) {
            mListView.setFooterViewVisible(View.VISIBLE);
        }
        loadData();
    }

    @Override
    public void onMore() {
        loadData();
    }

    @Override
    protected void loadData() {
        if (mIsLoading) {
            return;
        }

        if (canUpdateUi() && mAdapter != null && mAdapter.getCount() == 0) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }

        mIsLoading = true;
        myLoadData(this);
    }

    public abstract void myLoadData(IDataCallBack<ListModeBase<T>> callBackM);

    @Override
    public void onSuccess(final ListModeBase<T> listModeBase) {
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                onSuccessAfterAnimation(listModeBase);
            }
        });

    }

    protected void onSuccessAfterAnimation(ListModeBase<T> listModeBase) {
        mIsLoading = false;
        if (!canUpdateUi()) {
            return;
        }

        onPageLoadingCompleted(LoadCompleteType.OK);

        if (extraPartsLoadCallback != null && listModeBase != null) {
            extraPartsLoadCallback.onSuccess(listModeBase);
        }

        // 如果本来就没有数据则直接返回
        if (!mNoDataShowContent && (listModeBase == null || listModeBase.getTotalCount() == 0 || listModeBase.getMaxPageId() == 0)) {
            mListView.onRefreshComplete();
            return;
        }

        if ((listModeBase == null || listModeBase.getList() == null || listModeBase.getList().isEmpty())) {

            if (mAdapter.getListData() != null && !mAdapter.getListData().isEmpty()) {
                mListView.onRefreshComplete(false);
            } else {
                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                mListView.onRefreshComplete();
            }

            if (callBackM != null) {
                callBackM.onSuccess(listModeBase);
            }
        } else {

            if (mListModeBase == null) {
                mListModeBase = listModeBase;
            }
            if (mPageId == 1) {
                if (mAdapter != null) {
                    mAdapter.clear();
                }
            }
            mListModeBase.updateListModeBaseParams(listModeBase);
            if (callBackM != null) {
                callBackM.onSuccess(listModeBase);
            }

            if (mAdapter != null)
                mAdapter.addListData(listModeBase.getList());

            if (mPageId == 1) {
                mListView.getRefreshableView().setSelection(0);
            }

            boolean haveNextPage = listModeBase.getMaxPageId() > mPageId;

            if (listModeBase.getMaxPageId() == -1) {
                haveNextPage = listModeBase.getPageSize() * mPageId < listModeBase.getTotalCount();
            }

            if (haveNextPage) {
                mListView.onRefreshComplete(true);
                mPageId++;
                mPrePageId++;
            } else {
                mListView.onRefreshComplete(false);
                onPageLoadingCompleted(LoadCompleteType.OK);
                onNoHasNextPage();
            }
        }
        doAfterLoadData(listModeBase);
    }

    public void onNoHasNextPage() {

    }

    @Override
    public void onError(int code, String message) {
        if (callBackM != null) {
            callBackM.onError(code, message);
        }

        mIsLoading = false;
        if (!canUpdateUi()) {
            return;
        }
        if (extraPartsLoadCallback != null) {
            extraPartsLoadCallback.onError(code, message);
        }

        if (mPageId == 1) {
            if (mAdapter != null) {
                mAdapter.clear();
            }
            mListView.onRefreshComplete(true);
            mListView.setHasMoreNoFooterView(false);
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        } else {
            CustomToast.showFailToast(message);
            mListView.onRefreshComplete(true);
        }

    }

    public void setRequestSucOrFailCallBack(
            IDataCallBack<ListModeBase<T>> callBackM) {
        this.callBackM = callBackM;
    }

    protected void setExtraPartsLoadCallback(IDataCallBack<ListModeBase<T>> callBackM) {
        this.extraPartsLoadCallback = callBackM;
    }

    @Override
    protected void loadDataError() {
        if (mListView != null) {
            mListView.setMode(Mode.DISABLED);
            mListView.setHasMoreNoFooterView(false);
        }
    }

    @Override
    protected void loadDataOk() {
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
        if (mListView != null) {
            mListView.setMode(Mode.PULL_FROM_START);
        }
    }

    protected void doAfterLoadData(ListModeBase data) {
    }

    protected void setListViewPadding(int left,int top,int right,int bottom){
        if (mListView != null && mListView.getRefreshableView() != null) {
            mListView.getRefreshableView().setPadding(left, top, right, bottom);
        }
    }

    protected void setCustomScrollListener(AbsListView.OnScrollListener listener) {
        mCustomSrcollListener = listener;
    }
}
