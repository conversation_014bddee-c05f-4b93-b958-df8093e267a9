package com.ximalaya.ting.android.host.fragment.dialog;

import android.os.Bundle;
import android.view.View;
import android.view.ViewStub;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.Guideline;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;

/**
 * 上边部分为半透明的 fragment（仿dialog）
 * <p>
 * Created by xiaolei on 2022/1/5.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13701664636
 */
public abstract class DialogLikeFragment extends BaseFragment2 {

    private View mRecordVShadow;
    private Guideline mGuideline;

    @Override
    protected String getPageLogicName() {
        return null;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mRecordVShadow = findViewById(R.id.host_v_shadow);
        mGuideline = findViewById(R.id.host_guide_line);
        mRecordVShadow.setOnClickListener(v -> {
            if (!OneClickHelper.getInstance().onClick(v)) {
                return;
            }
            dismiss();
        });
        ViewStub mRecordVsLayout = findViewById(R.id.host_vs_layout);
        if (mRecordVsLayout != null) {
            mRecordVsLayout.setLayoutResource(getLayoutId());
            if (mRecordVsLayout.getParent() != null) {
                View view = mRecordVsLayout.inflate();
                if (mGuideline != null) {
                    float ratio = getContentRatio();
                    if (ratio == 0F) {
                        view.measure(0, 0);
                        try {
                            mGuideline.setGuidelinePercent((DeviceUtil.getScreenHeight(mContext) - view.getMeasuredHeight()) * 1.0f / DeviceUtil.getScreenHeight(mContext));
                        } catch (Exception ignored) {
                        }
                        mGuideline.requestLayout();
                    } else if (ratio > 0F && ratio < 1F) {
                        mGuideline.setGuidelinePercent(1 - ratio);
                        mGuideline.requestLayout();
                    }
                }
            }
        }
        View space = findViewById(R.id.host_guide_space);
        if (space != null) {
            int dp = getCornerRadius();
            if (dp < 1) {
                dp = 1;
            }
            space.getLayoutParams().height = BaseUtil.dp2px(mContext, dp);
        }
        initLayoutUi();
        // 背景为前一个fragment
        setPreFragmentShow(true);
    }

    protected float getContentRatio() {
        return 0.7F;
    }

    protected int getCornerRadius() {
        return 10;
    }

    protected abstract void initLayoutUi();

    protected abstract int getLayoutId();

    public DialogLikeFragment() {
        super();
    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.host_fra_dialog_like;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        doAfterAnimation(() -> {
            if (mRecordVShadow != null) {
                mRecordVShadow.setVisibility(View.VISIBLE);
            }
        });
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mRecordVShadow != null) {
            mRecordVShadow.setVisibility(View.INVISIBLE);
        }
    }

    protected void dismiss() {
        finish();
    }

    public void launch(@NonNull BaseFragment2 fragment) {
        fragment.startFragment(this, com.ximalaya.ting.android.firework.R.anim.firework_in_bottom_anim, com.ximalaya.ting.android.firework.R.anim.firework_out_bottom_anim);
    }
}
