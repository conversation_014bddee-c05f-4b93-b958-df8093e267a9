package com.ximalaya.ting.android.host.activity.multidex;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.Window;
import android.view.WindowManager;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.xmutil.Logger;

import androidx.multidex.MultiDex;

/**
 * Created by jack.qin on 2016/6/2.
 * <AUTHOR>
 */
public class LoadResActivity extends Activity {
    //    private MyReceiver receiver;
    @Override
    public void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(savedInstanceState);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        overridePendingTransition(R.anim.host_null_anim, R.anim.host_null_anim);
        setContentView(R.layout.host_layout_load);
        new LoadDexTask().execute();
    }

    class LoadDexTask extends AsyncTask {
        @Override
        protected Object doInBackground(Object[] params) {
            try {
                MultiDex.install(getApplication());
                Logger.d("loadDex", "install finish");
                MainApplication.getInstance().installFinish(getApplication());
            } catch (Exception e) {
                Logger.e("loadDex", e.getLocalizedMessage());
            }
            return null;
        }

        @Override
        protected void onPostExecute(Object o) {
            finish();
        }
    }


    @Override
    public void onBackPressed() {
        //cannot backpress
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Logger.i("killloaddex", "loadresactivity_onDestroy" + System.currentTimeMillis());
        System.exit(0);
    }

    class MyReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            finish();
            Logger.i("killloaddex", "loadresactivity_onReceive" + System.currentTimeMillis());
        }
    }
}
