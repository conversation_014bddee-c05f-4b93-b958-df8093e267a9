package com.ximalaya.ting.android.host.activity;

import static com.ximalaya.ting.android.host.activity.login.XmThirdAppAuthorizeActivity.THIRD_APP_AUTHORIZE_FLAG;
import static com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost.KEY_CUSTOMIZED_INTEREST_CARD_MODEL_NEW;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ComponentCallbacks2;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ShortcutInfo;
import android.content.pm.ShortcutManager;
import android.content.res.ColorStateList;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Typeface;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.Icon;
import android.graphics.drawable.StateListDrawable;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Looper;
import android.os.MessageQueue;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RadioGroup.OnCheckedChangeListener;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.airbnb.lottie.LottieComposition;
import com.airbnb.lottie.LottieCompositionFactory;
import com.airbnb.lottie.LottieDrawable;
import com.airbnb.lottie.LottieListener;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.bumptech.glide.manager.SupportRequestManagerFragment;
import com.google.gson.Gson;
import com.handmark.pulltorefresh.library.internal.XmLoadingLayout;
import com.tencent.connect.common.Constants;
import com.tencent.tauth.IUiListener;
import com.tencent.tauth.Tencent;
import com.ximalaya.android.resource.offline.OfflineResourceAPI;
import com.ximalaya.ting.android.activity.FixLaunchModeBug;
import com.ximalaya.ting.android.ad.preload.PreloadAdManager;
import com.ximalaya.ting.android.ad.splashad.ISplashAdStateChange;
import com.ximalaya.ting.android.ad.splashad.SplashAdStateChangeManager;
import com.ximalaya.ting.android.adsdk.util.config.ABConfig;
import com.ximalaya.ting.android.apm.startup.StartUpMonitor;
import com.ximalaya.ting.android.apm.startup.StartUpRecord;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.configurecenter.base.IConfigureCenter;
import com.ximalaya.ting.android.configurecenter.exception.NoCreateSignatureException;
import com.ximalaya.ting.android.configurecenter.exception.NonException;
import com.ximalaya.ting.android.detect.ILogPoster;
import com.ximalaya.ting.android.detect.PhoneGrade;
import com.ximalaya.ting.android.downloadservice.base.IDownloadService;
import com.ximalaya.ting.android.encryptcheck.EncryptServiceCheckUtil;
import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.fileprotector.FileProtectorManager;
import com.ximalaya.ting.android.fileprotector.IFileAccessCallback;
import com.ximalaya.ting.android.firework.FireworkAgent;
import com.ximalaya.ting.android.firework.FireworkApi;
import com.ximalaya.ting.android.firework.FireworkDebugger;
import com.ximalaya.ting.android.firework.IHighPropertyFireworkCallback;
import com.ximalaya.ting.android.firework.Util;
import com.ximalaya.ting.android.firework.model.Firework;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;
import com.ximalaya.ting.android.framework.autosize.utils.AutoSizeLog;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.earn.statistics.EarnStatisticsLifeCycleCallback;
import com.ximalaya.ting.android.framework.earn.statistics.IFragmentShowListenerForPush;
import com.ximalaya.ting.android.framework.earn.statistics.PushArrivedTraceManager;
import com.ximalaya.ting.android.framework.earn.statistics.itingMonitor.ITingArrivedTraceManager;
import com.ximalaya.ting.android.framework.earn.statistics.itingMonitor.ITingArrivedTraceModel;
import com.ximalaya.ting.android.framework.fragment.BaseActivityLikeFragment;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.fragment.ManageFragment;
import com.ximalaya.ting.android.framework.manager.AlbumCollectManager;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelperManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.BitmapUtils;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.FragmentUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.framework.util.fixtoast.ToastCompat;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.framework.view.snackbar.Snackbar;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.XiMaLaYaService;
import com.ximalaya.ting.android.host.abtest.ABManager;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.activity.login.XmThirdAppAuthorizeActivity;
import com.ximalaya.ting.android.host.activity.manager.SplashScreenManager;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.chatxmly.ChatXmlyPopupManager;
import com.ximalaya.ting.android.host.constant.NewUserStartTimeConstants;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.data.request.HomePageTabRequestTask;
import com.ximalaya.ting.android.host.dialog.freelisten.FreeListenDialogTrace;
import com.ximalaya.ting.android.host.dialog.freelisten.FreeListenTimeDialogManager;
import com.ximalaya.ting.android.host.dialog.freelistennew.FreeListenNewRecallUserDialogManager;
import com.ximalaya.ting.android.host.drivemode.DriveModeActivityV3;
import com.ximalaya.ting.android.host.e2e.manager.PubSubServiceManager;
import com.ximalaya.ting.android.host.e2e.manager.PubSubServiceManagerKt;
import com.ximalaya.ting.android.host.firework.ChildProtectFireworkInterceptor;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.ad.SplashAdController;
import com.ximalaya.ting.android.host.fragment.ad.SplashAdRecord;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.fragment.other.PreItingAuditionDialogFragment;
import com.ximalaya.ting.android.host.fragment.play.PlayBarAbManager;
import com.ximalaya.ting.android.host.fragment.play.PlayBarFragment;
import com.ximalaya.ting.android.host.fragment.web.IWebFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.AdHybridFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.AdSDKHybridFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.hybrid.HybridFragment;
import com.ximalaya.ting.android.host.hybrid.load.ApmHybridLoadUploader;
import com.ximalaya.ting.android.host.hybrid.manager.WebViewPool;
import com.ximalaya.ting.android.host.imchat.imlogin.IMLoginManager;
import com.ximalaya.ting.android.host.imchat.unread.IMUnreadMsgManager;
import com.ximalaya.ting.android.host.listener.IBindAction;
import com.ximalaya.ting.android.host.listener.IChatMessageCallback;
import com.ximalaya.ting.android.host.listener.IChildProtectDialogShowCallBack;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.listener.IGotoTop;
import com.ximalaya.ting.android.host.listener.IKeyDispatch;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListenerAndCallBackUseChange;
import com.ximalaya.ting.android.host.listener.IPaymentAction;
import com.ximalaya.ting.android.host.listener.IPhotoAction;
import com.ximalaya.ting.android.host.listener.IUnionPayAction;
import com.ximalaya.ting.android.host.live.push_report.XmLivePushReportHelper;
import com.ximalaya.ting.android.host.live.util.LiveDataReportUtil;
import com.ximalaya.ting.android.host.live.util.LiveItingUtil;
import com.ximalaya.ting.android.host.manager.HomeReserveDialogFireworkManager;
import com.ximalaya.ting.android.host.manager.PlanTerminateManagerForQuickListen;
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager;
import com.ximalaya.ting.android.host.manager.AccessibilityModeManager;
import com.ximalaya.ting.android.host.manager.ArriveTraceManager;
import com.ximalaya.ting.android.host.manager.BaseModeManager;
import com.ximalaya.ting.android.host.manager.BottomTabColorAndIconManager;
import com.ximalaya.ting.android.host.manager.BottomTabFragmentManager;
import com.ximalaya.ting.android.host.manager.BottomTabManager;
import com.ximalaya.ting.android.host.manager.BottomTabShowRule;
import com.ximalaya.ting.android.host.manager.BringUpAbManager;
import com.ximalaya.ting.android.host.manager.ChildProtectDialogManager;
import com.ximalaya.ting.android.host.manager.ChildXmlyTipManager;
import com.ximalaya.ting.android.host.manager.CleanSdkManager;
import com.ximalaya.ting.android.host.manager.DummyUserGuideManager;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.manager.EmergencyPlanManager;
import com.ximalaya.ting.android.host.manager.EventManager;
import com.ximalaya.ting.android.host.manager.FileNameUploadManager;
import com.ximalaya.ting.android.host.manager.FixVivoBackHomeUtil;
import com.ximalaya.ting.android.host.manager.HomeReachAbUtil;
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager;
import com.ximalaya.ting.android.host.manager.HomeRnTraceTimeManager;
import com.ximalaya.ting.android.host.manager.ISkinSettingChangeListener;
import com.ximalaya.ting.android.host.manager.InterestCardSwitchInfoManager;
import com.ximalaya.ting.android.host.manager.InterestTagManager;
import com.ximalaya.ting.android.host.manager.JumpAttributionManager;
import com.ximalaya.ting.android.host.manager.KidsOuterItingLaunchManager;
import com.ximalaya.ting.android.host.manager.ListRefreshCatchException;
import com.ximalaya.ting.android.host.manager.ListenAchievementManager;
import com.ximalaya.ting.android.host.manager.MineTabTagManager;
import com.ximalaya.ting.android.host.manager.NovelTabAbManager;
import com.ximalaya.ting.android.host.manager.OuterItingManager;
import com.ximalaya.ting.android.host.manager.OverseasUserVerifyManager;
import com.ximalaya.ting.android.host.manager.PlanTerminateManager;
import com.ximalaya.ting.android.host.manager.PlayCompleteRecommendManager;
import com.ximalaya.ting.android.host.manager.PrivacyProtocolChangeManager;
import com.ximalaya.ting.android.host.manager.ReadVolumeInterceptManager;
import com.ximalaya.ting.android.host.manager.RecallUserRightsData;
import com.ximalaya.ting.android.host.manager.RecallUserRightsManager;
import com.ximalaya.ting.android.host.manager.RecommendAlbumCardBeforePrivacyAgreeManager;
import com.ximalaya.ting.android.host.manager.RecommendPreLoadOptManager;
import com.ximalaya.ting.android.host.manager.ResourcePositionAdFreeManager;
import com.ximalaya.ting.android.host.manager.SharePosterManager;
import com.ximalaya.ting.android.host.manager.ShareResultManager;
import com.ximalaya.ting.android.host.manager.SkinManager;
import com.ximalaya.ting.android.host.manager.SoNameUploadManager;
import com.ximalaya.ting.android.host.manager.SpCheckManager;
import com.ximalaya.ting.android.host.manager.TeenModeManager;
import com.ximalaya.ting.android.host.manager.TempDataManager;
import com.ximalaya.ting.android.host.manager.TimeLimitManager;
import com.ximalaya.ting.android.host.manager.ToListenManager;
import com.ximalaya.ting.android.host.manager.VipBottomTabIconManager;
import com.ximalaya.ting.android.host.manager.account.FreePasswordManager;
import com.ximalaya.ting.android.host.manager.account.LoginUtil;
import com.ximalaya.ting.android.host.manager.account.NoReadManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.activity.MainActivityTabManager;
import com.ximalaya.ting.android.host.manager.activity.XmAppStartManager;
import com.ximalaya.ting.android.host.manager.activity.reddot.IRedDotAction;
import com.ximalaya.ting.android.host.manager.activity.reddot.MyRedDotPosition;
import com.ximalaya.ting.android.host.manager.activity.reddot.RedDotManage;
import com.ximalaya.ting.android.host.manager.activity.reddot.SubscribeRedDotPosition;
import com.ximalaya.ting.android.host.manager.activity.reddot.VipRedDotPosition;
import com.ximalaya.ting.android.host.manager.ad.AdApkPageInfoManager;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdPreloadHybridViewUtil;
import com.ximalaya.ting.android.host.manager.ad.NewVersionGuideVideoManager;
import com.ximalaya.ting.android.host.manager.ad.SoundAdConfigInitManager;
import com.ximalaya.ting.android.host.manager.ad.StartSplashAdHelper;
import com.ximalaya.ting.android.host.manager.ad.TouTiaoAdManager;
import com.ximalaya.ting.android.host.manager.ad.gamead.AdGameUtil;
import com.ximalaya.ting.android.host.manager.ad.thirdgamead.ThirdGameAdConstants;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockPaidManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockSnackBarManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.UnLockSoundPatchManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoConfigManager;
import com.ximalaya.ting.android.host.manager.adfree.CommonAdFreeManager;
import com.ximalaya.ting.android.host.manager.adfree.DirectionalAdFreeManager;
import com.ximalaya.ting.android.host.manager.ai.AiForceLoginManager;
import com.ximalaya.ting.android.host.manager.alarm.AlarmMissNotice;
import com.ximalaya.ting.android.host.manager.alarm.AlarmRecordManager;
import com.ximalaya.ting.android.host.manager.application.ApplicationManager;
import com.ximalaya.ting.android.host.manager.application.SpeedUpDex2oatInit;
import com.ximalaya.ting.android.host.manager.application.crash.XMNativeCrashHandler;
import com.ximalaya.ting.android.host.manager.bundleframework.BundleManager.BundleInfoManager;
import com.ximalaya.ting.android.host.manager.bundleframework.BundleManager.BundleRequestCache;
import com.ximalaya.ting.android.host.manager.bundleframework.BundleManager.PatchToken;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.listener.IActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.car.ICarFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.chat.IChatFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.kid.IKidFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.FeedActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LiveActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LoginActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.chat.ChatInternalServiceManager;
import com.ximalaya.ting.android.host.manager.chat.ChatUnreadManager;
import com.ximalaya.ting.android.host.manager.chat.PrivateChatToastSnackManager;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.childprotect.MinorsModeHelper;
import com.ximalaya.ting.android.host.manager.cmcc.CmccActivitySdkManager;
import com.ximalaya.ting.android.host.manager.comment.CommentDraftManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.device.WiFiDeviceController;
import com.ximalaya.ting.android.host.manager.dialog.monitor.InitAfterSettingUpdated;
import com.ximalaya.ting.android.host.manager.download.DownloadIncentiveAdManager;
import com.ximalaya.ting.android.host.manager.downloadapk.DownloadServiceManage;
import com.ximalaya.ting.android.host.manager.freeListen.ListenTaskMsgManager;
import com.ximalaya.ting.android.host.manager.freeListen.NewUserTaskMsgManager;
import com.ximalaya.ting.android.host.manager.freeflow.FreeFlowRecordTypeUtil;
import com.ximalaya.ting.android.host.manager.freeflow.FreeFlowService;
import com.ximalaya.ting.android.host.manager.freeunlock.PlayAutoUnlockTrackActionManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.iting.ItingManager;
import com.ximalaya.ting.android.host.manager.iting.ItingReturnBtnManager;
import com.ximalaya.ting.android.host.manager.kidmode.KidModeManager;
import com.ximalaya.ting.android.host.manager.kidmode.KidsHelper;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.notification.PushManager;
import com.ximalaya.ting.android.host.manager.notification.PushTask;
import com.ximalaya.ting.android.host.manager.pay.BoutiqueConfigManager;
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager;
import com.ximalaya.ting.android.host.manager.play.FreeListenSoundPatchManager;
import com.ximalaya.ting.android.host.manager.play.PlayAutoBuyTrackActionManager;
import com.ximalaya.ting.android.host.manager.play.PlayerManager;
import com.ximalaya.ting.android.host.manager.play.soundEffect.TrackPlaySoundEffectManager;
import com.ximalaya.ting.android.host.manager.recall.PreInstallNonePlayUserRecallManager;
import com.ximalaya.ting.android.host.manager.request.ChannelInfoRecordManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.request.DelayRequestManager;
import com.ximalaya.ting.android.host.manager.search.SearchManager;
import com.ximalaya.ting.android.host.manager.share.QQShareHelper;
import com.ximalaya.ting.android.host.manager.share.ShareConstants;
import com.ximalaya.ting.android.host.manager.share.VipGiftShareManager;
import com.ximalaya.ting.android.host.manager.share.biz.ClaCUserUtils;
import com.ximalaya.ting.android.host.manager.statistic.ListenTaskUtil;
import com.ximalaya.ting.android.host.manager.statistic.OneDayListenTimeForPlayProcessManager;
import com.ximalaya.ting.android.host.manager.statistic.ReadTimeReportManager;
import com.ximalaya.ting.android.host.manager.statistic.UserOneDateListenDuration;
import com.ximalaya.ting.android.host.manager.tabfragment.DynamicTabRedHintManager;
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager;
import com.ximalaya.ting.android.host.manager.trace.KeyManLogUploadManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.manager.track.LikeTrackStateChangeManager;
import com.ximalaya.ting.android.host.manager.update.UpdateManager;
import com.ximalaya.ting.android.host.manager.upload.BgPlayOptManagerForKeyMan;
import com.ximalaya.ting.android.host.manager.upload.UseLogUploadManager;
import com.ximalaya.ting.android.host.manager.vip.VipStatusChangedMsgManager;
import com.ximalaya.ting.android.host.memory.JavaMemoryManager;
import com.ximalaya.ting.android.host.model.InterestTagPageModel;
import com.ximalaya.ting.android.host.model.account.LoginInfoModel;
import com.ximalaya.ting.android.host.model.childprotect.ChildProtectInfo;
import com.ximalaya.ting.android.host.model.cloudhistory.CloudHistoryModel;
import com.ximalaya.ting.android.host.model.cloudhistory.CloudHistroyListenModel;
import com.ximalaya.ting.android.host.model.customize.InterestCardModel;
import com.ximalaya.ting.android.host.model.homepage.NoPlayTipModel;
import com.ximalaya.ting.android.host.model.imchat.UnreadModel;
import com.ximalaya.ting.android.host.model.localtts.LocalBookInfo;
import com.ximalaya.ting.android.host.model.newuser.NewUserActiveManager;
import com.ximalaya.ting.android.host.model.newuser.RecallUserManager;
import com.ximalaya.ting.android.host.model.push.PushModel;
import com.ximalaya.ting.android.host.model.setting.InterestCardSwitchInfo;
import com.ximalaya.ting.android.host.model.share.ShareCommand;
import com.ximalaya.ting.android.host.model.skin.SkinBottomIcon;
import com.ximalaya.ting.android.host.model.user.HomeUnRead;
import com.ximalaya.ting.android.host.model.user.NoReadModel;
import com.ximalaya.ting.android.host.model.user.UserBottomTag;
import com.ximalaya.ting.android.host.monitor.BlockFpsMonitor;
import com.ximalaya.ting.android.host.play.video.manager.XmVideoPlayStatisticsManager;
import com.ximalaya.ting.android.host.preinstall.PreInstallUtil;
import com.ximalaya.ting.android.host.push.NotificationPermissionOpenManager;
import com.ximalaya.ting.android.host.push.PushBadgeManager;
import com.ximalaya.ting.android.host.push.PushCommonUtil;
import com.ximalaya.ting.android.host.read.manager.TingReaderTrackAuthorityChangeManager;
import com.ximalaya.ting.android.host.safe.RiskUtils;
import com.ximalaya.ting.android.host.service.DriveModeBluetoothManager;
import com.ximalaya.ting.android.host.service.SmartDeviceBluetoothManager;
import com.ximalaya.ting.android.host.service.TingLocalMediaService;
import com.ximalaya.ting.android.host.service.xmremotecontrol.XmRemoteControlTraceUtil;
import com.ximalaya.ting.android.host.service.xmremotecontrol.XmRemoteControlUtil;
import com.ximalaya.ting.android.host.storage.AllBuzzStorageManager;
import com.ximalaya.ting.android.host.storage.StorageOptionManger;
import com.ximalaya.ting.android.host.util.AutoFontSizeUtil;
import com.ximalaya.ting.android.host.util.CheckLoginUtils;
import com.ximalaya.ting.android.host.util.CheckPlayManager;
import com.ximalaya.ting.android.host.util.ClipUtils;
import com.ximalaya.ting.android.host.util.CookieController;
import com.ximalaya.ting.android.host.util.DarkModeUtil;
import com.ximalaya.ting.android.host.util.ExitStatisticUtil;
import com.ximalaya.ting.android.host.util.FixHomeFeedShowBugUtil;
import com.ximalaya.ting.android.host.util.FixHomeShowBugUtil;
import com.ximalaya.ting.android.host.util.FullScreenUseNotchUtil;
import com.ximalaya.ting.android.host.util.GrowthItingUtil;
import com.ximalaya.ting.android.host.util.HomePageQuickListenGuideUtil;
import com.ximalaya.ting.android.host.util.HomeRnUtils;
import com.ximalaya.ting.android.host.util.InterestCardManager;
import com.ximalaya.ting.android.host.util.MultiAsyncTaskUtil;
import com.ximalaya.ting.android.host.util.MyListenRouterUtil;
import com.ximalaya.ting.android.host.util.NotificationUtil;
import com.ximalaya.ting.android.host.util.PageStayTrace;
import com.ximalaya.ting.android.host.util.PlayBarMustListenUtil;
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew;
import com.ximalaya.ting.android.host.util.RefreshHomeChannelUtils;
import com.ximalaya.ting.android.host.util.RiskManageUtil;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.ShortVideoItingUtil;
import com.ximalaya.ting.android.host.util.SyncCarTackProgressUtil;
import com.ximalaya.ting.android.host.util.UploadBlueBoothMessageUtil;
import com.ximalaya.ting.android.host.util.VersionUtil;
import com.ximalaya.ting.android.host.util.VipJumpPageUtils;
import com.ximalaya.ting.android.host.util.X5Util;
import com.ximalaya.ting.android.host.util.anchor.AnchorAbUtil;
import com.ximalaya.ting.android.host.util.common.CacheFileCleaner;
import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.FoldableScreenCompatUtil;
import com.ximalaya.ting.android.host.util.common.PackageManagerHook;
import com.ximalaya.ting.android.host.util.common.PackageUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.common.WifiOperatorManager;
import com.ximalaya.ting.android.host.util.constant.AppConfigConstants;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.util.feed.DyncAbTestUtil;
import com.ximalaya.ting.android.host.util.feed.FeedTraceUtils;
import com.ximalaya.ting.android.host.util.onekey.DailyNewsLogicManager;
import com.ximalaya.ting.android.host.util.onekey.DailyNewsUtil;
import com.ximalaya.ting.android.host.util.server.DownloadTools;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor;
import com.ximalaya.ting.android.host.util.startup.StartupOptManager;
import com.ximalaya.ting.android.host.util.view.EmotionUtil;
import com.ximalaya.ting.android.host.util.view.MainActivityViewUtil;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.host.view.CustomTipsView;
import com.ximalaya.ting.android.host.view.ToAddCoverImageView;
import com.ximalaya.ting.android.host.view.XmLottieDrawable;
import com.ximalaya.ting.android.host.view.ad.FreeKingToast;
import com.ximalaya.ting.android.host.view.dialog.ShareAndGainBookDialog;
import com.ximalaya.ting.android.host.view.other.HintFreeFlowDialog;
import com.ximalaya.ting.android.host.view.other.TabMenu;
import com.ximalaya.ting.android.host.view.popupwindow.BottomTabBubblePopupWindow;
import com.ximalaya.ting.android.host.widget.XmWidgetUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTrackingUtil;
import com.ximalaya.ting.android.hybridview.HybridAPI;
import com.ximalaya.ting.android.loginservice.model.BindLoginInfoModel;
import com.ximalaya.ting.android.main.manager.LogDumpManager;
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager;
import com.ximalaya.ting.android.main.util.MainTabClickHelper;
import com.ximalaya.ting.android.main.util.MyListenAbUtil;
import com.ximalaya.ting.android.main.util.MyListenGuideUtil;
import com.ximalaya.ting.android.manager.KeepAliveManager;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.opensdk.manager.StartServiceTimeCollectUtil;
import com.ximalaya.ting.android.opensdk.manager.StartServiceTimeoutFixUtil;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;
import com.ximalaya.ting.android.opensdk.model.live.schedule.Schedule;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.model.xdcs.XdcsEvent;
import com.ximalaya.ting.android.opensdk.model.xdcs.XdcsRecord;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.appnotification.NotificationChannelUtils;
import com.ximalaya.ting.android.opensdk.player.appnotification.NotificationStyleUtils;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater;
import com.ximalaya.ting.android.opensdk.player.manager.BgPlayOptManager;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl.PlayMode;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.soundpatch.volumebalance.SoundPatchAllResource;
import com.ximalaya.ting.android.opensdk.player.statistic.AppExitInfoManager;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayErrorStatisticManager;
import com.ximalaya.ting.android.opensdk.player.statistic.SystemSettingCollector;
import com.ximalaya.ting.android.opensdk.push.PushNotificationFilterManager;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.opensdk.util.SystemUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.android.routeservice.service.history.ICloudyHistory;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.wxcallback.wxsharelogin.XMWXEntryActivity;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmbootmonitor.BootMonitor;
import com.ximalaya.ting.android.xmdau.XmDauStat;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmpushservice.XmPushManager;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmtrace.utils.AppUtils;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.lang.ref.WeakReference;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Route(path = "/host/mainactivity")
public class MainActivity extends BaseFragmentActivity2 implements
        OnCheckedChangeListener, NoReadManage.INoReadUpdateListener,
        OnClickListener, IGotoTop, XmPlayerManager.IConnectListener,
        Router.IBundleInstallCallback, IChatMessageCallback, HomeRecommendPageLoadingOptimizationManager.IRecommendFragmentVisibleListener {

    public static final String TAG = "MAINACTIVITY";
    private long mStartTimeTest;
    public static final String ACTION_SHOW_SOUND_BOX_HINT = "action_show_sound_box_hint";

    private static final String BOTTOM_TAG = "bottom_tag";

    public static final String TEMP_DATA_MAIN_TIPS = "TEMP_DATA_MAIN_TIPS";
    private static final String LOTTIE_BOTTOM_TAB_HOME_PAGE_BTN_JSON = "/bottom_tab_home_page_btn.json";
    private static final String LOTTIE_BOTTOM_TAB_HOME_PAGE_BTN_REFRESH_JSON = "/bottom_tab_home_page_refresh_btn.json";
    private static final String LOTTIE_BOTTOM_TAB_FINDING_BTN_JSON = "/bottom_tab_finding_btn.json";
    private static final String LOTTIE_BOTTOM_TAB_FINDING_BTN_2_JSON = "/bottom_tab_finding_btn_2.json";
    private static final String LOTTIE_BOTTOM_TAB_FINDING_SUBSCRIBE_JSON = "/bottom_tab_finding_btn_subscribe.json";
    private static final String LOTTIE_BOTTOM_TAB_MINE_V9_BTN_JSON = "/bottom_tab_mine_v9_btn.json";
    private static final String LOTTIE_BOTTOM_TAB_VIP_BTN_JSON = "/bottom_tab_vip_btn.json";
    private static final String LOTTIE_BOTTOM_TAB_NOVEL_BTN_JSON = "/bottom_tab_novel_btn.json";
    private static final String LOTTIE_BOTTOM_TAB_CATEGORY_V2_BTN_JSON = "/bottom_tab_category_v2_btn.json";


    private static final String SHORT_CUT_HAS_CREATED = "host_short_cut_has_created";
    private static final String BOTTOM_ICON_LOTTIE_TAG = "lottie";

    public static long timeInRecommendFlowFirst = 0;//第一次在推荐流页面的时间 开始
    public static long timeInRecommendFlow = 0;//在推荐流页面的时间停留的时间
    public static boolean isLowMemoryDevice = true;
    public static boolean isPreItingShowing = false;
    private boolean mFromSkipPush;
    private boolean mFromItingDirectLanding; // 外部iting跳过首页（不跳过广告）
    private boolean mFromPushIgnoreHome; // push跳过首页（不跳过广告）
    private boolean mIsInterestCardShowing;
    private BlockFpsMonitor fpsMontor;

    private MyRedDotPosition mMyRedDotPosition;
    private VipRedDotPosition mVipRedDotPosition;
    private SubscribeRedDotPosition mSubscribeRedDotPosition;

    private boolean useAutoSizeHookResources = false;

    private final Map<Integer, String> mTabLottieFilePath = new HashMap<Integer, String>() {
        {
            put(TabFragmentManager.TAB_HOME_PAGE, LOTTIE_BOTTOM_TAB_HOME_PAGE_BTN_JSON);
            put(TabFragmentManager.TAB_FINDING, DyncAbTestUtil.isFeed2Subscribe() ?
                    LOTTIE_BOTTOM_TAB_FINDING_BTN_2_JSON : LOTTIE_BOTTOM_TAB_FINDING_BTN_JSON);
            put(TabFragmentManager.TAB_MY_AND_LISTEN, LOTTIE_BOTTOM_TAB_MINE_V9_BTN_JSON);
            put(TabFragmentManager.TAB_VIP, NovelTabAbManager.showCategoryV2Tab() ? LOTTIE_BOTTOM_TAB_CATEGORY_V2_BTN_JSON : (NovelTabAbManager.INSTANCE.showNovelTab() ?
                    LOTTIE_BOTTOM_TAB_NOVEL_BTN_JSON : LOTTIE_BOTTOM_TAB_VIP_BTN_JSON));
            put(TabFragmentManager.TAB_CATEGORY, LOTTIE_BOTTOM_TAB_CATEGORY_V2_BTN_JSON);
        }
    };

    public LoginInfoModel loginInfoModel;
    public UnreadModel mUnreadModel; // 消息中心未读消息model类
    public PlayerManager mPlayerManager;
    public static int mNeedLockScreen;
    /**
     * 用于标志是否退出应用
     */
    public boolean isExit = false;

    private boolean showMySpaceUnread = true;
    private boolean hasCallWindowFocused = false;

    private boolean isNeedToSyncHistory = false;
    private boolean mIsFirstSyncAndPullHistory = true;
    private UpdateManager updateManager;
    private ItingManager itingManager;
    @Nullable
    private TabFragmentManager tabFragmentManager;
    @Nullable
    private ManageFragment mManageFragment;
    private PlayBarFragment mPlayBarFragment;
    private ToAddCoverImageView mToAddCoverImageView;
    private SharedPreferencesUtil sp;
    private boolean mPlayButtonIsShow = true;
    private RadioGroup mRadioGroup;
    private RadioButton mRbHomePage;
    private RadioButton mRbFinding;
    private RadioButton mRbPlay;
    private RadioButton mRbCategory;
    private RadioButton mRbVip;
    private RadioButton mRbMineAndListen;
    private RadioButton mRbQuickListen;     //快听tab
    private boolean mIsLockScreen = false;
    private ILoginStatusChangeListener mLoginListener;
    private IChatFunctionAction.IUnreadMsgUpdateListener mUnreadMsgUpdateListener;

    private FrameLayout mPlayButtonImage;
    private FrameLayout mPlayButtonImageNew;
    /**
     * 设置图片拍照或者选择监听
     */
    private IPhotoAction mPhotoAction;
    private List<IBindAction> mBindActionList = new CopyOnWriteArrayList<>();
    // RN onActivityResult相关回调
    private IPaymentAction mPaymentAction;
    private IUnionPayAction mUnionPayActionListener;
    // menu
    private TabMenu mTabMenu;
    private List<String> mMenuTitle = Arrays.asList("播放历史",
            "定时关闭", "清理空间", "特色闹铃", "检查更新", "退出");
    private List<Integer> mMenuIcon = Arrays.asList(R.drawable.host_menu_history_selector, R.drawable.host_menu_timer_selector,
            R.drawable.host_menu_delete_selector, R.drawable.host_menu_wakeup_selector,
            R.drawable.host_menu_update_selector, R.drawable.host_menu_exit_selector);
    private WeakReference<BaseDialogFragment> mPlanTerminateFragmentWR;
    private boolean isAddLockScreen = false;
    private BaseFragment mLockScreenFragment;
    private IKeyDispatch iKeyDispatch;
    private Toast volumeToast;
    private Class mLastRemoveFragmentClass;
    private Toast mBackToast;
    // for hide bottom tab start
    private View mTabBg;
    private ImageView mIvTabBg;
    private FrameLayout mTabBgLay;
    private View mTabGroup;
    private View mHotView;
    // for hide bottom tab end
    private View vsDyncTip;

    private DialogBuilder mExitDialogBuilder;
    private View mBtnTop;
    private ShareAndGainBookDialog shareAndGainBookDialog;
    private long mLastBackPressedTime;
    private boolean mHasInitVipAttachButtonTabAbTast = false;
    private boolean mHasSchemeHandled = false;  // 是否有处理过的跳转事件
    private boolean mHasShowContinuePlayTips;
    private boolean isHandleIting = false;
    //发现tab数字角标，听友圈相关逻辑，进入app首次使用NoReadManage来更新，之后的都使用，发现bundle来更新
    private boolean mIsFirstTingGroupTabNum = true;

    private MainActivityViewUtil mMainActivityViewUtil;

    private RadioButton mLastCheckedBottomTab;
    private Drawable mBottomHomePageTabDrawable;
    private Drawable mBottomFiningTabDrawable;
    private Drawable mBottomHomePageRefreshDrawable;
    private boolean hasBottomAtmophere = false;
    private Map<RadioButton, Boolean> radioButtonLoadStatus = new HashMap<>();
    private Map<RadioButton, StateListDrawable> drawableMap = new HashMap<>();

    private Bundle mSavedBundle;
    private AudioManager mAudioManager;
    private StartSplashAdHelper mStartSplashAdHelper;
    private long mFeedId;
    private long mAnchorId;
    private boolean mHasTracedTabMineExposure = false;

    private boolean mLastClipContentCheckNoHandle;
    private boolean mLastClipContentCheckRequestClipboarded;

    private MainActivityTabManager mActivityTabManager;
    private AdPreloadHybridViewUtil mAdPreloadHybridViewUtil;

    // iting 跳转进来返回按钮
    private View mITingReturnView;

    public static boolean lastIsRestartActivity;
    private boolean nextNeedCheckShowOtherDialogs;
    private boolean mHasSetHomePageTabAsRefreshBtn;
    private boolean mHasIting = false;

    private boolean needCancelOtherDlgOnce = false; //当用户是新用户且ab到兴趣卡片优先启动的时候 需要取消一次其他dlg的弹出

    private boolean clickTabShowLogin = true;
    private boolean isUserLeaveHint = false;
    private boolean hasCheckInstall = false;
    private int mPendingRadioIndexFromITing = -1;

    private int mUserTabTagId = -1;
    private boolean mUserTabTagShowing = false;
    private List<UserBottomTag> mUserBottomTagList = new ArrayList<>();
    private long mLastUpdateAdFreeRequestTime = 0;
    private long mLastRequestRewardVideoConfigTime = 0;
    private long mLastUpdatePersonRequestTime = 0;
    private long mLastUpdatePersonUid = -1;
    private HomeReserveDialogFireworkManager homeReserveDialogFireworkManager = new HomeReserveDialogFireworkManager();

    private ManageFragment.StackChangeListener mStackChangeListener = new ManageFragment.StackChangeListener() {

        @Override
        public void onEntryAdd(Fragment fragment) {
            boolean setPlayBtnDrawable = true;
            if (fragment instanceof BaseFragment2) {
                setPlayBtnDrawable = ((BaseFragment2) fragment).getCanShowPlayButton();
            }
            if (setPlayBtnDrawable) {
                setPlayBtnDrawableAccFragment();
                if (!PlayBarAbManager.INSTANCE.useNewPlayBar() && getCurrentFragmentInManage() != null && mPlayBarFragment != null) {
                    mPlayBarFragment.showBackground(true);
                }
            }
            if (mPlayBarFragment != null && mManageFragment != null) {
                mPlayBarFragment.updatePlayBarBg(mManageFragment.getFragmentCount() == 0);
            }
            DynamicTabRedHintManager.INSTANCE.stopCountDown();
        }

        @Override
        public void onEntryRemove(Fragment fragment) {
            if (fragment != null) {
                mLastRemoveFragmentClass = fragment.getClass();
            } else {
                mLastRemoveFragmentClass = null;
            }
            if (mPlayBarFragment != null && mManageFragment != null) {
                mPlayBarFragment.updatePlayBarBg(mManageFragment.getFragmentCount() == 0);
            }
            setPlayBtnDrawableAccFragment();
            if (!PlayBarAbManager.INSTANCE.useNewPlayBar() && getCurrentFragmentInManage() == null && mPlayBarFragment != null) {
                mPlayBarFragment.showBackground(false);
            } else if (PlayBarAbManager.INSTANCE.useNewPlayBar() && getCurrentFragmentInManage() == null && fragment instanceof BaseFragment2) {
                BaseFragment2 baseFragment2 = (BaseFragment2) fragment;
                if (PlayBarAbManager.INSTANCE.useNewPlayBar() && !baseFragment2.isFilterPlayBarLocationSet()) {
                    if (fragment instanceof BaseFragment2 && !((BaseFragment2) fragment).isInManagerFragment()) {
                        // doNothing
                    } else {
                        changePlayButtonBottomMargin(true, 0);
                    }
                }
            }

            checkDynamicTabCanUpdate();
        }
    };

    private boolean mSendEndBroadCast;
    // 上报时间统计
    private long mStartTime;
    private boolean doAfterOnCreate;
    private boolean executeOnceAfterHomeShow;

    public void setKeyDispatch(IKeyDispatch iKeyDispatch) {
        this.iKeyDispatch = iKeyDispatch;
    }

    /**
     * MainActivity不保存状态
     */
    @SuppressLint("MissingSuperCall")
    @Override
    protected void onSaveInstanceState(Bundle outState) {
//        super.onSaveInstanceState(outState);
    }


    /**
     * 处理发现页tab头像红点
     */
    public void updateFindTabRedDot() {
        BottomTabShowRule bottomTabShowRule = BottomTabManager.INSTANCE.getTabShowRule(TabFragmentManager.TAB_FINDING);
        if (bottomTabShowRule != null && bottomTabShowRule.getShowImageDot()) {
            if (bottomTabShowRule.getImageDotFromSetting()) {
                setTabResource(mRbFinding, bottomTabShowRule.getImageUrl(), null, bottomTabShowRule.getBubbleMessage());
            } else {
                setTabResource(mRbFinding, bottomTabShowRule.getImageUrl(), bottomTabShowRule, bottomTabShowRule.getBubbleMessage());
            }
        }
    }

    private void setTabResource(RadioButton tab, String url, Object object, String message) {
        if (tab == null || TextUtils.isEmpty(url)) {
            return;
        }
        ImageManager.Options options = new ImageManager.Options();
        int dp30 = BaseUtil.dp2px(getContext(), 30);
        options.targetWidth = dp30;
        options.targetHeight = dp30;
        NoReadManage.getInstance(MainActivity.this).showRedDot(tab);
        ImageManager.from(this).downloadBitmap(url, options, (lastUrl, bitmap) -> {
            if (bitmap != null) {
                MMKVUtil.getInstance().saveLong(PreferenceConstantsInHost.KEY_TAB_HEAD_IMAGE_LAST_SHOW_DATE, System.currentTimeMillis());
                Drawable lastDrawable = tab.getCompoundDrawables()[1];
                Drawable roundDrawable = BitmapUtils.getRoundedDrawable(getContext(), bitmap, dp30);
                roundDrawable.setBounds(0, 0, dp30, dp30);
                tab.setCompoundDrawablesWithIntrinsicBounds(null,
                        roundDrawable, null, null);
                if (tab == mRbFinding && object instanceof BottomTabShowRule) {
                    BottomTabShowRule bottomTabShowRule = (BottomTabShowRule) object;
                    mFeedId = bottomTabShowRule.getFeedId();
                    mAnchorId = bottomTabShowRule.getUid();
//                    traceTabRedDotOrBubbleExposure(tab, false, false);
                }
                showTabBubblePopupWindow(tab, message);
                HandlerManager.obtainMainHandler().postDelayed(() -> {
                    if (canUpdateUi()) {
                        if (!BottomTabManager.INSTANCE.tabHasSkinIcon(tab.getId())) {
                            tab.setCompoundDrawablesWithIntrinsicBounds(null, lastDrawable, null, null);
                        }
                        if (tab == mRbFinding) {
                            mFeedId = 0;
                            mAnchorId = 0;
                        }
                        if (mBottomTabBubblePopupWindow != null) {
                            mBottomTabBubblePopupWindow.dismiss();
                            mBottomTabBubblePopupWindow = null;
                        }
                    }
                }, 5000);
            }
        });
    }

    private void showTabBubblePopupWindow(RadioButton tab, String message) {
        if (tab != null && !TextUtils.isEmpty(message)) {
            if (mBottomTabBubblePopupWindow == null) {
                mBottomTabBubblePopupWindow = new BottomTabBubblePopupWindow(this);
            }
            mBottomTabBubblePopupWindow.setContent(message);
            mBottomTabBubblePopupWindow.setMTabId(tab.getId());
            View popupView = mBottomTabBubblePopupWindow.getContentView();
            popupView.measure(0, 0);
            int popupWidth = popupView.getMeasuredWidth();
            int popupHeight = popupView.getMeasuredHeight();
            int[] location = new int[2];
            tab.getLocationOnScreen(location);
            int margin = BaseUtil.dp2px(getContext(), 16);
            int screenWidth = BaseUtil.getScreenWidth(getContext());
            int tabCenter = location[0] + tab.getWidth() / 2;
            int left = tabCenter - popupWidth / 2;
            int rightDelta = popupWidth / 2 - (screenWidth - tabCenter - margin);
            if (rightDelta > 0) {
                left -= rightDelta;
            }
            left = Math.max(margin, left);
            mBottomTabBubblePopupWindow.setOffset(tabCenter - left - BaseUtil.dp2px(getContext(), 4));
            try {
                mBottomTabBubblePopupWindow.showAtLocation(tab, Gravity.NO_GRAVITY, left, location[1] - popupHeight);
            } catch (Exception e) {
                e.printStackTrace();
                return;
            }
//            traceTabRedDotOrBubbleExposure(tab, true, false);
        }
    }

    public long getFeedId() {
        return mFeedId;
    }

    private void initLoginStatusListener() {
        if (mLoginListener == null) {
            mLoginListener = new ILoginStatusChangeListenerAndCallBackUseChange() {

                @Override
                public void onUserChange(LoginInfoModelNew oldModel,
                                         LoginInfoModelNew newModel) {
                    // onUserChange方法一般不要使用!!!!
                    XmRemoteControlUtil.onLoginStateChangeForMainProcess();
                }

                @Override
                public void onLogout(LoginInfoModelNew olderUser) {
                    final IDownloadService downloadService = RouteServiceUtil.getDownloadService();
                    downloadService.userLogout(olderUser.getUid(), true);
                    DailyNewsLogicManager.INSTANCE.saveRefreshStatusWhenLoginChanged(true);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            XmWidgetUtil.INSTANCE.refreshWidgetWhenLoginStatusChange();
                            try {
                                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().deleteDownloadTask();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    });
                    PlayCompleteRecommendManager.getInstance().reportOnLogout();
                    VipBottomTabIconManager.requestVipBottomTabIcon(true);

                    XmRemoteControlUtil.onLoginStateChangeForMainProcess();
                }

                @Override
                public void onLogin(LoginInfoModelNew model) {
                    final IDownloadService downloadService = RouteServiceUtil.getDownloadService();
                    downloadService.userChange(model.getUid(), true);
                    DailyNewsLogicManager.INSTANCE.saveRefreshStatusWhenLoginChanged(true);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            XmWidgetUtil.INSTANCE.refreshWidgetWhenLoginStatusChange();
                        }
                    });
                    PlayCompleteRecommendManager.getInstance().reportOnLogin();

                    updateLocalPortrait();

                    // 上报邀请者信息建立好友关系
                    String uid = SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_SHARE_WEIXIN_UID);
                    handleUid(uid);

                    fillPlayListByAccount();

                    ElderlyModeManager.getInstance().batchReportCourseData();

                    //切换账号后需要重新注册
                    PlayAutoUnlockTrackActionManager.getInstance().init(getApplicationContext());
                    VipBottomTabIconManager.requestVipBottomTabIcon(true);

                    //登录成功后获取1次移动活动
                    CmccActivitySdkManager.requestCmccActivityDelay(1000);

                    XmRemoteControlUtil.onLoginStateChangeForMainProcess();
                }
            };
            UserInfoMannage.getInstance().addLoginStatusChangeListener(mLoginListener);
        }
    }

    private ABTest.ICallback mAbTestCallBack = new ABTest.ICallback() {
        @Override
        public void onSuccess() {
            Logger.i("feiwen", "mAbTestCallBack  onSuccess");
            onConfigSuccessOrFail();
        }

        @Override
        public void onFailure(int code, Object errorObj) {
            Logger.i("feiwen", "mAbTestCallBack  onFailure");
            onConfigSuccessOrFail();
        }

        @Override
        public void onError(Exception exception) {
            Logger.i("feiwen", "mAbTestCallBack  onError");
            onConfigSuccessOrFail();
        }
    };

    private boolean mHasAbTestObtained;

    private void onConfigSuccessOrFail() {
        mHasAbTestObtained = true;
        SearchManager.getSearchAppConfig();
        saveAdAbTestConfig();
        // 消息数监听必须在AB和订阅红点接口后面
        DyncAbTestUtil.INSTANCE.refreshMessageTabAb();
        ChatInternalServiceManager.INSTANCE.registerService(IChatMessageCallback.class, this);
        ChatUnreadManager.INSTANCE.registerChatSession();
        String config = ABTest.getStringWithoutLog(CConstants.NewAbTest.RECALL_SOUND_1HOUR_NOAD20230811, "yes");
        MmkvCommonUtil.getInstance(getContext()).saveString(PreferenceConstantsInHost.KEY_RECALL_SOUND_1HOUR_NOAD20230811, config);
//        UnknownHostFixManager.INSTANCE.saveAbtestValue();
        MmkvCommonUtil.getInstance(getContext()).saveBoolean(CConstants.Group_android.ITEM_IS_UPLOAD_LOG_FAST,
                                ABTest.getBoolean(CConstants.Group_android.ITEM_IS_UPLOAD_LOG_FAST, false));
        Logger.i(UseLogUploadManager.TAG, "abtest___" + ABTest.getBoolean(CConstants.Group_android.ITEM_IS_UPLOAD_LOG_FAST, false));
        Logger.i(TAG, " register chat msg");
        //缓存华为意图abtest开关
//        HwIntentDonateManager.execGetAndCacheAbtestEnable();
        boolean isNeedPreloadLocal = ABTest.getBoolean(CConstants.ABTest.AB_IS_PRE_LOAD_LOCAL_DATA, false);
        MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveBoolean(CConstants.ABTest.AB_IS_PRE_LOAD_LOCAL_DATA, isNeedPreloadLocal);
        MmkvCommonUtil.getInstance(getContext()).saveBoolean(CConstants.ABTest.AB_IS_NEED_FORCE_PRELOAD_RECOMMEND,
                                                ABTest.getBoolean(CConstants.ABTest.AB_IS_NEED_FORCE_PRELOAD_RECOMMEND, false));
        MmkvCommonUtil.getInstance(getContext()).saveBoolean(CConstants.ABTest.AB_ITEM_CLEAN_SDK_REPORT_OPEN,
                ABTest.getBoolean(CConstants.ABTest.AB_ITEM_CLEAN_SDK_REPORT_OPEN,false));
        PlayBarMustListenUtil.Instance.init();

        boolean rtcSwitchOpen = ABTest.getBoolean("rtc_sdk_ab_switch_new", false);
        MMKVUtil.getInstance().saveBoolean(PubSubServiceManagerKt.KEY_ITEM_RTC_SDK_SWITCH_AB_OPEN, rtcSwitchOpen);
        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                PubSubServiceManager.Companion.getInstance().init();
            }
        });
    }

    private void saveAdAbTestConfig() {
        MMKVUtil.getInstance().saveBoolean(CConstants.Group_ad.ITEM_SOUND_PATCH_CLOSE_OPTIMIZE,
                ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_SOUND_PATCH_CLOSE_OPTIMIZE, 0) == 1);
    }

    // 用户登出后再登录 如果本地播放器没有内容 则读取播放历史构造播放列表
    private void fillPlayListByAccount() {
        PlayableModel sound = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getCurrSound();
        if (sound != null) {
            return;
        }
        Map params = new HashMap<>();
        params.put("pageSize", "2");
        CommonRequestM.getInstanse().getCloudHistory(params, new IDataCallBack<CloudHistoryModel>() {
            @Override
            public void onSuccess(@Nullable CloudHistoryModel chm) {
                if (chm != null) {
                    List<CloudHistroyListenModel> list = chm.getListenModels();
                    if (list != null && !list.isEmpty()) {
                        CloudHistroyListenModel hm = list.get(0);
                        if (hm != null) {
                            PlayTools.playTrackHistoy(MainActivity.this, false, hm.getCloudTrack(), null);
                        }
                    }
                }
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    private void updateLocalPortrait() {
        CommonRequestM.getUserPortrait(UserInfoMannage.getUid(), new IDataCallBack<InterestCardModel>() {
            @Override
            public void onSuccess(@Nullable InterestCardModel object) {
                if (object != null && object.hasTrait) {
                    String content = new Gson().toJson(object);
                    MMKVUtil.getInstance().saveString(KEY_CUSTOMIZED_INTEREST_CARD_MODEL_NEW, content);
                }
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    private void setPlayBtnDrawableAccFragment() {
        if (mPlayBarFragment != null) {
            mPlayBarFragment.setIconLevelStatus();
        }
//        if (mPlayIconImg == null) {
//            mPlayIconImg = (ImageView) MainActivity.this.findViewById(R.id.main_play_icon_img);
//        }
//        if (mPlayIconImg != null) {
//            if (XmPlayerManager.getInstance(getApplicationContext()).getCurrSound() == null
//                    && XmPlayerManager.getInstance(getApplicationContext()).getMixPlayTrack() == null) {
//                mPlayIconImg.getDrawable().setLevel(0);
//            } else {
//                mPlayIconImg.getDrawable().setLevel(1);
//            }
//        }
    }

    private boolean mIsRestoreFromBackground = false;
    private boolean initDelayNet = false;
    private boolean isCallDoOnFirstWindowFocusChanged = false;

    @Override
    @StartUpMonitor
    public void onWindowFocusChanged(boolean hasFocus) {
        Logger.i(TAG, "app_start_time  , count time special  ： ttype = MainActivity cost time : " + (System.currentTimeMillis() - mStartTimeTest));
        if (hasFocus) {
            hasCallWindowFocused = true;
        }
        if (hasFocus && !hasWindowFocusBefore() && isDoOnCreate) {
            ActivityWindowFirstFocusRun run = new ActivityWindowFirstFocusRun(MainActivity.this);
            HandlerManager.postOnUIThread(run);
        }
        super.onWindowFocusChanged(hasFocus);
        StartUpRecord.setMainWindowFocusChanged(this, hasFocus);

        Logger.i(TAG, "MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onWindowFocusChanged end");
    }

    @StartUpMonitor
    @Override
    public void doOnFirstWindowFocusChanged() {
        if (ViewUtil.isSplashAdShowing() || ViewUtil.isSplashAdBgShowing()) {
            return;
        }

        if (isCallDoOnFirstWindowFocusChanged) {
            return;
        }
        if (RecommendPreLoadOptManager.INSTANCE.isDebug()) {
            RecommendPreLoadOptManager.INSTANCE.log(Log.getStackTraceString(new Throwable()));
        }
        isCallDoOnFirstWindowFocusChanged = true;

        Logger.log("MainActivity acitivityLife : doOnFirstWindowFocusChanged");
        PerformanceMonitor.traceBegin("doOnFirstWindowFocusChanged");

        boolean keepAliveOpen = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME,
                CConstants.Group_android.ITEM_KEEP_ALIVE_ON_BACKGROUND, false);
        Logger.d("zimotag", "doOnFirstWindowFocusChanged keepAliveOpen=" + keepAliveOpen);
        if (keepAliveOpen) {
            KeepAliveManager.getInstance().startKeepAlive();
        }

        JavaMemoryManager.startMemoryMonitor(this.getApplication());
//        WifiSleepMonitor.getInstance().init(this);
        Router.setFirstPageDrawn(true);
        initPushChannel();
        initXiaoMiPush();
        doBundleUpdate();
        initAfterViewDraw();
        showContinuePlayTips();
        checkShowKillCardToast();
        checkFreeFlowStatus();
        LottieCompositionFactory.setMaxCacheSize(6);//解决lottie动画缓存过大问题
        if (!initDelayNet) {
            initDelayNet = true;
            startDelayNet();
        }

        if (!mSendEndBroadCast) {
            mSendEndBroadCast = true;
            BootMonitor.getInstance().sendEndBroadCast(MainActivity.this);
        }

        SpeedUpDex2oatInit.triggerSpeedUp();
        CookieController.updateConfigCheck(MainActivity.this);
        ToolUtil.configCenterToggleCheck(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.ITEM_HYBRID_LOAD_TRACE_OPEN, ApmHybridLoadUploader.sOpen, false, (result) -> {
            ApmHybridLoadUploader.sOpen = result;
        });
        ToolUtil.configCenterToggleCheck(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.ITEM_NOT_SUPPORT_WEBGL_OPEN_X5, X5Util.sWelGlNotSupportOpenX5, false, (result) -> {
            X5Util.sWelGlNotSupportOpenX5 = result;
        });
        ToolUtil.configCenterToggleCheck(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.ITEM_HYBRID_RELOCATION, HybridFragment.sInterceptRelocation, false, (result) -> {
            HybridFragment.sInterceptRelocation = result;
        });
        ToolUtil.configCenterToggleCheck(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.ITEM_XDCS_UPLOAD_START_UP, needUploadAppStart, false, (result) -> {
            needUploadAppStart = result;
        });
        XMNativeCrashHandler.getInstance().afterApmInitToUpload(getContext());

        // 开启app后根据配置中心开关，主动扫描已连接的蓝牙
        DriveModeBluetoothManager.getInstance().scanBlueToothInHomePage();

        ChildProtectManager.initTimeLimit(BaseApplication.getMyApplicationContext());
//        HwRecommendManager.getSingleInstance().checkIfNeedDonate();

        HandlerManager.postOnBackgroundThread(new Runnable() {
            @Override
            public void run() {
                // 播放DAU 初始化检查是否上报
                XmVideoPlayStatisticsManager.Companion.checkNeedUpload();
            }
        });

        CommonRequestM.soundPatchAllResource(new IDataCallBack<SoundPatchAllResource>() {
            @Override
            public void onSuccess(@Nullable @org.jetbrains.annotations.Nullable SoundPatchAllResource data) {

            }

            @Override
            public void onError(int code, String message) {

            }
        });

        Logger.d(TAG, "doOnFirstWindowFocusChanged end");
        AlarmRecordManager alarmRecordManager = AlarmRecordManager.getInstance(BaseApplication.getMyApplicationContext());
        alarmRecordManager.traceOnAlarmFirstLaunchOrEditAlarm(true);
        alarmRecordManager.caculateAndSetRecentlyAlarm();

        AlarmMissNotice.checkAndShow(getApplicationContext());

        ShortVideoItingUtil.init();

        //注册监听
        TingReaderTrackAuthorityChangeManager.getInstance().registerReceiver();

        FreePasswordManager.checkAuthCode(null);

        DeviceUtil.is64CpuAbi(is64CpuAbi -> {
            PackageInfo packageInfo = PackageUtil.getPackageInfoFromPackageName(BaseApplication.getMyApplicationContext(), "com.google.android.webview");
            String webVersion = "";
            if (packageInfo != null) {
                webVersion = packageInfo.versionName;
            }
            // 冷启动设备应用位数判断  其他事件
            new XMTraceApi.Trace()
                    .setMetaId(64444)
                    .setServiceId("others")
                    .put("isApp64CpuAbi", DeviceUtil.isApp64BitAbi(getApplicationContext()) + "")
                    .put("isDevice64CpuAbi", is64CpuAbi + "")
                    .put("version", webVersion)
                    .createTrace();
        });

        PageStayTrace.init();

        PerformanceMonitor.traceEnd("doOnFirstWindowFocusChanged", 12);
    }

    private void startDelayNet() {
        HybridAPI.refresh(null);
        //DeviceUtil.collectLocalInfo();
        DelayRequestManager.fireworkNativeDialog();
        DelayRequestManager.uploadTraceData();
//        ViewClickSchemeHandler.init();
        WifiOperatorManager.init(getApplicationContext());
    }

    private boolean mHasCheckClipContent = false; // 黏贴板读取标志

    private void clipContentCheck(boolean requestedClipboarded) {
        if (mHasCheckClipContent) {
            // 该周期内已经读取不在读取，只有当MainActivity.onStop调用后才能复位
            return;
        }
        mHasCheckClipContent = true;
        ClipUtils.getClipItemAt0Async(getApplicationContext(), new ClipUtils.IClipContentGotCallback() {
            @Override
            public void onClipContentGot(String clipContent) {
                if (TextUtils.isEmpty(clipContent)) {
                    // 直播站外投流新安装用户且剪切板内容无效时，执行上报新用户激活APP事件
                    LiveDataReportUtil.checkIsNewInstallAndReportActivateEvent();
                    return;
                }

                if (ShareAndGainBookDialog.checkSharePassWord(clipContent)) {
                    if (shareAndGainBookDialog == null || !shareAndGainBookDialog.isAddFix()) {
                        shareAndGainBookDialog = ShareAndGainBookDialog.newInstance(clipContent);
                        shareAndGainBookDialog.show(getSupportFragmentManager(), ShareAndGainBookDialog.TAG);
                    } else {
                        shareAndGainBookDialog.setSharePassWordModel(shareAndGainBookDialog.getSharePassWord(clipContent));
                        shareAndGainBookDialog.setContentChange(ShareAndGainBookDialog.TYPE_COMMON);
                    }
                    LiveDataReportUtil.setCanReport(false);
                } else if (clipContent.equals(AppConfigConstants.APP_RESUME_PASSWORD_COLLECTION)) {
                    if (shareAndGainBookDialog == null || !shareAndGainBookDialog.isAddFix()) {
                        shareAndGainBookDialog = ShareAndGainBookDialog.newInstance(ShareAndGainBookDialog.TYPE_COLLECTION);
                        shareAndGainBookDialog.show(getSupportFragmentManager(), ShareAndGainBookDialog.TAG);
                    } else {
                        shareAndGainBookDialog.setContentChange(ShareAndGainBookDialog.TYPE_COLLECTION);
                    }
                    LiveDataReportUtil.setCanReport(false);
                } else if (clipContent.equals(AppConfigConstants.APP_RESUME_PASSWORD_MEMBER)) {
                    if (shareAndGainBookDialog == null || !shareAndGainBookDialog.isAddFix()) {
                        shareAndGainBookDialog = ShareAndGainBookDialog.newInstance(ShareAndGainBookDialog.TYPE_MEMBER);
                        shareAndGainBookDialog.show(getSupportFragmentManager(), ShareAndGainBookDialog.TAG);
                    } else {
                        shareAndGainBookDialog.setContentChange(ShareAndGainBookDialog.TYPE_MEMBER);
                    }
                    LiveDataReportUtil.setCanReport(false);
                } else if (clipContent.equals(AppConfigConstants.APP_RESUME_PASSWORD_BOOK)) {
                    if (shareAndGainBookDialog == null || !shareAndGainBookDialog.isAddFix()) {
                        shareAndGainBookDialog = ShareAndGainBookDialog.newInstance(ShareAndGainBookDialog.TYPE_BOOK);
                        shareAndGainBookDialog.show(getSupportFragmentManager(), ShareAndGainBookDialog.TAG);
                    } else {
                        shareAndGainBookDialog.setContentChange(ShareAndGainBookDialog.TYPE_BOOK);
                    }
                    LiveDataReportUtil.setCanReport(false);
                } else if (clipContent.equals(AppConfigConstants.APP_RESUME_PASSWORD_NEW_MEMBER)) {
                    if (shareAndGainBookDialog == null || !shareAndGainBookDialog.isAddFix()) {
                        shareAndGainBookDialog = ShareAndGainBookDialog.newInstance(ShareAndGainBookDialog.TYPE_NEW_MEMBER);
                        shareAndGainBookDialog.show(getSupportFragmentManager(), ShareAndGainBookDialog.TAG);
                    } else {
                        shareAndGainBookDialog.setContentChange(ShareAndGainBookDialog.TYPE_NEW_MEMBER);
                    }
                    LiveDataReportUtil.setCanReport(false);
                } else if (clipContent.equals(AppConfigConstants.APP_RESUME_PASSWORD_NEW_COLLFREE)) {
                    if (shareAndGainBookDialog == null || !shareAndGainBookDialog.isAddFix()) {
                        shareAndGainBookDialog = ShareAndGainBookDialog.newInstance(ShareAndGainBookDialog.TYPE_NEW_COLLECION);
                        shareAndGainBookDialog.show(getSupportFragmentManager(), ShareAndGainBookDialog.TAG);
                    } else {
                        shareAndGainBookDialog.setContentChange(ShareAndGainBookDialog.TYPE_NEW_COLLECION);
                    }
                    LiveDataReportUtil.setCanReport(false);
                } else {
                    String realContent = ClipUtils.getRealXmClipContentByClip(clipContent);
                    if (!requestedClipboarded) {
                        ChannelInfoRecordManager.getInstance().clipContentUpdate(MainActivity.this, realContent);
                    }
                    checkIting(realContent);
                }
            }
        });
    }

    /**
     * vip的abtest和通知栏的abtest
     */
    private void initVipAttachButtonTabAndNotificationAbTest() {
        XmPlayerManager.getInstance(MainActivity.this).addOnConnectedListerner(this);
        if (mHasInitVipAttachButtonTabAbTast) {
            return;
        }
        mHasInitVipAttachButtonTabAbTast = true;

        ConfigureCenter.getInstance().registerConfigFetchCallback(mConfigFetchCallback);
    }


    IConfigureCenter.ConfigFetchCallback mConfigFetchCallback = new IConfigureCenter.ConfigFetchCallback() {
        @Override
        public void onUpdateSuccess() {
            // 更新一下sp中保存的配置的值
            BoutiqueConfigManager.saveBoutiqueSelectConfig(getApplicationContext());
            RecommendShowTagsUtilNew.INSTANCE.cacheConfig();
            AutoFontSizeUtil.cacheConfig();
            FixHomeShowBugUtil.cacheConfig();
            FixHomeFeedShowBugUtil.cacheConfig();
            CheckPlayManager.cacheConfig();
        }

        @Override
        public void onRequestError() {

        }
    };

    private void updateBottomTabBgColor() {
        if (mTabBg == null || mTabBg.getBackground() == null || mIvTabBg == null) {
            return;
        }
        mIvTabBg.setVisibility(View.GONE);
        mTabBg.setVisibility(View.VISIBLE);
        BottomTabColorAndIconManager colorAndIconManager = BottomTabColorAndIconManager.INSTANCE;
        SkinManager skinManager = SkinManager.INSTANCE;
        if (colorAndIconManager.hasValidBottomColor() || colorAndIconManager.canSetAtmosphereBottomTab()) {
            mTabBg.getBackground().mutate().setColorFilter(new PorterDuffColorFilter(colorAndIconManager.getBottomColor(), PorterDuff.Mode.SRC_IN));
            setTabsTextColorFromSkin();
        } else if (skinManager.hasValidBottomBg()) {
            ImageManager.from(getContext()).downloadBitmap(skinManager.getBottomBgUrl(), (lastUrl, bitmap) -> {
                if (TextUtils.equals(skinManager.getBottomBgUrl(), lastUrl) && bitmap != null) {
                    mIvTabBg.setImageBitmap(bitmap);
                    setTabsTextColorFromSkin();
                    mIvTabBg.setVisibility(View.VISIBLE);
                    mTabBg.setVisibility(View.INVISIBLE);
                }
            });
        } else {
            mTabBg.getBackground().setColorFilter(null);
            try {
                setTabsTextColor(R.color.host_theme_bottom_tab_text_selector);
            } catch (Exception e) {
                Logger.e(e);
            }
        }
    }

    private void setTabsTextColorFromSkin() {
        setTabTextColorFromSkin(mRbHomePage, TabFragmentManager.TAB_HOME_PAGE);
        setTabTextColorFromSkin(mRbFinding, TabFragmentManager.TAB_FINDING);
        setTabTextColorFromSkin(mRbVip, TabFragmentManager.TAB_VIP);
        setTabTextColorFromSkin(mRbCategory, TabFragmentManager.TAB_CATEGORY);
        setTabTextColorFromSkin(mRbMineAndListen, TabFragmentManager.TAB_MY_AND_LISTEN);
    }

    private void setTabTextColorFromSkin(RadioButton radioButton, int tabId) {
        BottomTabColorAndIconManager colorAndIconManager = BottomTabColorAndIconManager.INSTANCE;
        if (radioButton == null) {
            return;
        }
        if (colorAndIconManager == null) {
            setTabTextColor(radioButton, R.color.host_theme_bottom_tab_text_selector_with_skin_bg);
            return;
        }
        SkinBottomIcon skinBottomIcon = colorAndIconManager.getSkinBottomIcon(tabId);
        SkinBottomIcon bottomAtmosphereIconInfo = colorAndIconManager.getBottomAtmosphereIcon(tabId);
        if (bottomAtmosphereIconInfo != null && bottomAtmosphereIconInfo.hasValidTextColorAndDark() && BottomTabColorAndIconManager.INSTANCE.canSetAtmosphereBottomTab()) {
            int[][] states = new int[][]{
                    new int[]{android.R.attr.state_pressed},
                    new int[]{android.R.attr.state_selected},
                    new int[]{android.R.attr.state_checked},
                    new int[]{-android.R.attr.state_pressed},
            };
            int selColor = Color.parseColor(bottomAtmosphereIconInfo.getCharactersCheckedColor());
            int unselColor = Color.parseColor(bottomAtmosphereIconInfo.getCharactersUnCheckedColor());
            if (BaseFragmentActivity.sIsDarkMode) {
                selColor = Color.parseColor(bottomAtmosphereIconInfo.getDarkCharactersCheckedColor());
                unselColor = Color.parseColor(bottomAtmosphereIconInfo.getDarkCharactersUnCheckedColor());
            }
            int[] colors = new int[]{selColor, selColor, selColor, unselColor, unselColor, unselColor};
            ColorStateList colorStateList = new ColorStateList(states, colors);
            radioButton.setTextColor(colorStateList);
        } else if (skinBottomIcon != null && skinBottomIcon.hasValidTextColor()) {
            int[][] states = new int[][]{
                    new int[]{android.R.attr.state_pressed},
                    new int[]{android.R.attr.state_selected},
                    new int[]{android.R.attr.state_checked},
                    new int[]{-android.R.attr.state_pressed},
                    new int[]{-android.R.attr.state_selected},
                    new int[]{-android.R.attr.state_checked},
            };
            int selColor = Color.parseColor(skinBottomIcon.getCharactersCheckedColor());
            int unselColor = Color.parseColor(skinBottomIcon.getCharactersUnCheckedColor());
            int[] colors = new int[]{selColor, selColor, selColor, unselColor, unselColor, unselColor};
            ColorStateList colorStateList = new ColorStateList(states, colors);
            radioButton.setTextColor(colorStateList);
        } else {
            setTabTextColor(radioButton, R.color.host_theme_bottom_tab_text_selector_with_skin_bg);
        }
    }

    private void setTabsTextColor(int colorStateListResId) {
        setTabTextColor(mRbHomePage, colorStateListResId);
        setTabTextColor(mRbFinding, colorStateListResId);
        setTabTextColor(mRbCategory,colorStateListResId);
        if (NovelTabAbManager.INSTANCE.showNovelTab()) {
            setTabTextColor(mRbVip, R.color.host_theme_bottom_tab_novel_text_selector);
        } else {
            setTabTextColor(mRbVip, colorStateListResId);
        }
        setTabTextColor(mRbMineAndListen, colorStateListResId);
        if (QuickListenTabAbManager.showAIListenTab()) {
            setTabTextColor(mRbCategory, R.color.host_color_131313_dcdcdc);
        }
    }

    private void setTabTextColor(RadioButton radioButton, int colorStateListResId) {
        if (radioButton != null && getResources() != null) {
            try {
                radioButton.setTextColor(getResources().getColorStateList(colorStateListResId));
            } catch (Exception e) {
                Logger.e(e);
            }
        }
    }

    private void updateBottomTabResource() {
        if (mRadioGroup == null) {
            return;
        }

        drawableMap.clear();
        View curChecked = mRadioGroup.findViewById(mRadioGroup.getCheckedRadioButtonId());
        loadBottomTabResource(mRbHomePage, TabFragmentManager.TAB_HOME_PAGE, curChecked == mRbHomePage || curChecked == null);
        loadBottomTabResource(mRbFinding, TabFragmentManager.TAB_FINDING, curChecked == mRbFinding);
        loadBottomTabResource(mRbCategory, TabFragmentManager.TAB_CATEGORY, curChecked == mRbCategory);
        loadBottomTabResource(mRbVip, TabFragmentManager.TAB_VIP, curChecked == mRbVip);
        loadBottomTabResource(mRbMineAndListen, TabFragmentManager.TAB_MY_AND_LISTEN, curChecked == mRbMineAndListen);
    }

    private boolean hasSkinIcon(int tabId) {
        SkinBottomIcon icon = BottomTabColorAndIconManager.INSTANCE.getSkinBottomIcon(tabId);
        return icon != null && icon.isValid();
    }

    private boolean isBottomTabNeedLoad(RadioButton tab, Object object) {
        if (object == null || tab == null) {
            return false;
        }
        if (tab == mRbCategory && QuickListenTabAbManager.showAIListenTab()) {
            return false;
        }

        if ((QuickListenTabAbManager.showAIListenTab() && mRbQuickListen != null && mRbQuickListen == tab)) {
            return false;
        }

        if (!isBottomTabCurResourceOf(tab, object)) {
            return true;
        }
        Object success = tab.getTag(R.id.host_load_success);
        if (success instanceof Boolean && (Boolean) success) {
            return false;
        }
        return true;
    }

    private boolean isBottomTabCurResourceOf(RadioButton tab, Object object) {
        if (object == null || tab == null) {
            return false;
        }
        return object.equals(tab.getTag(R.id.host_cur_resource_tag));
    }

    private void loadBottomTabResource(RadioButton radioButton, int tabId, boolean isChecked) {
        if (radioButton == null) {
            return;
        }
        BottomTabColorAndIconManager colorAndIconManager = BottomTabColorAndIconManager.INSTANCE;
        SkinBottomIcon iconInfo = colorAndIconManager.getSkinBottomIcon(tabId);
        SkinBottomIcon bottomAtmosphereIconInfo = colorAndIconManager.getBottomAtmosphereIcon(tabId);
        Logger.log("f_tag colorAndIconManager.canSetAtmosphereBottomTab() " +colorAndIconManager.canSetAtmosphereBottomTab());
        if (bottomAtmosphereIconInfo != null && bottomAtmosphereIconInfo.isValid() && colorAndIconManager.canSetAtmosphereBottomTab()) {

            if (Boolean.TRUE.equals(radioButtonLoadStatus.get(radioButton))) {
                return;
            }
            radioButtonLoadStatus.put(radioButton, false); // 标记为正在加载
            radioButton.setTag(R.id.host_cur_resource_tag, bottomAtmosphereIconInfo);
            radioButton.setTag(R.id.host_load_success, false);
            new BottomTabAtmosphereIconLoadTask(bottomAtmosphereIconInfo, radioButton).start();
            if (mBottomTabBubblePopupWindow != null && mBottomTabBubblePopupWindow.getMTabId() == tabId) {
                mBottomTabBubblePopupWindow.dismiss();
                mBottomTabBubblePopupWindow = null;
            }
        } else if (iconInfo != null && iconInfo.isValid()) {
            if (tabId == TabFragmentManager.TAB_VIP && mVipRedDotPosition != null && NovelTabAbManager.INSTANCE.showVipTab()) {
                mVipRedDotPosition.setRedDotShow(true);
                mVipRedDotPosition.notifiRedDotShow();
            }
            if (isBottomTabNeedLoad(radioButton, iconInfo)) {
                // 改变了，需要重新加载
                radioButton.setTag(R.id.host_cur_resource_tag, iconInfo);
                radioButton.setTag(R.id.host_load_success, false);
                new SkinBottomTabIconLoadTask(iconInfo, radioButton).start();
                if (mBottomTabBubblePopupWindow != null && mBottomTabBubblePopupWindow.getMTabId() == tabId) {
                    mBottomTabBubblePopupWindow.dismiss();
                    mBottomTabBubblePopupWindow = null;
                }
            }
        } else if (isBottomTabNeedLoad(radioButton, getBottomIconLottieTag())) {
            if (tabId == TabFragmentManager.TAB_VIP && mVipRedDotPosition != null) {
                mVipRedDotPosition.setRedDotShow(false);
            }
            // 当前显示的不是lottie，需要加载下lottie资源
            String lottieFile = mTabLottieFilePath.get(tabId);
            if (!TextUtils.isEmpty(lottieFile)) {
                String lottieDirectory;
                if (BaseFragmentActivity.sIsDarkMode || colorAndIconManager.getMIsDarkMode()) {
                    lottieDirectory = "lottie-night";
                } else {
                    lottieDirectory = "lottie";
                }
                radioButton.setTag(R.id.host_cur_resource_tag, getBottomIconLottieTag());
                radioButton.setTag(R.id.host_load_success, false);
                loadBottomTabLottieResource(radioButton, lottieDirectory + lottieFile, isChecked);
            }
        }
    }

    private String getBottomIconLottieTag() {
        return BOTTOM_ICON_LOTTIE_TAG + BottomTabColorAndIconManager.INSTANCE.getMIsDarkMode();
    }

    public void cancelHomeTabRefreshDrawable() {
        if (mRadioGroup == null || mRbHomePage == null) {
            return;
        }
        if (hasBottomAtmophere) {
            return;
        }
        mRbHomePage.setTag(R.id.host_refresh_loading, false);
        boolean isCurChecked = (mRadioGroup.findViewById(mRadioGroup.getCheckedRadioButtonId()) == mRbHomePage);
        Logger.d(BOTTOM_TAG, "MainActivity cancelHomeTabRefreshDrawable: " + isCurChecked);

        if (mBottomHomePageRefreshDrawable instanceof LottieDrawable) {
            ((LottieDrawable) mBottomHomePageRefreshDrawable).setProgress(0);
            ((LottieDrawable) mBottomHomePageRefreshDrawable).cancelAnimation();
        }

        if (isCurChecked && mBottomHomePageTabDrawable instanceof LottieDrawable && mBottomHomePageTabDrawable != mRbHomePage.getCompoundDrawables()[1]) {
            mRbHomePage.setCompoundDrawablesWithIntrinsicBounds(null, mBottomHomePageTabDrawable, null, null);
            ((LottieDrawable) mBottomHomePageTabDrawable).setProgress(1.0f);
            mRbHomePage.setText(R.string.host_home_page);
        }
    }

    public void loadHomeTabRefreshDrawable(boolean playAnimation) {
        if (mRbHomePage == null || mRadioGroup == null) {
            return;
        }
        if (hasBottomAtmophere) {
            return;
        }
        mRbHomePage.setText(R.string.host_refresh);
        Logger.d(BOTTOM_TAG, "MainActivity loadHomeTabRefreshDrawable: " + playAnimation);
        if (mBottomHomePageRefreshDrawable != null) {
            Drawable drawable = mBottomHomePageRefreshDrawable;
            mRbHomePage.setCompoundDrawablesWithIntrinsicBounds(null, drawable, null, null);
            if (drawable instanceof LottieDrawable) {
                ((LottieDrawable) drawable).setProgress(0);
                ((LottieDrawable) drawable).loop(true);
                if (playAnimation) {
                    ((LottieDrawable) drawable).playAnimation();
                } else {
                    ((LottieDrawable) drawable).cancelAnimation();
                }
            }
            return;
        }

        mRbHomePage.setTag(R.id.host_refresh_loading, true);
        String lottieFile = (BaseFragmentActivity2.sIsDarkMode ? "lottie-night" : "lottie") + LOTTIE_BOTTOM_TAB_HOME_PAGE_BTN_REFRESH_JSON;
        try {
            LottieCompositionFactory.fromAsset(this, lottieFile).addListener(new LottieListener<LottieComposition>() {
                @Override
                public void onResult(LottieComposition composition) {
                    Logger.i(TAG, "loadBottomTabResource homepage refresh lottie loaded");
                    MyAsyncTask.execute(() -> {
                        XmLottieDrawable lottieDrawable = new XmLottieDrawable();
                        lottieDrawable.setComposition(composition);
                        lottieDrawable.setScale(1 / 3f);

                        mBottomHomePageRefreshDrawable = lottieDrawable;

                        HandlerManager.postOnUIThread(() -> {
                            // 这里做个简单的判断，如果还是选中的是首页、正在loading，那么就继续设置为refresh状态
                            boolean isCurChecked = (mRadioGroup.findViewById(mRadioGroup.getCheckedRadioButtonId()) == mRbHomePage);
                            if (canUpdateUi() && mRbHomePage != null && isCurChecked) {
                                Object tag = mRbHomePage.getTag(R.id.host_refresh_loading);
                                if (tag instanceof Boolean && (Boolean) tag) {
                                    mRbHomePage.setTag(R.id.host_refresh_loading, false);
                                    mRbHomePage.setCompoundDrawablesWithIntrinsicBounds(null, lottieDrawable, null, null);
                                    lottieDrawable.setProgress(0);
                                    lottieDrawable.loop(true);
                                    if (playAnimation) {
                                        lottieDrawable.playAnimation();
                                    } else {
                                        lottieDrawable.cancelAnimation();
                                    }
                                    mRbHomePage.setText(R.string.host_refresh);
                                }
                            }
                        });
                    });
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void loadBottomTabLottieResource(RadioButton radioButton, String fileName,
                                             boolean shouldStartPlayAfterLoaded) {
        if (radioButton != null) {
            try {
                LottieCompositionFactory.fromAsset(this, fileName).addListener(new LottieListener<LottieComposition>() {
                    @Override
                    public void onResult(LottieComposition composition) {
                        Logger.i(TAG, "loadBottomTabResource homepage lottie loaded");
                        // 判断下当前仍是lottie的，才设置上去
                        if (isBottomTabCurResourceOf(radioButton, getBottomIconLottieTag())) {
                            MyAsyncTask.execute(new Runnable() {
                                @Override
                                public void run() {
                                    XmLottieDrawable lottieDrawable = new XmLottieDrawable();
                                    lottieDrawable.setComposition(composition);
                                    lottieDrawable.setScale(1 / 3f);

                                    HandlerManager.postOnUIThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            radioButton.setTag(R.id.host_load_success, true);
                                            if (fix130WebViewBug()) {
                                                radioButton.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
                                            }
                                            if (radioButton == mRbHomePage) {
                                                mBottomHomePageTabDrawable = lottieDrawable;
                                            }
                                            if (radioButton == mRbHomePage && mHasSetHomePageTabAsRefreshBtn) {
                                            } else {
                                                if (radioButton == mRbFinding) {
                                                    radioButton.setTag(R.id.host_tab_dynamic_drawable, lottieDrawable);
//                                                    boolean hasSetDrawable = radioButton.getTag(R.id.host_tab_dynamic_red_hint_drawable) instanceof Boolean && (Boolean) radioButton.getTag(R.id.host_tab_dynamic_red_hint_drawable);
//                                                    if (!hasSetDrawable) {
                                                    radioButton.setCompoundDrawablesWithIntrinsicBounds(null, lottieDrawable, null, null);
//                                                    }
                                                } else if (mRbQuickListen == null || radioButton != mRbQuickListen){
                                                    //快听不用皮肤包
                                                    radioButton.setCompoundDrawablesWithIntrinsicBounds(null, lottieDrawable, null, null);
                                                }

                                                if (shouldStartPlayAfterLoaded) {
                                                    lottieDrawable.playAnimation();
                                                }
                                            }
                                        }
                                    });
                                }
                            });
                        }
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static Boolean sFix130WebViewBug = null;
    public static boolean fix130WebViewBug() {
        if (sFix130WebViewBug != null) {
            return sFix130WebViewBug;
        }
        boolean fix130WebConfig = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, "fix_webview_130_bug", true);
        if (!fix130WebConfig) {
            sFix130WebViewBug = false;
            return false;
        }
        PackageInfo packageInfo = PackageUtil.getPackageInfoFromPackageName(BaseApplication.getMyApplicationContext(), "com.google.android.webview");
        if (packageInfo == null) {
            sFix130WebViewBug = false;
            return false;
        }
        if (!TextUtils.isEmpty(packageInfo.versionName) && packageInfo.versionName.contains("130.0.6")) {
            // packageInfo.versionName = 130.0.6723.86
            sFix130WebViewBug = true;
            return true;
        }
        sFix130WebViewBug = false;
        return false;
    }

    public RadioButton getFindingTab() {
        return mRbFinding;
    }

    public void doSubscribeTabAnim() {
        BottomTabColorAndIconManager colorAndIconManager = BottomTabColorAndIconManager.INSTANCE;
        // 当前显示的不是lottie，需要加载下lottie资源
        String lottieFile = LOTTIE_BOTTOM_TAB_FINDING_SUBSCRIBE_JSON;
        if (!TextUtils.isEmpty(lottieFile)) {
            String lottieDirectory;
            if (BaseFragmentActivity.sIsDarkMode || colorAndIconManager.getMIsDarkMode()) {
                lottieDirectory = "lottie-night";
            } else {
                lottieDirectory = "lottie";
            }
            loadFindingTabLottieResource(lottieDirectory + lottieFile);
        }
    }

    private void loadFindingTabLottieResource(String fileName) {
        try {
            LottieCompositionFactory.fromAsset(this, fileName).addListener(new LottieListener<LottieComposition>() {
                @Override
                public void onResult(LottieComposition composition) {
                    MyAsyncTask.execute(new Runnable() {
                        @Override
                        public void run() {
                            XmLottieDrawable lottieDrawable = new XmLottieDrawable();
                            lottieDrawable.setComposition(composition);
                            lottieDrawable.setScale(1 / 3f);

                            HandlerManager.postOnUIThread(new Runnable() {
                                @Override
                                public void run() {
                                    mRbFinding.setCompoundDrawablesWithIntrinsicBounds(null, lottieDrawable, null, null);
                                    lottieDrawable.playAnimation();
                                    lottieDrawable.addAnimatorListener(new AnimatorListenerAdapter() {
                                        @Override
                                        public void onAnimationEnd(Animator animation) {
                                            super.onAnimationEnd(animation);
                                            mRbFinding.setTag(R.id.host_load_success, false);
                                            loadBottomTabResource(mRbFinding, TabFragmentManager.TAB_FINDING, mRbFinding.isChecked());
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean mHasDoBundleUpdate = false;

    private void doBundleUpdate() {
        if (mHasDoBundleUpdate) {
            return;
        }
        ConfigureCenter.getInstance().registerConfigureCallback(new IConfigureCenter.ConfigureCallback() {
            @Override
            public void onResult(boolean success) {
                ConfigureCenter.getInstance().unRegisterConfigureCallback(this);
                if (!success) {
                    mHasDoBundleUpdate = true;
                    BundleInfoManager.getInstance().checkAndUpdateAllBundle();
                    return;
                }
                boolean isNeedCheckRateLimits = ConfigureCenter.getInstance().getBool(
                        CConstants.Group_android.GROUP_NAME,
                        CConstants.Group_android.KEY_IS_NEED_CHECK_RATE_LIMITS, false);
                if (isNeedCheckRateLimits) {
                    JSONObject json = new JSONObject();
                    try {
                        json.put("client", "android");
                        json.put("domain", BundleRequestCache.REMOTE_PLUGIN_DOMAIN);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    CommonRequestM.checkRateLimits(json.toString(), new IDataCallBack<Boolean>() {
                        @Override
                        public void onSuccess(@Nullable Boolean data) {
                            if (data != null && data) {
                                mHasDoBundleUpdate = true;
                                BundleInfoManager.getInstance().checkAndUpdateAllBundle();
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            mHasDoBundleUpdate = true;
                            BundleInfoManager.getInstance().checkAndUpdateAllBundle();
                        }
                    });
                } else {
                    mHasDoBundleUpdate = true;
                    BundleInfoManager.getInstance().checkAndUpdateAllBundle();
                }
            }
        });

    }

    public static boolean hasInitAfterViewDraw = false;

    private boolean isHasInitPlayService;
    private void initPlayService() {
        if (isHasInitPlayService
                || HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()
        ) {
            return;
        }
        isHasInitPlayService = true;
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("_____initPlayService");
        XmPlayerManager.getInstance(this).setIfInAppInitialization(false);
        XmPlayerManager.getInstance(this).init(true);
        TingLocalMediaService.getInstance().init(this.getApplicationContext(), XmPlayerManager.getInstance(this.getApplicationContext()));
    }

    @StartUpMonitor
    public void initAfterViewDraw() {
        Logger.d("feiwen", "initAfterViewDraw -------- hasInitAfterViewDraw = " + hasInitAfterViewDraw);
        if (!hasInitAfterViewDraw) {
            hasInitAfterViewDraw = true;
            try {
                Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN);
            } catch (Throwable e) {
                e.printStackTrace();
            }
            initPlayService();
            //TODO IM登陆模块初始化

            if (!HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()) {
                initIMXChatLogin();
            }

            //TODO IM系统更新
            NoReadManage.getInstance(this).registerIMCallback();
            PrivateChatToastSnackManager.INSTANCE.registerIMCallback();
            NoReadManage.getInstance(this).addNoReadUpdateListenerListener(this);
            NoReadManage.getInstance(this).updateNoReadManageFromNet();
            ListenTaskMsgManager.getInstance(this);
            NoReadManage.getInstance(this).addNewSocketMsgListener(NewUserTaskMsgManager.INSTANCE);
            NoReadManage.getInstance(this).addNewSocketMsgListener(VipStatusChangedMsgManager.INSTANCE);
            LogDumpManager logDumpManager = LogDumpManager.INSTANCE;

            PushManager.getInstance().init(MainApplication.getInstance());

            XmLivePushReportHelper.INSTANCE.init();
            StatusBarManager.transparencyBar(this);
            try {
                this.startService(new Intent().setClass(this, XiMaLaYaService.class));
                DownloadServiceManage.getInstance().bindDownloadServiceService(this);
            } catch (Throwable t) {
                t.printStackTrace();
            }
            PhoneGrade.getInstance().setILogPoster(new ILogPosterWrapper(this));

            PhoneGrade.getInstance().checkDevice();
            try {
                Logger.d(TAG, "addIdleHandler");
                StartupOptManager.addMainLooperIdleHandler(new MessageQueue.IdleHandler() {
                    @Override
                    public boolean queueIdle() {
                        Logger.d(TAG, "addIdleHandler MyAsyncTask");
                        MyAsyncTask.execute(new Runnable() {
                            @Override
                            public void run() {
                                Logger.d(TAG, "addIdleHandler MyAsyncTask.execute");
                                EncryptServiceCheckUtil.checkIsRight(EncryptUtil.getInstance(MainActivity.this), MainActivity.this);
                                if (UseLogUploadManager.isNeedUploadFrequently()) {
                                    BgPlayOptManagerForKeyMan.getInstance().init(BaseApplication.getMyApplicationContext());
                                }
                                ConfigureCenter.getInstance().registerConfigureCallback(new IConfigureCenter.ConfigureCallback() {
                                    @Override
                                    public void onResult(boolean isSuccess) {
                                        ConfigureCenter.getInstance().unRegisterConfigureCallback(this);
                                        if (!isSuccess) {
                                            return;
                                        }
                                        MmkvCommonUtil.getInstance(MainActivity.this.getApplicationContext()).saveBoolean(CConstants.Group_android.ITEM_LAUNCH_XIMA_LOGO_CAN_SHOW,
                                                ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME,
                                                        CConstants.Group_android.ITEM_LAUNCH_XIMA_LOGO_CAN_SHOW,false));
                                        try {
                                            boolean isOpenAutoOpt = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME
                                                    , CConstants.Group_android.ITEM_IS_OPEN_AUTO_STORAGE_APT);
                                            if (isOpenAutoOpt) {
                                                StorageOptionManger.getSingleInstance().checkIfNeedAutoOptimizeDiskMemory();
                                                AllBuzzStorageManager.getSingleInstance().checkIfNeedAutoOptimizeDiskMemory();
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            XDCSCollectUtil.statErrorToXDCS("autoOptimizeDiskMemory", "crash:____" + Log.getStackTraceString(e));
                                        }

                                        boolean isUploadAppExitInfo = false;
                                        try {
                                            isUploadAppExitInfo = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME
                                                    , CConstants.Group_android.ITEM_IS_UPLOAD_APP_EXIT_INFO);

                                        } catch (NonException e) {
                                            e.printStackTrace();
                                        }
                                        try {
                                            int mainProcessDelayExitTime = ConfigureCenter.getInstance().getInt(CConstants.Group_android.GROUP_NAME
                                                    , CConstants.Group_android.ITEM_MAIN_PROCESS_DELAY_EXIT_TIME);
                                            MmkvCommonUtil.getInstance(MainActivity.this.getApplicationContext())
                                                    .saveInt(CConstants.Group_android.ITEM_MAIN_PROCESS_DELAY_EXIT_TIME, mainProcessDelayExitTime);
                                        } catch (NonException e) {
                                            e.printStackTrace();
                                        }
                                        boolean isUseCustomNotification = true;
                                        if (isUseCustomNotification) {
                                            boolean hasInit = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_IS_USE_CUSTOM_NOTIFICATION, false);
                                            if (!hasInit) {
                                                Logger.e("cf_test", "removeCompat:___NotificationStyleUtils.NOTIFICATION_STYLE");
                                                MmkvCommonUtil.getInstance(MainActivity.this.getApplicationContext()).removeCompat(NotificationStyleUtils.NOTIFICATION_STYLE);
                                                XmPlayerManager.getInstance(getApplicationContext()).changeNotificationStyle(NotificationStyleUtils.NOTIFICATION_STYLE_AUTO);
                                            }
                                        }
                                        MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInOpenSdk.KEY_IS_USE_CUSTOM_NOTIFICATION, true);
                                        if (isUploadAppExitInfo) {
                                            SystemSettingCollector.uploadSystemSettings(MainActivity.this.getApplicationContext());
                                            AppExitInfoManager.getSingleInstance().initialize(MainActivity.this.getApplicationContext());
                                            boolean isBgPlayProcessOptOpen = ABTest.getBoolean(CConstants.Group_android.ITEM_IS_BG_PLAY_OPT_OPEN, false);
                                            int maxKillTimes = ConfigureCenter.getInstance().getInt(CConstants.Group_android.GROUP_NAME
                                                    , BgPlayOptManager.sMaxKillTimeKey, 1);
                                            MmkvCommonUtil.getInstance(MainActivity.this.getApplicationContext()).saveInt(BgPlayOptManager.sMaxKillTimeKey, maxKillTimes);
                                            if (isBgPlayProcessOptOpen) {
                                                boolean isOpenLockScreen = MmkvCommonUtil.getInstance(MainActivity.this.getApplicationContext())
                                                        .getBooleanCompat(PreferenceConstantsInHost.KEY_LOCK_SCREEN_OPEN, true);
                                                boolean isLockScreenCheckBoxChecked = MmkvCommonUtil.getInstance(MainActivity.this.getApplicationContext())
                                                        .getBooleanCompat(PreferenceConstantsInHost.KEY_LOCK_SCREEN_CHECKBOX_CHECKED, true);
                                                if (isLockScreenCheckBoxChecked && isOpenLockScreen) {
                                                    BgPlayOptManager.getSingleInstance().init(MainActivity.this.getApplicationContext(), false);
                                                } else {
                                                    BgPlayOptManager.getSingleInstance().init(MainActivity.this.getApplicationContext(), true);
                                                }
                                            } else {
                                                BgPlayOptManager.getSingleInstance().init(MainActivity.this.getApplicationContext(), false);
                                            }

                                        }
                                        PackageManagerHook.getPackageManagerHookConfig();
                                    }
                                });
                                FileProtectorManager.getSingleInstance().addFileAccessCallback(IFileAccessCallback.DELETE_FILE, mFileDeleteCallback);
                                getNextSoundPatchConfig();
                            }
                        });
                        if (!RiskUtils.showHighRiskDialog(MainActivity.this)) {
                            // 隐私风险检查上报
                            RiskUtils.startPrivacyRiskCollector(getContext());
                        }
                        MultiAsyncTaskUtil.uploadException();
                        ReadTimeReportManager.INSTANCE.uploadReadDurationWhenAppKill();
                        return false;
                    }
                });

                HandlerManager.postOnUIThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        StartupOptManager.addMainLooperIdleHandler(new MessageQueue.IdleHandler() {
                            @Override
                            public boolean queueIdle() {
                                // 异步更新下UA,虽然此函数在子线程中运行,但是系统还是会在在主线程中做一些操作
                                DeviceUtil.getUAByWebSettingsAsync(MainActivity.this);
                                return false;
                            }
                        });
                    }
                }, 10000);
            } catch (Throwable e) {
                e.printStackTrace();
            }
            MyAsyncTask.execute(new Runnable() {
                @Override
                public void run() {
                    FireworkApi.getInstance().downloadOffLineRes();
                    FileNameUploadManager.INSTANCE.report();
                    SoNameUploadManager.INSTANCE.report();
                }
            });

            if (!XmAppStartManager.sHasRefreshed) {
                ABTest.refreshAbApi(getAbTestCallBack(MainActivity.this));
            } else {
                Logger.i("feiwen", "initAfterViewDraw  sHasRefreshed = true");
                onConfigSuccessOrFail();
            }
            OfflineResourceAPI.refreshConfig();

//            WebResInterceptManager.getInstance().fetchTheStaticResource();
//            XmRouterSchemeManager.getInstance().readSchemePathConfig(); // 去掉统跳请求scheme
            CacheFileCleaner.checkAndCleanUnusedFiles();

            UnLockSoundPatchManager.getInstance().init();
            AdMakeVipLocalManager.getInstance().init();
            FoldableScreenCompatUtil.INSTANCE.startListen();
            LikeTrackStateChangeManager.INSTANCE.init();
            FreeListenSoundPatchManager.INSTANCE.init();
            refreshCuSoundEffect();
            SplashScreenManager.updateSplashScreen();
            if (!HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()) {
                getUserBottomTabTagInfo();
            }
            updateFirstUserInfo();
            CleanSdkManager.INSTANCE.report();

        }
    }

    private boolean mHasLoadBottomTab = false;

    private void bottomTabLoad() {
        if (mHasLoadBottomTab) {
            return;
        }
        mHasLoadBottomTab = true;
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("_____bottomTabLoad");
        if (mRbFinding != null && vsDyncTip != null && MyListenAbUtil.INSTANCE.isKeepSubscribeInFeed()) {
            DynamicTabRedHintManager.INSTANCE.initView(mRbFinding, vsDyncTip);
        }
        checkDynamicTabCanUpdate();
        updateMineListenTabRedDot(false, false);
        traceBottomTabTagShow();
        getUserBottomTabTagInfo();
    }

    private void getNextSoundPatchConfig() {
        int gap = ConfigureCenter.getInstance().getInt(CConstants.Group_toc.GROUP_NAME, CConstants.Group_toc.NEXT_VOICE_GAP, 0);
        int minDuration = ConfigureCenter.getInstance().getInt(CConstants.Group_toc.GROUP_NAME, CConstants.Group_toc.NEXT_VOICE_LENGTH, 5);
        MmkvCommonUtil.getInstance(this).saveInt(PreferenceConstantsInOpenSdk.KEY_NEXT_SOUND_PATCH_GAP, gap);
        MmkvCommonUtil.getInstance(this).saveInt(PreferenceConstantsInOpenSdk.KEY_NEXT_SOUND_MIN_DURATION, minDuration);
    }

    private void updateFirstUserInfo() {
        if (!ToolUtil.isFirstInstallApp(this) && ToolUtil.isTodayFirstOpenApp()) {
            CommonRequestM.updateFirstUserInfo(new IDataCallBack<Boolean>() {
                @Override
                public void onSuccess(@Nullable Boolean data) {
                    Logger.log("Main updateFirstUserInfo data " + data);
                }

                @Override
                public void onError(int code, String message) {
                    Logger.log("Main updateFirstUserInfo onError  " + message);

                }
            });
        }
    }

    @Override
    public void onUnreadMessageAdd(int count, boolean isNewMessage) {
        Logger.i(TAG, "onUnreadMessageAdd " + count + " " + isNewMessage);
        NoReadModel noReadModel = NoReadManage.getInstance(this).getNoReadModel();
        if (noReadModel == null || !isNewMessage) {
            return;
        }
        noReadModel.setUnReadMessageCount(count);
        if (tabFragmentManager != null && tabFragmentManager.getCurrFragment() instanceof IMainFunctionAction.AbstractHomePageFragment) {
            ((IMainFunctionAction.AbstractHomePageFragment) tabFragmentManager.getCurrFragment()).refreshMsgCount();
        }
        updateMineListenTabRedDot(false, false);
    }

    static class ILogPosterWrapper implements ILogPoster {
        public WeakReference<MainActivity> mainRef;

        public ILogPosterWrapper(MainActivity activity) {
            mainRef = new WeakReference<>(activity);
        }

        @Override
        public void postMessage(String cause) {
            PhoneGrade.getInstance().setILogPoster(null);
            XDCSCollectUtil.statErrorToXDCS("PhoneGrade", "PhoneGrade fail reason " + cause);
        }
    }


    private void refreshCuSoundEffect() {
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
            @Override
            public void onConnected() {
                Logger.log("mainactivity play service connected");
                HandlerManager.postOnUIThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        TrackPlaySoundEffectManager.getInstance().refreshCurSoundEffect();
                    }
                }, 1000);

            }
        });
    }

    private void showContinuePlayTips() {
        if (RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment()) {
            return;
        }
        if (!ABTest.getString(CConstants.Group_toc.ITEM_CHASING_PRODUCT, "0").equals("0")) {
            return;
        }
        if (!ToolUtil.isFirstInstallApp(this) && !mHasShowContinuePlayTips && !isHandleIting) {
            mHasShowContinuePlayTips = true;
            EventManager.Action continuePlayTipAction = new EventManager.Action();
            continuePlayTipAction.name = "ContinuePlayTip";
            continuePlayTipAction.delay = 500;
            continuePlayTipAction.uiRunnable = new Runnable() {
                @Override
                public void run() {
                    if (XmPlayerManager.getInstance(MainActivity.this).getMixPlayTrack() != null) {
                        return;
                    }

                    PlayableModel playableModel = XmPlayerManager.getInstance(MainActivity.this)
                            .getCurrSound();

                    if (playableModel != null) {
                        final String[] title = {""};
                        if (playableModel instanceof Track) {
                            Track currentTrack = (Track) playableModel;
                            title[0] = currentTrack.getTrackTitle();
                            if (currentTrack.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY &&
                                    currentTrack.getChannelType() == ConstantsOpenSdk.CHANNEL_TYPE_DAILY_NEWS &&
                                    DailyNewsUtil.lastRequestOver2Hours(currentTrack.getChannelId())) {
                                int channelId = (int) currentTrack.getChannelId();
                                Map<String, String> params = new ArrayMap<>(2);
                                params.put(DTransferConstants.DAILY_NEWS_CHANNEL_ID, channelId + "");
                                long lastRequestTime = DailyNewsUtil.getLastRequestTime(channelId, System.currentTimeMillis());
                                params.put("startTime", lastRequestTime + "");
                                CommonRequestM.getDailyNewsUpdateCount(params, new IDataCallBack<Integer>() {
                                    @Override
                                    public void onSuccess(@Nullable final Integer updateCount) {
                                        if (!canUpdateUi() || updateCount == null) return;
                                        if (updateCount > 0) {
                                            title[0] = "继续播：今日热点为您带来" + updateCount + "条快讯";
                                            handleContinueTipsLogic(false, title[0]);
                                        } else {
                                            handleContinueTipsLogic(true, title[0]);
                                        }
                                    }

                                    @Override
                                    public void onError(int code, final String message) {
                                        if (!canUpdateUi()) return;
                                        handleContinueTipsLogic(true, title[0]);
                                    }
                                });
                                return;
                            }
                        } else if (playableModel instanceof Radio) {
                            title[0] = ((Radio) playableModel).getRadioName();
                        } else if (playableModel instanceof Schedule) {
                            title[0] = ((Schedule) playableModel).getRadioName();
                        }
                        handleContinueTipsLogic(true, title[0]);
                    } else {
                        DynamicTabRedHintManager.INSTANCE.willHideStrongTip();
                        checkNewUserPlayTipsGuide();
                    }
                }
            };
            if (UserInfoMannage.hasLogined()) {
                continuePlayTipAction.addDependentEvent(new EventManager.Event("tab_image_tip_dismiss"));
            }
            continuePlayTipAction.addDependentEvent(new EventManager.Event("final_dialog_dismiss"));
            EventManager.getInstance().addAction(continuePlayTipAction);
            // 3秒后如果没有其他弹窗 弹出续播
            HandlerManager.obtainMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    // 首页9.0引导或者账号页强引导和续播提示互斥
                    if (ViewUtil.haveDialogIsShowing(MainActivity.this)) {
                        return;
                    }
                    EventManager.getInstance().notifyEvent(new EventManager.Event("final_dialog_dismiss"));
                }
            }, 2000);
        } else {
            if (!mHasShowContinuePlayTips) {
                mHasShowContinuePlayTips = true;
            }
            DynamicTabRedHintManager.INSTANCE.willHideStrongTip();
        }
    }

    /**
     * 如果是新用户、处于零播状态、在首页、再次打开app时候显示引导播放,每天展示一次
     */
    private void checkNewUserPlayTipsGuide() {
        if (VersionUtil.isNewUser()) {
            long lastGuideTime = MmkvCommonUtil.getInstance(this).getLong(PreferenceConstantsInHost.KEY_TIME_CHECK_NEWUSER_PLAYTIPSGUIDE, -1);
            if ((System.currentTimeMillis() - lastGuideTime) < 24 * 60 * 60 * 1000) {
                return;
            }
            CommonRequestM.getNoPlayTipResult(new IDataCallBack<NoPlayTipModel>() {
                @Override
                public void onSuccess(@Nullable NoPlayTipModel data) {
                    if (!canUpdateUi()) {
                        return;
                    }
                    if (data != null && mRbHomePage.isChecked()) {
                        if (NativeHybridFragment.isItingScheme(data.getUrl())) {
                            // 点击肚挤眼播放限免专辑第一个声音
                            if (data.getType() == 2 && data.getAlbum() != null) {
                                NoPlayTipModel.AlbumBean tipAlbum = data.getAlbum();
                                if (mPlayBarFragment != null) {
                                    mPlayBarFragment.showNewUserPlayGuideTips(tipAlbum.getAlbumTitle(), data.getSubTitle(), data.getUrl(), tipAlbum.getCoverPath(), tipAlbum.getAlbumSubscript());
                                }
                            } else {
                                if (mPlayBarFragment != null) {
                                    mPlayBarFragment.showNewUserPlayGuideTips(data.getTitle(), data.getSubTitle(), data.getUrl(), "", "");
                                }
                                // 点击肚挤眼跳转到新人必听榜
                            }
                        }
                    }
                    MmkvCommonUtil.getInstance(MainActivity.this).saveLong(PreferenceConstantsInHost.KEY_TIME_CHECK_NEWUSER_PLAYTIPSGUIDE, System.currentTimeMillis());
                }

                @Override
                public void onError(int code, String message) {
                }
            });
        }
    }

    private void handleContinueTipsLogic(boolean addTip, String title) {
        if (!TextUtils.isEmpty(title) && mPlayButtonIsShow) {
            // 第一次显示强提示
            String content = addTip ? "继续播放：" + title : title;
            if (!SharedPreferencesUtil.getInstance(MainActivity.this).getBoolean(
                    PreferenceConstantsInHost.KEY_HAS_SHOWN_STRONG_CONTINUE_PLAY_TIPS, false)
                    // 广告 高光touch 横条没有在展示
                    && !TempDataManager.getInstance().getBoolean(TempDataManager.DATA_HIGHT_LIGHT_AD_SHOWING)) {
                SharedPreferencesUtil.getInstance(MainActivity.this).saveBoolean(
                        PreferenceConstantsInHost.KEY_HAS_SHOWN_STRONG_CONTINUE_PLAY_TIPS, true);
//                DynamicTabRedHintManager.INSTANCE.willShowStrongTip();
                if (mPlayBarFragment != null) {
                    mPlayBarFragment.showStrongContinuePlayTips(title, "");
                }
            } else {
                HandlerManager.postOnUIThreadDelay(DynamicTabRedHintManager.INSTANCE::willHideStrongTip, 5000);
                PlayBarFragment.ShowTipBroadCast.setTipsContent(MainActivity.this, content);
            }
        } else {
            DynamicTabRedHintManager.INSTANCE.willHideStrongTip();
        }
    }

    @StartUpMonitor
    private void initOnCreate(Bundle savedInstanceState) {
        AutoSizeLog.d("mainActivity onCreate");
        XMTraceApi.getInstance().setUploadDebug(ConstantsOpenSdk.isDebug);
        // DAU统计
        XmDauStat.getInstance().statStartup();

        mActivityTabManager = new MainActivityTabManager(this);

        BaseApplication.setMainActivity(this);
        if (NovelTabAbManager.INSTANCE.getShouldChangeTabType()) {
            NovelTabAbManager.INSTANCE.changeVipTabToNovel(false);
        }

        ElderlyModeManager.getInstance().init(this);
        VipGiftShareManager.INSTANCE.init();
        AnchorAbUtil.INSTANCE.getUseNewPersonal();

        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onCreate begin");
        super.onCreate(savedInstanceState);

        mIsRestoreFromBackground = savedInstanceState != null;


        XmLoadingLayout.setVerticalLayoutId(this, R.layout.host_refresh_header_usexmlottie_lay, ConstantsOpenSdk.isDebug);

        ExitStatisticUtil.registerCloseReceiver(this);

        //内部有10秒延时，不影响启动
//        HwIntentDonateManager.uploadHwCacheErrorEvent();
        //检测是否有移动活动,内部有5000延时，不影响启动
        CmccActivitySdkManager.requestCmccActivityDelay(5000);
    }

    @Override
    @StartUpMonitor
    protected void onCreate(Bundle savedInstanceState) {
        RecommendPreLoadOptManager.INSTANCE.log("MainActivity_onCreate___");
        ClipUtils.resetClipContent();
        SplashScreenManager.showSplashScreen(this);
        NewUserStartTimeConstants.sAcOnCreateTime = System.currentTimeMillis();
        StartUpRecord.addRecord("ma-create");
        StartUpRecord.setMainOnCreate(this);
        if (PreInstallUtil.canUseStatistics(this)) {
            uploadItingPath(getIntent());
        }
        if (!FixLaunchModeBug.canRunOnCreate) {
            Logger.log("MainActivity : onlyRunSuperOnCreate");
            onlyRunSuperOnCreate(savedInstanceState);
            return;
        }
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.updateAbConfig();
        RecommendPreLoadOptManager.INSTANCE.updateAbConfig();
        StartupOptManager.setIsOpenOpt(HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedForcePreloadRecommendPage());

        Uri itingUri = null;
        // iting（非推送）落地优化
        if (getIntent() != null && (getIntent().getData() != null || getIntent().getExtras() != null) && !ITingArrivedTraceManager.INSTANCE.isHotStart()) {
            if (getIntent().getData() != null) {
                itingUri = getIntent().getData();
            } else if (getIntent().getExtras() != null) {
                Bundle bundle = getIntent().getExtras();
                for (String key : bundle.keySet()) {
                    if (!TextUtils.isEmpty(key) && bundle.get(key) instanceof String) {
                        String value = (String) bundle.get(key);
                        if (!TextUtils.isEmpty(value) && value.contains("iting://open")) {
                            try {
                                itingUri = Uri.parse(value);
                                break;
                            } catch (Exception e) {
                            }
                        }
                    }
                }
            }
            Logger.d("pushTag", "itingUri = " + itingUri);
            if (itingUri != null && !TextUtils.isEmpty(itingUri.toString())) {
                boolean isHomeIting = itingUri.toString().contains("iting://open?msg_type=332") || "itingwelcom://open".equals(itingUri.toString());
                if (!isHomeIting) {
                    ITingArrivedTraceModel model = new ITingArrivedTraceModel(itingUri.toString());
                    model.initTraceType();
                    if (model.getDirectLanding() == ITingArrivedTraceModel.Companion.getDIRECTLANDING_TYPE_OK() ||
                            model.getDirectLanding() == ITingArrivedTraceModel.Companion.getDIRECTLANDING_TYPE_ONLY_JUMP_HOME()) {
                        ITingArrivedTraceManager.INSTANCE.getInstance().traceBaseHome(model);
                        mFromItingDirectLanding = true;
                    }
                    // iting直接跳过首页，ab实验
                    if (!mFromItingDirectLanding && OuterItingManager.INSTANCE.canSkipHomePage(itingUri)) {
                        ITingArrivedTraceManager.INSTANCE.getInstance().traceBaseHome(model);
                        mFromItingDirectLanding = true;
                        model.setDirectLanding(ITingArrivedTraceModel.Companion.getDIRECTLANDING_TYPE_ONLY_JUMP_HOME());
                        Logger.i("ArriveTraceManagerTest", "has iting, skip home page: "
                                + mFromItingDirectLanding);
                    }
                }
            }
        }

        // push跳过首页，不跳过广告
        if (PushCommonUtil.pushFromIntent(getIntent()) && PushCommonUtil.shouldSkipHome()) {
            mFromPushIgnoreHome = true;
        }

        HomeRnTraceTimeManager.init();
        ITingArrivedTraceManager.INSTANCE.setHotStart(true);
        ChildProtectManager.addChildProtectStatusListener(mChildProtectStatusListener);
        MinorsModeHelper.INSTANCE.startWatchingMinorsModeState(getApplicationContext());

        XmPlayerManager.getInstance(this).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
            @Override
            public void onConnected() {
                XmPlayerManager.getInstance(MainActivity.this).removeOnConnectedListerner(this);
                if (getIntent() != null &&
                        getIntent().hasExtra(XmNotificationCreater.NOTIFICATION_EXTRY_KEY) &&
                        getIntent().getStringExtra(XmNotificationCreater.NOTIFICATION_EXTRY_KEY).equals(XmNotificationCreater.NOTIFICATION_EXTYR_DATA)) {
                    boolean pushGuardFlag = XmPlayerManager.getInstance(MainActivity.this).getPushGuardFlag();
                    // 冷启动点击通知栏  其他事件
                    new XMTraceApi.Trace()
                            .setMetaId(51733)
                            .setServiceId("others") //  冷启动用户点通知栏打开app时上报
                            .put("description", pushGuardFlag ? "pushGuard" : "none") // pushGuard 表示保活带来的，none 表示非保活带来的
                            .createTrace();
                    if (pushGuardFlag) {
                        XmPlayerManager.getInstance(MainActivity.this).change2RecommendListForPush(false);
                        XmPlayerManager.getInstance(MainActivity.this).tracePushGuardFlag(true);
                    }
                }
                XmPlayerManager.getInstance(MainActivity.this).tracePushGuardFlag(false);
                if (ToolUtil.isFirstInstallApp(getContext())) {
                    BaseModeManager.INSTANCE.setTrackListToNormalPlayerIfNeeded(getContext());
                }
            }
        });

        // 针对iting://open跳转到MainActivity 无3G弹窗处理
        mStartTimeTest = System.currentTimeMillis();
        if (!PreInstallUtil.canUseStatistics(this)) {
            super.onCreate(savedInstanceState);
            StartServiceTimeoutFixUtil.updateActivityForeground(MainActivity.class.getName(), false);
            PreInstallUtil.getCompanyNameFromNotificationIntent(getIntent());
            Intent targetIntent = new Intent(this, PrivacyHintActivity.class);

            if (getIntent() != null && (getIntent().getData() != null
                    && getIntent().getData().toString().contains("iting") || getIntent().getBooleanExtra(THIRD_APP_AUTHORIZE_FLAG, false))) {
                targetIntent.setData(getIntent().getData());
                Bundle extras = getIntent().getExtras();
                if (extras != null) {
                    targetIntent.putExtras(extras);
                }

                targetIntent.putExtra(PreferenceConstantsInHost.KEY_NEED_SET_EXTRA_TO_STRART, true);
            }
            ITingArrivedTraceManager.INSTANCE.setHotStart(false);

            startActivity(targetIntent);
            finish();
            // 添加 return (有时卡顿导致会执行下面init方法)
            return;
        } else if (PrivacyProtocolChangeManager.INSTANCE.isNeedShowPrivacyProtocolChangeDialog()) {
            // 有iting的话先不出隐私协议变更弹窗
            if (!(getIntent() != null && getIntent().getData() != null
                    && getIntent().getData().toString().contains("iting"))) {
                super.onCreate(savedInstanceState);
                StartServiceTimeoutFixUtil.updateActivityForeground(MainActivity.class.getName(), false);
                PreInstallUtil.getCompanyNameFromNotificationIntent(getIntent());
                Intent targetIntent = new Intent(this, PrivacyHintActivity.class);
                ITingArrivedTraceManager.INSTANCE.setHotStart(false);
                startActivity(targetIntent);
                finish();
                return;
            }
        }

        if (itingUri != null) {
            try {
                String msgType = itingUri.getQueryParameter("msg_type");
                if ("11".equals(msgType) || "218".equals(msgType) || LiveItingUtil.isOpenLiveRoomPageItingByMsgType(msgType)) {
                    Logger.i("ting", "XmPlayerManager init forPlay");
                    XmPlayerManager.getInstance(this).init(true);
                    TingLocalMediaService.getInstance().init(this.getApplicationContext(), XmPlayerManager.getInstance(this.getApplicationContext()));
                    ArriveTraceManager.INSTANCE.setMPreLoadPlayer(true);
                    if (!LiveItingUtil.isOpenLiveRoomPageItingByMsgType(msgType)) {
                        OuterItingManager.INSTANCE.waitToPlay(itingUri.toString());
                    }
                } else if ("13".equals(msgType)) {
                    try {
                        String autoPlay = itingUri.getQueryParameter("auto_play");
                        if (TextUtils.isEmpty(autoPlay)) {
                            autoPlay = itingUri.getQueryParameter("autoplay");
                        }
                        if ("true".equals(autoPlay)) {
                            OuterItingManager.INSTANCE.waitToPlay(itingUri.toString());
                        }
                    } catch (Exception e) {
                        Logger.e(e);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        ArriveTraceManager.onAppStart();

        // 是否来自于定投吊起
        boolean isFromDirectional = false;
        if (getIntent() != null && getIntent().getData() != null) {
            isFromDirectional = DirectionalAdFreeManager.INSTANCE.isDirectionalChannel(getIntent().getData().toString());
        }

        if (ResourcePositionAdFreeManager.INSTANCE.needRequestResourceAdFree()) {
            ResourcePositionAdFreeManager.INSTANCE.beginRequestResourceAdFree();
        }

        boolean showNewVersionGuideVideo = false;
        mFromSkipPush = canSkipSplashPush(); // 推送部分类型 跳过广告和homePage逻辑
        boolean skipSplashAd = canSkipSplashAd();
        splashIsDestroyed = skipSplashAd;
        SplashAdController.onMainActivityCreated(this);
        // 检测弹出登录
        CheckLoginUtils.checkLogin(this, itingUri, mFromSkipPush);

        String channelId = "";
        if (getIntent() != null && getIntent().getData() != null) {
            channelId = DirectionalAdFreeManager.INSTANCE.getChannelId(getIntent().getData().toString());
        }

        final boolean isFromDirectionalFinal = isFromDirectional;
        final String finalChannelId = channelId;
        if (!skipSplashAd && !mFromSkipPush) {
            showNewVersionGuideVideo = NewVersionGuideVideoManager.willShowNewVideoVersion(this);

            // 如果是暗黑切换或者展示新版本引导视频不请求广告
            boolean canLoadAd = !lastIsRestartActivity && !showNewVersionGuideVideo;

            if (canLoadAd) {
                RecommendPreLoadOptManager.INSTANCE.log("————————getAdFreeResult");
                DirectionalAdFreeManager.INSTANCE.getAdFreeResult(new ResourcePositionAdFreeManager.IResultCallBack() {
                    @Override
                    public void onResultBack(boolean adFree) {
                        if (!adFree) {
                            // 预加载开屏广告
                            RecommendPreLoadOptManager.INSTANCE.log("————————preloadSplashAd");
                            preloadSplashAd(isFromDirectionalFinal, finalChannelId);
                            RecommendPreLoadOptManager.INSTANCE.afterPreloadAd(MainActivity.this);
                            HomeRnTraceTimeManager.afterPreloadAd();
                        } else {
                            SplashAdRecord.reportPreloadDiscardEvent("adFree");
                        }
                    }
                });
            } else {
                DirectionalAdFreeManager.INSTANCE.recordDirectional(false);
            }
        }

        Logger.log("MainActivity : MainActivity_OnCreate 1");

        initOnCreate(savedInstanceState);
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.addRecommendFragmentVisibleListeners(this);
        Logger.log("MainActivity : MainActivity_OnCreate 2");

        // onDestroy可能走不到，在onCreate里面再清一下
        PlayerManager.release();

        Logger.d("skipSplashAd", "skipSplashAd = " + skipSplashAd + ", mFromSkipPush = " +
                mFromSkipPush + ", showNewVersionGuideVideo = " + showNewVersionGuideVideo + ", lastIsRestartActivity= " + lastIsRestartActivity);
        StartUpRecord.addRecord("adf-before");
        mStartSplashAdHelper = new StartSplashAdHelper();

        final StringBuilder coldStartNoAdMsg = new StringBuilder();
        if (skipSplashAd || mFromSkipPush) {
            coldStartNoAdMsg.append("skipSplashAd = " + skipSplashAd + " , mFromSkipPush = " + mFromSkipPush);
            // 没有广告的时候
            ArriveTraceManager.onNoAd();

            SplashAdController.resetStatusBar(getWindow());
            if (!mFromSkipPush) {
                doOnCreate();
            } else {
                doOnCreateFromPush();
            }
        } else {
            RecommendPreLoadOptManager.INSTANCE.setAdContainerViewBg(this);
            HomeRnTraceTimeManager.setAdContainerViewBg();
            mStartSplashAdHelper.setFromDirectional(isFromDirectional);
            mStartSplashAdHelper.setChannelId(channelId);
            if (showNewVersionGuideVideo) {
                coldStartNoAdMsg.append("showNewVersionGuideVideo");
                registerSplashAdState();
                mStartSplashAdHelper.showNewVersionGuideVideoFragment(MainActivity.this);
            } else {
                // 切换暗黑等方式导致activity的重新启动将不启动广告
                if (!lastIsRestartActivity) {
                    coldStartNoAdMsg.append("adFree=");
                    DirectionalAdFreeManager.INSTANCE.getAdFreeResult(new ResourcePositionAdFreeManager.IResultCallBack() {
                        @Override
                        public void onResultBack(boolean adFree) {
                            Logger.d("skipSplashAd", "getAdFreeResult = " + adFree);
                            coldStartNoAdMsg.append(adFree + "");

                            if (adFree) {
                                splashIsDestroyed = true;
                                // 没有广告的时候
                                ArriveTraceManager.onNoAd();
                                SplashAdController.resetStatusBar(getWindow());
                                doOnCreateAfterAd();
                            } else {
                                int adDelayTime = MmkvCommonUtil.getInstance(getContext())
                                        .getInt(CConstants.Group_android.ITEM_LAUNCH_XIMA_LOGO_SHOW_TIME, 0);
                                // 是否延迟加载广告设置
                                String launchDelay = ABTest.getString(CConstants.Group_android.KEY_LAUNCH_DELAY, "0");
                                Logger.i("cf_test", "launchDelay_____" + launchDelay);
                                if ("1".equals(launchDelay)) {
                                    adDelayTime = 1000;
                                }
                                Logger.i("cf_test", "launchDelay——————adDelayTime____" + adDelayTime);
                                Runnable runnable = new Runnable() {
                                    @Override
                                    public void run() {
                                        registerSplashAdState();
                                        RecommendPreLoadOptManager.INSTANCE.log("MainActivity___showSplashAdFragment___");
                                        mStartSplashAdHelper.showSplashAdFragment(MainActivity.this, true, finalChannelId);
                                    }
                                };
                                if (adDelayTime > 0) {
                                    HandlerManager.postOnUIThreadDelay(runnable, adDelayTime);
                                } else {
                                    runnable.run();
                                }
                            }
                        }
                    });
                } else {
                    coldStartNoAdMsg.append("lastIsRestartActivity=true");
                    // 没有广告的时候
                    ArriveTraceManager.onNoAd();
                    SplashAdController.resetStatusBar(getWindow());
                    doOnCreateAfterAd();
                }
            }
            lastIsRestartActivity = false;
        }

        SplashAdRecord.reportEvent("COLD_START", coldStartNoAdMsg.toString());

        ResourcePositionAdFreeManager.INSTANCE.updateOpenAppTimeStamp();
        PlanTerminateManager.init(); // 需要在这里调用 兼容直接进落地页的推送
        PlanTerminateManagerForQuickListen.init(); // 需要在这里调用 兼容直接进落地页的推送
        statOuterIting(getIntent());
        checkThirdAppAuthorizeActivity(getIntent());
        SoundAdConfigInitManager.initTrackVentConfig(); // 中插声音流广告的配置，需要在首页就保存
        ListRefreshCatchException.registerRefreshException();

        InterestCardManager.registerReceiver(this);
        boolean hasIting = isHandleIting || mHasIting;
        if (ToolUtil.isFirstInstallApp(this) && hasIting) {
            GrowthItingUtil.Companion.setHasLauncherPage(true);
            HandlerManager.postOnUIThreadDelay(() -> {
                GrowthItingUtil.Companion.setHasLauncherPage(false);
            }, 20000);
        }

        mRunnable = () -> {
            mSecond--;
            if (mSecond == 0) {
                startThirdApp();
            } else {
                HandlerManager.postOnUIThreadDelay(mRunnable, 1 * 1000);
            }
        };
        clickTabShowLogin = ConfigureCenter.getInstance().getBool(CConstants.Group_toc.GROUP_NAME, "mypage_login", true);

        // 上报播放进程启动失败各阶段的数据
        StartServiceTimeCollectUtil.uploadCollectDataToApm(BaseApplication.getMyApplicationContext());
        SharedPreferencesUtil.getInstance(getApplicationContext()).saveLong(
                PreferenceConstantsInHost.LAST_MAIN_ACTIVITY_PAUSE_TIME, System.currentTimeMillis());
        if (ConstantsOpenSdk.isDebug && isOpenFpsMonitor()) {
            fpsMontor = new BlockFpsMonitor();
        }
        MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInHost.KEY_PLAY_AD_FRAGMENT_SHOWING, false);

        // 预加载chatBundle
        MyListenRouterUtil.getChatFragAction();
        mVipRedDotPosition = new VipRedDotPosition(RedDotManage.getInstance(), new IRedDotAction() {
            @Override
            public void hideRedDot() {
            }
            @Override
            public boolean isViewVisible() {
                return ViewStatusUtil.viewIsRealShowing(mRbHomePage);
            }
        });
        mSubscribeRedDotPosition = new SubscribeRedDotPosition(RedDotManage.getInstance(), new IRedDotAction() {
            @Override
            public void hideRedDot() {
                hideSubscribeRedDot();
            }
            @Override
            public boolean isViewVisible() {
                return ViewStatusUtil.viewIsRealShowing(mRbHomePage);
            }
        });
        mMyRedDotPosition = new MyRedDotPosition(RedDotManage.getInstance(), new IRedDotAction() {
            @Override
            public void hideRedDot() {
                hideMineTabRedDot();
            }

            @Override
            public boolean isViewVisible() {
                return ViewStatusUtil.viewIsRealShowing(mRbHomePage);
            }
        });
        RedDotManage.getInstance().addRedDotPosition(mVipRedDotPosition);
        RedDotManage.getInstance().addRedDotPosition(mMyRedDotPosition);
        RedDotManage.getInstance().addRedDotPosition(mSubscribeRedDotPosition);
        DownloadIncentiveAdManager.requestDownloadConfig();
        RefreshHomeChannelUtils.init();
    }

    @Override
    public void onRecommendFragmentVisible() {
        // 首页可见兜底回调
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("首页可见兜底回调____onRecommendFragmentVisible");
        replaceManageFragment();
        loadPlayBarData();
        UploadBlueBoothMessageUtil.uploadBuleMessage();
        initIMXChatLogin();
        bottomTabLoad();
        initPlayService();
        KeyManLogUploadManager.setIsAfterStartUp(true);
    }

    private boolean isOpenFpsMonitor() {
        return MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getBoolean(
                PreferenceConstantsInOpenSdk.KEY_OPEN_BLOCK_MONITOR, false);
    }

    private void preloadSplashAd(boolean isFromDirectional, String channelId) {
        Logger.d("SplashAdxAdPreLoadManager", "pre request start : " + System.currentTimeMillis());
        // 预加载开屏广告
        SplashAdController.preloadSplashAd(this, isFromDirectional, channelId);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
                || ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_SPLASH_SHOW_BANG, false)) {
            FullScreenUseNotchUtil.setFullScreenWithSystemUi(getWindow(), true);
        }
    }

    private void uploadItingPath(Intent intent) {
        if (intent != null && (intent.getData() != null || intent.getExtras() != null)) {
            Uri uri = intent.getData();
            String pathString = "";
            if (uri != null && !TextUtils.isEmpty(uri.toString())) {
                pathString = uri.toString();
            } else {
                if (intent.getAction() != null && "com.coloros.push.internal.action".equals(intent.getAction())) {
                    if (intent.hasExtra(AppConstants.EXTRA_OPPO_ITING_URL)) {
                        pathString = intent.getStringExtra(AppConstants.EXTRA_OPPO_ITING_URL);
                    }
                }
            }
            if (!TextUtils.isEmpty(pathString) && pathString.contains("iting")) {
                new XMTraceApi.Trace()
                        .setMetaId(42737)
                        .setServiceId("others")
                        .put("itingUrl", pathString)
                        .immediatelyUpload()
                        .createTrace();
            }
        }
    }

    // 是否可以跳过广告
    private boolean canSkipSplashAd() {
        boolean skipSplashAd = false;
        if (ToolUtil.isFirstInstallApp(this)) {
            return true;
        }
        Intent intent = getIntent();
        if (intent != null && intent.getData() != null) {
            if (intent.getData().toString().contains(AppConstants.DATA_SKIP_AD_SPLASH)) {
                skipSplashAd = true;
            } else {
                String isDisplayParam = intent.getData().getQueryParameter("is_display");
                skipSplashAd = "false".equals(isDisplayParam);
            }
            if (skipSplashAd) {
                SplashAdRecord.reportPreloadDiscardEvent("DATA_SKIP_AD_SPLASH");
            }
        } else if (intent != null && intent.hasExtra(XMWXEntryActivity.KEY_EXT_INFO_FROM_WX)) {
            String extInfo = intent.getStringExtra(XMWXEntryActivity.KEY_EXT_INFO_FROM_WX);
            if (!TextUtils.isEmpty(extInfo) && NativeHybridFragment.isItingScheme(extInfo)) {
                Uri dpUri = Uri.parse(extInfo);
                if (dpUri != null) {
                    String isDisplayParam = dpUri.getQueryParameter("is_display");
                    skipSplashAd = "false".equals(isDisplayParam);
                }
            }
        }

        if (HomeReachAbUtil.coldNoAd()) {
            skipSplashAd = true;
            Logger.log("MainActivity : HomeReachAbUtil coldAdFree");
            HomeReachAbUtil.traceNoAd(true);
        }
        return skipSplashAd;
    }

    // 是否来自可以跳过广告的推送:目前只配置了少数几种可以跳过的推送iting类型
    private boolean canSkipSplashPush() {
        boolean skipSplashPush = false;
        if (PushCommonUtil.pushFromIntent(getIntent()) &&
                PushCommonUtil.shouldSkipAdAndHome() &&
                !ElderlyModeManager.getInstance().isElderlyMode()) {
            // 推送 跳过广告、首页
            skipSplashPush = true;
            Logger.d("SplashAdxAdPreLoadManager", "canSkipSplashPush 1");
        } else if (ITingArrivedTraceManager.INSTANCE.getMTraceModel() != null &&
                ITingArrivedTraceManager.INSTANCE.getMTraceModel().getDirectLanding() == ITingArrivedTraceModel.Companion.getDIRECTLANDING_TYPE_OK()) {
            // 增长需求：iting跳过广告、首页
            skipSplashPush = true;
            Logger.d("SplashAdxAdPreLoadManager", "canSkipSplashPush 2");
        }
        return skipSplashPush;
    }

    private boolean hasInflate;

    private void containViewInflate() {
        if (hasInflate) {
            return;
        }
        hasInflate = true;

        ViewStub viewStub = findViewById(R.id.host_act_stub);
        if (viewStub == null) {
            return;
        }
        viewStub.inflate();
        findViewById(R.id.host_rl_root).setBackgroundResource(R.color.host_main_background);

        tabFragmentManager = new TabFragmentManager(this);

        mManageFragment = new ManageFragment();

        replaceManageFragment();
    }
    private boolean mHasReplaceManageFragment;
    private void replaceManageFragment() {
        if (Looper.getMainLooper() != Looper.myLooper()) {
            return;
        }
        if (mManageFragment == null || mManageFragment.isAddFix()) {
            return;
        }
        if (mHasReplaceManageFragment || HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()) {
            return;
        }
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("_____replaceManageFragment");
        mHasReplaceManageFragment = true;
        if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedForcePreloadRecommendPage()) {
            commitChildReplaceFragment(R.id.fragment_full, mManageFragment);
        } else {
            replaceFragment(R.id.fragment_full, mManageFragment);
        }
//        replaceFragment(R.id.fragment_full, mManageFragment);
        mManageFragment.addStackChangeListener(mStackChangeListener);

        mPlayButtonImage = findViewById(R.id.fragment_playbar);
        mPlayButtonImageNew = findViewById(R.id.fragment_playbar_new);
        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onCreate begin22");
        mPlayerManager = PlayerManager.getInstanse();
        mPlayerManager.init(this, R.id.fragment_play);
    }

    private void checkAndInitManageAndPlayFragment() {
        if (Looper.getMainLooper() != Looper.myLooper()) {
            return;
        }
        if (mManageFragment == null || mManageFragment.isAddFix()) {
            HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("_____mManageFragment.isAddFix()");
            return;
        }
        if (mHasReplaceManageFragment) {
            HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("_____mHasReplaceManageFragment == true");
            return;
        }
        mHasReplaceManageFragment = true;
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("_____checkAndInitManageAndPlayFragment");
//        replaceFragment(R.id.fragment_full, mManageFragment);
        if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedForcePreloadRecommendPage()) {
            commitChildReplaceFragment(R.id.fragment_full, mManageFragment);
        } else {
            replaceFragment(R.id.fragment_full, mManageFragment);
        }
        mManageFragment.addStackChangeListener(mStackChangeListener);

        mPlayButtonImage = findViewById(R.id.fragment_playbar);
        mPlayButtonImageNew = findViewById(R.id.fragment_playbar_new);
        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onCreate begin22");
        mPlayerManager = PlayerManager.getInstanse();
        mPlayerManager.init(this, R.id.fragment_play);
    }

    private void commitChildReplaceFragment(int id, Fragment fragment) {
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("_____commitChildReplaceFragment");
        getSupportFragmentManager().beginTransaction()
                .replace(id, fragment).commitAllowingStateLoss();
    }

    private PushModel mPushModel;
    private boolean mHadHandlePushLogicFinished = false;
    private boolean mHasHandlePushDialogDismiss = false;
    private MyProgressDialog mPushWaitingDialog;
    private final IFragmentShowListenerForPush mShowListenerForPush = new IFragmentShowListenerForPush() {
        @Override
        public void onFragmentShow(String fragmentName) {
            if (fragmentName == null || fragmentName.equals("PlayBarFragment") ||
                    fragmentName.equals("PlayBarFragmentNew") || fragmentName.equals("ManageFragment")) {
                return;
            }
            if (!mHasHandlePushDialogDismiss && (mFromSkipPush || mFromItingDirectLanding || mFromPushIgnoreHome) && !isDoOnCreate && mPushWaitingDialog != null) {
                mHasHandlePushDialogDismiss = true;
                HandlerManager.postOnUIThread(() -> {
                    if (mPushWaitingDialog != null) {
                        mPushWaitingDialog.dismiss();
                    }
                });
            }
        }

        @Override
        public void onFragmentDestroy(String fragmentName) {
            if (fragmentName == null || fragmentName.equals("PlayBarFragment") ||
                    fragmentName.equals("PlayBarFragmentNew") ||
                    fragmentName.equals("ManageFragment")) {
                return;
            }
            Logger.d("pushTag", "onFragmentDestroy");
            HandlerManager.postOnUIThread(MainActivity.this::continueHomeLogicAfterPush);
            HandlerManager.postOnUIThreadDelay(MainActivity.this::continueHomeLogicAfterPush, 3000); // 兜底防白屏
        }
    };

    private void continueHomeLogicAfterPush() {
        Logger.d("pushTag", "continueHomeLogicAfterPush");
        if (!mHadHandlePushLogicFinished &&
                (mFromSkipPush || mFromItingDirectLanding || mFromPushIgnoreHome) &&
                isManagerEmpty() &&
                !isDoOnCreate &&
                !isPlayFragmentVisible()) {
            mHadHandlePushLogicFinished = true;
            Logger.e("pushTag", "real  continueHomeLogicAfterPush");
            readyInit();
        }
    }

    public boolean isManagerEmpty() {
        if (mManageFragment == null) {
            return true;
        }
        return mManageFragment.getFragmentCount() == 0;
    }

    private void doOnCreateAfterAd() {
        if (mFromItingDirectLanding || mFromPushIgnoreHome) {
            // 跳过首页，先到达落地页
            doOnCreateFromPush();
        } else {
            doOnCreate();
            boolean debugMode = "1".equals(ToolUtil.getDebugSystemProperty("debug.mark.showGuide", "0"));
            if (debugMode || ConfigureCenter.getInstance().getInt(CConstants.Group_android.GROUP_NAME, "show_quick_listen_guide_switch", 0) == 1) {
                checkToShowQuickListenGuideView();
            }
        }
    }

    private void doOnCreateFromPush() {
        containViewInflate();

        // handle iting
        Intent intent = getIntent();
        boolean pushFromIntent = PushCommonUtil.pushFromIntent(intent);
        Logger.d("pushTag", "doOnCreateFromPush");
        boolean fromITing = ITingArrivedTraceManager.INSTANCE.getMTraceModel() != null;
        if (pushFromIntent || fromITing) {
            Logger.d("pushTag", "pushFromIntent = " + pushFromIntent + "   ,fromITing = " + fromITing);
            // 这里还是需要加载肚脐眼，否则去播放页的推送等还是会有问题
            initPlayBar();

            // 上报推送到达首页埋点
            PushModel pushModel = PushCommonUtil.pushUrlFromIntent(intent);
            PushCommonUtil.reportPushHomeTrace(pushModel);

            // 专辑页自带弹框 排除掉
            boolean fromItingAlbum = ITingArrivedTraceManager.INSTANCE.getMTraceModel() != null &&
                    ITingArrivedTraceManager.INSTANCE.getMTraceModel().getMsgType() != null &&
                    ITingArrivedTraceManager.INSTANCE.getMTraceModel().getMsgType().equals("13");

            if (!PushCommonUtil.shouldHandleItingWithDialog(pushModel) && !fromItingAlbum) {
                // 专辑页iting请求里已经有弹窗了   这里排除掉
                Logger.d("pushTag", "mPushWaitingDialog show");
                mPushWaitingDialog = new MyProgressDialog(this);
                mPushWaitingDialog.setMessage("正在加载数据，请等待...");
                mPushWaitingDialog.show();
            }
            ITingArrivedTraceManager.INSTANCE.setMTraceModel(null);

            if (ConstantsOpenSdk.isBundleFrameWork &&
                    Configure.mainBundleModel.isDl &&
                    !Configure.mainBundleModel.hasGenerateBundleFile) {
                PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("0", "main bundle not install");
                Router.getActionByCallback(Configure.BUNDLE_MAIN, new Router.IBundleInstallCallback() {
                    @Override
                    public void onInstallSuccess(BundleModel bundleModel) {
                        PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("1", "main bundle install ok");
                        if (Configure.mainBundleModel.bundleName.equals(bundleModel.bundleName)) {
                            Logger.d("pushTag", "main bundle install success，handle model");
                            if (mPushModel != null || fromITing) {
                                EarnStatisticsLifeCycleCallback.INSTANCE.setFragmentShowListener(mShowListenerForPush);
                                if (pushFromIntent) {
                                    OuterItingManager.INSTANCE.markOuterItingAction();
                                    ArriveTraceManager.onGotOuterIting(mPushModel.url, ArriveTraceManager.ITING_SOURCE_PUSH);
                                    PushCommonUtil.handlePushMsg(MainActivity.this, mPushModel);
                                    mPushModel = null;
                                } else {
                                    doSomethingByIntent(getIntent());
                                }
                            }
                        }
                    }

                    @Override
                    public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                        mPushModel = null;
                    }

                    @Override
                    public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                        mPushModel = null;
                    }
                }, false, BundleModel.DOWNLOAD_DO_NOT);

                mPushModel = pushModel;
            } else {
                Logger.d("pushTag", "直接handle model");
                EarnStatisticsLifeCycleCallback.INSTANCE.setFragmentShowListener(mShowListenerForPush);
                if (pushFromIntent) {
                    OuterItingManager.INSTANCE.markOuterItingAction();
                    ArriveTraceManager.onGotOuterIting(pushModel.url, ArriveTraceManager.ITING_SOURCE_PUSH);
                    PushCommonUtil.handlePushMsg(MainActivity.this, pushModel);
                } else {
                    doSomethingByIntent(intent);
                }
            }
        }
        // after resume  doOnCreate

        // 延时12秒 doOnCreate兜底 防止白屏过久
        if (isDoOnCreate) {
            Logger.d("pushTag", "延时10秒 doOnCreate  return");
            return;
        }
        HandlerManager.postOnUIThreadDelay(() -> {
            if (mManageFragment != null && !isDoOnCreate && isManagerEmpty() && !isPlayFragmentVisible()) {
                try {
                    PushArrivedTraceManager.INSTANCE.getInstance().statErrorTrace("Time out1: no action after enter homePage");
                    mHadHandlePushLogicFinished = true;
                    Logger.d("pushTag", "延时8秒 doOnCreate兜底");
//                    mFromSkipPush = false; // 置为false，在doOnCreate中走正常的推送到达逻辑   (这个逻辑可能会导致重复进落地页)
                    readyInit();
                    if (mPushWaitingDialog != null) {
                        mPushWaitingDialog.dismiss();
                        mPushWaitingDialog = null;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }, 12 * 1000);
        TingLocalMediaService.getInstance().init(getApplicationContext(), XmPlayerManager.getInstance(getApplicationContext()));
    }
    private boolean mHasLoadPlayBarData;

    private void loadPlayBarData() {
        if (mHasLoadPlayBarData
                || !HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedForcePreloadRecommendPage()
                || mPlayBarFragment == null) {
            return;
        }
        mHasLoadPlayBarData = true;
        mPlayBarFragment.loadData();
    }

    public void initPlayBar() {
//        if (mHasInitPlayBar
////                || HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()
//        ) {
//            return;
//        }
//        mHasInitPlayBar = true;
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("_____initPlayBar");
        ViewStatusUtil.setVisible(View.GONE, findViewById(R.id.fragment_playbar_new));
        ViewStatusUtil.setVisible(View.VISIBLE, findViewById(R.id.fragment_playbar));
        if (mPlayBarFragment == null) {
            mPlayBarFragment = new PlayBarFragment();
            Bundle bundle = new Bundle();
            mPlayBarFragment.setArguments(bundle);
//                if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedForcePreloadRecommendPage()) {
//                    commitChildReplaceFragment(R.id.fragment_playbar, mPlayBarFragment);
//                } else {
//                }
            replaceFragment(R.id.fragment_playbar, mPlayBarFragment);
        }
    }

    private boolean isDoOnCreate = false;

    @StartUpMonitor
    private void doOnCreate() {
        if (isDoOnCreate) {
            return;
        }

        InitAfterSettingUpdated.tryInit();

        EarnStatisticsLifeCycleCallback.INSTANCE.setFragmentShowListener(null);
        isDoOnCreate = true;

        MainApplication.getInstance().init();

        Logger.log("xinle MainActivity acitivityLife: doOnCreate ");

        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onInitDoSomething begin");

        // 先禁止统一弹屏弹出，等青少年模式弹窗弹完或者确定不弹之后再恢复
        FireworkApi.getInstance().setLocalEnableShow(false);

        containViewInflate();

        HandlerManager.postOnUIThreadDelay(() -> initPlayBar(), ToolUtil.isFirstInstallApp(this) ? 1500 : 0);

        XmAppStartManager.onInitDoSomething(this);

        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onInitDoSomething end");

        updateManager = new UpdateManager(this);
        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onCreate begin11");

        if (BaseUtil.getLargeMemoryClass(MainActivity.this) >= 96) {
            isLowMemoryDevice = false;
        }

        //TODO IM系统更新
        //NoReadManage.getInstance(this).addNoReadUpdateListenerListener(this);
//        this.startActivity(new Intent(MainActivity.this, LoginActivity.class));
        sp = SharedPreferencesUtil.getInstance(MainActivity.this);

        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onCreate begin33");

        int height = EmotionUtil.getKeyboardHeight(getApplicationContext());
        if (height > 0)
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        judgeHasIting(getIntent());

        if (ToolUtil.isFirstInstallApp(this)) {
            if (getIntent() != null) {
                getIntent().putExtra(BundleKeyConstants.KEY_IS_HANDLED_INTENT, true);
                doSomethingByIntent(getIntent());
            }
            RecommendAlbumCardBeforePrivacyAgreeManager.INSTANCE.doActionWhileMainActivityStart();
        } else if (!mFromSkipPush && !mFromItingDirectLanding && !mFromPushIgnoreHome) {
            mOnNewIntent = getIntent();
            if (splashIsDestroyed) {
                Logger.e("pushTag", "doOnCreate doSomethingByIntent");
                doSomethingByIntent(mOnNewIntent);
                mOnNewIntent = null;
            }
        }

        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = postAppStartTime");
        mStartTime = System.currentTimeMillis() - MainApplication.sApplication_start_time;

        //修改为兴趣卡片必弹的方案，取消随机弹框方案
        int plan = 2;
        TempDataManager.getInstance().saveInt("FRESH_GUIDE_PLAN", plan);

        initUi();

        if (ToolUtil.isFirstInstallApp(this) && SystemUtil.isHonorDevice() &&
                (getIntent() == null || getIntent().getData() == null ||
                        !getIntent().getData().toString().startsWith("iting://") ||
                        !getIntent().getData().toString().contains("pushSource=honorPush"))) {
            // 没有荣耀消息 打开荣耀消息盒子页面
            HandlerManager.postOnUIThreadDelay(() -> PushCommonUtil.getHonorMessagingData(MainActivity.this), 8000);
        }

        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = initUi");

        initLoginStatusListener();

        getWindow().setBackgroundDrawable(null);

        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onCreate begin44");

        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                SearchManager.addLoginStatusChangeListener();
                SearchManager.getRecommendAlbumIds(getApplicationContext());

                try {
                    CommonRequestM.bindAppForQQ(MainActivity.this);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                WiFiDeviceController.initBluetooth(getApplicationContext());

                DriveModeBluetoothManager.getInstance().init();
                SmartDeviceBluetoothManager.getInstance().init();
                ChatXmlyPopupManager.INSTANCE.init();
            }
        });

        HintFreeFlowDialog.haveShowing = false;

        AlbumEventManage.mOnCollectByUser = AdManager.onCollectByUser;

        postAlbumSubscribeOfUnlogin();
        TempDataManager.getInstance().saveObject(TEMP_DATA_MAIN_TIPS, new TempDataManager.IDataCallBack() {
            @Override
            public Object run() {
                if (canUpdateUi()) {
                    return new CustomTipsView(MainActivity.this);
                }
                return null;
            }
        });

        TimeLimitManager.getInstance().init();

        DownloadTools.lastNetType = NetworkType.getNetWorkType(this);

        PlayAutoUnlockTrackActionManager.getInstance().init(getApplicationContext());

        PlayAutoBuyTrackActionManager.getInstance().init(getApplicationContext());

        doAfterOnCreate = true;

        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onCreate begin55");

        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onCreate begin66");
        /**
         * 先清除儿童模式标记，然后在儿童模式activity里重新设置标记，避免因用户强杀app或者app在后台被系统回收
         * 造成的播放进程模式判断错误(播放进程播儿童模式里的专辑需验证儿童版会员权益)
         *
         * 在Activity.onCreate()的模式标记设置间隙依然存在播放进程模式判断错误的可能，最好通过playSource判断规避逻辑缺陷。
         */
        KidsHelper.clearModeFlag(getContext());

        if (mStartSplashAdHelper != null) {
            mStartSplashAdHelper.onMainActivityCreateRegisterActivityLife(getApplication());
        }

        KidModeManager.INSTANCE.go2KidModeIfNeed(mHasSchemeHandled || PushCommonUtil.pushFromIntent(getIntent()));
        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onCreate begin77");
        // 热修复生效埋点上报
        // PatchToken 不为空且不等于patch_default
        Logger.d("patch_token", "patch_token " + PatchToken.mHostPatchCheckIfValidData);
        if (!TextUtils.isEmpty(PatchToken.mHostPatchCheckIfValidData) && !"patch_default".equals(PatchToken.mHostPatchCheckIfValidData)) {
            Logger.d("patch_token", "patch valid");
            String patch_str = MmkvCommonUtil.getInstance(this).getString(AppConstants.KEY_PATCH_REPORTED_STR, null);
            if (!PatchToken.mHostPatchCheckIfValidData.equals(patch_str)) {
                Logger.d("patch_token", "patch has not reported");
                new XMTraceApi.Trace()
                        .setMetaId(27747)
                        .setServiceId("hotUpdate")
                        .put("token", PatchToken.mHostPatchCheckIfValidData)
                        .createTrace();
                MmkvCommonUtil.getInstance(this).saveString(AppConstants.KEY_PATCH_REPORTED_STR, PatchToken.mHostPatchCheckIfValidData);
            }
        }

        if (MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getBoolean(
                PreferenceConstantsInHost.KEY_FREE_LISTEN_OLD_USER_INVITATION, false)) {
            FreeListenDialogTrace.traceOldUserEvent("open_app");
        }
        FreeListenTimeDialogManager.addShowFreeListenDialogAction();

        checkShowDialogBizRoot();

        WebViewPool.getInstance().proCreate();

        if (ToListenManager.INSTANCE.shouldShowToListenNew()) {
            ToListenManager.INSTANCE.getTrackListBySync(null);
        }
        Logger.i(TAG, "app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onCreate finish");

        if (!HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()) {
            //未命中首页加载优化
            startWatchingExternalStorage();
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().tryToGetUserMission();
            } catch (Exception e) {
                e.printStackTrace();
            }
            DailyNewsLogicManager.INSTANCE.registerAlarmReceiver(this);
        }


        ClaCUserUtils.update();
    }

    private void checkShowDialogBizRoot() {
        RecommendPreLoadOptManager.INSTANCE.log("_____doOnCreate____checkShowLotteryDlg");
        checkShowLotteryDlg();
        checkShowInterestCardByRecallUser();
    }

    private void executeOnceAfterHomeShow() {
        if (executeOnceAfterHomeShow) {
            return;
        }
        executeOnceAfterHomeShow = true;
        startWatchingExternalStorage();
        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().tryToGetUserMission();
        } catch (Exception e) {
            e.printStackTrace();
        }
        DailyNewsLogicManager.INSTANCE.registerAlarmReceiver(this);
    }

    private void checkShowLotteryDlg() {
        Router.getActionByCallback(Configure.BUNDLE_MAIN, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                NewUserActiveManager.INSTANCE.checkNeedShowLotteryDlg(new NewUserActiveManager.OnCheckCallBack() {
                    @Override
                    public void onCheckCallBack(boolean needShow) {
//                        if (needShow && canUpdateUi()) {
//                            try {
//                                BaseDialogFragment newUserLotteryDlgFragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newNewUserLotteryDlgFragment();
//                                newUserLotteryDlgFragment.show(getSupportFragmentManager(), "NewUserLotteryDlgFragment");
//                            } catch (Exception e) {
//                                e.printStackTrace();
//                            }
//                        }
                    }
                });
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
            }
        });
    }

    private void checkShowInterestCardByRecallUser() {
        if (!ToolUtil.isFirstInstallApp(this)) {
            //首次安装的时候已经有别的逻辑弹兴趣卡片，所以召回用户只需要在这处理非首次安装情况
            int day = ConfigureCenter.getInstance().getInt("toc", "personal_queryshow_request_duration", 28);
            long lastRequestTime = MMKVUtil.getInstance().getLong(PreferenceConstantsInHost.KEY_RECALL_USER_REQUEST_TIME, -1L);
            if (System.currentTimeMillis() - lastRequestTime > day * 24 * 60 * 60 * 1000L) {
                Router.getActionByCallback(Configure.BUNDLE_MAIN, new Router.IBundleInstallCallback() {
                    @Override
                    public void onInstallSuccess(BundleModel bundleModel) {
                        try {
                            requestIsInterestCardOfHomepage(true);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    }

                    @Override
                    public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                    }
                });
            }
        }
    }

    private void judgeHasIting(Intent intent) {
        if (intent != null && intent.getData() != null && "iting".equals(intent.getData().getScheme())) {
            mHasIting = true;
        }
    }

    public void showStartDialogs() {
        RecommendPreLoadOptManager.INSTANCE.log("——————showStartDialogs");
        // 如果ab到新用户兴趣卡片优先弹出，则取消一次弹窗,避免弹窗重叠
        if (needCancelOtherDlgOnce) {
            needCancelOtherDlgOnce = false;
            return;
        }

        if (!CheckLoginUtils.isAllowShowDialog()) {
            CheckLoginUtils.needCheckShowStartDialogs();
            return;
        }
        CheckLoginUtils.showStartDialogs();

        Logger.i("MainActivity", "展示启动弹窗");
        if (!ElderlyModeManager.getInstance().isElderlyMode()
                && (needTryToShowInterestCardPage() || needTryToShowInterestCardNewPage())) {
            Logger.i("MainActivity", "新手引导弹窗");
            HandlerManager.obtainMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (!RecommendAlbumCardBeforePrivacyAgreeManager.INSTANCE.hasShowRecommendAlbumCardPage()) {
                        requestIsInterestCardOfHomepage(false);
                    } else {
                        VersionUtil.releaseRecordVersion();
                    }
                }
            }, 500);
            // 兴趣卡片如果不出的话，可能会出青少年模式弹窗，这里就只去设置允许统一弹屏，不去触发统一弹屏
            FireworkApi.getInstance().setLocalEnableShow(true);
        } else {
            showSubscribeGuideDialog();
        }
    }

    private void onChildProtectDialogNotShow() {
        // 设置允许统一弹屏弹出
        FireworkApi.getInstance().setLocalEnableShow(true);
        // 触发一下首页和推荐页的统一弹屏，如果还在首页的话
        if (tabFragmentManager != null && tabFragmentManager.getCurrFragment() instanceof IMainFunctionAction.AbstractHomePageFragment) {
            Fragment fragment = tabFragmentManager.getCurrFragment();
            Fragment curTabFragment = ((IMainFunctionAction.AbstractHomePageFragment) fragment).getCurTabFragment();
            FireworkAgent.onFragmentResume(fragment);
            if (curTabFragment != null) {
                FireworkAgent.onFragmentResume(curTabFragment);
            }
        }
    }

    private boolean needTryToShowInterestCardPage() {
        // 已填写过，不需要弹
        boolean hasCustomized = SharedPreferencesUtil.getInstance(this).getBoolean
                (PreferenceConstantsInHost.KEY_HAS_CUSTOMIZED, false);
        // 当请求成功，且服务端返回不需要弹，或者弹出成功后，把这个值存为false。
        boolean needShow = SharedPreferencesUtil.getInstance(this).getBoolean(
                PreferenceConstantsInHost.KEY_NEED_SHOW_CUSTOMIZED_PAGE_ON_APP_START, true);
        return needShow && !hasCustomized;
    }

    private boolean needTryToShowInterestCardNewPage() {
        // 已填写过，不需要弹
        boolean hasChosenCategory = MmkvCommonUtil.getInstance(this).getBooleanCompat(
                PreferenceConstantsInHost.KEY_HAS_CHOSEN_CATEGORY, false);
        boolean needShow = MmkvCommonUtil.getInstance(this).getBooleanCompat(
                PreferenceConstantsInHost.KEY_NEED_SHOW_CHOOSE_LIKE_PAGE_ON_APP_START, true);
        return needShow && !hasChosenCategory;
    }

    private void postDelayUiTask() {
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
//                if (((RadioButton)MainActivity.this.findViewById(R.id.tab_home_page)).getText().equals("首页")){
//
//                    ((RadioButton)MainActivity.this.findViewById(R.id.tab_home_page)).setText("不知道");
//                } else {
//                    ((RadioButton)MainActivity.this.findViewById(R.id.tab_home_page)).setText("首页");
//                }
//                postDelayUiTask();
            }
        }, 1000);
    }

    private void requestIsInterestCardOfHomepage(boolean isShowByCheckRecallUser) {
        if (ElderlyModeManager.getInstance().nowFromIting()) {
            return;
        }
        IDataCallBack<InterestCardSwitchInfo> dataCallBack = new IDataCallBack<InterestCardSwitchInfo>() {
            @Override
            public void onSuccess(@Nullable InterestCardSwitchInfo object) {
                if (isDestroyed) {
                    return;
                }
                if (object == null) {
                    onCustomPageDismissed();
                    tryToShowChildProtectDialog();
                    return;
                }
                MMKVUtil.getInstance().saveLong(PreferenceConstantsInHost.KEY_RECALL_USER_REQUEST_TIME, System.currentTimeMillis());
                if (isShowByCheckRecallUser) {
                    if (NewUserActiveManager.SCENETYPE_RECALLUSER.equals(object.sceneType) || NewUserActiveManager.SCENETYPE_RECALL_VIP_USER.equals(object.sceneType)) {
                        //召回用户要强制弹出兴趣卡片和限免弹窗
                        needCancelOtherDlgOnce = true;
                        MmkvCommonUtil.getInstance(MainActivity.this).saveBoolean(PreferenceConstantsInHost.KEY_RECALL_USER_FORCE_SHOW, true);
                        //召回用户如果有iting则不出兴趣卡片
                        if (isHandleIting || mHasIting) {
                            return;
                        }
                    } else {
                        return;
                    }
                }
                //新用户/召回用户没有iting，根据ab跳转去播放卡片
                if ((ToolUtil.isFirstInstallApp(MainActivity.this) || NewUserActiveManager.SCENETYPE_RECALLUSER.equals(object.sceneType)) && !isHandleIting && !mHasIting) {
                    if (object.uiType == 0 && object.playCardPage) {
                        try {
                            BaseFragment2 fra = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newRecommendAlbumCardFragment("", object.playCardPageStyle, true);
                            startFragment(fra);
                            // 新用户或者第一次安装的召回用户根据需要出限免弹窗, 28天后的召回用户限免弹窗在上面的逻辑已经处理
                            if (ToolUtil.isFirstInstallApp(MainActivity.this)) {
                                MmkvCommonUtil.getInstance(MainActivity.this).saveBoolean(PreferenceConstantsInHost.KEY_RECALL_USER_FORCE_SHOW, true);
                            }
                        } catch (Exception e) {
                            onCustomPageDismissed();
                        }
                        return;
                    }
                }

                gotoInterestCardByNative(object);
            }

            @Override
            public void onError(int code, String message) {
                tryToShowChildProtectDialog();
            }
        };
        // 首次启动后到这里，已经变成登录状态了，清掉前面请求的配置，重新请求
        if (ToolUtil.isFirstInstallApp(this) && UserInfoMannage.hasLogined()) {
            InterestCardSwitchInfoManager.getInstance().clearData();
        }
        //逻辑：请求兴趣卡片接口或者有iting进来的，后续客户端存本地27天标记。不再调
        long lastRequestTime = MMKVUtil.getInstance().getLong(PreferenceConstantsInHost.KEY_RECALL_USER_REQUEST_TIME, -1L);
        if (System.currentTimeMillis() - lastRequestTime > 27 * 24 * 60 * 60 * 1000L) {
            boolean hasIting = isHandleIting || mHasIting;
            InterestCardSwitchInfoManager.getInstance().loadData(hasIting, 0, dataCallBack);
        } else {
            Logger.i("FreeListenTimeDialogManager", "onCustomPageDismissed");
            FreeListenTimeDialogManager.notifyFreeListenDialogAction();
        }
    }

    private void gotoInterestCardByNative(InterestCardSwitchInfo object) {
        boolean showCustomizeFraForAppStart = object.uiType == 1 || object.uiType == 3
                || (object.uiType == 2 && !object.fullScreenStyle);
        boolean showChooseLikeFraForAppStart = object.uiType == 2 && object.fullScreenStyle;
        if (!showCustomizeFraForAppStart) {
            SharedPreferencesUtil.getInstance(MainActivity.this).saveBoolean(
                    PreferenceConstantsInHost.KEY_NEED_SHOW_CUSTOMIZED_PAGE_ON_APP_START, false);
        }
        if (!showChooseLikeFraForAppStart) {
            MmkvCommonUtil.getInstance(MainActivity.this).saveBoolean(
                    PreferenceConstantsInHost.KEY_NEED_SHOW_CHOOSE_LIKE_PAGE_ON_APP_START, false);
        }
        if (!showCustomizeFraForAppStart && !showChooseLikeFraForAppStart) {
            onCustomPageDismissed();
            tryToShowChildProtectDialog();
            return;
        }
        mIsInterestCardShowing = true;
        boolean finalShowCustomizeFraForAppStart = showCustomizeFraForAppStart && needTryToShowInterestCardPage();

        try {
            BaseFragment2 fra;
            if (finalShowCustomizeFraForAppStart) {
                fra = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newCustomizeFragmentForAppStart(object);
            } else {
                fra = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction()
                        .newChooseLikeFragmentNewForAppStart(object.playCardPage,
                                object.halfScreenGenderAge, object.skipShowPlayCard);
            }
            if (fra != null) {
                fra.setCallbackFinish(new IFragmentFinish() {
                    @Override
                    public void onFinishCallback(Class<?> cls, int fid, Object... params) {
                        boolean needReload = params != null && params.length > 0
                                && (params[0] instanceof Boolean) && (boolean) params[0];
                        if (needReload && finalShowCustomizeFraForAppStart) {
                            if (getTabFragmentManager() != null && getTabFragmentManager().getCurrFragment()
                                    instanceof IMainFunctionAction.AbstractHomePageFragment) {
                                IMainFunctionAction.AbstractHomePageFragment homePageFragment
                                        = (IMainFunctionAction.AbstractHomePageFragment) getTabFragmentManager().getCurrFragment();
                                homePageFragment.reloadTabData(true);
                                homePageFragment.notifyCustomizeDialogFinish();
                            }
                        }
                        if (params == null || (needReload && !object.playCardPage)) {
                            onCustomPageDismissed();
                        }
                    }
                });
                if (finalShowCustomizeFraForAppStart && object.fullScreenStyle) {
                    startFragment(fra, R.anim.host_slide_in_bottom, R.anim.host_slide_out_bottom);
                } else if (finalShowCustomizeFraForAppStart) {
                    showHalfScreenCustomizeFra(fra);
                } else {
                    startFragment(fra, R.anim.host_slide_in_bottom, R.anim.host_slide_out_bottom);
                }
            } else {
                onCustomPageDismissed();
            }
        } catch (Exception e) {
            onCustomPageDismissed();
        }
    }

    public void showHalfScreenCustomizeFra(BaseFragment2 fra) {
        // 半屏样式为了能看到底下的界面，不能走ManageFragment的逻辑
        addFragment(R.id.top_fragment_container, fra);
        showFragment(fra);
    }

    private void showSubscribeGuideDialog() {
        RecommendPreLoadOptManager.INSTANCE.log("——————showSubscribeGuideDialogs");
        if (ViewUtil.isSplashAdShowing() || ViewUtil.isSplashAdBgShowing()) {
            showOtherDialogs();
        } else {
            Logger.i("feiwen", "订阅引导弹窗 ");
            HandlerManager.obtainMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (!MyListenGuideUtil.INSTANCE.checkShowSubscribeEx2Guide()) {
                        showOtherDialogs();
                    }
                }
            }, 500);
        }
    }

    private void showOtherDialogs() {
        RecommendPreLoadOptManager.INSTANCE.log("——————showOtherDialogs");
        if (ViewUtil.isSplashAdShowing() || ViewUtil.isSplashAdBgShowing()) {
            nextNeedCheckShowOtherDialogs = true;
        } else {
            Logger.i("feiwen", "非新手引导的其他弹窗 ");
            HandlerManager.obtainMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    onCustomPageDismissed();
                    // 召回用户相关的弹窗要优先弹出
                    RecallUserManager.INSTANCE.checkNeedShowRecallDlg(new RecallUserManager.OnCheckCallBack() {
                        @Override
                        public void onCheckCallBack(boolean needShowRecallDlg) {
                            if (!needShowRecallDlg &&
                                    !FreeListenTimeDialogManager.checkFreeListenDialogEnable("NotificationPermission")) {
                                showOpenNotificationPermissionDialog();
                            }
                        }

                        @Override
                        public void afterRecallDlg() {
                            if (!FreeListenTimeDialogManager.checkFreeListenDialogEnable("NotificationPermission")) {
                                showOpenNotificationPermissionDialog();
                            }
                        }
                    });
                    showOtherDialogsExceptOpenNotificationPermissionDialog();
                }
            }, 500);
        }
    }

    /**
     * 最多循环4次判断mHasAbTestObtained是否拿到值了
     */
    private void checkToShowOpenNotificationPermissionDialog(int count, IDataCallBack<Boolean> callBack) {
        Logger.i("feiwen", "非新手引导的其他弹窗 第" + count + "次 mHasAbTestObtained = " + mHasAbTestObtained);
        if (mHasAbTestObtained || count >= 3) {
            if (callBack != null) {
                callBack.onSuccess(true);
            }
            return;
        }
        HandlerManager.obtainMainHandler().postDelayed(() ->
                checkToShowOpenNotificationPermissionDialog(count + 1, callBack), 500);
    }

    private void showOtherDialogsExceptOpenNotificationPermissionDialog() {
        HandlerManager.obtainMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                // 如果消息通知权限弹窗弹出，就不弹其他弹窗
                RecommendPreLoadOptManager.INSTANCE.log("ConfigFetch____tryToShowChildDialog");
                if (ViewUtil.haveDialogIsShowing(MainActivity.this)) {
                    return;
                }
                if (!isHandleIting) {
                    // 优先保证出青少年模式弹窗 本次不显示青少年弹窗的时候才出其他的
                    ConfigureCenter.getInstance().registerConfigFetchCallback(new IConfigureCenter.ConfigFetchCallback() {
                        @Override
                        public void onUpdateSuccess() {
                            ConfigureCenter.getInstance().unRegisterConfigFetchCallback(this);
                            showDialogLogicAfterConfigureCenter();
                        }

                        @Override
                        public void onRequestError() {
                            ConfigureCenter.getInstance().unRegisterConfigFetchCallback(this);
                            showDialogLogicAfterConfigureCenter();
                        }
                    });
                } else {
                    onChildProtectDialogNotShow();
                    checkToShowNewTabGuideView();
                    checkToShowQuickListenGuideView();
                }
            }
        }, 200);
    }

    private void checkToShowNewTabGuideView() {
        if (ViewUtil.haveDialogIsShowing(MainActivity.this)) {
            return;
        }
        if (tabFragmentManager != null && tabFragmentManager.getCurrFragment() instanceof IMainFunctionAction.AbstractHomePageFragment) {
            IMainFunctionAction.AbstractHomePageFragment fragment = (IMainFunctionAction.AbstractHomePageFragment) tabFragmentManager.getCurrFragment();
            fragment.checkToShowNewTabGuideView();
        }
    }

    private void showDialogLogicAfterConfigureCenter() {
        HandlerManager.postOnUIThread(() -> {
            try {
                showChildProtectDialog(() -> {
                    showImportantUpdatePage();
                    // 服务端目前依赖这个接口做升级的统计，尽量保证这个会走
                    updateManager.checkUpdata(null, true);
                    onChildProtectDialogNotShow();
                    checkToShowNewTabGuideView();
                    checkToShowQuickListenGuideView();
                });
            } catch (Exception e) {
                // 临时添加try catch，下版本放主线程
            }
        });
    }
    private void checkToShowQuickListenGuideView(){
        if (QuickListenTabAbManager.showAIListenTab() && mRbQuickListen != null) {
            mRbQuickListen.postDelayed(new Runnable() {
                @Override
                public void run() {
                    HomePageQuickListenGuideUtil.INSTANCE.checkShowGuide(MainActivity.this, mRbQuickListen);
                }
            }, 50);
        }
    }

    private void notifyShowInterestTagDialog() {
        EventManager.getInstance().notifyEvent(new EventManager.Event(
                EventManager.EVENT_SHOW_INTEREST_TAG_DIALOG));
    }

    public void tryToShowChildProtectDialog() {
        showChildProtectDialog(null, false);
    }

    private void showChildProtectDialog(IChildProtectDialogShowCallBack callBack) {
        showChildProtectDialog(callBack, true);
    }

    private void callShowOtherDialog(@Nullable IChildProtectDialogShowCallBack callBack) {
        if (callBack != null) {
            callBack.canShowOtherDialog();
        }
    }

    private void showChildProtectDialog(@Nullable IChildProtectDialogShowCallBack callBack, boolean needNotifyChildProtectDialogNotShow) {
        RecommendPreLoadOptManager.INSTANCE.log("____showChildProtectDialog");
        if (ElderlyModeManager.getInstance().handleOpenDialog()) {
            // 大字模式弹窗显示成功了 就不显示其他弹窗了
            if (needNotifyChildProtectDialogNotShow) {
                onChildProtectDialogNotShow();
            }
            return;
        }

        if (ElderlyModeManager.getInstance().hasShowElderlyDialogToday()) {
            // 大字模式弹窗今天显示过了 当天不出青少年弹窗 显示其他弹窗
            callShowOtherDialog(callBack);
            return;
        }

        if (ViewUtil.haveDialogIsShowing(this)) {
            RecommendPreLoadOptManager.INSTANCE.log("____showChildProtectDialog_____haveDialogIsShowing");
            callShowOtherDialog(callBack);
            return;
        }

        if (TempDataManager.getInstance().getBoolean("start_dialog_shown")) {
            callShowOtherDialog(callBack);
            return;
        }

        if (ChildProtectManager.isChildMode(this)) {
            callShowOtherDialog(callBack);
            return;
        }

        if (ElderlyModeManager.getInstance().isElderlyMode()) {
            callShowOtherDialog(callBack);
            return;
        }

        if (RecallUserRightsManager.INSTANCE.checkCanShow()) {
            RecallUserRightsManager.INSTANCE.requestRecallUserRightsData(new IDataCallBack<RecallUserRightsData>() {
                @Override
                public void onSuccess(@Nullable RecallUserRightsData data) {
                    if (ViewUtil.haveDialogIsShowing(MainActivity.this) ||
                            TempDataManager.getInstance().getBoolean("start_dialog_shown")) {
                        showGuideSubscription(callBack);
                        return;
                    }
                    if (data == null || TextUtils.isEmpty(data.getGiftId())
                            || TextUtils.isEmpty(data.getStyle()) || data.getStyleData() == null) {
                        showGuideSubscription(callBack);
                        return;
                    }
                    RecallUserRightsManager.INSTANCE.showDialog(data);
                }

                @Override
                public void onError(int code, String message) {
                    showGuideSubscription(callBack);
                }
            });
            return;
        }
        showGuideSubscription(callBack);
    }

    private void showGuideSubscription(@Nullable IChildProtectDialogShowCallBack callBack) {
        showInterestTagDialog(callBack);
    }

    private void showInterestTagDialog(@Nullable IChildProtectDialogShowCallBack callBack) {
        if (mHasIting || ViewUtil.haveDialogIsShowing(MainActivity.this)
                || RecommendFragmentAbManager.INSTANCE.getUseNewRecommendFragment()) {
            showHighPropertyFirework(callBack);
            return;
        }
        long lastShowTime = MMKVUtil.getInstance().getLong(
                PreferenceConstantsInHost.KEY_INTEREST_TAG_GUIDE_DIALOG_LAST_SHOW_TIME, 0);
        Map<String, String> params = new HashMap<>();
        params.put("lastShow", lastShowTime + "");
        CommonRequestM.getInterestTagGuide(params, new IDataCallBack<InterestTagPageModel>() {
            @Override
            public void onSuccess(@Nullable InterestTagPageModel data) {
                if (!canUpdateUi() || ViewUtil.haveDialogIsShowing(MainActivity.this)
                        || data == null || !data.getShowGuide() || ToolUtil.isEmptyCollects(data.getTagList())
                        || (lastShowTime != 0 && (System.currentTimeMillis() - lastShowTime) < data.getDaysInterval() * 24 * 3600 * 1000)
                        || RecommendFragmentAbManager.INSTANCE.getUseNewRecommendFragment()) {
                    showHighPropertyFirework(callBack);
                    return;
                }
                InterestTagManager.INSTANCE.setInterestTagPageModel(data);
                notifyShowInterestTagDialog();
            }

            @Override
            public void onError(int code, String message) {
                showHighPropertyFirework(callBack);
            }
        });
    }

    private ChildProtectFireworkInterceptor mChildProtectFireworkInterceptor;

    private void showHighPropertyFirework(IChildProtectDialogShowCallBack callBack) {
        if (tabFragmentManager != null && tabFragmentManager.getCurrFragment() instanceof IMainFunctionAction.AbstractHomePageFragment) {
            Fragment fragment = tabFragmentManager.getCurrFragment();
            Fragment curTabFragment = ((IMainFunctionAction.AbstractHomePageFragment) fragment).getCurTabFragment();
            if (curTabFragment != null && curTabFragment.isResumed() && curTabFragment.getUserVisibleHint() && Util.isParentFraVisible(curTabFragment)) {
                FireworkApi.getInstance().setLocalEnableShow(true);
                mChildProtectFireworkInterceptor = new ChildProtectFireworkInterceptor() {
                    @Override
                    public void show() {
                        FireworkDebugger.log("青少年弹窗, showHighPropertyFirework show");
                        ViewUtil.setHasDialogShow(false);

                        //需要合并展示的弹窗信息
                        Firework toMergeFirework = null;
                        if (getFirework() != null) {
                            toMergeFirework = getFirework().toMergeFirework;
                        }
                        ChildProtectDialogManager.INSTANCE.showDialog(toMergeFirework, isShown -> {
                            if (!isShown) {
                                callShowOtherDialog(callBack);
                            }
                        });
                    }

                    @Override
                    public boolean shouldInterceptShow(int planId, int fireworkId) {
                        if (ViewUtil.haveDialogIsShowing(MainActivity.this) ||
                                TempDataManager.getInstance().getBoolean("start_dialog_shown")) {
//                            callShowOtherDialog(callBack);
                            FireworkDebugger.log("青少年弹窗, showHighPropertyFirework shouldInterceptShow return s1");
                            return false;
                        }
                        FireworkDebugger.log("青少年弹窗, showHighPropertyFirework checkShowDialog");
                        return ChildProtectDialogManager.INSTANCE.checkShowDialog(ChildProtectDialogManager.Position.HOMEPAGE);
                    }
                };
                FireworkApi.getInstance().addFireworkShowingInterceptor(mChildProtectFireworkInterceptor);
                IHighPropertyFireworkCallback fireworkCallback = new IHighPropertyFireworkCallback() {
                    private boolean executed = false;

                    @Override
                    public void onFireworkShowing() {
                        FireworkDebugger.log("青少年弹窗, onFireworkShowing, executed: " + executed);
                        if (!executed) {
                            executed = true;
                            callShowOtherDialog(callBack);
                        }
                    }

                    @Override
                    public void onFireworkError() {
                        FireworkDebugger.log("青少年弹窗, onFireworkError, executed: " + executed);

                        if (!executed) {
                            executed = true;
                            showChildProtectView(callBack);
                        }
                    }
                };
                FireworkApi.getInstance().showHighPropertyFirework(curTabFragment, fireworkCallback);
                int delay = ConfigureCenter.getInstance().getInt(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.KEY_HIGH_PROPERTY_FIREWORK_DELAY, 15000);
                if (!ConstantsOpenSdk.isDebug) {
                    AppUtils.runOnUiThreadDelayed(() -> fireworkCallback.onFireworkError(), delay);
                }
                return;
            }
        }
        showChildProtectView(callBack);
    }

    private void showChildProtectView(@Nullable IChildProtectDialogShowCallBack callBack) {
        if (ViewUtil.haveDialogIsShowing(MainActivity.this) ||
                TempDataManager.getInstance().getBoolean("start_dialog_shown")) {
            FireworkDebugger.log("青少年弹窗, showChildProtectView callShowOtherDialog");
            callShowOtherDialog(callBack);
            return;
        }
        if (ChildProtectDialogManager.INSTANCE.checkShowDialog(ChildProtectDialogManager.Position.HOMEPAGE)) {
            FireworkDebugger.log("青少年弹窗, showChildProtectView checkShowDialog return true");
            ChildProtectDialogManager.INSTANCE.showDialog(null, isShown -> {
                if (!isShown) {
                    callShowOtherDialog(callBack);
                }
            });
        } else {
            FireworkDebugger.log("青少年弹窗, showChildProtectView checkShowDialog return false");
            callShowOtherDialog(callBack);
        }
    }

    /**
     * 当天第几次启动app 用于青少年模式弹窗需求
     */
    public static int getTodayStartAppTimes() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy:MM:dd", Locale.getDefault());
        String curTime = sdf.format(System.currentTimeMillis());
        String lastDate = MMKVUtil.getInstance().getString(PreferenceConstantsInHost.KEY_START_APP_TIMES_TODAY_DATE);
        MMKVUtil.getInstance().saveString(PreferenceConstantsInHost.KEY_START_APP_TIMES_TODAY_DATE, curTime);
        if (!TextUtils.isEmpty(curTime) && !TextUtils.isEmpty(lastDate) && lastDate.equals(curTime)) {
            int lastCount = MMKVUtil.getInstance().getInt(PreferenceConstantsInHost.KEY_START_APP_TIMES_TODAY_COUNT, 1);
            lastCount++;
            MMKVUtil.getInstance().saveInt(PreferenceConstantsInHost.KEY_START_APP_TIMES_TODAY_COUNT, lastCount);
            return lastCount;
        }
        return 1;
    }

    private void onCustomPageDismissed() {
        Logger.i("FreeListenTimeDialogManager", "onCustomPageDismissed");
        FreeListenTimeDialogManager.notifyFreeListenDialogAction();
        EventManager.Event event = new EventManager.Event("customize_page_destroy");
        event.sticky = true;
        EventManager.getInstance().notifyEvent(event);
        mIsInterestCardShowing = false;
    }

    public boolean isInterestCardShowing() {
        return mIsInterestCardShowing;
    }

    public static boolean hasSendStaticAppStartTime = false;
    /**
     * 22.3.11 流量优化 xdcs上报启动速度暂时关闭，目前在用apm采集
     *
     * @see CConstants.Group_android.ITEM_XDCS_UPLOAD_START_UP 开关keep 三个迭代过后没问题可剔除逻辑
     */
    public static boolean needUploadAppStart = false;

    private void postAppStartTime() {
        if (ConstantsOpenSdk.isDebug || !needUploadAppStart) return;
        if (!hasSendStaticAppStartTime) {
            hasSendStaticAppStartTime = true;
            if (0 < mStartTime && mStartTime < 10000) {
                List<XdcsEvent> eventList = new ArrayList<>();
                XdcsEvent errorEvent = new XdcsEvent();
                Map<String, String> propMap = new HashMap<>();
                propMap.put("app_start_time", mStartTime + "");

                propMap.put("is_first_start", ToolUtil.isFirstInstallApp(this) + "");
                propMap.put("system_version", android.os.Build.VERSION.SDK_INT + "");
                propMap.put("device_info", android.os.Build.MANUFACTURER);

                errorEvent.props = propMap;
                errorEvent.setType("APPSTARTTIME");
                errorEvent.setTs(System.currentTimeMillis());
                eventList.add(errorEvent);

                IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
                if (xdcsPost != null) {
                    xdcsPost.postError(XdcsRecord.createXdcsRecord(eventList));
                }
            }
        }
    }

    private void initUi() {
        ListenAchievementManager.init();
        BottomTabFragmentManager.INSTANCE.init();
        initVipAttachButtonTabAndNotificationAbTest();
//        mBtnTop = findViewById(R.id.btn_top);
//        mBtnTop.setOnClickListener(this);
//        AutoTraceHelper.bindData(mBtnTop, "");

        mRadioGroup = findViewById(R.id.rg_tabs);
        if (mRadioGroup != null) {
            mRadioGroup.post(new Runnable() {
                @Override
                public void run() {
                    ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) mRadioGroup.getLayoutParams();
                    layoutParams.height = (mRadioGroup.getHeight() > 0) ? mRadioGroup.getHeight()
                            : BaseUtil.dp2px(MainActivity.this, 63);
                    Logger.i(AutoSizeLog.TAG, "bottom size1111: " + mRadioGroup.getHeight());
                    mRadioGroup.post(() -> PlayBarAbManager.INSTANCE.setMainBottomTabHeight(mRadioGroup.getHeight()));
                }
            });
            mRadioGroup.setOnCheckedChangeListener(this);
            mRbHomePage = mRadioGroup.findViewById(R.id.tab_home_page);
            mRbFinding = mRadioGroup.findViewById(R.id.tab_finding);
            mRbFinding.setCompoundDrawablesWithIntrinsicBounds(0, DyncAbTestUtil.isFeed2Subscribe() ?
                    R.drawable.host_find_tab_02 : R.drawable.host_find_tab_01, 0, 0);
            vsDyncTip = findViewById(R.id.host_dync_tip_vs);
            if (!HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()) {
                DynamicTabRedHintManager.INSTANCE.initView(mRbFinding, vsDyncTip);
            }
            mRbPlay = mRadioGroup.findViewById(R.id.play);
            ViewStatusUtil.setVisible(PlayBarAbManager.INSTANCE.useNewPlayBar() ? View.GONE : View.VISIBLE, mRbPlay);
            mRbCategory = mRadioGroup.findViewById(R.id.tab_category);
            mRbVip = mRadioGroup.findViewById(R.id.tab_vip);
            mRbMineAndListen = mRadioGroup.findViewById(R.id.tab_myspace_and_listen);
            mRbMineAndListen.setVisibility(View.VISIBLE);
            if (NovelTabAbManager.INSTANCE.showNovelTab()) {
                mRbVip.setText("免费");
                setTabTextColor(mRbVip, R.color.host_theme_bottom_tab_novel_text_selector);
                if (!hasBottomAtmophere) {
                    mRbVip.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.host_novel_tab_01, 0, 0);
                }
            } else if (NovelTabAbManager.showCategoryV2Tab()) {
                mRbVip.setText("分类");
                if (!hasBottomAtmophere) {
                    mRbVip.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.host_category_v2_tab_icon, 0, 0);
                }
            }
            mRbVip.setVisibility(View.VISIBLE);

            if (QuickListenTabAbManager.showAIListenTab()) {
                mRbQuickListen = mRbVip;
            }
            if (MyListenAbUtil.INSTANCE.isKeepSubscribeInMine()) {
                if (QuickListenTabAbManager.showAIListenTab()) {
                    // 首页、快听、会员、我的
                    mRadioGroup.removeView(mRbCategory);
                    mRadioGroup.addView(mRbCategory, mRadioGroup.indexOfChild(mRbPlay));

                    mRadioGroup.removeView(mRbVip);
                    mRadioGroup.addView(mRbVip, mRadioGroup.indexOfChild(mRbPlay) + 1);

                    mRbQuickListen = mRbCategory;
                    mRbCategory.setVisibility(View.VISIBLE);
                } else {
                    // 首页、会员、分类、我的
                    mRbCategory.setVisibility(View.VISIBLE);
                }
                mRbFinding.setVisibility(View.GONE);
                mRadioGroup.removeView(mRbFinding);
            } else {
                // 首页、会员（快听）、订阅、我的
                mRbCategory.setVisibility(View.GONE);
                mRbFinding.setVisibility(View.VISIBLE);
            }

            if (QuickListenTabAbManager.showAIListenTab() && mRbQuickListen != null) {
                mRbQuickListen.setText("快听");
                setTabTextColor(mRbQuickListen, R.color.host_theme_bottom_tab_text_selector);
                mRbQuickListen.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.host_tab_quick_listen, 0, 0);
            }
            mRbHomePage.setOnClickListener(this);
            mRbFinding.setOnClickListener(this);
            mRbMineAndListen.setOnClickListener(this);
            mRbVip.setOnClickListener(this);
            mRbCategory.setOnClickListener(this);
            mSafeArea = findViewById(R.id.host_safe_area);
            AutoTraceHelper.bindData(mRadioGroup, AutoTraceHelper.MODULE_DEFAULT, "");
            AutoTraceHelper.bindData(mRbHomePage, AutoTraceHelper.MODULE_DEFAULT, "");
            AutoTraceHelper.bindData(mRbFinding, AutoTraceHelper.MODULE_DEFAULT, "");

            mRadioGroup.findViewById(R.id.play).setClickable(false);

            mRbFinding.setText(DyncAbTestUtil.isFeed2Subscribe() ? R.string.host_feed_tab_name_new : R.string.host_feed_tab_name);
        }

        if (ConstantsOpenSdk.isBundleFrameWork &&
                Configure.searchBundleModel.isDl && !Configure.searchBundleModel.hasGenerateBundleFile) {
            try {
                Router.getActionRouter(Configure.BUNDLE_SEARCH);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (ConstantsOpenSdk.isBundleFrameWork &&
                Configure.mainBundleModel.isDl &&
                !Configure.mainBundleModel.hasGenerateBundleFile &&
                !Configure.mainBundleModel.hasInitApplication) {
            Router.getActionByCallback(Configure.BUNDLE_MAIN, this, false, BundleModel.DOWNLOAD_DO_NOT);
        } else {
            Logger.i(BUNDLE_TAG, "main bundle already ready");
//            if (initViewOnShow || ToolUtil.isFirstInstallApp(getContext()) || !ViewUtil.isSplashAdShowing()) {
//                showDefaultBottomTab();
//            } else {
//                Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
//                    @Override
//                    public boolean queueIdle() {
//                        showDefaultBottomTab();
//                        return false;
//                    }
//                });
//            }
            showDefaultBottomTab();
        }

        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity initUi finish");

        mTabBg = findViewById(R.id.host_v_tabs_bg);
        mIvTabBg = findViewById(R.id.host_iv_tabs_bg);
        mTabBgLay = findViewById(R.id.host_tab_bg_lay);

        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity initUi finish111");

        RelativeLayout rootView = findViewById(R.id.host_rl_root);
        ListenTaskUtil.getInstance().register(this, rootView);

        if (ElderlyModeManager.getInstance().isElderlyMode()) {
            containViewInflate();
        }
        ElderlyModeManager.getInstance().handleTabLogic(this, mRadioGroup, mActivityTabManager);

        if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
            AccessibilityModeManager.INSTANCE.switchToAccessibilityModeTab();
        }

        PreloadAdManager.getInstance().setReleased(false);
    }

    public void changeVipTabToNovel() {
        if (NovelTabAbManager.INSTANCE.showNovelTab() && mRbVip != null) {
            if (getTabFragmentManager() != null && getTabFragmentManager().getCurrFragment()
                    instanceof IMainFunctionAction.AbstractHomePageFragment) {
                change2RecommendWhileInVipTab();
                reloadHomePageTabData();
            } else {
                new HomePageTabRequestTask().myexec();
            }
            mRbVip.setText("免费");
            if (!hasBottomAtmophere) {
                mRbVip.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.host_novel_tab_01, 0, 0);
            }
            if (mVipRedDotPosition != null) {
                mVipRedDotPosition.setRedDotShow(false);
            }
            mRbVip.setTag(R.id.host_cur_resource_tag, null);
            mRbVip.setTag(R.id.host_load_success, false);
            mTabLottieFilePath.put(TabFragmentManager.TAB_VIP, LOTTIE_BOTTOM_TAB_NOVEL_BTN_JSON);
            boolean isCurChecked = (mRadioGroup.findViewById(mRadioGroup.getCheckedRadioButtonId()) == mRbVip);
            loadBottomTabResource(mRbVip, TabFragmentManager.TAB_VIP, isCurChecked);
            updateBottomTabBgColor();
        }
    }


    public void changeNovelOrVipOrCategoryV2TabForce() {
        if (mRbVip != null) {
            boolean shouldSelectVipTab = false;
            if (getTabFragmentManager() != null && getTabFragmentManager().getCurrFragment()
                    instanceof IMainFunctionAction.AbstractHomePageFragment) {
                if (change2RecommendWhileInVipTab()) {
                    shouldSelectVipTab = true;
                    new HomePageTabRequestTask().myexec();
                } else {
                    reloadHomePageTabData();
                }
            } else {
                new HomePageTabRequestTask().myexec();
            }
            if (NovelTabAbManager.INSTANCE.showNovelTab()) {
                mRbVip.setText("免费");
                mTabLottieFilePath.put(TabFragmentManager.TAB_VIP, LOTTIE_BOTTOM_TAB_NOVEL_BTN_JSON);
                if (!hasBottomAtmophere) {
                    mRbVip.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.host_novel_tab_01, 0, 0);
                }
            } else if (NovelTabAbManager.showCategoryV2Tab()) {
                mRbVip.setText("分类");
                mTabLottieFilePath.put(TabFragmentManager.TAB_VIP, LOTTIE_BOTTOM_TAB_CATEGORY_V2_BTN_JSON);
                if (!hasBottomAtmophere) {
                    mRbVip.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.host_category_v2_tab_icon, 0, 0);
                }
            } else if (QuickListenTabAbManager.showAIListenTab() && mRbQuickListen != null) {
                mRbQuickListen.setText("快听");
                setTabTextColor(mRbQuickListen, R.color.host_theme_bottom_tab_text_selector);
                mRbQuickListen.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.host_tab_quick_listen, 0, 0);
            } else {
                mRbVip.setText("会员");
                mTabLottieFilePath.put(TabFragmentManager.TAB_VIP, LOTTIE_BOTTOM_TAB_VIP_BTN_JSON);
                if (!hasBottomAtmophere) {
                    mRbVip.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.host_vip_tab_01, 0, 0);
                }
            }
            if (mVipRedDotPosition != null) {
                mVipRedDotPosition.setRedDotShow(false);
            }
            mRbVip.setTag(R.id.host_cur_resource_tag, null);
            mRbVip.setTag(R.id.host_load_success, false);
            boolean isCurChecked = (mRadioGroup.findViewById(mRadioGroup.getCheckedRadioButtonId()) == mRbVip);
            loadBottomTabResource(mRbVip, TabFragmentManager.TAB_VIP, isCurChecked);
            updateBottomTabBgColor();
            if (tabFragmentManager != null) {
                FragmentManager fm = getSupportFragmentManager();
                BaseFragment2 vipOrNovelFragment = (BaseFragment2) fm.findFragmentByTag(String.valueOf(TabFragmentManager.TAB_VIP));
                FragmentTransaction transaction = fm.beginTransaction();
                if (transaction != null && vipOrNovelFragment != null) {
                    transaction.remove(vipOrNovelFragment);
                    transaction.commitNowAllowingStateLoss();
                }
                tabFragmentManager.resetNovelOrVipFlag();
                if (TabFragmentManager.TAB_VIP == tabFragmentManager.getCurrentTab()) {
                    tabFragmentManager.showFragment(TabFragmentManager.TAB_VIP, null);
                } else if (shouldSelectVipTab && TabFragmentManager.TAB_HOME_PAGE == tabFragmentManager.getCurrentTab()) {
                    mRbVip.setChecked(true);
                }
            }
        }
    }

    private boolean change2RecommendWhileInVipTab() {
        if (getTabFragmentManager() != null && getTabFragmentManager().getCurrFragment()
                instanceof IMainFunctionAction.AbstractHomePageFragment) {
            IMainFunctionAction.AbstractHomePageFragment homePageFragment
                    = (IMainFunctionAction.AbstractHomePageFragment) getTabFragmentManager().getCurrFragment();
            return homePageFragment.change2RecommendWhileInVipTab();
        }
        return false;
    }


    @Override
    protected void onNewIntent(Intent intent) {
        Logger.i(TAG, "MainActivity app_start_time onNewIntent");
        super.onNewIntent(intent);
        if (intent == null) {
            return;
        }
        if ((DarkModeUtil.getInstance().isHasReCreateIn3s()) || BaseApplication.getMainActivity() instanceof MainActivity && (MainActivity) BaseApplication.getMainActivity() != MainActivity.this) {
            DarkModeUtil.getInstance().setHasReCreateIn3s(false);
            HandlerManager.postOnUIThreadDelay(() -> {
                // 尝试解决暗黑切换后iting跳转不正常的问题
                if (BaseApplication.getMainActivity() instanceof MainActivity && (MainActivity) BaseApplication.getMainActivity() != MainActivity.this) {
                    BaseUtil.addIntentFlagClearTop(intent);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    MainApplication.getMyApplicationContext().startActivity(intent);
                    PushArrivedTraceManager.INSTANCE.getInstance().statErrorTrace("onNewIntent mainActivityError");
                }
            }, 2000);
            return;
        }
        if (PreInstallUtil.canUseStatistics(this)) {
            uploadItingPath(intent);
        }
        SplashAdController.onNewIntent(intent);
        mOnNewIntent = intent;
        doOnNewIntented = false;
        intent.putExtra("isFromNewIntent", true);
        doOnNewIntentReal(intent);
        statOuterIting(intent);

        checkThirdAppAuthorizeActivity(intent);
    }

    private Intent mOnNewIntent;
    private boolean doOnNewIntented = false;

    private void doOnNewIntentReal(Intent intent) {
        if (doOnNewIntented) {
            return;
        }
        doOnNewIntented = true;

        Logger.log("MainActivity acitivityLife: doOnNewIntentReal ");

        boolean hasKeyIsLogin;
        try {
            hasKeyIsLogin = intent.hasExtra(BundleKeyConstants.KEY_IS_LOGIN);
        } catch (Exception e) {
            return;
        }
        if (hasKeyIsLogin && tabFragmentManager != null) {
            checkRadio(tabFragmentManager.getCurrentTab(), null);
        }
        mIsRestoreFromBackground = false;

        if (!intent.getBooleanExtra(BundleKeyConstants.KEY_IS_HANDLED_INTENT, false)) {
            doSomethingByIntent(intent);
        }
        handleShortcut(intent);

        //驾驶模式跳转到设置-车载设备列表页面
        if (intent.getBooleanExtra("from_drivemode_to_mydrivedevicemanagefragment", false)) {
            try {
                BaseFragment2 myDriveDeviceManageFragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().MyDriveDeviceManageFragment();
                myDriveDeviceManageFragment.setCallbackFinish(new IFragmentFinish() {
                    @Override
                    public void onFinishCallback(Class<?> cls, int fid, Object... params) {
                        if (canUpdateUi()) {
                            //DriveModeActivityV2.startDriveModeActivityV3();
                            DriveModeActivityV3.startDriveModeActivityV3();
                        }
                    }
                });
                startFragment(myDriveDeviceManageFragment);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        updateChannelId(intent);
        mOnNewIntent = null;
    }

    private void doSomethingByIntent(Intent intent) {
        if (Logger.isDebug) {
            Logger.d("MainActivity", "doSomethingByIntent  " + intent + "   " + Log.getStackTraceString(new Throwable()));
        }
        //防止Activity被回收后，回到首页会再次跳转（例如打开开发者选项中的不保留后台服务）
        if (!mIsRestoreFromBackground) {
            if (itingManager == null) {
                itingManager = new ItingManager(this);
            }
            OuterItingManager.INSTANCE.markOuterItingAction();
            ArriveTraceManager.onGotIntent(intent);
            RiskManageUtil.RiskCheckBin riskCheckBin = RiskManageUtil.getRiskCheckBinOfIntent(intent);
            if (riskCheckBin.isRiskIntent()) {
                RiskManageUtil.reportRiskIntent(riskCheckBin, new RiskManageUtil.RiskManageVerifyListener() {
                    @Override
                    public void onVerifySuccess() {
                        mHasSchemeHandled = itingManager.doSomethingByIntent(intent);
                        if (mHasSchemeHandled) {
                            showItingReturnBtnIfNeeded();
                        }
                        try{
                            SplashAdRecord.reportRtbIting(MainActivity.this, intent, "2");
                        }catch (Throwable e){
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onVerifyCancle(int code, String message) {

                    }

                    @Override
                    public void onVerifyFail(int code, String message) {
                        CustomToast.showFailToast(message);
                    }
                });
            } else {
                mHasSchemeHandled = itingManager.doSomethingByIntent(intent);
                if (mHasSchemeHandled) {
                    showItingReturnBtnIfNeeded();
                }
                try{
                    SplashAdRecord.reportRtbIting(MainActivity.this, intent, "3");
                }catch (Throwable e){
                    e.printStackTrace();
                }
            }
        } else {
            mIsRestoreFromBackground = false;
        }
    }

    /**
     * 从外部应用跳进来需要加个返回按钮，比如从百度、vivo、快手等导过来的
     */
    private void showItingReturnBtnIfNeeded() {
        if (ItingReturnBtnManager.INSTANCE.shouldShowReturnBtn()) {
            if (mITingReturnView == null) {
                ViewStub viewStub = findViewById(R.id.host_vs_iting_return_btn);
                if (viewStub != null) {
                    mITingReturnView = viewStub.inflate();
                }
            }
            if (mITingReturnView == null) {
                return;
            }

            // 检查样式
            ItingReturnBtnManager.checkShowStyle(mITingReturnView);
            String btnText = ItingReturnBtnManager.INSTANCE.getBtnText();
            String url = ItingReturnBtnManager.INSTANCE.getBackUrl();

            mITingReturnView.setOnClickListener(v -> {
                if (OneClickHelper.getInstance().onClick(v)) {
                    goBackToChannel(url);

                    new XMTraceApi.Trace()
                            .setMetaId(29522)
                            .setServiceId("dialogClick")
                            .put("text", btnText)
                            .put("returnUrl", url)
                            .createTrace();
                }
            });
            mITingReturnView.setVisibility(View.VISIBLE);

            new XMTraceApi.Trace()
                    .setMetaId(29521)
                    .setServiceId("dialogView")
                    .put("text", btnText)
                    .put("returnUrl", url)
                    .createTrace();
        }
    }

    private void goBackToChannel(String url) {
        ItingReturnBtnManager.INSTANCE.reset();
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
        try {
            startActivity(intent);
        } catch (Exception e) {
            Logger.e(e);
        }
        mITingReturnView.setVisibility(View.INVISIBLE);
    }

    private void updateChannelId(Intent intent) {
        if (intent != null && intent.getData() != null) {
            String channelId = DirectionalAdFreeManager.INSTANCE.getChannelId(intent.getData().toString());
            if (mStartSplashAdHelper != null){
                mStartSplashAdHelper.setChannelId(channelId);
            }
        }
    }

    /**
     * 根据PushModel type 判断跳转方向
     */
    public void checkRank(int rankingListid, String type, String title) {

        try {
            if (type.equals(AppConstants.TYPE_ALBUM_STR)) {
                startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newRankContentListFragment(rankingListid, "main", title, AppConstants.TYPE_ALBUM, ConstantsOpenSdk.PLAY_FROM_RANK, BaseAlbumAdapter.RANKING_RULE_PLAYED));
            } else if (type.equals(AppConstants.TYPE_ANCHOR_STR)) {
                startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newRankContentListFragment(rankingListid, "main", title, AppConstants.TYPE_ANCHOR, ConstantsOpenSdk.PLAY_FROM_RANK, BaseAlbumAdapter.RANKING_RULE_PLAYED));
            } else if (type.equals(AppConstants.TYPE_TRACK_STR)) {
                startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newRankContentListFragment(rankingListid, "main", title, AppConstants.TYPE_TRACK, ConstantsOpenSdk.PLAY_FROM_RANK, BaseAlbumAdapter.RANKING_RULE_PLAYED));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void playLastPlayedSoundAndCheckIsConnected() {
        if (XmPlayerManager.getInstance(this).isConnected()) {
            // 如果播放列表长度>0 就直接播放,如果小于0,需要判断历史播放记录的长度,如果长度>0 ,则再添加一个加载历史记录成功的回调
            if (XmPlayerManager.getInstance(this).getPlayListSize() > 0) {
                playLastPlayedSound();
            } else if (XmPlayerManager.getInstance(this).getHistoryTrackListSize() > 0) {
                XmPlayerManager.getInstance(this).addHistoryListLoadSuccess(onLoadSuccess);
            }
        } else {
            XmPlayerManager.getInstance(this).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
                @Override
                public void onConnected() {
                    XmPlayerManager.getInstance(MainActivity.this).removeOnConnectedListerner(this);

                    if (XmPlayerManager.getInstance(MainActivity.this).getPlayListSize() > 0) {
                        playLastPlayedSound();
                    } else if (XmPlayerManager.getInstance(MainActivity.this).getHistoryTrackListSize() > 0) {
                        XmPlayerManager.getInstance(MainActivity.this).addHistoryListLoadSuccess(onLoadSuccess);
                    }
                }
            });
        }
    }

    private XmPlayerManager.IOnHistoryListLoadSuccess onLoadSuccess = new XmPlayerManager.IOnHistoryListLoadSuccess() {
        @Override
        public void onHistoryListLoadSuccess() {
            XmPlayerManager.getInstance(MainActivity.this).removeHistoryListLoadSuccess(this);
            playLastPlayedSound();
        }
    };

    private void playLastPlayedSound() {
        if (!XmPlayerManager.getInstance(this).isPlaying()) {
            PlayTools.play(this);
        }

        showPlayFragment(null, PlayerManager.PLAY_CURRENT);
    }

    boolean isCallOnResumeMy;

    @Override
    public void onResumeMy() {
        super.onResumeMy();
        Logger.i("cf_test", "____onResumeMy");
        SplashAdRecord.reportMainActivityResume(isDoOnCreate);

        CheckLoginUtils.mainActivityOnResume(this);

        if (isDoOnCreate) {
            isCallOnResumeMy = false;
            doResumeMyReal();
        }

        if (Math.abs(System.currentTimeMillis() - mLastUpdateAdFreeRequestTime) > 2000) {
            CommonAdFreeManager.INSTANCE.updateAdFreeConfig();
            mLastUpdateAdFreeRequestTime = System.currentTimeMillis();
        }
        if (Math.abs(System.currentTimeMillis() - mLastRequestRewardVideoConfigTime) > 6 * 60 * 60 * 1000) {
            RewardVideoConfigManager.getInstance().loadRewardVideoConfig();
            mLastRequestRewardVideoConfigTime = System.currentTimeMillis();
        }

        if (ToolUtil.getDebugSystemProperty("debug.shixin.cancel_outside", "-1").equals("1")) {
            ChildProtectDialogManager.INSTANCE.showDialogForPreview();
        }
    }

    private void doResumeMyReal() {
        if (isCallOnResumeMy) {
            return;
        }

        isCallOnResumeMy = true;

        isUserLeaveHint = false;
        Logger.log("MainActivity acitivityLife: doResumeMyReal ");

        if (mNeedLockScreen == AppConstants.LOCK_SCREAN) {
            showLockScreen();
        } else if (mNeedLockScreen == AppConstants.UNLOCK_SCREAN) {
            clearLockScreen();
        }

        if (isNeedToSyncHistory) {
            syncCloudHistory(true);
            isNeedToSyncHistory = false;
        }


        if (!playButtonIsShow() && mManageFragment != null
                && mManageFragment.getFragmentCount() == 0) {
            showPlayButton();
        }

        // 如果登录再次显示页面的时候拉下用户信息
        if (UserInfoMannage.hasLogined()) {
            if (UserInfoMannage.getUid() != mLastUpdatePersonUid || Math.abs(System.currentTimeMillis() - mLastUpdatePersonRequestTime) > 2000) {
                UserInfoMannage.getInstance().updatePersonInfo(false);
                mLastUpdatePersonRequestTime = System.currentTimeMillis();
            }
        }
        mLastUpdatePersonUid = UserInfoMannage.getUid();

        updateMineListenTabRedDot(true, false);

        //syncForMainActivity内部限制了，成功一次后下次不会发起请求，如果失败，下次可见再次同步数据
        ChildProtectManager.syncForMainActivity();

        UserOneDateListenDuration.onActivityResumeUpdateServiceTime(this, null);
        setBottomTabsAccessibility(true);

        ViewUtil.addDialogShowStateChange(mShowStateChange);

        if (!HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()) {
            UploadBlueBoothMessageUtil.uploadBuleMessage();
        }

        OverseasUserVerifyManager.onAppResume();

        // 上一次闭屏是因为跳通知权限页面，则resume时上报埋点（真的开启通知权限）
        NotificationPermissionOpenManager.checkNotificationPermissionStatusAndUpload();
        traceBottomTabTagShow();
        HandlerManager.removeCallbacks(mCheckRadioVisibleCount);
        HandlerManager.postOnUIThreadDelay(mCheckRadioVisibleCount, 2000);

        homeReserveDialogFireworkManager.init();
    }

    private final Runnable mCheckRadioVisibleCount = new Runnable() {
        @Override
        public void run() {
            if (canUpdateUi()) {
                MyListenAbUtil.INSTANCE.checkRadioVisibleCount(mRadioGroup);
            }
        }
    };

    private void checkDynamicTabCanUpdate() {
        if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()) {
            return;
        }
        if (mManageFragment == null || ToolUtil.isEmptyCollects(mManageFragment.mStacks)) {
            Logger.i(DynamicTabRedHintManager.TAG, "MainActivity checkDynamicTabCanUpdate");
            DynamicTabRedHintManager.INSTANCE.checkUpdate();
        }
    }

    private long mLastSyncCloudHistoryTime = 0;
    private long mSyncCloudHistoryInterval = 2 * 1000;
    private void syncCloudHistory(boolean isNeedToPull) {
        long curTime = System.currentTimeMillis();
        if (Math.abs(curTime - mLastSyncCloudHistoryTime) < mSyncCloudHistoryInterval) {
            Logger.i("cf_test", "__not__syncCloudHistory——————" + (curTime - mLastSyncCloudHistoryTime));
            return;
        }
        mLastSyncCloudHistoryTime = curTime;
        Logger.i("cf_test", "____syncCloudHistory");
        ICloudyHistory historyManager = RouterServiceManager.getInstance().getService(ICloudyHistory.class);
        if (historyManager != null) {
            historyManager.syncCloudHistory(isNeedToPull);
        }
    }

    public void checkIting(String content) {
        if (TextUtils.isEmpty(content)) {
            LiveDataReportUtil.checkIsNewInstallAndReportActivateEvent();
            return;
        }
        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                decodeShareCommand(content, new IHandleItingCallBack() {
                    @Override
                    public void handleIting(String rawIting, ShareCommand object) {
                        LiveDataReportUtil.setCanReport(false);
                        MainActivity.this.handleIting(rawIting, object);
                    }
                });
            }
        });

    }


    public interface IHandleItingCallBack {
        void handleIting(String rawIting, ShareCommand object);
    }

    public static void decodeShareCommand(String realContent, IHandleItingCallBack callBack) {
        if (TextUtils.isEmpty(realContent)) {
            return;
        }
        // 解密串码
        CommonRequestM.decodeShareCommand(realContent, new IDataCallBack<ShareCommand>() {
            @Override
            public void onSuccess(@Nullable ShareCommand object) {
                if (object == null) {
                    // 直播站外投流新安装用户且剪切板内容无效时，执行上报新用户激活APP事件
                    LiveDataReportUtil.checkIsNewInstallAndReportActivateEvent();
                    return;
                }
                SystemServiceManager.setClipBoardData(BaseApplication.getMyApplicationContext(), "");
                String url = object.getLink();
                if (TextUtils.isEmpty(url)) {
                    LiveDataReportUtil.checkIsNewInstallAndReportActivateEvent();
                    return;
                }

                uploadItingInfo(realContent, url);

                String rawIting = parseUrl(url);
                if (TextUtils.isEmpty(rawIting)) {
                    // 直播站外投流新安装用户且剪切板内容无效时，执行上报新用户激活APP事件
                    LiveDataReportUtil.checkIsNewInstallAndReportActivateEvent();
                    return;
                }

                // 解析iting请求  其他事件
                new XMTraceApi.Trace()
                        .setMetaId(42738)
                        .setServiceId("others")
                        .put("status", "success")
                        .put("itingUrl", rawIting)
                        .createTrace();
                if (callBack != null) {
                    callBack.handleIting(rawIting, object);
                }
            }

            @Override
            public void onError(int code, String message) {
                // 直播站外投流新安装用户且剪切板内容无效时，执行上报新用户激活APP事件
                LiveDataReportUtil.checkIsNewInstallAndReportActivateEvent();

                // 解析iting请求  其他事件
                new XMTraceApi.Trace()
                        .setMetaId(42738)
                        .setServiceId("others")
                        .put("status", "fail")
                        .put("error", "" + code)
                        .createTrace();
            }
        });
    }

    private static void uploadItingInfo(String realContent, String url) {
        UserTracking ut = new UserTracking();
        String uid = "null";
        if (UserInfoMannage.hasLogined()) {
            uid = String.valueOf(UserInfoMannage.getUid());
        }
        ut.setUserId(uid);
        ut.setSrcPageUrl(url);
        HashMap<String, String> params = ut.getParams();
        if (params != null) {
            params.put("command", realContent);
            params.put("isFirstInstall", "" + ToolUtil.isFirstInstallApp(BaseApplication.getMyApplicationContext()));
        }
        ut.statIting(XDCSCollectUtil.APP_NAME_EVENT, "viewSuccess");
        SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).saveBoolean(PreferenceConstantsInHost.KEY_H5_STRING_UPLOADED, true);

        new XMTraceApi.Trace()
                .setMetaId(10060)
                .setServiceId("viewSuccess")
                .put("pageUrl", url)
                .put("command", realContent)
                .put("isFirstInstalled", ToolUtil.isFirstInstallApp(BaseApplication.getMyApplicationContext()) ? "true" : "false")
                .createTrace();
    }

    private void handleIting(String rawIting, ShareCommand object) {
        if (TextUtils.isEmpty(rawIting)) {
            return;
        }

        if (mHasSchemeHandled) {
            mHasSchemeHandled = false;
            return;
        }

        isHandleIting = true;
        TempDataManager.getInstance().saveBoolean("is_handle_iting", true);
        Uri itingUri = Uri.parse(rawIting);
        String msgType = itingUri.getQueryParameter("msg_type");
        if (!TextUtils.isEmpty(msgType)) {
            if (msgType.equals(String.valueOf(AppConstants.PAGE_SOUND))) {
                // 声音 直接打开播放页播放
                directToIting(itingUri);
            } else if (msgType.equals(String.valueOf(AppConstants.PAGE_ALBUM_PLAY)) || msgType.equals(String.valueOf(AppConstants.PAGE_ALBUM))) {
                // 专辑
                directToIting(itingUri);
            } else if (msgType.equals(String.valueOf(AppConstants.PAGE_TO_AUDITION))) {
                PreItingAuditionDialogFragment fragment = new PreItingAuditionDialogFragment();
                fragment.show(getSupportFragmentManager(), "pre_iting", rawIting, object.getShowPicUrl(), object.getShowTitle(), itingUri.getQueryParameter("videoId"));
                isPreItingShowing = true;
            } else {
                directToIting(itingUri);
            }
        } else {
            directToIting(itingUri);
        }
    }

    private void directToIting(Uri itingUri) {
        // Iting跳转
        Intent intent = MainActivity.getMainActivityIntent(this);
        intent.setData(itingUri);
        startActivity(intent);
    }

    @Nullable
    private static String parseUrl(String url) {
        String iting = null;
        try {
            String result = URLDecoder.decode(url, "utf-8");
            String pIting = "iting://open\\?.*";
            Pattern r = Pattern.compile(pIting);
            Matcher m = r.matcher(result);
            if (m.find()) {
                iting = m.group(0);
            }
            String pUid = "uid=(\\d+)";
            r = Pattern.compile(pUid);
            m = r.matcher(result);
            if (m.find()) {
                try {
                    String uid = m.group(1);
                    handleUid(uid);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return iting;
    }

    private static void handleUid(String uid) {
        if (TextUtils.isEmpty(uid)) {
            return;
        }
        if (!UserInfoMannage.hasLogined()) {
            SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).saveString(PreferenceConstantsInHost.KEY_SHARE_WEIXIN_UID, uid);
        } else {
            CommonRequestM.uploadFriend(uid, new IDataCallBack<Boolean>() {
                @Override
                public void onSuccess(@Nullable Boolean object) {
                    SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).saveString(PreferenceConstantsInHost.KEY_SHARE_WEIXIN_UID, "");
                }

                @Override
                public void onError(int code, String message) {

                }
            });
        }
    }

    @Override
    protected void onStop() {
        Logger.i("cf_test", "____onStop");
        StartServiceTimeoutFixUtil.updateActivityForeground(MainActivity.class.getName(), false);
        mHasCheckClipContent = false; // 不可见后复位黏贴板读取开关
        if (!this.isFinishing()) {
            new UserTracking().statIting(XDCSCollectUtil.APP_NAME_EVENT, "sleep");
        }
        UserTrackingUtil.getInstance().statITing();
        super.onStop();
        isNeedToSyncHistory = true;
        mHasSchemeHandled = false;
//        mCanUploadH5Code = false;  // 退到后台，再次进入，就不该再上报了。
    }


    @Override
    protected void onStart() {
        try {
            super.onStart();
        } catch (Exception e) {
            e.printStackTrace();
            if (ConstantsOpenSdk.isDebug) {
                throw e;
            }
        }
        if (isMainActivityResumed) {
            new UserTracking().statIting(XDCSCollectUtil.APP_NAME_EVENT, "wakeUp");
            new UserTracking().statIting(XDCSCollectUtil.APP_NAME_EVENT, "engagement");
        }
        isMainActivityResumed = true;
//        AdOtherInstallManager.getInstance().initOpenAPPInstallList();
        StartServiceTimeoutFixUtil.updateActivityForeground(MainActivity.class.getName(), false);
    }

    private Runnable mRunnable;
    private int mSecond = 11 * 60;

    private void startThirdApp() {
        Intent intent = new Intent();
        ComponentName componentName = new ComponentName("com.ximalaya.ting.android.xmbatterydemo", "com.ximalaya.ting.android.xmbatterydemo.MainActivity");
        intent.setComponent(componentName);
        startActivity(intent);
    }

    @Override
    @StartUpMonitor
    public void onResume() {
        StartServiceTimeoutFixUtil.updateActivityForeground(MainActivity.class.getName(), true);
        PushManager.getInstance().onAppGoToForeground(this);
        ChildProtectDialogManager.INSTANCE.notifyMainActivityVisibilityChanged(true);
        super.onResume();
        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onResume finish");
//        HandlerManager.postOnUIThreadDelay(mRunnable, 1 * 1000);
        TraceHelperManager.INSTANCE.notifyMainActivityResume();
        JumpAttributionManager.setDriveMode(false);
        checkNeedLoadTabDataOnResume();
        if (ConstantsOpenSdk.isDebug && isOpenFpsMonitor()) {
            if (fpsMontor != null) {
                fpsMontor.startMonitoring();
            }
        }
        ChatXmlyPopupManager.INSTANCE.scanConnectBluetooth(0);
//        tryToShowHomePageChatXmlyBubble();
        OneDayListenTimeForPlayProcessManager.INSTANCE.onMainActivityResumeForMain();
    }

    private void reloadHomePageTabData() {
        if (getTabFragmentManager() != null && getTabFragmentManager().getCurrFragment()
                instanceof IMainFunctionAction.AbstractHomePageFragment) {
            IMainFunctionAction.AbstractHomePageFragment homePageFragment
                    = (IMainFunctionAction.AbstractHomePageFragment) getTabFragmentManager().getCurrFragment();
            homePageFragment.reloadTabData(true);
        }
    }

    private void tryToShowHomePageChatXmlyBubble() {
        if (getTabFragmentManager() != null && getTabFragmentManager().getCurrFragment()
                instanceof IMainFunctionAction.AbstractHomePageFragment) {
            IMainFunctionAction.AbstractHomePageFragment homePageFragment
                    = (IMainFunctionAction.AbstractHomePageFragment) getTabFragmentManager().getCurrFragment();
            homePageFragment.tryToShowChatXmlyBubble();
        }
    }

    private void checkNeedLoadTabDataOnResume() {
        ConfigureCenter.getInstance().registerConfigureCallback(new IConfigureCenter.ConfigureCallback() {
            @Override
            public void onResult(boolean success) {
                ConfigureCenter.getInstance().unRegisterConfigureCallback(this);
                if (success) {
                    long lastMainActivityPauseTime = SharedPreferencesUtil.getInstance(
                            getApplicationContext()).getLong(PreferenceConstantsInHost.LAST_MAIN_ACTIVITY_PAUSE_TIME);
                    int loadTabDataIntervalTime = ConfigureCenter.getInstance().getInt(CConstants.Group_android.GROUP_NAME,
                            CConstants.Group_android.KEY_LOAD_TAB_INTERVAL_TIME, 10);
                    long fromMainActivityPauseTime = System.currentTimeMillis() - lastMainActivityPauseTime;
                    if (fromMainActivityPauseTime > loadTabDataIntervalTime * 60 * 1000) {
                        reloadHomePageTabData();
                        Logger.i("cf_test", "超过时间————" + loadTabDataIntervalTime + "___重新加载tab数据");
                        return;
                    }
                    Logger.i("cf_test", "距离上一次推到后台的时间为s————" + (fromMainActivityPauseTime / 1000) + "___小于配置时间" + (loadTabDataIntervalTime * 60));
                }
            }
        });
    }

    @Override
    protected boolean doOnResumeNeedDelayToDrawFinish() {
        return true;
    }

    boolean isCallDoOnResumed;

    @Override
    protected void doOnResume() {
        Logger.d(TAG, "doOnResume call");
        super.doOnResume();

        if (isDoOnCreate) {
            isCallDoOnResumed = false;
            doOnResumeReal();
        }
    }

    private void doOnResumeReal() {
        if (isCallDoOnResumed) {
            return;
        }

        isCallDoOnResumed = true;

        Logger.log("MainActivity acitivityLife: doOnResumeReal ");

        KidsHelper.clearModeFlag(getContext());

        boolean requestClipboarded = false;
        boolean isAfterOnCreate = doAfterOnCreate;
        // onCreate 相关操作推迟到此处处理
        if (doAfterOnCreate) {
            doAfterOnCreate = false;
            // lastBindPhoneInfo 异步获取
            ToolUtil.getLastBindPhoneInfo(new IDataCallBack<BindLoginInfoModel>() {
                @Override
                public void onSuccess(@Nullable BindLoginInfoModel object) {
                    if (object != null) {
                        UserInfoMannage.gotoLogin(MainActivity.this);
                    }
                }

                @Override
                public void onError(int code, String message) {

                }
            });

            if (BringUpAbManager.INSTANCE.isUseGrowthSdk()) {
                requestClipboarded = true;
                TouTiaoAdManager.queryItingByGrowthSdk(this, getIntent(), this::showStartDialogs);
            } else {
                if (TouTiaoAdManager.needRequest(this, getIntent())) {
                    requestClipboarded = true;
                    ArriveTraceManager.onStartGetIting();
                    TouTiaoAdManager.queryItingUseChannelAndClipboard(this, new IHandleOk() {
                        @Override
                        public void onReady() {
                            showStartDialogs();
                        }
                    });
                } else {
                    showStartDialogs();
                }
            }

            VipJumpPageUtils.checkJumPage(this);

            AdMakeVipLocalManager.getInstance().checkRequestIsTargetUser(getIntent());

            HandlerManager.postOnBackgroundThread(() -> {
                try {
                    createShortcut();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

            handleShortcut(getIntent());

            //开启app后扫描已连接的蓝牙，判断是否为智能耳机设备
            SmartDeviceBluetoothManager.getInstance().getConnectHeadsetBluetooth(3000);

            //xdcs 上报启动时间
            postAppStartTime();

            // 登录迁移敏感数据
            LoginUtil.migrateData();

            // 登录更新token
            LoginUtil.refreshToken();
        }

        if (ToolUtil.isFirstInstallApp(this)) {
            // 广告预加载物料
            AdManager.preloadMaterials();
        }

        try {
            if (!ViewUtil.isSplashAdShowing()) {
                clipContentCheck(requestClipboarded);
            } else {
                mLastClipContentCheckNoHandle = true;
                mLastClipContentCheckRequestClipboarded = requestClipboarded;
            }
            ChannelInfoRecordManager.getInstance().recordStartup(!isAfterOnCreate);
        } catch (Exception e) {
            e.printStackTrace();
        }

        checkAndUpdateFirework();

        EmergencyPlanManager.getInstance().updateEmergencyPlan(); // 应急事故保底方案请求（针对服务端事故）

        updateBottomTabBgColor();
        updateBottomTabResource();
        SkinManager.INSTANCE.requestSkin(); // 请求皮肤包配置
        VipBottomTabIconManager.requestVipBottomTabIcon(false); // 请求会员底tab配置
        SkinManager.INSTANCE.addSkinSettingChangeListener(mSkinSettingChangeListener);
        VipBottomTabIconManager.INSTANCE.addVipBottomTabIconChangeListener(mSkinSettingChangeListener);
        BottomTabColorAndIconManager.INSTANCE.addSkinSettingChangeListener(mSkinSettingChangeListener);

        DummyUserGuideManager.getInstance().resumeGuideAction();

        //埋点播放统计 播放场景恢复为默认 用于驾驶模式
        if (XmPlayerManager.getInstance(MainActivity.this) != null) {
            XmPlayerManager.getInstance(MainActivity.this).setPlayScene(XmPlayerService.PLAY_SCENE_TYPE_DEFAULT);
        }

        // 检查下发的apk是否已经安装了
        if (!hasCheckInstall) {
            AdApkPageInfoManager.getInstance().checkIsInstall();
            hasCheckInstall = true;
        }

        checkDynamicTabCanUpdate();
        long lastAppGoToBackgroundTime = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getLong(PreferenceConstantsInHost.KEY_APP_LAST_GO_TO_BACKGROUND_TIME);
        if (lastAppGoToBackgroundTime > 0 && System.currentTimeMillis() - lastAppGoToBackgroundTime >= AdUnLockSnackBarManager.getHomeSnackBarResumeFap()) {
            // 前后台切换间隔大于30分钟才展示
            AdUnLockSnackBarManager.showSnackBarOnHomePageResume();
        }
//        if (FreeListenConfigManager.isFreeListenV2Open() && lastAppGoToBackgroundTime > 0 && System.currentTimeMillis() - lastAppGoToBackgroundTime >= 60 * 60 * 1000) {
//            // 命中时长模式，前后台切换间隔大于60分钟重新请求config接口
//            FreeListenConfigManager.getInstance().requestConfig(MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_SCENE_ID, ""),
//                    MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_EXP_ID, ""));
//        }
        MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveLong(PreferenceConstantsInHost.KEY_APP_LAST_GO_TO_BACKGROUND_TIME, 0);
        Logger.d(TAG, "doOnResume end");
//        HwSidManager.uploadSid();
        SyncCarTackProgressUtil.Instance.init();
    }

    private void checkAndUpdateFirework() {
        if (FireworkApi.getInstance().getLastUpdateTime() > 0 && System.currentTimeMillis() >= FireworkApi.getInstance().getLastUpdateTime() + 10 * 60 * 1000) {
            FireworkApi.getInstance().update();
        }
    }

    private void checkAndUpdateConfigure() {
        if (ConfigureCenter.getInstance().getLastUpdateTime() != 0 && System.currentTimeMillis() > ConfigureCenter.getInstance().getLastUpdateTime() + 20 * 60 * 1000) {
            try {
                ConfigureCenter.getInstance().update(getApplicationContext(), CConstants.ALL_GROUPS);
            } catch (NoCreateSignatureException e) {
                e.printStackTrace();
            }
        }
    }

    private void initPushChannel() {
        NotificationChannelUtils.createPersonalNotificationChannel(this);
    }

    @SuppressLint("StaticFieldLeak")
    public void initXiaoMiPush() {
        PushBadgeManager.getInstance().init();
        PushArrivedTraceManager.INSTANCE.setHotStart(true);
        boolean childProtectOpen = ChildProtectManager.isChildProtectOpen(this);
        Logger.e("miPush", "onwindowfocuschanged childProtect open: " + childProtectOpen);
        Logger.logToFile("miPush" + "onwindowfocuschanged childProtect open: " + childProtectOpen);

        HandlerManager.postOnUIThreadDelay(() -> {
            if (!childProtectOpen && !ApplicationManager.hasInitXiaoMiPush) {
                ApplicationManager.hasInitXiaoMiPush = true;
                if (XmPushManager.getInstance().getInitConfig() == null) {
                    //部分用户逻辑走到这里时推送config还是null,暂不知道原因,重新init一下
                    MainApplication.getInstance().applicationManager.initXmPush();
                }

                if (!XmPushManager.getInstance().hasInit()) {
                    Logger.d("miPush", "initXiaoMiPush init");
                    Logger.logToFile("miPush" + "initXiaoMiPush init");
                    boolean hasPermission = ContextCompat
                            .checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE)
                            == PackageManager.PERMISSION_GRANTED;
                    new UserTracking()
                            .putParam("hasPermission", "" + hasPermission)
                            .setId(8071)
                            .statIting(UserTracking.APP_NAME_EVENT, "miPush");
                    XmPushManager.getInstance().addPushInitCallback((type, msg) -> Logger.d("miPush", "type = " + type + "   msg = " + msg));
                    boolean openXiMaPipe = PushNotificationFilterManager.isXiMaPipeConfigOpen(this);
                    if (openXiMaPipe) {
                        XmPushManager.getInstance().addGeTuiFilterList(this, PushNotificationFilterManager.getPushContentFilterList());
                    }
                    XmPushManager.getInstance().init(BaseApplication.getMyApplicationContext(), BaseCall.getInstanse().getOkHttpClient());
                }
            }
        }, 3000);
    }

    /**
     * 显示当前的fragment
     *
     * @param isOnlyShowView 表示是否仅仅将Fragment的页面显示出来但是不调用系统提供的show方法
     */
    @Override
    public void showFragment(boolean isOnlyShowView) {
        super.showFragment(isOnlyShowView);
        if (tabFragmentManager == null) {
            return;
        }
        if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
            AccessibilityModeManager.INSTANCE.showCurFragment();
        } else {
            Fragment curFragment = tabFragmentManager.getCurrFragment();
            if (curFragment != null) {
                if (isOnlyShowView) {
                    if (curFragment.getView() != null) {
                        curFragment.getView().setVisibility(View.VISIBLE);
                    }
                } else {
                    FragmentUtil.hideOrShowFragment(curFragment, false);
                }
            }
        }
    }

    @Override
    public void onPause() {
        ChildProtectDialogManager.INSTANCE.notifyMainActivityVisibilityChanged(false);
        super.onPause();
        StartServiceTimeoutFixUtil.updateActivityForeground(MainActivity.class.getName(), false);
        if (!hasCallWindowFocused) {
            StartUpRecord.notUploadData = true;
        }
        TimeLimitManager.getInstance().save();
        DummyUserGuideManager.getInstance().pauseGuideAction();
        DummyUserGuideManager.getInstance().dismissNewUserNoPlayGuideDialogFra();
        FreeFlowRecordTypeUtil.checkIsFreeFlowAndUpload(this);
        postDelayUiTask();
        SkinManager.INSTANCE.removeSkinSettingChangeListener(mSkinSettingChangeListener);
        VipBottomTabIconManager.INSTANCE.removeVipBottomTabIconChangeListener(mSkinSettingChangeListener);
        BottomTabColorAndIconManager.INSTANCE.removeSkinSettingChangeListener(mSkinSettingChangeListener);
        DynamicTabRedHintManager.INSTANCE.stopCountDown();
        TraceHelperManager.INSTANCE.notifyMainActivityPause();
        SharedPreferencesUtil.getInstance(getApplicationContext()).saveLong(
                PreferenceConstantsInHost.LAST_MAIN_ACTIVITY_PAUSE_TIME, System.currentTimeMillis());
//        HandlerManager.removeCallbacks(mRunnable);
        if (ConstantsOpenSdk.isDebug && isOpenFpsMonitor()) {
            if (fpsMontor != null) {
                fpsMontor.stopMonitoring();
            }
        }
        PushManager.getInstance().onAppGoToBackground(this);
        ChatXmlyPopupManager.INSTANCE.cancelShowBubbleTask();
        ViewUtil.removeDialogShowStateChange(mShowStateChange);
    }


    @Override
    protected void onUserLeaveHint() {
        super.onUserLeaveHint();
        if (isXPlayFragmentRealVisible() && (BaseApplication.getTopActivity() instanceof MainActivity)) {
            isUserLeaveHint = true;
        }
    }

    private boolean isXPlayFragmentRealVisible() {
        try {
            MainActionRouter actionRouter = (MainActionRouter) Router.getActionRouter(Configure.BUNDLE_MAIN);
            if (actionRouter != null) {
                IMainFunctionAction functionAction = actionRouter.getFunctionAction();
                if (functionAction != null) {
                    return functionAction.isXPlayFragmentRealVisible();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 当用户点击home键回到后台会回调onUserLeaveHint
     * 在onPause之前
     */
    public boolean isUserLeaveHint() {
        return isUserLeaveHint;
    }

    @Override
    public void onPauseMy() {
        super.onPauseMy();
        Logger.log("MainActivity acitivityLife: onPause ");
        if (mManageFragment != null) {
            Logger.i(TAG, mManageFragment.getStackNum() + "");
            mManageFragment.setCurFragmentFinish(false);
        }

        Fragment fragment = getSupportFragmentManager().findFragmentByTag("pre_iting");
        if (fragment != null) {
            getSupportFragmentManager().beginTransaction().remove(fragment).commitAllowingStateLoss();
        }
//        setBottomTabsAccessibility(false);

        HandlerManager.postOnBackgroundThread(() -> {
            SpCheckManager.getInstance().checkSp();
        });

    }

    /**
     * 跳转其他fragment,底部tab不可见时，无障碍不触发底部的焦点
     *
     * @param canAccess
     */
    private void setBottomTabsAccessibility(boolean canAccess) {

        if (mActivityTabManager != null) {
            mActivityTabManager.setImportantForAccessibility(canAccess);
        }

        if (mRadioGroup != null) {

            try {
                IRNFunctionRouter functionAction = Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN).getFunctionAction();
                boolean isRNFragmentOnTop = functionAction != null && functionAction.isRNFragment(getCurrentTopFragment());
                if (isRNFragmentOnTop) {
                    //顶部是 RN 页面时，如果有弹窗关闭，会执行到 canAccess 为 true，这里增加一个判断
                    mRadioGroup.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
                    return;
                }
            } catch (Throwable ignore) {
            }

            mRadioGroup.setImportantForAccessibility(canAccess ?
                    View.IMPORTANT_FOR_ACCESSIBILITY_YES :
                    View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
        }
    }

    @Override
    public void hideFragment(boolean isOnlyHideView) {
        super.hideFragment(isOnlyHideView);
        if (tabFragmentManager == null) {
            return;
        }

        if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
            AccessibilityModeManager.INSTANCE.hideCurFragment();
        } else {
            Fragment curFragment = tabFragmentManager.getCurrFragment();
            if (curFragment != null) {
                if (isOnlyHideView) {
                    if (curFragment.getView() != null) {
                        curFragment.getView().setVisibility(View.GONE);
                    }
                } else {
                    FragmentUtil.hideOrShowFragment(curFragment, true);
                }
            }
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.host_act_main;
    }

    @Override
    public void onBackPressed() {
        if (isFinishing())
            return;

        if (mOnBackPressInterceptor != null
                && mOnBackPressInterceptor.intercept()) {
            ExitStatisticUtil.reportPressBack(ExitStatisticUtil.Step.ON_BACK_PRESS_INTERCEPTOR);
            return;
        }

        if (mIsLockScreen) {
            ExitStatisticUtil.reportPressBack(ExitStatisticUtil.Step.IN_LOCK_SCREEN);
            return;
        }

        // 如果有开屏广告也不处理返回按钮
        FragmentManager supportFragmentManager = getSupportFragmentManager();
        if (supportFragmentManager != null) {
            Fragment splashAdFragment =
                    supportFragmentManager.findFragmentById(R.id.ad_fragment_container);
            if (splashAdFragment != null) {
                Logger.logToFile("MainActivity onBackPressed = has splashAdFragment");
                ExitStatisticUtil.reportPressBack(ExitStatisticUtil.Step.IS_SHOW_AD);
                return;
            }
        }

        if (mPlanTerminateFragmentWR != null && mPlanTerminateFragmentWR.get() != null && mPlanTerminateFragmentWR.get().isVisible()) {
            mPlanTerminateFragmentWR.get().dismissAllowingStateLoss();
            ExitStatisticUtil.reportPressBack(ExitStatisticUtil.Step.IN_PLAN_TERMINATE);
            return;
        }

        if (mPlayerManager != null && mPlayerManager.isPlayFragmentVisable()) {
            //播放页拦截返回键时，不用showPreFragment
            if (!onbackPlayFragment()) {
                if (mManageFragment != null) {
                    mManageFragment.showPreFragment(true, false);
                }
            }
            ExitStatisticUtil.reportPressBack(ExitStatisticUtil.Step.PLAY_FRAGMENT_VISIBLE);
            return;
        }

        if (FreeListenNewRecallUserDialogManager.isShowing()) {
            FreeListenNewRecallUserDialogManager.dismiss();
            return;
        }

        BaseFragment currentFragment = null;
        if (mManageFragment != null && mManageFragment.getCurrentFragment() instanceof BaseFragment) {
            currentFragment = (BaseFragment) mManageFragment
                    .getCurrentFragment();
        }
        if (mManageFragment != null && mManageFragment.onBackPressed()) {

            if (mManageFragment.isFragmentInsideBack()) {
                return;
            }
            if (currentFragment != null && currentFragment.getUnderThisHasPlayFragment()) {
                Bundle bundle = new Bundle();
                bundle.putBoolean(PlayerManager.BUNDLE_KEY_PLAY_CURRENT_FROM_BACK_PRESS, true);
                showPlayFragment(null, bundle,
                        PlayerManager.PLAY_CURRENT);
            }
            ExitStatisticUtil.reportPressBack(ExitStatisticUtil.Step.MANAGE_FRAGMENT_HANDLED);
            return;
        }

        // 于坤鹏产品说去掉直接到桌面
///        boolean isExitAppDialogShow = true;
///        ItemSetting exitAppDialogSetting = ConfigureCenterManager.getInstance()
///                .getItemSettingByGroupNameAndItemName("toc", "exit_app_popup_set");
///        if (exitAppDialogSetting != null) {
///            isExitAppDialogShow = exitAppDialogSetting.getBoolean();
///        }
///        if (isExitAppDialogShow) {
///            String quitAdDialog = "quitAdDialog";
///            if (getSupportFragmentManager().findFragmentByTag(quitAdDialog) == null) {
///                try {
///                    Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newQuitAdDialog().show(getSupportFragmentManager(), quitAdDialog);
///                } catch (Exception e) {
///                    e.printStackTrace();
///                }
///            }
///        } else {
///            try {
///                Intent home = new Intent(Intent.ACTION_MAIN);
///                home.addCategory(Intent.CATEGORY_HOME);
///                startActivity(home);
///            } catch (Exception e) {
///                e.printStackTrace();
///            }
///        }
        if (mBackToast != null) {
            mBackToast.cancel();
            mBackToast = null;
        }

        if (mITingReturnView != null && mITingReturnView.getVisibility() == View.VISIBLE && ItingReturnBtnManager.INSTANCE.isBackKeyToChannel()) {
            goBackToChannel(ItingReturnBtnManager.INSTANCE.getBackUrl());
            ExitStatisticUtil.reportPressBack(ExitStatisticUtil.Step.IS_SHOW_ITING_RETURN_BTN);
            return;
        }

        try {
            if (Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().needShowExitPop()) {
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 夏鑫产品说改成按一次提示toast，并刷新。连续按第二次退回到桌面。
        long pressedTime = System.currentTimeMillis();

        //第一次按退出，弹窗或者弹 toast，同时刷新
        if ((pressedTime - mLastBackPressedTime) > 2000) {
//            mBackToast = Toast.makeText(this, R.string.host_press_again_to_exit_to_launcher, Toast.LENGTH_SHORT);
//            mBackToast.setGravity(Gravity.CENTER_VERTICAL, 0, 0);
//            mBackToast.show();

            if (getTabFragmentManager() != null) {
                Fragment currFragment = getTabFragmentManager().getCurrFragment();
                if (currFragment instanceof IMainFunctionAction.AbstractFindingFragment) {
                    // 如果内部处理了onBackPressed不再显示
                    if (((IMainFunctionAction.AbstractFindingFragment) currFragment).onBackPressed()) {
                        return;
                    }
                } else if (currFragment instanceof BaseFragment2) {
                    ((BaseFragment) currFragment).onRefresh();
                }
            }

            boolean needShowNewUserNoPlayGuide = PreInstallNonePlayUserRecallManager.needShowNewUserNoPlayGuide();
            ExitStatisticUtil.Step step = needShowNewUserNoPlayGuide ?
                    ExitStatisticUtil.Step.FIRST_PRESS_BACK_REQUEST_NONEPLAY_SERVER : ExitStatisticUtil.Step.FIRST_PRESS_BACK;
            ExitStatisticUtil.reportPressBack(step);

            if (needShowNewUserNoPlayGuide) {
                PreInstallNonePlayUserRecallManager.requestDataAndShow(this, new Runnable() {
                    @Override
                    public void run() {
                        //没命中或者请求失败
                        ToastCompat.makeText(MainActivity.this, R.string.host_press_again_to_exit_to_launcher, Toast.LENGTH_SHORT).show();
                    }
                });
            } else {
                ToastCompat.makeText(this, R.string.host_press_again_to_exit_to_launcher, Toast.LENGTH_SHORT).show();
            }
            mLastBackPressedTime = pressedTime;


            return;
        }

        // 如果在分屏或者画中画的情况下直接将页面销毁掉,不然可能还是在画中画状态下
        //在摩托罗拉机型或者三星安全文件夹中，直接finish，否则无法返回到桌面
        if ((Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
                && (isInMultiWindowMode() || isInPictureInPictureMode())) || DeviceUtil.isMotorolaDevice() || DeviceUtil.isSamsungSafeFolder()
                || FixVivoBackHomeUtil.isVivoHome()) {
            ExitStatisticUtil.reportPressBack(ExitStatisticUtil.Step.SECOND_PRESS_BACK);
            finish();
        } else {
            try {
                Intent home = new Intent(Intent.ACTION_MAIN);
                home.addCategory(Intent.CATEGORY_HOME);
                startActivity(home);

                ExitStatisticUtil.reportPressBack(ExitStatisticUtil.Step.SECOND_PRESS_BACK_HOME);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 不要调用activity自带finish方法，退出应用启动掉该方法
     */
    public void finishMy() {
        isExit = true;
        finish();
    }

    boolean isDestroyed = false;

    @Override
    protected void onDestroy() {
        HomeRecommendPageLoadingOptimizationManager.INSTANCE.removeRecommendFragmentVisibleListeners(this);
        ChildProtectManager.removeChildProtectStatusListener(mChildProtectStatusListener);
        StartServiceTimeoutFixUtil.updateActivityForeground(MainActivity.class.getName(), false);
        int initDarkSource = MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext()).getInt(CConstants.Group_android.KEY_DARK_MODE_INIT_SOURCE, 0);
        if (initDarkSource == 1) {
            sInitedDarkMode = false;
        }
        VipGiftShareManager.INSTANCE.release();
        RedDotManage.getInstance().removeRedDotPosition(mVipRedDotPosition);
        RedDotManage.getInstance().removeRedDotPosition(mSubscribeRedDotPosition);
        RedDotManage.getInstance().removeRedDotPosition(mMyRedDotPosition);
        PlayErrorStatisticManager.getSingleInstance().saveLastPlayErrorType();
        if (!FixLaunchModeBug.canRunOnCreate) {
            FixLaunchModeBug.canRunOnCreate = true;
            onlyRunSuperOnDestroy();
            return;
        }
        super.onDestroy();
        isDestroyed = true;
        /*
          为了防止有些手机在设置里面，设置不保留后台服务，每次启动新的activity，
          都会destroy前一个activity，必须使用该标识，保证这种情况下，不杀死相关服务
         */
        try {
            this.stopService(new Intent().setClass(this, XiMaLaYaService.class));
        } catch (Throwable t) {
            t.printStackTrace();
        }
        if (isExit) {
            MainApplication.getInstance().exit();
        }
        ToolUtil.fixInputMethodManagerLeak(this);
        ToolUtil.clearTextLineCache();
        if (mExitDialogBuilder != null) {
            mExitDialogBuilder.destory();
            mExitDialogBuilder = null;
        }
        ViewUtil.flushStackLocalLeaks(Looper.getMainLooper());
        RouteServiceUtil.getDownloadService().unRegisterAllCallback();
        DailyNewsLogicManager.INSTANCE.unRegisterAlarmReceiver(this);
        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().clearTangramBitmapCache();
        } catch (Exception e) {
            e.printStackTrace();
        }

        //TODO IM系统更新
        //TODO IM系统更新
        //TODO IM系统更新
        //TODO IM系统更新
        PrivateChatToastSnackManager.INSTANCE.release();
        NoReadManage.getInstance(this).removeNoReadUpdateListenerListener(this);
        NoReadManage.getInstance(this).removeNewSocketMsgListener(NewUserTaskMsgManager.INSTANCE);
        NoReadManage.getInstance(this).removeNewSocketMsgListener(VipStatusChangedMsgManager.INSTANCE);

        DownloadServiceManage.getInstance().unBindDownloadService(this);
        stopWatchingExternalStorage();
        if (mLoginListener != null) {
            UserInfoMannage.getInstance().removeLoginStatusChangeListener(
                    mLoginListener);
            mLoginListener = null;
        }

        timeInRecommendFlow = 0;
        timeInRecommendFlowFirst = 0;
        if (mManageFragment != null) { // if we call finish in onCreate , mManagerFragment will be null
            mManageFragment.removeStackChangeListener(mStackChangeListener);
        }

        //TODO IM系统更新
        //TODO IM系统更新
        //TODO IM系统更新
        //TODO IM系统更新
        NoReadManage.release();

        PlayerManager.release();
        SearchManager.removeLoginStatusChangeListener();
        BaseUtil.hasReadStart = false;
        ConfigureCenter.getInstance().unRegisterConfigFetchCallback(mConfigFetchCallback);
        XmPlayerManager.getInstance(MainActivity.this).removeOnConnectedListerner(this);
        removeActions();
        CommentDraftManager.getInstance().release();
        ListenTaskUtil.release(this);


        //TODO IM系统注销
        //TODO IM系统注销
        //TODO IM系统注销
        //TODO IM系统注销
        doIMXChatLogout();
        PhoneGrade.getInstance().setILogPoster(null);

        PreloadAdManager.getInstance().setReleased(true);

        DriveModeBluetoothManager.getInstance().release();
        SmartDeviceBluetoothManager.getInstance().release();
        ChatXmlyPopupManager.INSTANCE.release();
        AdUnLockPaidManager.release();
        RewardVideoAdManager.release();
        UnLockSoundPatchManager.getInstance().release();
        AdMakeVipLocalManager.getInstance().release();

        ElderlyModeManager.getInstance().release();
        FileProtectorManager.getSingleInstance().removeFileAccessCallback(IFileAccessCallback.DELETE_FILE, mFileDeleteCallback);
        ItingReturnBtnManager.INSTANCE.reset();

        if (mStartSplashAdHelper != null) {
            mStartSplashAdHelper.onMainActivityDestoryUnRegisterActivityLife(getApplication());
        }
        TouTiaoAdManager.sHasHandleIting = false;
        EarnStatisticsLifeCycleCallback.INSTANCE.setFragmentShowListener(null);
        PushBadgeManager.getInstance().release();
        PlanTerminateManager.removePlanTerminateListener();
        PlanTerminateManagerForQuickListen.removePlanTerminateListener();
        FoldableScreenCompatUtil.INSTANCE.release();
        LikeTrackStateChangeManager.INSTANCE.release();
        if (updateSnackBar != null) {
            if (updateSnackBar.isShowing()) {
                updateSnackBar.dismiss();
            }
            updateSnackBar = null;
        }

        try {
            Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFunctionAction().onMainActivityDestroy();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (mToAddCoverImageView != null) {
            mToAddCoverImageView.clearMyAnimation();
        }

        ListRefreshCatchException.unRegisterRefreshException();
        InterestCardManager.unRegisterReceiver(this);
        FireworkApi.getInstance().removeFireworkShowingInterceptor(mChildProtectFireworkInterceptor);
        ChatUnreadManager.INSTANCE.unRegisterChatSession();
        ChatInternalServiceManager.INSTANCE.unRegisterService(IChatMessageCallback.class);
//        OfflineResourceAPI.removeDeleteCache();

        //取消注册监听
        TingReaderTrackAuthorityChangeManager.getInstance().unRegisterReceiver();

        AccessibilityModeManager.INSTANCE.onMainActivityDestroyed();
        SyncCarTackProgressUtil.Instance.onDestroy();
        PlayBarMustListenUtil.Instance.onDestroy();
        HomeRnTraceTimeManager.destroy();
        RefreshHomeChannelUtils.destroy();
        homeReserveDialogFireworkManager.release();
    }

    private void removeActions() {
        EventManager.getInstance().removeAction("ContinuePlayTip");
        EventManager.getInstance().removeAction(EventManager.ACTION_DUMMY_USER_GUIDE);
    }

    @Override
    public void removeFramentFromManageFragment(Fragment fra) {
        if (mManageFragment != null) {
            if (mManageFragment.getCurrentFragment() == fra) {
                onBackPressed();
            } else {
                mManageFragment.removeFragmentFromStacks(fra, false);
            }
        } else {
            onBackPressed();
        }
    }

    public void removeTopFramentFromManageFragment() {
        if (mManageFragment != null) {
            mManageFragment.removeTopFragment();
        }
    }

    public int getMangeFragmentSize() {
        if (mManageFragment != null) {
            return mManageFragment.getStackNum();
        }
        return 0;
    }

    /**
     * 将栈顶的fragment关闭，然后启动一个新的fragment
     *
     * @param finishFragment 待关闭的fragment
     * @param startFragment  待启动的fragment
     */
    public void finishAndStartFragment(BaseFragment finishFragment, BaseFragment startFragment) {
        if (finishFragment == null || startFragment == null) {
            return;
        }
        removeTopFramentFromManageFragment();
        startFragment.setUnderThisHasPlayFragment(finishFragment.getUnderThisHasPlayFragment());
        startFragment(startFragment);
    }


    public void clearAllFragmentFromManageFragment() {
        if (mManageFragment != null) {
            mManageFragment.clearAllFragmentFromStacks();

            if (!playButtonIsShow()) {
                showPlayButton();
            }
        }
    }

    public boolean containsFragment(Class<?> className) {
        if (mManageFragment != null
                && mManageFragment.getCurrentFragment() != null) {
            return mManageFragment.getCurrentFragment().getClass()
                    .equals(className);
        }
        return false;
    }

    public boolean showFragmentInMainFragment(int checkedId, Bundle bundle) {
        if (tabFragmentManager != null && checkedId == tabFragmentManager.getCurrentTab()) {
            //在调用showFragment的时候并没有吧bundle设置进去，
            //此处进行设置，目前是当前页面的时候，解决页面再次可见获取不到对应的getArguments问题
            Fragment currFragment = tabFragmentManager.getCurrFragment();

            // 需要切换到首页
            if (checkedId == TabFragmentManager.TAB_HOME_PAGE) {
                // 校验是否使用新版青少年模式首页
                boolean isUseNewStyle = TeenModeManager.INSTANCE.isUseNewTeenModeStyle();
                if (isUseNewStyle) {
                    boolean isOpenTeenMode = ChildProtectManager.isChildProtectOpen(this);
                    // 开启了青少年模式，当前页面非TeenHomePageFragment
                    if ((isOpenTeenMode && !(currFragment instanceof IKidFunctionAction.AbstractTeenHomePageFragment))) {
                        tabFragmentManager.showFragment(checkedId, bundle);
                        return false;
                    }
                    // 青少年模式已关闭，但当前首页非HomePageFragment
                    if (!isOpenTeenMode && !(currFragment instanceof IMainFunctionAction.AbstractHomePageFragment)) {
                        tabFragmentManager.showFragment(TabFragmentManager.TAB_HOME_PAGE, null);
                        return false;
                    }
                }
            }

            if (currFragment != null && bundle != null) {
                try {
                    currFragment.setArguments(bundle);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            showFragment(false);
            return false;
        } else {
            checkRadio(checkedId, bundle);
            return true;
        }
    }

    /**
     * 显示播放页面
     */
    public void showPlayFragment(View fromView, int channel, int playerType) {
        showPlayFragment(fromView, channel, null, playerType);
    }

    public void showPlayFragment(View fromView, int playerType) {
        showPlayFragment(fromView, 0, null, playerType);
    }

    /**
     * 现场直播
     */
    public void showPlayFragmentByActivityLive(View fromView, long activityid, String shareUrl) {
        showPlayFragment(fromView, PlayerManager.ACTIVITY_LIVE_TAG);
    }

    /**
     * 个人直播
     */
    public void showLiveAudioPlayFragment(View fromView, Bundle bundle) {
        //如果打开儿童模式，不能收听直播；
        if (ChildProtectManager.isChildProtectOpen(BaseApplication.getMyApplicationContext())) {
            ChildProtectInfo childProtectInfo = new ChildProtectInfo();
            childProtectInfo.form = ChildProtectInfo.FROM_LIVE;
            ChildProtectManager.openForbidPlayPage(childProtectInfo);
            return;
        }
        showPlayFragment(fromView, bundle, PlayerManager.ACTIVITY_LIVE_TAG);
    }


    /**
     * 显示微课直播间的播放页面
     */
    public void showWeikeLiveFragment(View fromView, Bundle bundle) {
        showPlayFragment(fromView, bundle, PlayerManager.WEIKE_LIVE_ROOM);
    }

    /**
     * 显示微课 极简 播放页面
     */
    public void showWeikeSimplePlayFragment(View fromView, Bundle bundle) {
        showPlayFragment(fromView, bundle, PlayerManager.WEIKE_SIMPLE_PLAY);
    }

    public void showDubShowPPTPlayFragment(View fromView, Bundle bundle) {
        try {
            if (bundle != null) {
                bundle.putBoolean(BundleKeyConstants.KEY_IS_NEW_FRAGMENT, true);
            }
            startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().getDubShowPPTPlayFragmentClass(), bundle);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showPlayFragment(View fromView, Bundle bundle, int playerType) {
        showPlayFragment(fromView, 0, bundle, playerType);
    }

    public void showPlayFragment(View fromView, int channel, Bundle bundle, int playerType) {
        if (mPlayerManager == null || isFinishing() || isDestroyed()) {
            return;
        }
        if (bundle == null) {
            bundle = new Bundle();
        }
        if (mPlayerManager.getCurrentFragment() == null) {
            if (fromView != null) {
                bundle.putParcelable(BundleKeyConstants.KEY_BURIEDPOINT, (Parcelable) fromView.getTag(R.id.host_buried_points));
            }
        }
        bundle.putInt(BundleKeyConstants.KEY_CHANNEL_PLAY_FRAGMENT, channel);

        mPlayerManager.startPlayFragment(true, playerType, bundle);
        Logger.e("BaseFragment", "start play fragment");
        // 更新playBar状态
        if (mPlayBarFragment != null) {
            if (AccessibilityModeManager.INSTANCE.isAccessibilityMode() && mPlayBarFragment.getView() != null) {
                mPlayBarFragment.getView().setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
            }
        }
        refreshPlayBarPlaying(XmPlayerManager.getInstance(MainActivity.this).isPlaying());
    }

    /**
     * 响应返回事件,正常hide playfragmetn
     */
    public boolean onbackPlayFragment() {
        if (mPlayerManager.getCurrentFragment() != null
                && mPlayerManager.isPlayFragmentAdded()
                && !mPlayerManager.getCurrentFragment().onBackPressed()) {
            hidePlayFragmentWillShow(null, false);

            // 需要放到hidePlayFragmentWillShow 之后
            if (mPlayerManager.getCurrentFragment() != null) {
                mPlayerManager.getCurrentFragment().setBackHintFragment(true);
            }
//            showPlayButton();
            return false;
        }
        return true;
    }

    /**
     * 隐藏播放页
     */
    public void hidePlayFragment(Fragment fromFra) {
        hidePlayFragmentWillShow(fromFra, true);
    }

    /**
     * 隐藏播放页并且是否要返回时再次显示
     */
    public void hidePlayFragmentWillShow(Fragment fromFra, boolean flag) {
        if (mPlayerManager == null || mPlayerManager.getCurrentFragment() == null || mPlayerManager.getCurrentFragment().isHidden()) {
            return;
        }
        mPlayerManager.getCurrentFragment().setBackHintFragment(false);

        if (flag && mPlayerManager.isPlayFragmentVisable()
                && fromFra != null) {
            ((BaseFragment) fromFra).setUnderThisHasPlayFragment(true);
        }

        mPlayerManager.setPlayFragmentIsVisable(false);
        if (fromFra == null) {
            if (mManageFragment != null && mManageFragment.getCurrentFragment() != null) {
                mManageFragment.getCurrentFragment().onResume();
            }
        }
        hideFragment(mPlayerManager.getCurrentFragment(),
                R.anim.host_player_push_up_in, R.anim.host_player_push_down_out);

        // 更新playBar状态
        if (mPlayBarFragment != null) {
            if (AccessibilityModeManager.INSTANCE.isAccessibilityMode() && mPlayBarFragment.getView() != null) {
                mPlayBarFragment.getView().setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_AUTO);
            }
        }
        refreshPlayBar();
        if (!mHadHandlePushLogicFinished && (mFromSkipPush || mFromItingDirectLanding || mFromPushIgnoreHome) && !isDoOnCreate) {
            Logger.d("pushTag", "hidePlayFragmentWillShow");
            HandlerManager.postOnUIThread(this::continueHomeLogicAfterPush);
            HandlerManager.postOnUIThreadDelay(this::continueHomeLogicAfterPush, 3000); // 兜底防白屏
        }
    }

    /**
     * for xdcs
     */
    public void startFragment(Fragment fra, View fromView) {
        startFragment(fra, fromView, 0, 0);
    }

    /**
     * for xdcs
     */
    public void startFragment(Fragment fra, View fromView, int inAim, int outAim) {
        if (mManageFragment != null) {
            forceAddManageFragment();
            hidePlayFragment(fra);
            mManageFragment.startFragment(fra, fromView, inAim, outAim);
        }
    }

    private void forceAddManageFragment() {
        if (!mManageFragment.isAdded()) {
            replaceFragment(R.id.fragment_full, mManageFragment);
        }
    }

    /**
     * for xdcs
     */
    @Deprecated
    public Fragment startFragment(Class<?> className, Bundle bundle,
                                  View fromView) {
        checkAndInitManageAndPlayFragment();
        if (bundle == null) {
            bundle = new Bundle();
        }
        if (mManageFragment != null) {
            forceAddManageFragment();
            Fragment f = null;
            try {
                if (Fragment.class.isAssignableFrom(className)) {
                    if (className == NativeHybridFragment.class) {
                        f = NativeHybridFragment.newInstance(bundle);
                    }
                    if (f == null) {
                        f = (Fragment) className.newInstance();
                        hidePlayFragment(f);
                    }
                } else {
                    return null;
                }
            } catch (Exception e) {
                return null;
            }
            return mManageFragment.startFragment(f, bundle, fromView);
        }
        return null;
    }

    /**
     * for xdcs
     */
    @Deprecated
    public Fragment startFragment(Class<?> className, Bundle bundle,
                                  View fromView, int inAim, int outAim) {
        checkAndInitManageAndPlayFragment();
        if (bundle == null) {
            bundle = new Bundle();
        }
        if (mManageFragment != null) {
            forceAddManageFragment();
            Fragment f = null;
            try {
                if (Fragment.class.isAssignableFrom(className)) {
                    if (className == NativeHybridFragment.class) {
                        f = NativeHybridFragment.newInstance(bundle);
                    }

                    if (f == null) {
                        f = (Fragment) className.newInstance();
                        hidePlayFragment(f);
                    }
                } else {
                    return null;
                }
            } catch (Exception e) {
                return null;
            }
            return mManageFragment.startFragment(f, bundle, fromView, inAim,
                    outAim);
        }
        return null;
    }

    public Fragment startFragment(Class<?> className, Bundle bundle) {
        checkAndInitManageAndPlayFragment();
        if (mManageFragment != null) {
            forceAddManageFragment();
            Fragment f = null;
            try {
                if (Fragment.class.isAssignableFrom(className)) {
                    if (className == NativeHybridFragment.class) {
                        f = NativeHybridFragment.newInstance(bundle);
                    }
                    if (f == null) {
                        f = (Fragment) className.newInstance();
                        hidePlayFragment(f);
                    }
                } else {
                    return null;
                }
            } catch (Exception e) {
                return null;
            }
            return mManageFragment.startFragment(f, bundle);
        }
        return null;
    }

    public Fragment startFragment(Class<?> className, String tag, Bundle bundle, int inAim, int outAim) {
        checkAndInitManageAndPlayFragment();
        if (mManageFragment != null) {
            forceAddManageFragment();
            Fragment f = null;
            try {
                if (Fragment.class.isAssignableFrom(className)) {
                    if (className == NativeHybridFragment.class) {
                        f = NativeHybridFragment.newInstance(bundle);
                    }
                    if (f == null) {
                        f = (Fragment) className.newInstance();
                        hidePlayFragment(f);
                    }
                } else {
                    return null;
                }
            } catch (Exception e) {
                return null;
            }
            return mManageFragment.startFragment(f, bundle, tag, inAim, outAim);
        }
        return null;
    }

    public void startFragment(Fragment fra, int inAnim, int outAnim) {
        startFragment(fra, "", inAnim, outAnim);
    }

    public void startFragment(Fragment fra, String tag, int inAnim, int outAnim) {
        startFragment(fra, tag, inAnim, outAnim, false);
    }

    public void startFragment(Fragment fra, String tag, int inAnim, int outAnim , boolean keepPlayFra) {
        if (Looper.getMainLooper() != Looper.myLooper()) {
            String finalTag = tag;
            runOnUiThread(() -> {
                if (canUpdateUi()) {
                    startFragment(fra, finalTag, inAnim, outAnim);
                }
            });
            return;
        }
        checkAndInitManageAndPlayFragment();
        if (TextUtils.isEmpty(tag)) { // 如果为""，设置为null
            tag = null;
        }

        Logger.log("MainActivity acitivityLife : startFragment " + mManageFragment);
        if (mManageFragment != null) {
            forceAddManageFragment();
            CustomTipsView.onStartFragment(); //防止切换页面后tips突然显示出来
            if (!keepPlayFra) {
                hidePlayFragment(fra);
            }
            try {
                mManageFragment.startFragment(fra, tag, inAnim, outAnim);
            } catch (Exception e) {
                if (!mHasCatchManageFragmentError) {
                    mHasCatchManageFragmentError = true;
                    String finalTag = tag;
                    HandlerManager.postOnUIThreadDelay(() -> {
                        if (canUpdateUi()) {
                            startFragment(fra, finalTag, inAnim, outAnim);
                        }
                    }, 100);
                } else {
                    XDCSCollectUtil.statErrorToXDCS("ManageFragment", "manageFragmentError____" + Log.getStackTraceString(e));
                    throw new RuntimeException(e);
                }
            }
        }
    }

    private boolean mHasCatchManageFragmentError = false;

    public void startFragment(Fragment fra) {
        startFragment(fra, 0, 0);
        if (Logger.isDebug) {
            Logger.logToSd("startfragment " + Log.getStackTraceString(new Throwable()));
        }
    }

    /**
     * 显示前一个Fragment 仅限在ManageFragment中的
     */
    public void showPreFragment(boolean isFromPlayFragment, boolean isOnlyShowView) {
        if (mManageFragment != null && mManageFragment.isAdded()) {
            mManageFragment.showPreFragment(isFromPlayFragment, isOnlyShowView);
        }
    }


    /**
     * 隐藏前一个Fragment 仅限在ManageFragment中的
     */
    public void hidePreFragment(boolean isFromPlayFragment, boolean isOnlyHideView) {
        if (mManageFragment != null && mManageFragment.isAdded()) {
            mManageFragment.hidePreFragment(isFromPlayFragment, isOnlyHideView);
        }
    }

    public Fragment getCurrentFragmentInManage() {
        if (mManageFragment != null && mManageFragment.isAdded()) {
            return mManageFragment.getCurrentFragment();
        }
        return null;
    }

    @Nullable
    public ManageFragment getManageFragment() {
        return mManageFragment;
    }

    /**
     * 判断播放钮是否处于显示状态
     */
    public boolean playButtonIsShow() {
        return mPlayButtonIsShow;
    }

    private float mLastPlayBarBottomMargin = 0;
    private int mLastBottomBarMarginX = 0;
    private View mSafeArea;
    private boolean mLastPlayBarInMainBottomTab = false;

    public void changePlayButtonBottomMargin(boolean isInMainBottomTab, float playButtonBottomMargin) {
        if (!canUpdateUi()) {
            return;
        }
        if (!mPlayButtonIsShow) {
            PlayBarAbManager.INSTANCE.obtainMainHandler().postDelayed(() -> ViewStatusUtil.setVisible(View.GONE, mSafeArea), 200);
            return;
        }
        if (isInMainBottomTab && !isManagerEmpty()) {
            // 兼容从首页扫码进落地页，落地页resume之后会调HomaPage的resume的case
            return;
        }
        PlayBarAbManager.INSTANCE.obtainMainHandler().postDelayed(() -> ViewStatusUtil.setVisible(isInMainBottomTab || playButtonBottomMargin > 0 ? View.GONE : View.VISIBLE, mSafeArea), 200);
        if (mLastPlayBarBottomMargin == playButtonBottomMargin && mLastPlayBarInMainBottomTab == isInMainBottomTab) {
            return;
        }
//        Logger.e("sjc", "changePlayButtonBottomMargin  = " + playButtonBottomMargin  + "   ,isInMainBottomTab = " + isInMainBottomTab);
        View layout = findViewById(R.id.fragment_playbar_new);
        if (layout == null) {
            return;
        }
        mLastPlayBarInMainBottomTab = isInMainBottomTab;
        mLastPlayBarBottomMargin = playButtonBottomMargin;
        int mBottomTabHeight = mRadioGroup != null ? mRadioGroup.getHeight() : 0;
        Logger.i(AutoSizeLog.TAG, "bottom size: " + mBottomTabHeight);
        if (mBottomTabHeight > 0) {
            PlayBarAbManager.INSTANCE.setMainBottomTabHeight(mBottomTabHeight);
        } else {
            mBottomTabHeight = PlayBarAbManager.INSTANCE.getMainBottomTabHeight();
        }
        float translationY = 0;
        if (isInMainBottomTab) {
            translationY = -playButtonBottomMargin;
        } else {
            translationY = mBottomTabHeight - playButtonBottomMargin;
            if (playButtonBottomMargin == 0) {
                translationY = translationY - BaseUtil.dp2px(this, 12);
            }
        }
        ObjectAnimator translationYAnimator = ObjectAnimator.ofFloat(layout, "translationY", translationY);
        translationYAnimator.setDuration(60);
        translationYAnimator.setStartDelay(240);
        translationYAnimator.start();
    }

    public void startMovePlayButtonX(float moveX) {
        if (mPlayButtonIsShow && PlayBarAbManager.INSTANCE.useNewPlayBar()) {
            if (moveX == -BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext()) && moveX != 0) {
                moveX = 0;
            }
            int moveXTemp = (int) moveX;
            if (moveXTemp == mLastBottomBarMarginX) {
                return;
            }
            mLastBottomBarMarginX = moveXTemp;
            View layout = findViewById(R.id.fragment_playbar_new);
            if (layout == null) {
                return;
            }
            layout.scrollTo(moveXTemp, 0);
        }
    }

    public void showPlayButton() {
        if (!mPlayButtonIsShow) {
//            Logger.e("sjc", "showPlayButton  = " + Log.getStackTraceString(new Throwable()));

            if (PlayBarAbManager.INSTANCE.useNewPlayBar()) {
                PlayBarAbManager.INSTANCE.obtainMainHandler().removeCallbacks(showPlayButtonRunnable);
                PlayBarAbManager.INSTANCE.obtainMainHandler().postDelayed(showPlayButtonRunnable, 300);
            } else {
                View layout = findViewById(R.id.fragment_playbar);
                ObjectAnimator.ofFloat(layout, "translationY", 0).start();
            }
            mPlayButtonIsShow = true;
            refreshPlayBarPlaying(XmPlayerManager.getInstance(MainActivity.this).isPlaying());
        }
    }

    private Runnable showPlayButtonRunnable = () -> {
        View layout = findViewById(R.id.fragment_playbar_new);
        if (layout == null) {
            return;
        }
        layout.scrollTo(0, 0);
    };

    public void hidePlayButton() {
        if (mPlayButtonIsShow) {
//            Logger.e("sjc", "hidePlayButton  = " + Log.getStackTraceString(new Throwable()));
            View layout = null;
            if (PlayBarAbManager.INSTANCE.useNewPlayBar()) {
                PlayBarAbManager.INSTANCE.obtainMainHandler().removeCallbacks(showPlayButtonRunnable);
                layout = findViewById(R.id.fragment_playbar_new);
                if (layout == null) {
                    return;
                }
                layout.scrollTo(0, BaseUtil.getScreenHeight(BaseApplication.getMyApplicationContext()));
            } else {
                layout = findViewById(R.id.fragment_playbar);
                if (layout == null) {
                    return;
                }
                ObjectAnimator animator = ObjectAnimator.ofFloat(layout, "translationY", BaseUtil.getScreenHeight(this));
                animator.start();
            }
            mPlayButtonIsShow = false;
            refreshPlayBarPlaying(XmPlayerManager.getInstance(MainActivity.this).isPlaying());
        }
    }

    public void startAddToListenAnimator(String coverUrl) {
        if (PlayBarAbManager.INSTANCE.useNewPlayBar()) {
            return;
        }
        if (TextUtils.isEmpty(coverUrl)) {
            return;
        }
        if (mToAddCoverImageView == null) {
            ViewStub toListenVs = findViewById(R.id.host_to_listen_animation_vs);
            if (toListenVs == null) {
                return;
            }
            View toListenView = toListenVs.inflate();
            mToAddCoverImageView = toListenView.findViewById(R.id.host_to_listen_animation_iv);
        }
        if (mToAddCoverImageView == null) {
            return;
        }
        mToAddCoverImageView.playToListenAnimation(coverUrl);
    }

    public void startAddToListenAnimator(Bitmap bitmap) {
        if (PlayBarAbManager.INSTANCE.useNewPlayBar()) {
            return;
        }
        if (mToAddCoverImageView == null) {
            ViewStub toListenVs = findViewById(R.id.host_to_listen_animation_vs);
            if (toListenVs == null) {
                return;
            }
            View toListenView = toListenVs.inflate();
            mToAddCoverImageView = toListenView.findViewById(R.id.host_to_listen_animation_iv);
        }
        if (mToAddCoverImageView == null) {
            return;
        }
        mToAddCoverImageView.playToListenAnimation(bitmap);
    }

    public void startPlayBarAddToListenAnimator() {
        if (mPlayBarFragment != null) {
            mPlayBarFragment.playToListenAnimation();
        }
    }

    /**
     * 选择跳转到电台页面
     */
    public void switchLivePlay() {
        clearAllFragmentFromManageFragment();
        hidePlayFragmentWillShow(null, false);
        showFragmentInMainFragment(TabFragmentManager.TAB_HOME_PAGE, null);
        try {
            if (tabFragmentManager != null) {
                Fragment fragment = tabFragmentManager.getCurrFragment();
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().switchChildTabInFindingFragment(fragment, TabFragmentManager.INTENT_CHILD_TAB_LIVE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 跳转到发现页. tabType取值：TabFragmentManager.FINDING_TAB_*
    public void switchHomeFindingTargetTab(String tabType) {
        if (TextUtils.isEmpty(tabType)) {
            return;
        }
        clearAllFragmentFromManageFragment();
        hidePlayFragment(null);
        showFragmentInMainFragment(TabFragmentManager.TAB_FINDING, null);
        try {
            Router.getActionByCallback(Configure.BUNDLE_FEED, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.feedBundleModel.bundleName.equals(bundleModel.bundleName)) {
                        try {
                            if (tabFragmentManager != null) {
                                Fragment fragment = tabFragmentManager.getCurrFragment();
                                Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFragmentAction().switchChildTabInFindingFragment(fragment, tabType);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 跳转到myspace页面
     * <p>
     * 请使用gotoMySpacePage方法代替，包含abtest处理
     */
    @Deprecated
    public void switchMySpace() {
//        if (!playButtonIsShow()) {
//            showPlayButton();V
//        }
//        showFragmentInMainFragment(TabFragmentManager.TAB_MY, null);
        gotoMySpacePage(null);
    }

    /**
     * 没有历史记录跳转到推荐声音列表
     */
    public void showNoHistoryRecommentTrackList() {
        if (ElderlyModeManager.getInstance().isElderlyMode()) {
            ElderlyModeManager.getInstance().gotoElderlyRankFragment();
            return;
        }
        try {
            NativeHybridFragment.start(this, "iting://open?msg_type=366", false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        try {
            Router.getActionByCallback(Configure.BUNDLE_LOGIN, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.loginBundleModel.bundleName.equals(bundleModel.bundleName) &&
                            ToolUtil.activityIsValid(MainActivity.this)) {
                        try {
                            Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFunctionAction().
                                    wmwbAccessManagerAuthorizeCallBack(MainActivity.this,
                                            requestCode, resultCode, data);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

        dispatchResult(requestCode, resultCode, data);

        //从发送短信页面跳转回来,通知share的回调函数
        if (requestCode == AppConstants.REQUEST_CODE_MESSAGE_SHARE) {
            ShareResultManager.getInstance().shareFinish(ShareConstants.SHARE_STRING_MESSAGE, true);
        }
//2选择头像
        //1拍照
        if (mPhotoAction != null) {
            if (requestCode == IPhotoAction.IMAGE_FROM_CAMERA) {
                if (resultCode == Activity.RESULT_OK) {
                    mPhotoAction.catchPhoto(requestCode, data);
                } else if (resultCode == Activity.RESULT_CANCELED) {
                    mPhotoAction.canceled();
                }
            } else if (requestCode == IPhotoAction.IMAGE_FROM_PHOTOS) {
                if (resultCode == Activity.RESULT_OK) {
                    mPhotoAction.catchPhoto(requestCode, data);
                } else if (resultCode == Activity.RESULT_CANCELED) {
                    mPhotoAction.canceled();
                }
            } else if (requestCode == IPhotoAction.IMAGE_CROP) {
                Logger.e(TAG, "看看返回的东西   " + "   resultCode = " + resultCode
                        + "   " + mPhotoAction);
                if (resultCode == Activity.RESULT_OK) {
                    mPhotoAction.cropPhoto();
                } else if (resultCode == Activity.RESULT_CANCELED) {
                    mPhotoAction.canceled();
                }
            } else if (requestCode == IPhotoAction.VIDEO_FROM_CAMERA) {
                if (resultCode == Activity.RESULT_OK) {
                    mPhotoAction.catchPhoto(requestCode, data);
                } else if (resultCode == Activity.RESULT_CANCELED) {
                    mPhotoAction.canceled();
                }
            }
        }
        if (requestCode == Constants.REQUEST_QQ_SHARE || requestCode == Constants.REQUEST_QZONE_SHARE) {// 分享到qq或者空间时回调需要
            IUiListener qqShareListener = QQShareHelper.getIUiListener();
            if (qqShareListener != null) {
                Tencent.onActivityResultData(requestCode, resultCode, data, qqShareListener);
            }
        } else if (requestCode == Constants.REQUEST_LOGIN) { // 绑定qq时 回调需要
            Tencent.onActivityResultData(requestCode, resultCode, data, null);
        }

        if (requestCode == IBindAction.REQUEST_BIND_SINA
                || requestCode == IBindAction.REQUEST_BIND_QQ) {
            if (resultCode == Activity.RESULT_OK) {
                for (IBindAction action : mBindActionList) {
                    action.refreshStatus();
                }
            }
        }
        if (mPaymentAction != null) {
            mPaymentAction.onPayResult(this, requestCode, resultCode, data);
        }
        if (mUnionPayActionListener != null) {
            if (requestCode == IUnionPayAction.REQUEST_CODE_UNION_PAY) {
                mUnionPayActionListener.unionPayResult(requestCode, resultCode, data);
                mUnionPayActionListener = null;
            }
        }

        // 从资源位进入养成活动时，退出时自动退出至游戏中心
        if ((ThirdGameAdConstants.THIRD_GAME_REQUEST_CODE == requestCode) && (data != null)) {
            autoJumpToGameCenter(data);
        }

        // 在智能硬件H5页面提示打开蓝牙，系统蓝牙弹窗回调处理
        if (requestCode == AppConstants.REQUEST_CODE_ENABLE_BLUETOOTH) {
            if (mManageFragment != null) {
                Fragment currentFragment = mManageFragment.getCurrentFragment();
                if (currentFragment instanceof NativeHybridFragment) {
                    currentFragment.onActivityResult(requestCode, resultCode, data);
                }
            }
        }

//        if (requestCode == XmThirdAppAuthorizeActivity.THIRD_APP_AUTHORIZE_REQUEST_CODE) {
//            setResult(resultCode, data);
//            finish();
//        }

        if (requestCode == MinorsModeHelper.REQUEST_PARENTAL_CREDENTIAL_CONFIRM) {
            MinorsModeHelper.INSTANCE.onActivityResult(requestCode, resultCode, data);
        }
    }

    private void autoJumpToGameCenter(Intent data) {
        boolean isNeedJumpToGameCanter = data.getBooleanExtra(ThirdGameAdConstants.AUTO_JUMP_GAME_CENTER, false);
        if (!isNeedJumpToGameCanter) {
            return;
        }
        AdGameUtil.autoJumpToGameCenter();
    }

    @SuppressLint("RestrictedApi")
    private void dispatchResult(int requestCode, int resultCode, Intent data) {
        FragmentManager fm = getSupportFragmentManager();
        int index = requestCode >> 16;
        if (index != 0) {
            index--;
            if (fm.getFragments() == null || index < 0
                    || index >= fm.getFragments().size()) {
                return;
            }
            Fragment frag = fm.getFragments().get(index);
            if (frag != null) {
                handleResult(frag, requestCode, resultCode, data);
            }
        }
    }

    /**
     * 递归调用，对所有子Fragement生效
     */
    private void handleResult(Fragment frag, int requestCode, int resultCode,
                              Intent data) {
        frag.onActivityResult(requestCode & 0xffff, resultCode, data);
        @SuppressLint("RestrictedApi") List<Fragment> frags = frag.getChildFragmentManager().getFragments();
        for (Fragment f : frags) {
            if (f != null)
                handleResult(f, requestCode, resultCode, data);
        }
    }

    public void addPhotoActionListener(IPhotoAction listener) {
        mPhotoAction = listener;
    }

    public void removePhotoActionListener(IPhotoAction listener) {
        if (mPhotoAction == listener) {
            mPhotoAction = null;
        }
    }

    public void addPayActionListener(IPaymentAction listener) {
        if (listener != null) {
            mPhotoAction = null;
            mPaymentAction = listener;
        }
    }

    public void setUnionPayActionListener(IUnionPayAction listener) {
        mUnionPayActionListener = listener;
    }

    public void removePayActionListener(IPaymentAction listener) {
        if (mPaymentAction == listener) {
            mPaymentAction = null;
        }
    }

    public void addBindActionListener(IBindAction listener) {
        if (!mBindActionList.contains(listener)) {
            mBindActionList.add(listener);
        }
    }

    public void removeBindActionListener(IBindAction listener) {
        if (mBindActionList != null) {
            mBindActionList.remove(listener);
        }
    }

    @Override
    public boolean onMenuOpened(int featureId, @NonNull final Menu menu) {

        if (isFinishing())
            return false;

        if (mTabMenu == null) {
            mTabMenu = new TabMenu(getApplicationContext());
            mTabMenu.setMenuTitle(mMenuTitle);
            mTabMenu.setMenuIcon(mMenuIcon);
            mTabMenu.setOnItemClickListener(new OnItemClickListener() {

                @Override
                public void onItemClick(AdapterView<?> parent, View view,
                                        int position, long id) {
                    switch (position) {
                        case 0: // history
                            MyListenRouterUtil.getMyListenBundle(bundleModel -> {
                                IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                                if (fragAction != null && canUpdateUi()) {
                                    BaseFragment frag = fragAction.newHistoryFragment(true, false, true, BundleKeyConstants.KEY_MYLISTEN_IS_FROM_OTHER);
                                    if (frag != null) {
                                        startFragment(frag);
                                    }
                                }
                            });
                            break;
                        case 1: // exit
                            if (mPlanTerminateFragmentWR == null || mPlanTerminateFragmentWR.get() == null) {
                                BaseDialogFragment fragment = (BaseDialogFragment) getSupportFragmentManager().findFragmentByTag("PlanTerminateFragment");
                                if (fragment != null) {
                                    mPlanTerminateFragmentWR = new WeakReference<>(fragment);
                                }
                            }
                            if (mPlanTerminateFragmentWR == null) {
                                try {
                                    BaseDialogFragment fragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newPlanTerminateFragment();
                                    mPlanTerminateFragmentWR = new WeakReference<>(fragment);
                                    if (fragment.isAdded()) {
                                        return;
                                    }
                                    fragment.show(getSupportFragmentManager(), "PlanTerminateFragment");
                                    SharedPreferencesUtil.getInstance(getApplicationContext()).
                                            saveBoolean(PreferenceConstantsInHost.TINGMAIN_KEY_IS_FROM_MENU_TAB, true);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                            if (mPlanTerminateFragmentWR != null && mPlanTerminateFragmentWR.get() != null) {
                                if (!mPlanTerminateFragmentWR.get().isAdded()) {
                                    try {
                                        mPlanTerminateFragmentWR.get().show(getSupportFragmentManager(), "PlanTerminateFragment");
                                        SharedPreferencesUtil.getInstance(getApplicationContext()).
                                                saveBoolean(PreferenceConstantsInHost.TINGMAIN_KEY_IS_FROM_MENU_TAB, true);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                            }
                            break;
                        case 2: // delete
                            try {
                                startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newFragmentByFid(Configure.MainFragmentFid.DOWNLOAD_CACHE_FRAGMENT));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case 3: // network setting
                            try {
                                startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newFragmentByFid(Configure.MainFragmentFid.ALARM_SET_FRAGMENT));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case 4: // update
                            updateManager.checkUpdata(view, false);
                            break;
                        case 5: // exit
                            finishMy();
                            break;
                        default:
                            break;
                    }
                    mTabMenu.dismiss();
                }
            });
            mTabMenu.setBackground(R.color.host_black_1);
            mTabMenu.setMenuSelector(R.color.host_transparent);
        }
        if (mTabMenu.isShowing()) {
            mTabMenu.dismiss();
        } else {
            if (PlayBarAbManager.INSTANCE.useNewPlayBar()) {
                if (mPlayButtonImageNew != null) {
                    mTabMenu.showAt(mPlayButtonImageNew);
                }
            } else {
                if (mPlayButtonImage != null) {
                    mTabMenu.showAt(mPlayButtonImage);
                }
            }
        }
        return false;
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time) + " , type = MainActivity onCheckedChanged");
        // 青少年模式下，不能显示发现页,vip页
        if (ChildProtectManager.isChildMode(this) && ((checkedId == R.id.tab_finding && !DyncAbTestUtil.isFeed2Subscribe()) || checkedId == R.id.tab_vip
                || checkedId == R.id.tab_category)) {
            if (mLastCheckedBottomTab != null) {
                group.check(mLastCheckedBottomTab.getId());
            }
            ChildProtectManager.showFeatureCannotUseToast();
            return;
        }
        if (ElderlyModeManager.getInstance().isElderlyMode()) {
            //老年模式不显示正常模式的页面
            return;
        }

        boolean clickQuickListenTab = mRbQuickListen != null && checkedId == mRbQuickListen.getId();
        if (clickQuickListenTab && QuickListenTabAbManager.showAIListenTab()) {
            if (mLastCheckedBottomTab != null) {
                group.check(mLastCheckedBottomTab.getId());
            }
            QuickListenTabAbManager.INSTANCE.go2QuickListenMainPage(this, "底tab");
            return;
        }

        if (checkedId == R.id.tab_finding) {
            if(mSubscribeRedDotPosition != null && mSubscribeRedDotPosition.isShowRedDot()) {
                new XMTraceApi.Trace()
                        .click(59651) // 用户点击时上报
                        .put("currPage", "forAll")
                        .put("xmRequestId", "")
                        .put("guideType", "引导")
                        .put("tabName", "订阅")
                        .createTrace();
            }
            FeedTraceUtils.clearTrace();
            FeedTraceUtils.isFeedTabClicked = true;
            FeedTraceUtils.clickTime = DateTimeUtil.long2String(System.currentTimeMillis(), "MM月dd日 HH:mm:ss");
            FeedTraceUtils.appendTraceStep("发现页checked");
            NoReadManage.getInstance(getContext()).homeUnRead.setUnreadSubscribeCountV2(0);
//            NoReadManage.getInstance(getContext()).homeUnRead.setSubscribeHasNew(false);
            updateSubscribeTabRedDot();
        }

        traceTabRedDotOnClickEvent(checkedId);

        if (checkedId == R.id.tab_finding) {
            DynamicTabRedHintManager.INSTANCE.resetHintDataOnClick();
        } else {
            DynamicTabRedHintManager.INSTANCE.startCountDown();
        }

        Object obj = group.getTag();
        if (tabFragmentManager != null) {
            tabFragmentManager.showFragment(checkedId, obj != null ? obj : mSavedBundle);
        }
        group.setTag(null);
        mSavedBundle = null;

        if (checkedId == R.id.tab_myspace_and_listen) {
            if (MyListenAbUtil.INSTANCE.getSlideExperienceFlag() != 3 && MyListenAbUtil.INSTANCE.getSlideExperienceFlag() != 4) {
                onUnreadMessageAdd(0, true);
            }
            MainTabClickHelper.INSTANCE.onMineClick();
            if (UserInfoMannage.hasLogined()) {
                SharePosterManager.getInstance().tryShowMileStoneDialog(this);
            } else {
                if (clickTabShowLogin && !VersionUtil.isNewUser() && !MyListenAbUtil.INSTANCE.isKeepSubscribeInMine()) {
                    UserInfoMannage.hasGotoLoginByMine = true;
                    UserInfoMannage.gotoLogin(this, LoginByConstants.LOGIN_BY_MINE_CLICK);
                }
            }

        } else {
            UserInfoMannage.hasGotoLoginByMine = false;
        }

        // 这里替换首页的tab为非刷新状态的drawable
//        resetHomeRbToDrawable(mLastCheckedBottomTab, mBottomHomePageTabDrawable);

        if (mLastCheckedBottomTab != null) {
            mLastCheckedBottomTab.setTypeface(Typeface.DEFAULT);
            Drawable drawable = mLastCheckedBottomTab.getCompoundDrawables()[1];
            if (mLastCheckedBottomTab == mRbHomePage) {
                // 首页要回到后台，如果还是刷新的Drawable 则说明正在刷新
                if (drawable != null && drawable == mBottomHomePageRefreshDrawable) {
                    Logger.d(BOTTOM_TAG, "MainActivity onCheckedChanged: " + mHasSetHomePageTabAsRefreshBtn);
                    setHomePageTabAsRefreshBtn(false);
                    drawable = mLastCheckedBottomTab.getCompoundDrawables()[1];
                }
            }
            if (drawable instanceof LottieDrawable) {
                ((LottieDrawable) drawable).cancelAnimation();
                ((LottieDrawable) drawable).setProgress(0);
            }
        }
        RadioButton radioButton = group.findViewById(checkedId);
        radioButton.setTypeface(Typeface.create("sans-serif-light", Typeface.BOLD));
        mLastCheckedBottomTab = radioButton;

        Logger.d(BOTTOM_TAG, "MainActivity onCheckedChanged: " + (radioButton.getId()));

        // 保证首页tab是默认的drawable
//        resetHomeRbToDrawable(radioButton, mBottomHomePageTabDrawable);

        if (radioButton != null) {
            Drawable drawable = radioButton.getCompoundDrawables()[1];
            if (drawable instanceof StateListDrawable && drawable.getCurrent() instanceof AnimationDrawable) {
                ((AnimationDrawable) drawable.getCurrent()).stop();
                ((AnimationDrawable) drawable.getCurrent()).start();
            } else if (drawable instanceof LottieDrawable) {
                if (drawable == mBottomHomePageRefreshDrawable) {
                    ((LottieDrawable) drawable).cancelAnimation();
                    ((LottieDrawable) drawable).setProgress(0f);
                } else {
                    ((LottieDrawable) drawable).cancelAnimation();
                    ((LottieDrawable) drawable).playAnimation();
                }
            }
        }

        if (tabFragmentManager != null) {
            SharedPreferencesUtil.getInstance(this).saveInt(PreferenceConstantsInHost.TINGHOST_KEY_LAST_TAB_INDEX,
                    tabFragmentManager.getCurrentTabIndex(checkedId));
        }
    }

    public void saveHasShowBottomTabTagState() {
        if (mActivityTabManager != null) {
            TextView textView = mActivityTabManager.getFreeListenerMakeView();
            if (textView != null && textView.getVisibility() == View.VISIBLE) {
                hideUserBottomTabTag();
                MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInHost.KEY_USER_BOTTOM_TAB_TAG_HAS_SHOW + mUserTabTagId + "", true);
                foreachBottomTagUpdateUI();
            }
        }
    }

    private boolean getBottomTabTagHasShow(int id) {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost.KEY_USER_BOTTOM_TAB_TAG_HAS_SHOW + id + "", false);
    }

    private static final String BUNDLE_TAG = "MainActivityBundle";

    // 切换首页青少年模式UI
    public void switchHomePageTeenMode(boolean isOpen) {
        if (!canUpdateUi()) {
            return;
        }

        boolean useNewStyle = TeenModeManager.INSTANCE.isUseNewTeenModeStyle();
        if (!useNewStyle) return; // 配置关闭，不执行后续方法

        // 当前tab非首页时不处理
        if (tabFragmentManager == null || TabFragmentManager.TAB_HOME_PAGE != tabFragmentManager.getCurrentTab()) {
            return;
        }

        Fragment currFragment = tabFragmentManager.getCurrFragment();
        if (currFragment != null) {
            // 青少年模式已打开，但首页非TeenHomePageFragment
            if (isOpen && !(currFragment instanceof IKidFunctionAction.AbstractTeenHomePageFragment)) {
                tabFragmentManager.showFragment(TabFragmentManager.TAB_HOME_PAGE, null);
                return;
            }

            // 青少年模式已关闭，但首页非HomePageFragment
            if (!isOpen && !(currFragment instanceof IMainFunctionAction.AbstractHomePageFragment)) {
                tabFragmentManager.showFragment(TabFragmentManager.TAB_HOME_PAGE, null);
            }
        }
    }

    public void goHome() {
        clearAllFragmentFromManageFragment();
        showFragmentInMainFragment(TabFragmentManager.TAB_HOME_PAGE, null);
        if (!playButtonIsShow()) {
            showPlayButton();
        }
    }

    public void goFreeListenTab(String fromSource) {
        if (NovelTabAbManager.INSTANCE.showNovelTab()) {
            clearAllFragmentFromManageFragment();
            hidePlayFragment(null);
            HandlerManager.postOnUIThreadDelay(() -> {
                if (tabFragmentManager != null && TabFragmentManager.TAB_VIP == tabFragmentManager.getCurrentTab()) {
                    if (tabFragmentManager != null) {
                        tabFragmentManager.showFragment(TabFragmentManager.TAB_VIP, null);
                    }
                } else {
                    showFragmentInMainFragment(TabFragmentManager.TAB_VIP, null);
                }
                if (!playButtonIsShow()) {
                    showPlayButton();
                }
                updateBottomTabResource();
            }, 100);
        } else {
            NovelTabAbManager.INSTANCE.go2SingleNovelPage(this);
        }
    }

    public void gotoListen() {
        gotoListen((Bundle) null);
    }

    public void gotoListen(int pos, String secTab) {
        Bundle bundle = new Bundle();
        if (pos >= 0) {
            bundle.putInt(BundleKeyConstants.KEY_DESTINATION, pos);
            if (secTab != null) {
                bundle.putString(BundleKeyConstants.KEY_SECOND_DESTINATION, secTab);
            }
        }
        gotoListen(bundle);
    }

    public void gotoListen(Bundle bundle) {
        clearAllFragmentFromManageFragment();
        hidePlayFragment(null);

        if (bundle == null) {
            bundle = new Bundle();
        }
        Bundle finalBundle = bundle;
        HandlerManager.postOnUIThreadDelay(() -> {
            if (tabFragmentManager != null && TabFragmentManager.TAB_MY_AND_LISTEN == tabFragmentManager.getCurrentTab()) {
                if (tabFragmentManager != null) {
                    tabFragmentManager.showFragment(TabFragmentManager.TAB_MY_AND_LISTEN, finalBundle);
                }
            } else {
                showFragmentInMainFragment(TabFragmentManager.TAB_MY_AND_LISTEN, finalBundle);
            }
            if (!playButtonIsShow()) {
                showPlayButton();
            }
        }, 100);
    }

    /**
     * NOTE:该方法是跳转到个人中心页面，并不是选中"我的"tab，
     * NOTE:传入bundle请勿使用"intent_child_tab"作为key
     * <p>
     * abtest三种不同的"个人中心展示"位置:
     * 1.a方案直接切换到"我的"
     * 2.b方案直接切换到"我的"
     * 3.c方案切换到"我的"并选中"个人中心"tab
     *
     * @param bundle 支持传入bundle参数，可以在MineFragment和MySpaceFragment的onMyResume中进行参数处理
     */
    public void gotoMySpacePage(Bundle bundle) {
        if (ElderlyModeManager.getInstance().isElderlyMode()) {
            ElderlyModeManager.getInstance().gotoElderlyMineFragment();
            return;
        }
        clearAllFragmentFromManageFragment();

        showFragmentInMainFragment(TabFragmentManager.TAB_MY_AND_LISTEN, bundle);

        if (!playButtonIsShow()) {
            showPlayButton();
        }
    }

    public void checkRadio(final int checkedId, Bundle bundle) {
        if (mRadioGroup == null
                || mRadioGroup.findViewById(checkedId) == null || !isRunShowDefaultBottomTab) {
            // 未选中默认tab则记录当前选中tab,等待执行默认tab api
            if (bundle != null) { //app关闭的情况下，外站iting打开，会丢失bundle
                mSavedBundle = bundle;
            }
            // 改成：如果命中快听实验，第二个显示快听，第三个显示VIP或免费听。如果没命中快听实验，第二个显示VIP或免费听，第三个显示分类
            if (checkedId == TabFragmentManager.TAB_VIP) {
                if (MyListenAbUtil.INSTANCE.isKeepSubscribeInMine() && QuickListenTabAbManager.showAIListenTab()) {
                    mPendingRadioIndexFromITing = 2;
                } else {
                    mPendingRadioIndexFromITing = 1;
                }
            } else if (checkedId == TabFragmentManager.TAB_CATEGORY) {
                if (MyListenAbUtil.INSTANCE.isKeepSubscribeInMine() && QuickListenTabAbManager.showAIListenTab()) {
                    mPendingRadioIndexFromITing = 1;
                } else {
                    mPendingRadioIndexFromITing = 2;
                }
            } else if (checkedId == TabFragmentManager.TAB_FINDING) {
                mPendingRadioIndexFromITing = 2;
            } else if (checkedId == TabFragmentManager.TAB_MY_AND_LISTEN) {
                mPendingRadioIndexFromITing = 3;
            } else {
                mPendingRadioIndexFromITing = -1;
            }
            return;
        }
        mRadioGroup.setTag(bundle);
        RadioButton button = mRadioGroup.findViewById(checkedId);
        setRadioButtonCheckedSafe(button, true);
    }

    private BottomTabBubblePopupWindow mBottomTabBubblePopupWindow;

    @Override
    public void update(NoReadModel noReadModel) {
        if (isFinishing() || noReadModel == null) {
            return;
        }
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                BottomTabManager bottomTabManager = BottomTabManager.INSTANCE;
                bottomTabManager.dealWithNoReadMessage(noReadModel);
                if (!bottomTabManager.getMShowImageBubble()) {
                    EventManager.getInstance().notifyEvent(new EventManager.Event("tab_image_tip_dismiss"));
                }
                //发现tab头像红点
                updateFindTabRedDot();
                updateMineListenTabRedDot(true, false);
            }
        });
    }

    @Override
    public void update(HomeUnRead homeUnRead) {
        if (isFinishing() || homeUnRead == null) {
            return;
        }
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                updateSubscribeTabRedDot();
            }
        });
    }

    public synchronized void showLockScreen() {
        if (mLockScreenFragment == null) {
            try {
                mLockScreenFragment = ((ICarFragmentAction) Router
                        .<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN)
                        .getFragmentAction())
                        .newFragmentByFid(Configure.CarFragmentFid.LOCK_SCREEN_FRAGMENT);
            } catch (Exception e) {
                e.printStackTrace();
                return;
            }
        }
        if (mLockScreenFragment == null) {
            return;
        }
        if (!mLockScreenFragment.isAddFix() && !isAddLockScreen) {
            isAddLockScreen = true;
            Logger.i("cf_test", "添加锁屏——————addFragmentToLayout");
            addCarloadLockScreenLayout();
            addFragmentToLayout(R.id.host_lock_screen, mLockScreenFragment,
                    R.anim.host_player_push_up_in, R.anim.host_player_push_down_out);
        } else {
            showFragment(mLockScreenFragment, R.anim.host_player_push_up_in,
                    R.anim.host_player_push_down_out);
        }
        mIsLockScreen = true;
    }

    private void addCarloadLockScreenLayout() {
        if (mMainActivityViewUtil == null) {
            mMainActivityViewUtil = new MainActivityViewUtil(this);
        }
        mMainActivityViewUtil.addCarLoadScreenLockView();
    }

    public synchronized void clearLockScreen() {
        if (mLockScreenFragment == null) {
            return;
        }
        if (!mLockScreenFragment.isAdded()) {
            return;
        }
        mLockScreenFragment.onPause();
        hideFragment(mLockScreenFragment, R.anim.host_player_push_up_in,
                R.anim.host_player_push_down_out);
        mIsLockScreen = false;
    }

    private void startWatchingExternalStorage() {
        RouteServiceUtil.getStoragePathManager().startWatchingExternalStorage();
    }

    private void stopWatchingExternalStorage() {
        RouteServiceUtil.getStoragePathManager().stopWatchingExternalStorage();
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        if (v.getId() == R.id.host_btn_top) {
            notifyBtnTopListener(v);
        } else {
            if (tabFragmentManager != null) {
                // 这里可以用mHasSetHomePageTabAsRefreshBtn，但是得在cancel中修改下，后期再优化
                boolean refreshState = mRbHomePage != null && "刷新".equals(mRbHomePage.getText());
                tabFragmentManager.refreshTab(v.getId(), refreshState);
            }
        }
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        if (level == ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL) {
            if (tabFragmentManager != null) {
                tabFragmentManager.removeFragmentByTrimMemory();
            }
        } else if (level == ComponentCallbacks2.TRIM_MEMORY_BACKGROUND) {
            if (mManageFragment != null) {
                mManageFragment.cleanFragmentsAsLowMemory();
            }
        } else if (level == ComponentCallbacks2.TRIM_MEMORY_MODERATE ||
                level == ComponentCallbacks2.TRIM_MEMORY_COMPLETE) {
            if (!BaseUtil.isForegroundIsMyApplication(this)) {
                finish();
            }
        }
        Logger.i(TAG, "onTrimMemory level = " + level);
    }

    public boolean playFragmentIsVis() {
        return mPlayerManager != null && mPlayerManager.isPlayFragmentVisable();
    }

    public boolean onKeyDown(int keyCode, android.view.KeyEvent event) {
        if (ConstantsOpenSdk.isDebug) {
            Log.d("ExitStatisticUtil", "onKeyDown: " + keyCode + ", KEYCODE_HOME: " + android.view.KeyEvent.KEYCODE_HOME);
        }
        if (keyCode == android.view.KeyEvent.KEYCODE_HOME) {
            ExitStatisticUtil.reportPressHome();
        }

        if (PlayTools.isDLNAState(getApplicationContext())) {
            switch (keyCode) {
                case android.view.KeyEvent.KEYCODE_VOLUME_DOWN:
                    WiFiDeviceController.volumeDown(getApplicationContext());
                    showVolumeDialog(false);
                    return true;
                case android.view.KeyEvent.KEYCODE_VOLUME_UP:
                    WiFiDeviceController.volumeUp(getApplicationContext());
                    showVolumeDialog(true);
                    return true;
                default:
                    break;
            }
        } else if (ReadVolumeInterceptManager.getInstance().readNeedInterceptVolumeAction()) {
            switch (keyCode) {
                case android.view.KeyEvent.KEYCODE_VOLUME_DOWN:
                    ReadVolumeInterceptManager.getInstance().dispatchVolumeDown();
                    return true;
                case android.view.KeyEvent.KEYCODE_VOLUME_UP:
                    ReadVolumeInterceptManager.getInstance().dispatchVolumeUp();
                    return true;
                default:
                    break;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    private void showVolumeDialog(boolean up) {
        if (up) {
            show(R.drawable.host_volumeincrease);
        } else {
            show(R.drawable.host_volumedecrease);
        }
    }

    private void show(int resID) {
        if (volumeToast == null) {
            volumeToast = new Toast(getApplicationContext());
            View layout = LayoutInflater.from(this).inflate(
                    R.layout.host_toast_volume, null);
            volumeToast.setView(layout);
            volumeToast.setGravity(Gravity.CENTER, 0, -200);
            volumeToast.setDuration(Toast.LENGTH_SHORT);
        }

        View view = volumeToast.getView();
        if (view != null) {
            ImageView iv = view.findViewById(R.id.main_volumnView1);
            iv.setImageResource(resID);
            volumeToast.show();
        }
    }

    @SuppressLint("RestrictedApi")
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (iKeyDispatch != null) {
            iKeyDispatch.dispatchKeyEvent(event);
        }

        return super.dispatchKeyEvent(event);
    }

//    public void showGuide(List<GuideModel> list) {
//        if (list != null && !list.isEmpty()) {
//            commonGuideFragment = CommonGuideFragment.newInstance(list, StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR);
//            commonGuideFragment.showGuide(this, R.id.fra_terminate_container, getSupportFragmentManager());
//        }
//    }

//    public void hideGuide() {
//        if (commonGuideFragment != null) {
//            commonGuideFragment.hideGuide(getSupportFragmentManager());
//            commonGuideFragment = null;
//        }
//    }

    /**
     * 引导页 begin
     */

    public void closeWebFragment() {
        closeWebFragment(false);
    }

    public void closeWebFragment(boolean doNotResumePlayFragment) {
        if (mManageFragment != null) {
            Fragment currentFragment = mManageFragment.getCurrentFragment();
            if (currentFragment instanceof NativeHybridFragment) {
                if (doNotResumePlayFragment) {
                    ((NativeHybridFragment) currentFragment).setUnderThisHasPlayFragment(false);
                }
                mManageFragment.showPreFragment(((BaseActivityLikeFragment) currentFragment).getUnderThisHasPlayFragment(), false);

                ((NativeHybridFragment) currentFragment).close();
            } else if (currentFragment instanceof AdSDKHybridFragment) {
                if (doNotResumePlayFragment) {
                    ((AdSDKHybridFragment) currentFragment).setUnderThisHasPlayFragment(false);
                }
                mManageFragment.showPreFragment(((BaseActivityLikeFragment) currentFragment).getUnderThisHasPlayFragment(), false);

                ((AdSDKHybridFragment) currentFragment).close();
            }
        }
    }

    /**
     * 引导页 end
     */

    public PlayBarFragment getPlayBarFragment() {
        return mPlayBarFragment;
    }

    public void refreshPlayBarPlayDirect() {
        if (mPlayBarFragment != null) {
            mPlayBarFragment.setPlayDirect();
        }
    }

    public void refreshPlayBarPlaying(boolean playing) {
        if (mPlayBarFragment != null) {
            mPlayBarFragment.refreshPlaying(playing);
        }
    }

    public void refreshPlayBar() {
        if (mPlayBarFragment != null) {
            mPlayBarFragment.refreshPlayBar();
        }
    }

    public void resetPlayBarCover() {
        if (mPlayBarFragment != null) {
            mPlayBarFragment.resetCoverView();
        }
    }

    public void refreshPlayBarByBook(LocalBookInfo localBookInfo) {
        if (mPlayBarFragment != null) {
            mPlayBarFragment.refreshPlayBarByBook(localBookInfo);
        }
    }

    public RadioGroup getRadioGroup() {
        return mRadioGroup;
    }

    public TabFragmentManager getTabFragmentManager() {
        return tabFragmentManager;
    }

    public UpdateManager getUpdateManager() {
        return updateManager;
    }

    /**
     * 播放页面是否可见，当前是否处于播放页面
     */
    public boolean isPlayFragmentVisible() {
        return mPlayerManager != null && mPlayerManager.isPlayFragmentVisable();
    }

    private Set<IGotoTop.IGotoTopBtnClickListener> mTopListener = new HashSet<>();
    private Set<IGotoTop.IGotoTopBtnClickVisibleListener> mTopVisibleListener = new HashSet<>();

    @Override
    public void reset() {
        if (mBtnTop == null) {
            return;
        }
        mBtnTop.setVisibility(View.GONE);
        notifyBtnTopVisibleChange(false);
    }

    @Override
    public void addOnClickListener(IGotoTop.IGotoTopBtnClickListener listener) {
        if (listener != null) {
            mTopListener.add(listener);
        }
    }

    @Override
    public void removeOnClickListener(IGotoTop.IGotoTopBtnClickListener listener) {
        if (listener != null) {
            mTopListener.remove(listener);
        }
    }

    @Override
    public void addVisibleListener(IGotoTopBtnClickVisibleListener listener) {
        if (null != listener) {
            mTopVisibleListener.add(listener);
        }
    }

    @Override
    public void removeVisibleListener(IGotoTopBtnClickVisibleListener listener) {
        if (null != listener) {
            mTopVisibleListener.remove(listener);
        }
    }

    @Override
    public void setState(boolean show) {
        if (!show && mBtnTop == null) {
            return;
        }
        checkIfNeedInitBtnTop();
        mBtnTop.setVisibility(show ? View.VISIBLE : View.GONE);
        notifyBtnTopVisibleChange(show);
    }

    private void checkIfNeedInitBtnTop() {
        if (mBtnTop != null) {
            return;
        }
        if (mMainActivityViewUtil == null) {
            mMainActivityViewUtil = new MainActivityViewUtil(this);
        }
        mMainActivityViewUtil.addGoTopView();
        mBtnTop = findViewById(R.id.host_btn_top);
        mBtnTop.setOnClickListener(this);
        AutoTraceHelper.bindData(mBtnTop, AutoTraceHelper.MODULE_DEFAULT, "");
    }

    private void notifyBtnTopListener(View v) {
        for (IGotoTop.IGotoTopBtnClickListener listener : mTopListener) {
            listener.onClick(v);
        }
    }

    private void notifyBtnTopVisibleChange(boolean isVisible) {
        for (IGotoTop.IGotoTopBtnClickVisibleListener listener : mTopVisibleListener) {
            listener.isVisible(isVisible);
        }
    }

    private final String SPKEY_IS_SHOWED_UPDATE_NOTIFICATION = "spkey_is_showed_update_notification";
    private Snackbar updateSnackBar;

    private void showUpdateNotification() {

        boolean isShowed = sp.getBoolean(SPKEY_IS_SHOWED_UPDATE_NOTIFICATION, true);
        if ((VersionUtil.isNewVersion()) || !isShowed) {
            if (!ViewUtil.haveDialogIsShowing(this)) {
                View contentView = LayoutInflater.from(this).inflate(R.layout.view_custom_notification, null);
                RoundImageView iv = contentView.findViewById(R.id.iv_notification_head);
                iv.setImageResource(R.drawable.main_new_version_notification_icon);
                TextView tvMainTitle = contentView.findViewById(R.id.tv_notification_title);
                tvMainTitle.setText("恭喜升级新版本");
                TextView tvSubTitle = contentView.findViewById(R.id.tv_notification_subtitle);
                tvSubTitle.setText("来看看有什么新功能吧～");

                updateSnackBar = Snackbar.with(this)
                        .customView(contentView)
                        .swapVertical()
                        .duration(Snackbar.SnackbarDuration.LENGTH_MUCH_LONG)
                        .position(Snackbar.SnackbarPosition.TOP)
                        .spaceColor(Color.parseColor("#E7000000"));
                contentView.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        PushManager.getInstance().traceShow(true);
                        //新版本介绍
                        String url = UrlConstants.getInstanse().getHybridHost()
                                + "hybrid/api/appFunctionIntro?version=" + DeviceUtil.getVersion(getApplicationContext())
                                + "&device=android&contentType=0";
                        Bundle bundle = new Bundle();
                        bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
                        bundle.putBoolean(IWebFragment.SHOW_SHARE_BTN, true);
                        startFragment(NativeHybridFragment.class, bundle, null);
                        updateSnackBar.dismiss();

                        new UserTracking("newVersionPush", "button")
                                .setItemId("推送")
                                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                    }
                });
                AutoTraceHelper.bindData(contentView, AutoTraceHelper.MODULE_DEFAULT, "");
                PushManager.getInstance().showPush(PushTask.create(PushTask.Type.APP_UPDATE)
                        .setSnackbar(updateSnackBar).setTitle("恭喜升级新版本"));
                sp.saveBoolean(SPKEY_IS_SHOWED_UPDATE_NOTIFICATION, true);
            } else {
                sp.saveBoolean(SPKEY_IS_SHOWED_UPDATE_NOTIFICATION, false);
            }
        }
    }

    /**
     * 重新设置的条件为:
     * 1. sp 数据为false 说明没有创建
     * 2. 应用升级后，快捷方式仍会存在，因此需要重新设置下
     */
    private void createShortcut() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
            if (!VersionUtil.hasUpgrade() && MmkvCommonUtil.getInstance(getApplicationContext()).getBooleanCompat(SHORT_CUT_HAS_CREATED, false)) {
                return;
            }
            ShortcutManager manager = getSystemService(ShortcutManager.class);
            if (manager == null) return;

            Intent aiSearchIntent = new Intent(Intent.ACTION_MAIN, Uri.parse("shortcut://aiSearch"), this, MainActivity.class);
            ShortcutInfo aiSearchShortcut = new ShortcutInfo.Builder(this, "aiSearch")
                    .setIcon(Icon.createWithResource(getApplicationContext(), R.drawable.host_shortcut_aisearch))
                    .setShortLabel(getString(R.string.host_shortcut_ai_search))
                    .setIntent(aiSearchIntent)
                    .setRank(0)
                    .build();

            Intent downloadIntent = new Intent(Intent.ACTION_MAIN, Uri.parse("shortcut://download"), this, MainActivity.class);
            ShortcutInfo downloadShortcut = new ShortcutInfo.Builder(this, "download")
                    .setIcon(Icon.createWithResource(getApplicationContext(), R.drawable.host_shortcut_download))
                    .setShortLabel(getString(R.string.host_shortcut_download))
                    .setIntent(downloadIntent)
                    .setRank(1)
                    .build();

            Intent subscribeIntent = new Intent(Intent.ACTION_MAIN, Uri.parse("shortcut://subscribe"), this, MainActivity.class);
            ShortcutInfo subscribeShortcut = new ShortcutInfo.Builder(this, "subscribe")
                    .setIcon(Icon.createWithResource(getApplicationContext(), R.drawable.host_shortcut_order))
                    .setShortLabel(getString(R.string.host_shortcut_subscribe))
                    .setIntent(subscribeIntent)
                    .setRank(2)
                    .build();

//            Intent dailyIntent = new Intent(Intent.ACTION_MAIN, Uri.parse("shortcut://daily"), this, MainActivity.class);
//            ShortcutInfo dailyShortcut = new ShortcutInfo.Builder(this, "daily")
//                    .setIcon(Icon.createWithResource(getApplicationContext(), R.drawable.host_shortcut_listen))
//                    .setShortLabel(getString(R.string.host_shortcut_daily))
//                    .setIntent(dailyIntent)
//                    .setRank(2)
//                    .build();

            Intent playrecordIntent = new Intent(Intent.ACTION_MAIN, Uri.parse("shortcut://playrecord"), this, MainActivity.class);
            ShortcutInfo playrecordShortcut = new ShortcutInfo.Builder(this, "playrecord")
                    .setIcon(Icon.createWithResource(getApplicationContext(), R.drawable.host_shortcut_history))
                    .setShortLabel(getString(R.string.host_shortcut_history))
                    .setIntent(playrecordIntent)
                    .setRank(3)
                    .build();

            try {
                manager.addDynamicShortcuts(Arrays.asList(aiSearchShortcut, downloadShortcut, subscribeShortcut, playrecordShortcut));
                MmkvCommonUtil.getInstance(getApplicationContext()).saveBoolean(SHORT_CUT_HAS_CREATED, true);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    private void handleShortcut(Intent intent) {
        Uri uri = intent.getData();
        if (uri != null) {
            String scheme = uri.getScheme();
            if ("shortcut".equals(scheme)) {
                String host = uri.getHost();
                if ("download".equals(host)) {
                    clearAllFragmentFromManageFragment();
                    //下载
                    MyListenRouterUtil.getMyListenBundle(bundleModel -> {
                        IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                        if (fragAction != null && canUpdateUi()) {
                            Bundle bundle = new Bundle();
                            bundle.putString(BundleKeyConstants.KEY_MYLISTEN_IS_FROM, BundleKeyConstants.KEY_MYLISTEN_IS_FROM_OTHER);
                            BaseFragment2 frag = fragAction.newDownloadFragment();
                            if (frag != null) {
                                frag.setArguments(bundle);
                                startFragment(frag);
                            }
                        }
                    });
                } else if ("daily".equals(host)) {
                    //一键听电台
                    if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost.KEY_CLOSE_DAILY_NEWS_SHORT_CUT, false)) {
                        return;
                    }
                    clearAllFragmentFromManageFragment();
                    try {
                        String itingUri = "iting://open?msg_type=74&channelGroupId=1";
                        ToolUtil.clickUrlAction(this, itingUri, null);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else if ("playrecord".equals(host)) {
                    //播放历史
                    clearAllFragmentFromManageFragment();
                    MyListenRouterUtil.getMyListenBundle(bundleModel -> {
                        IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                        if (fragAction != null && canUpdateUi()) {
                            BaseFragment frag = fragAction.newHistoryFragment(true, false, true, BundleKeyConstants.KEY_MYLISTEN_IS_FROM_OTHER);
                            if (frag != null) {
                                startFragment(frag);
                            }
                        }
                    });
                } else if ("subscribe".equals(host)) {
                    //我的订阅
                    clearAllFragmentFromManageFragment();
                    hidePlayFragment(null);
                    try {
                        String itingUri = "iting://open?msg_type=125&tab=subscribe";
                        ToolUtil.clickUrlAction(this, itingUri, null);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (!playButtonIsShow()) {
                        showPlayButton();
                    }
                } else if ("aiSearch".equals(host)) {
                    if (AiForceLoginManager.INSTANCE.isForceHide()) {
                        // 直接拦截跳转
                        return;
                    }
                    clearAllFragmentFromManageFragment();
                    ChildXmlyTipManager.INSTANCE.goChildChatXmly(ChildXmlyTipManager.SOURCE_SEARCH_AI);
                }
            }
        }
    }

    @Override
    public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
        if (Configure.mainBundleModel.bundleName.equals(bundleModel.bundleName)) {
            Logger.i(BUNDLE_TAG, "main bundle install error");
        }
    }

    @Override
    public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
        if (Configure.mainBundleModel.bundleName.equals(bundleModel.bundleName)) {
            Logger.i(BUNDLE_TAG, "main bundle install error");
        }
    }

    @Override
    public void onInstallSuccess(BundleModel bundleModel) {
        if (Configure.mainBundleModel.bundleName.equals(bundleModel.bundleName)) {
            Logger.i(BUNDLE_TAG, "main bundle install success");
            Logger.logToFile("MainActivity_app_start_time = " + (System.currentTimeMillis() - MainApplication.sApplication_start_time));
            showDefaultBottomTab();
        }
    }

    public boolean isRunShowDefaultBottomTab = false;

    private void showDefaultBottomTab() {
        if (isRunShowDefaultBottomTab) {
            return;
        }
        if (!Configure.mainBundleModel.hasGenerateBundleFile) {
            return;
        }

        if (AccessibilityModeManager.INSTANCE.isAccessibilityMode(true)) {
            return;
        }

        isRunShowDefaultBottomTab = true;

        int firstBottomTab = -1;

        boolean userLastIndex = ConfigureCenter.getInstance().getBool("toc", "lasttab_button_switch", false);
        if (userLastIndex) {
            firstBottomTab = SharedPreferencesUtil.getInstance(this).getInt(PreferenceConstantsInHost
                    .TINGHOST_KEY_LAST_TAB_INDEX, -1);
        }

        if (mPendingRadioIndexFromITing != -1) {
            firstBottomTab = mPendingRadioIndexFromITing;
        }
        if (firstBottomTab == -1) {
            firstBottomTab = SharedPreferencesUtil.getInstance(this).getInt(PreferenceConstantsInHost.TINGMAIN_KEY_BOTTOM_FIRST, 0);
        }

        if (ElderlyModeManager.getInstance().isKeepPageInMine()) {
            ElderlyModeManager.getInstance().setKeepPageInMine(false);
            firstBottomTab = 3;
        }

        switch (firstBottomTab) {
            case 1:
                if (MyListenAbUtil.INSTANCE.isKeepSubscribeInMine()) {
                    setRadioButtonCheckedSafe(mRbCategory, true);
                } else {
                    setRadioButtonCheckedSafe(mRbVip, true);
                }
                break;
            case 2:
                if (MyListenAbUtil.INSTANCE.isKeepSubscribeInMine()) {
                    setRadioButtonCheckedSafe(mRbVip, true);
                } else {
                    setRadioButtonCheckedSafe(mRbFinding, true);
                }
                break;
            case 3:
                setRadioButtonCheckedSafe(mRbMineAndListen, true);
                break;
            case 0:
            default:
                setRadioButtonCheckedSafe(mRbHomePage, true);
                break;
        }

    }

    private void setRadioButtonCheckedSafe(RadioButton radioButton, boolean checked) {
        mPendingRadioIndexFromITing = -1;
        if (radioButton != null) {
            radioButton.setChecked(checked);
        }
    }

    private void showOpenNotificationPermissionDialog() {
        Logger.i("FreeListenTimeDialogManager", "showOpenNotificationPermissionDialog");
        if (!ViewUtil.haveDialogIsShowing(this)) {
            NotificationPermissionOpenManager.checkToShowHomePageNotificationPermissionDialog(this);
        }
    }

    private void showOpenNotificationGuide() {
        if (!ViewUtil.haveDialogIsShowing(this)
                && NotificationUtil.shouldShowNotificationDialog(this)) {
            try {
                BaseDialogFragment fragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newNotificationOpenFragment();
                if (fragment != null) {
                    fragment.show(getSupportFragmentManager(), "open_notification_guide");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void showImportantUpdatePage() {
        try {
            BaseFragment2 fra = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newImportantUpdateInfoFragment();
            if (fra != null) {
                fra.setCallbackFinish(new IFragmentFinish() {
                    @Override
                    public void onFinishCallback(Class<?> cls, int fid, Object... params) {
                        showOpenNotificationGuide();
                        showUpdateNotification();
                    }
                });
                addFragment(R.id.top_fragment_container, fra);
                showFragment(fra);
                TempDataManager.getInstance().saveBoolean("start_dialog_shown", true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Class getLastRemoveFragmentClass() {
        return mLastRemoveFragmentClass;
    }


    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        Logger.i(TAG, "onConfigurationChanged invoked, newConfig.orientation:" + newConfig.orientation
                + ", fontScale: " + newConfig.fontScale);
        super.onConfigurationChanged(newConfig);
        Context context = BaseApplication.getMyApplicationContext();
        if (context != null && context.getResources() != null
                && context.getResources().getConfiguration() != null
                && context.getResources().getDisplayMetrics() != null) {
            Logger.i(TAG, "mainAct " + this + ", fontScale: " + context.getResources().getConfiguration().fontScale
                    + ", scaledDensity: " + context.getResources().getDisplayMetrics().scaledDensity);
        }
        FoldableScreenCompatUtil.INSTANCE.onConfigurationChanged(newConfig);
        RecommendShowTagsUtilNew.INSTANCE.clearDrawableCache();
    }

    @Override
    public boolean needFoldPort() {
        try {
            return !Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFragmentAction().isCurrentLiveFragment();
        } catch (Exception e) {
            e.printStackTrace();
            Logger.e(TAG, "needFoldPort" + e.getMessage());
            return super.needFoldPort();
        }
    }

    public boolean isVipTabLimitFreeTagShown() {
        TextView tvVipTabLimitFreeTag = mActivityTabManager.getTvRedVipTitle();
        if (tvVipTabLimitFreeTag == null) {
            return false;
        }
        return tvVipTabLimitFreeTag.getVisibility() == View.VISIBLE;
    }

    public void updateVipTabLimitFreeTag(boolean visible) {
        TextView tvVipTabLimitFreeTag = mActivityTabManager.getTvRedVipTitle();
        if (tvVipTabLimitFreeTag == null) {
            return;
        }
        int visibility = tvVipTabLimitFreeTag.getVisibility();
        if ((visible && (visibility == View.VISIBLE))) {
            return;
        }
        if ((!visible && (visibility != View.VISIBLE))) {
            return;
        }
        if (visible) {
            // 新首页-会员底tab“限免专区”引导  控件曝光
            new XMTraceApi.Trace()
                    .setMetaId(49638)
                    .setServiceId("slipPage")
                    .put("currPage", "newHomePage")
                    .createTrace();
        }
        runOnUiThread(() -> tvVipTabLimitFreeTag.setVisibility(visible ? View.VISIBLE : View.GONE));
    }

    public void updateSubscribeTabRedDot() {
        if (!canUpdateUi() || mRadioGroup == null || MyListenAbUtil.INSTANCE.isKeepSubscribeInMine() || MyListenAbUtil.INSTANCE.isKeepSubscribeInFeedNoRedPoint()) {
            return;
        }
        TextView tvRedDot = mActivityTabManager.getTvRedDotFind();
        ImageView ivRedDot = mActivityTabManager.getIvRedDotFind();
        boolean isCurChecked = (mRadioGroup.findViewById(mRadioGroup.getCheckedRadioButtonId()) == mRbFinding);

        if (isFinishing() || tvRedDot == null || ivRedDot == null) {
            return;
        }
        boolean subscribeHasNew = NoReadManage.getInstance(this).homeUnRead.getUnreadSubscribeCountV2() > 0;
        boolean getAlbumAndTingListSubscribeRedPoint = NoReadManage.getInstance(this).getAlbumAndTingListSubscribeRedPoint() &&
                MyListenAbUtil.INSTANCE.isInABExperimentGroup();
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (!isCurChecked && (subscribeHasNew || getAlbumAndTingListSubscribeRedPoint)) {
                    ivRedDot.setVisibility(View.VISIBLE);
                    tvRedDot.setVisibility(View.INVISIBLE);
//                    hideMineTabRedDot();
                    traceTabRedDotOrBubbleExposure(mRbFinding);
                    if (mSubscribeRedDotPosition != null) {
                        mSubscribeRedDotPosition.setRedDotShow(true);
                        mSubscribeRedDotPosition.notifiRedDotShow();
                    }
                } else {
                    ivRedDot.setVisibility(View.INVISIBLE);
                    tvRedDot.setVisibility(View.INVISIBLE);
                    if (mSubscribeRedDotPosition != null) {
                        mSubscribeRedDotPosition.setRedDotShow(false);
                    }
                }
            }
        });
    }

    private void hideSubscribeRedDot() {
        if (mSubscribeRedDotPosition != null) {
            mSubscribeRedDotPosition.setRedDotShow(false);
        }
        TextView tvRedDot = mActivityTabManager.getTvRedDotFind();
        ImageView ivRedDot = mActivityTabManager.getIvRedDotFind();
        if (tvRedDot != null) {
            tvRedDot.setVisibility(View.GONE);
        }
        if (ivRedDot != null) {
            ivRedDot.setVisibility(View.GONE);
        }
    }

    private void hideMineTabRedDot() {
        TextView tvRedDotMineListen = mActivityTabManager.getTvRedDotMineAndListen();
        ImageView ivRedDotMineListen = mActivityTabManager.getIvRedDotMineAndListen();
        if (tvRedDotMineListen != null) {
            tvRedDotMineListen.setVisibility(View.GONE);
        }
        if (ivRedDotMineListen != null) {
            ivRedDotMineListen.setVisibility(View.GONE);
        }
        mMyRedDotPosition.setRedDotShow(false);
    }

    public void updateMineListenTabRedDot(final boolean fromListener, final boolean hasNewSubscribe) {
        if (!canUpdateUi() || mRadioGroup == null || HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()) {
            return;
        }
        TextView tvRedDotMineListen = mActivityTabManager.getTvRedDotMineAndListen();
        ImageView ivRedDotMineListen = mActivityTabManager.getIvRedDotMineAndListen();
        boolean isCurChecked = (mRadioGroup.findViewById(mRadioGroup.getCheckedRadioButtonId()) == mRbMineAndListen);
        final NoReadModel noReadModel = NoReadManage.getInstance(this).getNoReadModel();
        final UnreadModel unreadModel = mUnreadModel;
        int unreadSubscribeCount = NoReadManage.getInstance(this).homeUnRead.getUnreadSubscribeCountV2();
        boolean getAlbumAndTingListSubscribeRedPoint = NoReadManage.getInstance(this).getAlbumAndTingListSubscribeRedPoint();
        boolean noMessageTab = ChildProtectManager.isChildProtectOpen(this) || MyListenAbUtil.INSTANCE.getSlideExperienceFlag() == 3 || MyListenAbUtil.INSTANCE.getSlideExperienceFlag() == 4;
        if (isFinishing() || tvRedDotMineListen == null || ivRedDotMineListen == null) {
            return;
        }
        //NOTE:老的代码中，有在子线程调用此方法，修改逻辑务必切换到主线程更新
        runOnUiThread(() -> {
            boolean isFeed2Subscribe = DyncAbTestUtil.isFeed2Subscribe();
            boolean canShowMessageTab = DyncAbTestUtil.canShowMessageTab();
            boolean canShowMineTabRed = DyncAbTestUtil.INSTANCE.canShowMineTabRed();
            if ((canShowMessageTab && !canShowMineTabRed) || mUserTabTagShowing || isCurChecked) {
                ViewStatusUtil.setVisible(View.GONE, tvRedDotMineListen, ivRedDotMineListen);
                return;
            }
//            if (!isFeed2Subscribe && noReadModel != null && noReadModel.getUnreadSubscribeCount() > 0) {
//                tvRedDotMineListen.setText(noReadModel.getUnreadSubscribeCount() >= 99 ? "99+"
//                        : noReadModel.getUnreadSubscribeCount() + "");
//                ViewStatusUtil.setVisible(View.VISIBLE, tvRedDotMineListen);
//                ViewStatusUtil.setVisible(View.GONE, ivRedDotMineListen);
//            } else
//            if (!isFeed2Subscribe && noReadModel != null && noReadModel.getUnreadTrackFeedCount() > 0) {
//                ViewStatusUtil.setVisible(View.VISIBLE, ivRedDotMineListen);
//                ViewStatusUtil.setVisible(View.GONE, tvRedDotMineListen);
//            } else
            if (MyListenAbUtil.INSTANCE.isKeepSubscribeInMine() && canShowMineTabRed && (unreadSubscribeCount > 0 || getAlbumAndTingListSubscribeRedPoint)) {
                int allCount = noMessageTab ? unreadSubscribeCount : (unreadSubscribeCount + noReadModel.getUnReadMessageCount());
                if (allCount > 0 && allCount < 100) {
                    tvRedDotMineListen.setText(allCount + "");
                    ViewStatusUtil.setVisible(View.VISIBLE, tvRedDotMineListen);
                    ViewStatusUtil.setVisible(View.GONE, ivRedDotMineListen);
                } else {
                    ViewStatusUtil.setVisible(View.GONE, tvRedDotMineListen);
                    ViewStatusUtil.setVisible(View.VISIBLE, ivRedDotMineListen);
                }
            } else if (canShowMineTabRed && noReadModel != null && noReadModel.getUnReadMessageCount() > 0 && !noMessageTab) {
                if (noReadModel.getUnReadMessageCount() > 99) {
                    ViewStatusUtil.setVisible(View.GONE, tvRedDotMineListen);
                    ViewStatusUtil.setVisible(View.VISIBLE, ivRedDotMineListen);
                } else {
                    tvRedDotMineListen.setText(noReadModel.getUnReadMessageCount() + "");
                    ViewStatusUtil.setVisible(View.VISIBLE, tvRedDotMineListen);
                    ViewStatusUtil.setVisible(View.GONE, ivRedDotMineListen);
                }
            } else if (!canShowMessageTab && ListenTaskMsgManager.getInstance(getApplicationContext()).isNeedShowMineRedDot()) {
                if (tvRedDotMineListen.getVisibility() != View.VISIBLE
                        && ivRedDotMineListen.getVisibility() != View.VISIBLE) {
                    ivRedDotMineListen.setVisibility(View.VISIBLE);
                }
            } else if (!canShowMessageTab && UserInfoMannage.hasLogined() && unreadModel != null && unreadModel.totalUnreadCount() > 0) {
                ViewStatusUtil.setVisible(View.VISIBLE, ivRedDotMineListen);
                ViewStatusUtil.setVisible(View.GONE, tvRedDotMineListen);
            } else {
                ViewStatusUtil.setVisible(View.GONE, tvRedDotMineListen, ivRedDotMineListen);
            }
        });
        if (ivRedDotMineListen.getVisibility() == View.VISIBLE || tvRedDotMineListen.getVisibility() == View.VISIBLE) {
            traceTabRedDotOrBubbleExposure(mRbMineAndListen);
            if (mMyRedDotPosition != null) {
                mMyRedDotPosition.setRedDotShow(true);
                mMyRedDotPosition.notifiRedDotShow();
            }
        } else {
            if (mMyRedDotPosition != null) {
                mMyRedDotPosition.setRedDotShow(false);
            }
        }
    }

    public void setHomePageTabAsRefreshBtn(boolean asRefreshBtn) {
        Logger.d(BOTTOM_TAG, "MainActivity setHomePageTabAsRefreshBtn: " + asRefreshBtn);
        mHasSetHomePageTabAsRefreshBtn = asRefreshBtn;
        if (mRadioGroup != null && mRbHomePage != null) {
            boolean isCurChecked = (mRadioGroup.findViewById(mRadioGroup.getCheckedRadioButtonId()) == mRbHomePage);
            Logger.d(BOTTOM_TAG, "MainActivity setHomePageTabAsRefreshBtn isCurChecked: " + isCurChecked);
            if (asRefreshBtn) {
                loadHomeTabRefreshDrawable(false);
                if (!hasBottomAtmophere) {
                    mRbHomePage.setText(R.string.host_refresh);
                }
            } else {
                if (mBottomHomePageTabDrawable != null) {
                    mRbHomePage.setCompoundDrawablesWithIntrinsicBounds(null, mBottomHomePageTabDrawable, null, null);
                    mRbHomePage.setText(R.string.host_home_page);
                    if (isCurChecked) {
                        Drawable drawable = mRbHomePage.getCompoundDrawables()[1];
                        if (drawable instanceof LottieDrawable) {
                            ((LottieDrawable) drawable).cancelAnimation();
                            ((LottieDrawable) drawable).playAnimation();
                        }
                    }
                } else {
                    mRbHomePage.setText(R.string.host_home_page);
                    mRbHomePage.setTag(R.id.host_load_success, false);
                    loadBottomTabResource(mRbHomePage, TabFragmentManager.TAB_HOME_PAGE, isCurChecked);
                }
            }
        }
    }

    public void setOnBackPressInterceptor(OnBackPressInterceptor interceptor) {
        this.mOnBackPressInterceptor = interceptor;
    }

    public void removeOnBackPressInterceptorByTag(String tag) {
        if (mOnBackPressInterceptor != null
                && mOnBackPressInterceptor.interceptorTag() != null
                && mOnBackPressInterceptor.interceptorTag().equals(tag)) {
            mOnBackPressInterceptor = null;
        }
    }

    public void removeOnBackPressInterceptor() {
        mOnBackPressInterceptor = null;
    }

    private OnBackPressInterceptor mOnBackPressInterceptor;

    @Override
    public void onConnected() {
        // XmPlayerManager.getInstance(MainActivity.this).setNotificationType(NOTIFICATION_TYPE_PLUS_REDUCE_TIME); 暂时去掉通知栏abtest
        if (mIsFirstSyncAndPullHistory) {
            syncCloudHistory(true);
            mIsFirstSyncAndPullHistory = false;
        }
        int playMode = PlayTools.getSavedPlayMode(MainActivity.this);
        XmPlayerManager.getInstance(MainActivity.this).setPlayMode(PlayMode.getIndex(playMode));
        boolean isElderlyMode = ElderlyModeManager.getInstance().isElderlyMode();
        XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).setElderlyMode(isElderlyMode);
    }

    /**
     * intercept back event
     **/
    public interface OnBackPressInterceptor {
        boolean intercept();

        String interceptorTag();
    }

    private static void postAlbumSubscribeOfUnlogin() {
        if (UserInfoMannage.hasLogined())
            return;

        new MyAsyncTask<Void, Void, Void>() {
            @Override
            protected Void doInBackground(Void... voids) {
                List<Album> list = AlbumCollectManager.getInstance(MainApplication.getMyApplicationContext()).getAlbumList();
                new UserTracking().setAlbumAmount(list == null ? 0 : list.size()).statIting(XDCSCollectUtil.APP_NAME_EVENT, "localSubscribed");
                return null;
            }
        }.execute();
    }

    /**
     * 显示或者隐藏首页tab
     *
     * @param show
     **/
    public void showOrHideBottomTabUi(boolean show) {
        if (mTabBg == null) {
            mTabBg = findViewById(R.id.host_v_tabs_bg);
        }
        if (mTabGroup == null) {
            mTabGroup = findViewById(R.id.rg_tabs);
        }
        if (mHotView == null) {
            mHotView = mActivityTabManager.getRootBottomLay();
        }

        if (mTabBgLay == null) {
            mTabBgLay = findViewById(R.id.host_tab_bg_lay);
        }

        if (mTabGroup == null || mTabBg == null || mHotView == null) {
            return;
        }

        if (show) {
            mTabBg.setVisibility(View.VISIBLE);
            mTabBgLay.setVisibility(View.VISIBLE);
            mTabGroup.setVisibility(View.VISIBLE);
            mHotView.setVisibility(View.VISIBLE);
        } else {
            mTabBg.setVisibility(View.GONE);
            mTabBgLay.setVisibility(View.GONE);
            mTabGroup.setVisibility(View.GONE);
            mHotView.setVisibility(View.GONE);
        }
    }

    private boolean isShowedKillCardToast = false;

    // 检查是否需要显示大王卡的提示
    private void checkShowKillCardToast() {
        if (!isShowedKillCardToast && ToolUtil.activityIsValid(this) && NetworkType.isConnectMOBILE(this)) {
            isShowedKillCardToast = true;
            HandlerManager.postOnUIThreadDelay(new Runnable() {
                @Override
                public void run() {
                    if (FreeFlowServiceUtil.getFreeFlowService() != null
                            && FreeFlowServiceUtil.getFreeFlowService().isKingCard() && ToolUtil.activityIsValid(MainActivity.this)) {
                        FreeKingToast.showToast(MainActivity.this);

                        new UserTracking()
                                .setModuleType("大王卡免流toast")
                                .setId("7580")
                                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
                    }
                }
            }, 1000);
        }
    }

    // 联通或者移动手机订购的免流套餐如果超出免流范围进行弹窗提示
    private void checkFreeFlowStatus() {
        IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
        if (freeFlowService != null && freeFlowService.freeFlowUseOver()) {
            FreeFlowService.showFreeRemaindZeroDialog();
        }
    }

    public boolean canUpdateUi() {
        if (isFinishing()) {
            return false;
        }
        return !isDestroyed();
    }

    public boolean recommendFragmentIsShowing() {
        if (getTabFragmentManager() != null && getTabFragmentManager().getCurrFragment()
                instanceof IMainFunctionAction.AbstractHomePageFragment) {
            return ((IMainFunctionAction.AbstractHomePageFragment) getTabFragmentManager()
                    .getCurrFragment()).recommendFragmentIsShowing();
        }

        return false;
    }

    public Fragment getRecommendFragmentStaggeredIfShowing() {
        if (getTabFragmentManager() != null && getTabFragmentManager().getCurrFragment()
                instanceof IMainFunctionAction.AbstractHomePageFragment) {
            IMainFunctionAction.AbstractHomePageFragment currFragment = (IMainFunctionAction.AbstractHomePageFragment) getTabFragmentManager()
                    .getCurrFragment();
            if (currFragment.recommendFragmentStaggeredIsShowing()){
                return currFragment.getCurTabFragment();
            }
        }
        return null;
    }

    public boolean isHomePageFragmentOnTop() {
        if (getTabFragmentManager() != null && getTabFragmentManager().getCurrFragment()
                instanceof IMainFunctionAction.AbstractHomePageFragment) {
            return ((IMainFunctionAction.AbstractHomePageFragment) getTabFragmentManager().getCurrFragment())
                    .isFragmentOnTop();
        }
        return false;
    }

    private String getTabMessageType(RadioButton tab) {
        if (tab == mRbFinding) {
            TextView tvFindRedView = mActivityTabManager.getTvRedDotFind();
            if (tvFindRedView != null && tvFindRedView.getVisibility() == View.VISIBLE) {
                return "redNum";
            }
        }
        if (tab == mRbMineAndListen) {
            TextView tvMineRedView = mActivityTabManager.getTvRedDotMineAndListen();
            if (tvMineRedView != null && tvMineRedView.getVisibility() == View.VISIBLE) {
                return "redNum";
            }
        }
        return "redPoint";
    }

    private void traceTabRedDotOrBubbleExposure(RadioButton tab) {
        if (tab == null) {
            return;
        }
        String messageType = getTabMessageType(tab);
        String tabName;
        String anchorId = "";
        String feedId = "";
        if (tab == mRbFinding) {
            if (mFeedId > 0) {
                anchorId = String.valueOf(mAnchorId);
                feedId = String.valueOf(mFeedId);
            }
            tabName = "订阅";
        } else {
            if (mHasTracedTabMineExposure) {
                return;
            }
            mHasTracedTabMineExposure = true;
            tabName = "账号";
        }
        new XMTraceApi.Trace()
                .setMetaId(27289)
                .setServiceId("slipPage")
                .put("messageType", messageType)
                .put("tabName", tabName)
                .put("anchorId", anchorId)
                .put("feedId", feedId)
                .put(XmRequestIdManager.IS_DUPLICATE_VIEW, "2")
                .createTrace();
    }

    private void traceTabRedDotOnClickEvent(int tabId) {
        if (tabId == R.id.tab_home_page) {
            return;
        }
        String messageType;
        String tabName;
        String anchorId = "";
        String feedId = "";
        String tabNameNew = "";
        boolean isRedDotShow = false;
        if (tabId == R.id.tab_finding) {
            messageType = getTabMessageType(mRbFinding);
            tabName = "订阅";
            tabNameNew = "订阅";
            anchorId = String.valueOf(mAnchorId);
            feedId = String.valueOf(mFeedId);
        } else {
            messageType = getTabMessageType(mRbMineAndListen);
            tabName = "账号";
        }
        if (tabId == R.id.tab_vip) {
            if (NovelTabAbManager.INSTANCE.showNovelTab()) {
                tabNameNew = "免费";
                tabName = "免费";
            } else if (NovelTabAbManager.showCategoryV2Tab()) {
                tabNameNew = "分类";
                tabName = "分类";
                // 全局_底tab-分类  其他事件
                new XMTraceApi.Trace()
                        .setMetaId(64996)
                        .put("from", "tab")
                        .setServiceId("others") // 点击「分类」底 tab
                        .createTrace();
            } else {
                tabNameNew = "会员";
                tabName = "会员";
            }
            if (mVipRedDotPosition != null && mVipRedDotPosition.isShowRedDot()) {
                isRedDotShow = true;
            }
        } else if (tabId == R.id.tab_myspace_and_listen) {
            tabNameNew = "我的";
            tabName = "我的";
            if (mMyRedDotPosition != null && mMyRedDotPosition.isShowRedDot()) {
                isRedDotShow = true;
            }
        }
        new XMTraceApi.Trace()
                .click(27288)
                .put("messageType", messageType)
                .put("tabName", tabName)
                .put("feedId", feedId)
                .put("anchorId", anchorId)
                .createTrace();
        if (isRedDotShow) {
            // 全局页-红点频控  点击事件
            new XMTraceApi.Trace()
                    .click(59651) // 用户点击时上报
                    .put("currPage", "forAll")
                    .put("xmRequestId", "")
                    .put("guideType", "引导")
                    .put("tabName", tabNameNew)
                    .createTrace();
        }
    }

    private boolean mHasInitIMXChatLogin;
    /**
     * 初始化IM模块的登陆
     */
    @StartUpMonitor
    private void initIMXChatLogin() {
        if (mHasInitIMXChatLogin) {
            return;
        }
        mHasInitIMXChatLogin = true;
        //初始化IM连接的登陆管理
        IMLoginManager.getInstance().init(getApplicationContext());

        //消息未读数据管理
        if (mUnreadMsgUpdateListener == null) {
            mUnreadMsgUpdateListener = model -> {
                if (model == null) {
                    return;
                }
                mUnreadModel = model;

                View tvRedDotMine = mActivityTabManager.getTvRedDotMineAndListen();
                View ivRedDotMine = mActivityTabManager.getIvRedDotMineAndListen();

                if (tvRedDotMine == null || ivRedDotMine == null) {
                    return;
                }
                if (showMySpaceUnread && NoReadManage.getInstance(this).canShowRedDot()) {
                    if (model.totalUnreadCount() - model.mGroupQuietUnreadCount > 0) {
                        ivRedDotMine.setVisibility(View.VISIBLE);
                    } else {
                        ivRedDotMine.setVisibility(View.INVISIBLE);
                    }
                }
                updateMineListenTabRedDot(true, false);
            };
        }
        IMUnreadMsgManager.getInstance(getApplicationContext())
                .registerUnreadMsgUpdateListener(mUnreadMsgUpdateListener);


        //触发chat模块下载安装
        try {
            Router.getActionRouter(Configure.BUNDLE_CHAT);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * IM模块的注销
     */
    private void doIMXChatLogout() {
        try {

            if (mUnreadMsgUpdateListener != null) {
                IMUnreadMsgManager.getInstance(this).unregisterUnreadMsgUpdateListener(mUnreadMsgUpdateListener);
            }

            IMLoginManager.getInstance().removeLoginStatusListener();

            IMLoginManager.getInstance().imLogout();

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private ISkinSettingChangeListener mSkinSettingChangeListener = new SkinSettingChangeActivityWrapListener(this);

    static class SkinSettingChangeActivityWrapListener implements ISkinSettingChangeListener {
        public WeakReference<MainActivity> mainRef;

        public SkinSettingChangeActivityWrapListener(MainActivity activity) {
            mainRef = new WeakReference<>(activity);
        }

        public void onBottomColorChanged() {
            if (mainRef != null) {
                MainActivity mainActivity = mainRef.get();
                if (mainActivity != null && mainActivity.canUpdateUi()) {
                    mainActivity.updateBottomTabBgColor();
                }
            }
        }

        public void onBottomIconChanged() {
            if (mainRef != null) {
                MainActivity mainActivity = mainRef.get();
                if (mainActivity != null && mainActivity.canUpdateUi()) {
                    mainActivity.updateBottomTabResource();
                }
            }
        }

        @Override
        public void onMainColorChanged() {

        }

        @Override
        public void onAtmosphereInfoChanged() {

        }
    }

    private class BottomTabAtmosphereIconLoadTask{
        private SkinBottomIcon mIconInfo;
        private RadioButton mRbTab;
        private Bitmap mCheckedBitmap;
        private Bitmap mUncheckedBitmap;
        private int mFinishedTaskCount;

        BottomTabAtmosphereIconLoadTask(SkinBottomIcon iconInfo, RadioButton rbTab) {
            mIconInfo = iconInfo;
            mRbTab = rbTab;
        }

        private boolean allTasksSucceeded() {
            boolean allLoaded = true;
            Iterator<Boolean> iterator = radioButtonLoadStatus.values().iterator();
            while (iterator.hasNext()) {
                if (!iterator.next()) {
                    allLoaded = false;
                    break;
                }
            }
            return allLoaded;
        }

        void start() {
            if (mIconInfo == null || mRbTab == null) {
                return;
            }
            int width = BaseUtil.dp2px(getContext(), 30);
            int height = BaseUtil.dp2px(getContext(), 30);
            Runnable actionAfterLoad = () -> {
                if (mFinishedTaskCount >= 2 && mCheckedBitmap != null && mUncheckedBitmap != null
                        && isBottomTabCurResourceOf(mRbTab, mIconInfo)) {
                    StateListDrawable stateListDrawable = new StateListDrawable();
                    Drawable checkedDrawable = new BitmapDrawable(getResources(), mCheckedBitmap);
                    Drawable uncheckedDrawable = new BitmapDrawable(getResources(), mUncheckedBitmap);
                    stateListDrawable.addState(new int[]{android.R.attr.state_checked}, checkedDrawable);
                    stateListDrawable.addState(new int[]{}, uncheckedDrawable);

                    drawableMap.put(mRbTab, stateListDrawable);
                    radioButtonLoadStatus.put(mRbTab, true);
                    if (mRbTab == mRbHomePage) {
                        mBottomHomePageTabDrawable = stateListDrawable;
                    }
                    if (mRbTab == mRbFinding) {
                        mRbTab.setTag(R.id.host_tab_dynamic_drawable, stateListDrawable);
                    }
                    mRbTab.setTag(R.id.host_load_success, true);
                    boolean allTasksSucceeded = allTasksSucceeded();
                    if (drawableMap.size() >= 4 && allTasksSucceeded) {
                        // 请求成功了才设置tabs
                        for (Map.Entry<RadioButton, StateListDrawable> entry : drawableMap.entrySet()) {
                            RadioButton tab = entry.getKey();
                            StateListDrawable valueDrawable = entry.getValue();
                            tab.setCompoundDrawablesWithIntrinsicBounds(null, valueDrawable, null, null);
                        }
                        hasBottomAtmophere = true;
                    }
                }
            };
            ImageManager.Options options = new ImageManager.Options();
            options.targetWidth = width;
            options.targetHeight = height;
            ImageManager.from(MainActivity.this).downloadBitmap(mIconInfo.getCheckedUrl(), options, (lastUrl, bitmap) -> {
                mCheckedBitmap = bitmap;
                mFinishedTaskCount++;
                actionAfterLoad.run();
            });
            ImageManager.from(MainActivity.this).downloadBitmap(mIconInfo.getUnCheckedUrl(), options, (lastUrl, bitmap) -> {
                mUncheckedBitmap = bitmap;
                mFinishedTaskCount++;
                actionAfterLoad.run();
            });
        }
    }

    private class SkinBottomTabIconLoadTask {
        private SkinBottomIcon mIconInfo;
        private RadioButton mRbTab;
        private Bitmap mCheckedBitmap;
        private Bitmap mUncheckedBitmap;
        private int mFinishedTaskCount;


        SkinBottomTabIconLoadTask(SkinBottomIcon iconInfo, RadioButton rbTab) {
            mIconInfo = iconInfo;
            mRbTab = rbTab;
        }

        void start() {
            if (mIconInfo == null || mRbTab == null) {
                return;
            }
            int width = BaseUtil.dp2px(getContext(), 30);
            int height = BaseUtil.dp2px(getContext(), 30);
            Runnable actionAfterLoad = () -> {
                if (mFinishedTaskCount >= 2 && mCheckedBitmap != null && mUncheckedBitmap != null
                        && isBottomTabCurResourceOf(mRbTab, mIconInfo)) {
                    StateListDrawable stateListDrawable = new StateListDrawable();
                    Drawable checkedDrawable = new BitmapDrawable(getResources(), mCheckedBitmap);
                    Drawable uncheckedDrawable = new BitmapDrawable(getResources(), mUncheckedBitmap);
                    stateListDrawable.addState(new int[]{android.R.attr.state_checked}, checkedDrawable);
                    stateListDrawable.addState(new int[]{}, uncheckedDrawable);

                    if (mRbTab == mRbHomePage) {
                        mBottomHomePageTabDrawable = stateListDrawable;
                    }
                    if (mRbTab == mRbFinding) {
                        mRbTab.setTag(R.id.host_tab_dynamic_drawable, stateListDrawable);
//                        boolean hasSetDrawable = mRbTab.getTag(R.id.host_tab_dynamic_red_hint_drawable) instanceof Boolean && (Boolean) mRbTab.getTag(R.id.host_tab_dynamic_red_hint_drawable);
//                        if (!hasSetDrawable) {
                        mRbTab.setCompoundDrawablesWithIntrinsicBounds(null, stateListDrawable, null, null);
//                        }
                    } else {
                        // 加载了皮肤包，把原来保存的默认资源去掉
                        if (mRbTab == mRbHomePage) {
                            mBottomHomePageTabDrawable = null;
                        }
                        mRbTab.setCompoundDrawablesWithIntrinsicBounds(null, stateListDrawable, null, null);
                    }

                    mRbTab.setTag(R.id.host_load_success, true);
                }
            };
            ImageManager.Options options = new ImageManager.Options();
            options.targetWidth = width;
            options.targetHeight = height;
            ImageManager.from(MainActivity.this).downloadBitmap(mIconInfo.getCheckedUrl(), options, (lastUrl, bitmap) -> {
                mCheckedBitmap = bitmap;
                mFinishedTaskCount++;
                actionAfterLoad.run();
            });
            ImageManager.from(MainActivity.this).downloadBitmap(mIconInfo.getUnCheckedUrl(), options, (lastUrl, bitmap) -> {
                mUncheckedBitmap = bitmap;
                mFinishedTaskCount++;
                actionAfterLoad.run();
            });
        }

    }

    private static ABTest.ICallback getAbTestCallBack(MainActivity activity) {
        WeakReference<MainActivity> activityWR = new WeakReference<>(activity);
        return new ABTest.ICallback() {
            @Override
            public void onSuccess() {
                MainActivity mainActivity = activityWR.get();
                if (mainActivity != null) {
                    if (mainActivity.mAbTestCallBack != null) {
                        mainActivity.mAbTestCallBack.onSuccess();
                    }
                }
                InitAfterSettingUpdated.onABTestUpdate();

                ABManager.abDataUpdate();
            }

            @Override
            public void onFailure(int code, Object errorObj) {
                MainActivity mainActivity = activityWR.get();
                if (mainActivity != null) {
                    if (mainActivity.mAbTestCallBack != null) {
                        mainActivity.mAbTestCallBack.onFailure(code, errorObj);
                    }
                }
            }

            @Override
            public void onError(Exception exception) {
                MainActivity mainActivity = activityWR.get();
                if (mainActivity != null) {
                    if (mainActivity.mAbTestCallBack != null) {
                        mainActivity.mAbTestCallBack.onError(exception);
                    }
                }
            }
        };
    }

    @Override
    public void showFragment(Fragment fra) {
        super.showFragment(fra);
    }

    public void showSplashAdFragment(boolean useFragment) { // 供预览功能使用
        if (mStartSplashAdHelper == null) {
            mStartSplashAdHelper = new StartSplashAdHelper();
        }
        if (useFragment) {
            mStartSplashAdHelper.showSplashAdFragment(this, true, "");
        } else {
            mStartSplashAdHelper.showSplashAdActivityOnHotStart(this, false);
        }
    }

    @Override
    public boolean isInitInThisActivity() {
        return PreInstallUtil.canUseStatistics(this);
    }

    private boolean initViewOnShow;
    private boolean splashIsDestroyed;

    // 监听启动页状态
    private void registerSplashAdState() {
        SplashAdStateChangeManager.getInstance().addSplashAdStateListener(mSplashAdStateChange);
    }

    public void clearAdBackgroud() {
        findViewById(R.id.ad_fragment_container).setBackground(null);
    }

    public @Nullable
    Fragment getCurrentTopFragment() {
        if (getSupportFragmentManager() == null) {
            return null;
        }
        ManageFragment manageFragment = getManageFragment();
        if (manageFragment != null
                && manageFragment.mStacks != null
                && manageFragment.mStacks.size() > 0) {
            if (manageFragment.getCurrentFragment() != null) {
                return manageFragment.getCurrentFragment();
            }
        }
        List<Fragment> fragmentList = getSupportFragmentManager().getFragments();
        if (fragmentList == null || fragmentList.size() == 0) {
            return null;
        }
        for (Fragment fragment : fragmentList) {
            if (!fragment.isHidden()
                    && !(fragment instanceof ManageFragment)
                    && !(fragment instanceof PlayBarFragment)
                    && !(fragment instanceof SupportRequestManagerFragment)) {
                return fragment;
            }
        }
        return null;
    }

    public @Nullable
    BaseFragment2 getCurrentTopBaseFragment() {
        if (getSupportFragmentManager() == null) {
            return null;
        }
        ManageFragment manageFragment = getManageFragment();
        if (manageFragment != null
                && manageFragment.mStacks != null
                && manageFragment.mStacks.size() > 0) {
            if (manageFragment.getCurrentFragment() instanceof BaseFragment2) {
                return (BaseFragment2) manageFragment.getCurrentFragment();
            }
        }
        List<Fragment> fragmentList = getSupportFragmentManager().getFragments();
        if (fragmentList == null || fragmentList.size() == 0) {
            return null;
        }
        for (Fragment fragment : fragmentList) {
            if (!fragment.isHidden()
                    && !(fragment instanceof PlayBarFragment)
                    && fragment instanceof BaseFragment2) {
                return (BaseFragment2) fragment;
            }
        }
        return null;
    }

    private ISplashAdStateChange mSplashAdStateChange = new ISplashAdStateChange() {
        @Override
        public void onSplashAdShow(SplashAdShowData splashAdShowData) {
            RecommendPreLoadOptManager.INSTANCE.log("__onSplashAdShow__");
            RecommendPreLoadOptManager.INSTANCE.onAdLoadShow(MainActivity.this);
            HomeRnTraceTimeManager.onAdLoadShow();
            HomeRecommendPageLoadingOptimizationManager.INSTANCE.onAdShow();
            HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("getRandomDrawableFromTimeSegment_____onSplashAdShow");
            ArriveTraceManager.onSplashShowBegin();
            containViewInflate();
            if (mFromItingDirectLanding || mFromPushIgnoreHome) {
                return;
            }
            if (splashAdShowData.isGotoWebAd
                    && ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_CAN_PRELOAD_WEBVIEW, true)
                    && !HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedForcePreloadRecommendPage()) {
                // 预加载nativeHybrid
                if (mAdPreloadHybridViewUtil == null) {
                    mAdPreloadHybridViewUtil = new AdPreloadHybridViewUtil();
                }

                mAdPreloadHybridViewUtil.preloadNativeHybridFragment(MainActivity.this);
            }

            if (!splashAdShowData.showOverToShowBottom) {
                HomeRecommendPageLoadingOptimizationManager.INSTANCE.setMIsNeedForcePreLoadFromAD(false);
            }

            // 如果展示的是gif和h5类开机,不进行初始化,防止因为底部在加载导致UI卡顿问题
            if (splashAdShowData.showOverToShowBottom
                    && !HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedForcePreloadRecommendPage()) {
                return;
            }

            // 联合霸屏类广告需要立马执行showDefaultBottomTab
            initViewOnShow = splashAdShowData.initViewOnShow;

            // 要跳过首页，这里先不加载
            if (!mFromItingDirectLanding) {
                if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent() && !HomeRnUtils.isShowRnFragment()) {
                    try {
                        IActionRouter mainActionRouter = Router.getActionRouter(Configure.BUNDLE_MAIN);
                        if (mainActionRouter instanceof MainActionRouter) {
                            ((MainActionRouter) mainActionRouter).getFunctionAction().preloadHomePageLayout(new Runnable() {
                                @Override
                                public void run() {
                                    initAll();
                                }
                            });
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        initAll();
                    }
                } else {
                    initAll();
                }
            }
        }

        @Override
        public void onSplashAdClick() {
            HomeRecommendPageLoadingOptimizationManager.INSTANCE.onAdClick();
            HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("_____onSplashAdClick");
            containViewInflate();
        }

        @Override
        public void onSplashAdDestroy() {
            HomeRecommendPageLoadingOptimizationManager.INSTANCE.onAdDestroy();
            RecommendPreLoadOptManager.INSTANCE.onAdNullOrDisperse(MainActivity.this);
            HomeRnTraceTimeManager.onAdNullOrDisperse();
            HomeRecommendPageLoadingOptimizationManager.INSTANCE.log("_____onSplashAdDestroy");
            replaceManageFragment();

            if (ArriveTraceManager.onAdDestroyCanRecordAppArrive()) {
                ArriveTraceManager.onAppArrive();
            }
            SplashScreenManager.clearSplashScreen(MainActivity.this);
            SplashAdStateChangeManager.getInstance().removeSplashAdStateListener(this);
            if (mFromItingDirectLanding || mFromPushIgnoreHome) {
                // 跳过首页，先到达落地页
                doOnCreateFromPush();
            } else {
                readyInit();
                bottomTabLoad();
            }
            loadPlayBarData();
            initPlayService();
            UploadBlueBoothMessageUtil.uploadBuleMessage();
            StartupOptManager.onSplashAdDestroy();
            initIMXChatLogin();
        }
    };

    /**
     * 广告结束、冷启动推送结束调用处理初始化相关流程
     */
    private void readyInit() {

        Log.w("z_start", "MainActivity readyInit");
        PerformanceMonitor.stop();

        splashIsDestroyed = true;
        if (mLastClipContentCheckNoHandle) {
            clipContentCheck(mLastClipContentCheckRequestClipboarded);
            mLastClipContentCheckNoHandle = false;
        }
        if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedForcePreloadRecommendPage()) {
            executeOnceAfterHomeShow();
        }
        initAll();
        // 广告页面destory了立马执行showBottomTab
        showDefaultBottomTab();

        if (mAdPreloadHybridViewUtil != null) {
            mAdPreloadHybridViewUtil.cleanPreload();
        }

        if (hasWindowFocusBefore()) {
            Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
                @Override
                public boolean queueIdle() {
                    long startTime = System.currentTimeMillis();
                    doOnFirstWindowFocusChanged();
                    Logger.log("MainActivity : doOnFirstWindowFocusChanged " + (System.currentTimeMillis() - startTime));
                    return false;
                }
            });
        }
        checkShowOtherDialogs();
    }

    public void checkShowOtherDialogs() {
        if (nextNeedCheckShowOtherDialogs) {
            nextNeedCheckShowOtherDialogs = false;
            showOtherDialogs();
        }
    }

    public void initAll() {
        long curTime = System.currentTimeMillis();

        doOnCreate();
        if (mOnNewIntent != null && splashIsDestroyed) {
            Logger.d("pushTag", "initAll doSomethingByIntent");
            doSomethingByIntent(mOnNewIntent);
        }
        doResumeMyReal();
        doOnResumeReal();
        mFromSkipPush = false;
        mFromItingDirectLanding = false;
        mFromPushIgnoreHome = false;

        Logger.log("MainActivity : initAllTime " + (System.currentTimeMillis() - curTime));
    }

    @Override
    protected boolean transparencyBar() {
        return false;
    }

    private IFileAccessCallback mFileDeleteCallback = new IFileAccessCallback() {
        @Override
        public void onFileHandle(int handleMethod, String filePath) {
            if (handleMethod == IFileAccessCallback.DELETE_FILE) {
                if (!TextUtils.isEmpty(filePath) && !filePath.contains("com.ximalaya.ting.android")
                        && filePath.toLowerCase().contains("jpg")) {
                    if (ConstantsOpenSdk.isDebug) {
                        throw new RuntimeException("禁止删除非私有目录下的用户文件！____"
                                + filePath);
                    } else {
                        XDCSCollectUtil.statErrorToXDCS("illegal_delete_user_file"
                                , filePath
                                        + "______"
                                        + Log.getStackTraceString(new Throwable()));
                    }
                }
            }
        }
    };

    public static boolean needRequestHomeAd = true;

    public void startAdFragment(Bundle bundle) {
        // 如果点击跳转到广告落地页如果首页显示了,不再请求首页接口
        boolean nextNeedRequestHomeAd = true;
        if (mManageFragment != null && (mManageFragment.mStacks == null || mManageFragment.mStacks.size() == 0)
                && getTabFragmentManager() != null && getTabFragmentManager().getCurrentTab() == TabFragmentManager.TAB_HOME_PAGE
                && getTabFragmentManager().getCurrFragment() instanceof IMainFunctionAction.AbstractHomePageFragment && ((IMainFunctionAction.AbstractHomePageFragment) getTabFragmentManager().getCurrFragment()).recommendFragmentIsShowing()) {
            nextNeedRequestHomeAd = false;
        }

        needRequestHomeAd = nextNeedRequestHomeAd;

        boolean useAdHybridFragment = false;

        if (bundle != null && bundle.getBoolean(BundleKeyConstants.KEY_USE_AD_HYBRID_FRAGEMENT)) {
            useAdHybridFragment = true;
        }

        NativeHybridFragment nativeHybridFragment;
        if (useAdHybridFragment) {
            AdHybridFragment fragment = new AdHybridFragment();
            fragment.setArguments(bundle);
            nativeHybridFragment = fragment;
        } else {
            nativeHybridFragment = (NativeHybridFragment) NativeHybridFragment.newInstance(bundle);
        }

        if (mAdPreloadHybridViewUtil != null) {
            mAdPreloadHybridViewUtil.setPreloadData(new AdPreloadHybridViewUtil.IInflateCallBack() {
                @Override
                public void onInflateFinished(@Nullable View view) {
                    Logger.log("MainActivity : startAdFragment preloadView isNull=" + (view == null));
                    nativeHybridFragment.setPreloadView(view);

                    startHybridFragment(bundle, nativeHybridFragment);
                }
            });
        } else {
            startHybridFragment(bundle, nativeHybridFragment);
        }
    }

    private void startHybridFragment(Bundle bundle, NativeHybridFragment nativeHybridFragment) {
        if (bundle != null && bundle.getBoolean(BundleKeyConstants.KEY_IS_FROM_AD_VI_CLICK)) {
            try {
                addFragment(R.id.host_landing_page_load_fragment_container, nativeHybridFragment);
                showFragment(nativeHybridFragment);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            startFragment(nativeHybridFragment, -1, -1);
        }
    }

    @Override
    public void recreate() {
        lastIsRestartActivity = true;
        super.recreate();
    }

    public static Intent getMainActivityIntent(Context context) {
        Intent intent = new Intent(context, MainActivity.class);
        if (MainApplication.getMainActivity() != null) {
            BaseUtil.addIntentFlagClearTop(intent);
        }
        if (!(context instanceof Activity)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        return intent;
    }

    public View getRbMineAndListen() {
        return mRbMineAndListen;
    }

    public View getRbVip() {
        return mRbVip;
    }

    private void statOuterIting(Intent intent) {
        if (intent == null) {
            return;
        }
        String url = "";
        if (PushCommonUtil.pushFromIntent(intent)) {
            String message = intent.getStringExtra(AppConfigConstants.PUSH_MESSAGE);
            if (!TextUtils.isEmpty(message)) {
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    url = jsonObject.optString("url", "");
                } catch (Exception e) {
                    Logger.e(e);
                }
            }
        } else if (intent.getData() != null) {
            String tUrl = "";
            if (intent.hasExtra("url")) {
                tUrl = intent.getStringExtra("url");
                // 从qq过来的，会额外带上一些参数，里面的url是来源页面的，不是想要打开的，所以过滤下这种情况，优先取data里的url
                if (intent.hasExtra("preAct") && "QQBrowserActivity".equals(intent.getStringExtra("preAct"))) {
                    tUrl = "";
                }
            }
            if (!TextUtils.isEmpty(tUrl)) {
                url = tUrl;
            } else {
                url = intent.getData().toString();
            }
        }
        statOuterIting(url, true);
    }

    public static void statOuterIting(String url) {
        statOuterIting(url, false);
    }

    // 站外iting跳转埋点，用于补充资源位场景
    private static void statOuterIting(String url, boolean fromIntent) {
        if (!TextUtils.isEmpty(url) && url.startsWith("iting:")) {
            new XMTraceApi.Trace()
                    .setMetaId(39799)
                    .setServiceId("others")
                    .put("itingUrl", url)
                    .isUbtSource()
                    .clearPrevSource()
                    .createTrace();

            String xbsource = null;
            try {
                PushModel model = ItingManager.getPushModelFromUri(Uri.parse(url), "");
                xbsource = model.deliveryChannelid;
                if (fromIntent && !TextUtils.isEmpty(model.growthContentId)) {
                    GrowthItingUtil.setGrowthContentId(model.growthContentId);
                }
                KidsOuterItingLaunchManager.INSTANCE.recordIting(url, model);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (!TextUtils.isEmpty(xbsource)) {
                new XMTraceApi.Trace()
                        .setMetaId(44739)
                        .setServiceId("others")
                        .put("itingUrl", url)
                        .put("status", "success")
                        .put("error", "")
                        .put("xbsource", xbsource)
                        .createTrace();
            }
        }
    }

    public MainActivityTabManager getActivityTabManager() {
        return mActivityTabManager;
    }

    private final IOnDialogShowStateChangeWrapper mShowStateChange = new IOnDialogShowStateChangeWrapper(this);

    static class IOnDialogShowStateChangeWrapper implements ViewUtil.IOnDialogShowStateChange {
        public WeakReference<MainActivity> mainRef;

        public IOnDialogShowStateChangeWrapper(MainActivity activity) {
            mainRef = new WeakReference<>(activity);
        }

        @Override
        public boolean dialogShowStateChange(boolean hasShow) {
            Logger.log("MainActivity : hasDialogShow " + hasShow);
            if (mainRef == null || mainRef.get() == null) {
                return false;
            }
            mainRef.get().setBottomTabsAccessibility(!hasShow);
            mainRef.get().setRootViewAccessibility(!hasShow);
            mainRef.get().setPlayBarAccessibility(!hasShow);
            return false;
        }
    }


    private void setRootViewAccessibility(boolean canAccess) {
        View rootView = findViewById(R.id.fragment_container);
        if (rootView != null) {
            rootView.setImportantForAccessibility(canAccess ?
                    View.IMPORTANT_FOR_ACCESSIBILITY_YES :
                    View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
        }
    }

    private void setPlayBarAccessibility(boolean canAccess) {
        View playBar = findViewById(R.id.fragment_playbar);
        if (playBar != null) {
            playBar.setImportantForAccessibility(canAccess ?
                    View.IMPORTANT_FOR_ACCESSIBILITY_YES :
                    View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
        }
    }

    private final ChildProtectManager.IChildProtectStatusLoad mChildProtectStatusListener = () -> {
        if (!canUpdateUi()) {
            return;
        }
        boolean useNewStyle = TeenModeManager.INSTANCE.isUseNewTeenModeStyle();
        if (!useNewStyle) return; // 配置关闭，不执行后续方法

        boolean isTeenMode = ChildProtectManager.isChildProtectOpen(this);
        switchHomePageTeenMode(isTeenMode);
    };

    private void foreachBottomTagUpdateUI() {
        if (mUserBottomTagList == null || mUserBottomTagList.isEmpty()) {
            return;
        }
        for (UserBottomTag userTag : mUserBottomTagList) {
            if (userTag != null) {
                boolean bottomTabTagHasShow = getBottomTabTagHasShow(userTag.getId());
                if (mActivityTabManager != null) {
                    TextView textView = mActivityTabManager.getFreeListenerMakeView();
                    if (textView != null) {
                        if (bottomTabTagHasShow) {
                            continue;
                        }
                        if (userTag.getIconText() == null || userTag.getIconText().isEmpty()) {
                            hideUserBottomTabTag();
                        } else {
                            mUserTabTagId = userTag.getId();
                            textView.setVisibility(View.VISIBLE);
                            textView.setText(userTag.getIconText());
                            MineTabTagManager.setTagValue(userTag.getIconText());
                            hideMineTabRedDot();
                            mUserTabTagShowing = true;
                            removeUserBottomTagList(userTag.getId());
                            if (userTag.getIconExpire() != null && userTag.getIconExpire() > 0) {
                                postDelayHideUserBottomTabTag(userTag.getIconExpire());
                            }
                            traceBottomTabTagShow();
                            break;
                        }
                    }
                }
            }
        }

    }

    private void removeUserBottomTagList(int id) {
        if (mUserBottomTagList == null || mUserBottomTagList.isEmpty()) {
            return;
        }
        Iterator<UserBottomTag> iterator = mUserBottomTagList.iterator();
        if (iterator == null) {
            return;
        }
        while (iterator.hasNext()) {
            UserBottomTag next = iterator.next();
            if (next != null && next.getId() == id) {
                iterator.remove();
            }
        }
    }


    private void getUserBottomTabTagInfo() {
        CommonRequestM.getUserBottomTag(new IDataCallBack<List<UserBottomTag>>() {
            @Override
            public void onSuccess(@Nullable List<UserBottomTag> dataList) {
                if (dataList == null || dataList.isEmpty() || !canUpdateUi()) {
                    return;
                }

                mUserBottomTagList.clear();
                mUserBottomTagList.addAll(dataList);
                foreachBottomTagUpdateUI();
            }

            @Override
            public void onError(int code, String message) {
                Logger.d("f_tag", "getUserBottomTabTagInfo onError message " + message);
            }
        });
    }

    private void hideUserBottomTabTag() {
        if (mActivityTabManager == null) {
            return;
        }
        TextView textView = mActivityTabManager.getFreeListenerMakeView();
        if (textView != null) {
            MineTabTagManager.setTagValue("");
            textView.setVisibility(View.GONE);
            mUserTabTagShowing = false;
        }
    }

    private void traceBottomTabTagShow() {
        // 我的 Tab 气泡  其他事件
        if (mActivityTabManager == null) {
            return;
        }
        if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.canNotInitLessImportantComponent()) {
            return;
        }
        TextView textView = mActivityTabManager.getFreeListenerMakeView();
        if (textView != null && textView.getVisibility() == View.VISIBLE && ViewStatusUtil.viewIsRealShowing(textView)) {
            MineTabTagManager.traceBottomTabTagShow();
        }
    }

    private void postDelayHideUserBottomTabTag(int delayTime) {
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                hideUserBottomTabTag();
                foreachBottomTagUpdateUI();
            }
        }, (long) delayTime * 60 * 1000);
    }

//    @Override
//    public synchronized Resources getResources() {
//        if (!AutoSizeConfig.getInstance().getNeedHookResource()) {
//            return super.getResources();
//        }
//        // 规避AutoSize适配异常，参考https://github.com/JessYanCoding/AndroidAutoSize/issues/13
//        if (super.getResources() != null && AutoSize.checkInit()
//                && !AutoSizeConfig.getInstance().isStop()
//                && BaseUtil.isMainThread()) {
//            AutoSizeCompat.autoConvertDensityOfGlobal(super.getResources());
//        }
//        return super.getResources();
//    }

    private void checkThirdAppAuthorizeActivity(Intent intent) {
        if (intent != null && intent.getBooleanExtra(THIRD_APP_AUTHORIZE_FLAG, false)) {
            Intent authorizeIntent = new Intent(this, XmThirdAppAuthorizeActivity.class);
            authorizeIntent.setData(intent.getData());
            Bundle extras = intent.getExtras();
            if (extras != null) {
                authorizeIntent.putExtras(extras);
            }
//            startActivityForResult(authorizeIntent, XmThirdAppAuthorizeActivity.THIRD_APP_AUTHORIZE_REQUEST_CODE);
            XmRemoteControlTraceUtil.trace66685();
            startActivity(authorizeIntent);
        }
    }
}

