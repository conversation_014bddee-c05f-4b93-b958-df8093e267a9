package com.ximalaya.ting.android.host.activity.login

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.framework.commoninterface.ISimpleDataCallback
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.constants.LoginByConstants
import com.ximalaya.ting.android.host.manager.account.LoginUtil
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LoginActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew
import com.ximalaya.ting.android.host.util.FullScreenUseNotchUtil
import com.ximalaya.ting.android.loginservice.*
import com.ximalaya.ting.android.loginservice.base.ILogin
import com.ximalaya.ting.android.loginservice.base.ILogin.LoginStrategies
import com.ximalaya.ting.android.loginservice.base.ILoginStrategy
import com.ximalaya.ting.android.loginservice.base.IThirdLoginStrategyFactory
import com.ximalaya.ting.android.loginservice.base.LoginFailMsg
import com.ximalaya.ting.android.loginservice.loginstrategy.AbLoginStrategy
import com.ximalaya.ting.android.xmutil.Logger
import java.lang.ref.WeakReference

/**
 * Created by WolfXu on 2020-10-15.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
class LoginGuideActivity : BaseFragmentActivity2() {

    private val TAG: String? = LoginGuideActivity::class.simpleName
    private var mVgLoginBtn: ViewGroup? = null
    private var mTvSkipBtn: TextView? = null
    private var mTvOtherMethodLoginBtn: TextView? = null
    private var mIvWechatLogin: ImageView? = null
    private var mIvQqLogin: ImageView? = null
    private var xmLoginCallBack: XMLoginCallBack? = null

    protected var loginParamsBundle: Bundle? = null// 记录用户登录源传递的参数
    private var mThirdLoginStrategyFactory: IThirdLoginStrategyFactory? = null

    @LoginStrategies
    private var loginStrategy = 0
    private var mThirdStrategy: ILoginStrategy? = null
    private var mHandleRequestCode: IHandleRequestCode? = null

    override fun onCreate(savedState: Bundle?) {
        FullScreenUseNotchUtil.setFullScreenWithSystemUi(window, true)
        super.onCreate(savedState)
        initUi()
    }

    private fun initUi() {
        mVgLoginBtn = findViewById(R.id.host_vg_login_btn)
        mTvSkipBtn = findViewById(R.id.host_tv_skip_btn)
        mTvOtherMethodLoginBtn = findViewById(R.id.host_tv_other_method_login_btn)
        mIvWechatLogin = findViewById(R.id.host_iv_login_wechat)
        mIvQqLogin = findViewById(R.id.host_iv_login_qq)

        mVgLoginBtn?.setOnClickListener(mOnClickListener)
        mTvSkipBtn?.setOnClickListener(mOnClickListener)
        mTvOtherMethodLoginBtn?.setOnClickListener(mOnClickListener)
        mIvWechatLogin?.setOnClickListener(mOnClickListener)
        mIvQqLogin?.setOnClickListener(mOnClickListener)

//        initCallback(this)

    }

    override fun getContainerLayoutId(): Int {
        return R.layout.host_act_login_guide
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (getLoginService() != null) {
            if (loginStrategy == ILogin.LOGIN_FLAG_QQ) {
                Router.getActionRouter<LoginActionRouter>(Configure.BUNDLE_LOGIN)!!.functionAction.
                requestTencentOnActivityResultData(requestCode, resultCode, data, mThirdStrategy)
            }
        }
    }

    override fun onDestroy() {
        releaseLoginData()
        super.onDestroy()
    }

    override fun transparencyBar(): Boolean {
        return false
    }

    private fun startMainActivity() {
        try {
            val intent = Intent(this, MainActivity::class.java)
            startActivity(intent)
        } catch (e: Exception) {
            Logger.e(e)
        }
        finish()
    }

    private fun gotoNormalLogin(openChannel: String?) {
        UserInfoMannage.gotoLogin(applicationContext, LoginByConstants.LOGIN_BY_FULL_SCREEN,
                true, openChannel, null, false)
        finish()
    }

    private fun initCallback(activity: LoginGuideActivity) {
        xmLoginCallBack = object : XMLoginCallBack() {
            override fun onXMLoginSuccess(loginInfoModel: LoginInfoModelNew, xmLoginInfo: XmLoginInfo) {
                //登录成功后处理
                handleLoginSuccess(loginInfoModel)
            }

            override fun onLoginBegin() {
            }

            override fun onLoginSuccess(xmLoginInfo: XmLoginInfo) {}
            override fun onLoginFailed(msg: LoginFailMsg) {
                handleLoginFail(msg)
            }
        }
        try {
            Router.getActionByCallback(Configure.BUNDLE_LOGIN, object : Router.IBundleInstallCallback {
                override fun onInstallSuccess(bundleModel: BundleModel) {
                    if (Configure.loginBundleModel.bundleName == bundleModel.bundleName) {
                        mHandleRequestCode = Router.getActionRouter<LoginActionRouter>(Configure.BUNDLE_LOGIN)!!.functionAction.getHandleRequestCode(activity, object : ISimpleDataCallback<Int, Any> {
                            override fun getData(): Int {
                                return loginStrategy
                            }

                            override fun setData(`object`: Any?) {
                            }
                        }, false)
                        LoginRequest.setHandleRequestCode(WeakReference(mHandleRequestCode))
                    }
                }

                override fun onLocalInstallError(throwable: Throwable, bundleModel: BundleModel) {
                }

                override fun onRemoteInstallError(t: Throwable, bundleModel: BundleModel) {}
            })
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    protected fun handleLoginSuccess(loginInfoModel: LoginInfoModelNew?) {
        releaseLoginData()
        if (loginInfoModel != null) {
            requestBack(loginInfoModel)
        }
    }

    private fun handleLoginFail(msg: LoginFailMsg) {
        releaseLoginData()
        val mLoginInfo = LoginInfoModelNew()
        mLoginInfo.ret = msg.errorCode
        mLoginInfo.msg = "" + msg.errorMsg
        requestBack(mLoginInfo)
    }

    private fun releaseLoginData() {
        if (mThirdStrategy != null) {
            mThirdStrategy!!.release()
            mThirdStrategy = null
        }
        if (getLoginService() != null) {
            getLoginService()!!.release()
        }
    }

    protected fun doLoginWithQQ() {
        try {
            Router.getActionByCallback(Configure.BUNDLE_LOGIN, object : Router.IBundleInstallCallback {
                override fun onInstallSuccess(bundleModel: BundleModel) {
                    if (Configure.loginBundleModel.bundleName == bundleModel.bundleName) {
                        loginWithQQ()
                    }
                }

                override fun onLocalInstallError(throwable: Throwable, bundleModel: BundleModel) {
                }

                override fun onRemoteInstallError(t: Throwable, bundleModel: BundleModel) {}
            })
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }

    }

    protected fun loginWithQQ() {
        loginStrategy = ILogin.LOGIN_FLAG_QQ
        getLoginService()?.loginWithThirdSdk(ILogin.LOGIN_FLAG_QQ,
                getLoginStrategyFactory(), this, xmLoginCallBack)
    }

    protected fun doLoginWithWeiXin() {
        try {
            Router.getActionByCallback(Configure.BUNDLE_LOGIN, object : Router.IBundleInstallCallback {
                override fun onInstallSuccess(bundleModel: BundleModel) {
                    if (Configure.loginBundleModel.bundleName == bundleModel.bundleName) {
                        loginWithWeiXin()
                    }
                }

                override fun onLocalInstallError(throwable: Throwable, bundleModel: BundleModel) {
                }

                override fun onRemoteInstallError(t: Throwable, bundleModel: BundleModel) {}
            })
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    protected fun loginWithWeiXin() {
        loginStrategy = ILogin.LOGIN_FLAG_WEIXIN
        getLoginService()?.loginWithThirdSdk(ILogin.LOGIN_FLAG_WEIXIN,
                getLoginStrategyFactory(), this, xmLoginCallBack)
    }


    private fun getLoginService(): LoginService? {
        return LoginService.getInstance()
    }

    private fun getLoginStrategyFactory(): IThirdLoginStrategyFactory? {
        if (mThirdLoginStrategyFactory == null) {
            mThirdLoginStrategyFactory = IThirdLoginStrategyFactory { type ->
                val abLoginStrategy: AbLoginStrategy =
                        Router.getActionRouter<LoginActionRouter>(Configure.BUNDLE_LOGIN)!!.functionAction.getLoginStrategyByType(type)
                mThirdStrategy = abLoginStrategy
                abLoginStrategy
            }
        }
        return mThirdLoginStrategyFactory
    }

    private fun requestBack(result: LoginInfoModelNew) {
        if (this.isFinishing()) {
            return
        }
        if (result.ret == 0) { // 登录成功
            val srcPage = "引导用户登录全屏"
            val bundle: Bundle? = this.getIntent().extras
            LoginUtil.loginSuccess(this, result, loginStrategy, srcPage, bundle)
//            startMainActivity()
        } else {
            Logger.e("login", result.msg)
            if (!TextUtils.isEmpty(result.msg)) {
                CustomToast.showFailToast(result.msg)
            }
        }
    }

    private val mOnClickListener = View.OnClickListener {
        if (it == mVgLoginBtn) {
            gotoNormalLogin(null)
        } else if (it == mTvSkipBtn) {
            startMainActivity()
        } else if (it == mTvOtherMethodLoginBtn) {
            gotoNormalLogin(null)
        } else if (it == mIvWechatLogin) {
            gotoNormalLogin("weixin")
//            if (NetworkType.isConnectTONetWork(applicationContext)) {
//                doLoginWithWeiXin()
//            } else {
//                CustomToast.showFailToast("网络连接不可用，请检查网络设置")
//            }
        } else if (it == mIvQqLogin) {
            gotoNormalLogin("qq")
//            if (NetworkType.isConnectTONetWork(applicationContext)) {
//                doLoginWithQQ()
//            } else {
//                CustomToast.showFailToast("网络连接不可用，请检查网络设置")
//            }
        }
    }
}