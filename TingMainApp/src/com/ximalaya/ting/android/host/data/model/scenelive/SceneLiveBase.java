package com.ximalaya.ting.android.host.data.model.scenelive;

/**
 * Created by neil.qian on 2016/6/7.
 * <AUTHOR>
 */
public interface SceneLiveBase {
    int SCENE_LIVE_NOTICE = 1;
    int SCENE_LIVE_ING = 2;
    int SCENE_LIVE_REST = 3;
    int SCENE_LIVE_END = 4;

    String ID = "id";
    String STATUS = "status";
    String COVER = "coverPath";
    String NAME = "name";
    String DESCRIPTION = "description";
    String SHORTDESCRIPTION = "shortDescription";
    String SCHEDULEID = "scheduleId";
    String STARTTS = "startTs";
    String ENDTS = "endTs";
    String REMAINMS = "remainMs";
    String PLAYCOUNT = "playCount";
    String SHAREURL = "shareUrl";
    String CHATID = "chatId";
    String ACTIVITYID = "activityId";
    String TRACKID = "trackId";
    String ONLINECOUNT = "onlineCount";
    String UID = "uid";
    String TOTALLIVEMILLSECONDS = "totalLiveMillSeconds";
}
