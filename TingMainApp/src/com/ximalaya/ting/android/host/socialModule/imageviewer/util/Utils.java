package com.ximalaya.ting.android.host.socialModule.imageviewer.util;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Point;
import android.graphics.drawable.Drawable;
import android.opengl.EGL14;
import android.opengl.EGLConfig;
import android.opengl.EGLContext;
import android.opengl.EGLDisplay;
import android.opengl.EGLSurface;
import android.opengl.GLES20;
import android.os.Build;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.PadAdaptUtil;
import com.ximalaya.ting.android.host.socialModule.imageviewer.IViewHandle;
import com.ximalaya.ting.android.host.socialModule.imageviewer.ViewerContextProvider;
import com.ximalaya.ting.android.xmutil.Logger;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.util.List;

import androidx.core.view.ViewCompat;

/**
 * Created by qianmenchao on 2018/12/17.
 *
 * <AUTHOR>
 */
public class Utils {

    /**
     * {@see DisplayListCanvas#ThrowIfCannotDraw(Bitmap)}
     */
    private static final int MAX_BITMAP_SIZE = 100 * 1024 * 1024;
    private static int sEglTextureMaxSize = 0;

    public static void removeView(View view) {
        try {
            if (view != null && view.getParent() != null && ViewCompat.isAttachedToWindow(view)) {
                ((ViewGroup) view.getParent()).removeView(view);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static <T> T getCollectionsData(List<T> list, int position) {
        return list != null && position >= 0 && position < list.size() ? list.get(position) : null;
    }

    public static void setBitmapToView(Bitmap bitmap, ImageView imageView) {
        if (bitmap == null || imageView == null) {
            return;
        }

        int byteCount = bitmap.getByteCount();

        if (byteCount <= MAX_BITMAP_SIZE) {
            imageView.setImageBitmap(bitmap);
            return;
        }

        float multiplier = MAX_BITMAP_SIZE / (float) byteCount;
        int width = (int) (bitmap.getWidth() * multiplier);
        int height = (int) (bitmap.getHeight() * multiplier);
        int textureMaxSize = getMaxTextureSize();
        boolean mIsOverTextTureMaxSize = textureMaxSize != 0 && (width > textureMaxSize || height > textureMaxSize);
        if (mIsOverTextTureMaxSize) {
            if (height >= width) {
                width = width * textureMaxSize / height;
                height = textureMaxSize;
            } else {
                height = height * textureMaxSize / width;
                width = textureMaxSize;
            }
        }
        imageView.setImageBitmap(Bitmap.createScaledBitmap(bitmap, width, height, false));
    }

    public static int getMaxTextureSize() {
        if (sEglTextureMaxSize != 0) {
            return sEglTextureMaxSize;
        }
        sEglTextureMaxSize = getMaxTextureSizeEgl14();
        return sEglTextureMaxSize;
    }

    private static int getMaxTextureSizeEgl14() {
        EGLDisplay dpy = EGL14.eglGetDisplay(EGL14.EGL_DEFAULT_DISPLAY);
        int[] vers = new int[2];
        EGL14.eglInitialize(dpy, vers, 0, vers, 1);
        int[] configAttr = {
            EGL14.EGL_COLOR_BUFFER_TYPE,
            EGL14.EGL_RGB_BUFFER, EGL14.EGL_LEVEL, 0,
            EGL14.EGL_RENDERABLE_TYPE,
            EGL14.EGL_OPENGL_ES2_BIT,
            EGL14.EGL_SURFACE_TYPE,
            EGL14.EGL_PBUFFER_BIT, EGL14.EGL_NONE
        };
        EGLConfig[] configs = new EGLConfig[1];
        int[] numConfig = new int[1];
        EGL14.eglChooseConfig(dpy, configAttr, 0,
            configs, 0, 1, numConfig, 0);
        if (numConfig[0] == 0) {
            return 0;
        }
        EGLConfig config = configs[0];
        int[] surfAttr = {
            EGL14.EGL_WIDTH, 64, EGL14.EGL_HEIGHT, 64,
            EGL14.EGL_NONE
        };
        EGLSurface surf = EGL14
            .eglCreatePbufferSurface(dpy, config, surfAttr, 0);
        int[] ctxAttrib = {
            EGL14.EGL_CONTEXT_CLIENT_VERSION, 2,
            EGL14.EGL_NONE
        };
        EGLContext ctx = EGL14.eglCreateContext(dpy, config, EGL14.EGL_NO_CONTEXT, ctxAttrib, 0);
        EGL14.eglMakeCurrent(dpy, surf, surf, ctx);
        int[] maxSize = new int[1];
        GLES20.glGetIntegerv(GLES20.GL_MAX_TEXTURE_SIZE,
            maxSize, 0);
        EGL14.eglMakeCurrent(dpy, EGL14.EGL_NO_SURFACE,
            EGL14.EGL_NO_SURFACE, EGL14.EGL_NO_CONTEXT);
        EGL14.eglDestroySurface(dpy, surf);
        EGL14.eglDestroyContext(dpy, ctx);
        EGL14.eglTerminate(dpy);
        return maxSize[0];
    }

    public static void postDebugCrash(boolean condition, String message) {
        if (condition && ViewerContextProvider.isDebug()) {
            throw new RuntimeException(TextUtils.isEmpty(message) ? "viewer crash" : message);
        }
    }

    public static boolean isGif(String url) {
        return !TextUtils.isEmpty(url) && url.contains("gif");
    }

    public static String md5(String input) {
        String result = input;
        if (input != null) {
            try {
                MessageDigest md = MessageDigest.getInstance("MD5");
                md.update(input.getBytes());
                BigInteger hash = new BigInteger(1, md.digest());

                for (result = hash.toString(16); result.length() < 32; result = "0" + result) {
                }
            } catch (Exception var4) {
                Logger.d(FileUtil.TAG, "md5加密出错" + var4.getMessage());
            }
        }
        return result;
    }

    public static void pauseGif(Drawable drawable) {
        if (!ViewerContextProvider.pauseGifOnMoving()) {
            return;
        }
        if (drawable == null) {
            return;
        }
        IViewHandle viewHandle = ViewerContextProvider.self().getViewHandle();
        if (viewHandle == null) {
            return;
        }
        viewHandle.handGifDrawable(drawable, IViewHandle.PAUSE_GIF);
    }

    public static void resumeGif(Drawable drawable) {
        if (!ViewerContextProvider.pauseGifOnMoving()) {
            return;
        }
        if (drawable == null) {
            return;
        }
        IViewHandle viewHandle = ViewerContextProvider.self().getViewHandle();
        if (viewHandle == null) {
            return;
        }
        viewHandle.handGifDrawable(drawable, IViewHandle.RESUME_GIF);
    }

    public static boolean isPad() {
        return ViewerContextProvider.getPadCompat() != null && ViewerContextProvider.getPadCompat().isPad();
    }

    public static int getPadScreenWidth() {
        return ViewerContextProvider.getPadCompat() != null ? ViewerContextProvider.getPadCompat().getScreenWidth() : 0;
    }

    public static int getPadScreenHeight() {
        return ViewerContextProvider.getPadCompat() != null ? ViewerContextProvider.getPadCompat().getScreenHeight()
            : 0;
    }
}
