package com.ximalaya.ting.android.host.socialModule.imageviewer.drag;

import android.graphics.drawable.Drawable;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.ximalaya.ting.android.host.socialModule.imageviewer.transaction.AnimationParams;
import com.ximalaya.ting.android.host.socialModule.imageviewer.transaction.TransitionParams;
import com.ximalaya.ting.android.host.socialModule.imageviewer.util.Utils;
import com.ximalaya.ting.android.host.socialModule.imageviewer.view.ImageItemView;

/**
 * Created by qianmenchao on 2018/12/25.
 *
 * <AUTHOR>
 */
public class BasePhotoGesture implements IPhotoViewGesture {

    private float mDownX;
    private float mDownY;
    private float mCurrentViewX;
    private float mCurrentViewY;
    private float mAdjustImgWidth;
    private float mAdjustImgHeight;
    private float mExitYThreshold;
    private float mAlphaBase;
    private Drawable mBackground;
    private float mBackgroundAlpha;
    private float mPreviewWidth, mPreviewHeight;
    private ImageItemView mImageItemView;
    private TransitionParams mParam;
    private ViewGroup.LayoutParams mImageParams;
    protected float mCurScale;
    private float mOriginWidth;
    private float mOriginHeight;
    private float mAdjustScale;

    public BasePhotoGesture() {}

    @Override
    public void setMoveView(ImageItemView imageItemView) {
        this.mImageItemView = imageItemView;
        handViewData();
    }

    private void handViewData() {
        if (mImageItemView == null || mParam == null) {
            return;
        }
        ImageView photoView = getPhotoView();
        this.mImageParams = photoView.getLayoutParams();
        setBackground(mImageItemView.getBackground());
        Drawable drawable = photoView.getDrawable();
        if (drawable != null) {
            mOriginWidth = drawable.getIntrinsicWidth();
            mOriginHeight = drawable.getIntrinsicHeight();
        } else if (mParam.sourceViewWidth != 0 && mParam.sourceViewHeight != 0) {
            mOriginWidth = mParam.sourceViewWidth;
            mOriginHeight = mParam.sourceViewHeight;
        }
    }


    @Override
    public void setViewData(TransitionParams params) {
        this.mParam = params;
        handViewData();
    }

    public void setBackground(Drawable drawable) {
        if (drawable != null) {
            mBackground = drawable.mutate();
        } else {
            mBackground = null;
        }
    }

    private void preMove() {
        mBackgroundAlpha = 255;
        mPreviewWidth = mImageItemView.getPhotoView().getWidth();
        mPreviewHeight = mImageItemView.getPhotoView().getHeight();
        calculateValue(mPreviewHeight);
        mAdjustScale = Math.min(mPreviewWidth / mOriginWidth, mPreviewHeight / mOriginHeight);

    }

    private void calculateValue(float height) {
        mExitYThreshold = height / 3f;
        mAlphaBase = mExitYThreshold * 2;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        switch (ev.getAction() & ev.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                mDownX = ev.getX();
                mDownY = ev.getY();
                break;
            case MotionEvent.ACTION_MOVE:
                if (mImageItemView != null && mImageItemView.getScale() <= 1f) {
                    float diffX = ev.getX() - mDownX;
                    float diffY = ev.getY() - mDownY;
                    if (Math.abs(diffX) > 0 && Math.abs(diffX) < Math.abs(diffY)) {
                        preMove();
                        return true;
                    }
                }
                break;
            default:
                break;
        }
        return false;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int action = event.getAction();
        float x = event.getX();
        float y = event.getY();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                mDownX = x;
                mDownY = y;
                break;
            case MotionEvent.ACTION_MOVE:
                handActionMove(mDownX, mDownY, x, y);
                mDownX = x;
                mDownY = y;
                break;
            case MotionEvent.ACTION_UP:
                handActionUp();
                float diffX = x - mDownX;
                float diffY = y - mDownY;
                if(Math.abs(diffX)<10 && Math.abs(diffY)<10){
                    return false;
                }
                break;
            default:
                break;
        }
        return true;
    }

    private void handActionMove(float x1, float y1, float x2, float y2) {
        final float diffX = x2 - x1;
        final float diffY = y2 - y1;
        View imageView = mImageItemView.getPhotoView();
        final float viewX = imageView.getX() + diffX;
        final float viewY = imageView.getY() + diffY;
        scaleImage(viewX,viewY);
        moveImage(viewX,viewY);
        changeAlpha(viewY);
        Utils.pauseGif(mImageItemView.getPhotoView().getDrawable());
    }

    protected void scaleImage(float newX,float newY){
        ImageView imageView = getPhotoView();
        if (newY <= 0) {
            mCurScale = 1f;
            if (imageView.getY() > 0) {
                mImageParams.width = (int) (mPreviewWidth * mCurScale);
                mImageParams.height = (int) (mPreviewHeight * mCurScale);
                imageView.setLayoutParams(mImageParams);
            }
        } else {
            float minScaleWeight = 0.25f;
            mCurScale = Math.min(Math.max((1f - Math.abs(newY) / mPreviewHeight), minScaleWeight), 1);
            mImageParams.width = (int) (mPreviewWidth * mCurScale);
            mImageParams.height = (int) (mPreviewHeight * mCurScale);
            imageView.setLayoutParams(mImageParams);
        }
    }


    private void changeAlpha(float newY){
        ImageView imageView = getPhotoView();
        if (newY == 0) {
            mBackgroundAlpha = 255;
            if (imageView.getY() > 0 && mBackground != null) {
                mBackground.setAlpha((int)mBackgroundAlpha);
            }
        } else if(newY < 0 ){
            final float value = Math.abs(newY) / mAlphaBase;
            mBackgroundAlpha = (value <= 0.8f ? 1 - value : 0.2f) * 255;
            if (mBackground != null &&  imageView.getDrawable()!=null) {
                mBackground.setAlpha((int)mBackgroundAlpha);
            }
        }else {
            final float value = Math.abs(newY) / mAlphaBase;
            mBackgroundAlpha = (value <= 0.8f ? 1 - value : 0.2f) * 255;
            if (mBackground != null && imageView.getDrawable()!=null) {
                mBackground.setAlpha((int)mBackgroundAlpha);
            }
        }
    }

    protected void moveImage(float newX,float newY){
        ImageView imageView = getPhotoView();
        imageView.setX(newX);
        imageView.setY(newY);
    }

    private void handActionUp() {
        if (mPreviewWidth == 0) {
            preMove();
        }
        ImageView photoView = mImageItemView.getPhotoView();
        final float viewX = photoView.getX();
        final float viewY = photoView.getY();
        mAdjustImgWidth = mOriginWidth * mAdjustScale;
        mAdjustImgHeight = mOriginHeight * mAdjustScale;
        mCurrentViewX = viewX + (mPreviewWidth - mAdjustImgWidth) / 2 * mCurScale;
        mCurrentViewY = viewY + (mPreviewHeight - mAdjustImgHeight) / 2 * mCurScale;
        if (Math.abs(viewY) > mExitYThreshold) {
            startExit();
            return;
        }

        startRestore();
        Utils.resumeGif(mImageItemView.getPhotoView().getDrawable());
    }

    private void startRestore() {
        final View imageView = mImageItemView.getPhotoView();
        final float from_x = imageView.getX();
        final float from_y = imageView.getY();
        final float to_x = 0;
        final float to_y = 0;
        final float old_width = imageView.getWidth();
        final float old_height = imageView.getHeight();
        final float new_width = mPreviewWidth;
        final float new_height = mPreviewHeight;
        AnimationParams params = new AnimationParams();
        params.fromX = from_x;
        params.fromY = from_y;
        params.toX = to_x;
        params.toY = to_y;
        params.oldWidth = old_width;
        params.oldHeight = old_height;
        params.newWidth = new_width;
        params.newHeight = new_height;
        params.duration = 200;
        mImageItemView.startRestore(params);
    }

    protected ImageView getPhotoView() {
        return mImageItemView.getPhotoView();
    }

    private void startExit() {
        final ImageView imageView = getPhotoView();
        final TransitionParams viewData = mParam;
        final float from_x = mCurrentViewX;
        final float from_y = mCurrentViewY;
        final float toX = viewData.sourceViewX;
        final float toY = viewData.sourceViewY;
        final float old_width = mAdjustImgWidth * mCurScale;
        final float old_height = mAdjustImgHeight * mCurScale;
        final float new_width = viewData.sourceViewWidth;
        final float new_height = viewData.sourceViewHeight;
        boolean notUseCrop =  ((mCurrentViewX + mAdjustImgWidth * mCurScale) <= 0 || mCurrentViewX >= mPreviewWidth || mCurrentViewY >= mPreviewHeight);
        if(!notUseCrop){
            imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        }
        AnimationParams params = new AnimationParams();
        params.fromX = from_x;
        params.fromY = from_y;
        params.toX = toX;
        params.toY = toY;
        params.oldWidth = old_width;
        params.oldHeight = old_height;
        params.newWidth = new_width;
        params.newHeight = new_height;
        params.duration = 200;
        mImageItemView.startExitOnMove(params);
    }
}
