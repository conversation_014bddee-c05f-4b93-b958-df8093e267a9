package com.ximalaya.ting.android.host.live.video_beautify;

import android.text.TextUtils;

import androidx.annotation.WorkerThread;

import com.ximalaya.ting.android.host.manager.OnlyUseMainProcessSharePreUtil;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.live.ByteDanceMaterialVo;
import com.ximalaya.ting.android.host.model.nvs.MaterialInfo;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.io.File;
import java.io.FileInputStream;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件名称
 *
 * <AUTHOR>
 * @desc 文件描述
 * @email <EMAIL>
 * @wiki 说明文档的链接地址
 * @server 服务端开发人员放在这里
 * @since 2022/10/8 19:10
 */
public class BytesUtils {

    public static final int TYPE_FILTER_NUM = 2;
    public static final int TYPE_STICKER_NUM = 3;

    public static final String TYPE_FILTER = "FILTER";
    public static final String TYPE_STICKER = "STICKER";

    public static MaterialInfo convertByteModelToOld(ByteDanceMaterialVo model) {

        MaterialInfo res = new MaterialInfo();
        res.coverUrl = model.coverPath;
        res.displayName = model.showName;
        res.downloadUrl = model.resourcePath;
        res.sourceCode = model.key;
        res.id = model.id;

        if (model.type == TYPE_FILTER_NUM) {
            res.type = TYPE_FILTER;
        } else if (model.type == TYPE_STICKER_NUM) {
            res.type = TYPE_STICKER;
        }
        return res;

    }

    public static List<MaterialInfo> convertByteModelListToOld(List<ByteDanceMaterialVo> inputList) {

        if (inputList == null || inputList.isEmpty()) {
            return null;
        }

        List<MaterialInfo> resList = new ArrayList<>(inputList.size());

        for (ByteDanceMaterialVo model : inputList) {
            MaterialInfo materialInfo = convertByteModelToOld(model);
            resList.add(materialInfo);
        }

        return resList;
    }


    public static boolean isSameMaterial(MaterialInfo infoA, MaterialInfo infoB) {

        if (infoA == null && infoB == null) {
            return true;
        }

        if (infoA != null && infoB != null) {
            return TextUtils.equals(infoA.type, infoB.type) && infoA.id == infoB.id;
        }


        return false;

    }





    /**
     *  取消或者收藏道具
     * @param id 道具id
     * @param isCollect true是收藏道具
     */
    public static void CollectOrCancelSticker(int id ,
                                              boolean isCollect ,
                                              final IDataCallBack<String> callBack) {

        Map<String, String> params = new HashMap<>(2);
        // bgm type都为1
        params.put("type", BytesUtils.TYPE_STICKER_NUM + "");
        params.put("materialId", id + "");
        if (isCollect) {
            CommonRequestM.starByteMaterial(params, new IDataCallBack<String>() {
                @Override
                public void onSuccess(String object) {
                    if (callBack!=null) callBack.onSuccess(object);
                }

                @Override
                public void onError(int code, String message) {
                    if (callBack!=null) callBack.onError(code,message);
                }
            });
        } else {

            CommonRequestM.cancleByteStarMaterial(params, new IDataCallBack<String>() {
                @Override
                public void onSuccess(String object) {
                    if (callBack!=null) callBack.onSuccess(object);
                }

                @Override
                public void onError(int code, String message) {
                    if (callBack!=null) callBack.onError(code,message);
                }
            });
        }
    }

    public static MaterialInfo findTargetInList(int itemId, List<MaterialInfo> list) {
        if (itemId < 0 || list == null || list.isEmpty()) {
            return null;
        }

        for (MaterialInfo materialInfo : list) {
            if (materialInfo != null && materialInfo.id == itemId) {
                return materialInfo;
            }
        }

        return null;

    }


    /**
     * 获取各个美颜基础设置的默认值
     * @param itemId 美颜基础设置id
     * @return 设置的默认值
     */
    public static int getDefaultValueByItemId(int itemId) {

        switch (itemId) {
            case BeautifySettingConstants.ID_BASE_MOPI: {
                return BeautifySettingConstants.FACE_SMOOTH_DEFAULT;
            }
            case BeautifySettingConstants.ID_BASE_SHOULIAN: {
                return BeautifySettingConstants.RESHAPE_THINFACE_DEFAULT;
            }
            case BeautifySettingConstants.ID_BASE_DAYAN: {
                return BeautifySettingConstants.RESHAPE_BIGEYE_DEFAULT;
            }
            case BeautifySettingConstants.ID_BASE_QINXI: {
                return BeautifySettingConstants.FACE_SHARPEN_DEFAULT;
            }
            case BeautifySettingConstants.ID_BASE_MEIBAI: {
                return BeautifySettingConstants.FACE_WHITEN_DEFAULT;
            }
            case BeautifySettingConstants.ID_BASE_ZHAILIAN: {
                return BeautifySettingConstants.RESHAPE_SMALL_FACE_DEFAULT;
            }
            case BeautifySettingConstants.ID_BASE_QUANGU: {
                return BeautifySettingConstants.RESHAPE_QUANGU_DEFAULT;
            }
            case BeautifySettingConstants.ID_BASE_XIAEGU: {
                return BeautifySettingConstants.RESHAPE_XIAEGU_DEFAULT;
            }
            case BeautifySettingConstants.ID_BASE_SHOUBI: {
                return BeautifySettingConstants.RESHAPE_THIN_NOSE_DEFAULT;
            }
            case BeautifySettingConstants.ID_BASE_ZOUXIN: {
                return BeautifySettingConstants.RESHAPE_MOUSE_SHAPE_DEFAULT;
            }
            case BeautifySettingConstants.ID_BASE_FALINWEN: {
                return BeautifySettingConstants._4ITEMS_FALINWEN_DEFAULT;
            }

            default:
                break;
        }

        return -1;

    }







    @WorkerThread
    public static String md5(File file) {
        try {
            MessageDigest m = MessageDigest.getInstance("MD5");
            m.reset();
            FileInputStream fis = new FileInputStream(file);
            byte[] buf = new byte[1024 * 4]; // 4k buffer
            int l;
            while ((l = fis.read(buf, 0, buf.length)) != -1) {
                m.update(buf, 0, l);
            }
            fis.close();
            String md5 = byteArrayToHexString(m.digest());
            return md5;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static String byteArrayToHexString(byte[] b) {
        StringBuilder resultSb = new StringBuilder();
        for (int i = 0; i < b.length; i++) {
            resultSb.append(byteToHexString(b[i]));
        }
        return resultSb.toString();
    }

    private static String byteToHexString(byte b) {
        String[] hexDigits = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};
        int n = b;
        if (n < 0) {
            n = 0x100 + n;
        }
        int d1 = n >> 4;
        int d2 = n & 0xF;
        return hexDigits[d1] + hexDigits[d2];
    }








}
