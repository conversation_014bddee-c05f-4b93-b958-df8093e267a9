package com.ximalaya.ting.android.host.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.ximalaya.ting.android.host.car.CarLinkManager;

/**
 * 类的大体描述放在这里。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18621868330
 * @wiki Wiki网址放在这里
 * @server 服务端开发人员放在这里
 * @since 2020/2/28
 */
public class AbqReceiver extends BroadcastReceiver {
    //ABQ连接广播
    private static final String CONNECT_XIMALAYA = "HAP_BIND";
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (action == null) {
            return;
        }
        if (CONNECT_XIMALAYA.equals(action)) {//接收ABQ连接广播
            CarLinkManager carLinkManager = CarLinkManager.getInstance(context);
            carLinkManager.onABQConnect(intent);
        }
    }
}
