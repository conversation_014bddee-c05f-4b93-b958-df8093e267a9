package com.ximalaya.ting.android.host.view.popupwindow

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.view.BubbleTextView
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import kotlin.math.min

/**
 * Created by changle.fang on 2021-01-20.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
class BottomTabBubblePopupWindow constructor(context: Context) : PopupWindow(context) {

    private val mContentView: View = LayoutInflater.from(context).inflate(R.layout.host_popupwindow_bottom_tab_bubble, null)
    private var mTvContent: BubbleTextView? = null
    var mTabId : Int = 0
    private var mMaxWidth = BaseUtil.getScreenWidth(context) - BaseUtil.dp2px(context, 32f)

    init {
        mTvContent = mContentView.findViewById(R.id.host_tv_content)
        setBackgroundDrawable(null)
        isOutsideTouchable = true
        isTouchable = true
        isFocusable = true
        contentView = mContentView
        contentView.setOnClickListener { dismiss() }
        AutoTraceHelper.bindData(contentView, "", "")
    }

    fun setContent(content: String) {
        mTvContent?.let {
            it.text = content
            it.measure(0, 0)
            width = min(it.measuredWidth, mMaxWidth)
        }
    }

    fun setOffset(offset : Int) {
        mTvContent?.mOffset = offset
    }



}