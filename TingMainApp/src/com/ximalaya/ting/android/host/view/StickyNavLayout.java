package com.ximalaya.ting.android.host.view;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import android.widget.GridView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.OverScroller;
import android.widget.ScrollView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.handmark.pulltorefresh.library.PullToRefreshGridView;
import com.handmark.pulltorefresh.library.PullToRefreshRecyclerView;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.framework.view.refreshload.ShowToastRefreshLoadMoreListView;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.adapter.ChangeableTabAdapter;
import com.ximalaya.ting.android.host.listener.IStickyNavResource;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNScrollViewProvider;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * Created by xmly on 10/08/2018.
 *
 * <AUTHOR>
 * 专辑页导航控件
 */
public class StickyNavLayout extends LinearLayout {
    private static final String TAG = "StickyNavLayout";

    public static final int SCROLL_DOWN = 12;
    public static final int SCROLL_UP = 21;
    private boolean isStick;
    private int screenH = 0;

    public interface ScrollListener {
        void onScroll(int scrollY, int totalScrollY);

        void onScrollStop(int orient, int scrollY, int totalScrollY);

        void onScrollToEdge(int scrollY, int totalScrollY);

        void onStateChange(boolean isStick);
    }

    private View mTop;
    private View mNav;
    private ViewGroup mContent;
    private View mTopAdContent;

    private int mTopViewHeight;
    private int mTopViewHeightWithTitle;
    private int mTopOffset;//顶部偏移量
    private ViewGroup mInnerScrollView;
    private boolean isTopHidden = false;
    private boolean disableNullViewDeal = false;

    private final OverScroller mScroller;
    private VelocityTracker mVelocityTracker;
    private int mTouchSlop;
    private final int mMaximumVelocity;
    private final int mMinimumVelocity;

    private float mLastX;
    private float mLastY;
    private float mDownY;
    private int mVelocityY;
    private boolean mDragging;

    private boolean isInControl = false;
    private int lastItem = -1;
    private ScrollListener mScrollListener;
    private OnNavScrollListener mOnNavScrollListener;
    private boolean canScroll = true;
    private boolean canScrollVertical = true;
    private boolean shouldIgnore = false;
    private boolean mDealMoveEvent = false;
    private int mTopBottomMargin = 0;
    private boolean mDealScrollSelf = false;
    private Rect mTopViewRect;

    private IOnSizeChange mSizeChange;

    public StickyNavLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        setOrientation(LinearLayout.VERTICAL);

        mScroller = new OverScroller(context, new DecelerateInterpolator(10f));
        mTouchSlop = ViewConfiguration.get(context).getScaledTouchSlop() / 5;
        mMaximumVelocity = ViewConfiguration.get(context)
                .getScaledMaximumFlingVelocity();
        mMinimumVelocity = ViewConfiguration.get(context)
                .getScaledMinimumFlingVelocity();
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        mTop = findViewById(R.id.host_id_stickynavlayout_topview);
        mNav = findViewById(R.id.host_id_stickynavlayout_indicator);
        mContent = (ViewGroup) findViewById(R.id.host_id_stickynavlayout_content);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int height = MeasureSpec.getSize(heightMeasureSpec);
        ViewGroup.LayoutParams params = mContent.getLayoutParams();
        int navH = getNavHeight();
        if (screenH == 0) {
            screenH = BaseUtil.getNoVirtualNavBarScreenHeight(getContext());
        }
        // 先取控件本身的高度，会更准确
        height = (height > 0) ? height : screenH;
        params.height = height - navH - mTopOffset;
        super.onMeasure(widthMeasureSpec, MeasureSpec.makeMeasureSpec(height, MeasureSpec.UNSPECIFIED));
    }

    private int getNavHeight() {
        if (mNav != null && mNav.getVisibility() != GONE) {
            return mNav.getMeasuredHeight();
        } else {
            return 0;
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        int adHeight = 0;
        if (mTopAdContent != null && mTopAdContent.getVisibility() == View.VISIBLE && mTopAdContent.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams)mTopAdContent.getLayoutParams();
            adHeight = mTopAdContent.getMeasuredHeight() + lp.topMargin + lp.bottomMargin;
        }
        mTopViewHeightWithTitle = mTop.getMeasuredHeight() + adHeight;
        mTopViewHeight = mTopViewHeightWithTitle - mTopOffset;

        if (mSizeChange != null) {
            mSizeChange.onSizeChange(w, h, oldw, oldh);
        }
    }


    @Override
    public void addView(View child, int index, ViewGroup.LayoutParams params) {
        super.addView(child, index, params);
        int id = child.getId();
        if (id == R.id.host_id_stickynavlayout_topview) {
            mTop = child;
        } else if (id == R.id.host_id_stickynavlayout_indicator) {
            mNav = child;
        } else if (id == R.id.host_id_stickynavlayout_content) {
            mContent = (ViewGroup) child;
        } else if (id == R.id.host_id_stickynavlayout_top_ad_content) {
            mTopAdContent = child;
        }
    }

    public void setContentViewGroup(ViewGroup viewGroup) {
        if (viewGroup == null) {
            return;
        }
        mContent = viewGroup;
    }

    public void setTopViewHeight(int height) {
        mTopViewHeight = height;
        mTopViewHeightWithTitle = height;
    }

    public void setTopViewHeightWithTitle(int height) {
        mTopViewHeightWithTitle = height;
        mTopViewHeight = mTopViewHeightWithTitle - mTopOffset;
    }

    public int getTopViewHeight() {
        return mTopViewHeight;
    }

    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (!canScroll) {
            return true;
        }
        int action = ev.getAction();
        float y = ev.getY();
        float x = ev.getX();

        switch (action) {
            case MotionEvent.ACTION_DOWN:
                mLastY = y;
                mDownY = y;
                if (!mScroller.isFinished()) {
                    mScroller.abortAnimation();
                }
                break;
            case MotionEvent.ACTION_MOVE:
                float dy = y - mLastY;
                if (!canScrollVertical && Math.abs(x - mLastX) < Math.abs(dy)) {
                    return true;
                }
                if (mTop.getVisibility() == GONE) {
                    mTopViewHeight = -mTopOffset;
                } else {
                    mTopViewHeight = mTopViewHeightWithTitle - mTopOffset;
                    if (mTopBottomMargin > 0) {
                        mTopViewHeight -= mTopBottomMargin;
                    }
                }
                if (!mInnerScrollViewNeedCache) {
//                    mInnerScrollView = null;
                }
                getCurrentScrollView();

                if (mInnerScrollView instanceof ScrollView) {
                    if (mInnerScrollView.getScrollY() == 0 && isTopHidden && dy > 0
                            && !isInControl) {
                        isInControl = true;
                        ev.setAction(MotionEvent.ACTION_CANCEL);
                        MotionEvent ev2 = MotionEvent.obtain(ev);
                        dispatchTouchEvent(ev);
                        ev2.setAction(MotionEvent.ACTION_DOWN);
                        return dispatchTouchEvent(ev2);
                    }
                } else if (mInnerScrollView instanceof ListView) {

                    ListView lv = (ListView) mInnerScrollView;
                    if (mDealMoveEvent) {
                        int firstVisiblePos = lv.getFirstVisiblePosition();
                        View c = lv.getChildAt(0);
                        boolean dispatchToChild = firstVisiblePos < 1 && !isInControl && c != null
                                && c.getTop() == 0 && isTopHidden && dy > 0;
                        if (dispatchToChild) {
                            isInControl = true;
                            ev.setAction(MotionEvent.ACTION_CANCEL);
                            MotionEvent ev2 = MotionEvent.obtain(ev);
                            dispatchTouchEvent(ev);
                            ev2.setAction(MotionEvent.ACTION_DOWN);
                            return dispatchTouchEvent(ev2);
                        }
                    } else {
                        View c = lv.getChildAt(lv.getFirstVisiblePosition());
                        //下滑情况下，如果TopView在展示中，ListView第一个item没有到顶或者TopView隐藏，ListView第一个item到顶。需要分发给ListView
                        boolean dispatchToChild = !isInControl && c != null && ((c.getTop() > 0 && !isTopHidden) || ((c.getTop() == 0 && isTopHidden)))
                                && dy > 0;
                        if (dispatchToChild) {
                            isInControl = true;
                            ev.setAction(MotionEvent.ACTION_CANCEL);
                            MotionEvent ev2 = MotionEvent.obtain(ev);
                            dispatchTouchEvent(ev);
                            ev2.setAction(MotionEvent.ACTION_DOWN);
                            return dispatchTouchEvent(ev2);
                        }
                    }
                } else if (mInnerScrollView instanceof RefreshLoadMoreListView || mInnerScrollView instanceof ShowToastRefreshLoadMoreListView) {
                    ListView lv = null;
                    if (mInnerScrollView instanceof RefreshLoadMoreListView) {
                        lv = ((RefreshLoadMoreListView) mInnerScrollView).getRefreshableView();
                    } else if (mInnerScrollView instanceof ShowToastRefreshLoadMoreListView) {
                        lv = ((ShowToastRefreshLoadMoreListView) mInnerScrollView).getRefreshableView();
                    }

                    View c = lv.getChildAt(0);
                    if (mDealMoveEvent) {
                        int firstVisiblePos = lv.getFirstVisiblePosition();
                        if (firstVisiblePos < 1 && !isInControl && c != null && c.getTop() == 0
                                && isTopHidden && dy > 0) {
                            isInControl = true;
                            ev.setAction(MotionEvent.ACTION_CANCEL);
                            MotionEvent ev2 = MotionEvent.obtain(ev);
                            dispatchTouchEvent(ev);
                            ev2.setAction(MotionEvent.ACTION_DOWN);
                            return dispatchTouchEvent(ev2);
                        }
                    } else {
                        Logger.d("stickNavLayout", "mInnerScrollView dispatchTouchEvent "
                                + (!isInControl && isTopHidden && (dy > 0) && c != null && c.getTop() == 0));
                        if (!isInControl && c != null && c.getTop() == 0 && isTopHidden
                                && dy > 0) {
                            isInControl = true;
                            ev.setAction(MotionEvent.ACTION_CANCEL);
                            MotionEvent ev2 = MotionEvent.obtain(ev);
                            dispatchTouchEvent(ev);
                            ev2.setAction(MotionEvent.ACTION_DOWN);
                            return dispatchTouchEvent(ev2);
                        }
                    }
                } else if (mInnerScrollView instanceof RecyclerView) {
                    RecyclerView rv = (RecyclerView) mInnerScrollView;
                    View c = rv.getChildAt(0);
                    boolean dispatchToChild = !isInControl && c != null && c.getTop() == 0 && isTopHidden
                            && dy > 0;
                    if (mDealMoveEvent) {
                        dispatchToChild = !isInControl && !rv.canScrollVertically(-1) && isTopHidden && dy > 0;
                    }
                    if (dispatchToChild) {
                        isInControl = true;
                        ev.setAction(MotionEvent.ACTION_CANCEL);
                        MotionEvent ev2 = MotionEvent.obtain(ev);
                        dispatchTouchEvent(ev);
                        ev2.setAction(MotionEvent.ACTION_DOWN);
                        return dispatchTouchEvent(ev2);
                    }
                } else if (mInnerScrollView instanceof PullToRefreshRecyclerView) {

                    RecyclerView lv = ((PullToRefreshRecyclerView) mInnerScrollView).getRefreshableView();

                    boolean canScrollUp = lv.canScrollVertically(-1);

                    if (!isInControl && !canScrollUp && isTopHidden
                            && dy > 0) {
                        isInControl = true;
                        ev.setAction(MotionEvent.ACTION_CANCEL);
                        MotionEvent ev2 = MotionEvent.obtain(ev);
                        dispatchTouchEvent(ev);
                        ev2.setAction(MotionEvent.ACTION_DOWN);
                        return dispatchTouchEvent(ev2);
                    }

                } else if (mInnerScrollView instanceof GridView) {
                    GridView lv = (GridView) mInnerScrollView;
                    View c = lv.getChildAt(lv.getFirstVisiblePosition());

                    if (!isInControl && c != null && c.getTop() == 0 && isTopHidden && dy > 0) {
                        isInControl = true;
                        ev.setAction(MotionEvent.ACTION_CANCEL);
                        MotionEvent ev2 = MotionEvent.obtain(ev);
                        dispatchTouchEvent(ev);
                        ev2.setAction(MotionEvent.ACTION_DOWN);
                        return dispatchTouchEvent(ev2);
                    }
                } else if (mInnerScrollView instanceof PullToRefreshGridView) {
                    GridView gv = ((PullToRefreshGridView) mInnerScrollView).getRefreshableView();
                    View c = gv.getChildAt(gv.getFirstVisiblePosition());
                    if (!isInControl && c != null && c.getTop() == 0 && isTopHidden
                            && dy > 0) {
                        isInControl = true;
                        ev.setAction(MotionEvent.ACTION_CANCEL);
                        MotionEvent ev2 = MotionEvent.obtain(ev);
                        dispatchTouchEvent(ev);
                        ev2.setAction(MotionEvent.ACTION_DOWN);
                        return dispatchTouchEvent(ev2);
                    }
                } else if (mInnerScrollView == null) {
                    if (isTopHidden && dy > 0 && !isInControl && mIsInInterceptState) {
                        isInControl = true;
                        ev.setAction(MotionEvent.ACTION_CANCEL);
                        MotionEvent ev2 = MotionEvent.obtain(ev);
                        dispatchTouchEvent(ev);
                        ev2.setAction(MotionEvent.ACTION_DOWN);
                        return dispatchTouchEvent(ev2);
                    }
                }
                break;
            default:
                break;
        }
        Logger.d("stickNavLayout", "super dispatchTouchEvent " + (ev.getAction() == MotionEvent.ACTION_MOVE));
        return super.dispatchTouchEvent(ev);
    }

    private boolean mIsInInterceptState = false;

    /**
     *
     */
    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (!canScroll) {
            return true;
        }
        final int action = ev.getAction();
        float x = ev.getX();
        float y = ev.getY();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                mLastX = x;
                mLastY = y;
                mDownY = y;
                break;
            case MotionEvent.ACTION_MOVE:
                float dx = x - mLastX;
                float dy = y - mLastY;
                if (!canScrollVertical && Math.abs(dx) < Math.abs(dy)) {
                    return true;
                }
                if (Math.abs(dx) > Math.abs(dy)) {
                    return false;
                }
                if (!mInnerScrollViewNeedCache) {
                    mInnerScrollView = null;
                }
                getCurrentScrollView();
                if (Math.abs(dy) > mTouchSlop) {
                    mDragging = true;
                    if (mInnerScrollView instanceof ScrollView) {
                        // 如果topView没有隐藏
                        // 或sc的scrollY = 0 && topView隐藏 && 下拉，则拦截
                        boolean isIntercept = (!isTopHidden) && dy < 0
                                || (mInnerScrollView.getScrollY() == 0
                                && isTopHidden && dy > 0);
                        if (isIntercept) {

                            initVelocityTrackerIfNotExists();
                            mVelocityTracker.addMovement(ev);
                            mLastY = y;
                            mIsInInterceptState = true;
                            return true;
                        }
                    } else if (mInnerScrollView instanceof ListView) {

                        ListView lv = (ListView) mInnerScrollView;
                        int firstPosition = lv.getFirstVisiblePosition();
                        if (mDealMoveEvent) {
                            View c = lv.getChildAt(0);
                            boolean isIntercept = (!isTopHidden && (dy < 0 || (firstPosition < 1 && (c == null || c.getTop() == 0)))) ||
                                    ((firstPosition < 1 && (c == null || c.getTop() == 0)) && isTopHidden && dy > 0);
                            if (isIntercept) {

                                initVelocityTrackerIfNotExists();
                                mVelocityTracker.addMovement(ev);
                                mLastY = y;
                                mIsInInterceptState = true;
                                return true;
                            }
                        } else {
                            View c = lv.getChildAt(firstPosition);
                            // 如果topView没有隐藏
                            // 或sc的listView在顶部 && topView隐藏 && 下拉，则拦截
                            boolean isIntercept = (!isTopHidden && firstPosition <= 1 && c.getTop() == 0) || //
                                    (c != null //
                                            && c.getTop() == 0//
                                            && isTopHidden && dy > 0);
                            if (isIntercept) {

                                initVelocityTrackerIfNotExists();
                                mVelocityTracker.addMovement(ev);
                                mLastY = y;
                                mIsInInterceptState = true;
                                return true;
                            }
                        }
                    } else if (mInnerScrollView instanceof RefreshLoadMoreListView || mInnerScrollView instanceof ShowToastRefreshLoadMoreListView) {
                        ListView lv = null;
                        RefreshLoadMoreListView refreshLoadMoreListView = null;
                        if (mInnerScrollView instanceof RefreshLoadMoreListView) {
                            lv = ((RefreshLoadMoreListView) mInnerScrollView).getRefreshableView();
                            refreshLoadMoreListView = (RefreshLoadMoreListView) mInnerScrollView;
                        } else if (mInnerScrollView instanceof ShowToastRefreshLoadMoreListView) {
                            lv = ((ShowToastRefreshLoadMoreListView) mInnerScrollView).getRefreshableView();
                            refreshLoadMoreListView = (RefreshLoadMoreListView) ((ShowToastRefreshLoadMoreListView) mInnerScrollView).getRefreshListView();
                        }

                        View c = lv.getChildAt(0);
                        if (mDealMoveEvent) {
                            Logger.d("stickNavLayout", "interceptTouchEvent mInnerScrollView RefreshLoadMoreListView");
                            // 如果topView没有隐藏
                            // 或sc的listView在顶部 && topView隐藏 && 下拉，则拦截

                            //fix bug 在顶部的时候下拉刷新失效
                            //fix bug 头部全展示，RefreshLoadMoreListView禁用下拉刷新时，先下拉不松手再上滑，头部未顶上去
                            if (getScrollY() == 0 && dy > 0 && refreshLoadMoreListView.isPullToRefreshEnabled()) {
                                return false;
                            }

                            Logger.d("stickNavLayout", "interceptTouchEvent mInnerScrollView RefreshLoadMoreListView2");

                            if (shouldIgnore) {
                                // 修复：售前页视频全屏时，仍能从边角呼出该控件，原因暂时未知 TODO
                                return false;
                            }

                            Logger.d("stickNavLayout", "interceptTouchEvent mInnerScrollView RefreshLoadMoreListView3");

                            int firstVisiblePos = lv.getFirstVisiblePosition();

                            boolean isToTop = (!isTopHidden && (dy < 0 || (firstVisiblePos < 1 && (c == null || c.getTop() == 0)))) ||
                                    ((firstVisiblePos < 1 && (c == null || c.getTop() == 0)) && isTopHidden && dy > 0);
                            Logger.d("stickNavLayout", "interceptTouchEvent mInnerScrollView "+isTopHidden+" "+firstVisiblePos+" "+c.getTop()+" "+dy);
                            if (isToTop) {
                                initVelocityTrackerIfNotExists();
                                mVelocityTracker.addMovement(ev);
                                mLastY = y;
                                mIsInInterceptState = true;
                                return true;
                            }
                        } else {
                            Logger.d("stickNavLayout", "interceptTouchEvent mInnerScrollView RefreshLoadMoreListView");
                            // 如果topView没有隐藏
                            // 或sc的listView在顶部 && topView隐藏 && 下拉，则拦截

                            //fix bug 在顶部的时候下拉刷新失效
                            //fix bug 头部全展示，RefreshLoadMoreListView禁用下拉刷新时，先下拉不松手再上滑，头部未顶上去
                            if (getScrollY() == 0 && dy > 0 && refreshLoadMoreListView.isPullToRefreshEnabled()) {
                                return false;
                            }

                            Logger.d("stickNavLayout", "interceptTouchEvent mInnerScrollView RefreshLoadMoreListView2");

                            if (shouldIgnore) {
                                // 修复：售前页视频全屏时，仍能从边角呼出该控件，原因暂时未知 TODO
                                return false;
                            }

                            Logger.d("stickNavLayout", "interceptTouchEvent mInnerScrollView RefreshLoadMoreListView3");

                            Logger.d("stickNavLayout", "interceptTouchEvent " + isTopHidden + ", "
                                    + (dy < 0) + ", " + (c != null && c.getTop() == 0));

                            int firstVisiblePos = lv.getFirstVisiblePosition();
                            //fix by zale.zhang 解决上滑时顶部没有顶上去的bug
                            boolean isToTop = ((!isTopHidden && (dy < 0 || (c == null || c.getTop() == 0))) || //
                                    ((c == null || c.getTop() == 0) && isTopHidden && dy > 0));
                            if (isToTop) {
                                initVelocityTrackerIfNotExists();
                                mVelocityTracker.addMovement(ev);
                                mLastY = y;
                                mIsInInterceptState = true;
                                return true;
                            }
                        }
                    } else if (mInnerScrollView instanceof PullToRefreshRecyclerView) {
                        RecyclerView recyclerView = ((PullToRefreshRecyclerView) mInnerScrollView).getRefreshableView();
                        int firstPosition = 0;
                        View c = null;
                        if (recyclerView.getLayoutManager() instanceof LinearLayoutManager) {
                            firstPosition = ((LinearLayoutManager) recyclerView.getLayoutManager()).findFirstVisibleItemPosition();
                            c = recyclerView.getLayoutManager().getChildAt(0);
                        } else if (recyclerView.getLayoutManager() instanceof StaggeredGridLayoutManager) {
                            StaggeredGridLayoutManager layoutManager = (StaggeredGridLayoutManager) recyclerView.getLayoutManager();
                            int[] positions = new int[layoutManager.getSpanCount()];
                            layoutManager.findFirstVisibleItemPositions(positions);
                            firstPosition = positions[0];
                            c = layoutManager.getChildAt(0);
                        }
                        // 如果topView没有隐藏
                        // 或sc的listView在顶部 && topView隐藏 && 下拉，则拦截

                        //fix bug 在顶部的时候下拉刷新失效
                        if (getScrollY() == 0 && dy > 0) {
                            return false;
                        }

                        //fix by zale.zhang 解决上滑时顶部没有顶上去的bug
                        boolean isToTop = ((!isTopHidden && (dy < 0 || (c == null || c.getTop() == 0))) ||
                                ((c == null || c.getTop() == 0)
                                        && firstPosition <= 0  // fix recyclerView无内容时无法下滑显示头部的bug
                                        && isTopHidden && dy > 0));
                        if (isToTop) {

                            initVelocityTrackerIfNotExists();
                            mVelocityTracker.addMovement(ev);
                            mLastY = y;
                            mIsInInterceptState = true;
                            return true;
                        }
                    } else if (mInnerScrollView instanceof RecyclerView) {
                        RecyclerView rv = (RecyclerView) mInnerScrollView;
                        View c = rv.getChildAt(0);
                        int firstPosition = 0;
                        if (rv.getLayoutManager() instanceof LinearLayoutManager) {
                            firstPosition = ((LinearLayoutManager) rv.getLayoutManager()).findFirstVisibleItemPosition();
                            c = rv.getLayoutManager().getChildAt(0);
                        }
                        Rect viewBound = new Rect();
                        if (c != null) {
                            rv.getDecoratedBoundsWithMargins(c, viewBound);
                        }
                        boolean isIntercept = (!isTopHidden && (dy < 0 || (c == null || viewBound.top == 0))) ||
                                ((c == null || viewBound.top == 0) && firstPosition <= 0 && dy > 0);
                        Logger.e(TAG, String.valueOf("viewBound.top "+viewBound.top+" firstPosition"+firstPosition+" c"+ c==null +" dy:"+dy));
//                        boolean isIntercept = !isTopHidden || c != null && viewBound.top == 0 && dy > 0;
                        if (isIntercept) {
                            initVelocityTrackerIfNotExists();
                            mVelocityTracker.addMovement(ev);
                            mLastY = y;
                            mIsInInterceptState = true;
                            return true;
                        }
                    } else if (mInnerScrollView instanceof GridView) {
                        GridView lv = (GridView) mInnerScrollView;
                        View c = lv.getChildAt(lv.getFirstVisiblePosition());
                        // 如果topView没有隐藏
                        // 或sc的listView在顶部 && topView隐藏 && 下拉，则拦截

                        boolean isIntercept = (!isTopHidden && lv.getFirstVisiblePosition() <= 1) || //
                                (c != null //
                                        && c.getTop() == 0//
                                        && isTopHidden && dy > 0);
                        if (isIntercept) {

                            initVelocityTrackerIfNotExists();
                            mVelocityTracker.addMovement(ev);
                            mLastY = y;
                            mIsInInterceptState = true;
                            return true;
                        }
                    } else if (mInnerScrollView instanceof PullToRefreshGridView) {
                        GridView gv = ((PullToRefreshGridView) mInnerScrollView).getRefreshableView();
                        View c = gv.getChildAt(gv.getFirstVisiblePosition());
                        // 如果topView没有隐藏
                        // 或sc的listView在顶部 && topView隐藏 && 下拉，则拦截

                        //fix bug 在顶部的时候下拉刷新失效
                        if (getScrollY() == 0 && dy > 0) {
                            return false;
                        }

                        //fix by zale.zhang 解决上滑时顶部没有顶上去的bug
                        boolean isToTop = (!isTopHidden || //
                                (c != null //
                                        && c.getTop() == 0//
                                        && isTopHidden && dy > 0));
                        if (isToTop) {

                            initVelocityTrackerIfNotExists();
                            mVelocityTracker.addMovement(ev);
                            mLastY = y;
                            mIsInInterceptState = true;
                            return true;
                        }
                    } else if (mInnerScrollView == null) {
                        if (!disableNullViewDeal) {
                            // 目前主要是针对rn的页面，没法提供innerScrollView出来。这里没判断是否rn页面，因为这个逻辑感觉是可以通用的
                            boolean isIntercept = !isTopHidden;
                            if (isIntercept || mIsInInterceptState) {
                                initVelocityTrackerIfNotExists();
                                mVelocityTracker.addMovement(ev);
                                mLastY = y;
                                mIsInInterceptState = true;
                                return true;
                            }
                        }
                    }
                }
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                mDragging = false;
                mIsInInterceptState = false;
                recycleVelocityTracker();
                break;
            default:
                break;
        }

        Logger.i(TAG, "mIsInInterceptState: " + mIsInInterceptState);
        return super.onInterceptTouchEvent(ev);
    }

    @Override
    public void requestDisallowInterceptTouchEvent(boolean disallowIntercept) {
        super.requestDisallowInterceptTouchEvent(disallowIntercept);
        mIsInInterceptState = !disallowIntercept;
    }

    private void getCurrentScrollView() {
        //mark modify at 170428
        if (mContent instanceof ViewPager) {
            int currentItem = ((ViewPager) mContent).getCurrentItem();
            //当adapter中的fragment被替换成其他的fragment的时候，lastItem还是currentItem的时候
            //可以使用resetCurrentPageStatus()来解决
            if (currentItem == lastItem && mInnerScrollView != null) {
                return;
            } else {
                lastItem = currentItem;
            }


            //mark modify at 170428
            PagerAdapter a = ((ViewPager) mContent).getAdapter();
            if (a instanceof IStickyNavResource) {
                Fragment fra = (Fragment) a.instantiateItem(mContent, currentItem);
                if (fra != null && fra.getView() != null) {
                    mInnerScrollView = (ViewGroup) fra.getView().findViewById(((IStickyNavResource) a).getScrollViewId());
                }
            } else if (a instanceof ChangeableTabAdapter) {
                Fragment fragment = ((ChangeableTabAdapter) a).getFragmentAtPosition(currentItem);
                if (fragment instanceof IStickyNavLayoutScrollViewProvider) {
                    mInnerScrollView = ((IStickyNavLayoutScrollViewProvider) fragment).getScrollView();
                }
            } else if (a instanceof FragmentPagerAdapter) {
                FragmentPagerAdapter fadapter = (FragmentPagerAdapter) a;
                Fragment item = (Fragment) fadapter.instantiateItem(mContent,
                        currentItem);
                if (item != null && item.getView() != null) {
                    mInnerScrollView = (ViewGroup) (item.getView()
                            .findViewById(R.id.host_id_stickynavlayout_innerscrollview));

                    if (mInnerScrollView == null && item instanceof IMainFunctionAction.ICommentTabFragment) {

                        IMainFunctionAction.ICommentTabFragment commentTabFragment = (IMainFunctionAction.ICommentTabFragment) item;
                        mInnerScrollView = (ViewGroup) commentTabFragment.getInnerScrollView();
                    }
                }
            } else if (a instanceof FragmentStatePagerAdapter) {
                FragmentStatePagerAdapter fsAdapter = (FragmentStatePagerAdapter) a;
                Fragment item = (Fragment) fsAdapter.instantiateItem(mContent,
                        currentItem);
                if (item != null && item instanceof IRNScrollViewProvider) {
                    mInnerScrollView = ((IRNScrollViewProvider) item).getScrollView();
                } else if (item != null && item.getView() != null) {
                    mInnerScrollView = (ViewGroup) (item.getView()
                            .findViewById(R.id.host_id_stickynavlayout_innerscrollview));

                    if (mInnerScrollView == null && item instanceof IMainFunctionAction.ICommentTabFragment) {

                        IMainFunctionAction.ICommentTabFragment commentTabFragment = (IMainFunctionAction.ICommentTabFragment) item;
                        mInnerScrollView = (ViewGroup) commentTabFragment.getInnerScrollView();
                    }
                }
            }
        } else {
            mInnerScrollView = mContent;
        }

    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!canScroll) {
            return true;
        }
        initVelocityTrackerIfNotExists();
        mVelocityTracker.addMovement(event);
        int action = event.getAction();
        float y = event.getY();
        float x = event.getX();

        switch (action) {
            case MotionEvent.ACTION_DOWN:
                if (!mScroller.isFinished())
                    mScroller.abortAnimation();
                mLastY = y;
                mDownY = y;
                return true;
            case MotionEvent.ACTION_MOVE:
                float dy = y - mLastY;
                if (!canScrollVertical && Math.abs(x - mLastX) < Math.abs(dy)) {
                    return true;
                }

                if (!mDragging && Math.abs(dy) > mTouchSlop) {
                    mDragging = true;
                }
                if (mDragging) {
                    scrollBy(0, (int) -dy);
                    if (mScrollListener != null) {
                        mScrollListener.onScroll(getScrollY(), mTopViewHeight);
                    }

                    // 如果topView隐藏，且上滑动时，则改变当前事件为ACTION_DOWN
                    if (getScrollY() == mTopViewHeight && dy < 0) {
                        event.setAction(MotionEvent.ACTION_DOWN);
                        dispatchTouchEvent(event);
                        isInControl = false;
                        mIsInInterceptState = false;
                    }
                }
                mLastY = y;
                break;
            case MotionEvent.ACTION_CANCEL:
                mDragging = false;
                recycleVelocityTracker();
                if (!mScroller.isFinished()) {
                    mScroller.abortAnimation();
                }
                break;
            case MotionEvent.ACTION_UP:
                mDragging = false;
                mVelocityTracker.computeCurrentVelocity(1000, mMaximumVelocity);
                int velocityY = mVelocityY = (int) mVelocityTracker.getYVelocity();
                if (Math.abs(velocityY) > mMinimumVelocity) {
                    fling(-velocityY);
                } else {
                    if (mScrollListener != null) {
                        int orient = 0;
                        if (y - mDownY > 0) {
                            orient = SCROLL_DOWN;
                        } else if (y - mDownY < 0) {
                            orient = SCROLL_UP;
                        }
                        mScrollListener.onScrollStop(orient, getScrollY(), mTopViewHeight);
                    }
                }
                recycleVelocityTracker();
                break;
            default:
                break;
        }

        return super.onTouchEvent(event);
    }

    private void fling(int velocityY) {
        mScroller.fling(0, getScrollY(), 0, velocityY, 0, 0, 0, mTopViewHeight);
        invalidate();
    }

    @Override
    public void scrollTo(int x, int y) {
        if (y > mTopViewHeight) {
            y = mTopViewHeight;
        }

        if (y < 0) {
            y = 0;
        }

        if (y != getScrollY() && !needInterceptScroll(y)) {
            super.scrollTo(x, y);
        }

        isTopHidden = getScrollY() == mTopViewHeight;

        if (mOnNavScrollListener != null) {
            mOnNavScrollListener.scroll(getScrollY());
        }

        if (mScrollListener != null) {
            if ((y == 0 || y == mTopViewHeight)) {
                mScrollListener.onScrollToEdge(y, mTopViewHeight);
                if (!isStick && y == mTopViewHeight) {
                    isStick = true;
                    mScrollListener.onStateChange(true);
                } else if (isStick && y != mTopViewHeight) {
                    isStick = false;
                    mScrollListener.onStateChange(false);
                }
            } else {
                if (isStick) {
                    isStick = false;
                    mScrollListener.onStateChange(false);
                }
            }
        }
    }

    private boolean needInterceptScroll(int y) {
        if (mDealScrollSelf && y > getScrollY()) {
            if (mTopViewRect == null) {
                mTopViewRect = new Rect();
            }

            return mTop.getGlobalVisibleRect(mTopViewRect) &&
                    mTopViewRect.bottom < BaseUtil.getScreenHeight(getContext())
                            - BaseUtil.dp2px(getContext(), 100);
        }

        return false;
    }

    public void resetState() {
        isTopHidden = false;
        mDragging = false;
        mLastY = 0;
        isInControl = false;
        recycleVelocityTracker();
    }


    /**
     * 当adapter中的fragment被替换成其他的fragment的时候，
     * 在getCurrentScrollView中。lastItem并没有变化就触发了return然后造成头部无法下来，
     * 而且mInnerScrollView也是之前被销毁view的持有，也可能造成内存泄漏问题
     * <p>
     * 此方法将lastItem，mInnerScrollView进行重置
     * viewPager的adapter被重新设置或者其中的fragment被替换成其他的，可以调用滑动到顶部+调用本方法进行处理
     */
    public void resetCurrentPageStatus() {
        resetState();
        lastItem = -1;
        mInnerScrollView = null;
    }

    private boolean mInnerScrollViewNeedCache = true;
    public void setScrollViewNeedCache(boolean needCache) {
        mInnerScrollViewNeedCache = needCache;
    }

    public boolean isTopHidden() {
        return isTopHidden;
    }

    public void setTopHidden(boolean topHidden) {
        isTopHidden = topHidden;
    }

    public void disableNullDeal(boolean disable) {
        disableNullViewDeal = disable;
    }

    @Override
    public void computeScroll() {
        if (mScroller.computeScrollOffset()) {
            scrollTo(0, mScroller.getCurrY());
            invalidate();
            if (!mDragging) {
                if (mScrollListener != null) {
                    mScrollListener.onScroll(getScrollY(), mTopViewHeight);
                }
            }
        } else {
            if (mScrollListener != null
                    && !mDragging
                    && (Math.abs(mVelocityY) > mMinimumVelocity)) {
                //fling滑动停止
                int orient = 0;
                if (mVelocityY > 0) {
                    orient = SCROLL_DOWN;
                } else if (mVelocityY < 0) {
                    orient = SCROLL_UP;
                }
                mScrollListener.onScrollStop(orient, getScrollY(), mTopViewHeight);
            }
        }
    }

    private void initVelocityTrackerIfNotExists() {
        if (mVelocityTracker == null) {
            mVelocityTracker = VelocityTracker.obtain();
        }
    }

    private void recycleVelocityTracker() {
        if (mVelocityTracker != null) {
            mVelocityTracker.recycle();
            mVelocityTracker = null;
        }
    }

    public void setScrollListener(ScrollListener mScrollListener) {
        this.mScrollListener = mScrollListener;
    }

    public void startScroll(int startY, int dY, int duration) {
        mScroller.startScroll(0, startY, 0, dY, duration);
        invalidate();
    }

    public void smoothScrollToTop() {
        startScroll(getScrollY(), -getScrollY(), 500);
    }

    public void smoothScrollToNav() {
        if (!isTopHidden) {
            startScroll(getScrollY(), mTopViewHeightWithTitle - mTopOffset - getScrollY(), 500);
        }
    }

    public void scrollToNav() {
        if (!isTopHidden) {
            scrollTo(0, mTopViewHeightWithTitle - mTopOffset);
        }
    }

    public void setTopOffset(int titleHeight) {
        mTopOffset = titleHeight;
    }

    public int getTopOffset() {
        return mTopOffset;
    }

    public void setOnNavScrollListener(OnNavScrollListener listener) {
        this.mOnNavScrollListener = listener;
    }

    public interface OnNavScrollListener {
        void scroll(int scrollY);
    }

    public void setTouchSlop(int touchSlop) {
        mTouchSlop = touchSlop;
    }

    public void setShouldIgnore(boolean shouldIgnore) {
        this.shouldIgnore = shouldIgnore;
    }

    public boolean canScroll() {
        return canScroll;
    }

    public void setCanScroll(boolean canScroll) {
        this.canScroll = canScroll;
    }

    public boolean isCanScrollVertical() {
        return canScrollVertical;
    }

    public void setCanScrollVertical(boolean canScrollVertical) {
        this.canScrollVertical = canScrollVertical;
    }

    public void setDealMoveEvent(boolean dealMoveEvent) {
        this.mDealMoveEvent = dealMoveEvent;
    }

    public void setTopBottomMargin(int margin) {
        mTopBottomMargin = margin;
    }

    public void isDealScrollSelf(boolean bool) {
        mDealScrollSelf = bool;
    }

    public interface IStickyNavLayoutScrollViewProvider {
        ViewGroup getScrollView();
    }

    public interface IStickyContentInvisibleHeightChangeListener {
        void onStickyContentInvisibleHeightChanged(int invisibleHeight);
    }

    public interface IOnSizeChange {
        void onSizeChange(int w, int h, int oldw, int oldh);
    }

    public void setSizeChange(IOnSizeChange sizeChange) {
        mSizeChange = sizeChange;
    }
}
