package com.ximalaya.ting.android.host.view.recyclerview

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.absoluteValue

/**
 * Created by WolfXu on 2020-12-01.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
class StartScaleSnapHelper(
        private val mMinScale: Float,
        private val mMaxScale: Float,
        private val mScaleChangeDistance: Int,
        flingOffset: Int) : StartSnapHelper(flingOffset) {

    override fun attachToRecyclerView(recyclerView: RecyclerView?) {
        super.attachToRecyclerView(recyclerView)
        recyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                updateItemViewScale()
            }
        })
    }

    private fun updateItemViewScale() {
        val recyclerView = mRecyclerView ?: return
        val layoutManager = recyclerView.layoutManager ?: return
        for (i in 0 until recyclerView.childCount) {
            val itemView = recyclerView.getChildAt(i)
            val distanceToStart = distanceToStart(layoutManager, itemView, getHorizontalHelper(layoutManager)).absoluteValue
            if (distanceToStart >= mScaleChangeDistance) {
                updateViewScale(itemView, mMinScale)
            } else {
                val scale = (mScaleChangeDistance - distanceToStart) * (mMaxScale - mMinScale) /
                        mScaleChangeDistance + mMinScale
                updateViewScale(itemView, scale)
            }
        }
    }

    private fun updateViewScale(view: View, scale: Float) {
        view.scaleX = scale
        view.scaleY = scale
        val pivotY = view.height.toFloat()
        if (view.pivotY != pivotY) {
            view.pivotY = pivotY
        }
    }
}