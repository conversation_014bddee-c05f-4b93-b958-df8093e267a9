package com.ximalaya.ting.android.host.view;

import android.view.View;

import androidx.viewpager.widget.ViewPager;

import com.ximalaya.ting.android.framework.util.BaseUtil;

/**
 * <AUTHOR>
 * @Time 2022/11/15
 * @Description
 */
public class TransformA implements ViewPager.PageTransformer {
    public TransformA(ViewPager boundViewPager) {
    }

    @Override
    public void transformPage(View view, float position) {
        //缩放比例
        float scale;
        float alpha;
        if (position <= 0.0f) {         // 前一页
            scale = 1 + 0.2f * position;
            alpha = 1 + 0.2f * position;
            if (position > -0.5) {
                view.setAlpha(alpha);
            } else {
                view.setAlpha(1);
            }
            if (Float.isNaN(scale)) {
                scale = 0;
            }
            view.setScaleX(scale);
            view.setScaleY(scale);
            view.setZ(scale * 10);
        } else {      // 后一页
            scale = 1 - 0.2f * position;
            alpha = 1 - 0.2f * position;
            if (position < 0.5) {
                view.setAlpha(alpha);
            } else {
                view.setAlpha(1);
            }
            if (Float.isNaN(scale)) {
                scale = 0;
            }
            view.setScaleX(scale);
            view.setScaleY(scale);
            view.setZ(scale * 10);
        }
        int bannerWidth = view.getWidth();
        float offset = (BaseUtil.getScreenWidth(view.getContext()) - BaseUtil.dp2px(view.getContext(), 16) * 2.0f - bannerWidth) / 2.0f;
        int translationX = (int) (0.9 * bannerWidth - offset);
        view.setTranslationX(-(translationX * position));
        if (position == 0) {
            view.setClickable(true);
        } else {
            view.setClickable(false);
        }
    }
}
