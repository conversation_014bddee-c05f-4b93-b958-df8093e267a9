package com.ximalaya.ting.android.host.view.ad;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.manager.ad.AdPositionIdManager;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.view.BannerView;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

/**
 * Created by le.xin on 2019-12-31.
 *
 * 广告来源view
 * <AUTHOR>
 * @email <EMAIL>
 */
public class AdSourceFromView extends FrameLayout {

    public AdSourceFromView(@NonNull Context context) {
        super(context);

    }

    public AdSourceFromView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public AdSourceFromView(@NonNull Context context,
                            @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    private TextView mTextView;
    private ImageView mImageView;


    public boolean setAdvertis(Advertis advertis) {
        return setAdvertis(advertis, advertis != null ? AdPositionIdManager.getPositionNameByPositionId(advertis.getPositionId() + "") : "");
    }


    public boolean setAdvertis(Advertis advertis ,String positionName) {
        if(advertis == null) {
            setVisibility(View.GONE);
            return false;
        }

        return setAdvertis(advertis.getMaterialProvideSource(), advertis.getInScreenSource(), positionName);
    }

    public boolean setAdvertis(String materialProvideSource, int inScreenSource,String positionName) {
        // tag 是否是合并的
        boolean isTagMerge = BannerView.BANNER_VIEW_POSITION.equals(positionName)
                || AppConstants.AD_POSITION_NAME_PLAY_COMMENT_TOP.equals(positionName);
        if(TextUtils.isEmpty(materialProvideSource)) {
            setVisibility(View.GONE);
            return false;
        } else if(inScreenSource == 1) {  // 展示文字
            if(mImageView != null) {
                mImageView.setVisibility(View.GONE);
            }

            createTextView(positionName);

            mTextView.setText(materialProvideSource);

            mTextView.setVisibility(View.VISIBLE);

            setVisibility(View.VISIBLE);
        } else if(inScreenSource == 2) { // 展示图片(这个在开屏和焦点图上是合并的)
            if(isTagMerge) {
                ImageManager.from(getContext()).downloadBitmap(materialProvideSource, new ImageManager.DisplayCallback() {
                    @Override
                    public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                        createTextView(positionName);

                        BitmapDrawable left = new BitmapDrawable(getResources(), bitmap);
                        // 图片高度设置为8
                        int leftNum = BaseUtil.dp2px(getContext(), 1);
                        left.setBounds(leftNum,
                                0,
                                (int) (left.getMinimumWidth() * 1.0f / left.getMinimumHeight() * BaseUtil.dp2px(getContext() ,8) + leftNum),
                                BaseUtil.dp2px(getContext() ,8));

                        mTextView.setCompoundDrawablePadding(leftNum * 2);
                        mTextView.setCompoundDrawables(left,null ,null ,null);
                        mTextView.setText("广告");
                        mTextView.setVisibility(View.VISIBLE);
                    }
                } ,false);
            } else {
                if(mTextView != null) {
                    mTextView.setVisibility(View.GONE);
                }

                createImageView(positionName);

                ImageManager.from(getContext()).displayImage(getContext() ,mImageView,
                        materialProvideSource, -1, -1 ,0 ,BaseUtil.dp2px(getContext() ,8) ,
                        new ImageManager.DisplayCallback() {
                            @Override
                            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                                if(bitmap == null) {
                                    setVisibility(View.GONE);
                                } else {
                                    int i = BaseUtil.dp2px(getContext(), 8);

                                    int height = bitmap.getHeight();
                                    if(height != 0) {
                                        int width = (int) (bitmap.getWidth() * 1.0f / bitmap.getHeight() * i);
                                        ViewGroup.LayoutParams layoutParams =
                                                mImageView.getLayoutParams();
                                        if(layoutParams != null) {
                                            layoutParams.width = width;
                                            mImageView.setLayoutParams(layoutParams);
                                        }
                                    }
                                }
                            }
                        },null ,false ,true);
            }

            setVisibility(View.VISIBLE);
        }
        if(isTagMerge) {
            return true;
        } else {
            return false;
        }
    }

    private void createImageView(String positionName) {
        if(mImageView == null) {
            mImageView = new ImageView(getContext());
            if (AppConstants.AD_POSITION_NAME_SOUND_PATCH_PLAY_BANNER.equals(positionName)
                    || AppConstants.AD_POSITION_NAME_ALBUM_NOTICE.equals(positionName)) {
                // 缩小来源
                mImageView.setLayoutParams(new
                        FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT ,
                        BaseUtil.dp2px(getContext() , 6)));
                setPadding(BaseUtil.dp2px(getContext(), 2), BaseUtil.dp2px(getContext(), 1), BaseUtil.dp2px(getContext(), 2), BaseUtil.dp2px(getContext(), 1));
            } else {
                mImageView.setLayoutParams(new
                        FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT ,
                        BaseUtil.dp2px(getContext() , 8)));
                setPadding(BaseUtil.dp2px(getContext(), 3), BaseUtil.dp2px(getContext(), 2), BaseUtil.dp2px(getContext(), 3), BaseUtil.dp2px(getContext(), 2));
            }
            addView(mImageView);
        }
        setBackgroundResource(R.drawable.host_bg_ad_source_from_top_right_corner);
    }

    private void createTextView(String positionName) {
        if(mTextView == null) {
            mTextView = new TextView(getContext());
            mTextView.setLayoutParams(new
                    FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT ,ViewGroup.LayoutParams.WRAP_CONTENT));
            mTextView.setTextColor(ContextCompat.getColor(getContext() , R.color.host_white_forty));
            mTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 8);
            setPadding(0 , 0, 0,0);

            if(AppConstants.AD_POSITION_NAME_LOADING.equals(positionName)
                    || BannerView.BANNER_VIEW_POSITION.equals(positionName)
                    || AppConstants.AD_POSITION_NAME_FIND_NATIVE.equals(positionName)) {
                int padding = BaseUtil.dp2px(getContext(), 2);
                mTextView.setPadding(padding, padding, padding, padding);
                if(BannerView.BANNER_VIEW_POSITION.equals(positionName)) {
                    setBackgroundResource(R.drawable.host_bg_ad_source_from_bottom_left_corner);
                } else if(AppConstants.AD_POSITION_NAME_FIND_NATIVE.equals(positionName)) {
                    setBackgroundResource(R.drawable.host_bg_ad_source_from_top_left_corner);
                } else {
                    setBackgroundColor(ContextCompat.getColor(getContext() ,
                            R.color.host_color_0d000000));
                }
            } else if (AppConstants.AD_POSITION_NAME_SOUND_PATCH_PLAY_BANNER.equals(positionName)
                    || AppConstants.AD_POSITION_NAME_ALBUM_NOTICE.equals(positionName)) {
                // 缩小来源
                mTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 6);
                setPadding(BaseUtil.dp2px(getContext(), 2), BaseUtil.dp2px(getContext(), 1), BaseUtil.dp2px(getContext(), 2), BaseUtil.dp2px(getContext(), 1));
                setBackgroundResource(R.drawable.host_bg_ad_source_from_top_right_corner);
            } else {
                setPadding(BaseUtil.dp2px(getContext(), 3), BaseUtil.dp2px(getContext(), 2), BaseUtil.dp2px(getContext(), 3), BaseUtil.dp2px(getContext(), 2));
                setBackgroundResource(R.drawable.host_bg_ad_source_from_top_right_corner);
            }

            mTextView.setCompoundDrawables(null ,null ,null ,null);
            mTextView.setCompoundDrawablePadding(0);
            addView(mTextView);
        }
    }


}
