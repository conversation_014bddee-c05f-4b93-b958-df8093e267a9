package com.ximalaya.ting.android.host.service.xmcontrolapi;

import android.os.Parcel;
import android.os.Parcelable;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.service.XiaoaiControllService;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

public class Song implements Parcelable {
    private String id;
    private boolean isLiked;
    private Album album;
    private Singer singer;
    private String title;
    private String largeCover;
    private boolean isCollected;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Album getAlbum() {
        return this.album;
    }

    public void setAlbum(Album album) {
        this.album = album;
    }

    public Singer getSinger() {
        return this.singer;
    }

    public void setSinger(Singer singer) {
        this.singer = singer;
    }

    public String getLargeCover() {
        return largeCover;
    }

    public void setLargeCover(String largeCover) {
        this.largeCover = largeCover;
    }

    public static Song fromTrack(Track track) {
        if(track == null) {
            return null;
        }

        Song song = new Song();
        song.id = track.getDataId() + "";
        song.title = track.getTrackTitle();
        song.isLiked = track.isLike();
        song.isCollected = track.isCollect();

        if(track.getAlbum() != null) {
            Album album = new Album();
            album.setId(track.getAlbum().getAlbumId());
            album.setCoverUri(track.getAlbum().getValidCover());
            if (TextUtils.isEmpty(album.getCoverUri())) {
                album.setCoverUri(track.getValidCoverSmallFirst());
            }
            song.setLargeCover(track.getCoverUrlLarge());
            album.setTitle(track.getAlbum().getAlbumTitle());
            song.album = album;

            Logger.log("Song : getCoverUrl " + album.getCoverUri());
        }

        if(track.getAnnouncer() != null) {
            Singer singer = new Singer();
            singer.setId(track.getAnnouncer().getAnnouncerId());
            singer.setTitle(track.getAnnouncer().getNickname());
            song.setSinger(singer);
        }

        return song;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.id);
        dest.writeParcelable(this.album, flags);
        dest.writeParcelable(this.singer, flags);
        dest.writeString(this.title);
        dest.writeInt(this.isLiked ? 1 : 0);

        if (XiaoaiControllService.supportExtParam) {
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("largeCover", largeCover);
                jsonObject.put("isCollected", isCollected);

            } catch (Exception e) {
                e.printStackTrace();
            }
            dest.writeString(jsonObject.toString());
        }
    }

    public void readFromParcel(Parcel in) {
        this.id = in.readString();
        this.album = in.readParcelable(Album.class.getClassLoader());
        this.singer = in.readParcelable(Singer.class.getClassLoader());
        this.title = in.readString();
        this.isLiked = in.readInt() == 1;
        if (XiaoaiControllService.supportExtParam) {
            String s = in.readString();
            try {
                JSONObject jsonObject = new JSONObject(s);
                this.largeCover = jsonObject.optString("largeCover");
                this.isCollected = jsonObject.optBoolean("isCollected");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public Song() {
    }

    public static final Creator<Song> CREATOR = new Creator<Song>() {
        @Override
        public Song createFromParcel(Parcel source) {
            Song song = new Song();
            song.readFromParcel(source);
            return song;
        }

        @Override
        public Song[] newArray(int size) {
            return new Song[size];
        }
    };


    @Override
    public String toString() {
        return "Song{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                '}';
    }

    public boolean isLiked() {
        return isLiked;
    }

    public void setLiked(boolean liked) {
        isLiked = liked;
    }

    public boolean isCollected(){
        return isCollected;
    }

    public void setCollected(boolean collected){
        isCollected = collected;
    }
}
  