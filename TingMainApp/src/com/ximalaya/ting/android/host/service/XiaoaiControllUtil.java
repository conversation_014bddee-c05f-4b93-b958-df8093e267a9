package com.ximalaya.ting.android.host.service;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import com.ximalaya.ting.android.framework.earn.statistics.PushArrivedTraceManager;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.manager.PlayCompleteRecommendManager;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.live.schedule.Schedule;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.service.IServiceLifeCallBack;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import androidx.annotation.Nullable;

class XiaoaiControllUtil {

    public static final String ACTION = "com.ximalaya.ting.android.xiaoai.controll.action";

    // 所有的指令通过这个字段传递
    public static final String CONTROL_TYPE = "controlType";

    // 进入专辑
    public static final String CONTROL_TYPE_START_ALBUM = "startAlbum";
    // 传入的albumId 类型为long
    public static final String DATA_ALBUM_ID = "albumId";

    // 打开播放页
    public static final String CONTROL_TYPE_OPEN_PLAY = "openPlay";

    // 播放某条声音
    public static final String CONTROL_TYPE_PLAY_TRACK = "playTrack";
    // 传入的trackId 类型为long
    public static final String DATA_TRACK_ID = "trackId";

    // 暂停
    public static final String CONTROL_TYPE_PAUSE = "pause";

    // 播放
    public static final String CONTROL_TYPE_PLAY = "play";

    // 下一曲
    public static final String CONTROL_TYPE_NEXT = "next";

    // 上一曲
    public static final String CONTROL_TYPE_PRE = "pre";

    // 停止
    public static final String CONTROL_TYPE_STOP = "stop";

    // 设置数据流量
    public static final String CONTROL_TYPE_SET_MOBILE_PLAY_ENABLE = "mobilePlayEnable";

    // 获取是否可以使用流量播放
    // 0 不允许
    // 1 允许本次
    // 2 一直允许(杀掉app同样生效)
    public static final String DATA_SET_MOBILE_PLAY_ABLE = "setMobilePlay";

    // 没有下一曲
    public static final int ERROR_CODE_NO_NEXT = -4;
    // 没有上一曲
    public static final int ERROR_CODE_NO_PRE = -5;
    // 数据流量不可播放
    public static final int ERROR_CODE_MOBILE_NET_CANNOT_PLAY = -1;
    // 没有vip权限
    public static final int ERROR_CODE_NO_VIP = -3;
    // 没有此音频的资源
    public static final int ERROR_CODE_NO_SOUND_SOURCE = -6;

    private boolean hasXiaoaiApp = false;

    private Map<String ,Long> pageInfoAndVersions = new HashMap<String ,Long>() {
        {
            put("com.miui.voiceassist" ,304002000L);
            put("com.xiaomi.xiaoailite" ,101006000L);
        }
    };

    class CurPageInfo {
        String pageName;
        Long pageVersion;
    }

    private CurPageInfo mCurPageInfo;

    public void init(Context context) {
        Set<Map.Entry<String, Long>> entries = pageInfoAndVersions.entrySet();
        for (Map.Entry<String ,Long> entry : entries) {
            try {
                PackageInfo info = context.getPackageManager()
                        .getPackageInfo(entry.getKey(), 0);
                if(info == null) {
                    hasXiaoaiApp = false;
                } else {
                    if(info.versionCode >= entry.getValue()) {
                        mCurPageInfo = new CurPageInfo();
                        mCurPageInfo.pageName = entry.getKey();
                        mCurPageInfo.pageVersion = entry.getValue();

                        hasXiaoaiApp = true;

                        return;
                    } else {
                        hasXiaoaiApp = false;
                    }
                }
            } catch (PackageManager.NameNotFoundException e) {
                hasXiaoaiApp = false;
            } catch (Exception e) {
                hasXiaoaiApp = false;
            }
        }

        Logger.logToFile("XiaoaiControl == init = " + hasXiaoaiApp);
    }

    private volatile static XiaoaiControllUtil sControllUtil;

    private XiaoaiControllUtil() {
        init(MainApplication.getMyApplicationContext());
    }

    public static XiaoaiControllUtil getInstance() {
        if (sControllUtil == null) {
            synchronized (XiaoaiControllUtil.class) {
                if (sControllUtil == null) {
                    sControllUtil = new XiaoaiControllUtil();
                }
            }
        }
        return sControllUtil;
    }

    public boolean isHasXiaoaiApp() {
        return hasXiaoaiApp;
    }

    public void setHasXiaoaiApp(boolean hasXiaoaiApp) {
        this.hasXiaoaiApp = hasXiaoaiApp;
        MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext()).saveBoolean(PreferenceConstantsInHost.KEY_HAS_RECEIVER_XIAOAI_CONTROL ,hasXiaoaiApp);
    }

    // 发送播放状态
    public void sendPlayState(boolean isPlaying, long id, String title) {
        if(canSendMessage()) {
            Intent intent = new Intent();
            intent.setAction("com.ximalaya.ting.android.xiaoai.PLAYER_STATUS");
            if(mCurPageInfo != null) {
                intent.setPackage(mCurPageInfo.pageName);
            }
            intent.putExtra("status", isPlaying ? 2 : 3);
            intent.putExtra("id", id);
            intent.putExtra("title", title);
            MainApplication.getMyApplicationContext().sendBroadcast(intent);
        }
    }

    // 发送播放错误的信息
    public void sendPlayError(int code, String message) {
        if(canSendMessage()) {
            Intent intent = new Intent();
            intent.setAction("com.ximalaya.ting.android.xiaoai.PLAYER_ERROR");
            if(mCurPageInfo != null) {
                intent.setPackage(mCurPageInfo.pageName);
            }
            intent.putExtra("code", code);
            intent.putExtra("message", message);
            MainApplication.getMyApplicationContext().sendBroadcast(intent);
        }
    }

    private boolean canSendMessage() {
        return hasXiaoaiApp && MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext())
                .getBoolean(PreferenceConstantsInHost.KEY_HAS_RECEIVER_XIAOAI_CONTROL);
    }

    public void onPlayStart() {
        if(canSendMessage()) {
            PlayableModel playableModel = null;
            if (XmPlayerService.getPlayerSrvice() != null) {
                playableModel = XmPlayerService.getPlayerSrvice().getCurrPlayModel();
            }
            if(playableModel != null) {
                if(playableModel instanceof Track) {
                    XiaoaiControllUtil.getInstance().sendPlayState(true ,playableModel.getDataId() ,
                            ((Track) playableModel).getTrackTitle());
                } else if(playableModel instanceof Schedule) {
                    XiaoaiControllUtil.getInstance().sendPlayState(true ,playableModel.getDataId() ,
                            ((Schedule) playableModel).getRadioName());
                }
            }
        }
    }

    public void onPlayPause() {
        if(canSendMessage()) {
            PlayableModel playableModel = null;
            if (XmPlayerService.getPlayerSrvice() != null) {
                playableModel = XmPlayerService.getPlayerSrvice().getCurrPlayModel();
            }
            if(playableModel != null) {
                if(playableModel instanceof Track) {
                    XiaoaiControllUtil.getInstance().sendPlayState(false ,playableModel.getDataId() ,
                            ((Track) playableModel).getTrackTitle());
                } else if(playableModel instanceof Schedule) {
                    XiaoaiControllUtil.getInstance().sendPlayState(false ,playableModel.getDataId() ,
                            ((Schedule) playableModel).getRadioName());
                }
            }
        }
    }

    public void onStartCommand(Intent intent , Context context) {
        Logger.logToFile("XiaoaiControl == onStartCommand = " + intent);
        if(intent != null && ACTION.equals(intent.getAction())) {
            String action = intent.getStringExtra(CONTROL_TYPE);
            Logger.logToFile("XiaoaiControl == onStartCommand = action=" + action);
            if(!TextUtils.isEmpty(action)) {
                XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
                if (mCurPageInfo != null) {
                    XiaoaiControllService.trace(mCurPageInfo.pageName, action, null);
                }
                XiaoaiControllUtil.getInstance().setHasXiaoaiApp(true);
                switch (action) {
                    case CONTROL_TYPE_START_ALBUM:
                        try {
                            String albumId = intent.getStringExtra(DATA_ALBUM_ID);
                            if(!TextUtils.isEmpty(albumId)) {
                                try {
                                    long albumIdLong = Long.parseLong(albumId);
                                    if(albumIdLong > 0) {
                                        Intent intent1 = new Intent(Intent.ACTION_VIEW);
                                        intent1.setData(Uri.parse("iting://open?msg_type=13&album_id=" + albumId));
                                        intent1.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                        context.startActivity(intent1);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        break;
                    case CONTROL_TYPE_OPEN_PLAY:
                        try {
                            Intent intent1 = new Intent(Intent.ACTION_VIEW);
                            intent1.setData(Uri.parse("iting://open?msg_type=27"));
                            intent1.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            context.startActivity(intent1);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        break;
                    case CONTROL_TYPE_PLAY_TRACK:
                        String trackIdStr = intent.getStringExtra(DATA_TRACK_ID);
                        Logger.logToFile("XiaoaiControl == onStartCommand = trackIdStr=" + trackIdStr);
                        if(trackIdStr != null) {
                            long trackId = 0;
                            try {
                                trackId = Long.parseLong(trackIdStr);
                            } catch (NumberFormatException e) {
                                e.printStackTrace();
                            }
                            if(trackId > 0) {
                                long finalTrackId1 = trackId;
                                checkConnectState(context, new XiaoaiControllService.IServiceState() {
                                    @Override
                                    public void onServiceConnect() {
                                        playTrackById(finalTrackId1, new IDataCallBack<TrackM>() {
                                            @Override
                                            public void onSuccess(@Nullable TrackM data) {

                                            }

                                            @Override
                                            public void onError(int code, String message) {
                                                sendNoSoundSourceError();
                                            }
                                        });
                                    }

                                    @Override
                                    public void onServiceTimeout() {

                                    }
                                });
                            }
                        }
                        break;
                    case CONTROL_TYPE_PAUSE:
                        if (playerService != null) {
                            playerService.pausePlay(false, PauseReason.Common.XIAOAI_ACTION_PAUSE);
                        }
                        break;
                    case CONTROL_TYPE_PLAY:
                        if (playerService != null) {
                            playerService.startPlay();
                        }
                        break;
                    case CONTROL_TYPE_NEXT:
                        if (playerService != null ) {
                            if (playerService.hasNextSound()) {
                                playerService.playNext();
                            } else {
                                XiaoaiControllUtil.getInstance().sendPlayError(ERROR_CODE_NO_NEXT ,"没有下一曲");
                            }
                        }
                        break;
                    case CONTROL_TYPE_PRE:
                        if (playerService != null ) {
                            if (playerService.hasPreSound()) {
                                playerService.playPre();
                            } else {
                                XiaoaiControllUtil.getInstance().sendPlayError(ERROR_CODE_NO_PRE ,"没有上一曲");
                            }
                        }
                        break;
                    case CONTROL_TYPE_STOP:
                        if (playerService != null) {
                            playerService.stopPlay(PauseReason.StopBusiness.XiaoAiControlUtil);
                        }
                        break;
                    case CONTROL_TYPE_SET_MOBILE_PLAY_ENABLE:
//                        String eable = intent.getStringExtra(DATA_SET_MOBILE_PLAY_ABLE);
//                        if("0".equals(eable)) {
//                            NetworkUtils.isAllowUse3G = false;
//                        } else if("1".equals(eable)) {
//                            NetworkUtils.isAllowUse3G = true;
//                        } else if("2".equals(eable)) {
//                            SharedPreferencesUtil
//                                    .getInstance(
//                                            MainApplication.getMyApplicationContext())
//                                    .saveBoolean(PreferenceConstantsInHost.TINGMAIN_KEY_WITHOUT_WIFI, true);
//                        }
                        break;
                }
            }
        }
    }

    public static void playTrackById(long trackId, IDataCallBack<TrackM> callBack) {
        Map<String, String> specificParams = new HashMap<>();
        specificParams.put("device", "android");
        specificParams.put("trackId", trackId + "");
        specificParams.put("extNeedType", "1");
        CommonRequestM.getTrackInfoDetail(specificParams, new IDataCallBack<TrackM>() {
            @Override
            public void onSuccess(TrackM t) {
                Logger.logToFile("XiaoaiControl == playTrackByCommonList 1");

                if (XiaoaiControllService.getXiaoaiService() != null) {
                    XiaoaiControllService.getXiaoaiService().playTrackByHistory(t, true,  new XiaoaiControllService.IPlayHistoryCallBack() {
                        @Override
                        public void onError() {
                            if (callBack != null) {
                                callBack.onError(604, "播放历史错误");
                            }
                        }

                        @Override
                        public void onSuccess() {
                            if (callBack != null) {
                                callBack.onSuccess(t);
                            }
                        }
                    });
                }
            }

            @Override
            public void onError(int code, String message) {
                if (callBack != null) {
                    callBack.onError(code, message);
                }
            }
        });
    }

    private void checkConnectState(Context context, XiaoaiControllService.IServiceState callback) {
        XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
        if (playerSrvice != null) {
            if (callback != null) {
                callback.onServiceConnect();
            }
        } else {
            long mLastHandleTrackTime = System.currentTimeMillis();

            XmPlayerService.addServiceLife(new IServiceLifeCallBack() {
                @Override
                public void onServiceCreate() {
                    XmPlayerService.removeServiceLife(this);

                    //超过10s放弃以前的操作
                    if (System.currentTimeMillis() - mLastHandleTrackTime > 10_000) {
                        callback.onServiceTimeout();
                        return;
                    }

                    if (callback != null) {
                        callback.onServiceConnect();
                    }
                }

                @Override
                public void onServiceDestory() {
                    XmPlayerService.removeServiceLife(this);
                }
            });

            initPlayerService(context);
        }
    }

    private void initPlayerService(Context context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(XmPlayerService.getIntent(context, true));
            } else {
                context.startService(XmPlayerService.getIntent(context, false));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void sendNoWifiError() {
        XiaoaiControllUtil.getInstance().sendPlayError(ERROR_CODE_MOBILE_NET_CANNOT_PLAY ,"移动网络不能播放");
    }

    private void sendNoSoundSourceError() {
        XiaoaiControllUtil.getInstance().sendPlayError(ERROR_CODE_NO_SOUND_SOURCE ,"此声音不存在");
    }

}