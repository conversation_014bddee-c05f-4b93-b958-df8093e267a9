<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/host_bg_list_selector"
    android:descendantFocusability="blocksDescendants"
    android:paddingTop="28dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/main_cover_group"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginLeft="16dp"
        app:cardBackgroundColor="@color/host_transparent"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp">
        <!--专辑图-->
        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_iv_album_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@null"
            android:scaleType="centerCrop"
            android:src="@drawable/host_default_album"
            app:border_color="@color/host_color_e8e8e8_2a2a2a"
            app:border_width="1px"
            app:corner_radius="4dp"
            app:pressdown_shade="false"
            tools:background="@drawable/host_bg_album_cover" />

        <!--付费角标-->
        <ImageView
            android:id="@+id/main_iv_album_pay_cover_tag"
            android:layout_width="wrap_content"
            android:layout_height="12dp"
            android:scaleType="fitStart"
            android:contentDescription="专辑标签"
            android:layout_alignLeft="@+id/main_iv_album_cover"
            android:layout_alignTop="@+id/main_iv_album_cover"
            android:visibility="invisible"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/main_album_activity_123_2018"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:contentDescription="123活动"
            android:scaleType="fitStart"
            android:visibility="gone"
            tools:visibility="visible" />
    </androidx.cardview.widget.CardView>


    <!--订阅星星-->
    <ImageView
        android:id="@+id/main_subscribe_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/main_cover_group"
        android:layout_alignParentRight="true"
        android:layout_marginRight="15dp"
        android:contentDescription="订阅"
        android:padding="2dp"
        android:scaleType="centerInside"
        android:src="@drawable/host_subscribe_start_selector"
        android:visibility="visible" />

    <LinearLayout
        android:id="@+id/main_item_album_recomend"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_alignTop="@id/main_cover_group"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="16dp"
        android:layout_toRightOf="@id/main_cover_group"
        android:gravity="center_vertical"
        android:orientation="vertical">
        <!--专辑标题-->
        <TextView
            android:id="@+id/main_tv_album_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="专辑标题"
            android:ellipsize="end"
            android:fontFamily="sans-serif-light"
            android:maxLines="1"
            android:textColor="@color/host_color_333333_dcdcdc"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="专辑标题专辑标题专辑标题专辑标题专辑标题专辑标题" />


        <!--副标题-->
        <TextView
            android:id="@+id/main_tv_album_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/main_tv_album_title"
            android:layout_alignStart="@+id/main_tv_album_title"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="16dp"
            android:layout_toLeftOf="@+id/main_subscribe_start"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/host_color_999999_8d8d91"
            android:textSize="11sp"
            android:visibility="gone" />

        <FrameLayout
            android:id="@+id/main_thirdLine"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/main_tv_album_subtitle"
            android:layout_alignLeft="@+id/main_tv_album_title"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="16dp"
            android:layout_toStartOf="@+id/main_subscribe_start">

            <!--专辑信息容器-->
            <LinearLayout
                android:id="@+id/main_layout_album_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal" />

            <!--专辑价格-->
            <TextView
                android:id="@+id/main_tv_album_display_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="专辑原价"
                android:gravity="center"
                android:text="价格"
                android:textColor="@color/host_color_999999_8d8d91"
                android:textSize="11sp"
                android:visibility="invisible"
                tools:visibility="visible" />
        </FrameLayout>

    </LinearLayout>


    <!--专辑条分割线-->
<!--    <View-->
<!--        android:id="@+id/main_album_border"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="0.5dp"-->
<!--        android:background="@color/host_color_eeeeee_353535"-->
<!--        android:layout_alignStart="@+id/main_tv_album_title"-->
<!--        android:layout_below="@+id/main_thirdLine"-->
<!--        android:layout_marginTop="12dp"/>-->

</RelativeLayout>