<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/host_banner_v_imge_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:corner="all"
    app:corner_radius="8dp"
    tools:layout_height="137dp">

    <ImageView
        android:id="@+id/banner_image_ad_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        tools:background="@drawable/host_banner_defual_bg" />

    <View
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:background="@drawable/host_ad_banner_cover_bg_top_mc" />

    <View
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/host_ad_banner_cover_bg_bottom" />

    <com.ximalaya.ting.android.host.view.MyClubBannerDanMuView
        android:id="@+id/banner_danmu_view"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="8dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:layout_height="60dp"
        android:layout_alignParentBottom="true"
        android:layout_toLeftOf="@id/banner_mc_button_lay"
        android:layout_marginBottom="12dp" />

    <TextView
        android:id="@+id/banner_image_ad_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:ellipsize="end"
        android:lineSpacingExtra="1dp"
        android:maxLines="2"
        android:shadowColor="#0a000000"
        android:shadowDx="3"
        android:shadowDy="3"
        android:shadowRadius="4"
        android:textColor="@color/host_color_ffffff"
        android:textSize="16dp"
        android:textStyle="bold"
        tools:text="                     29年装修设计施工经验，十九年行业居首，为您省心省力！" />

    <TextView
        android:id="@+id/banner_image_hot_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="12dp"
        android:background="@drawable/host_mc_hot_item_bg_d6ff4646"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingLeft="4dp"
        android:paddingTop="2dp"
        android:paddingRight="4dp"
        android:paddingBottom="2dp"
        android:textColor="@color/host_white"
        android:textSize="10dp"
        android:textStyle="bold"
        tools:text="1268人热聊中" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:id="@+id/banner_mc_button_lay"
        android:layout_marginRight="12dp"
        android:layout_marginBottom="12dp"
        android:gravity="center_vertical"
        android:background="@drawable/host_ad_banner_mc_button_bg"
        android:orientation="horizontal"
        android:paddingLeft="12dp"
        android:paddingTop="4dp"
        android:paddingRight="12dp"
        android:paddingBottom="4dp">

        <com.ximalaya.ting.android.framework.view.LottieAnimationViewCompat
            android:id="@+id/banner_mc_button_animation"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginRight="2dp"
            android:scaleType="centerCrop"
            app:lottie_autoPlay="false"
            app:lottie_loop="true"
            app:lottie_fileName="lottie/home_banner_mc_tip/data.json" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:shadowColor="#38000000"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="4.0"
            android:text="立即参与"
            android:textColor="#ffffffff"
            android:textSize="12sp" />
    </LinearLayout>

</com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout>
