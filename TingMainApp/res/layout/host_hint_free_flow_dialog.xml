<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="20dp"
    android:layout_marginRight="20dp"
    android:background="@drawable/host_free_flow_bg"
    android:gravity="center">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/host_hint_free_flow_dialog_bg" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/msg_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:clickable="false"
            android:gravity="left|top"
            android:lineSpacingMultiplier="1.2"
            android:longClickable="false"
            android:maxLines="12"
            android:minHeight="70dp"
            android:paddingTop="60dp"
            android:text="@string/host_sure_to_open"
            android:textColor="@color/host_color_575757_cfcfcf"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/main_freeflow_goto"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_marginLeft="80dp"
            android:layout_marginRight="80dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/host_semicircle_rectangle_orange"
            android:gravity="center"
            android:text="@string/host_open_free_flow_service"
            android:textColor="@color/host_white"
            android:textSize="16sp"
            android:visibility="visible" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginTop="45dp"
            android:background="@color/host_color_e8e8e8_2a2a2a" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/neutral_btn"
                style="@style/round_bottom_btn_left_bg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:maxLines="1"
                android:paddingBottom="15dp"
                android:paddingTop="15dp"
                android:text="否"
                android:textColor="@color/host_color_575757_cfcfcf"
                android:textSize="16sp" />

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/host_color_e8e8e8_2a2a2a" />

            <Button
                android:id="@+id/ok_btn"
                style="@style/round_bottom_btn_right_bg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:maxLines="1"
                android:paddingBottom="15dp"
                android:paddingTop="15dp"
                android:text="是"
                android:textColor="@color/host_white"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/main_freeflow_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true"
        android:paddingBottom="16dp"
        android:paddingLeft="16dp"
        android:paddingRight="8dp"
        android:paddingTop="8dp"
        android:src="@drawable/host_free_flow_dialog_close"
        android:contentDescription="@string/host_close"/>

</RelativeLayout>