<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true">

    <View
        android:id="@+id/host_mask_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#b3000000" />

    <ImageView
        android:id="@+id/host_iv_arrow_top"
        android:layout_width="30dp"
        android:layout_height="10dp"
        android:layout_marginBottom="11dp"
        android:src="@drawable/host_ic_arrow_bottom"
        android:translationY="-1dp"
        app:layout_constraintBottom_toTopOf="@+id/host_iv_bottom_tab"
        app:layout_constraintEnd_toEndOf="@+id/host_iv_bottom_tab"
        app:layout_constraintStart_toStartOf="@+id/host_iv_bottom_tab" />

    <View
        android:id="@+id/host_view_bottom_left_space"
        android:layout_width="70dp"
        android:layout_height="1dp"
        app:layout_constraintEnd_toStartOf="@+id/host_iv_bottom_tab"
        app:layout_constraintTop_toTopOf="@+id/host_iv_bottom_tab" />

    <androidx.cardview.widget.CardView
        android:id="@+id/host_card_lottie"
        android:layout_width="360dp"
        android:layout_height="0dp"
        app:cardBackgroundColor="@color/host_white"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toTopOf="@+id/host_iv_arrow_top"
        app:layout_constraintDimensionRatio="1029:1086"
        app:layout_constraintStart_toStartOf="@+id/host_view_bottom_left_space">

        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/host_iv_lottie"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible"
            app:lottie_repeatCount="0" />
    </androidx.cardview.widget.CardView>

    <View
        android:id="@+id/host_iv_close"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintEnd_toEndOf="@+id/host_card_lottie"
        app:layout_constraintTop_toTopOf="@+id/host_card_lottie" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/host_iv_bottom_tab"
        android:layout_width="63dp"
        android:layout_height="63dp"
        android:layout_marginLeft="86dp"
        android:src="@drawable/host_ic_quick_listen_guide_tab"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>