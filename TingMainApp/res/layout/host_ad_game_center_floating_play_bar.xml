<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:makeramen="http://schemas.android.com/apk/res-auto"
    xmlns:skin="http://schemas.android.com/android/skin"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/host_cm_game_play_bar_bg"
    android:orientation="horizontal"
    tools:background="#80000000">

    <!--总宽度48，在view中要手动设置-->
    <FrameLayout
        android:id="@+id/host_cmgame_fragment_playbar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        skin:enable="true">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/host_cmgame_sound_cover_img"
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:layout_gravity="center"
            android:background="@drawable/host_default_album"
            android:contentDescription="@string/host_enter_play_page"
            android:scaleType="centerCrop"
            makeramen:corner_radius="50dp"
            makeramen:pressdown_shade="false"
            makeramen:round_background="true" />

        <com.ximalaya.ting.android.host.view.bar.RoundProgressBar
            android:id="@+id/host_cmgame_round_progressbar_play_progress"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center"
            android:visibility="visible"
            makeramen:cap="ROUND"
            makeramen:roundColor="@color/host_color_e8e8e8"
            makeramen:roundProgressColor="#E83F46"
            makeramen:roundWidth="3dp"
            makeramen:style="STROKE"
            makeramen:textIsDisplayable="false" />
    </FrameLayout>

    <!--宽度135，在View中需要手动设置-->
    <LinearLayout
        android:id="@+id/host_cmgame_layout_play_bar_content"
        android:layout_width="135dp"
        android:layout_height="48dp"
        android:layout_centerVertical="true"
        android:layout_toRightOf="@+id/host_cmgame_fragment_playbar"
        android:background="#ffffff"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/host_cmgame_layout_play_pre"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingLeft="12dp"
            android:paddingRight="10dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:scaleType="fitXY"
                android:src="@drawable/host_cggame_play_bar_pre" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/host_cmgame_layout_play_pause"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/host_cmgame_iv_play_or_pause"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:scaleType="fitXY"
                android:src="@drawable/host_cmgame_play_bar_play" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/host_cmgame_layout_play_next"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:scaleType="fitXY"
                android:src="@drawable/host_cmgame_play_bar_next" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>