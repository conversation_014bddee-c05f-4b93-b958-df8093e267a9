<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.host.view.CornerRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:id="@+id/host_video_lay"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center_horizontal"
    android:layout_marginTop="5dp"
    android:visibility="visible">

    <FrameLayout
        android:id="@+id/host_video_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/host_sb_video_progress"
        />

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/host_video_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:layout_above="@+id/host_sb_video_progress"
        android:scaleType="centerCrop" />

    <FrameLayout
        android:id="@+id/host_video_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/host_sb_video_progress"
        android:background="#7f000000" />

    <ProgressBar
        android:id="@+id/host_ic_video_loading"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_centerInParent="true"
        android:indeterminate="true"
        android:indeterminateDrawable="@drawable/host_video_progress_drawable"
        android:visibility="gone" />
    <SeekBar
        android:id="@+id/host_sb_video_progress"
        android:layout_width="match_parent"
        android:background="@color/host_gray"
        android:thumb="@null"
        android:progress="0"
        android:layout_alignParentRight="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentBottom="true"
        android:layout_height="2dp"
        android:progressTint="@color/host_color_99ffffff"
        android:visibility="gone"
        tool:visibility="visible"/>
    <ImageView
        android:id="@+id/host_ic_video_mute"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="6dp"
        android:background="@drawable/host_bg_rect_4d000000_radius_16"
        android:scaleType="centerInside"
        android:src="@drawable/host_icon_video_mute"
        android:visibility="gone"
        tool:visibility="visible"/>
    <LinearLayout
        android:id="@+id/host_vg_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="10dp"
        android:layout_centerInParent="true"
        android:gravity="center">

        <ImageView
            android:id="@+id/host_ic_video_play_pause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/host_btn_video_play" />

        <TextView
            android:id="@+id/host_tv_play_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="2dp"
            android:textColor="@color/host_white"
            android:textSize="14sp" />

    </LinearLayout>

</com.ximalaya.ting.android.host.view.CornerRelativeLayout>
