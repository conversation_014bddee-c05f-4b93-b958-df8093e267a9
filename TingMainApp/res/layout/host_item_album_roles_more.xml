<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/host_album_role_item_root_ll"
    android:layout_marginStart="10dp"
    android:orientation="vertical"
    android:gravity="center_vertical"
    android:background="@drawable/host_bg_rect_0fffffff_left_radius_4"
    tools:background="@color/host_gray"
    android:layout_width="84dp"
    android:layout_height="54dp">

    <TextView
        android:id="@+id/host_item_role_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:ellipsize="end"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:textColor="@color/host_color_ffffff"
        android:textSize="13sp"
        android:text="更多角色" />

    <TextView
        android:id="@+id/host_item_role_count_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:layout_marginTop="6dp"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:textColor="@color/host_color_80ffffff"
        android:textSize="11sp"
        tools:text="3个" />

</LinearLayout>