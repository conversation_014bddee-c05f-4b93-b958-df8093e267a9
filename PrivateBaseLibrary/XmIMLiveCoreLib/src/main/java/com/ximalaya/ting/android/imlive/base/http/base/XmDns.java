package com.ximalaya.ting.android.im.base.http.base;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;

import okhttp3.Dns;

/**
 * Created by jack.qin on 2019/2/26.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class XmDns implements Dns {
    private long timeout;

    public XmDns(long timeout) {
        this.timeout = timeout;
    }

    @NonNull
    @Override
    public List<InetAddress> lookup(@NonNull final String hostname) throws UnknownHostException {
        if (TextUtils.isEmpty(hostname)) {
            throw new UnknownHostException("hostname == null");
        } else {
            try {
                FutureTask<List<InetAddress>> task = new FutureTask<>(
                        new Callable<List<InetAddress>>() {
                            @Override
                            public List<InetAddress> call() throws Exception {
                                return Arrays.asList(InetAddress.getAllByName(hostname));
                            }
                        });
                new Thread(task, "dns_lookup_thread").start();
                return task.get(timeout, TimeUnit.MILLISECONDS);
            } catch (Exception var4) {
                UnknownHostException unknownHostException =
                        new UnknownHostException("Broken system behaviour for dns lookup of " + hostname);
                unknownHostException.initCause(var4);
                throw unknownHostException;
            }
        }
    }
}
