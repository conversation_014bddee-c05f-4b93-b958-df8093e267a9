package com.ximalaya.ting.android.im.base;

import android.content.Context;
import android.support.annotation.NonNull;

import com.squareup.wire.Message;
import com.ximalaya.ting.android.im.base.constants.IMConnectionStatus;
import com.ximalaya.ting.android.im.base.constants.ImConnectionState;
import com.ximalaya.ting.android.im.base.constants.errinfo.ImErrorCode;
import com.ximalaya.ting.android.im.base.interf.connect.ISendMessageCallback;
import com.ximalaya.ting.android.im.base.interf.IRequestResultCallBack;
import com.ximalaya.ting.android.im.base.interf.connect.IBuildConnectionResultCallback;
import com.ximalaya.ting.android.im.base.interf.listener.IConnectionListener;
import com.ximalaya.ting.android.im.base.interf.listener.IGetSendRecManagerChangeListener;
import com.ximalaya.ting.android.im.base.interf.listener.IGetSocketManagerChangeListener;
import com.ximalaya.ting.android.im.base.model.ByteDataMessage;
import com.ximalaya.ting.android.im.base.model.ImConnectionInputConfig;
import com.ximalaya.ting.android.im.base.model.SendDataMsgWrapper;
import com.ximalaya.ting.android.im.base.sendrecmanage.IImSendRecManager;
import com.ximalaya.ting.android.im.base.sendrecmanage.ImSendRecManager;
import com.ximalaya.ting.android.im.base.sendrecmanage.bytemsgparse.BaseImMessageAdapter;
import com.ximalaya.ting.android.im.base.sendrecmanage.joinprocess.BaseJoinMsgHandler;
import com.ximalaya.ting.android.im.base.sendrecmanage.tasksetting.BaseMsgToTaskConvertor;
import com.ximalaya.ting.android.im.base.socketmanage.ISocketConnManager;
import com.ximalaya.ting.android.im.base.socketmanage.SocketConnManager;

import java.util.ArrayList;
import java.util.List;


/**
 * IM系统基础连接模块
 * 上层业务以此模块的基础功能搭建
 *
 * <AUTHOR>
 * @since 2019/01/29
 */
public class XmIMConnection implements IXmBaseConnection, IGetSocketManagerChangeListener, IGetSendRecManagerChangeListener {


    private String mConnectionName;

    //底层socket连接的管理单元
    private ISocketConnManager mSocketConnManager;
    //消息收发管理单元
    private IImSendRecManager mImSendRecManager;

    //连接状态和消息接收的监听
    private List<IConnectionListener> mConnectionListeners = new ArrayList<>();


    public XmIMConnection(String name) {
        mConnectionName = name;
    }


    @Override
    public void initConnection(@NonNull Context context,
                               @NonNull BaseImMessageAdapter adapter,
                               @NonNull BaseMsgToTaskConvertor convertor,
                               @NonNull BaseJoinMsgHandler joinMsgHandler) {

        //底层socket连接的管理单元
        mSocketConnManager = SocketConnManager.newInstance(context, joinMsgHandler, mConnectionName);
        //消息收发管理单元
        mImSendRecManager  = ImSendRecManager.newInstance(context, adapter, convertor, mConnectionName);

        //注册子模块的变化监听
        registerSubManagerListeners();

    }

    /**
     * 注册内部子模块的信息变化监听
     */
    private void registerSubManagerListeners() {
        if (mSocketConnManager != null) {
            mSocketConnManager.addManagerChangeListener(this);
        }

        if (mImSendRecManager != null) {
            mImSendRecManager.addManagerChangeListener(this);
        }
    }

    /**
     * 注销内部子模块的信息变化监听
     */
    private void unRegisterSubManagerListeners() {
        if (mSocketConnManager != null) {
            mSocketConnManager.removeManagerChangeListener(this);
        }

        if (mImSendRecManager != null) {
            mImSendRecManager.removeManagerChangeListener(this);
        }
    }


    /**
     * 销毁释放基础模块的连接和资源
     */
    @Override
    public void release() {

        //注销子模块的监听
        unRegisterSubManagerListeners();

        if (mConnectionListeners != null) {
            mConnectionListeners.clear();
        }

        if (mSocketConnManager != null) {
            mSocketConnManager.release();
        }

        if (mImSendRecManager != null) {
            mImSendRecManager.release();
        }

    }

    private volatile boolean isStopping = false;

    @Override
    public void closeConnection() {

        if (isStopping) {
            return;
        }

        isStopping = true;

        if (mSocketConnManager != null) {
            mSocketConnManager.stop();
        }

        if (mImSendRecManager != null) {
            mImSendRecManager.stop();
        }

        isStopping = false;
    }

    /**
     * 创建长连接
     * @param inputConfig 输入参数
     * @param callback 结果回调
     */
    @Override
    public void connect(ImConnectionInputConfig inputConfig,
                        final IBuildConnectionResultCallback callback) {

        //初始化没完成，直接报错失败
        if (mSocketConnManager == null) {

            if (callback != null) {
                callback.onFail(ImErrorCode.ERRCODE_CONNECTION_INIT_FAILED, "IMConnection Init Fail!");
            }
            return;
        }

        mSocketConnManager.buildConnection(inputConfig, new IBuildConnectionResultCallback() {

            @Override
            public void onSuccess(Message rsp) {
                if (callback != null) {
                    callback.onSuccess(rsp);
                }
            }

            @Override
            public void onFail(int errCode, String errMsg) {

                if (callback != null) {
                    callback.onFail(errCode, errMsg);
                }

            }
        });

    }


    /**
     * 发送request消息，需要回复
     * @param token msg唯一性标识
     * @param sendRequest 请求内容、msg/request均可
     * @param callBack 结果回调
     * @param <T> 发送request的消息格式
     * @param <K> 收到的回复response消息格式
     */
    @Override
    public <T extends Message, K extends Message> void sendIMRequest(final long token,
                                                                     T sendRequest,
                                                                     final IRequestResultCallBack<K> callBack) {

        //初始化失败，无法进行发送
        if (mImSendRecManager == null) {

            if (callBack != null) {
                callBack.onFail(ImErrorCode.ERRCODE_CONNECTION_INIT_FAILED, "IMConnection Init Fail!");
            }

            return;
        }

        mImSendRecManager.sendImRequestMessage(token, sendRequest, true,
                new ISendMessageCallback<K>() {
            @Override
            public void onSuccess(long msgUniqueId, K responseMsg, int msgWhat) {

                if (msgUniqueId == token) {

                    if (callBack != null) {
                        callBack.onSuccess(responseMsg);
                    }

                } else {
                    if (callBack != null) {
                        callBack.onFail(ImErrorCode.ERRCODE_MSG_TOKEN_NOT_MATCH,
                                "IMConnection Init Fail!");
                    }
                }

            }

            @Override
            public void onFail(long token, int errCode, String errMsg) {

                if (callBack != null) {
                    callBack.onFail(errCode, errMsg);
                }
            }
        });

    }

    /**
     * 发送不需要回复的notify消息
     * @param token msg唯一性标识
     * @param sendNotify notify请求的消息内容
     * @param callBack 结果回调
     * @param <T> 发送的消息格式
     */
    @Override
    public <T extends Message> void sendIMNotify(final long token,
                                                 T sendNotify,
                                                 final IRequestResultCallBack<Boolean> callBack) {
        //初始化失败，无法进行发送
        if (mImSendRecManager == null) {

            if (callBack != null) {
                callBack.onFail(ImErrorCode.ERRCODE_CONNECTION_INIT_FAILED,
                        "IMConnection Init Fail!");
            }

            return;
        }

        mImSendRecManager.sendImRequestMessage(token, sendNotify, false, new ISendMessageCallback<Message>() {
            @Override
            public void onSuccess(long msgUniqueId, Message responseMsg, int msgWhat) {
                if (msgUniqueId == token) {

                    if (callBack != null) {
                        callBack.onSuccess(true);
                    }

                } else {
                    if (callBack != null) {
                        callBack.onFail(ImErrorCode.ERRCODE_MSG_TOKEN_NOT_MATCH, "IMConnection Init Fail!");
                    }
                }
            }

            @Override
            public void onFail(long token, int errCode, String errMsg) {
                if (callBack != null) {
                    callBack.onFail(errCode, errMsg);
                }
            }
        });
    }

    @Override
    public void registerConnListener(IConnectionListener listener) {

        if (mConnectionListeners != null && !mConnectionListeners.contains(listener)) {
            mConnectionListeners.add(listener);
        }

    }

    @Override
    public void unRegisterConnListener(IConnectionListener listener) {

        if (mConnectionListeners != null) {
            mConnectionListeners.remove(listener);
        }

    }

    /**
     * 获取当前的连接状态
     * @return 当前的连接状态
     */
    @Override
    public int getConnectionStatus() {

        if (mSocketConnManager == null) {
            return ImConnectionState.STATE_IDLE;
        }

        return mSocketConnManager.getCurrentStatus();
    }

    @Override
    public IMConnectionStatus getCurrentIMConnStatus() {

        return ImConnectionState.getEnumByStatusCode(getConnectionStatus());
    }


    /**
     * 修改连接所在前后台状态
     * @param isFront 是否是前台
     */
    @Override
    public void changeConnFrontOrBack(boolean isFront) {

        if (mSocketConnManager != null) {
            mSocketConnManager.onConnFrontOrBackChange(isFront);
        }

    }

    @Override
    public boolean isConnectionFront() {

        if (mSocketConnManager != null) {
            mSocketConnManager.isConnectionFront();
        }

        return false;
    }


    ///====================  获取内部子模块的变化输出 BEGIN ==========================

    private IMConnectionStatus mConnStatus = IMConnectionStatus.IM_IDLE;

    @Override
    public void onConnStatusChanged(int status, String msg) {

        if (mImSendRecManager != null) {
            mImSendRecManager.onConnStateChanged(status, msg);
        }


        IMConnectionStatus curStatus = ImConnectionState.getEnumByStatusCode(status);

        if (mConnStatus != curStatus) {

            mConnStatus = curStatus;

            // 将底层连接的状态传递到外界
            if (mConnectionListeners != null && !mConnectionListeners.isEmpty()) {
                for (IConnectionListener listener : mConnectionListeners) {

                    listener.onConnectionStatusChange(curStatus);
                }
            }
        }


    }

    @Override
    public void onReceiveByteMsg(ByteDataMessage msg) {

        if (mImSendRecManager != null) {
            mImSendRecManager.onReceiveByteMsg(msg);
        }

    }

    //通知外部进行一次新的长连接login登陆，目前分为两种状况：
    //1. conn获取的IPList 重试次数超出上限
    //2. 网络中断恢复/网络转换 后 重连发现输入参数缺失，通知上层进行login
    @Override
    public void onNeedNewLoginOperation() {

        //向外部业务层要求进行新的带http的login操作
        if (mConnectionListeners != null && !mConnectionListeners.isEmpty()) {
            for (IConnectionListener listener : mConnectionListeners) {

                listener.onRequestNewLogin();
            }
        }
    }


    @Override
    public void onGetSendTimeOut() {

        if (mSocketConnManager != null) {
            mSocketConnManager.onGetSendTimeOut();
        }

    }

    @Override
    public void onGetWriteByteMsgToConn(Message msg,
                                        SendDataMsgWrapper.IWriteByteMsgCallback callback) {

        if (mSocketConnManager != null) {
            mSocketConnManager.onGetWriteByteMsgToConn(msg, callback);
        }

    }

    @Override
    public void onReceivePushMessage(Message contentMsg, String msgName) {

        // 接收到解析好的pb消息，向外界分发

        if (mConnectionListeners != null && !mConnectionListeners.isEmpty()) {
            for (IConnectionListener listener : mConnectionListeners) {
                listener.onReceiveMessages(contentMsg, msgName);
            }
        }



    }

    ///====================  获取内部子模块的变化输出 END ==========================




}
