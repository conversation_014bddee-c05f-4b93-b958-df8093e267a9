// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: IMC.Chat.proto at 53:1
package IMC.Chat;

import com.squareup.wire.FieldEncoding;
import com.squareup.wire.Message;
import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.ProtoReader;
import com.squareup.wire.ProtoWriter;
import com.squareup.wire.WireField;
import com.squareup.wire.internal.Internal;
import java.io.IOException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import okio.ByteString;

public final class SingleMessageHistoryReq extends Message<SingleMessageHistoryReq, SingleMessageHistoryReq.Builder> {
  public static final ProtoAdapter<SingleMessageHistoryReq> ADAPTER = new ProtoAdapter_SingleMessageHistoryReq();

  private static final long serialVersionUID = 0L;

  public static final String DEFAULT_USERID = "";

  public static final String DEFAULT_PEERID = "";

  public static final Long DEFAULT_STARTID = 0L;

  public static final Long DEFAULT_MSGCNT = 0L;

  public static final Long DEFAULT_UNIQUEID = 0L;

  /**
   * 发送请求id
   */
  @WireField(
      tag = 1,
      adapter = "com.squareup.wire.ProtoAdapter#STRING",
      label = WireField.Label.REQUIRED
  )
  public final String userId;

  /**
   * 对端Id
   */
  @WireField(
      tag = 2,
      adapter = "com.squareup.wire.ProtoAdapter#STRING"
  )
  public final String peerId;

  /**
   * 起始位置
   */
  @WireField(
      tag = 3,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64",
      label = WireField.Label.REQUIRED
  )
  public final Long startId;

  /**
   * 消息量
   */
  @WireField(
      tag = 4,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64",
      label = WireField.Label.REQUIRED
  )
  public final Long msgCnt;

  /**
   * 客户端标识
   */
  @WireField(
      tag = 5,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64",
      label = WireField.Label.REQUIRED
  )
  public final Long uniqueId;

  public SingleMessageHistoryReq(String userId, String peerId, Long startId, Long msgCnt, Long uniqueId) {
    this(userId, peerId, startId, msgCnt, uniqueId, ByteString.EMPTY);
  }

  public SingleMessageHistoryReq(String userId, String peerId, Long startId, Long msgCnt, Long uniqueId, ByteString unknownFields) {
    super(ADAPTER, unknownFields);
    this.userId = userId;
    this.peerId = peerId;
    this.startId = startId;
    this.msgCnt = msgCnt;
    this.uniqueId = uniqueId;
  }

  @Override
  public Builder newBuilder() {
    Builder builder = new Builder();
    builder.userId = userId;
    builder.peerId = peerId;
    builder.startId = startId;
    builder.msgCnt = msgCnt;
    builder.uniqueId = uniqueId;
    builder.addUnknownFields(unknownFields());
    return builder;
  }

  @Override
  public boolean equals(Object other) {
    if (other == this) return true;
    if (!(other instanceof SingleMessageHistoryReq)) return false;
    SingleMessageHistoryReq o = (SingleMessageHistoryReq) other;
    return unknownFields().equals(o.unknownFields())
        && userId.equals(o.userId)
        && Internal.equals(peerId, o.peerId)
        && startId.equals(o.startId)
        && msgCnt.equals(o.msgCnt)
        && uniqueId.equals(o.uniqueId);
  }

  @Override
  public int hashCode() {
    int result = super.hashCode;
    if (result == 0) {
      result = unknownFields().hashCode();
      result = result * 37 + userId.hashCode();
      result = result * 37 + (peerId != null ? peerId.hashCode() : 0);
      result = result * 37 + startId.hashCode();
      result = result * 37 + msgCnt.hashCode();
      result = result * 37 + uniqueId.hashCode();
      super.hashCode = result;
    }
    return result;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append(", userId=").append(userId);
    if (peerId != null) builder.append(", peerId=").append(peerId);
    builder.append(", startId=").append(startId);
    builder.append(", msgCnt=").append(msgCnt);
    builder.append(", uniqueId=").append(uniqueId);
    return builder.replace(0, 2, "SingleMessageHistoryReq{").append('}').toString();
  }

  public static final class Builder extends Message.Builder<SingleMessageHistoryReq, Builder> {
    public String userId;

    public String peerId;

    public Long startId;

    public Long msgCnt;

    public Long uniqueId;

    public Builder() {
    }

    /**
     * 发送请求id
     */
    public Builder userId(String userId) {
      this.userId = userId;
      return this;
    }

    /**
     * 对端Id
     */
    public Builder peerId(String peerId) {
      this.peerId = peerId;
      return this;
    }

    /**
     * 起始位置
     */
    public Builder startId(Long startId) {
      this.startId = startId;
      return this;
    }

    /**
     * 消息量
     */
    public Builder msgCnt(Long msgCnt) {
      this.msgCnt = msgCnt;
      return this;
    }

    /**
     * 客户端标识
     */
    public Builder uniqueId(Long uniqueId) {
      this.uniqueId = uniqueId;
      return this;
    }

    @Override
    public SingleMessageHistoryReq build() {
      if (userId == null
          || startId == null
          || msgCnt == null
          || uniqueId == null) {
        throw Internal.missingRequiredFields(userId, "userId",
            startId, "startId",
            msgCnt, "msgCnt",
            uniqueId, "uniqueId");
      }
      return new SingleMessageHistoryReq(userId, peerId, startId, msgCnt, uniqueId, super.buildUnknownFields());
    }
  }

  private static final class ProtoAdapter_SingleMessageHistoryReq extends ProtoAdapter<SingleMessageHistoryReq> {
    ProtoAdapter_SingleMessageHistoryReq() {
      super(FieldEncoding.LENGTH_DELIMITED, SingleMessageHistoryReq.class);
    }

    @Override
    public int encodedSize(SingleMessageHistoryReq value) {
      return ProtoAdapter.STRING.encodedSizeWithTag(1, value.userId)
          + (value.peerId != null ? ProtoAdapter.STRING.encodedSizeWithTag(2, value.peerId) : 0)
          + ProtoAdapter.UINT64.encodedSizeWithTag(3, value.startId)
          + ProtoAdapter.UINT64.encodedSizeWithTag(4, value.msgCnt)
          + ProtoAdapter.UINT64.encodedSizeWithTag(5, value.uniqueId)
          + value.unknownFields().size();
    }

    @Override
    public void encode(ProtoWriter writer, SingleMessageHistoryReq value) throws IOException {
      ProtoAdapter.STRING.encodeWithTag(writer, 1, value.userId);
      if (value.peerId != null) ProtoAdapter.STRING.encodeWithTag(writer, 2, value.peerId);
      ProtoAdapter.UINT64.encodeWithTag(writer, 3, value.startId);
      ProtoAdapter.UINT64.encodeWithTag(writer, 4, value.msgCnt);
      ProtoAdapter.UINT64.encodeWithTag(writer, 5, value.uniqueId);
      writer.writeBytes(value.unknownFields());
    }

    @Override
    public SingleMessageHistoryReq decode(ProtoReader reader) throws IOException {
      Builder builder = new Builder();
      long token = reader.beginMessage();
      for (int tag; (tag = reader.nextTag()) != -1;) {
        switch (tag) {
          case 1: builder.userId(ProtoAdapter.STRING.decode(reader)); break;
          case 2: builder.peerId(ProtoAdapter.STRING.decode(reader)); break;
          case 3: builder.startId(ProtoAdapter.UINT64.decode(reader)); break;
          case 4: builder.msgCnt(ProtoAdapter.UINT64.decode(reader)); break;
          case 5: builder.uniqueId(ProtoAdapter.UINT64.decode(reader)); break;
          default: {
            FieldEncoding fieldEncoding = reader.peekFieldEncoding();
            Object value = fieldEncoding.rawProtoAdapter().decode(reader);
            builder.addUnknownField(tag, fieldEncoding, value);
          }
        }
      }
      reader.endMessage(token);
      return builder.build();
    }

    @Override
    public SingleMessageHistoryReq redact(SingleMessageHistoryReq value) {
      Builder builder = value.newBuilder();
      builder.clearUnknownFields();
      return builder.build();
    }
  }
}
