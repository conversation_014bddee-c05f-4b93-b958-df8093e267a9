package com.ximalaya.ting.android.xmlog.manager;

import android.content.Context;
import android.os.FileObserver;
import android.text.TextUtils;
import android.view.TextureView;

import com.ximalaya.ting.android.xmlog.Logger;
import com.ximalaya.ting.android.xmlog.XmLogger;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by jack.qin on 2018/5/30.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class LogFileObserver extends FileObserver {
    private String logDirPath;
    private String tmpLogDir = "";
    private Context context;
    private static final String TAG = LogFileObserver.class.getSimpleName();

    public LogFileObserver(Context context, String path, int mask) {
        super(path, FileObserver.CREATE | FileObserver.MOVED_FROM);
        logDirPath = path;
        this.context = context;
    }

    @Override
    public void onEvent(int event, String path) {
        if (event == FileObserver.CREATE) {
            Logger.i(TAG, "create patch" + path);
            FileUtil.cleanUnFormalFile(logDirPath);
            ArrayList<File> fileList = FileUtil.getCanUploadFiles(logDirPath);
            if (fileList != null && fileList.size() > 0) {
                Logger.i(TAG, "can upload patch" + fileList.toString());
                if (XmLogger.uploadHandler != null && XmLogger.enable) {
                    XmLogger.uploadHandler.uploadFileList(fileList);
                }
//                UploadService.startUploadLogFile(context, fileList);
            }
        } else if (event == FileObserver.MOVED_FROM) {
            initTmpLogDir();
            if (TextUtils.isEmpty(tmpLogDir)) {
                return;
            }
            File dir = new File(tmpLogDir);
            File[] files = dir.listFiles();
            if (files != null && files.length > 0) {
                ArrayList<File> fileList = new ArrayList<>(Arrays.asList(files));
                if (XmLogger.uploadHandler != null && XmLogger.enable) {
                    XmLogger.uploadHandler.uploadFileList(fileList);
                }
            }

        }
    }

    public void flushFile() {
        initTmpLogDir();
        if (TextUtils.isEmpty(tmpLogDir)) {
            return;
        }
        try {
            File dirFile = new File(tmpLogDir);
            if (!dirFile.exists() && !dirFile.isDirectory()) {
                if (!dirFile.mkdirs()) {
                    return;
                }
            }
            List<File> files = FileUtil.getAllFile(logDirPath);
            if (files != null && files.size() > 0) {
                for (File file : files) {
                    if (file.exists() && file.isFile()) {
                        File tmpFile = new File(tmpLogDir, file.getName());
                        if (tmpFile.exists()) {
                            tmpFile.delete();
                        }
                        file.renameTo(tmpFile);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initTmpLogDir() {
        if (!TextUtils.isEmpty(tmpLogDir)) {
            return;
        }
        File logDir = new File(logDirPath);
        if (logDir.exists() && logDir.isDirectory()) {
            tmpLogDir = logDir.getParent() + File.separator + "tmp";
        } else {
            if (logDir.mkdirs()) {
                tmpLogDir = logDir.getParent() + File.separator + "tmp";
            } else {
                tmpLogDir = "";
            }
        }
    }
}
